[!net:github.com] skip
[!git] skip
env GO111MODULE=off

# Clone the repo via HTTP manually.
exec git clone -q http://github.com/golang/example github.com/golang/example

# Update without GOINSECURE should fail.
# We need -f to ignore import comments.
! go get -d -u -f github.com/golang/example/hello

# Update with GOINSECURE should succeed.
env GOINSECURE=github.com/golang/example/hello
go get -d -u -f github.com/golang/example/hello
