/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.profile-container {
  min-height: 100vh;
  background: #f5f5f5;
  padding: 0.625rem;
}
.user-card {
  background: linear-gradient(135deg, #2196F3 0%, #1976D2 100%);
  border-radius: 0.625rem;
  padding: 1.25rem;
  margin-bottom: 0.9375rem;
  display: flex;
  align-items: center;
  box-shadow: 0 0.25rem 0.78125rem rgba(33, 150, 243, 0.3);
}
.avatar {
  width: 3.75rem;
  height: 3.75rem;
  border-radius: 1.875rem;
  background: rgba(255, 255, 255, 0.2);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 0.9375rem;
}
.avatar-text {
  font-size: 1.5rem;
  font-weight: bold;
  color: #ffffff;
}
.user-info {
  flex: 1;
}
.username {
  display: block;
  font-size: 1.125rem;
  font-weight: bold;
  color: #ffffff;
  margin-bottom: 0.3125rem;
}
.role {
  display: block;
  font-size: 0.875rem;
  color: rgba(255, 255, 255, 0.8);
}
.menu-section {
  background: #ffffff;
  border-radius: 0.625rem;
  margin-bottom: 0.9375rem;
  overflow: hidden;
  box-shadow: 0 0.125rem 0.46875rem rgba(0, 0, 0, 0.1);
}
.menu-item {
  display: flex;
  align-items: center;
  padding: 0.9375rem 1.25rem;
  border-bottom: 0.03125rem solid #f0f0f0;
}
.menu-item:last-child {
  border-bottom: none;
}
.menu-item:active {
  background: #f8f8f8;
}
.menu-icon {
  width: 1.875rem;
  height: 1.875rem;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1rem;
  margin-right: 0.9375rem;
}
.menu-text {
  flex: 1;
  font-size: 1rem;
  color: #333333;
}
.menu-arrow {
  font-size: 0.875rem;
  color: #999999;
}
.logout-section {
  padding: 0 0.625rem;
}
.logout-btn {
  width: 100%;
  height: 2.75rem;
  background: #ffffff;
  color: #ff4757;
  border: 0.0625rem solid #ff4757;
  border-radius: 0.375rem;
  font-size: 1rem;
  font-weight: bold;
}
.logout-btn:active {
  background: #ff4757;
  color: #ffffff;
}