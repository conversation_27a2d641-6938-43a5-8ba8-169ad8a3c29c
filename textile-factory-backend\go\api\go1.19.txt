pkg crypto/x509, func ParseRevocationList([]uint8) (*RevocationList, error) #50674
pkg crypto/x509, method (*CertPool) Clone() *CertPool #35044
pkg crypto/x509, method (*CertPool) Equal(*CertPool) bool #46057
pkg crypto/x509, method (*RevocationList) CheckSignatureFrom(*Certificate) error #50674
pkg crypto/x509, type RevocationList struct, AuthorityKeyId []uint8 #50674
pkg crypto/x509, type RevocationList struct, Extensions []pkix.Extension #50674
pkg crypto/x509, type RevocationList struct, Issuer pkix.Name #50674
pkg crypto/x509, type RevocationList struct, Raw []uint8 #50674
pkg crypto/x509, type RevocationList struct, RawIssuer []uint8 #50674
pkg crypto/x509, type RevocationList struct, RawTBSRevocationList []uint8 #50674
pkg crypto/x509, type RevocationList struct, Signature []uint8 #50674
pkg debug/elf, const EM_LOONGARCH = 258 #46229
pkg debug/elf, const EM_LOONGARCH Machine #46229
pkg debug/elf, const R_LARCH_32 = 1 #46229
pkg debug/elf, const R_LARCH_32 R_LARCH #46229
pkg debug/elf, const R_LARCH_64 = 2 #46229
pkg debug/elf, const R_LARCH_64 R_LARCH #46229
pkg debug/elf, const R_LARCH_ADD16 = 48 #46229
pkg debug/elf, const R_LARCH_ADD16 R_LARCH #46229
pkg debug/elf, const R_LARCH_ADD24 = 49 #46229
pkg debug/elf, const R_LARCH_ADD24 R_LARCH #46229
pkg debug/elf, const R_LARCH_ADD32 = 50 #46229
pkg debug/elf, const R_LARCH_ADD32 R_LARCH #46229
pkg debug/elf, const R_LARCH_ADD64 = 51 #46229
pkg debug/elf, const R_LARCH_ADD64 R_LARCH #46229
pkg debug/elf, const R_LARCH_ADD8 = 47 #46229
pkg debug/elf, const R_LARCH_ADD8 R_LARCH #46229
pkg debug/elf, const R_LARCH_COPY = 4 #46229
pkg debug/elf, const R_LARCH_COPY R_LARCH #46229
pkg debug/elf, const R_LARCH_IRELATIVE = 12 #46229
pkg debug/elf, const R_LARCH_IRELATIVE R_LARCH #46229
pkg debug/elf, const R_LARCH_JUMP_SLOT = 5 #46229
pkg debug/elf, const R_LARCH_JUMP_SLOT R_LARCH #46229
pkg debug/elf, const R_LARCH_MARK_LA = 20 #46229
pkg debug/elf, const R_LARCH_MARK_LA R_LARCH #46229
pkg debug/elf, const R_LARCH_MARK_PCREL = 21 #46229
pkg debug/elf, const R_LARCH_MARK_PCREL R_LARCH #46229
pkg debug/elf, const R_LARCH_NONE = 0 #46229
pkg debug/elf, const R_LARCH_NONE R_LARCH #46229
pkg debug/elf, const R_LARCH_RELATIVE = 3 #46229
pkg debug/elf, const R_LARCH_RELATIVE R_LARCH #46229
pkg debug/elf, const R_LARCH_SOP_ADD = 35 #46229
pkg debug/elf, const R_LARCH_SOP_ADD R_LARCH #46229
pkg debug/elf, const R_LARCH_SOP_AND = 36 #46229
pkg debug/elf, const R_LARCH_SOP_AND R_LARCH #46229
pkg debug/elf, const R_LARCH_SOP_ASSERT = 30 #46229
pkg debug/elf, const R_LARCH_SOP_ASSERT R_LARCH #46229
pkg debug/elf, const R_LARCH_SOP_IF_ELSE = 37 #46229
pkg debug/elf, const R_LARCH_SOP_IF_ELSE R_LARCH #46229
pkg debug/elf, const R_LARCH_SOP_NOT = 31 #46229
pkg debug/elf, const R_LARCH_SOP_NOT R_LARCH #46229
pkg debug/elf, const R_LARCH_SOP_POP_32_S_0_10_10_16_S2 = 45 #46229
pkg debug/elf, const R_LARCH_SOP_POP_32_S_0_10_10_16_S2 R_LARCH #46229
pkg debug/elf, const R_LARCH_SOP_POP_32_S_0_5_10_16_S2 = 44 #46229
pkg debug/elf, const R_LARCH_SOP_POP_32_S_0_5_10_16_S2 R_LARCH #46229
pkg debug/elf, const R_LARCH_SOP_POP_32_S_10_12 = 40 #46229
pkg debug/elf, const R_LARCH_SOP_POP_32_S_10_12 R_LARCH #46229
pkg debug/elf, const R_LARCH_SOP_POP_32_S_10_16 = 41 #46229
pkg debug/elf, const R_LARCH_SOP_POP_32_S_10_16 R_LARCH #46229
pkg debug/elf, const R_LARCH_SOP_POP_32_S_10_16_S2 = 42 #46229
pkg debug/elf, const R_LARCH_SOP_POP_32_S_10_16_S2 R_LARCH #46229
pkg debug/elf, const R_LARCH_SOP_POP_32_S_10_5 = 38 #46229
pkg debug/elf, const R_LARCH_SOP_POP_32_S_10_5 R_LARCH #46229
pkg debug/elf, const R_LARCH_SOP_POP_32_S_5_20 = 43 #46229
pkg debug/elf, const R_LARCH_SOP_POP_32_S_5_20 R_LARCH #46229
pkg debug/elf, const R_LARCH_SOP_POP_32_U = 46 #46229
pkg debug/elf, const R_LARCH_SOP_POP_32_U R_LARCH #46229
pkg debug/elf, const R_LARCH_SOP_POP_32_U_10_12 = 39 #46229
pkg debug/elf, const R_LARCH_SOP_POP_32_U_10_12 R_LARCH #46229
pkg debug/elf, const R_LARCH_SOP_PUSH_ABSOLUTE = 23 #46229
pkg debug/elf, const R_LARCH_SOP_PUSH_ABSOLUTE R_LARCH #46229
pkg debug/elf, const R_LARCH_SOP_PUSH_DUP = 24 #46229
pkg debug/elf, const R_LARCH_SOP_PUSH_DUP R_LARCH #46229
pkg debug/elf, const R_LARCH_SOP_PUSH_GPREL = 25 #46229
pkg debug/elf, const R_LARCH_SOP_PUSH_GPREL R_LARCH #46229
pkg debug/elf, const R_LARCH_SOP_PUSH_PCREL = 22 #46229
pkg debug/elf, const R_LARCH_SOP_PUSH_PCREL R_LARCH #46229
pkg debug/elf, const R_LARCH_SOP_PUSH_PLT_PCREL = 29 #46229
pkg debug/elf, const R_LARCH_SOP_PUSH_PLT_PCREL R_LARCH #46229
pkg debug/elf, const R_LARCH_SOP_PUSH_TLS_GD = 28 #46229
pkg debug/elf, const R_LARCH_SOP_PUSH_TLS_GD R_LARCH #46229
pkg debug/elf, const R_LARCH_SOP_PUSH_TLS_GOT = 27 #46229
pkg debug/elf, const R_LARCH_SOP_PUSH_TLS_GOT R_LARCH #46229
pkg debug/elf, const R_LARCH_SOP_PUSH_TLS_TPREL = 26 #46229
pkg debug/elf, const R_LARCH_SOP_PUSH_TLS_TPREL R_LARCH #46229
pkg debug/elf, const R_LARCH_SOP_SL = 33 #46229
pkg debug/elf, const R_LARCH_SOP_SL R_LARCH #46229
pkg debug/elf, const R_LARCH_SOP_SR = 34 #46229
pkg debug/elf, const R_LARCH_SOP_SR R_LARCH #46229
pkg debug/elf, const R_LARCH_SOP_SUB = 32 #46229
pkg debug/elf, const R_LARCH_SOP_SUB R_LARCH #46229
pkg debug/elf, const R_LARCH_SUB16 = 53 #46229
pkg debug/elf, const R_LARCH_SUB16 R_LARCH #46229
pkg debug/elf, const R_LARCH_SUB24 = 54 #46229
pkg debug/elf, const R_LARCH_SUB24 R_LARCH #46229
pkg debug/elf, const R_LARCH_SUB32 = 55 #46229
pkg debug/elf, const R_LARCH_SUB32 R_LARCH #46229
pkg debug/elf, const R_LARCH_SUB64 = 56 #46229
pkg debug/elf, const R_LARCH_SUB64 R_LARCH #46229
pkg debug/elf, const R_LARCH_SUB8 = 52 #46229
pkg debug/elf, const R_LARCH_SUB8 R_LARCH #46229
pkg debug/elf, const R_LARCH_TLS_DTPMOD32 = 6 #46229
pkg debug/elf, const R_LARCH_TLS_DTPMOD32 R_LARCH #46229
pkg debug/elf, const R_LARCH_TLS_DTPMOD64 = 7 #46229
pkg debug/elf, const R_LARCH_TLS_DTPMOD64 R_LARCH #46229
pkg debug/elf, const R_LARCH_TLS_DTPREL32 = 8 #46229
pkg debug/elf, const R_LARCH_TLS_DTPREL32 R_LARCH #46229
pkg debug/elf, const R_LARCH_TLS_DTPREL64 = 9 #46229
pkg debug/elf, const R_LARCH_TLS_DTPREL64 R_LARCH #46229
pkg debug/elf, const R_LARCH_TLS_TPREL32 = 10 #46229
pkg debug/elf, const R_LARCH_TLS_TPREL32 R_LARCH #46229
pkg debug/elf, const R_LARCH_TLS_TPREL64 = 11 #46229
pkg debug/elf, const R_LARCH_TLS_TPREL64 R_LARCH #46229
pkg debug/elf, method (R_LARCH) GoString() string #46229
pkg debug/elf, method (R_LARCH) String() string #46229
pkg debug/elf, type R_LARCH int #46229
pkg debug/pe, const IMAGE_COMDAT_SELECT_ANY = 2 #51868
pkg debug/pe, const IMAGE_COMDAT_SELECT_ANY ideal-int #51868
pkg debug/pe, const IMAGE_COMDAT_SELECT_ASSOCIATIVE = 5 #51868
pkg debug/pe, const IMAGE_COMDAT_SELECT_ASSOCIATIVE ideal-int #51868
pkg debug/pe, const IMAGE_COMDAT_SELECT_EXACT_MATCH = 4 #51868
pkg debug/pe, const IMAGE_COMDAT_SELECT_EXACT_MATCH ideal-int #51868
pkg debug/pe, const IMAGE_COMDAT_SELECT_LARGEST = 6 #51868
pkg debug/pe, const IMAGE_COMDAT_SELECT_LARGEST ideal-int #51868
pkg debug/pe, const IMAGE_COMDAT_SELECT_NODUPLICATES = 1 #51868
pkg debug/pe, const IMAGE_COMDAT_SELECT_NODUPLICATES ideal-int #51868
pkg debug/pe, const IMAGE_COMDAT_SELECT_SAME_SIZE = 3 #51868
pkg debug/pe, const IMAGE_COMDAT_SELECT_SAME_SIZE ideal-int #51868
pkg debug/pe, const IMAGE_FILE_MACHINE_LOONGARCH32 = 25138 #46229
pkg debug/pe, const IMAGE_FILE_MACHINE_LOONGARCH32 ideal-int #46229
pkg debug/pe, const IMAGE_FILE_MACHINE_LOONGARCH64 = 25188 #46229
pkg debug/pe, const IMAGE_FILE_MACHINE_LOONGARCH64 ideal-int #46229
pkg debug/pe, const IMAGE_SCN_CNT_CODE = 32 #51868
pkg debug/pe, const IMAGE_SCN_CNT_CODE ideal-int #51868
pkg debug/pe, const IMAGE_SCN_CNT_INITIALIZED_DATA = 64 #51868
pkg debug/pe, const IMAGE_SCN_CNT_INITIALIZED_DATA ideal-int #51868
pkg debug/pe, const IMAGE_SCN_CNT_UNINITIALIZED_DATA = 128 #51868
pkg debug/pe, const IMAGE_SCN_CNT_UNINITIALIZED_DATA ideal-int #51868
pkg debug/pe, const IMAGE_SCN_LNK_COMDAT = 4096 #51868
pkg debug/pe, const IMAGE_SCN_LNK_COMDAT ideal-int #51868
pkg debug/pe, const IMAGE_SCN_MEM_DISCARDABLE = 33554432 #51868
pkg debug/pe, const IMAGE_SCN_MEM_DISCARDABLE ideal-int #51868
pkg debug/pe, const IMAGE_SCN_MEM_EXECUTE = 536870912 #51868
pkg debug/pe, const IMAGE_SCN_MEM_EXECUTE ideal-int #51868
pkg debug/pe, const IMAGE_SCN_MEM_READ = 1073741824 #51868
pkg debug/pe, const IMAGE_SCN_MEM_READ ideal-int #51868
pkg debug/pe, const IMAGE_SCN_MEM_WRITE = 2147483648 #51868
pkg debug/pe, const IMAGE_SCN_MEM_WRITE ideal-int #51868
pkg debug/pe, method (*File) COFFSymbolReadSectionDefAux(int) (*COFFSymbolAuxFormat5, error) #51868
pkg debug/pe, type COFFSymbolAuxFormat5 struct #51868
pkg debug/pe, type COFFSymbolAuxFormat5 struct, Checksum uint32 #51868
pkg debug/pe, type COFFSymbolAuxFormat5 struct, NumLineNumbers uint16 #51868
pkg debug/pe, type COFFSymbolAuxFormat5 struct, NumRelocs uint16 #51868
pkg debug/pe, type COFFSymbolAuxFormat5 struct, SecNum uint16 #51868
pkg debug/pe, type COFFSymbolAuxFormat5 struct, Selection uint8 #51868
pkg debug/pe, type COFFSymbolAuxFormat5 struct, Size uint32 #51868
pkg encoding/binary, func AppendUvarint([]uint8, uint64) []uint8 #51644
pkg encoding/binary, func AppendVarint([]uint8, int64) []uint8 #51644
pkg encoding/binary, type AppendByteOrder interface { AppendUint16, AppendUint32, AppendUint64, String } #50601
pkg encoding/binary, type AppendByteOrder interface, AppendUint16([]uint8, uint16) []uint8 #50601
pkg encoding/binary, type AppendByteOrder interface, AppendUint32([]uint8, uint32) []uint8 #50601
pkg encoding/binary, type AppendByteOrder interface, AppendUint64([]uint8, uint64) []uint8 #50601
pkg encoding/binary, type AppendByteOrder interface, String() string #50601
pkg encoding/csv, method (*Reader) InputOffset() int64 #43401
pkg encoding/xml, method (*Decoder) InputPos() (int, int) #45628
pkg flag, func TextVar(encoding.TextUnmarshaler, string, encoding.TextMarshaler, string) #45754
pkg flag, method (*FlagSet) TextVar(encoding.TextUnmarshaler, string, encoding.TextMarshaler, string) #45754
pkg fmt, func Append([]uint8, ...interface{}) []uint8 #47579
pkg fmt, func Appendf([]uint8, string, ...interface{}) []uint8 #47579
pkg fmt, func Appendln([]uint8, ...interface{}) []uint8 #47579
pkg go/doc, method (*Package) HTML(string) []uint8 #51082
pkg go/doc, method (*Package) Markdown(string) []uint8 #51082
pkg go/doc, method (*Package) Parser() *comment.Parser #51082
pkg go/doc, method (*Package) Printer() *comment.Printer #51082
pkg go/doc, method (*Package) Synopsis(string) string #51082
pkg go/doc, method (*Package) Text(string) []uint8 #51082
pkg go/doc/comment, func DefaultLookupPackage(string) (string, bool) #51082
pkg go/doc/comment, method (*DocLink) DefaultURL(string) string #51082
pkg go/doc/comment, method (*Heading) DefaultID() string #51082
pkg go/doc/comment, method (*List) BlankBefore() bool #51082
pkg go/doc/comment, method (*List) BlankBetween() bool #51082
pkg go/doc/comment, method (*Parser) Parse(string) *Doc #51082
pkg go/doc/comment, method (*Printer) Comment(*Doc) []uint8 #51082
pkg go/doc/comment, method (*Printer) HTML(*Doc) []uint8 #51082
pkg go/doc/comment, method (*Printer) Markdown(*Doc) []uint8 #51082
pkg go/doc/comment, method (*Printer) Text(*Doc) []uint8 #51082
pkg go/doc/comment, type Block interface, unexported methods #51082
pkg go/doc/comment, type Code struct #51082
pkg go/doc/comment, type Code struct, Text string #51082
pkg go/doc/comment, type Doc struct #51082
pkg go/doc/comment, type Doc struct, Content []Block #51082
pkg go/doc/comment, type Doc struct, Links []*LinkDef #51082
pkg go/doc/comment, type DocLink struct #51082
pkg go/doc/comment, type DocLink struct, ImportPath string #51082
pkg go/doc/comment, type DocLink struct, Name string #51082
pkg go/doc/comment, type DocLink struct, Recv string #51082
pkg go/doc/comment, type DocLink struct, Text []Text #51082
pkg go/doc/comment, type Heading struct #51082
pkg go/doc/comment, type Heading struct, Text []Text #51082
pkg go/doc/comment, type Italic string #51082
pkg go/doc/comment, type Link struct #51082
pkg go/doc/comment, type Link struct, Auto bool #51082
pkg go/doc/comment, type Link struct, Text []Text #51082
pkg go/doc/comment, type Link struct, URL string #51082
pkg go/doc/comment, type LinkDef struct #51082
pkg go/doc/comment, type LinkDef struct, Text string #51082
pkg go/doc/comment, type LinkDef struct, URL string #51082
pkg go/doc/comment, type LinkDef struct, Used bool #51082
pkg go/doc/comment, type List struct #51082
pkg go/doc/comment, type List struct, ForceBlankBefore bool #51082
pkg go/doc/comment, type List struct, ForceBlankBetween bool #51082
pkg go/doc/comment, type List struct, Items []*ListItem #51082
pkg go/doc/comment, type ListItem struct #51082
pkg go/doc/comment, type ListItem struct, Content []Block #51082
pkg go/doc/comment, type ListItem struct, Number string #51082
pkg go/doc/comment, type Paragraph struct #51082
pkg go/doc/comment, type Paragraph struct, Text []Text #51082
pkg go/doc/comment, type Parser struct #51082
pkg go/doc/comment, type Parser struct, LookupPackage func(string) (string, bool) #51082
pkg go/doc/comment, type Parser struct, LookupSym func(string, string) bool #51082
pkg go/doc/comment, type Parser struct, Words map[string]string #51082
pkg go/doc/comment, type Plain string #51082
pkg go/doc/comment, type Printer struct #51082
pkg go/doc/comment, type Printer struct, DocLinkBaseURL string #51082
pkg go/doc/comment, type Printer struct, DocLinkURL func(*DocLink) string #51082
pkg go/doc/comment, type Printer struct, HeadingID func(*Heading) string #51082
pkg go/doc/comment, type Printer struct, HeadingLevel int #51082
pkg go/doc/comment, type Printer struct, TextCodePrefix string #51082
pkg go/doc/comment, type Printer struct, TextPrefix string #51082
pkg go/doc/comment, type Printer struct, TextWidth int #51082
pkg go/doc/comment, type Text interface, unexported methods #51082
pkg go/types, method (*Func) Origin() *Func #51682
pkg go/types, method (*Var) Origin() *Var #51682
pkg hash/maphash, func Bytes(Seed, []uint8) uint64 #42710
pkg hash/maphash, func String(Seed, string) uint64 #42710
pkg html/template, method (*Template) Funcs(template.FuncMap) *Template #46121
pkg html/template, type FuncMap = template.FuncMap #46121
pkg net/http, method (*MaxBytesError) Error() string #30715
pkg net/http, type MaxBytesError struct #30715
pkg net/http, type MaxBytesError struct, Limit int64 #30715
pkg net/url, func JoinPath(string, ...string) (string, error) #47005
pkg net/url, method (*URL) JoinPath(...string) *URL #47005
pkg net/url, type URL struct, OmitHost bool #46059
pkg os/exec, method (*Cmd) Environ() []string #50599
pkg os/exec, type Cmd struct, Err error #43724
pkg os/exec, var ErrDot error #43724
pkg regexp/syntax, const ErrNestingDepth = "expression nests too deeply" #51684
pkg regexp/syntax, const ErrNestingDepth ErrorCode #51684
pkg runtime/debug, func SetMemoryLimit(int64) int64 #48409
pkg sort, func Find(int, func(int) int) (int, bool) #50340
pkg sync/atomic, method (*Bool) CompareAndSwap(bool, bool) bool #50860
pkg sync/atomic, method (*Bool) Load() bool #50860
pkg sync/atomic, method (*Bool) Store(bool) #50860
pkg sync/atomic, method (*Bool) Swap(bool) bool #50860
pkg sync/atomic, method (*Int32) Add(int32) int32 #50860
pkg sync/atomic, method (*Int32) CompareAndSwap(int32, int32) bool #50860
pkg sync/atomic, method (*Int32) Load() int32 #50860
pkg sync/atomic, method (*Int32) Store(int32) #50860
pkg sync/atomic, method (*Int32) Swap(int32) int32 #50860
pkg sync/atomic, method (*Int64) Add(int64) int64 #50860
pkg sync/atomic, method (*Int64) CompareAndSwap(int64, int64) bool #50860
pkg sync/atomic, method (*Int64) Load() int64 #50860
pkg sync/atomic, method (*Int64) Store(int64) #50860
pkg sync/atomic, method (*Int64) Swap(int64) int64 #50860
pkg sync/atomic, method (*Pointer[$0]) CompareAndSwap(*$0, *$0) bool #50860
pkg sync/atomic, method (*Pointer[$0]) Load() *$0 #50860
pkg sync/atomic, method (*Pointer[$0]) Store(*$0) #50860
pkg sync/atomic, method (*Pointer[$0]) Swap(*$0) *$0 #50860
pkg sync/atomic, method (*Uint32) Add(uint32) uint32 #50860
pkg sync/atomic, method (*Uint32) CompareAndSwap(uint32, uint32) bool #50860
pkg sync/atomic, method (*Uint32) Load() uint32 #50860
pkg sync/atomic, method (*Uint32) Store(uint32) #50860
pkg sync/atomic, method (*Uint32) Swap(uint32) uint32 #50860
pkg sync/atomic, method (*Uint64) Add(uint64) uint64 #50860
pkg sync/atomic, method (*Uint64) CompareAndSwap(uint64, uint64) bool #50860
pkg sync/atomic, method (*Uint64) Load() uint64 #50860
pkg sync/atomic, method (*Uint64) Store(uint64) #50860
pkg sync/atomic, method (*Uint64) Swap(uint64) uint64 #50860
pkg sync/atomic, method (*Uintptr) Add(uintptr) uintptr #50860
pkg sync/atomic, method (*Uintptr) CompareAndSwap(uintptr, uintptr) bool #50860
pkg sync/atomic, method (*Uintptr) Load() uintptr #50860
pkg sync/atomic, method (*Uintptr) Store(uintptr) #50860
pkg sync/atomic, method (*Uintptr) Swap(uintptr) uintptr #50860
pkg sync/atomic, type Bool struct #50860
pkg sync/atomic, type Int32 struct #50860
pkg sync/atomic, type Int64 struct #50860
pkg sync/atomic, type Pointer[$0 interface{}] struct #50860
pkg sync/atomic, type Uint32 struct #50860
pkg sync/atomic, type Uint64 struct #50860
pkg sync/atomic, type Uintptr struct #50860
pkg time, method (Duration) Abs() Duration #51414
pkg time, method (Time) ZoneBounds() (Time, Time) #50062
pkg crypto/x509, func ParseCRL //deprecated #50674
pkg crypto/x509, func ParseDERCRL //deprecated #50674
pkg crypto/x509, method (*Certificate) CheckCRLSignature //deprecated #50674
pkg crypto/x509, method (*Certificate) CreateCRL //deprecated #50674
pkg crypto/x509/pkix, type CertificateList //deprecated #50674
pkg crypto/x509/pkix, type TBSCertificateList //deprecated #50674
pkg go/doc, func Synopsis //deprecated #51082
pkg go/doc, func ToHTML //deprecated #51082
pkg go/doc, func ToText //deprecated #51082
pkg io/ioutil, func NopCloser //deprecated #42026
pkg io/ioutil, func ReadAll //deprecated #42026
pkg io/ioutil, func ReadDir //deprecated #42026
pkg io/ioutil, func ReadFile //deprecated #42026
pkg io/ioutil, func TempDir //deprecated #42026
pkg io/ioutil, func TempFile //deprecated #42026
pkg io/ioutil, func WriteFile //deprecated #42026
pkg io/ioutil, var Discard //deprecated #42026
