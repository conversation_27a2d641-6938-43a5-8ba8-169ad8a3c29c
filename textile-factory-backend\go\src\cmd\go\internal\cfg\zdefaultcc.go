// Code generated by go tool dist; DO NOT EDIT.

package cfg

const DefaultPkgConfig = `pkg-config`
func DefaultCC(goos, goarch string) string {
	switch goos+`/`+goarch {
	}
	switch goos {
	case "darwin", "ios", "freebsd", "openbsd":
		return "clang"
	}
	return "gcc"
}
func DefaultCXX(goos, goarch string) string {
	switch goos+`/`+goarch {
	}
	switch goos {
	case "darwin", "ios", "freebsd", "openbsd":
		return "clang++"
	}
	return "g++"
}
