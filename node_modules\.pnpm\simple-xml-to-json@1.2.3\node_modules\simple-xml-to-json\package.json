{"name": "simple-xml-to-json", "description": "Convert XML to JSON - Fast & Simple", "keywords": ["XML", "xml", "JSON", "json", "js", "xml2json", "xml2js", "convertor", "conversion", "parsing", "parser", "AST", "ast", "simple", "fast", "lightweight", "small"], "homepage": "https://github.com/nirgit/simple-xml-to-json", "license": "MIT", "version": "1.2.3", "author": "<PERSON><PERSON> <<EMAIL>>", "contributors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://nirgit.github.io/"}], "main": "lib/simpleXmlToJson.min.js", "typings": "typings/simple-xml-to-json.d.ts", "files": ["lib", "typings"], "scripts": {"build": "npm run clean && rollup -c --inline", "clean": "rm -rf ./lib/ && mkdir lib ", "lint": "eslint ./src ./test", "prettify": "prettier --write ./**/*.js", "example": "node example/example.js", "test": "jest ", "make-new-release-lib": "npm run validate-lib-health && sh ./scripts/make-new-release.sh", "validate-lib-health": "npm run prettify && npm run lint && npm test"}, "repository": {"type": "git", "url": "https://github.com/nirgit/simple-xml-to-json.git"}, "bugs": {"url": "https://github.com/nirgit/simple-xml-to-json/issues"}, "devDependencies": {"@rollup/plugin-commonjs": "^25.0.7", "@rollup/plugin-replace": "^5.0.5", "@rollup/plugin-terser": "^0.4.4", "acorn": "^8.11.3", "acorn-walk": "^8.3.2", "eslint": "^9.1.1", "eslint-config-prettier": "^9.1.0", "eslint-plugin-jest": "^28.3.0", "globals": "^15.1.0", "jest": "^29.7.0", "magic-string": "^0.30.10", "prettier": "3.2.5", "rollup": "^4.17.1"}, "engines": {"node": ">=20.12.2"}}