# The build cache is required to build anything. It also may be needed to
# initialize the build system, which is needed for commands like 'go env'.
# However, there are lots of commands the cache is not needed for, and we
# shouldn't require it when it won't be used.
#
# TODO(golang.org/issue/39882): commands below should work, too.
# * go clean -modcache
# * go env
# * go fix
# * go fmt
# * go generate
# * go get
# * go list (without -export or -compiled)

env GOCACHE=off

# Commands that don't completely load packages should work.
go doc fmt
stdout Printf

! go tool compile -h
stderr usage:

go version
stdout '^go version'


# Module commands that don't load packages should work.
go mod init m
exists go.mod

go mod edit -require rsc.io/quote@v1.5.2

go mod download rsc.io/quote

go mod graph
stdout rsc.io/quote

go mod verify


# Commands that load but don't build packages should work.
go fmt .

go doc .

-- main.go --
package main

func main() {}
