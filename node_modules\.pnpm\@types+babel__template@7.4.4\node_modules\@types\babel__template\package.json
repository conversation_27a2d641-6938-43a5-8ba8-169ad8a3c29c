{"name": "@types/babel__template", "version": "7.4.4", "description": "TypeScript definitions for @babel/template", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/babel__template", "license": "MIT", "contributors": [{"name": "<PERSON>", "githubUsername": "yortus", "url": "https://github.com/yortus"}, {"name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/marvinhagemeister"}, {"name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/mgroenhoff"}, {"name": "ExE Boss", "githubUsername": "ExE-Boss", "url": "https://github.com/ExE-Boss"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/babel__template"}, "scripts": {}, "dependencies": {"@babel/parser": "^7.1.0", "@babel/types": "^7.0.0"}, "typesPublisherContentHash": "5730d754b4d1fcd41676b093f9e32b340c749c4d37b126dfa312e394467e86c6", "typeScriptVersion": "4.5"}