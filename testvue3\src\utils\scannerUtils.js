/**
 * 商米扫码头工具类
 * 支持商米PDA设备的扫码功能（L2、P2Lite等）
 * 集成织厂机器异常管理系统后端API
 */
import { API } from './api.js'

class ScannerUtils {
  constructor() {
    this.scanReceiver = null
    this.isPageActive = false
    this.scanCallback = null
    this.isInitialized = false
    this.scanMode = 'general' // general: 通用扫码, report: 上报扫码, repair: 维修扫码
    this.scanInterface = null
    this.serviceConnection = null
  }

  /**
   * 初始化扫码功能
   * @param {Function} callback 扫码成功回调函数
   * @param {Boolean} isPageActive 页面是否活跃
   * @param {String} mode 扫码模式: general/report/repair
   */
  async initScanner(callback, isPageActive = true, mode = 'general') {
	  console.log('initScaner', mode)
    this.scanCallback = callback
    this.isPageActive = isPageActive
    this.scanMode = mode

    // 检查是否为安卓平台
    if (uni.getSystemInfoSync().platform !== 'android') {
      console.log('非安卓平台，使用uni.scanCode')
      return this.useUniScanCode()
    }

    try {
      // 先尝试商米扫码头
      const sunmiResult = await this.initSunmiScanner()
      if (sunmiResult.success) {
        return sunmiResult
      }
      
      // 如果商米扫码头失败，尝试新大陆扫码头
      console.log('商米扫码头初始化失败，尝试新大陆扫码头')
      return await this.initNewlandScanner()
      
    } catch (error) {
      console.error('初始化扫码功能失败：', error)
      // 降级到uni.scanCode
      return this.useUniScanCode()
    }
  }

  /**
   * 初始化商米扫码头
   */
  async initSunmiScanner() {
    try {
      const main = plus.android.runtimeMainActivity()
      
      // 先注册商米广播接收器（这个不依赖服务连接）
      await this.registerSunmiBroadcastReceiver(main)
      console.log('商米广播接收器注册成功')
      
      // 尝试绑定商米扫码服务（可选，主要用于主动控制扫码）
      try {
        await this.bindSunmiScannerService(main)
        console.log('商米扫码服务绑定成功')
      } catch (serviceError) {
        console.log('商米扫码服务绑定失败，但广播接收器已注册，扫码功能仍可用:', serviceError)
        // 服务绑定失败不影响广播接收功能
      }

      this.isInitialized = true
      console.log('商米扫码头初始化完成')
      
      return {
        success: true,
        message: '商米扫码功能已启用，请使用扫码枪扫描'
      }
    } catch (error) {
      console.error('商米扫码头初始化失败：', error)
      throw error
    }
  }

  /**
   * 绑定商米扫码服务
   */
  async bindSunmiScannerService(main) {
    try {
      const Intent = plus.android.importClass("android.content.Intent")
      const Context = plus.android.importClass("android.content.Context")
      
      // 创建服务连接 - 使用uniapp的方式实现ServiceConnection
      this.serviceConnection = plus.android.implements("android.content.ServiceConnection", {
        onServiceConnected: (name, service) => {
          console.log('商米扫码服务连接成功')
          try {
            // 尝试获取IScanInterface实例
            const IScanInterface = plus.android.importClass("com.sunmi.scanner.IScanInterface")
            if (IScanInterface && IScanInterface.Stub) {
              this.scanInterface = IScanInterface.Stub.asInterface(service)
              console.log('IScanInterface获取成功')
            }
          } catch (interfaceError) {
            console.log('获取IScanInterface失败，但服务已连接:', interfaceError)
          }
        },
        onServiceDisconnected: (name) => {
          console.log('商米扫码服务断开连接')
          this.scanInterface = null
        }
      })
      
      const intent = new Intent()
      intent.setPackage("com.sunmi.scanner")
      intent.setAction("com.sunmi.scanner.IScanInterface")
      
      // 尝试先启动服务
      try {
        main.startService(intent)
        console.log('商米扫码服务启动成功')
      } catch (startError) {
        console.log('启动服务失败，尝试直接绑定:', startError)
      }
      
      // 绑定服务
      const BIND_AUTO_CREATE = Context.BIND_AUTO_CREATE || 1
      const bindResult = main.bindService(intent, this.serviceConnection, BIND_AUTO_CREATE)
      
      if (!bindResult) {
        console.log('bindService返回false，可能服务不存在')
        throw new Error('绑定商米扫码服务失败：服务不可用')
      }
      
      console.log('商米扫码服务绑定请求已发送')
      
      // 等待服务连接
      return new Promise((resolve, reject) => {
        const timeout = setTimeout(() => {
          reject(new Error('服务连接超时'))
        }, 5000)
        
        const originalOnServiceConnected = this.serviceConnection.onServiceConnected
        this.serviceConnection.onServiceConnected = (name, service) => {
          clearTimeout(timeout)
          originalOnServiceConnected(name, service)
          resolve()
        }
      })
      
    } catch (error) {
      console.error('绑定商米扫码服务失败：', error)
      throw error
    }
  }

  /**
   * 注册商米广播接收器
   */
  async registerSunmiBroadcastReceiver(main) {
    try {
      const IntentFilter = plus.android.importClass("android.content.IntentFilter")
      const BroadcastReceiver = plus.android.importClass("android.content.BroadcastReceiver")
      
      const filter = new IntentFilter()
      filter.addAction("com.sunmi.scanner.ACTION_DATA_CODE_RECEIVED")

      this.scanReceiver = plus.android.implements(
        "io.dcloud.feature.internal.reflect.BroadcastReceiver",
        {
          onReceive: (context, intent) => {
            console.log('商米扫码广播接收，isPageActive:', this.isPageActive)
            // 只有当页面活动时才处理广播
            if (!this.isPageActive) return

            try {
              const scanResult = intent.getStringExtra("data")
              const sourceBytes = intent.getByteArrayExtra("source_byte")
              
              console.log('商米扫码结果:', scanResult, sourceBytes)
              
              if (scanResult && this.scanCallback) {
                // 根据扫码模式调用不同的处理逻辑
                this.handleScanResult(scanResult)
              }
            } catch (error) {
              console.error('处理商米广播数据时出错：', error)
              if (this.scanCallback) {
                this.scanCallback({
                  success: false,
                  error: error.message
                })
              }
            }
          }
        }
      )

      // 注册广播接收器
      main.registerReceiver(this.scanReceiver, filter)
      console.log('商米扫码广播接收器注册成功')
    } catch (error) {
      console.error('注册商米广播接收器失败：', error)
      throw error
    }
  }

  /**
   * 初始化新大陆扫码头（作为备选方案）
   */
  async initNewlandScanner() {
    try {
      const main = plus.android.runtimeMainActivity()

      // 配置新大陆扫码枪广播设置
      await this.configureNewlandScannerBroadcast(main)

      // 注册新大陆广播接收器
      await this.registerNewlandBroadcastReceiver(main)

      this.isInitialized = true
      console.log('新大陆扫码头初始化成功')
      
      return {
        success: true,
        message: '新大陆扫码功能已启用，请使用扫码枪扫描'
      }
    } catch (error) {
      console.error('新大陆扫码头初始化失败：', error)
      throw error
    }
  }

  /**
   * 配置新大陆扫码枪广播设置
   */
  async configureNewlandScannerBroadcast(main) {
    try {
      const Intent = plus.android.importClass("android.content.Intent")
      const intent = new Intent("com.android.scanner.service_settings")
      
      intent.putExtra(
        "action_barcode_broadcast",
        "com.android.server.scannerservice.broadcast"
      )
      intent.putExtra("key_barcode_broadcast", "scannerdata")
      
      main.sendBroadcast(intent)
      console.log('新大陆扫码枪广播配置完成')
    } catch (error) {
      console.error('配置新大陆扫码枪广播失败：', error)
      throw error
    }
  }

  /**
   * 注册新大陆广播接收器
   */
  async registerNewlandBroadcastReceiver(main) {
    try {
      const IntentFilter = plus.android.importClass("android.content.IntentFilter")
      const filter = new IntentFilter()
      filter.addAction("com.android.server.scannerservice.broadcast")

      this.scanReceiver = plus.android.implements(
        "io.dcloud.feature.internal.reflect.BroadcastReceiver",
        {
          onReceive: (context, intent) => {
			      console.log('新大陆扫码广播接收，isPageActive:',this.isPageActive)
            // 只有当页面活动时才处理广播
            if (!this.isPageActive) return

            try {
              const scanResult = intent.getStringExtra("scannerdata")
              console.log('新大陆扫码结果:', scanResult)
              
              if (scanResult && this.scanCallback) {
                // 根据扫码模式调用不同的处理逻辑
                this.handleScanResult(scanResult)
              }
            } catch (error) {
              console.error('处理新大陆广播数据时出错：', error)
              if (this.scanCallback) {
                this.scanCallback({
                  success: false,
                  error: error.message
                })
              }
            }
          }
        }
      )

      // 注册广播接收器
      main.registerReceiver(this.scanReceiver, filter)
      console.log('新大陆扫码广播接收器注册成功')
    } catch (error) {
      console.error('注册新大陆广播接收器失败：', error)
      throw error
    }
  }

  /**
   * 使用uni.scanCode作为降级方案
   */
  useUniScanCode() {
    console.log('使用uni.scanCode扫码')
    uni.scanCode({
      success: (res) => {
        if (this.scanCallback) {
          // 根据扫码模式调用不同的处理逻辑
          this.handleScanResult(res.result)
        }
      },
      fail: (err) => {
        console.error('扫码失败:', err)
        if (this.scanCallback) {
          this.scanCallback({
            success: false,
            error: err.errMsg || '扫码失败'
          })
        }
      }
    })

    return {
      success: true,
      message: '使用系统扫码功能'
    }
  }

  /**
   * 设置页面活跃状态
   */
  setPageActive(isActive) {
    this.isPageActive = isActive
  }

  /**
   * 销毁扫码功能
   */
  destroy() {
    if (this.scanReceiver && uni.getSystemInfoSync().platform === 'android') {
      try {
        const main = plus.android.runtimeMainActivity()
        main.unregisterReceiver(this.scanReceiver)
        console.log('扫码广播接收器已注销')
      } catch (error) {
        console.error('注销广播接收器失败：', error)
      }
    }
    
    // 解绑商米扫码服务
    if (this.serviceConnection && uni.getSystemInfoSync().platform === 'android') {
      try {
        const main = plus.android.runtimeMainActivity()
        main.unbindService(this.serviceConnection)
        console.log('商米扫码服务已解绑')
      } catch (error) {
        console.error('解绑商米扫码服务失败：', error)
      }
    }
    
    this.scanReceiver = null
    this.serviceConnection = null
    this.scanInterface = null
    this.scanCallback = null
    this.isInitialized = false
  }

  /**
   * 手动触发扫码（用于按钮点击等场景）
   */
  startScan() {
    if (!this.isInitialized) {
      return this.useUniScanCode()
    }
    
    // 对于扫码头，通常是自动触发的
    // 这里可以显示提示信息
    uni.showToast({
      title: '请使用扫码枪扫描',
      icon: 'none',
      duration: 2000
    })
  }

  /**
   * 根据扫码模式处理扫码结果
   * @param {String} scanResult 扫码结果
   */
  async handleScanResult(scanResult) {
    console.log('处理扫码结果:', scanResult, '模式:', this.scanMode)
    
    try {
      let apiResult = null
      
      // 根据扫码模式调用不同的API
      switch (this.scanMode) {
        case 'report':
          // 上报模式：检查机器是否可以上报异常
          apiResult = await API.qr.scanForReport(scanResult)
          break
        case 'repair':
          // 维修模式：获取机器的待维修异常
          apiResult = await API.qr.scanForRepair(scanResult)
          break
        default:
          // 通用模式：获取机器基本信息
          apiResult = await API.qr.scan(scanResult)
          break
      }
      
      // 调用回调函数，传递API结果
      if (this.scanCallback) {
        this.scanCallback({
          success: true,
          result: scanResult,
          scanType: 'QR_CODE',
          mode: this.scanMode,
          data: apiResult
        })
      }
    } catch (error) {
      console.error('API调用失败:', error)
      
      // 如果API调用失败，仍然返回扫码结果，但标记API错误
      if (this.scanCallback) {
        this.scanCallback({
          success: true,
          result: scanResult,
          scanType: 'QR_CODE',
          mode: this.scanMode,
          apiError: error.message,
          data: null
        })
      }
    }
  }

  /**
   * 设置扫码模式
   * @param {String} mode 扫码模式
   */
  setScanMode(mode) {
    this.scanMode = mode
    console.log('扫码模式已设置为:', mode)
  }
}

// 创建单例实例
const scannerUtils = new ScannerUtils()

export default scannerUtils
