[!symlink] skip
env GO111MODULE=off

mkdir $WORK/tmp/gopath/src/x/y/_vendor/src/x
symlink $WORK/tmp/gopath/src/x/y/_vendor/src/x/y -> ../../..
mkdir $WORK/tmp/gopath/src/x/y/_vendor/src/x/y/w
cp w.go $WORK/tmp/gopath/src/x/y/w/w.go
symlink $WORK/tmp/gopath/src/x/y/w/vendor -> ../_vendor/src
mkdir $WORK/tmp/gopath/src/x/y/_vendor/src/x/y/z
cp z.go $WORK/tmp/gopath/src/x/y/z/z.go

env GOPATH=$WORK/tmp/gopath/src/x/y/_vendor${:}$WORK/tmp/gopath
cd $WORK/tmp/gopath/src
go list ./...

-- w.go --
package w

import "x/y/z"
-- z.go --
package z
