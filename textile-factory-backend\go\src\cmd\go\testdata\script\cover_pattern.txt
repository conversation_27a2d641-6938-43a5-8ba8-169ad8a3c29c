[compiler:gccgo] skip

# If coverpkg=m/sleepy... expands by package loading
# (as opposed to pattern matching on deps)
# then it will try to load sleepy<PERSON>, which does not compile,
# and the test command will fail.
! go list m/sleepy...
go test -c -n -coverprofile=$TMPDIR/cover.out -coverpkg=m/sleepy... -run=^$ m/sleepy1

-- go.mod --
module m

go 1.16
-- sleepy1/p_test.go --
package p

import (
	"testing"
	"time"
)

func Test1(t *testing.T) {
	time.Sleep(200 * time.Millisecond)
}
-- sleepy2/p_test.go --
package p

import (
	"testing"
	"time"
)

func Test1(t *testing.T) {
	time.Sleep(200 * time.Millisecond)
}
-- sleepybad/p.go --
package p

import ^

var _ = io.DoesNotExist
