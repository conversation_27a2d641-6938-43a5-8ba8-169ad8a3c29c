# Test go build -pgo=auto flag with multiple main packages.

go install -a -n -pgo=auto ./a ./b ./nopgo

# a/default.pgo applies to package a and (transitive)
# dependencies.
stderr 'compile.*-pgoprofile=.*a(/|\\\\)default\.pgo.*a(/|\\\\)a\.go'
stderr 'compile.*-pgoprofile=.*a(/|\\\\)default\.pgo.*dep(/|\\\\)dep\.go'
stderr 'compile.*-pgoprofile=.*a(/|\\\\)default\.pgo.*dep2(/|\\\\)dep2\.go'
stderr -count=1 'compile.*-pgoprofile=.*a(/|\\\\)default\.pgo.*dep3(/|\\\\)dep3\.go'

# b/default.pgo applies to package b and (transitive)
# dependencies.
stderr 'compile.*-pgoprofile=.*b(/|\\\\)default\.pgo.*b(/|\\\\)b\.go'
stderr 'compile.*-pgoprofile=.*b(/|\\\\)default\.pgo.*dep(/|\\\\)dep\.go'
stderr 'compile.*-pgoprofile=.*b(/|\\\\)default\.pgo.*dep2(/|\\\\)dep2\.go'
stderr -count=1 'compile.*-pgoprofile=.*b(/|\\\\)default\.pgo.*dep3(/|\\\\)dep3\.go'

# nopgo should be built without PGO.
! stderr 'compile.*-pgoprofile=.*nopgo(/|\\\\)nopgo\.go'

# Dependencies should also be built without PGO.
# Here we want to match a compile action without -pgoprofile,
# by matching 3 occurrences of "compile dep.go", among which
# 2 of them have -pgoprofile (therefore one without).
stderr -count=3 'compile.*dep(/|\\\\)dep.go'
stderr -count=2 'compile.*-pgoprofile=.*dep(/|\\\\)dep\.go'

stderr -count=3 'compile.*dep2(/|\\\\)dep2.go'
stderr -count=2 'compile.*-pgoprofile=.*dep2(/|\\\\)dep2\.go'

stderr -count=3 'compile.*dep3(/|\\\\)dep3.go'
stderr -count=2 'compile.*-pgoprofile=.*dep3(/|\\\\)dep3\.go'

# check that pgo appears or not in build info as expected
stderr 'path\\ttest/a\\n.*build\\t-pgo=.*a(/|\\\\)default\.pgo'
stderr 'path\\ttest/b\\n.*build\\t-pgo=.*b(/|\\\\)default\.pgo'
! stderr 'path\\ttest/nopgo\\n.*build\\t-pgo='

# go test works the same way
go test -a -n -pgo=auto ./a ./b ./nopgo
stderr 'compile.*-pgoprofile=.*a(/|\\\\)default\.pgo.*a(/|\\\\)a_test\.go'
stderr 'compile.*-pgoprofile=.*a(/|\\\\)default\.pgo.*dep(/|\\\\)dep\.go'
stderr 'compile.*-pgoprofile=.*b(/|\\\\)default\.pgo.*b(/|\\\\)b_test\.go'
stderr 'compile.*-pgoprofile=.*b(/|\\\\)default\.pgo.*dep(/|\\\\)dep\.go'
! stderr 'compile.*-pgoprofile=.*nopgo(/|\\\\)nopgo_test\.go'

# test-only dependencies also have profiles attached
stderr 'compile.*-pgoprofile=.*a(/|\\\\)default\.pgo.*testdep(/|\\\\)testdep\.go'
stderr 'compile.*-pgoprofile=.*b(/|\\\\)default\.pgo.*testdep(/|\\\\)testdep\.go'
stderr 'compile.*-pgoprofile=.*a(/|\\\\)default\.pgo.*testdep2(/|\\\\)testdep2\.go'
stderr 'compile.*-pgoprofile=.*b(/|\\\\)default\.pgo.*testdep2(/|\\\\)testdep2\.go'

# go list -deps prints packages built multiple times.
go list -pgo=auto -deps ./a ./b ./nopgo
stdout 'test/dep \[test/a\]'
stdout 'test/dep \[test/b\]'
stdout 'test/dep$'

# Here we have 3 main packages, a, b, and nopgo, where a and b each has
# its own default.pgo profile, and nopgo has none.
# All 3 main packages import dep and dep2, both of which then import dep3
# (a diamond-shape import graph).
-- go.mod --
module test
go 1.20
-- a/a.go --
package main
import _ "test/dep"
import _ "test/dep2"
func main() {}
-- a/a_test.go --
package main
import "testing"
import _ "test/testdep"
func TestA(*testing.T) {}
-- a/default.pgo --
-- b/b.go --
package main
import _ "test/dep"
import _ "test/dep2"
func main() {}
-- b/b_test.go --
package main
import "testing"
import _ "test/testdep"
func TestB(*testing.T) {}
-- b/default.pgo --
-- nopgo/nopgo.go --
package main
import _ "test/dep"
import _ "test/dep2"
func main() {}
-- nopgo/nopgo_test.go --
package main
import "testing"
func TestNopgo(*testing.T) {}
-- dep/dep.go --
package dep
import _ "test/dep3"
-- dep2/dep2.go --
package dep2
-- dep3/dep3.go --
package dep3
-- testdep/testdep.go --
package testdep
import _ "test/testdep2"
-- testdep2/testdep2.go --
package testdep2
