// Code generated by avx512test. DO NOT EDIT.

#include "../../../../../../runtime/textflag.h"

TEXT asmtest_avx512dq(SB), NOSPLIT, $0
	KADDB K3, K1, K6                                   // c5f54af3
	KADDB K1, K1, K6                                   // c5f54af1
	KADDB K3, K5, K6                                   // c5d54af3
	KADDB K1, K5, K6                                   // c5d54af1
	KADDB K3, K1, K5                                   // c5f54aeb
	KADDB K1, K1, K5                                   // c5f54ae9
	KADDB K3, K5, K5                                   // c5d54aeb
	KADDB K1, K5, K5                                   // c5d54ae9
	KADDW K6, K6, K1                                   // c5cc4ace
	KADDW K4, K6, K1                                   // c5cc4acc
	KADDW K6, K7, K1                                   // c5c44ace
	KADDW K4, K7, K1                                   // c5c44acc
	KADDW K6, K6, K3                                   // c5cc4ade
	KADDW K4, K6, K3                                   // c5cc4adc
	KADDW K6, K7, K3                                   // c5c44ade
	KADDW K4, K7, K3                                   // c5c44adc
	KANDB K2, K4, K4                                   // c5dd41e2
	KANDB K7, K4, K4                                   // c5dd41e7
	KANDB K2, K5, K4                                   // c5d541e2
	KANDB K7, K5, K4                                   // c5d541e7
	KANDB K2, K4, K6                                   // c5dd41f2
	KANDB K7, K4, K6                                   // c5dd41f7
	KANDB K2, K5, K6                                   // c5d541f2
	KANDB K7, K5, K6                                   // c5d541f7
	KANDNB K7, K5, K3                                  // c5d542df
	KANDNB K6, K5, K3                                  // c5d542de
	KANDNB K7, K4, K3                                  // c5dd42df
	KANDNB K6, K4, K3                                  // c5dd42de
	KANDNB K7, K5, K1                                  // c5d542cf
	KANDNB K6, K5, K1                                  // c5d542ce
	KANDNB K7, K4, K1                                  // c5dd42cf
	KANDNB K6, K4, K1                                  // c5dd42ce
	KMOVB K7, 17(SP)                                   // c5f9917c2411
	KMOVB K6, 17(SP)                                   // c5f991742411
	KMOVB K7, -17(BP)(SI*4)                            // c5f9917cb5ef
	KMOVB K6, -17(BP)(SI*4)                            // c5f99174b5ef
	KMOVB K4, AX                                       // c5f993c4
	KMOVB K6, AX                                       // c5f993c6
	KMOVB K4, R9                                       // c57993cc
	KMOVB K6, R9                                       // c57993ce
	KMOVB K5, K0                                       // c5f990c5
	KMOVB K4, K0                                       // c5f990c4
	KMOVB 7(AX), K0                                    // c5f9904007
	KMOVB (DI), K0                                     // c5f99007
	KMOVB K5, K7                                       // c5f990fd
	KMOVB K4, K7                                       // c5f990fc
	KMOVB 7(AX), K7                                    // c5f9907807
	KMOVB (DI), K7                                     // c5f9903f
	KMOVB CX, K4                                       // c5f992e1
	KMOVB SP, K4                                       // c5f992e4
	KMOVB CX, K6                                       // c5f992f1
	KMOVB SP, K6                                       // c5f992f4
	KNOTB K1, K4                                       // c5f944e1
	KNOTB K3, K4                                       // c5f944e3
	KNOTB K1, K6                                       // c5f944f1
	KNOTB K3, K6                                       // c5f944f3
	KORB K3, K1, K6                                    // c5f545f3
	KORB K1, K1, K6                                    // c5f545f1
	KORB K3, K5, K6                                    // c5d545f3
	KORB K1, K5, K6                                    // c5d545f1
	KORB K3, K1, K5                                    // c5f545eb
	KORB K1, K1, K5                                    // c5f545e9
	KORB K3, K5, K5                                    // c5d545eb
	KORB K1, K5, K5                                    // c5d545e9
	KORTESTB K6, K1                                    // c5f998ce
	KORTESTB K7, K1                                    // c5f998cf
	KORTESTB K6, K3                                    // c5f998de
	KORTESTB K7, K3                                    // c5f998df
	KSHIFTLB $127, K4, K7                              // c4e37932fc7f
	KSHIFTLB $127, K6, K7                              // c4e37932fe7f
	KSHIFTLB $127, K4, K6                              // c4e37932f47f
	KSHIFTLB $127, K6, K6                              // c4e37932f67f
	KSHIFTRB $42, K4, K4                               // c4e37930e42a
	KSHIFTRB $42, K5, K4                               // c4e37930e52a
	KSHIFTRB $42, K4, K6                               // c4e37930f42a
	KSHIFTRB $42, K5, K6                               // c4e37930f52a
	KTESTB K4, K7                                      // c5f999fc
	KTESTB K6, K7                                      // c5f999fe
	KTESTB K4, K6                                      // c5f999f4
	KTESTB K6, K6                                      // c5f999f6
	KTESTW K6, K6                                      // c5f899f6
	KTESTW K4, K6                                      // c5f899f4
	KTESTW K6, K7                                      // c5f899fe
	KTESTW K4, K7                                      // c5f899fc
	KXNORB K5, K0, K4                                  // c5fd46e5
	KXNORB K4, K0, K4                                  // c5fd46e4
	KXNORB K5, K7, K4                                  // c5c546e5
	KXNORB K4, K7, K4                                  // c5c546e4
	KXNORB K5, K0, K6                                  // c5fd46f5
	KXNORB K4, K0, K6                                  // c5fd46f4
	KXNORB K5, K7, K6                                  // c5c546f5
	KXNORB K4, K7, K6                                  // c5c546f4
	KXORB K5, K3, K1                                   // c5e547cd
	KXORB K4, K3, K1                                   // c5e547cc
	KXORB K5, K1, K1                                   // c5f547cd
	KXORB K4, K1, K1                                   // c5f547cc
	KXORB K5, K3, K5                                   // c5e547ed
	KXORB K4, K3, K5                                   // c5e547ec
	KXORB K5, K1, K5                                   // c5f547ed
	KXORB K4, K1, K5                                   // c5f547ec
	VANDNPD X15, X0, K4, X22                           // 62c1fd0c55f7
	VANDNPD X11, X0, K4, X22                           // 62c1fd0c55f3
	VANDNPD X0, X0, K4, X22                            // 62e1fd0c55f0
	VANDNPD (R8), X0, K4, X22                          // 62c1fd0c5530
	VANDNPD 15(DX)(BX*2), X0, K4, X22                  // 62e1fd0c55b45a0f000000
	VANDNPD X15, X17, K4, X22                          // 62c1f50455f7
	VANDNPD X11, X17, K4, X22                          // 62c1f50455f3
	VANDNPD X0, X17, K4, X22                           // 62e1f50455f0
	VANDNPD (R8), X17, K4, X22                         // 62c1f5045530
	VANDNPD 15(DX)(BX*2), X17, K4, X22                 // 62e1f50455b45a0f000000
	VANDNPD X15, X7, K4, X22                           // 62c1c50c55f7
	VANDNPD X11, X7, K4, X22                           // 62c1c50c55f3
	VANDNPD X0, X7, K4, X22                            // 62e1c50c55f0
	VANDNPD (R8), X7, K4, X22                          // 62c1c50c5530
	VANDNPD 15(DX)(BX*2), X7, K4, X22                  // 62e1c50c55b45a0f000000
	VANDNPD X15, X0, K4, X5                            // 62d1fd0c55ef
	VANDNPD X11, X0, K4, X5                            // 62d1fd0c55eb
	VANDNPD X0, X0, K4, X5                             // 62f1fd0c55e8
	VANDNPD (R8), X0, K4, X5                           // 62d1fd0c5528
	VANDNPD 15(DX)(BX*2), X0, K4, X5                   // 62f1fd0c55ac5a0f000000
	VANDNPD X15, X17, K4, X5                           // 62d1f50455ef
	VANDNPD X11, X17, K4, X5                           // 62d1f50455eb
	VANDNPD X0, X17, K4, X5                            // 62f1f50455e8
	VANDNPD (R8), X17, K4, X5                          // 62d1f5045528
	VANDNPD 15(DX)(BX*2), X17, K4, X5                  // 62f1f50455ac5a0f000000
	VANDNPD X15, X7, K4, X5                            // 62d1c50c55ef
	VANDNPD X11, X7, K4, X5                            // 62d1c50c55eb
	VANDNPD X0, X7, K4, X5                             // 62f1c50c55e8
	VANDNPD (R8), X7, K4, X5                           // 62d1c50c5528
	VANDNPD 15(DX)(BX*2), X7, K4, X5                   // 62f1c50c55ac5a0f000000
	VANDNPD X15, X0, K4, X14                           // 6251fd0c55f7
	VANDNPD X11, X0, K4, X14                           // 6251fd0c55f3
	VANDNPD X0, X0, K4, X14                            // 6271fd0c55f0
	VANDNPD (R8), X0, K4, X14                          // 6251fd0c5530
	VANDNPD 15(DX)(BX*2), X0, K4, X14                  // 6271fd0c55b45a0f000000
	VANDNPD X15, X17, K4, X14                          // 6251f50455f7
	VANDNPD X11, X17, K4, X14                          // 6251f50455f3
	VANDNPD X0, X17, K4, X14                           // 6271f50455f0
	VANDNPD (R8), X17, K4, X14                         // 6251f5045530
	VANDNPD 15(DX)(BX*2), X17, K4, X14                 // 6271f50455b45a0f000000
	VANDNPD X15, X7, K4, X14                           // 6251c50c55f7
	VANDNPD X11, X7, K4, X14                           // 6251c50c55f3
	VANDNPD X0, X7, K4, X14                            // 6271c50c55f0
	VANDNPD (R8), X7, K4, X14                          // 6251c50c5530
	VANDNPD 15(DX)(BX*2), X7, K4, X14                  // 6271c50c55b45a0f000000
	VANDNPD Y17, Y12, K5, Y0                           // 62b19d2d55c1
	VANDNPD Y7, Y12, K5, Y0                            // 62f19d2d55c7
	VANDNPD Y9, Y12, K5, Y0                            // 62d19d2d55c1
	VANDNPD 99(R15)(R15*8), Y12, K5, Y0                // 62919d2d5584ff63000000
	VANDNPD 7(AX)(CX*8), Y12, K5, Y0                   // 62f19d2d5584c807000000
	VANDNPD Y17, Y1, K5, Y0                            // 62b1f52d55c1
	VANDNPD Y7, Y1, K5, Y0                             // 62f1f52d55c7
	VANDNPD Y9, Y1, K5, Y0                             // 62d1f52d55c1
	VANDNPD 99(R15)(R15*8), Y1, K5, Y0                 // 6291f52d5584ff63000000
	VANDNPD 7(AX)(CX*8), Y1, K5, Y0                    // 62f1f52d5584c807000000
	VANDNPD Y17, Y14, K5, Y0                           // 62b18d2d55c1
	VANDNPD Y7, Y14, K5, Y0                            // 62f18d2d55c7
	VANDNPD Y9, Y14, K5, Y0                            // 62d18d2d55c1
	VANDNPD 99(R15)(R15*8), Y14, K5, Y0                // 62918d2d5584ff63000000
	VANDNPD 7(AX)(CX*8), Y14, K5, Y0                   // 62f18d2d5584c807000000
	VANDNPD Y17, Y12, K5, Y22                          // 62a19d2d55f1
	VANDNPD Y7, Y12, K5, Y22                           // 62e19d2d55f7
	VANDNPD Y9, Y12, K5, Y22                           // 62c19d2d55f1
	VANDNPD 99(R15)(R15*8), Y12, K5, Y22               // 62819d2d55b4ff63000000
	VANDNPD 7(AX)(CX*8), Y12, K5, Y22                  // 62e19d2d55b4c807000000
	VANDNPD Y17, Y1, K5, Y22                           // 62a1f52d55f1
	VANDNPD Y7, Y1, K5, Y22                            // 62e1f52d55f7
	VANDNPD Y9, Y1, K5, Y22                            // 62c1f52d55f1
	VANDNPD 99(R15)(R15*8), Y1, K5, Y22                // 6281f52d55b4ff63000000
	VANDNPD 7(AX)(CX*8), Y1, K5, Y22                   // 62e1f52d55b4c807000000
	VANDNPD Y17, Y14, K5, Y22                          // 62a18d2d55f1
	VANDNPD Y7, Y14, K5, Y22                           // 62e18d2d55f7
	VANDNPD Y9, Y14, K5, Y22                           // 62c18d2d55f1
	VANDNPD 99(R15)(R15*8), Y14, K5, Y22               // 62818d2d55b4ff63000000
	VANDNPD 7(AX)(CX*8), Y14, K5, Y22                  // 62e18d2d55b4c807000000
	VANDNPD Y17, Y12, K5, Y13                          // 62319d2d55e9
	VANDNPD Y7, Y12, K5, Y13                           // 62719d2d55ef
	VANDNPD Y9, Y12, K5, Y13                           // 62519d2d55e9
	VANDNPD 99(R15)(R15*8), Y12, K5, Y13               // 62119d2d55acff63000000
	VANDNPD 7(AX)(CX*8), Y12, K5, Y13                  // 62719d2d55acc807000000
	VANDNPD Y17, Y1, K5, Y13                           // 6231f52d55e9
	VANDNPD Y7, Y1, K5, Y13                            // 6271f52d55ef
	VANDNPD Y9, Y1, K5, Y13                            // 6251f52d55e9
	VANDNPD 99(R15)(R15*8), Y1, K5, Y13                // 6211f52d55acff63000000
	VANDNPD 7(AX)(CX*8), Y1, K5, Y13                   // 6271f52d55acc807000000
	VANDNPD Y17, Y14, K5, Y13                          // 62318d2d55e9
	VANDNPD Y7, Y14, K5, Y13                           // 62718d2d55ef
	VANDNPD Y9, Y14, K5, Y13                           // 62518d2d55e9
	VANDNPD 99(R15)(R15*8), Y14, K5, Y13               // 62118d2d55acff63000000
	VANDNPD 7(AX)(CX*8), Y14, K5, Y13                  // 62718d2d55acc807000000
	VANDNPD Z20, Z0, K7, Z7                            // 62b1fd4f55fc
	VANDNPD Z28, Z0, K7, Z7                            // 6291fd4f55fc
	VANDNPD 99(R15)(R15*8), Z0, K7, Z7                 // 6291fd4f55bcff63000000
	VANDNPD 7(AX)(CX*8), Z0, K7, Z7                    // 62f1fd4f55bcc807000000
	VANDNPD Z20, Z6, K7, Z7                            // 62b1cd4f55fc
	VANDNPD Z28, Z6, K7, Z7                            // 6291cd4f55fc
	VANDNPD 99(R15)(R15*8), Z6, K7, Z7                 // 6291cd4f55bcff63000000
	VANDNPD 7(AX)(CX*8), Z6, K7, Z7                    // 62f1cd4f55bcc807000000
	VANDNPD Z20, Z0, K7, Z9                            // 6231fd4f55cc
	VANDNPD Z28, Z0, K7, Z9                            // 6211fd4f55cc
	VANDNPD 99(R15)(R15*8), Z0, K7, Z9                 // 6211fd4f558cff63000000
	VANDNPD 7(AX)(CX*8), Z0, K7, Z9                    // 6271fd4f558cc807000000
	VANDNPD Z20, Z6, K7, Z9                            // 6231cd4f55cc
	VANDNPD Z28, Z6, K7, Z9                            // 6211cd4f55cc
	VANDNPD 99(R15)(R15*8), Z6, K7, Z9                 // 6211cd4f558cff63000000
	VANDNPD 7(AX)(CX*8), Z6, K7, Z9                    // 6271cd4f558cc807000000
	VANDNPS X15, X25, K7, X18                          // 62c1340755d7
	VANDNPS X28, X25, K7, X18                          // 6281340755d4
	VANDNPS 17(SP)(BP*1), X25, K7, X18                 // 62e1340755942c11000000
	VANDNPS -7(CX)(DX*8), X25, K7, X18                 // 62e134075594d1f9ffffff
	VANDNPS X15, X3, K7, X18                           // 62c1640f55d7
	VANDNPS X28, X3, K7, X18                           // 6281640f55d4
	VANDNPS 17(SP)(BP*1), X3, K7, X18                  // 62e1640f55942c11000000
	VANDNPS -7(CX)(DX*8), X3, K7, X18                  // 62e1640f5594d1f9ffffff
	VANDNPS X15, X18, K7, X18                          // 62c16c0755d7
	VANDNPS X28, X18, K7, X18                          // 62816c0755d4
	VANDNPS 17(SP)(BP*1), X18, K7, X18                 // 62e16c0755942c11000000
	VANDNPS -7(CX)(DX*8), X18, K7, X18                 // 62e16c075594d1f9ffffff
	VANDNPS X15, X25, K7, X8                           // 6251340755c7
	VANDNPS X28, X25, K7, X8                           // 6211340755c4
	VANDNPS 17(SP)(BP*1), X25, K7, X8                  // 6271340755842c11000000
	VANDNPS -7(CX)(DX*8), X25, K7, X8                  // 627134075584d1f9ffffff
	VANDNPS X15, X3, K7, X8                            // 6251640f55c7
	VANDNPS X28, X3, K7, X8                            // 6211640f55c4
	VANDNPS 17(SP)(BP*1), X3, K7, X8                   // 6271640f55842c11000000
	VANDNPS -7(CX)(DX*8), X3, K7, X8                   // 6271640f5584d1f9ffffff
	VANDNPS X15, X18, K7, X8                           // 62516c0755c7
	VANDNPS X28, X18, K7, X8                           // 62116c0755c4
	VANDNPS 17(SP)(BP*1), X18, K7, X8                  // 62716c0755842c11000000
	VANDNPS -7(CX)(DX*8), X18, K7, X8                  // 62716c075584d1f9ffffff
	VANDNPS X15, X25, K7, X27                          // 6241340755df
	VANDNPS X28, X25, K7, X27                          // 6201340755dc
	VANDNPS 17(SP)(BP*1), X25, K7, X27                 // 62613407559c2c11000000
	VANDNPS -7(CX)(DX*8), X25, K7, X27                 // 62613407559cd1f9ffffff
	VANDNPS X15, X3, K7, X27                           // 6241640f55df
	VANDNPS X28, X3, K7, X27                           // 6201640f55dc
	VANDNPS 17(SP)(BP*1), X3, K7, X27                  // 6261640f559c2c11000000
	VANDNPS -7(CX)(DX*8), X3, K7, X27                  // 6261640f559cd1f9ffffff
	VANDNPS X15, X18, K7, X27                          // 62416c0755df
	VANDNPS X28, X18, K7, X27                          // 62016c0755dc
	VANDNPS 17(SP)(BP*1), X18, K7, X27                 // 62616c07559c2c11000000
	VANDNPS -7(CX)(DX*8), X18, K7, X27                 // 62616c07559cd1f9ffffff
	VANDNPS Y2, Y28, K6, Y31                           // 62611c2655fa
	VANDNPS Y21, Y28, K6, Y31                          // 62211c2655fd
	VANDNPS Y12, Y28, K6, Y31                          // 62411c2655fc
	VANDNPS (AX), Y28, K6, Y31                         // 62611c265538
	VANDNPS 7(SI), Y28, K6, Y31                        // 62611c2655be07000000
	VANDNPS Y2, Y13, K6, Y31                           // 6261142e55fa
	VANDNPS Y21, Y13, K6, Y31                          // 6221142e55fd
	VANDNPS Y12, Y13, K6, Y31                          // 6241142e55fc
	VANDNPS (AX), Y13, K6, Y31                         // 6261142e5538
	VANDNPS 7(SI), Y13, K6, Y31                        // 6261142e55be07000000
	VANDNPS Y2, Y7, K6, Y31                            // 6261442e55fa
	VANDNPS Y21, Y7, K6, Y31                           // 6221442e55fd
	VANDNPS Y12, Y7, K6, Y31                           // 6241442e55fc
	VANDNPS (AX), Y7, K6, Y31                          // 6261442e5538
	VANDNPS 7(SI), Y7, K6, Y31                         // 6261442e55be07000000
	VANDNPS Y2, Y28, K6, Y8                            // 62711c2655c2
	VANDNPS Y21, Y28, K6, Y8                           // 62311c2655c5
	VANDNPS Y12, Y28, K6, Y8                           // 62511c2655c4
	VANDNPS (AX), Y28, K6, Y8                          // 62711c265500
	VANDNPS 7(SI), Y28, K6, Y8                         // 62711c26558607000000
	VANDNPS Y2, Y13, K6, Y8                            // 6271142e55c2
	VANDNPS Y21, Y13, K6, Y8                           // 6231142e55c5
	VANDNPS Y12, Y13, K6, Y8                           // 6251142e55c4
	VANDNPS (AX), Y13, K6, Y8                          // 6271142e5500
	VANDNPS 7(SI), Y13, K6, Y8                         // 6271142e558607000000
	VANDNPS Y2, Y7, K6, Y8                             // 6271442e55c2
	VANDNPS Y21, Y7, K6, Y8                            // 6231442e55c5
	VANDNPS Y12, Y7, K6, Y8                            // 6251442e55c4
	VANDNPS (AX), Y7, K6, Y8                           // 6271442e5500
	VANDNPS 7(SI), Y7, K6, Y8                          // 6271442e558607000000
	VANDNPS Y2, Y28, K6, Y1                            // 62f11c2655ca
	VANDNPS Y21, Y28, K6, Y1                           // 62b11c2655cd
	VANDNPS Y12, Y28, K6, Y1                           // 62d11c2655cc
	VANDNPS (AX), Y28, K6, Y1                          // 62f11c265508
	VANDNPS 7(SI), Y28, K6, Y1                         // 62f11c26558e07000000
	VANDNPS Y2, Y13, K6, Y1                            // 62f1142e55ca
	VANDNPS Y21, Y13, K6, Y1                           // 62b1142e55cd
	VANDNPS Y12, Y13, K6, Y1                           // 62d1142e55cc
	VANDNPS (AX), Y13, K6, Y1                          // 62f1142e5508
	VANDNPS 7(SI), Y13, K6, Y1                         // 62f1142e558e07000000
	VANDNPS Y2, Y7, K6, Y1                             // 62f1442e55ca
	VANDNPS Y21, Y7, K6, Y1                            // 62b1442e55cd
	VANDNPS Y12, Y7, K6, Y1                            // 62d1442e55cc
	VANDNPS (AX), Y7, K6, Y1                           // 62f1442e5508
	VANDNPS 7(SI), Y7, K6, Y1                          // 62f1442e558e07000000
	VANDNPS Z12, Z9, K3, Z3                            // 62d1344b55dc
	VANDNPS Z22, Z9, K3, Z3                            // 62b1344b55de
	VANDNPS (AX), Z9, K3, Z3                           // 62f1344b5518
	VANDNPS 7(SI), Z9, K3, Z3                          // 62f1344b559e07000000
	VANDNPS Z12, Z19, K3, Z3                           // 62d1644355dc
	VANDNPS Z22, Z19, K3, Z3                           // 62b1644355de
	VANDNPS (AX), Z19, K3, Z3                          // 62f164435518
	VANDNPS 7(SI), Z19, K3, Z3                         // 62f16443559e07000000
	VANDNPS Z12, Z9, K3, Z30                           // 6241344b55f4
	VANDNPS Z22, Z9, K3, Z30                           // 6221344b55f6
	VANDNPS (AX), Z9, K3, Z30                          // 6261344b5530
	VANDNPS 7(SI), Z9, K3, Z30                         // 6261344b55b607000000
	VANDNPS Z12, Z19, K3, Z30                          // 6241644355f4
	VANDNPS Z22, Z19, K3, Z30                          // 6221644355f6
	VANDNPS (AX), Z19, K3, Z30                         // 626164435530
	VANDNPS 7(SI), Z19, K3, Z30                        // 6261644355b607000000
	VANDPD X22, X24, K7, X7                            // 62b1bd0754fe
	VANDPD X1, X24, K7, X7                             // 62f1bd0754f9
	VANDPD X11, X24, K7, X7                            // 62d1bd0754fb
	VANDPD -17(BP)(SI*2), X24, K7, X7                  // 62f1bd0754bc75efffffff
	VANDPD 7(AX)(CX*2), X24, K7, X7                    // 62f1bd0754bc4807000000
	VANDPD X22, X7, K7, X7                             // 62b1c50f54fe
	VANDPD X1, X7, K7, X7                              // 62f1c50f54f9
	VANDPD X11, X7, K7, X7                             // 62d1c50f54fb
	VANDPD -17(BP)(SI*2), X7, K7, X7                   // 62f1c50f54bc75efffffff
	VANDPD 7(AX)(CX*2), X7, K7, X7                     // 62f1c50f54bc4807000000
	VANDPD X22, X0, K7, X7                             // 62b1fd0f54fe
	VANDPD X1, X0, K7, X7                              // 62f1fd0f54f9
	VANDPD X11, X0, K7, X7                             // 62d1fd0f54fb
	VANDPD -17(BP)(SI*2), X0, K7, X7                   // 62f1fd0f54bc75efffffff
	VANDPD 7(AX)(CX*2), X0, K7, X7                     // 62f1fd0f54bc4807000000
	VANDPD X22, X24, K7, X13                           // 6231bd0754ee
	VANDPD X1, X24, K7, X13                            // 6271bd0754e9
	VANDPD X11, X24, K7, X13                           // 6251bd0754eb
	VANDPD -17(BP)(SI*2), X24, K7, X13                 // 6271bd0754ac75efffffff
	VANDPD 7(AX)(CX*2), X24, K7, X13                   // 6271bd0754ac4807000000
	VANDPD X22, X7, K7, X13                            // 6231c50f54ee
	VANDPD X1, X7, K7, X13                             // 6271c50f54e9
	VANDPD X11, X7, K7, X13                            // 6251c50f54eb
	VANDPD -17(BP)(SI*2), X7, K7, X13                  // 6271c50f54ac75efffffff
	VANDPD 7(AX)(CX*2), X7, K7, X13                    // 6271c50f54ac4807000000
	VANDPD X22, X0, K7, X13                            // 6231fd0f54ee
	VANDPD X1, X0, K7, X13                             // 6271fd0f54e9
	VANDPD X11, X0, K7, X13                            // 6251fd0f54eb
	VANDPD -17(BP)(SI*2), X0, K7, X13                  // 6271fd0f54ac75efffffff
	VANDPD 7(AX)(CX*2), X0, K7, X13                    // 6271fd0f54ac4807000000
	VANDPD X22, X24, K7, X8                            // 6231bd0754c6
	VANDPD X1, X24, K7, X8                             // 6271bd0754c1
	VANDPD X11, X24, K7, X8                            // 6251bd0754c3
	VANDPD -17(BP)(SI*2), X24, K7, X8                  // 6271bd07548475efffffff
	VANDPD 7(AX)(CX*2), X24, K7, X8                    // 6271bd0754844807000000
	VANDPD X22, X7, K7, X8                             // 6231c50f54c6
	VANDPD X1, X7, K7, X8                              // 6271c50f54c1
	VANDPD X11, X7, K7, X8                             // 6251c50f54c3
	VANDPD -17(BP)(SI*2), X7, K7, X8                   // 6271c50f548475efffffff
	VANDPD 7(AX)(CX*2), X7, K7, X8                     // 6271c50f54844807000000
	VANDPD X22, X0, K7, X8                             // 6231fd0f54c6
	VANDPD X1, X0, K7, X8                              // 6271fd0f54c1
	VANDPD X11, X0, K7, X8                             // 6251fd0f54c3
	VANDPD -17(BP)(SI*2), X0, K7, X8                   // 6271fd0f548475efffffff
	VANDPD 7(AX)(CX*2), X0, K7, X8                     // 6271fd0f54844807000000
	VANDPD Y12, Y3, K4, Y9                             // 6251e52c54cc
	VANDPD Y21, Y3, K4, Y9                             // 6231e52c54cd
	VANDPD Y14, Y3, K4, Y9                             // 6251e52c54ce
	VANDPD (BX), Y3, K4, Y9                            // 6271e52c540b
	VANDPD -17(BP)(SI*1), Y3, K4, Y9                   // 6271e52c548c35efffffff
	VANDPD Y12, Y2, K4, Y9                             // 6251ed2c54cc
	VANDPD Y21, Y2, K4, Y9                             // 6231ed2c54cd
	VANDPD Y14, Y2, K4, Y9                             // 6251ed2c54ce
	VANDPD (BX), Y2, K4, Y9                            // 6271ed2c540b
	VANDPD -17(BP)(SI*1), Y2, K4, Y9                   // 6271ed2c548c35efffffff
	VANDPD Y12, Y9, K4, Y9                             // 6251b52c54cc
	VANDPD Y21, Y9, K4, Y9                             // 6231b52c54cd
	VANDPD Y14, Y9, K4, Y9                             // 6251b52c54ce
	VANDPD (BX), Y9, K4, Y9                            // 6271b52c540b
	VANDPD -17(BP)(SI*1), Y9, K4, Y9                   // 6271b52c548c35efffffff
	VANDPD Y12, Y3, K4, Y1                             // 62d1e52c54cc
	VANDPD Y21, Y3, K4, Y1                             // 62b1e52c54cd
	VANDPD Y14, Y3, K4, Y1                             // 62d1e52c54ce
	VANDPD (BX), Y3, K4, Y1                            // 62f1e52c540b
	VANDPD -17(BP)(SI*1), Y3, K4, Y1                   // 62f1e52c548c35efffffff
	VANDPD Y12, Y2, K4, Y1                             // 62d1ed2c54cc
	VANDPD Y21, Y2, K4, Y1                             // 62b1ed2c54cd
	VANDPD Y14, Y2, K4, Y1                             // 62d1ed2c54ce
	VANDPD (BX), Y2, K4, Y1                            // 62f1ed2c540b
	VANDPD -17(BP)(SI*1), Y2, K4, Y1                   // 62f1ed2c548c35efffffff
	VANDPD Y12, Y9, K4, Y1                             // 62d1b52c54cc
	VANDPD Y21, Y9, K4, Y1                             // 62b1b52c54cd
	VANDPD Y14, Y9, K4, Y1                             // 62d1b52c54ce
	VANDPD (BX), Y9, K4, Y1                            // 62f1b52c540b
	VANDPD -17(BP)(SI*1), Y9, K4, Y1                   // 62f1b52c548c35efffffff
	VANDPD Z2, Z18, K4, Z11                            // 6271ed4454da
	VANDPD Z21, Z18, K4, Z11                           // 6231ed4454dd
	VANDPD (BX), Z18, K4, Z11                          // 6271ed44541b
	VANDPD -17(BP)(SI*1), Z18, K4, Z11                 // 6271ed44549c35efffffff
	VANDPD Z2, Z24, K4, Z11                            // 6271bd4454da
	VANDPD Z21, Z24, K4, Z11                           // 6231bd4454dd
	VANDPD (BX), Z24, K4, Z11                          // 6271bd44541b
	VANDPD -17(BP)(SI*1), Z24, K4, Z11                 // 6271bd44549c35efffffff
	VANDPD Z2, Z18, K4, Z5                             // 62f1ed4454ea
	VANDPD Z21, Z18, K4, Z5                            // 62b1ed4454ed
	VANDPD (BX), Z18, K4, Z5                           // 62f1ed44542b
	VANDPD -17(BP)(SI*1), Z18, K4, Z5                  // 62f1ed4454ac35efffffff
	VANDPD Z2, Z24, K4, Z5                             // 62f1bd4454ea
	VANDPD Z21, Z24, K4, Z5                            // 62b1bd4454ed
	VANDPD (BX), Z24, K4, Z5                           // 62f1bd44542b
	VANDPD -17(BP)(SI*1), Z24, K4, Z5                  // 62f1bd4454ac35efffffff
	VANDPS X20, X31, K7, X6                            // 62b1040754f4
	VANDPS X24, X31, K7, X6                            // 6291040754f0
	VANDPS X7, X31, K7, X6                             // 62f1040754f7
	VANDPS 15(R8)(R14*1), X31, K7, X6                  // 6291040754b4300f000000
	VANDPS 15(R8)(R14*2), X31, K7, X6                  // 6291040754b4700f000000
	VANDPS X20, X3, K7, X6                             // 62b1640f54f4
	VANDPS X24, X3, K7, X6                             // 6291640f54f0
	VANDPS X7, X3, K7, X6                              // 62f1640f54f7
	VANDPS 15(R8)(R14*1), X3, K7, X6                   // 6291640f54b4300f000000
	VANDPS 15(R8)(R14*2), X3, K7, X6                   // 6291640f54b4700f000000
	VANDPS X20, X28, K7, X6                            // 62b11c0754f4
	VANDPS X24, X28, K7, X6                            // 62911c0754f0
	VANDPS X7, X28, K7, X6                             // 62f11c0754f7
	VANDPS 15(R8)(R14*1), X28, K7, X6                  // 62911c0754b4300f000000
	VANDPS 15(R8)(R14*2), X28, K7, X6                  // 62911c0754b4700f000000
	VANDPS X20, X31, K7, X7                            // 62b1040754fc
	VANDPS X24, X31, K7, X7                            // 6291040754f8
	VANDPS X7, X31, K7, X7                             // 62f1040754ff
	VANDPS 15(R8)(R14*1), X31, K7, X7                  // 6291040754bc300f000000
	VANDPS 15(R8)(R14*2), X31, K7, X7                  // 6291040754bc700f000000
	VANDPS X20, X3, K7, X7                             // 62b1640f54fc
	VANDPS X24, X3, K7, X7                             // 6291640f54f8
	VANDPS X7, X3, K7, X7                              // 62f1640f54ff
	VANDPS 15(R8)(R14*1), X3, K7, X7                   // 6291640f54bc300f000000
	VANDPS 15(R8)(R14*2), X3, K7, X7                   // 6291640f54bc700f000000
	VANDPS X20, X28, K7, X7                            // 62b11c0754fc
	VANDPS X24, X28, K7, X7                            // 62911c0754f8
	VANDPS X7, X28, K7, X7                             // 62f11c0754ff
	VANDPS 15(R8)(R14*1), X28, K7, X7                  // 62911c0754bc300f000000
	VANDPS 15(R8)(R14*2), X28, K7, X7                  // 62911c0754bc700f000000
	VANDPS X20, X31, K7, X8                            // 6231040754c4
	VANDPS X24, X31, K7, X8                            // 6211040754c0
	VANDPS X7, X31, K7, X8                             // 6271040754c7
	VANDPS 15(R8)(R14*1), X31, K7, X8                  // 621104075484300f000000
	VANDPS 15(R8)(R14*2), X31, K7, X8                  // 621104075484700f000000
	VANDPS X20, X3, K7, X8                             // 6231640f54c4
	VANDPS X24, X3, K7, X8                             // 6211640f54c0
	VANDPS X7, X3, K7, X8                              // 6271640f54c7
	VANDPS 15(R8)(R14*1), X3, K7, X8                   // 6211640f5484300f000000
	VANDPS 15(R8)(R14*2), X3, K7, X8                   // 6211640f5484700f000000
	VANDPS X20, X28, K7, X8                            // 62311c0754c4
	VANDPS X24, X28, K7, X8                            // 62111c0754c0
	VANDPS X7, X28, K7, X8                             // 62711c0754c7
	VANDPS 15(R8)(R14*1), X28, K7, X8                  // 62111c075484300f000000
	VANDPS 15(R8)(R14*2), X28, K7, X8                  // 62111c075484700f000000
	VANDPS Y31, Y16, K2, Y30                           // 62017c2254f7
	VANDPS Y22, Y16, K2, Y30                           // 62217c2254f6
	VANDPS Y6, Y16, K2, Y30                            // 62617c2254f6
	VANDPS 15(R8)(R14*4), Y16, K2, Y30                 // 62017c2254b4b00f000000
	VANDPS -7(CX)(DX*4), Y16, K2, Y30                  // 62617c2254b491f9ffffff
	VANDPS Y31, Y1, K2, Y30                            // 6201742a54f7
	VANDPS Y22, Y1, K2, Y30                            // 6221742a54f6
	VANDPS Y6, Y1, K2, Y30                             // 6261742a54f6
	VANDPS 15(R8)(R14*4), Y1, K2, Y30                  // 6201742a54b4b00f000000
	VANDPS -7(CX)(DX*4), Y1, K2, Y30                   // 6261742a54b491f9ffffff
	VANDPS Y31, Y30, K2, Y30                           // 62010c2254f7
	VANDPS Y22, Y30, K2, Y30                           // 62210c2254f6
	VANDPS Y6, Y30, K2, Y30                            // 62610c2254f6
	VANDPS 15(R8)(R14*4), Y30, K2, Y30                 // 62010c2254b4b00f000000
	VANDPS -7(CX)(DX*4), Y30, K2, Y30                  // 62610c2254b491f9ffffff
	VANDPS Y31, Y16, K2, Y26                           // 62017c2254d7
	VANDPS Y22, Y16, K2, Y26                           // 62217c2254d6
	VANDPS Y6, Y16, K2, Y26                            // 62617c2254d6
	VANDPS 15(R8)(R14*4), Y16, K2, Y26                 // 62017c225494b00f000000
	VANDPS -7(CX)(DX*4), Y16, K2, Y26                  // 62617c22549491f9ffffff
	VANDPS Y31, Y1, K2, Y26                            // 6201742a54d7
	VANDPS Y22, Y1, K2, Y26                            // 6221742a54d6
	VANDPS Y6, Y1, K2, Y26                             // 6261742a54d6
	VANDPS 15(R8)(R14*4), Y1, K2, Y26                  // 6201742a5494b00f000000
	VANDPS -7(CX)(DX*4), Y1, K2, Y26                   // 6261742a549491f9ffffff
	VANDPS Y31, Y30, K2, Y26                           // 62010c2254d7
	VANDPS Y22, Y30, K2, Y26                           // 62210c2254d6
	VANDPS Y6, Y30, K2, Y26                            // 62610c2254d6
	VANDPS 15(R8)(R14*4), Y30, K2, Y26                 // 62010c225494b00f000000
	VANDPS -7(CX)(DX*4), Y30, K2, Y26                  // 62610c22549491f9ffffff
	VANDPS Y31, Y16, K2, Y7                            // 62917c2254ff
	VANDPS Y22, Y16, K2, Y7                            // 62b17c2254fe
	VANDPS Y6, Y16, K2, Y7                             // 62f17c2254fe
	VANDPS 15(R8)(R14*4), Y16, K2, Y7                  // 62917c2254bcb00f000000
	VANDPS -7(CX)(DX*4), Y16, K2, Y7                   // 62f17c2254bc91f9ffffff
	VANDPS Y31, Y1, K2, Y7                             // 6291742a54ff
	VANDPS Y22, Y1, K2, Y7                             // 62b1742a54fe
	VANDPS Y6, Y1, K2, Y7                              // 62f1742a54fe
	VANDPS 15(R8)(R14*4), Y1, K2, Y7                   // 6291742a54bcb00f000000
	VANDPS -7(CX)(DX*4), Y1, K2, Y7                    // 62f1742a54bc91f9ffffff
	VANDPS Y31, Y30, K2, Y7                            // 62910c2254ff
	VANDPS Y22, Y30, K2, Y7                            // 62b10c2254fe
	VANDPS Y6, Y30, K2, Y7                             // 62f10c2254fe
	VANDPS 15(R8)(R14*4), Y30, K2, Y7                  // 62910c2254bcb00f000000
	VANDPS -7(CX)(DX*4), Y30, K2, Y7                   // 62f10c2254bc91f9ffffff
	VANDPS Z6, Z6, K5, Z7                              // 62f14c4d54fe
	VANDPS Z22, Z6, K5, Z7                             // 62b14c4d54fe
	VANDPS 15(R8)(R14*4), Z6, K5, Z7                   // 62914c4d54bcb00f000000
	VANDPS -7(CX)(DX*4), Z6, K5, Z7                    // 62f14c4d54bc91f9ffffff
	VANDPS Z6, Z16, K5, Z7                             // 62f17c4554fe
	VANDPS Z22, Z16, K5, Z7                            // 62b17c4554fe
	VANDPS 15(R8)(R14*4), Z16, K5, Z7                  // 62917c4554bcb00f000000
	VANDPS -7(CX)(DX*4), Z16, K5, Z7                   // 62f17c4554bc91f9ffffff
	VANDPS Z6, Z6, K5, Z13                             // 62714c4d54ee
	VANDPS Z22, Z6, K5, Z13                            // 62314c4d54ee
	VANDPS 15(R8)(R14*4), Z6, K5, Z13                  // 62114c4d54acb00f000000
	VANDPS -7(CX)(DX*4), Z6, K5, Z13                   // 62714c4d54ac91f9ffffff
	VANDPS Z6, Z16, K5, Z13                            // 62717c4554ee
	VANDPS Z22, Z16, K5, Z13                           // 62317c4554ee
	VANDPS 15(R8)(R14*4), Z16, K5, Z13                 // 62117c4554acb00f000000
	VANDPS -7(CX)(DX*4), Z16, K5, Z13                  // 62717c4554ac91f9ffffff
	VBROADCASTF32X2 X16, K3, Y1                        // 62b27d2b19c8
	VBROADCASTF32X2 X28, K3, Y1                        // 62927d2b19cc
	VBROADCASTF32X2 X8, K3, Y1                         // 62d27d2b19c8
	VBROADCASTF32X2 -17(BP)(SI*8), K3, Y1              // 62f27d2b198cf5efffffff
	VBROADCASTF32X2 (R15), K3, Y1                      // 62d27d2b190f
	VBROADCASTF32X2 X16, K3, Y27                       // 62227d2b19d8
	VBROADCASTF32X2 X28, K3, Y27                       // 62027d2b19dc
	VBROADCASTF32X2 X8, K3, Y27                        // 62427d2b19d8
	VBROADCASTF32X2 -17(BP)(SI*8), K3, Y27             // 62627d2b199cf5efffffff
	VBROADCASTF32X2 (R15), K3, Y27                     // 62427d2b191f
	VBROADCASTF32X2 X16, K3, Y19                       // 62a27d2b19d8
	VBROADCASTF32X2 X28, K3, Y19                       // 62827d2b19dc
	VBROADCASTF32X2 X8, K3, Y19                        // 62c27d2b19d8
	VBROADCASTF32X2 -17(BP)(SI*8), K3, Y19             // 62e27d2b199cf5efffffff
	VBROADCASTF32X2 (R15), K3, Y19                     // 62c27d2b191f
	VBROADCASTF32X2 X15, K2, Z1                        // 62d27d4a19cf
	VBROADCASTF32X2 X11, K2, Z1                        // 62d27d4a19cb
	VBROADCASTF32X2 X1, K2, Z1                         // 62f27d4a19c9
	VBROADCASTF32X2 7(SI)(DI*8), K2, Z1                // 62f27d4a198cfe07000000
	VBROADCASTF32X2 -15(R14), K2, Z1                   // 62d27d4a198ef1ffffff
	VBROADCASTF32X2 X15, K2, Z3                        // 62d27d4a19df
	VBROADCASTF32X2 X11, K2, Z3                        // 62d27d4a19db
	VBROADCASTF32X2 X1, K2, Z3                         // 62f27d4a19d9
	VBROADCASTF32X2 7(SI)(DI*8), K2, Z3                // 62f27d4a199cfe07000000
	VBROADCASTF32X2 -15(R14), K2, Z3                   // 62d27d4a199ef1ffffff
	VBROADCASTF32X8 -17(BP)(SI*2), K1, Z28             // 62627d491ba475efffffff
	VBROADCASTF32X8 7(AX)(CX*2), K1, Z28               // 62627d491ba44807000000
	VBROADCASTF32X8 -17(BP)(SI*2), K1, Z13             // 62727d491bac75efffffff
	VBROADCASTF32X8 7(AX)(CX*2), K1, Z13               // 62727d491bac4807000000
	VBROADCASTF64X2 -7(CX)(DX*1), K7, Y21              // 62e2fd2f1aac11f9ffffff
	VBROADCASTF64X2 -15(R14)(R15*4), K7, Y21           // 6282fd2f1aacbef1ffffff
	VBROADCASTF64X2 -7(CX)(DX*1), K7, Y7               // 62f2fd2f1abc11f9ffffff
	VBROADCASTF64X2 -15(R14)(R15*4), K7, Y7            // 6292fd2f1abcbef1ffffff
	VBROADCASTF64X2 -7(CX)(DX*1), K7, Y30              // 6262fd2f1ab411f9ffffff
	VBROADCASTF64X2 -15(R14)(R15*4), K7, Y30           // 6202fd2f1ab4bef1ffffff
	VBROADCASTF64X2 15(DX)(BX*1), K1, Z14              // 6272fd491ab41a0f000000
	VBROADCASTF64X2 -7(CX)(DX*2), K1, Z14              // 6272fd491ab451f9ffffff
	VBROADCASTF64X2 15(DX)(BX*1), K1, Z28              // 6262fd491aa41a0f000000
	VBROADCASTF64X2 -7(CX)(DX*2), K1, Z28              // 6262fd491aa451f9ffffff
	VBROADCASTI32X2 X14, K1, X19                       // 62c27d0959de
	VBROADCASTI32X2 X0, K1, X19                        // 62e27d0959d8
	VBROADCASTI32X2 7(SI)(DI*1), K1, X19               // 62e27d09599c3e07000000
	VBROADCASTI32X2 15(DX)(BX*8), K1, X19              // 62e27d09599cda0f000000
	VBROADCASTI32X2 X14, K1, X13                       // 62527d0959ee
	VBROADCASTI32X2 X0, K1, X13                        // 62727d0959e8
	VBROADCASTI32X2 7(SI)(DI*1), K1, X13               // 62727d0959ac3e07000000
	VBROADCASTI32X2 15(DX)(BX*8), K1, X13              // 62727d0959acda0f000000
	VBROADCASTI32X2 X14, K1, X2                        // 62d27d0959d6
	VBROADCASTI32X2 X0, K1, X2                         // 62f27d0959d0
	VBROADCASTI32X2 7(SI)(DI*1), K1, X2                // 62f27d0959943e07000000
	VBROADCASTI32X2 15(DX)(BX*8), K1, X2               // 62f27d095994da0f000000
	VBROADCASTI32X2 X25, K7, Y13                       // 62127d2f59e9
	VBROADCASTI32X2 X11, K7, Y13                       // 62527d2f59eb
	VBROADCASTI32X2 X17, K7, Y13                       // 62327d2f59e9
	VBROADCASTI32X2 -7(DI)(R8*1), K7, Y13              // 62327d2f59ac07f9ffffff
	VBROADCASTI32X2 (SP), K7, Y13                      // 62727d2f592c24
	VBROADCASTI32X2 X25, K7, Y18                       // 62827d2f59d1
	VBROADCASTI32X2 X11, K7, Y18                       // 62c27d2f59d3
	VBROADCASTI32X2 X17, K7, Y18                       // 62a27d2f59d1
	VBROADCASTI32X2 -7(DI)(R8*1), K7, Y18              // 62a27d2f599407f9ffffff
	VBROADCASTI32X2 (SP), K7, Y18                      // 62e27d2f591424
	VBROADCASTI32X2 X25, K7, Y24                       // 62027d2f59c1
	VBROADCASTI32X2 X11, K7, Y24                       // 62427d2f59c3
	VBROADCASTI32X2 X17, K7, Y24                       // 62227d2f59c1
	VBROADCASTI32X2 -7(DI)(R8*1), K7, Y24              // 62227d2f598407f9ffffff
	VBROADCASTI32X2 (SP), K7, Y24                      // 62627d2f590424
	VBROADCASTI32X2 X18, K2, Z15                       // 62327d4a59fa
	VBROADCASTI32X2 X11, K2, Z15                       // 62527d4a59fb
	VBROADCASTI32X2 X9, K2, Z15                        // 62527d4a59f9
	VBROADCASTI32X2 -7(CX), K2, Z15                    // 62727d4a59b9f9ffffff
	VBROADCASTI32X2 15(DX)(BX*4), K2, Z15              // 62727d4a59bc9a0f000000
	VBROADCASTI32X2 X18, K2, Z30                       // 62227d4a59f2
	VBROADCASTI32X2 X11, K2, Z30                       // 62427d4a59f3
	VBROADCASTI32X2 X9, K2, Z30                        // 62427d4a59f1
	VBROADCASTI32X2 -7(CX), K2, Z30                    // 62627d4a59b1f9ffffff
	VBROADCASTI32X2 15(DX)(BX*4), K2, Z30              // 62627d4a59b49a0f000000
	VBROADCASTI32X8 (R14), K3, Z5                      // 62d27d4b5b2e
	VBROADCASTI32X8 -7(DI)(R8*8), K3, Z5               // 62b27d4b5bacc7f9ffffff
	VBROADCASTI32X8 (R14), K3, Z1                      // 62d27d4b5b0e
	VBROADCASTI32X8 -7(DI)(R8*8), K3, Z1               // 62b27d4b5b8cc7f9ffffff
	VBROADCASTI64X2 15(R8), K4, Y5                     // 62d2fd2c5aa80f000000
	VBROADCASTI64X2 (BP), K4, Y5                       // 62f2fd2c5a6d00
	VBROADCASTI64X2 15(R8), K4, Y24                    // 6242fd2c5a800f000000
	VBROADCASTI64X2 (BP), K4, Y24                      // 6262fd2c5a4500
	VBROADCASTI64X2 15(R8), K4, Y21                    // 62c2fd2c5aa80f000000
	VBROADCASTI64X2 (BP), K4, Y21                      // 62e2fd2c5a6d00
	VBROADCASTI64X2 15(R8)(R14*8), K5, Z3              // 6292fd4d5a9cf00f000000
	VBROADCASTI64X2 -15(R14)(R15*2), K5, Z3            // 6292fd4d5a9c7ef1ffffff
	VBROADCASTI64X2 15(R8)(R14*8), K5, Z5              // 6292fd4d5aacf00f000000
	VBROADCASTI64X2 -15(R14)(R15*2), K5, Z5            // 6292fd4d5aac7ef1ffffff
	VCVTPD2QQ X15, K7, X0                              // 62d1fd0f7bc7
	VCVTPD2QQ X11, K7, X0                              // 62d1fd0f7bc3
	VCVTPD2QQ X0, K7, X0                               // 62f1fd0f7bc0
	VCVTPD2QQ -17(BP)(SI*8), K7, X0                    // 62f1fd0f7b84f5efffffff
	VCVTPD2QQ (R15), K7, X0                            // 62d1fd0f7b07
	VCVTPD2QQ X15, K7, X17                             // 62c1fd0f7bcf
	VCVTPD2QQ X11, K7, X17                             // 62c1fd0f7bcb
	VCVTPD2QQ X0, K7, X17                              // 62e1fd0f7bc8
	VCVTPD2QQ -17(BP)(SI*8), K7, X17                   // 62e1fd0f7b8cf5efffffff
	VCVTPD2QQ (R15), K7, X17                           // 62c1fd0f7b0f
	VCVTPD2QQ X15, K7, X7                              // 62d1fd0f7bff
	VCVTPD2QQ X11, K7, X7                              // 62d1fd0f7bfb
	VCVTPD2QQ X0, K7, X7                               // 62f1fd0f7bf8
	VCVTPD2QQ -17(BP)(SI*8), K7, X7                    // 62f1fd0f7bbcf5efffffff
	VCVTPD2QQ (R15), K7, X7                            // 62d1fd0f7b3f
	VCVTPD2QQ Y0, K2, Y6                               // 62f1fd2a7bf0
	VCVTPD2QQ Y19, K2, Y6                              // 62b1fd2a7bf3
	VCVTPD2QQ Y31, K2, Y6                              // 6291fd2a7bf7
	VCVTPD2QQ -15(R14)(R15*1), K2, Y6                  // 6291fd2a7bb43ef1ffffff
	VCVTPD2QQ -15(BX), K2, Y6                          // 62f1fd2a7bb3f1ffffff
	VCVTPD2QQ Y0, K2, Y1                               // 62f1fd2a7bc8
	VCVTPD2QQ Y19, K2, Y1                              // 62b1fd2a7bcb
	VCVTPD2QQ Y31, K2, Y1                              // 6291fd2a7bcf
	VCVTPD2QQ -15(R14)(R15*1), K2, Y1                  // 6291fd2a7b8c3ef1ffffff
	VCVTPD2QQ -15(BX), K2, Y1                          // 62f1fd2a7b8bf1ffffff
	VCVTPD2QQ Y0, K2, Y9                               // 6271fd2a7bc8
	VCVTPD2QQ Y19, K2, Y9                              // 6231fd2a7bcb
	VCVTPD2QQ Y31, K2, Y9                              // 6211fd2a7bcf
	VCVTPD2QQ -15(R14)(R15*1), K2, Y9                  // 6211fd2a7b8c3ef1ffffff
	VCVTPD2QQ -15(BX), K2, Y9                          // 6271fd2a7b8bf1ffffff
	VCVTPD2QQ Z12, K5, Z14                             // 6251fd4d7bf4
	VCVTPD2QQ Z13, K5, Z14                             // 6251fd4d7bf5
	VCVTPD2QQ Z12, K5, Z13                             // 6251fd4d7bec
	VCVTPD2QQ Z13, K5, Z13                             // 6251fd4d7bed
	VCVTPD2QQ Z2, K3, Z21                              // 62e1fd4b7bea
	VCVTPD2QQ Z7, K3, Z21                              // 62e1fd4b7bef
	VCVTPD2QQ -17(BP), K3, Z21                         // 62e1fd4b7badefffffff
	VCVTPD2QQ -15(R14)(R15*8), K3, Z21                 // 6281fd4b7bacfef1ffffff
	VCVTPD2QQ Z2, K3, Z9                               // 6271fd4b7bca
	VCVTPD2QQ Z7, K3, Z9                               // 6271fd4b7bcf
	VCVTPD2QQ -17(BP), K3, Z9                          // 6271fd4b7b8defffffff
	VCVTPD2QQ -15(R14)(R15*8), K3, Z9                  // 6211fd4b7b8cfef1ffffff
	VCVTPD2UQQ X24, K3, X7                             // 6291fd0b79f8
	VCVTPD2UQQ X7, K3, X7                              // 62f1fd0b79ff
	VCVTPD2UQQ X0, K3, X7                              // 62f1fd0b79f8
	VCVTPD2UQQ 7(SI)(DI*1), K3, X7                     // 62f1fd0b79bc3e07000000
	VCVTPD2UQQ 15(DX)(BX*8), K3, X7                    // 62f1fd0b79bcda0f000000
	VCVTPD2UQQ X24, K3, X13                            // 6211fd0b79e8
	VCVTPD2UQQ X7, K3, X13                             // 6271fd0b79ef
	VCVTPD2UQQ X0, K3, X13                             // 6271fd0b79e8
	VCVTPD2UQQ 7(SI)(DI*1), K3, X13                    // 6271fd0b79ac3e07000000
	VCVTPD2UQQ 15(DX)(BX*8), K3, X13                   // 6271fd0b79acda0f000000
	VCVTPD2UQQ X24, K3, X8                             // 6211fd0b79c0
	VCVTPD2UQQ X7, K3, X8                              // 6271fd0b79c7
	VCVTPD2UQQ X0, K3, X8                              // 6271fd0b79c0
	VCVTPD2UQQ 7(SI)(DI*1), K3, X8                     // 6271fd0b79843e07000000
	VCVTPD2UQQ 15(DX)(BX*8), K3, X8                    // 6271fd0b7984da0f000000
	VCVTPD2UQQ Y27, K3, Y28                            // 6201fd2b79e3
	VCVTPD2UQQ Y0, K3, Y28                             // 6261fd2b79e0
	VCVTPD2UQQ Y11, K3, Y28                            // 6241fd2b79e3
	VCVTPD2UQQ (SI), K3, Y28                           // 6261fd2b7926
	VCVTPD2UQQ 7(SI)(DI*2), K3, Y28                    // 6261fd2b79a47e07000000
	VCVTPD2UQQ Y27, K3, Y2                             // 6291fd2b79d3
	VCVTPD2UQQ Y0, K3, Y2                              // 62f1fd2b79d0
	VCVTPD2UQQ Y11, K3, Y2                             // 62d1fd2b79d3
	VCVTPD2UQQ (SI), K3, Y2                            // 62f1fd2b7916
	VCVTPD2UQQ 7(SI)(DI*2), K3, Y2                     // 62f1fd2b79947e07000000
	VCVTPD2UQQ Y27, K3, Y24                            // 6201fd2b79c3
	VCVTPD2UQQ Y0, K3, Y24                             // 6261fd2b79c0
	VCVTPD2UQQ Y11, K3, Y24                            // 6241fd2b79c3
	VCVTPD2UQQ (SI), K3, Y24                           // 6261fd2b7906
	VCVTPD2UQQ 7(SI)(DI*2), K3, Y24                    // 6261fd2b79847e07000000
	VCVTPD2UQQ Z3, K2, Z27                             // 6261fd4a79db
	VCVTPD2UQQ Z0, K2, Z27                             // 6261fd4a79d8
	VCVTPD2UQQ Z3, K2, Z14                             // 6271fd4a79f3
	VCVTPD2UQQ Z0, K2, Z14                             // 6271fd4a79f0
	VCVTPD2UQQ Z8, K1, Z14                             // 6251fd4979f0
	VCVTPD2UQQ Z24, K1, Z14                            // 6211fd4979f0
	VCVTPD2UQQ 15(R8), K1, Z14                         // 6251fd4979b00f000000
	VCVTPD2UQQ (BP), K1, Z14                           // 6271fd49797500
	VCVTPD2UQQ Z8, K1, Z7                              // 62d1fd4979f8
	VCVTPD2UQQ Z24, K1, Z7                             // 6291fd4979f8
	VCVTPD2UQQ 15(R8), K1, Z7                          // 62d1fd4979b80f000000
	VCVTPD2UQQ (BP), K1, Z7                            // 62f1fd49797d00
	VCVTPS2QQ X19, K3, X15                             // 62317d0b7bfb
	VCVTPS2QQ X13, K3, X15                             // 62517d0b7bfd
	VCVTPS2QQ X2, K3, X15                              // 62717d0b7bfa
	VCVTPS2QQ (BX), K3, X15                            // 62717d0b7b3b
	VCVTPS2QQ -17(BP)(SI*1), K3, X15                   // 62717d0b7bbc35efffffff
	VCVTPS2QQ X19, K3, X11                             // 62317d0b7bdb
	VCVTPS2QQ X13, K3, X11                             // 62517d0b7bdd
	VCVTPS2QQ X2, K3, X11                              // 62717d0b7bda
	VCVTPS2QQ (BX), K3, X11                            // 62717d0b7b1b
	VCVTPS2QQ -17(BP)(SI*1), K3, X11                   // 62717d0b7b9c35efffffff
	VCVTPS2QQ X19, K3, X1                              // 62b17d0b7bcb
	VCVTPS2QQ X13, K3, X1                              // 62d17d0b7bcd
	VCVTPS2QQ X2, K3, X1                               // 62f17d0b7bca
	VCVTPS2QQ (BX), K3, X1                             // 62f17d0b7b0b
	VCVTPS2QQ -17(BP)(SI*1), K3, X1                    // 62f17d0b7b8c35efffffff
	VCVTPS2QQ X14, K7, Y20                             // 62c17d2f7be6
	VCVTPS2QQ X0, K7, Y20                              // 62e17d2f7be0
	VCVTPS2QQ 99(R15)(R15*1), K7, Y20                  // 62817d2f7ba43f63000000
	VCVTPS2QQ (DX), K7, Y20                            // 62e17d2f7b22
	VCVTPS2QQ X14, K7, Y12                             // 62517d2f7be6
	VCVTPS2QQ X0, K7, Y12                              // 62717d2f7be0
	VCVTPS2QQ 99(R15)(R15*1), K7, Y12                  // 62117d2f7ba43f63000000
	VCVTPS2QQ (DX), K7, Y12                            // 62717d2f7b22
	VCVTPS2QQ X14, K7, Y3                              // 62d17d2f7bde
	VCVTPS2QQ X0, K7, Y3                               // 62f17d2f7bd8
	VCVTPS2QQ 99(R15)(R15*1), K7, Y3                   // 62917d2f7b9c3f63000000
	VCVTPS2QQ (DX), K7, Y3                             // 62f17d2f7b1a
	VCVTPS2QQ Y5, K4, Z6                               // 62f17d4c7bf5
	VCVTPS2QQ Y28, K4, Z6                              // 62917d4c7bf4
	VCVTPS2QQ Y7, K4, Z6                               // 62f17d4c7bf7
	VCVTPS2QQ Y5, K4, Z14                              // 62717d4c7bf5
	VCVTPS2QQ Y28, K4, Z14                             // 62117d4c7bf4
	VCVTPS2QQ Y7, K4, Z14                              // 62717d4c7bf7
	VCVTPS2QQ Y0, K4, Z26                              // 62617d4c7bd0
	VCVTPS2QQ Y22, K4, Z26                             // 62217d4c7bd6
	VCVTPS2QQ Y13, K4, Z26                             // 62417d4c7bd5
	VCVTPS2QQ 7(AX)(CX*4), K4, Z26                     // 62617d4c7b948807000000
	VCVTPS2QQ 7(AX)(CX*1), K4, Z26                     // 62617d4c7b940807000000
	VCVTPS2QQ Y0, K4, Z14                              // 62717d4c7bf0
	VCVTPS2QQ Y22, K4, Z14                             // 62317d4c7bf6
	VCVTPS2QQ Y13, K4, Z14                             // 62517d4c7bf5
	VCVTPS2QQ 7(AX)(CX*4), K4, Z14                     // 62717d4c7bb48807000000
	VCVTPS2QQ 7(AX)(CX*1), K4, Z14                     // 62717d4c7bb40807000000
	VCVTPS2UQQ X2, K4, X2                              // 62f17d0c79d2
	VCVTPS2UQQ X27, K4, X2                             // 62917d0c79d3
	VCVTPS2UQQ X26, K4, X2                             // 62917d0c79d2
	VCVTPS2UQQ (R8), K4, X2                            // 62d17d0c7910
	VCVTPS2UQQ 15(DX)(BX*2), K4, X2                    // 62f17d0c79945a0f000000
	VCVTPS2UQQ X2, K4, X24                             // 62617d0c79c2
	VCVTPS2UQQ X27, K4, X24                            // 62017d0c79c3
	VCVTPS2UQQ X26, K4, X24                            // 62017d0c79c2
	VCVTPS2UQQ (R8), K4, X24                           // 62417d0c7900
	VCVTPS2UQQ 15(DX)(BX*2), K4, X24                   // 62617d0c79845a0f000000
	VCVTPS2UQQ X22, K2, Y31                            // 62217d2a79fe
	VCVTPS2UQQ X30, K2, Y31                            // 62017d2a79fe
	VCVTPS2UQQ X3, K2, Y31                             // 62617d2a79fb
	VCVTPS2UQQ 7(SI)(DI*8), K2, Y31                    // 62617d2a79bcfe07000000
	VCVTPS2UQQ -15(R14), K2, Y31                       // 62417d2a79bef1ffffff
	VCVTPS2UQQ X22, K2, Y8                             // 62317d2a79c6
	VCVTPS2UQQ X30, K2, Y8                             // 62117d2a79c6
	VCVTPS2UQQ X3, K2, Y8                              // 62717d2a79c3
	VCVTPS2UQQ 7(SI)(DI*8), K2, Y8                     // 62717d2a7984fe07000000
	VCVTPS2UQQ -15(R14), K2, Y8                        // 62517d2a7986f1ffffff
	VCVTPS2UQQ X22, K2, Y1                             // 62b17d2a79ce
	VCVTPS2UQQ X30, K2, Y1                             // 62917d2a79ce
	VCVTPS2UQQ X3, K2, Y1                              // 62f17d2a79cb
	VCVTPS2UQQ 7(SI)(DI*8), K2, Y1                     // 62f17d2a798cfe07000000
	VCVTPS2UQQ -15(R14), K2, Y1                        // 62d17d2a798ef1ffffff
	VCVTPS2UQQ Y28, K2, Z21                            // 62817d4a79ec
	VCVTPS2UQQ Y13, K2, Z21                            // 62c17d4a79ed
	VCVTPS2UQQ Y7, K2, Z21                             // 62e17d4a79ef
	VCVTPS2UQQ Y28, K2, Z13                            // 62117d4a79ec
	VCVTPS2UQQ Y13, K2, Z13                            // 62517d4a79ed
	VCVTPS2UQQ Y7, K2, Z13                             // 62717d4a79ef
	VCVTPS2UQQ Y2, K3, Z11                             // 62717d4b79da
	VCVTPS2UQQ Y21, K3, Z11                            // 62317d4b79dd
	VCVTPS2UQQ Y12, K3, Z11                            // 62517d4b79dc
	VCVTPS2UQQ 17(SP)(BP*8), K3, Z11                   // 62717d4b799cec11000000
	VCVTPS2UQQ 17(SP)(BP*4), K3, Z11                   // 62717d4b799cac11000000
	VCVTPS2UQQ Y2, K3, Z25                             // 62617d4b79ca
	VCVTPS2UQQ Y21, K3, Z25                            // 62217d4b79cd
	VCVTPS2UQQ Y12, K3, Z25                            // 62417d4b79cc
	VCVTPS2UQQ 17(SP)(BP*8), K3, Z25                   // 62617d4b798cec11000000
	VCVTPS2UQQ 17(SP)(BP*4), K3, Z25                   // 62617d4b798cac11000000
	VCVTQQ2PD X13, K3, X11                             // 6251fe0be6dd
	VCVTQQ2PD X6, K3, X11                              // 6271fe0be6de
	VCVTQQ2PD X12, K3, X11                             // 6251fe0be6dc
	VCVTQQ2PD 17(SP)(BP*1), K3, X11                    // 6271fe0be69c2c11000000
	VCVTQQ2PD -7(CX)(DX*8), K3, X11                    // 6271fe0be69cd1f9ffffff
	VCVTQQ2PD X13, K3, X15                             // 6251fe0be6fd
	VCVTQQ2PD X6, K3, X15                              // 6271fe0be6fe
	VCVTQQ2PD X12, K3, X15                             // 6251fe0be6fc
	VCVTQQ2PD 17(SP)(BP*1), K3, X15                    // 6271fe0be6bc2c11000000
	VCVTQQ2PD -7(CX)(DX*8), K3, X15                    // 6271fe0be6bcd1f9ffffff
	VCVTQQ2PD X13, K3, X30                             // 6241fe0be6f5
	VCVTQQ2PD X6, K3, X30                              // 6261fe0be6f6
	VCVTQQ2PD X12, K3, X30                             // 6241fe0be6f4
	VCVTQQ2PD 17(SP)(BP*1), K3, X30                    // 6261fe0be6b42c11000000
	VCVTQQ2PD -7(CX)(DX*8), K3, X30                    // 6261fe0be6b4d1f9ffffff
	VCVTQQ2PD Y3, K3, Y9                               // 6271fe2be6cb
	VCVTQQ2PD Y2, K3, Y9                               // 6271fe2be6ca
	VCVTQQ2PD Y9, K3, Y9                               // 6251fe2be6c9
	VCVTQQ2PD 7(SI)(DI*1), K3, Y9                      // 6271fe2be68c3e07000000
	VCVTQQ2PD 15(DX)(BX*8), K3, Y9                     // 6271fe2be68cda0f000000
	VCVTQQ2PD Y3, K3, Y1                               // 62f1fe2be6cb
	VCVTQQ2PD Y2, K3, Y1                               // 62f1fe2be6ca
	VCVTQQ2PD Y9, K3, Y1                               // 62d1fe2be6c9
	VCVTQQ2PD 7(SI)(DI*1), K3, Y1                      // 62f1fe2be68c3e07000000
	VCVTQQ2PD 15(DX)(BX*8), K3, Y1                     // 62f1fe2be68cda0f000000
	VCVTQQ2PD Z27, K2, Z3                              // 6291fe4ae6db
	VCVTQQ2PD Z15, K2, Z3                              // 62d1fe4ae6df
	VCVTQQ2PD Z27, K2, Z12                             // 6211fe4ae6e3
	VCVTQQ2PD Z15, K2, Z12                             // 6251fe4ae6e7
	VCVTQQ2PD Z23, K1, Z23                             // 62a1fe49e6ff
	VCVTQQ2PD Z6, K1, Z23                              // 62e1fe49e6fe
	VCVTQQ2PD 7(SI)(DI*4), K1, Z23                     // 62e1fe49e6bcbe07000000
	VCVTQQ2PD -7(DI)(R8*2), K1, Z23                    // 62a1fe49e6bc47f9ffffff
	VCVTQQ2PD Z23, K1, Z5                              // 62b1fe49e6ef
	VCVTQQ2PD Z6, K1, Z5                               // 62f1fe49e6ee
	VCVTQQ2PD 7(SI)(DI*4), K1, Z5                      // 62f1fe49e6acbe07000000
	VCVTQQ2PD -7(DI)(R8*2), K1, Z5                     // 62b1fe49e6ac47f9ffffff
	VCVTQQ2PS Z8, K2, Y12                              // 6251fc4a5be0
	VCVTQQ2PS Z28, K2, Y12                             // 6211fc4a5be4
	VCVTQQ2PS Z8, K2, Y21                              // 62c1fc4a5be8
	VCVTQQ2PS Z28, K2, Y21                             // 6281fc4a5bec
	VCVTQQ2PS Z8, K2, Y14                              // 6251fc4a5bf0
	VCVTQQ2PS Z28, K2, Y14                             // 6211fc4a5bf4
	VCVTQQ2PS Z21, K1, Y30                             // 6221fc495bf5
	VCVTQQ2PS Z5, K1, Y30                              // 6261fc495bf5
	VCVTQQ2PS 17(SP), K1, Y30                          // 6261fc495bb42411000000
	VCVTQQ2PS -17(BP)(SI*4), K1, Y30                   // 6261fc495bb4b5efffffff
	VCVTQQ2PS Z21, K1, Y26                             // 6221fc495bd5
	VCVTQQ2PS Z5, K1, Y26                              // 6261fc495bd5
	VCVTQQ2PS 17(SP), K1, Y26                          // 6261fc495b942411000000
	VCVTQQ2PS -17(BP)(SI*4), K1, Y26                   // 6261fc495b94b5efffffff
	VCVTQQ2PS Z21, K1, Y7                              // 62b1fc495bfd
	VCVTQQ2PS Z5, K1, Y7                               // 62f1fc495bfd
	VCVTQQ2PS 17(SP), K1, Y7                           // 62f1fc495bbc2411000000
	VCVTQQ2PS -17(BP)(SI*4), K1, Y7                    // 62f1fc495bbcb5efffffff
	VCVTQQ2PSX X20, K7, X23                            // 62a1fc0f5bfc
	VCVTQQ2PSX X2, K7, X23                             // 62e1fc0f5bfa
	VCVTQQ2PSX X9, K7, X23                             // 62c1fc0f5bf9
	VCVTQQ2PSX -17(BP)(SI*2), K7, X23                  // 62e1fc0f5bbc75efffffff
	VCVTQQ2PSX 7(AX)(CX*2), K7, X23                    // 62e1fc0f5bbc4807000000
	VCVTQQ2PSX X20, K7, X30                            // 6221fc0f5bf4
	VCVTQQ2PSX X2, K7, X30                             // 6261fc0f5bf2
	VCVTQQ2PSX X9, K7, X30                             // 6241fc0f5bf1
	VCVTQQ2PSX -17(BP)(SI*2), K7, X30                  // 6261fc0f5bb475efffffff
	VCVTQQ2PSX 7(AX)(CX*2), K7, X30                    // 6261fc0f5bb44807000000
	VCVTQQ2PSX X20, K7, X8                             // 6231fc0f5bc4
	VCVTQQ2PSX X2, K7, X8                              // 6271fc0f5bc2
	VCVTQQ2PSX X9, K7, X8                              // 6251fc0f5bc1
	VCVTQQ2PSX -17(BP)(SI*2), K7, X8                   // 6271fc0f5b8475efffffff
	VCVTQQ2PSX 7(AX)(CX*2), K7, X8                     // 6271fc0f5b844807000000
	VCVTQQ2PSY Y16, K1, X26                            // 6221fc295bd0
	VCVTQQ2PSY Y1, K1, X26                             // 6261fc295bd1
	VCVTQQ2PSY Y30, K1, X26                            // 6201fc295bd6
	VCVTQQ2PSY -7(DI)(R8*1), K1, X26                   // 6221fc295b9407f9ffffff
	VCVTQQ2PSY (SP), K1, X26                           // 6261fc295b1424
	VCVTQQ2PSY Y16, K1, X19                            // 62a1fc295bd8
	VCVTQQ2PSY Y1, K1, X19                             // 62e1fc295bd9
	VCVTQQ2PSY Y30, K1, X19                            // 6281fc295bde
	VCVTQQ2PSY -7(DI)(R8*1), K1, X19                   // 62a1fc295b9c07f9ffffff
	VCVTQQ2PSY (SP), K1, X19                           // 62e1fc295b1c24
	VCVTQQ2PSY Y16, K1, X0                             // 62b1fc295bc0
	VCVTQQ2PSY Y1, K1, X0                              // 62f1fc295bc1
	VCVTQQ2PSY Y30, K1, X0                             // 6291fc295bc6
	VCVTQQ2PSY -7(DI)(R8*1), K1, X0                    // 62b1fc295b8407f9ffffff
	VCVTQQ2PSY (SP), K1, X0                            // 62f1fc295b0424
	VCVTTPD2QQ X6, K5, X6                              // 62f1fd0d7af6
	VCVTTPD2QQ X1, K5, X6                              // 62f1fd0d7af1
	VCVTTPD2QQ X8, K5, X6                              // 62d1fd0d7af0
	VCVTTPD2QQ (R14), K5, X6                           // 62d1fd0d7a36
	VCVTTPD2QQ -7(DI)(R8*8), K5, X6                    // 62b1fd0d7ab4c7f9ffffff
	VCVTTPD2QQ X6, K5, X17                             // 62e1fd0d7ace
	VCVTTPD2QQ X1, K5, X17                             // 62e1fd0d7ac9
	VCVTTPD2QQ X8, K5, X17                             // 62c1fd0d7ac8
	VCVTTPD2QQ (R14), K5, X17                          // 62c1fd0d7a0e
	VCVTTPD2QQ -7(DI)(R8*8), K5, X17                   // 62a1fd0d7a8cc7f9ffffff
	VCVTTPD2QQ X6, K5, X28                             // 6261fd0d7ae6
	VCVTTPD2QQ X1, K5, X28                             // 6261fd0d7ae1
	VCVTTPD2QQ X8, K5, X28                             // 6241fd0d7ae0
	VCVTTPD2QQ (R14), K5, X28                          // 6241fd0d7a26
	VCVTTPD2QQ -7(DI)(R8*8), K5, X28                   // 6221fd0d7aa4c7f9ffffff
	VCVTTPD2QQ Y14, K7, Y24                            // 6241fd2f7ac6
	VCVTTPD2QQ Y21, K7, Y24                            // 6221fd2f7ac5
	VCVTTPD2QQ Y1, K7, Y24                             // 6261fd2f7ac1
	VCVTTPD2QQ 99(R15)(R15*8), K7, Y24                 // 6201fd2f7a84ff63000000
	VCVTTPD2QQ 7(AX)(CX*8), K7, Y24                    // 6261fd2f7a84c807000000
	VCVTTPD2QQ Y14, K7, Y13                            // 6251fd2f7aee
	VCVTTPD2QQ Y21, K7, Y13                            // 6231fd2f7aed
	VCVTTPD2QQ Y1, K7, Y13                             // 6271fd2f7ae9
	VCVTTPD2QQ 99(R15)(R15*8), K7, Y13                 // 6211fd2f7aacff63000000
	VCVTTPD2QQ 7(AX)(CX*8), K7, Y13                    // 6271fd2f7aacc807000000
	VCVTTPD2QQ Y14, K7, Y20                            // 62c1fd2f7ae6
	VCVTTPD2QQ Y21, K7, Y20                            // 62a1fd2f7ae5
	VCVTTPD2QQ Y1, K7, Y20                             // 62e1fd2f7ae1
	VCVTTPD2QQ 99(R15)(R15*8), K7, Y20                 // 6281fd2f7aa4ff63000000
	VCVTTPD2QQ 7(AX)(CX*8), K7, Y20                    // 62e1fd2f7aa4c807000000
	VCVTTPD2QQ Z6, K7, Z22                             // 62e1fd4f7af6
	VCVTTPD2QQ Z8, K7, Z22                             // 62c1fd4f7af0
	VCVTTPD2QQ Z6, K7, Z11                             // 6271fd4f7ade
	VCVTTPD2QQ Z8, K7, Z11                             // 6251fd4f7ad8
	VCVTTPD2QQ Z12, K6, Z25                            // 6241fd4e7acc
	VCVTTPD2QQ Z17, K6, Z25                            // 6221fd4e7ac9
	VCVTTPD2QQ 99(R15)(R15*1), K6, Z25                 // 6201fd4e7a8c3f63000000
	VCVTTPD2QQ (DX), K6, Z25                           // 6261fd4e7a0a
	VCVTTPD2QQ Z12, K6, Z12                            // 6251fd4e7ae4
	VCVTTPD2QQ Z17, K6, Z12                            // 6231fd4e7ae1
	VCVTTPD2QQ 99(R15)(R15*1), K6, Z12                 // 6211fd4e7aa43f63000000
	VCVTTPD2QQ (DX), K6, Z12                           // 6271fd4e7a22
	VCVTTPD2UQQ X15, K7, X16                           // 62c1fd0f78c7
	VCVTTPD2UQQ X11, K7, X16                           // 62c1fd0f78c3
	VCVTTPD2UQQ X1, K7, X16                            // 62e1fd0f78c1
	VCVTTPD2UQQ (CX), K7, X16                          // 62e1fd0f7801
	VCVTTPD2UQQ 99(R15), K7, X16                       // 62c1fd0f788763000000
	VCVTTPD2UQQ X15, K7, X28                           // 6241fd0f78e7
	VCVTTPD2UQQ X11, K7, X28                           // 6241fd0f78e3
	VCVTTPD2UQQ X1, K7, X28                            // 6261fd0f78e1
	VCVTTPD2UQQ (CX), K7, X28                          // 6261fd0f7821
	VCVTTPD2UQQ 99(R15), K7, X28                       // 6241fd0f78a763000000
	VCVTTPD2UQQ X15, K7, X8                            // 6251fd0f78c7
	VCVTTPD2UQQ X11, K7, X8                            // 6251fd0f78c3
	VCVTTPD2UQQ X1, K7, X8                             // 6271fd0f78c1
	VCVTTPD2UQQ (CX), K7, X8                           // 6271fd0f7801
	VCVTTPD2UQQ 99(R15), K7, X8                        // 6251fd0f788763000000
	VCVTTPD2UQQ Y21, K2, Y5                            // 62b1fd2a78ed
	VCVTTPD2UQQ Y7, K2, Y5                             // 62f1fd2a78ef
	VCVTTPD2UQQ Y30, K2, Y5                            // 6291fd2a78ee
	VCVTTPD2UQQ (BX), K2, Y5                           // 62f1fd2a782b
	VCVTTPD2UQQ -17(BP)(SI*1), K2, Y5                  // 62f1fd2a78ac35efffffff
	VCVTTPD2UQQ Y21, K2, Y17                           // 62a1fd2a78cd
	VCVTTPD2UQQ Y7, K2, Y17                            // 62e1fd2a78cf
	VCVTTPD2UQQ Y30, K2, Y17                           // 6281fd2a78ce
	VCVTTPD2UQQ (BX), K2, Y17                          // 62e1fd2a780b
	VCVTTPD2UQQ -17(BP)(SI*1), K2, Y17                 // 62e1fd2a788c35efffffff
	VCVTTPD2UQQ Y21, K2, Y13                           // 6231fd2a78ed
	VCVTTPD2UQQ Y7, K2, Y13                            // 6271fd2a78ef
	VCVTTPD2UQQ Y30, K2, Y13                           // 6211fd2a78ee
	VCVTTPD2UQQ (BX), K2, Y13                          // 6271fd2a782b
	VCVTTPD2UQQ -17(BP)(SI*1), K2, Y13                 // 6271fd2a78ac35efffffff
	VCVTTPD2UQQ Z8, K5, Z3                             // 62d1fd4d78d8
	VCVTTPD2UQQ Z2, K5, Z3                             // 62f1fd4d78da
	VCVTTPD2UQQ Z8, K5, Z21                            // 62c1fd4d78e8
	VCVTTPD2UQQ Z2, K5, Z21                            // 62e1fd4d78ea
	VCVTTPD2UQQ Z7, K3, Z3                             // 62f1fd4b78df
	VCVTTPD2UQQ Z9, K3, Z3                             // 62d1fd4b78d9
	VCVTTPD2UQQ 7(SI)(DI*8), K3, Z3                    // 62f1fd4b789cfe07000000
	VCVTTPD2UQQ -15(R14), K3, Z3                       // 62d1fd4b789ef1ffffff
	VCVTTPD2UQQ Z7, K3, Z27                            // 6261fd4b78df
	VCVTTPD2UQQ Z9, K3, Z27                            // 6241fd4b78d9
	VCVTTPD2UQQ 7(SI)(DI*8), K3, Z27                   // 6261fd4b789cfe07000000
	VCVTTPD2UQQ -15(R14), K3, Z27                      // 6241fd4b789ef1ffffff
	VCVTTPS2QQ X18, K3, X25                            // 62217d0b7aca
	VCVTTPS2QQ X11, K3, X25                            // 62417d0b7acb
	VCVTTPS2QQ X9, K3, X25                             // 62417d0b7ac9
	VCVTTPS2QQ -7(CX)(DX*1), K3, X25                   // 62617d0b7a8c11f9ffffff
	VCVTTPS2QQ -15(R14)(R15*4), K3, X25                // 62017d0b7a8cbef1ffffff
	VCVTTPS2QQ X18, K3, X11                            // 62317d0b7ada
	VCVTTPS2QQ X11, K3, X11                            // 62517d0b7adb
	VCVTTPS2QQ X9, K3, X11                             // 62517d0b7ad9
	VCVTTPS2QQ -7(CX)(DX*1), K3, X11                   // 62717d0b7a9c11f9ffffff
	VCVTTPS2QQ -15(R14)(R15*4), K3, X11                // 62117d0b7a9cbef1ffffff
	VCVTTPS2QQ X18, K3, X17                            // 62a17d0b7aca
	VCVTTPS2QQ X11, K3, X17                            // 62c17d0b7acb
	VCVTTPS2QQ X9, K3, X17                             // 62c17d0b7ac9
	VCVTTPS2QQ -7(CX)(DX*1), K3, X17                   // 62e17d0b7a8c11f9ffffff
	VCVTTPS2QQ -15(R14)(R15*4), K3, X17                // 62817d0b7a8cbef1ffffff
	VCVTTPS2QQ X2, K3, Y5                              // 62f17d2b7aea
	VCVTTPS2QQ X24, K3, Y5                             // 62917d2b7ae8
	VCVTTPS2QQ (R8), K3, Y5                            // 62d17d2b7a28
	VCVTTPS2QQ 15(DX)(BX*2), K3, Y5                    // 62f17d2b7aac5a0f000000
	VCVTTPS2QQ X2, K3, Y24                             // 62617d2b7ac2
	VCVTTPS2QQ X24, K3, Y24                            // 62017d2b7ac0
	VCVTTPS2QQ (R8), K3, Y24                           // 62417d2b7a00
	VCVTTPS2QQ 15(DX)(BX*2), K3, Y24                   // 62617d2b7a845a0f000000
	VCVTTPS2QQ X2, K3, Y21                             // 62e17d2b7aea
	VCVTTPS2QQ X24, K3, Y21                            // 62817d2b7ae8
	VCVTTPS2QQ (R8), K3, Y21                           // 62c17d2b7a28
	VCVTTPS2QQ 15(DX)(BX*2), K3, Y21                   // 62e17d2b7aac5a0f000000
	VCVTTPS2QQ Y16, K2, Z12                            // 62317d4a7ae0
	VCVTTPS2QQ Y9, K2, Z12                             // 62517d4a7ae1
	VCVTTPS2QQ Y13, K2, Z12                            // 62517d4a7ae5
	VCVTTPS2QQ Y16, K2, Z22                            // 62a17d4a7af0
	VCVTTPS2QQ Y9, K2, Z22                             // 62c17d4a7af1
	VCVTTPS2QQ Y13, K2, Z22                            // 62c17d4a7af5
	VCVTTPS2QQ Y9, K1, Z11                             // 62517d497ad9
	VCVTTPS2QQ Y6, K1, Z11                             // 62717d497ade
	VCVTTPS2QQ Y3, K1, Z11                             // 62717d497adb
	VCVTTPS2QQ -7(DI)(R8*1), K1, Z11                   // 62317d497a9c07f9ffffff
	VCVTTPS2QQ (SP), K1, Z11                           // 62717d497a1c24
	VCVTTPS2QQ Y9, K1, Z5                              // 62d17d497ae9
	VCVTTPS2QQ Y6, K1, Z5                              // 62f17d497aee
	VCVTTPS2QQ Y3, K1, Z5                              // 62f17d497aeb
	VCVTTPS2QQ -7(DI)(R8*1), K1, Z5                    // 62b17d497aac07f9ffffff
	VCVTTPS2QQ (SP), K1, Z5                            // 62f17d497a2c24
	VCVTTPS2UQQ X13, K1, X11                           // 62517d0978dd
	VCVTTPS2UQQ X6, K1, X11                            // 62717d0978de
	VCVTTPS2UQQ X12, K1, X11                           // 62517d0978dc
	VCVTTPS2UQQ -17(BP), K1, X11                       // 62717d09789defffffff
	VCVTTPS2UQQ -15(R14)(R15*8), K1, X11               // 62117d09789cfef1ffffff
	VCVTTPS2UQQ X13, K1, X15                           // 62517d0978fd
	VCVTTPS2UQQ X6, K1, X15                            // 62717d0978fe
	VCVTTPS2UQQ X12, K1, X15                           // 62517d0978fc
	VCVTTPS2UQQ -17(BP), K1, X15                       // 62717d0978bdefffffff
	VCVTTPS2UQQ -15(R14)(R15*8), K1, X15               // 62117d0978bcfef1ffffff
	VCVTTPS2UQQ X13, K1, X30                           // 62417d0978f5
	VCVTTPS2UQQ X6, K1, X30                            // 62617d0978f6
	VCVTTPS2UQQ X12, K1, X30                           // 62417d0978f4
	VCVTTPS2UQQ -17(BP), K1, X30                       // 62617d0978b5efffffff
	VCVTTPS2UQQ -15(R14)(R15*8), K1, X30               // 62017d0978b4fef1ffffff
	VCVTTPS2UQQ X23, K1, Y14                           // 62317d2978f7
	VCVTTPS2UQQ X30, K1, Y14                           // 62117d2978f6
	VCVTTPS2UQQ X8, K1, Y14                            // 62517d2978f0
	VCVTTPS2UQQ -17(BP)(SI*2), K1, Y14                 // 62717d2978b475efffffff
	VCVTTPS2UQQ 7(AX)(CX*2), K1, Y14                   // 62717d2978b44807000000
	VCVTTPS2UQQ X23, K1, Y18                           // 62a17d2978d7
	VCVTTPS2UQQ X30, K1, Y18                           // 62817d2978d6
	VCVTTPS2UQQ X8, K1, Y18                            // 62c17d2978d0
	VCVTTPS2UQQ -17(BP)(SI*2), K1, Y18                 // 62e17d29789475efffffff
	VCVTTPS2UQQ 7(AX)(CX*2), K1, Y18                   // 62e17d2978944807000000
	VCVTTPS2UQQ X23, K1, Y31                           // 62217d2978ff
	VCVTTPS2UQQ X30, K1, Y31                           // 62017d2978fe
	VCVTTPS2UQQ X8, K1, Y31                            // 62417d2978f8
	VCVTTPS2UQQ -17(BP)(SI*2), K1, Y31                 // 62617d2978bc75efffffff
	VCVTTPS2UQQ 7(AX)(CX*2), K1, Y31                   // 62617d2978bc4807000000
	VCVTTPS2UQQ Y18, K7, Z6                            // 62b17d4f78f2
	VCVTTPS2UQQ Y3, K7, Z6                             // 62f17d4f78f3
	VCVTTPS2UQQ Y24, K7, Z6                            // 62917d4f78f0
	VCVTTPS2UQQ Y18, K7, Z22                           // 62a17d4f78f2
	VCVTTPS2UQQ Y3, K7, Z22                            // 62e17d4f78f3
	VCVTTPS2UQQ Y24, K7, Z22                           // 62817d4f78f0
	VCVTTPS2UQQ Y2, K2, Z1                             // 62f17d4a78ca
	VCVTTPS2UQQ Y7, K2, Z1                             // 62f17d4a78cf
	VCVTTPS2UQQ Y21, K2, Z1                            // 62b17d4a78cd
	VCVTTPS2UQQ 99(R15)(R15*8), K2, Z1                 // 62917d4a788cff63000000
	VCVTTPS2UQQ 7(AX)(CX*8), K2, Z1                    // 62f17d4a788cc807000000
	VCVTTPS2UQQ Y2, K2, Z15                            // 62717d4a78fa
	VCVTTPS2UQQ Y7, K2, Z15                            // 62717d4a78ff
	VCVTTPS2UQQ Y21, K2, Z15                           // 62317d4a78fd
	VCVTTPS2UQQ 99(R15)(R15*8), K2, Z15                // 62117d4a78bcff63000000
	VCVTTPS2UQQ 7(AX)(CX*8), K2, Z15                   // 62717d4a78bcc807000000
	VCVTUQQ2PD X13, K6, X21                            // 62c1fe0e7aed
	VCVTUQQ2PD X0, K6, X21                             // 62e1fe0e7ae8
	VCVTUQQ2PD X30, K6, X21                            // 6281fe0e7aee
	VCVTUQQ2PD 15(R8)(R14*8), K6, X21                  // 6281fe0e7aacf00f000000
	VCVTUQQ2PD -15(R14)(R15*2), K6, X21                // 6281fe0e7aac7ef1ffffff
	VCVTUQQ2PD X13, K6, X1                             // 62d1fe0e7acd
	VCVTUQQ2PD X0, K6, X1                              // 62f1fe0e7ac8
	VCVTUQQ2PD X30, K6, X1                             // 6291fe0e7ace
	VCVTUQQ2PD 15(R8)(R14*8), K6, X1                   // 6291fe0e7a8cf00f000000
	VCVTUQQ2PD -15(R14)(R15*2), K6, X1                 // 6291fe0e7a8c7ef1ffffff
	VCVTUQQ2PD X13, K6, X11                            // 6251fe0e7add
	VCVTUQQ2PD X0, K6, X11                             // 6271fe0e7ad8
	VCVTUQQ2PD X30, K6, X11                            // 6211fe0e7ade
	VCVTUQQ2PD 15(R8)(R14*8), K6, X11                  // 6211fe0e7a9cf00f000000
	VCVTUQQ2PD -15(R14)(R15*2), K6, X11                // 6211fe0e7a9c7ef1ffffff
	VCVTUQQ2PD Y11, K3, Y28                            // 6241fe2b7ae3
	VCVTUQQ2PD Y27, K3, Y28                            // 6201fe2b7ae3
	VCVTUQQ2PD Y17, K3, Y28                            // 6221fe2b7ae1
	VCVTUQQ2PD 99(R15)(R15*4), K3, Y28                 // 6201fe2b7aa4bf63000000
	VCVTUQQ2PD 15(DX), K3, Y28                         // 6261fe2b7aa20f000000
	VCVTUQQ2PD Y11, K3, Y1                             // 62d1fe2b7acb
	VCVTUQQ2PD Y27, K3, Y1                             // 6291fe2b7acb
	VCVTUQQ2PD Y17, K3, Y1                             // 62b1fe2b7ac9
	VCVTUQQ2PD 99(R15)(R15*4), K3, Y1                  // 6291fe2b7a8cbf63000000
	VCVTUQQ2PD 15(DX), K3, Y1                          // 62f1fe2b7a8a0f000000
	VCVTUQQ2PD Y11, K3, Y8                             // 6251fe2b7ac3
	VCVTUQQ2PD Y27, K3, Y8                             // 6211fe2b7ac3
	VCVTUQQ2PD Y17, K3, Y8                             // 6231fe2b7ac1
	VCVTUQQ2PD 99(R15)(R15*4), K3, Y8                  // 6211fe2b7a84bf63000000
	VCVTUQQ2PD 15(DX), K3, Y8                          // 6271fe2b7a820f000000
	VCVTUQQ2PD Z12, K7, Z1                             // 62d1fe4f7acc
	VCVTUQQ2PD Z16, K7, Z1                             // 62b1fe4f7ac8
	VCVTUQQ2PD Z12, K7, Z3                             // 62d1fe4f7adc
	VCVTUQQ2PD Z16, K7, Z3                             // 62b1fe4f7ad8
	VCVTUQQ2PD Z14, K4, Z28                            // 6241fe4c7ae6
	VCVTUQQ2PD Z28, K4, Z28                            // 6201fe4c7ae4
	VCVTUQQ2PD 15(R8)(R14*4), K4, Z28                  // 6201fe4c7aa4b00f000000
	VCVTUQQ2PD -7(CX)(DX*4), K4, Z28                   // 6261fe4c7aa491f9ffffff
	VCVTUQQ2PD Z14, K4, Z13                            // 6251fe4c7aee
	VCVTUQQ2PD Z28, K4, Z13                            // 6211fe4c7aec
	VCVTUQQ2PD 15(R8)(R14*4), K4, Z13                  // 6211fe4c7aacb00f000000
	VCVTUQQ2PD -7(CX)(DX*4), K4, Z13                   // 6271fe4c7aac91f9ffffff
	VCVTUQQ2PS Z3, K4, Y16                             // 62e1ff4c7ac3
	VCVTUQQ2PS Z12, K4, Y16                            // 62c1ff4c7ac4
	VCVTUQQ2PS Z3, K4, Y12                             // 6271ff4c7ae3
	VCVTUQQ2PS Z12, K4, Y12                            // 6251ff4c7ae4
	VCVTUQQ2PS Z3, K4, Y6                              // 62f1ff4c7af3
	VCVTUQQ2PS Z12, K4, Y6                             // 62d1ff4c7af4
	VCVTUQQ2PS Z15, K7, Y26                            // 6241ff4f7ad7
	VCVTUQQ2PS Z30, K7, Y26                            // 6201ff4f7ad6
	VCVTUQQ2PS (R8), K7, Y26                           // 6241ff4f7a10
	VCVTUQQ2PS 15(DX)(BX*2), K7, Y26                   // 6261ff4f7a945a0f000000
	VCVTUQQ2PS Z15, K7, Y3                             // 62d1ff4f7adf
	VCVTUQQ2PS Z30, K7, Y3                             // 6291ff4f7ade
	VCVTUQQ2PS (R8), K7, Y3                            // 62d1ff4f7a18
	VCVTUQQ2PS 15(DX)(BX*2), K7, Y3                    // 62f1ff4f7a9c5a0f000000
	VCVTUQQ2PS Z15, K7, Y8                             // 6251ff4f7ac7
	VCVTUQQ2PS Z30, K7, Y8                             // 6211ff4f7ac6
	VCVTUQQ2PS (R8), K7, Y8                            // 6251ff4f7a00
	VCVTUQQ2PS 15(DX)(BX*2), K7, Y8                    // 6271ff4f7a845a0f000000
	VCVTUQQ2PSX X14, K2, X16                           // 62c1ff0a7ac6
	VCVTUQQ2PSX X19, K2, X16                           // 62a1ff0a7ac3
	VCVTUQQ2PSX X8, K2, X16                            // 62c1ff0a7ac0
	VCVTUQQ2PSX -15(R14)(R15*1), K2, X16               // 6281ff0a7a843ef1ffffff
	VCVTUQQ2PSX -15(BX), K2, X16                       // 62e1ff0a7a83f1ffffff
	VCVTUQQ2PSX X14, K2, X14                           // 6251ff0a7af6
	VCVTUQQ2PSX X19, K2, X14                           // 6231ff0a7af3
	VCVTUQQ2PSX X8, K2, X14                            // 6251ff0a7af0
	VCVTUQQ2PSX -15(R14)(R15*1), K2, X14               // 6211ff0a7ab43ef1ffffff
	VCVTUQQ2PSX -15(BX), K2, X14                       // 6271ff0a7ab3f1ffffff
	VCVTUQQ2PSX X14, K2, X11                           // 6251ff0a7ade
	VCVTUQQ2PSX X19, K2, X11                           // 6231ff0a7adb
	VCVTUQQ2PSX X8, K2, X11                            // 6251ff0a7ad8
	VCVTUQQ2PSX -15(R14)(R15*1), K2, X11               // 6211ff0a7a9c3ef1ffffff
	VCVTUQQ2PSX -15(BX), K2, X11                       // 6271ff0a7a9bf1ffffff
	VCVTUQQ2PSY Y28, K5, X8                            // 6211ff2d7ac4
	VCVTUQQ2PSY Y1, K5, X8                             // 6271ff2d7ac1
	VCVTUQQ2PSY Y23, K5, X8                            // 6231ff2d7ac7
	VCVTUQQ2PSY (CX), K5, X8                           // 6271ff2d7a01
	VCVTUQQ2PSY 99(R15), K5, X8                        // 6251ff2d7a8763000000
	VCVTUQQ2PSY Y28, K5, X26                           // 6201ff2d7ad4
	VCVTUQQ2PSY Y1, K5, X26                            // 6261ff2d7ad1
	VCVTUQQ2PSY Y23, K5, X26                           // 6221ff2d7ad7
	VCVTUQQ2PSY (CX), K5, X26                          // 6261ff2d7a11
	VCVTUQQ2PSY 99(R15), K5, X26                       // 6241ff2d7a9763000000
	VCVTUQQ2PSY Y28, K5, X23                           // 6281ff2d7afc
	VCVTUQQ2PSY Y1, K5, X23                            // 62e1ff2d7af9
	VCVTUQQ2PSY Y23, K5, X23                           // 62a1ff2d7aff
	VCVTUQQ2PSY (CX), K5, X23                          // 62e1ff2d7a39
	VCVTUQQ2PSY 99(R15), K5, X23                       // 62c1ff2d7abf63000000
	VEXTRACTF32X8 $0, Z12, K4, Y18                     // 62337d4c1be200
	VEXTRACTF32X8 $0, Z13, K4, Y18                     // 62337d4c1bea00
	VEXTRACTF32X8 $0, Z12, K4, Y24                     // 62137d4c1be000
	VEXTRACTF32X8 $0, Z13, K4, Y24                     // 62137d4c1be800
	VEXTRACTF32X8 $0, Z12, K4, Y9                      // 62537d4c1be100
	VEXTRACTF32X8 $0, Z13, K4, Y9                      // 62537d4c1be900
	VEXTRACTF32X8 $0, Z12, K4, 15(R8)                  // 62537d4c1ba00f00000000
	VEXTRACTF32X8 $0, Z13, K4, 15(R8)                  // 62537d4c1ba80f00000000
	VEXTRACTF32X8 $0, Z12, K4, (BP)                    // 62737d4c1b650000
	VEXTRACTF32X8 $0, Z13, K4, (BP)                    // 62737d4c1b6d0000
	VEXTRACTF64X2 $1, Y3, K4, X8                       // 62d3fd2c19d801
	VEXTRACTF64X2 $1, Y19, K4, X8                      // 62c3fd2c19d801
	VEXTRACTF64X2 $1, Y23, K4, X8                      // 62c3fd2c19f801
	VEXTRACTF64X2 $1, Y3, K4, X1                       // 62f3fd2c19d901
	VEXTRACTF64X2 $1, Y19, K4, X1                      // 62e3fd2c19d901
	VEXTRACTF64X2 $1, Y23, K4, X1                      // 62e3fd2c19f901
	VEXTRACTF64X2 $1, Y3, K4, X0                       // 62f3fd2c19d801
	VEXTRACTF64X2 $1, Y19, K4, X0                      // 62e3fd2c19d801
	VEXTRACTF64X2 $1, Y23, K4, X0                      // 62e3fd2c19f801
	VEXTRACTF64X2 $1, Y3, K4, -17(BP)(SI*8)            // 62f3fd2c199cf5efffffff01
	VEXTRACTF64X2 $1, Y19, K4, -17(BP)(SI*8)           // 62e3fd2c199cf5efffffff01
	VEXTRACTF64X2 $1, Y23, K4, -17(BP)(SI*8)           // 62e3fd2c19bcf5efffffff01
	VEXTRACTF64X2 $1, Y3, K4, (R15)                    // 62d3fd2c191f01
	VEXTRACTF64X2 $1, Y19, K4, (R15)                   // 62c3fd2c191f01
	VEXTRACTF64X2 $1, Y23, K4, (R15)                   // 62c3fd2c193f01
	VEXTRACTF64X2 $0, Z21, K7, X15                     // 62c3fd4f19ef00
	VEXTRACTF64X2 $0, Z9, K7, X15                      // 6253fd4f19cf00
	VEXTRACTF64X2 $0, Z21, K7, X0                      // 62e3fd4f19e800
	VEXTRACTF64X2 $0, Z9, K7, X0                       // 6273fd4f19c800
	VEXTRACTF64X2 $0, Z21, K7, X16                     // 62a3fd4f19e800
	VEXTRACTF64X2 $0, Z9, K7, X16                      // 6233fd4f19c800
	VEXTRACTF64X2 $0, Z21, K7, 7(SI)(DI*8)             // 62e3fd4f19acfe0700000000
	VEXTRACTF64X2 $0, Z9, K7, 7(SI)(DI*8)              // 6273fd4f198cfe0700000000
	VEXTRACTF64X2 $0, Z21, K7, -15(R14)                // 62c3fd4f19aef1ffffff00
	VEXTRACTF64X2 $0, Z9, K7, -15(R14)                 // 6253fd4f198ef1ffffff00
	VEXTRACTI32X8 $1, Z23, K4, Y21                     // 62a37d4c3bfd01
	VEXTRACTI32X8 $1, Z9, K4, Y21                      // 62337d4c3bcd01
	VEXTRACTI32X8 $1, Z23, K4, Y20                     // 62a37d4c3bfc01
	VEXTRACTI32X8 $1, Z9, K4, Y20                      // 62337d4c3bcc01
	VEXTRACTI32X8 $1, Z23, K4, Y6                      // 62e37d4c3bfe01
	VEXTRACTI32X8 $1, Z9, K4, Y6                       // 62737d4c3bce01
	VEXTRACTI32X8 $1, Z23, K4, -15(R14)(R15*1)         // 62837d4c3bbc3ef1ffffff01
	VEXTRACTI32X8 $1, Z9, K4, -15(R14)(R15*1)          // 62137d4c3b8c3ef1ffffff01
	VEXTRACTI32X8 $1, Z23, K4, -15(BX)                 // 62e37d4c3bbbf1ffffff01
	VEXTRACTI32X8 $1, Z9, K4, -15(BX)                  // 62737d4c3b8bf1ffffff01
	VEXTRACTI64X2 $0, Y31, K2, X7                      // 6263fd2a39ff00
	VEXTRACTI64X2 $0, Y6, K2, X7                       // 62f3fd2a39f700
	VEXTRACTI64X2 $0, Y11, K2, X7                      // 6273fd2a39df00
	VEXTRACTI64X2 $0, Y31, K2, X16                     // 6223fd2a39f800
	VEXTRACTI64X2 $0, Y6, K2, X16                      // 62b3fd2a39f000
	VEXTRACTI64X2 $0, Y11, K2, X16                     // 6233fd2a39d800
	VEXTRACTI64X2 $0, Y31, K2, X31                     // 6203fd2a39ff00
	VEXTRACTI64X2 $0, Y6, K2, X31                      // 6293fd2a39f700
	VEXTRACTI64X2 $0, Y11, K2, X31                     // 6213fd2a39df00
	VEXTRACTI64X2 $0, Y31, K2, -7(CX)                  // 6263fd2a39b9f9ffffff00
	VEXTRACTI64X2 $0, Y6, K2, -7(CX)                   // 62f3fd2a39b1f9ffffff00
	VEXTRACTI64X2 $0, Y11, K2, -7(CX)                  // 6273fd2a3999f9ffffff00
	VEXTRACTI64X2 $0, Y31, K2, 15(DX)(BX*4)            // 6263fd2a39bc9a0f00000000
	VEXTRACTI64X2 $0, Y6, K2, 15(DX)(BX*4)             // 62f3fd2a39b49a0f00000000
	VEXTRACTI64X2 $0, Y11, K2, 15(DX)(BX*4)            // 6273fd2a399c9a0f00000000
	VEXTRACTI64X2 $2, Z27, K2, X1                      // 6263fd4a39d902
	VEXTRACTI64X2 $2, Z14, K2, X1                      // 6273fd4a39f102
	VEXTRACTI64X2 $2, Z27, K2, X7                      // 6263fd4a39df02
	VEXTRACTI64X2 $2, Z14, K2, X7                      // 6273fd4a39f702
	VEXTRACTI64X2 $2, Z27, K2, X9                      // 6243fd4a39d902
	VEXTRACTI64X2 $2, Z14, K2, X9                      // 6253fd4a39f102
	VEXTRACTI64X2 $2, Z27, K2, 99(R15)(R15*8)          // 6203fd4a399cff6300000002
	VEXTRACTI64X2 $2, Z14, K2, 99(R15)(R15*8)          // 6213fd4a39b4ff6300000002
	VEXTRACTI64X2 $2, Z27, K2, 7(AX)(CX*8)             // 6263fd4a399cc80700000002
	VEXTRACTI64X2 $2, Z14, K2, 7(AX)(CX*8)             // 6273fd4a39b4c80700000002
	VFPCLASSPDX $65, X14, K4, K1                       // 62d3fd0c66ce41
	VFPCLASSPDX $65, X19, K4, K1                       // 62b3fd0c66cb41
	VFPCLASSPDX $65, X8, K4, K1                        // 62d3fd0c66c841
	VFPCLASSPDX $65, (R14), K4, K1                     // 62d3fd0c660e41
	VFPCLASSPDX $65, -7(DI)(R8*8), K4, K1              // 62b3fd0c668cc7f9ffffff41
	VFPCLASSPDX $65, X14, K4, K3                       // 62d3fd0c66de41
	VFPCLASSPDX $65, X19, K4, K3                       // 62b3fd0c66db41
	VFPCLASSPDX $65, X8, K4, K3                        // 62d3fd0c66d841
	VFPCLASSPDX $65, (R14), K4, K3                     // 62d3fd0c661e41
	VFPCLASSPDX $65, -7(DI)(R8*8), K4, K3              // 62b3fd0c669cc7f9ffffff41
	VFPCLASSPDY $67, Y31, K1, K6                       // 6293fd2966f743
	VFPCLASSPDY $67, Y5, K1, K6                        // 62f3fd2966f543
	VFPCLASSPDY $67, Y0, K1, K6                        // 62f3fd2966f043
	VFPCLASSPDY $67, 7(SI)(DI*8), K1, K6               // 62f3fd2966b4fe0700000043
	VFPCLASSPDY $67, -15(R14), K1, K6                  // 62d3fd2966b6f1ffffff43
	VFPCLASSPDY $67, Y31, K1, K7                       // 6293fd2966ff43
	VFPCLASSPDY $67, Y5, K1, K7                        // 62f3fd2966fd43
	VFPCLASSPDY $67, Y0, K1, K7                        // 62f3fd2966f843
	VFPCLASSPDY $67, 7(SI)(DI*8), K1, K7               // 62f3fd2966bcfe0700000043
	VFPCLASSPDY $67, -15(R14), K1, K7                  // 62d3fd2966bef1ffffff43
	VFPCLASSPDZ $127, Z3, K3, K6                       // 62f3fd4b66f37f
	VFPCLASSPDZ $127, Z27, K3, K6                      // 6293fd4b66f37f
	VFPCLASSPDZ $127, 7(AX)(CX*4), K3, K6              // 62f3fd4b66b488070000007f
	VFPCLASSPDZ $127, 7(AX)(CX*1), K3, K6              // 62f3fd4b66b408070000007f
	VFPCLASSPDZ $127, Z3, K3, K4                       // 62f3fd4b66e37f
	VFPCLASSPDZ $127, Z27, K3, K4                      // 6293fd4b66e37f
	VFPCLASSPDZ $127, 7(AX)(CX*4), K3, K4              // 62f3fd4b66a488070000007f
	VFPCLASSPDZ $127, 7(AX)(CX*1), K3, K4              // 62f3fd4b66a408070000007f
	VFPCLASSPSX $0, X8, K4, K4                         // 62d37d0c66e000
	VFPCLASSPSX $0, X26, K4, K4                        // 62937d0c66e200
	VFPCLASSPSX $0, X23, K4, K4                        // 62b37d0c66e700
	VFPCLASSPSX $0, 99(R15)(R15*4), K4, K4             // 62937d0c66a4bf6300000000
	VFPCLASSPSX $0, 15(DX), K4, K4                     // 62f37d0c66a20f00000000
	VFPCLASSPSX $0, X8, K4, K6                         // 62d37d0c66f000
	VFPCLASSPSX $0, X26, K4, K6                        // 62937d0c66f200
	VFPCLASSPSX $0, X23, K4, K6                        // 62b37d0c66f700
	VFPCLASSPSX $0, 99(R15)(R15*4), K4, K6             // 62937d0c66b4bf6300000000
	VFPCLASSPSX $0, 15(DX), K4, K6                     // 62f37d0c66b20f00000000
	VFPCLASSPSY $97, Y5, K5, K4                        // 62f37d2d66e561
	VFPCLASSPSY $97, Y19, K5, K4                       // 62b37d2d66e361
	VFPCLASSPSY $97, Y31, K5, K4                       // 62937d2d66e761
	VFPCLASSPSY $97, 7(SI)(DI*1), K5, K4               // 62f37d2d66a43e0700000061
	VFPCLASSPSY $97, 15(DX)(BX*8), K5, K4              // 62f37d2d66a4da0f00000061
	VFPCLASSPSY $97, Y5, K5, K5                        // 62f37d2d66ed61
	VFPCLASSPSY $97, Y19, K5, K5                       // 62b37d2d66eb61
	VFPCLASSPSY $97, Y31, K5, K5                       // 62937d2d66ef61
	VFPCLASSPSY $97, 7(SI)(DI*1), K5, K5               // 62f37d2d66ac3e0700000061
	VFPCLASSPSY $97, 15(DX)(BX*8), K5, K5              // 62f37d2d66acda0f00000061
	VFPCLASSPSZ $81, Z7, K7, K2                        // 62f37d4f66d751
	VFPCLASSPSZ $81, Z9, K7, K2                        // 62d37d4f66d151
	VFPCLASSPSZ $81, (SI), K7, K2                      // 62f37d4f661651
	VFPCLASSPSZ $81, 7(SI)(DI*2), K7, K2               // 62f37d4f66947e0700000051
	VFPCLASSPSZ $81, Z7, K7, K7                        // 62f37d4f66ff51
	VFPCLASSPSZ $81, Z9, K7, K7                        // 62d37d4f66f951
	VFPCLASSPSZ $81, (SI), K7, K7                      // 62f37d4f663e51
	VFPCLASSPSZ $81, 7(SI)(DI*2), K7, K7               // 62f37d4f66bc7e0700000051
	VFPCLASSSD $42, X12, K7, K0                        // 62d3fd0f67c42a or 62d3fd2f67c42a or 62d3fd4f67c42a
	VFPCLASSSD $42, X16, K7, K0                        // 62b3fd0f67c02a or 62b3fd2f67c02a or 62b3fd4f67c02a
	VFPCLASSSD $42, X23, K7, K0                        // 62b3fd0f67c72a or 62b3fd2f67c72a or 62b3fd4f67c72a
	VFPCLASSSD $42, (BX), K7, K0                       // 62f3fd0f67032a or 62f3fd2f67032a or 62f3fd4f67032a
	VFPCLASSSD $42, -17(BP)(SI*1), K7, K0              // 62f3fd0f678435efffffff2a or 62f3fd2f678435efffffff2a or 62f3fd4f678435efffffff2a
	VFPCLASSSD $42, X12, K7, K5                        // 62d3fd0f67ec2a or 62d3fd2f67ec2a or 62d3fd4f67ec2a
	VFPCLASSSD $42, X16, K7, K5                        // 62b3fd0f67e82a or 62b3fd2f67e82a or 62b3fd4f67e82a
	VFPCLASSSD $42, X23, K7, K5                        // 62b3fd0f67ef2a or 62b3fd2f67ef2a or 62b3fd4f67ef2a
	VFPCLASSSD $42, (BX), K7, K5                       // 62f3fd0f672b2a or 62f3fd2f672b2a or 62f3fd4f672b2a
	VFPCLASSSD $42, -17(BP)(SI*1), K7, K5              // 62f3fd0f67ac35efffffff2a or 62f3fd2f67ac35efffffff2a or 62f3fd4f67ac35efffffff2a
	VFPCLASSSS $79, X23, K6, K6                        // 62b37d0e67f74f or 62b37d2e67f74f or 62b37d4e67f74f
	VFPCLASSSS $79, X11, K6, K6                        // 62d37d0e67f34f or 62d37d2e67f34f or 62d37d4e67f34f
	VFPCLASSSS $79, X31, K6, K6                        // 62937d0e67f74f or 62937d2e67f74f or 62937d4e67f74f
	VFPCLASSSS $79, 7(SI)(DI*1), K6, K6                // 62f37d0e67b43e070000004f or 62f37d2e67b43e070000004f or 62f37d4e67b43e070000004f
	VFPCLASSSS $79, 15(DX)(BX*8), K6, K6               // 62f37d0e67b4da0f0000004f or 62f37d2e67b4da0f0000004f or 62f37d4e67b4da0f0000004f
	VFPCLASSSS $79, X23, K6, K5                        // 62b37d0e67ef4f or 62b37d2e67ef4f or 62b37d4e67ef4f
	VFPCLASSSS $79, X11, K6, K5                        // 62d37d0e67eb4f or 62d37d2e67eb4f or 62d37d4e67eb4f
	VFPCLASSSS $79, X31, K6, K5                        // 62937d0e67ef4f or 62937d2e67ef4f or 62937d4e67ef4f
	VFPCLASSSS $79, 7(SI)(DI*1), K6, K5                // 62f37d0e67ac3e070000004f or 62f37d2e67ac3e070000004f or 62f37d4e67ac3e070000004f
	VFPCLASSSS $79, 15(DX)(BX*8), K6, K5               // 62f37d0e67acda0f0000004f or 62f37d2e67acda0f0000004f or 62f37d4e67acda0f0000004f
	VINSERTF32X8 $1, Y12, Z0, K2, Z23                  // 62c37d4a1afc01
	VINSERTF32X8 $1, Y21, Z0, K2, Z23                  // 62a37d4a1afd01
	VINSERTF32X8 $1, Y14, Z0, K2, Z23                  // 62c37d4a1afe01
	VINSERTF32X8 $1, 17(SP)(BP*1), Z0, K2, Z23         // 62e37d4a1abc2c1100000001
	VINSERTF32X8 $1, -7(CX)(DX*8), Z0, K2, Z23         // 62e37d4a1abcd1f9ffffff01
	VINSERTF32X8 $1, Y12, Z11, K2, Z23                 // 62c3254a1afc01
	VINSERTF32X8 $1, Y21, Z11, K2, Z23                 // 62a3254a1afd01
	VINSERTF32X8 $1, Y14, Z11, K2, Z23                 // 62c3254a1afe01
	VINSERTF32X8 $1, 17(SP)(BP*1), Z11, K2, Z23        // 62e3254a1abc2c1100000001
	VINSERTF32X8 $1, -7(CX)(DX*8), Z11, K2, Z23        // 62e3254a1abcd1f9ffffff01
	VINSERTF32X8 $1, Y12, Z0, K2, Z19                  // 62c37d4a1adc01
	VINSERTF32X8 $1, Y21, Z0, K2, Z19                  // 62a37d4a1add01
	VINSERTF32X8 $1, Y14, Z0, K2, Z19                  // 62c37d4a1ade01
	VINSERTF32X8 $1, 17(SP)(BP*1), Z0, K2, Z19         // 62e37d4a1a9c2c1100000001
	VINSERTF32X8 $1, -7(CX)(DX*8), Z0, K2, Z19         // 62e37d4a1a9cd1f9ffffff01
	VINSERTF32X8 $1, Y12, Z11, K2, Z19                 // 62c3254a1adc01
	VINSERTF32X8 $1, Y21, Z11, K2, Z19                 // 62a3254a1add01
	VINSERTF32X8 $1, Y14, Z11, K2, Z19                 // 62c3254a1ade01
	VINSERTF32X8 $1, 17(SP)(BP*1), Z11, K2, Z19        // 62e3254a1a9c2c1100000001
	VINSERTF32X8 $1, -7(CX)(DX*8), Z11, K2, Z19        // 62e3254a1a9cd1f9ffffff01
	VINSERTF64X2 $0, X3, Y16, K4, Y30                  // 6263fd2418f300
	VINSERTF64X2 $0, X26, Y16, K4, Y30                 // 6203fd2418f200
	VINSERTF64X2 $0, X23, Y16, K4, Y30                 // 6223fd2418f700
	VINSERTF64X2 $0, 7(AX)(CX*4), Y16, K4, Y30         // 6263fd2418b4880700000000
	VINSERTF64X2 $0, 7(AX)(CX*1), Y16, K4, Y30         // 6263fd2418b4080700000000
	VINSERTF64X2 $0, X3, Y1, K4, Y30                   // 6263f52c18f300
	VINSERTF64X2 $0, X26, Y1, K4, Y30                  // 6203f52c18f200
	VINSERTF64X2 $0, X23, Y1, K4, Y30                  // 6223f52c18f700
	VINSERTF64X2 $0, 7(AX)(CX*4), Y1, K4, Y30          // 6263f52c18b4880700000000
	VINSERTF64X2 $0, 7(AX)(CX*1), Y1, K4, Y30          // 6263f52c18b4080700000000
	VINSERTF64X2 $0, X3, Y30, K4, Y30                  // 62638d2418f300
	VINSERTF64X2 $0, X26, Y30, K4, Y30                 // 62038d2418f200
	VINSERTF64X2 $0, X23, Y30, K4, Y30                 // 62238d2418f700
	VINSERTF64X2 $0, 7(AX)(CX*4), Y30, K4, Y30         // 62638d2418b4880700000000
	VINSERTF64X2 $0, 7(AX)(CX*1), Y30, K4, Y30         // 62638d2418b4080700000000
	VINSERTF64X2 $0, X3, Y16, K4, Y26                  // 6263fd2418d300
	VINSERTF64X2 $0, X26, Y16, K4, Y26                 // 6203fd2418d200
	VINSERTF64X2 $0, X23, Y16, K4, Y26                 // 6223fd2418d700
	VINSERTF64X2 $0, 7(AX)(CX*4), Y16, K4, Y26         // 6263fd241894880700000000
	VINSERTF64X2 $0, 7(AX)(CX*1), Y16, K4, Y26         // 6263fd241894080700000000
	VINSERTF64X2 $0, X3, Y1, K4, Y26                   // 6263f52c18d300
	VINSERTF64X2 $0, X26, Y1, K4, Y26                  // 6203f52c18d200
	VINSERTF64X2 $0, X23, Y1, K4, Y26                  // 6223f52c18d700
	VINSERTF64X2 $0, 7(AX)(CX*4), Y1, K4, Y26          // 6263f52c1894880700000000
	VINSERTF64X2 $0, 7(AX)(CX*1), Y1, K4, Y26          // 6263f52c1894080700000000
	VINSERTF64X2 $0, X3, Y30, K4, Y26                  // 62638d2418d300
	VINSERTF64X2 $0, X26, Y30, K4, Y26                 // 62038d2418d200
	VINSERTF64X2 $0, X23, Y30, K4, Y26                 // 62238d2418d700
	VINSERTF64X2 $0, 7(AX)(CX*4), Y30, K4, Y26         // 62638d241894880700000000
	VINSERTF64X2 $0, 7(AX)(CX*1), Y30, K4, Y26         // 62638d241894080700000000
	VINSERTF64X2 $0, X3, Y16, K4, Y7                   // 62f3fd2418fb00
	VINSERTF64X2 $0, X26, Y16, K4, Y7                  // 6293fd2418fa00
	VINSERTF64X2 $0, X23, Y16, K4, Y7                  // 62b3fd2418ff00
	VINSERTF64X2 $0, 7(AX)(CX*4), Y16, K4, Y7          // 62f3fd2418bc880700000000
	VINSERTF64X2 $0, 7(AX)(CX*1), Y16, K4, Y7          // 62f3fd2418bc080700000000
	VINSERTF64X2 $0, X3, Y1, K4, Y7                    // 62f3f52c18fb00
	VINSERTF64X2 $0, X26, Y1, K4, Y7                   // 6293f52c18fa00
	VINSERTF64X2 $0, X23, Y1, K4, Y7                   // 62b3f52c18ff00
	VINSERTF64X2 $0, 7(AX)(CX*4), Y1, K4, Y7           // 62f3f52c18bc880700000000
	VINSERTF64X2 $0, 7(AX)(CX*1), Y1, K4, Y7           // 62f3f52c18bc080700000000
	VINSERTF64X2 $0, X3, Y30, K4, Y7                   // 62f38d2418fb00
	VINSERTF64X2 $0, X26, Y30, K4, Y7                  // 62938d2418fa00
	VINSERTF64X2 $0, X23, Y30, K4, Y7                  // 62b38d2418ff00
	VINSERTF64X2 $0, 7(AX)(CX*4), Y30, K4, Y7          // 62f38d2418bc880700000000
	VINSERTF64X2 $0, 7(AX)(CX*1), Y30, K4, Y7          // 62f38d2418bc080700000000
	VINSERTF64X2 $1, X13, Z24, K1, Z0                  // 62d3bd4118c501
	VINSERTF64X2 $1, X28, Z24, K1, Z0                  // 6293bd4118c401
	VINSERTF64X2 $1, X24, Z24, K1, Z0                  // 6293bd4118c001
	VINSERTF64X2 $1, (SI), Z24, K1, Z0                 // 62f3bd41180601
	VINSERTF64X2 $1, 7(SI)(DI*2), Z24, K1, Z0          // 62f3bd4118847e0700000001
	VINSERTF64X2 $1, X13, Z12, K1, Z0                  // 62d39d4918c501
	VINSERTF64X2 $1, X28, Z12, K1, Z0                  // 62939d4918c401
	VINSERTF64X2 $1, X24, Z12, K1, Z0                  // 62939d4918c001
	VINSERTF64X2 $1, (SI), Z12, K1, Z0                 // 62f39d49180601
	VINSERTF64X2 $1, 7(SI)(DI*2), Z12, K1, Z0          // 62f39d4918847e0700000001
	VINSERTF64X2 $1, X13, Z24, K1, Z25                 // 6243bd4118cd01
	VINSERTF64X2 $1, X28, Z24, K1, Z25                 // 6203bd4118cc01
	VINSERTF64X2 $1, X24, Z24, K1, Z25                 // 6203bd4118c801
	VINSERTF64X2 $1, (SI), Z24, K1, Z25                // 6263bd41180e01
	VINSERTF64X2 $1, 7(SI)(DI*2), Z24, K1, Z25         // 6263bd41188c7e0700000001
	VINSERTF64X2 $1, X13, Z12, K1, Z25                 // 62439d4918cd01
	VINSERTF64X2 $1, X28, Z12, K1, Z25                 // 62039d4918cc01
	VINSERTF64X2 $1, X24, Z12, K1, Z25                 // 62039d4918c801
	VINSERTF64X2 $1, (SI), Z12, K1, Z25                // 62639d49180e01
	VINSERTF64X2 $1, 7(SI)(DI*2), Z12, K1, Z25         // 62639d49188c7e0700000001
	VINSERTI32X8 $1, Y24, Z17, K7, Z20                 // 628375473ae001
	VINSERTI32X8 $1, Y13, Z17, K7, Z20                 // 62c375473ae501
	VINSERTI32X8 $1, Y20, Z17, K7, Z20                 // 62a375473ae401
	VINSERTI32X8 $1, 15(R8)(R14*1), Z17, K7, Z20       // 628375473aa4300f00000001
	VINSERTI32X8 $1, 15(R8)(R14*2), Z17, K7, Z20       // 628375473aa4700f00000001
	VINSERTI32X8 $1, Y24, Z0, K7, Z20                  // 62837d4f3ae001
	VINSERTI32X8 $1, Y13, Z0, K7, Z20                  // 62c37d4f3ae501
	VINSERTI32X8 $1, Y20, Z0, K7, Z20                  // 62a37d4f3ae401
	VINSERTI32X8 $1, 15(R8)(R14*1), Z0, K7, Z20        // 62837d4f3aa4300f00000001
	VINSERTI32X8 $1, 15(R8)(R14*2), Z0, K7, Z20        // 62837d4f3aa4700f00000001
	VINSERTI32X8 $1, Y24, Z17, K7, Z0                  // 629375473ac001
	VINSERTI32X8 $1, Y13, Z17, K7, Z0                  // 62d375473ac501
	VINSERTI32X8 $1, Y20, Z17, K7, Z0                  // 62b375473ac401
	VINSERTI32X8 $1, 15(R8)(R14*1), Z17, K7, Z0        // 629375473a84300f00000001
	VINSERTI32X8 $1, 15(R8)(R14*2), Z17, K7, Z0        // 629375473a84700f00000001
	VINSERTI32X8 $1, Y24, Z0, K7, Z0                   // 62937d4f3ac001
	VINSERTI32X8 $1, Y13, Z0, K7, Z0                   // 62d37d4f3ac501
	VINSERTI32X8 $1, Y20, Z0, K7, Z0                   // 62b37d4f3ac401
	VINSERTI32X8 $1, 15(R8)(R14*1), Z0, K7, Z0         // 62937d4f3a84300f00000001
	VINSERTI32X8 $1, 15(R8)(R14*2), Z0, K7, Z0         // 62937d4f3a84700f00000001
	VINSERTI64X2 $0, X11, Y26, K7, Y14                 // 6253ad2738f300
	VINSERTI64X2 $0, X31, Y26, K7, Y14                 // 6213ad2738f700
	VINSERTI64X2 $0, X3, Y26, K7, Y14                  // 6273ad2738f300
	VINSERTI64X2 $0, 17(SP), Y26, K7, Y14              // 6273ad2738b4241100000000
	VINSERTI64X2 $0, -17(BP)(SI*4), Y26, K7, Y14       // 6273ad2738b4b5efffffff00
	VINSERTI64X2 $0, X11, Y30, K7, Y14                 // 62538d2738f300
	VINSERTI64X2 $0, X31, Y30, K7, Y14                 // 62138d2738f700
	VINSERTI64X2 $0, X3, Y30, K7, Y14                  // 62738d2738f300
	VINSERTI64X2 $0, 17(SP), Y30, K7, Y14              // 62738d2738b4241100000000
	VINSERTI64X2 $0, -17(BP)(SI*4), Y30, K7, Y14       // 62738d2738b4b5efffffff00
	VINSERTI64X2 $0, X11, Y12, K7, Y14                 // 62539d2f38f300
	VINSERTI64X2 $0, X31, Y12, K7, Y14                 // 62139d2f38f700
	VINSERTI64X2 $0, X3, Y12, K7, Y14                  // 62739d2f38f300
	VINSERTI64X2 $0, 17(SP), Y12, K7, Y14              // 62739d2f38b4241100000000
	VINSERTI64X2 $0, -17(BP)(SI*4), Y12, K7, Y14       // 62739d2f38b4b5efffffff00
	VINSERTI64X2 $0, X11, Y26, K7, Y21                 // 62c3ad2738eb00
	VINSERTI64X2 $0, X31, Y26, K7, Y21                 // 6283ad2738ef00
	VINSERTI64X2 $0, X3, Y26, K7, Y21                  // 62e3ad2738eb00
	VINSERTI64X2 $0, 17(SP), Y26, K7, Y21              // 62e3ad2738ac241100000000
	VINSERTI64X2 $0, -17(BP)(SI*4), Y26, K7, Y21       // 62e3ad2738acb5efffffff00
	VINSERTI64X2 $0, X11, Y30, K7, Y21                 // 62c38d2738eb00
	VINSERTI64X2 $0, X31, Y30, K7, Y21                 // 62838d2738ef00
	VINSERTI64X2 $0, X3, Y30, K7, Y21                  // 62e38d2738eb00
	VINSERTI64X2 $0, 17(SP), Y30, K7, Y21              // 62e38d2738ac241100000000
	VINSERTI64X2 $0, -17(BP)(SI*4), Y30, K7, Y21       // 62e38d2738acb5efffffff00
	VINSERTI64X2 $0, X11, Y12, K7, Y21                 // 62c39d2f38eb00
	VINSERTI64X2 $0, X31, Y12, K7, Y21                 // 62839d2f38ef00
	VINSERTI64X2 $0, X3, Y12, K7, Y21                  // 62e39d2f38eb00
	VINSERTI64X2 $0, 17(SP), Y12, K7, Y21              // 62e39d2f38ac241100000000
	VINSERTI64X2 $0, -17(BP)(SI*4), Y12, K7, Y21       // 62e39d2f38acb5efffffff00
	VINSERTI64X2 $0, X11, Y26, K7, Y1                  // 62d3ad2738cb00
	VINSERTI64X2 $0, X31, Y26, K7, Y1                  // 6293ad2738cf00
	VINSERTI64X2 $0, X3, Y26, K7, Y1                   // 62f3ad2738cb00
	VINSERTI64X2 $0, 17(SP), Y26, K7, Y1               // 62f3ad27388c241100000000
	VINSERTI64X2 $0, -17(BP)(SI*4), Y26, K7, Y1        // 62f3ad27388cb5efffffff00
	VINSERTI64X2 $0, X11, Y30, K7, Y1                  // 62d38d2738cb00
	VINSERTI64X2 $0, X31, Y30, K7, Y1                  // 62938d2738cf00
	VINSERTI64X2 $0, X3, Y30, K7, Y1                   // 62f38d2738cb00
	VINSERTI64X2 $0, 17(SP), Y30, K7, Y1               // 62f38d27388c241100000000
	VINSERTI64X2 $0, -17(BP)(SI*4), Y30, K7, Y1        // 62f38d27388cb5efffffff00
	VINSERTI64X2 $0, X11, Y12, K7, Y1                  // 62d39d2f38cb00
	VINSERTI64X2 $0, X31, Y12, K7, Y1                  // 62939d2f38cf00
	VINSERTI64X2 $0, X3, Y12, K7, Y1                   // 62f39d2f38cb00
	VINSERTI64X2 $0, 17(SP), Y12, K7, Y1               // 62f39d2f388c241100000000
	VINSERTI64X2 $0, -17(BP)(SI*4), Y12, K7, Y1        // 62f39d2f388cb5efffffff00
	VINSERTI64X2 $3, X7, Z31, K6, Z17                  // 62e3854638cf03
	VINSERTI64X2 $3, X0, Z31, K6, Z17                  // 62e3854638c803
	VINSERTI64X2 $3, 7(AX), Z31, K6, Z17               // 62e3854638880700000003
	VINSERTI64X2 $3, (DI), Z31, K6, Z17                // 62e38546380f03
	VINSERTI64X2 $3, X7, Z0, K6, Z17                   // 62e3fd4e38cf03
	VINSERTI64X2 $3, X0, Z0, K6, Z17                   // 62e3fd4e38c803
	VINSERTI64X2 $3, 7(AX), Z0, K6, Z17                // 62e3fd4e38880700000003
	VINSERTI64X2 $3, (DI), Z0, K6, Z17                 // 62e3fd4e380f03
	VINSERTI64X2 $3, X7, Z31, K6, Z23                  // 62e3854638ff03
	VINSERTI64X2 $3, X0, Z31, K6, Z23                  // 62e3854638f803
	VINSERTI64X2 $3, 7(AX), Z31, K6, Z23               // 62e3854638b80700000003
	VINSERTI64X2 $3, (DI), Z31, K6, Z23                // 62e38546383f03
	VINSERTI64X2 $3, X7, Z0, K6, Z23                   // 62e3fd4e38ff03
	VINSERTI64X2 $3, X0, Z0, K6, Z23                   // 62e3fd4e38f803
	VINSERTI64X2 $3, 7(AX), Z0, K6, Z23                // 62e3fd4e38b80700000003
	VINSERTI64X2 $3, (DI), Z0, K6, Z23                 // 62e3fd4e383f03
	VORPD X11, X24, K7, X23                            // 62c1bd0756fb
	VORPD X23, X24, K7, X23                            // 62a1bd0756ff
	VORPD X2, X24, K7, X23                             // 62e1bd0756fa
	VORPD -17(BP)(SI*8), X24, K7, X23                  // 62e1bd0756bcf5efffffff
	VORPD (R15), X24, K7, X23                          // 62c1bd07563f
	VORPD X11, X14, K7, X23                            // 62c18d0f56fb
	VORPD X23, X14, K7, X23                            // 62a18d0f56ff
	VORPD X2, X14, K7, X23                             // 62e18d0f56fa
	VORPD -17(BP)(SI*8), X14, K7, X23                  // 62e18d0f56bcf5efffffff
	VORPD (R15), X14, K7, X23                          // 62c18d0f563f
	VORPD X11, X0, K7, X23                             // 62c1fd0f56fb
	VORPD X23, X0, K7, X23                             // 62a1fd0f56ff
	VORPD X2, X0, K7, X23                              // 62e1fd0f56fa
	VORPD -17(BP)(SI*8), X0, K7, X23                   // 62e1fd0f56bcf5efffffff
	VORPD (R15), X0, K7, X23                           // 62c1fd0f563f
	VORPD X11, X24, K7, X11                            // 6251bd0756db
	VORPD X23, X24, K7, X11                            // 6231bd0756df
	VORPD X2, X24, K7, X11                             // 6271bd0756da
	VORPD -17(BP)(SI*8), X24, K7, X11                  // 6271bd07569cf5efffffff
	VORPD (R15), X24, K7, X11                          // 6251bd07561f
	VORPD X11, X14, K7, X11                            // 62518d0f56db
	VORPD X23, X14, K7, X11                            // 62318d0f56df
	VORPD X2, X14, K7, X11                             // 62718d0f56da
	VORPD -17(BP)(SI*8), X14, K7, X11                  // 62718d0f569cf5efffffff
	VORPD (R15), X14, K7, X11                          // 62518d0f561f
	VORPD X11, X0, K7, X11                             // 6251fd0f56db
	VORPD X23, X0, K7, X11                             // 6231fd0f56df
	VORPD X2, X0, K7, X11                              // 6271fd0f56da
	VORPD -17(BP)(SI*8), X0, K7, X11                   // 6271fd0f569cf5efffffff
	VORPD (R15), X0, K7, X11                           // 6251fd0f561f
	VORPD X11, X24, K7, X31                            // 6241bd0756fb
	VORPD X23, X24, K7, X31                            // 6221bd0756ff
	VORPD X2, X24, K7, X31                             // 6261bd0756fa
	VORPD -17(BP)(SI*8), X24, K7, X31                  // 6261bd0756bcf5efffffff
	VORPD (R15), X24, K7, X31                          // 6241bd07563f
	VORPD X11, X14, K7, X31                            // 62418d0f56fb
	VORPD X23, X14, K7, X31                            // 62218d0f56ff
	VORPD X2, X14, K7, X31                             // 62618d0f56fa
	VORPD -17(BP)(SI*8), X14, K7, X31                  // 62618d0f56bcf5efffffff
	VORPD (R15), X14, K7, X31                          // 62418d0f563f
	VORPD X11, X0, K7, X31                             // 6241fd0f56fb
	VORPD X23, X0, K7, X31                             // 6221fd0f56ff
	VORPD X2, X0, K7, X31                              // 6261fd0f56fa
	VORPD -17(BP)(SI*8), X0, K7, X31                   // 6261fd0f56bcf5efffffff
	VORPD (R15), X0, K7, X31                           // 6241fd0f563f
	VORPD Y16, Y5, K1, Y8                              // 6231d52956c0
	VORPD Y9, Y5, K1, Y8                               // 6251d52956c1
	VORPD Y13, Y5, K1, Y8                              // 6251d52956c5
	VORPD 99(R15)(R15*2), Y5, K1, Y8                   // 6211d52956847f63000000
	VORPD -7(DI), Y5, K1, Y8                           // 6271d5295687f9ffffff
	VORPD Y16, Y24, K1, Y8                             // 6231bd2156c0
	VORPD Y9, Y24, K1, Y8                              // 6251bd2156c1
	VORPD Y13, Y24, K1, Y8                             // 6251bd2156c5
	VORPD 99(R15)(R15*2), Y24, K1, Y8                  // 6211bd2156847f63000000
	VORPD -7(DI), Y24, K1, Y8                          // 6271bd215687f9ffffff
	VORPD Y16, Y21, K1, Y8                             // 6231d52156c0
	VORPD Y9, Y21, K1, Y8                              // 6251d52156c1
	VORPD Y13, Y21, K1, Y8                             // 6251d52156c5
	VORPD 99(R15)(R15*2), Y21, K1, Y8                  // 6211d52156847f63000000
	VORPD -7(DI), Y21, K1, Y8                          // 6271d5215687f9ffffff
	VORPD Y16, Y5, K1, Y11                             // 6231d52956d8
	VORPD Y9, Y5, K1, Y11                              // 6251d52956d9
	VORPD Y13, Y5, K1, Y11                             // 6251d52956dd
	VORPD 99(R15)(R15*2), Y5, K1, Y11                  // 6211d529569c7f63000000
	VORPD -7(DI), Y5, K1, Y11                          // 6271d529569ff9ffffff
	VORPD Y16, Y24, K1, Y11                            // 6231bd2156d8
	VORPD Y9, Y24, K1, Y11                             // 6251bd2156d9
	VORPD Y13, Y24, K1, Y11                            // 6251bd2156dd
	VORPD 99(R15)(R15*2), Y24, K1, Y11                 // 6211bd21569c7f63000000
	VORPD -7(DI), Y24, K1, Y11                         // 6271bd21569ff9ffffff
	VORPD Y16, Y21, K1, Y11                            // 6231d52156d8
	VORPD Y9, Y21, K1, Y11                             // 6251d52156d9
	VORPD Y13, Y21, K1, Y11                            // 6251d52156dd
	VORPD 99(R15)(R15*2), Y21, K1, Y11                 // 6211d521569c7f63000000
	VORPD -7(DI), Y21, K1, Y11                         // 6271d521569ff9ffffff
	VORPD Y16, Y5, K1, Y24                             // 6221d52956c0
	VORPD Y9, Y5, K1, Y24                              // 6241d52956c1
	VORPD Y13, Y5, K1, Y24                             // 6241d52956c5
	VORPD 99(R15)(R15*2), Y5, K1, Y24                  // 6201d52956847f63000000
	VORPD -7(DI), Y5, K1, Y24                          // 6261d5295687f9ffffff
	VORPD Y16, Y24, K1, Y24                            // 6221bd2156c0
	VORPD Y9, Y24, K1, Y24                             // 6241bd2156c1
	VORPD Y13, Y24, K1, Y24                            // 6241bd2156c5
	VORPD 99(R15)(R15*2), Y24, K1, Y24                 // 6201bd2156847f63000000
	VORPD -7(DI), Y24, K1, Y24                         // 6261bd215687f9ffffff
	VORPD Y16, Y21, K1, Y24                            // 6221d52156c0
	VORPD Y9, Y21, K1, Y24                             // 6241d52156c1
	VORPD Y13, Y21, K1, Y24                            // 6241d52156c5
	VORPD 99(R15)(R15*2), Y21, K1, Y24                 // 6201d52156847f63000000
	VORPD -7(DI), Y21, K1, Y24                         // 6261d5215687f9ffffff
	VORPD Z9, Z9, K1, Z0                               // 62d1b54956c1
	VORPD Z25, Z9, K1, Z0                              // 6291b54956c1
	VORPD -7(CX), Z9, K1, Z0                           // 62f1b5495681f9ffffff
	VORPD 15(DX)(BX*4), Z9, K1, Z0                     // 62f1b54956849a0f000000
	VORPD Z9, Z3, K1, Z0                               // 62d1e54956c1
	VORPD Z25, Z3, K1, Z0                              // 6291e54956c1
	VORPD -7(CX), Z3, K1, Z0                           // 62f1e5495681f9ffffff
	VORPD 15(DX)(BX*4), Z3, K1, Z0                     // 62f1e54956849a0f000000
	VORPD Z9, Z9, K1, Z26                              // 6241b54956d1
	VORPD Z25, Z9, K1, Z26                             // 6201b54956d1
	VORPD -7(CX), Z9, K1, Z26                          // 6261b5495691f9ffffff
	VORPD 15(DX)(BX*4), Z9, K1, Z26                    // 6261b54956949a0f000000
	VORPD Z9, Z3, K1, Z26                              // 6241e54956d1
	VORPD Z25, Z3, K1, Z26                             // 6201e54956d1
	VORPD -7(CX), Z3, K1, Z26                          // 6261e5495691f9ffffff
	VORPD 15(DX)(BX*4), Z3, K1, Z26                    // 6261e54956949a0f000000
	VORPS X2, X0, K1, X20                              // 62e17c0956e2
	VORPS X8, X0, K1, X20                              // 62c17c0956e0
	VORPS X9, X0, K1, X20                              // 62c17c0956e1
	VORPS 7(SI)(DI*8), X0, K1, X20                     // 62e17c0956a4fe07000000
	VORPS -15(R14), X0, K1, X20                        // 62c17c0956a6f1ffffff
	VORPS X2, X9, K1, X20                              // 62e1340956e2
	VORPS X8, X9, K1, X20                              // 62c1340956e0
	VORPS X9, X9, K1, X20                              // 62c1340956e1
	VORPS 7(SI)(DI*8), X9, K1, X20                     // 62e1340956a4fe07000000
	VORPS -15(R14), X9, K1, X20                        // 62c1340956a6f1ffffff
	VORPS X2, X13, K1, X20                             // 62e1140956e2
	VORPS X8, X13, K1, X20                             // 62c1140956e0
	VORPS X9, X13, K1, X20                             // 62c1140956e1
	VORPS 7(SI)(DI*8), X13, K1, X20                    // 62e1140956a4fe07000000
	VORPS -15(R14), X13, K1, X20                       // 62c1140956a6f1ffffff
	VORPS X2, X0, K1, X5                               // 62f17c0956ea
	VORPS X8, X0, K1, X5                               // 62d17c0956e8
	VORPS X9, X0, K1, X5                               // 62d17c0956e9
	VORPS 7(SI)(DI*8), X0, K1, X5                      // 62f17c0956acfe07000000
	VORPS -15(R14), X0, K1, X5                         // 62d17c0956aef1ffffff
	VORPS X2, X9, K1, X5                               // 62f1340956ea
	VORPS X8, X9, K1, X5                               // 62d1340956e8
	VORPS X9, X9, K1, X5                               // 62d1340956e9
	VORPS 7(SI)(DI*8), X9, K1, X5                      // 62f1340956acfe07000000
	VORPS -15(R14), X9, K1, X5                         // 62d1340956aef1ffffff
	VORPS X2, X13, K1, X5                              // 62f1140956ea
	VORPS X8, X13, K1, X5                              // 62d1140956e8
	VORPS X9, X13, K1, X5                              // 62d1140956e9
	VORPS 7(SI)(DI*8), X13, K1, X5                     // 62f1140956acfe07000000
	VORPS -15(R14), X13, K1, X5                        // 62d1140956aef1ffffff
	VORPS X2, X0, K1, X25                              // 62617c0956ca
	VORPS X8, X0, K1, X25                              // 62417c0956c8
	VORPS X9, X0, K1, X25                              // 62417c0956c9
	VORPS 7(SI)(DI*8), X0, K1, X25                     // 62617c09568cfe07000000
	VORPS -15(R14), X0, K1, X25                        // 62417c09568ef1ffffff
	VORPS X2, X9, K1, X25                              // 6261340956ca
	VORPS X8, X9, K1, X25                              // 6241340956c8
	VORPS X9, X9, K1, X25                              // 6241340956c9
	VORPS 7(SI)(DI*8), X9, K1, X25                     // 62613409568cfe07000000
	VORPS -15(R14), X9, K1, X25                        // 62413409568ef1ffffff
	VORPS X2, X13, K1, X25                             // 6261140956ca
	VORPS X8, X13, K1, X25                             // 6241140956c8
	VORPS X9, X13, K1, X25                             // 6241140956c9
	VORPS 7(SI)(DI*8), X13, K1, X25                    // 62611409568cfe07000000
	VORPS -15(R14), X13, K1, X25                       // 62411409568ef1ffffff
	VORPS Y11, Y7, K7, Y9                              // 6251442f56cb
	VORPS Y26, Y7, K7, Y9                              // 6211442f56ca
	VORPS Y12, Y7, K7, Y9                              // 6251442f56cc
	VORPS -7(CX)(DX*1), Y7, K7, Y9                     // 6271442f568c11f9ffffff
	VORPS -15(R14)(R15*4), Y7, K7, Y9                  // 6211442f568cbef1ffffff
	VORPS Y11, Y6, K7, Y9                              // 62514c2f56cb
	VORPS Y26, Y6, K7, Y9                              // 62114c2f56ca
	VORPS Y12, Y6, K7, Y9                              // 62514c2f56cc
	VORPS -7(CX)(DX*1), Y6, K7, Y9                     // 62714c2f568c11f9ffffff
	VORPS -15(R14)(R15*4), Y6, K7, Y9                  // 62114c2f568cbef1ffffff
	VORPS Y11, Y26, K7, Y9                             // 62512c2756cb
	VORPS Y26, Y26, K7, Y9                             // 62112c2756ca
	VORPS Y12, Y26, K7, Y9                             // 62512c2756cc
	VORPS -7(CX)(DX*1), Y26, K7, Y9                    // 62712c27568c11f9ffffff
	VORPS -15(R14)(R15*4), Y26, K7, Y9                 // 62112c27568cbef1ffffff
	VORPS Y11, Y7, K7, Y6                              // 62d1442f56f3
	VORPS Y26, Y7, K7, Y6                              // 6291442f56f2
	VORPS Y12, Y7, K7, Y6                              // 62d1442f56f4
	VORPS -7(CX)(DX*1), Y7, K7, Y6                     // 62f1442f56b411f9ffffff
	VORPS -15(R14)(R15*4), Y7, K7, Y6                  // 6291442f56b4bef1ffffff
	VORPS Y11, Y6, K7, Y6                              // 62d14c2f56f3
	VORPS Y26, Y6, K7, Y6                              // 62914c2f56f2
	VORPS Y12, Y6, K7, Y6                              // 62d14c2f56f4
	VORPS -7(CX)(DX*1), Y6, K7, Y6                     // 62f14c2f56b411f9ffffff
	VORPS -15(R14)(R15*4), Y6, K7, Y6                  // 62914c2f56b4bef1ffffff
	VORPS Y11, Y26, K7, Y6                             // 62d12c2756f3
	VORPS Y26, Y26, K7, Y6                             // 62912c2756f2
	VORPS Y12, Y26, K7, Y6                             // 62d12c2756f4
	VORPS -7(CX)(DX*1), Y26, K7, Y6                    // 62f12c2756b411f9ffffff
	VORPS -15(R14)(R15*4), Y26, K7, Y6                 // 62912c2756b4bef1ffffff
	VORPS Y11, Y7, K7, Y3                              // 62d1442f56db
	VORPS Y26, Y7, K7, Y3                              // 6291442f56da
	VORPS Y12, Y7, K7, Y3                              // 62d1442f56dc
	VORPS -7(CX)(DX*1), Y7, K7, Y3                     // 62f1442f569c11f9ffffff
	VORPS -15(R14)(R15*4), Y7, K7, Y3                  // 6291442f569cbef1ffffff
	VORPS Y11, Y6, K7, Y3                              // 62d14c2f56db
	VORPS Y26, Y6, K7, Y3                              // 62914c2f56da
	VORPS Y12, Y6, K7, Y3                              // 62d14c2f56dc
	VORPS -7(CX)(DX*1), Y6, K7, Y3                     // 62f14c2f569c11f9ffffff
	VORPS -15(R14)(R15*4), Y6, K7, Y3                  // 62914c2f569cbef1ffffff
	VORPS Y11, Y26, K7, Y3                             // 62d12c2756db
	VORPS Y26, Y26, K7, Y3                             // 62912c2756da
	VORPS Y12, Y26, K7, Y3                             // 62d12c2756dc
	VORPS -7(CX)(DX*1), Y26, K7, Y3                    // 62f12c27569c11f9ffffff
	VORPS -15(R14)(R15*4), Y26, K7, Y3                 // 62912c27569cbef1ffffff
	VORPS Z17, Z20, K2, Z9                             // 62315c4256c9
	VORPS Z0, Z20, K2, Z9                              // 62715c4256c8
	VORPS 99(R15)(R15*8), Z20, K2, Z9                  // 62115c42568cff63000000
	VORPS 7(AX)(CX*8), Z20, K2, Z9                     // 62715c42568cc807000000
	VORPS Z17, Z0, K2, Z9                              // 62317c4a56c9
	VORPS Z0, Z0, K2, Z9                               // 62717c4a56c8
	VORPS 99(R15)(R15*8), Z0, K2, Z9                   // 62117c4a568cff63000000
	VORPS 7(AX)(CX*8), Z0, K2, Z9                      // 62717c4a568cc807000000
	VORPS Z17, Z20, K2, Z28                            // 62215c4256e1
	VORPS Z0, Z20, K2, Z28                             // 62615c4256e0
	VORPS 99(R15)(R15*8), Z20, K2, Z28                 // 62015c4256a4ff63000000
	VORPS 7(AX)(CX*8), Z20, K2, Z28                    // 62615c4256a4c807000000
	VORPS Z17, Z0, K2, Z28                             // 62217c4a56e1
	VORPS Z0, Z0, K2, Z28                              // 62617c4a56e0
	VORPS 99(R15)(R15*8), Z0, K2, Z28                  // 62017c4a56a4ff63000000
	VORPS 7(AX)(CX*8), Z0, K2, Z28                     // 62617c4a56a4c807000000
	VPEXTRD $64, X22, CX                               // 62e37d0816f140
	VPEXTRD $64, X30, CX                               // 62637d0816f140
	VPEXTRD $64, X22, SP                               // 62e37d0816f440
	VPEXTRD $64, X30, SP                               // 62637d0816f440
	VPEXTRD $64, X22, 99(R15)(R15*2)                   // 62837d0816b47f6300000040
	VPEXTRD $64, X30, 99(R15)(R15*2)                   // 62037d0816b47f6300000040
	VPEXTRD $64, X22, -7(DI)                           // 62e37d0816b7f9ffffff40
	VPEXTRD $64, X30, -7(DI)                           // 62637d0816b7f9ffffff40
	VPEXTRQ $27, X30, R9                               // 6243fd0816f11b
	VPEXTRQ $27, X30, R13                              // 6243fd0816f51b
	VPEXTRQ $27, X30, -15(R14)(R15*1)                  // 6203fd0816b43ef1ffffff1b
	VPEXTRQ $27, X30, -15(BX)                          // 6263fd0816b3f1ffffff1b
	VPINSRD $82, R9, X22, X21                          // 62c34d0022e952
	VPINSRD $82, CX, X22, X21                          // 62e34d0022e952
	VPINSRD $82, -7(CX)(DX*1), X22, X21                // 62e34d0022ac11f9ffffff52
	VPINSRD $82, -15(R14)(R15*4), X22, X21             // 62834d0022acbef1ffffff52
	VPINSRD $82, R9, X7, X21                           // 62c3450822e952
	VPINSRD $82, CX, X7, X21                           // 62e3450822e952
	VPINSRD $82, -7(CX)(DX*1), X7, X21                 // 62e3450822ac11f9ffffff52
	VPINSRD $82, -15(R14)(R15*4), X7, X21              // 6283450822acbef1ffffff52
	VPINSRD $82, R9, X19, X21                          // 62c3650022e952
	VPINSRD $82, CX, X19, X21                          // 62e3650022e952
	VPINSRD $82, -7(CX)(DX*1), X19, X21                // 62e3650022ac11f9ffffff52
	VPINSRD $82, -15(R14)(R15*4), X19, X21             // 6283650022acbef1ffffff52
	VPINSRD $82, R9, X22, X0                           // 62d34d0022c152
	VPINSRD $82, CX, X22, X0                           // 62f34d0022c152
	VPINSRD $82, -7(CX)(DX*1), X22, X0                 // 62f34d00228411f9ffffff52
	VPINSRD $82, -15(R14)(R15*4), X22, X0              // 62934d002284bef1ffffff52
	VPINSRD $82, R9, X19, X0                           // 62d3650022c152
	VPINSRD $82, CX, X19, X0                           // 62f3650022c152
	VPINSRD $82, -7(CX)(DX*1), X19, X0                 // 62f36500228411f9ffffff52
	VPINSRD $82, -15(R14)(R15*4), X19, X0              // 629365002284bef1ffffff52
	VPINSRD $82, R9, X22, X28                          // 62434d0022e152
	VPINSRD $82, CX, X22, X28                          // 62634d0022e152
	VPINSRD $82, -7(CX)(DX*1), X22, X28                // 62634d0022a411f9ffffff52
	VPINSRD $82, -15(R14)(R15*4), X22, X28             // 62034d0022a4bef1ffffff52
	VPINSRD $82, R9, X7, X28                           // 6243450822e152
	VPINSRD $82, CX, X7, X28                           // 6263450822e152
	VPINSRD $82, -7(CX)(DX*1), X7, X28                 // 6263450822a411f9ffffff52
	VPINSRD $82, -15(R14)(R15*4), X7, X28              // 6203450822a4bef1ffffff52
	VPINSRD $82, R9, X19, X28                          // 6243650022e152
	VPINSRD $82, CX, X19, X28                          // 6263650022e152
	VPINSRD $82, -7(CX)(DX*1), X19, X28                // 6263650022a411f9ffffff52
	VPINSRD $82, -15(R14)(R15*4), X19, X28             // 6203650022a4bef1ffffff52
	VPINSRQ $126, DX, X1, X16                          // 62e3f50822c27e
	VPINSRQ $126, BP, X1, X16                          // 62e3f50822c57e
	VPINSRQ $126, 7(AX)(CX*4), X1, X16                 // 62e3f508228488070000007e
	VPINSRQ $126, 7(AX)(CX*1), X1, X16                 // 62e3f508228408070000007e
	VPINSRQ $126, DX, X7, X16                          // 62e3c50822c27e
	VPINSRQ $126, BP, X7, X16                          // 62e3c50822c57e
	VPINSRQ $126, 7(AX)(CX*4), X7, X16                 // 62e3c508228488070000007e
	VPINSRQ $126, 7(AX)(CX*1), X7, X16                 // 62e3c508228408070000007e
	VPINSRQ $126, DX, X9, X16                          // 62e3b50822c27e
	VPINSRQ $126, BP, X9, X16                          // 62e3b50822c57e
	VPINSRQ $126, 7(AX)(CX*4), X9, X16                 // 62e3b508228488070000007e
	VPINSRQ $126, 7(AX)(CX*1), X9, X16                 // 62e3b508228408070000007e
	VPINSRQ $126, DX, X1, X31                          // 6263f50822fa7e
	VPINSRQ $126, BP, X1, X31                          // 6263f50822fd7e
	VPINSRQ $126, 7(AX)(CX*4), X1, X31                 // 6263f50822bc88070000007e
	VPINSRQ $126, 7(AX)(CX*1), X1, X31                 // 6263f50822bc08070000007e
	VPINSRQ $126, DX, X7, X31                          // 6263c50822fa7e
	VPINSRQ $126, BP, X7, X31                          // 6263c50822fd7e
	VPINSRQ $126, 7(AX)(CX*4), X7, X31                 // 6263c50822bc88070000007e
	VPINSRQ $126, 7(AX)(CX*1), X7, X31                 // 6263c50822bc08070000007e
	VPINSRQ $126, DX, X9, X31                          // 6263b50822fa7e
	VPINSRQ $126, BP, X9, X31                          // 6263b50822fd7e
	VPINSRQ $126, 7(AX)(CX*4), X9, X31                 // 6263b50822bc88070000007e
	VPINSRQ $126, 7(AX)(CX*1), X9, X31                 // 6263b50822bc08070000007e
	VPMOVD2M X3, K6                                    // 62f27e0839f3
	VPMOVD2M X26, K6                                   // 62927e0839f2
	VPMOVD2M X23, K6                                   // 62b27e0839f7
	VPMOVD2M X3, K7                                    // 62f27e0839fb
	VPMOVD2M X26, K7                                   // 62927e0839fa
	VPMOVD2M X23, K7                                   // 62b27e0839ff
	VPMOVD2M Y5, K6                                    // 62f27e2839f5
	VPMOVD2M Y28, K6                                   // 62927e2839f4
	VPMOVD2M Y7, K6                                    // 62f27e2839f7
	VPMOVD2M Y5, K4                                    // 62f27e2839e5
	VPMOVD2M Y28, K4                                   // 62927e2839e4
	VPMOVD2M Y7, K4                                    // 62f27e2839e7
	VPMOVD2M Z1, K4                                    // 62f27e4839e1
	VPMOVD2M Z9, K4                                    // 62d27e4839e1
	VPMOVD2M Z1, K6                                    // 62f27e4839f1
	VPMOVD2M Z9, K6                                    // 62d27e4839f1
	VPMOVM2D K6, X21                                   // 62e27e0838ee
	VPMOVM2D K5, X21                                   // 62e27e0838ed
	VPMOVM2D K6, X1                                    // 62f27e0838ce
	VPMOVM2D K5, X1                                    // 62f27e0838cd
	VPMOVM2D K6, X11                                   // 62727e0838de
	VPMOVM2D K5, X11                                   // 62727e0838dd
	VPMOVM2D K1, Y28                                   // 62627e2838e1
	VPMOVM2D K5, Y28                                   // 62627e2838e5
	VPMOVM2D K1, Y13                                   // 62727e2838e9
	VPMOVM2D K5, Y13                                   // 62727e2838ed
	VPMOVM2D K1, Y7                                    // 62f27e2838f9
	VPMOVM2D K5, Y7                                    // 62f27e2838fd
	VPMOVM2D K3, Z7                                    // 62f27e4838fb
	VPMOVM2D K1, Z7                                    // 62f27e4838f9
	VPMOVM2D K3, Z21                                   // 62e27e4838eb
	VPMOVM2D K1, Z21                                   // 62e27e4838e9
	VPMOVM2Q K5, X13                                   // 6272fe0838ed
	VPMOVM2Q K4, X13                                   // 6272fe0838ec
	VPMOVM2Q K5, X0                                    // 62f2fe0838c5
	VPMOVM2Q K4, X0                                    // 62f2fe0838c4
	VPMOVM2Q K5, X30                                   // 6262fe0838f5
	VPMOVM2Q K4, X30                                   // 6262fe0838f4
	VPMOVM2Q K7, Y2                                    // 62f2fe2838d7
	VPMOVM2Q K6, Y2                                    // 62f2fe2838d6
	VPMOVM2Q K7, Y21                                   // 62e2fe2838ef
	VPMOVM2Q K6, Y21                                   // 62e2fe2838ee
	VPMOVM2Q K7, Y12                                   // 6272fe2838e7
	VPMOVM2Q K6, Y12                                   // 6272fe2838e6
	VPMOVM2Q K4, Z16                                   // 62e2fe4838c4
	VPMOVM2Q K6, Z16                                   // 62e2fe4838c6
	VPMOVM2Q K4, Z25                                   // 6262fe4838cc
	VPMOVM2Q K6, Z25                                   // 6262fe4838ce
	VPMOVQ2M X14, K1                                   // 62d2fe0839ce
	VPMOVQ2M X19, K1                                   // 62b2fe0839cb
	VPMOVQ2M X8, K1                                    // 62d2fe0839c8
	VPMOVQ2M X14, K3                                   // 62d2fe0839de
	VPMOVQ2M X19, K3                                   // 62b2fe0839db
	VPMOVQ2M X8, K3                                    // 62d2fe0839d8
	VPMOVQ2M Y3, K6                                    // 62f2fe2839f3
	VPMOVQ2M Y2, K6                                    // 62f2fe2839f2
	VPMOVQ2M Y9, K6                                    // 62d2fe2839f1
	VPMOVQ2M Y3, K7                                    // 62f2fe2839fb
	VPMOVQ2M Y2, K7                                    // 62f2fe2839fa
	VPMOVQ2M Y9, K7                                    // 62d2fe2839f9
	VPMOVQ2M Z12, K6                                   // 62d2fe4839f4
	VPMOVQ2M Z13, K6                                   // 62d2fe4839f5
	VPMOVQ2M Z12, K4                                   // 62d2fe4839e4
	VPMOVQ2M Z13, K4                                   // 62d2fe4839e5
	VPMULLQ X13, X3, K7, X17                           // 62c2e50f40cd
	VPMULLQ X28, X3, K7, X17                           // 6282e50f40cc
	VPMULLQ X24, X3, K7, X17                           // 6282e50f40c8
	VPMULLQ 15(R8)(R14*4), X3, K7, X17                 // 6282e50f408cb00f000000
	VPMULLQ -7(CX)(DX*4), X3, K7, X17                  // 62e2e50f408c91f9ffffff
	VPMULLQ X13, X26, K7, X17                          // 62c2ad0740cd
	VPMULLQ X28, X26, K7, X17                          // 6282ad0740cc
	VPMULLQ X24, X26, K7, X17                          // 6282ad0740c8
	VPMULLQ 15(R8)(R14*4), X26, K7, X17                // 6282ad07408cb00f000000
	VPMULLQ -7(CX)(DX*4), X26, K7, X17                 // 62e2ad07408c91f9ffffff
	VPMULLQ X13, X23, K7, X17                          // 62c2c50740cd
	VPMULLQ X28, X23, K7, X17                          // 6282c50740cc
	VPMULLQ X24, X23, K7, X17                          // 6282c50740c8
	VPMULLQ 15(R8)(R14*4), X23, K7, X17                // 6282c507408cb00f000000
	VPMULLQ -7(CX)(DX*4), X23, K7, X17                 // 62e2c507408c91f9ffffff
	VPMULLQ X13, X3, K7, X15                           // 6252e50f40fd
	VPMULLQ X28, X3, K7, X15                           // 6212e50f40fc
	VPMULLQ X24, X3, K7, X15                           // 6212e50f40f8
	VPMULLQ 15(R8)(R14*4), X3, K7, X15                 // 6212e50f40bcb00f000000
	VPMULLQ -7(CX)(DX*4), X3, K7, X15                  // 6272e50f40bc91f9ffffff
	VPMULLQ X13, X26, K7, X15                          // 6252ad0740fd
	VPMULLQ X28, X26, K7, X15                          // 6212ad0740fc
	VPMULLQ X24, X26, K7, X15                          // 6212ad0740f8
	VPMULLQ 15(R8)(R14*4), X26, K7, X15                // 6212ad0740bcb00f000000
	VPMULLQ -7(CX)(DX*4), X26, K7, X15                 // 6272ad0740bc91f9ffffff
	VPMULLQ X13, X23, K7, X15                          // 6252c50740fd
	VPMULLQ X28, X23, K7, X15                          // 6212c50740fc
	VPMULLQ X24, X23, K7, X15                          // 6212c50740f8
	VPMULLQ 15(R8)(R14*4), X23, K7, X15                // 6212c50740bcb00f000000
	VPMULLQ -7(CX)(DX*4), X23, K7, X15                 // 6272c50740bc91f9ffffff
	VPMULLQ X13, X3, K7, X8                            // 6252e50f40c5
	VPMULLQ X28, X3, K7, X8                            // 6212e50f40c4
	VPMULLQ X24, X3, K7, X8                            // 6212e50f40c0
	VPMULLQ 15(R8)(R14*4), X3, K7, X8                  // 6212e50f4084b00f000000
	VPMULLQ -7(CX)(DX*4), X3, K7, X8                   // 6272e50f408491f9ffffff
	VPMULLQ X13, X26, K7, X8                           // 6252ad0740c5
	VPMULLQ X28, X26, K7, X8                           // 6212ad0740c4
	VPMULLQ X24, X26, K7, X8                           // 6212ad0740c0
	VPMULLQ 15(R8)(R14*4), X26, K7, X8                 // 6212ad074084b00f000000
	VPMULLQ -7(CX)(DX*4), X26, K7, X8                  // 6272ad07408491f9ffffff
	VPMULLQ X13, X23, K7, X8                           // 6252c50740c5
	VPMULLQ X28, X23, K7, X8                           // 6212c50740c4
	VPMULLQ X24, X23, K7, X8                           // 6212c50740c0
	VPMULLQ 15(R8)(R14*4), X23, K7, X8                 // 6212c5074084b00f000000
	VPMULLQ -7(CX)(DX*4), X23, K7, X8                  // 6272c507408491f9ffffff
	VPMULLQ Y28, Y31, K2, Y17                          // 6282852240cc
	VPMULLQ Y13, Y31, K2, Y17                          // 62c2852240cd
	VPMULLQ Y7, Y31, K2, Y17                           // 62e2852240cf
	VPMULLQ 15(DX)(BX*1), Y31, K2, Y17                 // 62e28522408c1a0f000000
	VPMULLQ -7(CX)(DX*2), Y31, K2, Y17                 // 62e28522408c51f9ffffff
	VPMULLQ Y28, Y8, K2, Y17                           // 6282bd2a40cc
	VPMULLQ Y13, Y8, K2, Y17                           // 62c2bd2a40cd
	VPMULLQ Y7, Y8, K2, Y17                            // 62e2bd2a40cf
	VPMULLQ 15(DX)(BX*1), Y8, K2, Y17                  // 62e2bd2a408c1a0f000000
	VPMULLQ -7(CX)(DX*2), Y8, K2, Y17                  // 62e2bd2a408c51f9ffffff
	VPMULLQ Y28, Y1, K2, Y17                           // 6282f52a40cc
	VPMULLQ Y13, Y1, K2, Y17                           // 62c2f52a40cd
	VPMULLQ Y7, Y1, K2, Y17                            // 62e2f52a40cf
	VPMULLQ 15(DX)(BX*1), Y1, K2, Y17                  // 62e2f52a408c1a0f000000
	VPMULLQ -7(CX)(DX*2), Y1, K2, Y17                  // 62e2f52a408c51f9ffffff
	VPMULLQ Y28, Y31, K2, Y7                           // 6292852240fc
	VPMULLQ Y13, Y31, K2, Y7                           // 62d2852240fd
	VPMULLQ Y7, Y31, K2, Y7                            // 62f2852240ff
	VPMULLQ 15(DX)(BX*1), Y31, K2, Y7                  // 62f2852240bc1a0f000000
	VPMULLQ -7(CX)(DX*2), Y31, K2, Y7                  // 62f2852240bc51f9ffffff
	VPMULLQ Y28, Y8, K2, Y7                            // 6292bd2a40fc
	VPMULLQ Y13, Y8, K2, Y7                            // 62d2bd2a40fd
	VPMULLQ Y7, Y8, K2, Y7                             // 62f2bd2a40ff
	VPMULLQ 15(DX)(BX*1), Y8, K2, Y7                   // 62f2bd2a40bc1a0f000000
	VPMULLQ -7(CX)(DX*2), Y8, K2, Y7                   // 62f2bd2a40bc51f9ffffff
	VPMULLQ Y28, Y1, K2, Y7                            // 6292f52a40fc
	VPMULLQ Y13, Y1, K2, Y7                            // 62d2f52a40fd
	VPMULLQ Y7, Y1, K2, Y7                             // 62f2f52a40ff
	VPMULLQ 15(DX)(BX*1), Y1, K2, Y7                   // 62f2f52a40bc1a0f000000
	VPMULLQ -7(CX)(DX*2), Y1, K2, Y7                   // 62f2f52a40bc51f9ffffff
	VPMULLQ Y28, Y31, K2, Y9                           // 6212852240cc
	VPMULLQ Y13, Y31, K2, Y9                           // 6252852240cd
	VPMULLQ Y7, Y31, K2, Y9                            // 6272852240cf
	VPMULLQ 15(DX)(BX*1), Y31, K2, Y9                  // 62728522408c1a0f000000
	VPMULLQ -7(CX)(DX*2), Y31, K2, Y9                  // 62728522408c51f9ffffff
	VPMULLQ Y28, Y8, K2, Y9                            // 6212bd2a40cc
	VPMULLQ Y13, Y8, K2, Y9                            // 6252bd2a40cd
	VPMULLQ Y7, Y8, K2, Y9                             // 6272bd2a40cf
	VPMULLQ 15(DX)(BX*1), Y8, K2, Y9                   // 6272bd2a408c1a0f000000
	VPMULLQ -7(CX)(DX*2), Y8, K2, Y9                   // 6272bd2a408c51f9ffffff
	VPMULLQ Y28, Y1, K2, Y9                            // 6212f52a40cc
	VPMULLQ Y13, Y1, K2, Y9                            // 6252f52a40cd
	VPMULLQ Y7, Y1, K2, Y9                             // 6272f52a40cf
	VPMULLQ 15(DX)(BX*1), Y1, K2, Y9                   // 6272f52a408c1a0f000000
	VPMULLQ -7(CX)(DX*2), Y1, K2, Y9                   // 6272f52a408c51f9ffffff
	VPMULLQ Z3, Z20, K4, Z0                            // 62f2dd4440c3
	VPMULLQ Z30, Z20, K4, Z0                           // 6292dd4440c6
	VPMULLQ 15(R8)(R14*8), Z20, K4, Z0                 // 6292dd444084f00f000000
	VPMULLQ -15(R14)(R15*2), Z20, K4, Z0               // 6292dd4440847ef1ffffff
	VPMULLQ Z3, Z28, K4, Z0                            // 62f29d4440c3
	VPMULLQ Z30, Z28, K4, Z0                           // 62929d4440c6
	VPMULLQ 15(R8)(R14*8), Z28, K4, Z0                 // 62929d444084f00f000000
	VPMULLQ -15(R14)(R15*2), Z28, K4, Z0               // 62929d4440847ef1ffffff
	VPMULLQ Z3, Z20, K4, Z6                            // 62f2dd4440f3
	VPMULLQ Z30, Z20, K4, Z6                           // 6292dd4440f6
	VPMULLQ 15(R8)(R14*8), Z20, K4, Z6                 // 6292dd4440b4f00f000000
	VPMULLQ -15(R14)(R15*2), Z20, K4, Z6               // 6292dd4440b47ef1ffffff
	VPMULLQ Z3, Z28, K4, Z6                            // 62f29d4440f3
	VPMULLQ Z30, Z28, K4, Z6                           // 62929d4440f6
	VPMULLQ 15(R8)(R14*8), Z28, K4, Z6                 // 62929d4440b4f00f000000
	VPMULLQ -15(R14)(R15*2), Z28, K4, Z6               // 62929d4440b47ef1ffffff
	VRANGEPD $11, X24, X23, K2, X12                    // 6213c50250e00b
	VRANGEPD $11, X14, X23, K2, X12                    // 6253c50250e60b
	VRANGEPD $11, X0, X23, K2, X12                     // 6273c50250e00b
	VRANGEPD $11, 17(SP)(BP*8), X23, K2, X12           // 6273c50250a4ec110000000b
	VRANGEPD $11, 17(SP)(BP*4), X23, K2, X12           // 6273c50250a4ac110000000b
	VRANGEPD $11, X24, X11, K2, X12                    // 6213a50a50e00b
	VRANGEPD $11, X14, X11, K2, X12                    // 6253a50a50e60b
	VRANGEPD $11, X0, X11, K2, X12                     // 6273a50a50e00b
	VRANGEPD $11, 17(SP)(BP*8), X11, K2, X12           // 6273a50a50a4ec110000000b
	VRANGEPD $11, 17(SP)(BP*4), X11, K2, X12           // 6273a50a50a4ac110000000b
	VRANGEPD $11, X24, X31, K2, X12                    // 6213850250e00b
	VRANGEPD $11, X14, X31, K2, X12                    // 6253850250e60b
	VRANGEPD $11, X0, X31, K2, X12                     // 6273850250e00b
	VRANGEPD $11, 17(SP)(BP*8), X31, K2, X12           // 6273850250a4ec110000000b
	VRANGEPD $11, 17(SP)(BP*4), X31, K2, X12           // 6273850250a4ac110000000b
	VRANGEPD $11, X24, X23, K2, X16                    // 6283c50250c00b
	VRANGEPD $11, X14, X23, K2, X16                    // 62c3c50250c60b
	VRANGEPD $11, X0, X23, K2, X16                     // 62e3c50250c00b
	VRANGEPD $11, 17(SP)(BP*8), X23, K2, X16           // 62e3c5025084ec110000000b
	VRANGEPD $11, 17(SP)(BP*4), X23, K2, X16           // 62e3c5025084ac110000000b
	VRANGEPD $11, X24, X11, K2, X16                    // 6283a50a50c00b
	VRANGEPD $11, X14, X11, K2, X16                    // 62c3a50a50c60b
	VRANGEPD $11, X0, X11, K2, X16                     // 62e3a50a50c00b
	VRANGEPD $11, 17(SP)(BP*8), X11, K2, X16           // 62e3a50a5084ec110000000b
	VRANGEPD $11, 17(SP)(BP*4), X11, K2, X16           // 62e3a50a5084ac110000000b
	VRANGEPD $11, X24, X31, K2, X16                    // 6283850250c00b
	VRANGEPD $11, X14, X31, K2, X16                    // 62c3850250c60b
	VRANGEPD $11, X0, X31, K2, X16                     // 62e3850250c00b
	VRANGEPD $11, 17(SP)(BP*8), X31, K2, X16           // 62e385025084ec110000000b
	VRANGEPD $11, 17(SP)(BP*4), X31, K2, X16           // 62e385025084ac110000000b
	VRANGEPD $11, X24, X23, K2, X23                    // 6283c50250f80b
	VRANGEPD $11, X14, X23, K2, X23                    // 62c3c50250fe0b
	VRANGEPD $11, X0, X23, K2, X23                     // 62e3c50250f80b
	VRANGEPD $11, 17(SP)(BP*8), X23, K2, X23           // 62e3c50250bcec110000000b
	VRANGEPD $11, 17(SP)(BP*4), X23, K2, X23           // 62e3c50250bcac110000000b
	VRANGEPD $11, X24, X11, K2, X23                    // 6283a50a50f80b
	VRANGEPD $11, X14, X11, K2, X23                    // 62c3a50a50fe0b
	VRANGEPD $11, X0, X11, K2, X23                     // 62e3a50a50f80b
	VRANGEPD $11, 17(SP)(BP*8), X11, K2, X23           // 62e3a50a50bcec110000000b
	VRANGEPD $11, 17(SP)(BP*4), X11, K2, X23           // 62e3a50a50bcac110000000b
	VRANGEPD $11, X24, X31, K2, X23                    // 6283850250f80b
	VRANGEPD $11, X14, X31, K2, X23                    // 62c3850250fe0b
	VRANGEPD $11, X0, X31, K2, X23                     // 62e3850250f80b
	VRANGEPD $11, 17(SP)(BP*8), X31, K2, X23           // 62e3850250bcec110000000b
	VRANGEPD $11, 17(SP)(BP*4), X31, K2, X23           // 62e3850250bcac110000000b
	VRANGEPD $12, Y3, Y18, K1, Y15                     // 6273ed2150fb0c
	VRANGEPD $12, Y19, Y18, K1, Y15                    // 6233ed2150fb0c
	VRANGEPD $12, Y23, Y18, K1, Y15                    // 6233ed2150ff0c
	VRANGEPD $12, (R8), Y18, K1, Y15                   // 6253ed2150380c
	VRANGEPD $12, 15(DX)(BX*2), Y18, K1, Y15           // 6273ed2150bc5a0f0000000c
	VRANGEPD $12, Y3, Y24, K1, Y15                     // 6273bd2150fb0c
	VRANGEPD $12, Y19, Y24, K1, Y15                    // 6233bd2150fb0c
	VRANGEPD $12, Y23, Y24, K1, Y15                    // 6233bd2150ff0c
	VRANGEPD $12, (R8), Y24, K1, Y15                   // 6253bd2150380c
	VRANGEPD $12, 15(DX)(BX*2), Y24, K1, Y15           // 6273bd2150bc5a0f0000000c
	VRANGEPD $12, Y3, Y9, K1, Y15                      // 6273b52950fb0c
	VRANGEPD $12, Y19, Y9, K1, Y15                     // 6233b52950fb0c
	VRANGEPD $12, Y23, Y9, K1, Y15                     // 6233b52950ff0c
	VRANGEPD $12, (R8), Y9, K1, Y15                    // 6253b52950380c
	VRANGEPD $12, 15(DX)(BX*2), Y9, K1, Y15            // 6273b52950bc5a0f0000000c
	VRANGEPD $12, Y3, Y18, K1, Y22                     // 62e3ed2150f30c
	VRANGEPD $12, Y19, Y18, K1, Y22                    // 62a3ed2150f30c
	VRANGEPD $12, Y23, Y18, K1, Y22                    // 62a3ed2150f70c
	VRANGEPD $12, (R8), Y18, K1, Y22                   // 62c3ed2150300c
	VRANGEPD $12, 15(DX)(BX*2), Y18, K1, Y22           // 62e3ed2150b45a0f0000000c
	VRANGEPD $12, Y3, Y24, K1, Y22                     // 62e3bd2150f30c
	VRANGEPD $12, Y19, Y24, K1, Y22                    // 62a3bd2150f30c
	VRANGEPD $12, Y23, Y24, K1, Y22                    // 62a3bd2150f70c
	VRANGEPD $12, (R8), Y24, K1, Y22                   // 62c3bd2150300c
	VRANGEPD $12, 15(DX)(BX*2), Y24, K1, Y22           // 62e3bd2150b45a0f0000000c
	VRANGEPD $12, Y3, Y9, K1, Y22                      // 62e3b52950f30c
	VRANGEPD $12, Y19, Y9, K1, Y22                     // 62a3b52950f30c
	VRANGEPD $12, Y23, Y9, K1, Y22                     // 62a3b52950f70c
	VRANGEPD $12, (R8), Y9, K1, Y22                    // 62c3b52950300c
	VRANGEPD $12, 15(DX)(BX*2), Y9, K1, Y22            // 62e3b52950b45a0f0000000c
	VRANGEPD $12, Y3, Y18, K1, Y20                     // 62e3ed2150e30c
	VRANGEPD $12, Y19, Y18, K1, Y20                    // 62a3ed2150e30c
	VRANGEPD $12, Y23, Y18, K1, Y20                    // 62a3ed2150e70c
	VRANGEPD $12, (R8), Y18, K1, Y20                   // 62c3ed2150200c
	VRANGEPD $12, 15(DX)(BX*2), Y18, K1, Y20           // 62e3ed2150a45a0f0000000c
	VRANGEPD $12, Y3, Y24, K1, Y20                     // 62e3bd2150e30c
	VRANGEPD $12, Y19, Y24, K1, Y20                    // 62a3bd2150e30c
	VRANGEPD $12, Y23, Y24, K1, Y20                    // 62a3bd2150e70c
	VRANGEPD $12, (R8), Y24, K1, Y20                   // 62c3bd2150200c
	VRANGEPD $12, 15(DX)(BX*2), Y24, K1, Y20           // 62e3bd2150a45a0f0000000c
	VRANGEPD $12, Y3, Y9, K1, Y20                      // 62e3b52950e30c
	VRANGEPD $12, Y19, Y9, K1, Y20                     // 62a3b52950e30c
	VRANGEPD $12, Y23, Y9, K1, Y20                     // 62a3b52950e70c
	VRANGEPD $12, (R8), Y9, K1, Y20                    // 62c3b52950200c
	VRANGEPD $12, 15(DX)(BX*2), Y9, K1, Y20            // 62e3b52950a45a0f0000000c
	VRANGEPD $13, Z21, Z12, K7, Z14                    // 62339d4f50f50d
	VRANGEPD $13, Z9, Z12, K7, Z14                     // 62539d4f50f10d
	VRANGEPD $13, Z21, Z13, K7, Z14                    // 6233954f50f50d
	VRANGEPD $13, Z9, Z13, K7, Z14                     // 6253954f50f10d
	VRANGEPD $13, Z21, Z12, K7, Z13                    // 62339d4f50ed0d
	VRANGEPD $13, Z9, Z12, K7, Z13                     // 62539d4f50e90d
	VRANGEPD $13, Z21, Z13, K7, Z13                    // 6233954f50ed0d
	VRANGEPD $13, Z9, Z13, K7, Z13                     // 6253954f50e90d
	VRANGEPD $14, Z23, Z27, K1, Z2                     // 62b3a54150d70e
	VRANGEPD $14, Z9, Z27, K1, Z2                      // 62d3a54150d10e
	VRANGEPD $14, (R14), Z27, K1, Z2                   // 62d3a54150160e
	VRANGEPD $14, -7(DI)(R8*8), Z27, K1, Z2            // 62b3a5415094c7f9ffffff0e
	VRANGEPD $14, Z23, Z25, K1, Z2                     // 62b3b54150d70e
	VRANGEPD $14, Z9, Z25, K1, Z2                      // 62d3b54150d10e
	VRANGEPD $14, (R14), Z25, K1, Z2                   // 62d3b54150160e
	VRANGEPD $14, -7(DI)(R8*8), Z25, K1, Z2            // 62b3b5415094c7f9ffffff0e
	VRANGEPD $14, Z23, Z27, K1, Z7                     // 62b3a54150ff0e
	VRANGEPD $14, Z9, Z27, K1, Z7                      // 62d3a54150f90e
	VRANGEPD $14, (R14), Z27, K1, Z7                   // 62d3a541503e0e
	VRANGEPD $14, -7(DI)(R8*8), Z27, K1, Z7            // 62b3a54150bcc7f9ffffff0e
	VRANGEPD $14, Z23, Z25, K1, Z7                     // 62b3b54150ff0e
	VRANGEPD $14, Z9, Z25, K1, Z7                      // 62d3b54150f90e
	VRANGEPD $14, (R14), Z25, K1, Z7                   // 62d3b541503e0e
	VRANGEPD $14, -7(DI)(R8*8), Z25, K1, Z7            // 62b3b54150bcc7f9ffffff0e
	VRANGEPS $15, X0, X20, K1, X11                     // 62735d0150d80f
	VRANGEPS $15, X9, X20, K1, X11                     // 62535d0150d90f
	VRANGEPS $15, X13, X20, K1, X11                    // 62535d0150dd0f
	VRANGEPS $15, 7(SI)(DI*4), X20, K1, X11            // 62735d01509cbe070000000f
	VRANGEPS $15, -7(DI)(R8*2), X20, K1, X11           // 62335d01509c47f9ffffff0f
	VRANGEPS $15, X0, X5, K1, X11                      // 6273550950d80f
	VRANGEPS $15, X9, X5, K1, X11                      // 6253550950d90f
	VRANGEPS $15, X13, X5, K1, X11                     // 6253550950dd0f
	VRANGEPS $15, 7(SI)(DI*4), X5, K1, X11             // 62735509509cbe070000000f
	VRANGEPS $15, -7(DI)(R8*2), X5, K1, X11            // 62335509509c47f9ffffff0f
	VRANGEPS $15, X0, X25, K1, X11                     // 6273350150d80f
	VRANGEPS $15, X9, X25, K1, X11                     // 6253350150d90f
	VRANGEPS $15, X13, X25, K1, X11                    // 6253350150dd0f
	VRANGEPS $15, 7(SI)(DI*4), X25, K1, X11            // 62733501509cbe070000000f
	VRANGEPS $15, -7(DI)(R8*2), X25, K1, X11           // 62333501509c47f9ffffff0f
	VRANGEPS $15, X0, X20, K1, X23                     // 62e35d0150f80f
	VRANGEPS $15, X9, X20, K1, X23                     // 62c35d0150f90f
	VRANGEPS $15, X13, X20, K1, X23                    // 62c35d0150fd0f
	VRANGEPS $15, 7(SI)(DI*4), X20, K1, X23            // 62e35d0150bcbe070000000f
	VRANGEPS $15, -7(DI)(R8*2), X20, K1, X23           // 62a35d0150bc47f9ffffff0f
	VRANGEPS $15, X0, X5, K1, X23                      // 62e3550950f80f
	VRANGEPS $15, X9, X5, K1, X23                      // 62c3550950f90f
	VRANGEPS $15, X13, X5, K1, X23                     // 62c3550950fd0f
	VRANGEPS $15, 7(SI)(DI*4), X5, K1, X23             // 62e3550950bcbe070000000f
	VRANGEPS $15, -7(DI)(R8*2), X5, K1, X23            // 62a3550950bc47f9ffffff0f
	VRANGEPS $15, X0, X25, K1, X23                     // 62e3350150f80f
	VRANGEPS $15, X9, X25, K1, X23                     // 62c3350150f90f
	VRANGEPS $15, X13, X25, K1, X23                    // 62c3350150fd0f
	VRANGEPS $15, 7(SI)(DI*4), X25, K1, X23            // 62e3350150bcbe070000000f
	VRANGEPS $15, -7(DI)(R8*2), X25, K1, X23           // 62a3350150bc47f9ffffff0f
	VRANGEPS $15, X0, X20, K1, X2                      // 62f35d0150d00f
	VRANGEPS $15, X9, X20, K1, X2                      // 62d35d0150d10f
	VRANGEPS $15, X13, X20, K1, X2                     // 62d35d0150d50f
	VRANGEPS $15, 7(SI)(DI*4), X20, K1, X2             // 62f35d015094be070000000f
	VRANGEPS $15, -7(DI)(R8*2), X20, K1, X2            // 62b35d01509447f9ffffff0f
	VRANGEPS $15, X0, X5, K1, X2                       // 62f3550950d00f
	VRANGEPS $15, X9, X5, K1, X2                       // 62d3550950d10f
	VRANGEPS $15, X13, X5, K1, X2                      // 62d3550950d50f
	VRANGEPS $15, 7(SI)(DI*4), X5, K1, X2              // 62f355095094be070000000f
	VRANGEPS $15, -7(DI)(R8*2), X5, K1, X2             // 62b35509509447f9ffffff0f
	VRANGEPS $15, X0, X25, K1, X2                      // 62f3350150d00f
	VRANGEPS $15, X9, X25, K1, X2                      // 62d3350150d10f
	VRANGEPS $15, X13, X25, K1, X2                     // 62d3350150d50f
	VRANGEPS $15, 7(SI)(DI*4), X25, K1, X2             // 62f335015094be070000000f
	VRANGEPS $15, -7(DI)(R8*2), X25, K1, X2            // 62b33501509447f9ffffff0f
	VRANGEPS $0, Y21, Y5, K1, Y19                      // 62a3552950dd00
	VRANGEPS $0, Y20, Y5, K1, Y19                      // 62a3552950dc00
	VRANGEPS $0, Y6, Y5, K1, Y19                       // 62e3552950de00
	VRANGEPS $0, 17(SP)(BP*1), Y5, K1, Y19             // 62e35529509c2c1100000000
	VRANGEPS $0, -7(CX)(DX*8), Y5, K1, Y19             // 62e35529509cd1f9ffffff00
	VRANGEPS $0, Y21, Y16, K1, Y19                     // 62a37d2150dd00
	VRANGEPS $0, Y20, Y16, K1, Y19                     // 62a37d2150dc00
	VRANGEPS $0, Y6, Y16, K1, Y19                      // 62e37d2150de00
	VRANGEPS $0, 17(SP)(BP*1), Y16, K1, Y19            // 62e37d21509c2c1100000000
	VRANGEPS $0, -7(CX)(DX*8), Y16, K1, Y19            // 62e37d21509cd1f9ffffff00
	VRANGEPS $0, Y21, Y2, K1, Y19                      // 62a36d2950dd00
	VRANGEPS $0, Y20, Y2, K1, Y19                      // 62a36d2950dc00
	VRANGEPS $0, Y6, Y2, K1, Y19                       // 62e36d2950de00
	VRANGEPS $0, 17(SP)(BP*1), Y2, K1, Y19             // 62e36d29509c2c1100000000
	VRANGEPS $0, -7(CX)(DX*8), Y2, K1, Y19             // 62e36d29509cd1f9ffffff00
	VRANGEPS $0, Y21, Y5, K1, Y14                      // 6233552950f500
	VRANGEPS $0, Y20, Y5, K1, Y14                      // 6233552950f400
	VRANGEPS $0, Y6, Y5, K1, Y14                       // 6273552950f600
	VRANGEPS $0, 17(SP)(BP*1), Y5, K1, Y14             // 6273552950b42c1100000000
	VRANGEPS $0, -7(CX)(DX*8), Y5, K1, Y14             // 6273552950b4d1f9ffffff00
	VRANGEPS $0, Y21, Y16, K1, Y14                     // 62337d2150f500
	VRANGEPS $0, Y20, Y16, K1, Y14                     // 62337d2150f400
	VRANGEPS $0, Y6, Y16, K1, Y14                      // 62737d2150f600
	VRANGEPS $0, 17(SP)(BP*1), Y16, K1, Y14            // 62737d2150b42c1100000000
	VRANGEPS $0, -7(CX)(DX*8), Y16, K1, Y14            // 62737d2150b4d1f9ffffff00
	VRANGEPS $0, Y21, Y2, K1, Y14                      // 62336d2950f500
	VRANGEPS $0, Y20, Y2, K1, Y14                      // 62336d2950f400
	VRANGEPS $0, Y6, Y2, K1, Y14                       // 62736d2950f600
	VRANGEPS $0, 17(SP)(BP*1), Y2, K1, Y14             // 62736d2950b42c1100000000
	VRANGEPS $0, -7(CX)(DX*8), Y2, K1, Y14             // 62736d2950b4d1f9ffffff00
	VRANGEPS $0, Y21, Y5, K1, Y21                      // 62a3552950ed00
	VRANGEPS $0, Y20, Y5, K1, Y21                      // 62a3552950ec00
	VRANGEPS $0, Y6, Y5, K1, Y21                       // 62e3552950ee00
	VRANGEPS $0, 17(SP)(BP*1), Y5, K1, Y21             // 62e3552950ac2c1100000000
	VRANGEPS $0, -7(CX)(DX*8), Y5, K1, Y21             // 62e3552950acd1f9ffffff00
	VRANGEPS $0, Y21, Y16, K1, Y21                     // 62a37d2150ed00
	VRANGEPS $0, Y20, Y16, K1, Y21                     // 62a37d2150ec00
	VRANGEPS $0, Y6, Y16, K1, Y21                      // 62e37d2150ee00
	VRANGEPS $0, 17(SP)(BP*1), Y16, K1, Y21            // 62e37d2150ac2c1100000000
	VRANGEPS $0, -7(CX)(DX*8), Y16, K1, Y21            // 62e37d2150acd1f9ffffff00
	VRANGEPS $0, Y21, Y2, K1, Y21                      // 62a36d2950ed00
	VRANGEPS $0, Y20, Y2, K1, Y21                      // 62a36d2950ec00
	VRANGEPS $0, Y6, Y2, K1, Y21                       // 62e36d2950ee00
	VRANGEPS $0, 17(SP)(BP*1), Y2, K1, Y21             // 62e36d2950ac2c1100000000
	VRANGEPS $0, -7(CX)(DX*8), Y2, K1, Y21             // 62e36d2950acd1f9ffffff00
	VRANGEPS $1, Z14, Z3, K7, Z27                      // 6243654f50de01
	VRANGEPS $1, Z7, Z3, K7, Z27                       // 6263654f50df01
	VRANGEPS $1, Z14, Z0, K7, Z27                      // 62437d4f50de01
	VRANGEPS $1, Z7, Z0, K7, Z27                       // 62637d4f50df01
	VRANGEPS $1, Z14, Z3, K7, Z14                      // 6253654f50f601
	VRANGEPS $1, Z7, Z3, K7, Z14                       // 6273654f50f701
	VRANGEPS $1, Z14, Z0, K7, Z14                      // 62537d4f50f601
	VRANGEPS $1, Z7, Z0, K7, Z14                       // 62737d4f50f701
	VRANGEPS $2, Z1, Z22, K2, Z8                       // 62734d4250c102
	VRANGEPS $2, Z16, Z22, K2, Z8                      // 62334d4250c002
	VRANGEPS $2, 99(R15)(R15*4), Z22, K2, Z8           // 62134d425084bf6300000002
	VRANGEPS $2, 15(DX), Z22, K2, Z8                   // 62734d4250820f00000002
	VRANGEPS $2, Z1, Z25, K2, Z8                       // 6273354250c102
	VRANGEPS $2, Z16, Z25, K2, Z8                      // 6233354250c002
	VRANGEPS $2, 99(R15)(R15*4), Z25, K2, Z8           // 621335425084bf6300000002
	VRANGEPS $2, 15(DX), Z25, K2, Z8                   // 6273354250820f00000002
	VRANGEPS $2, Z1, Z22, K2, Z24                      // 62634d4250c102
	VRANGEPS $2, Z16, Z22, K2, Z24                     // 62234d4250c002
	VRANGEPS $2, 99(R15)(R15*4), Z22, K2, Z24          // 62034d425084bf6300000002
	VRANGEPS $2, 15(DX), Z22, K2, Z24                  // 62634d4250820f00000002
	VRANGEPS $2, Z1, Z25, K2, Z24                      // 6263354250c102
	VRANGEPS $2, Z16, Z25, K2, Z24                     // 6223354250c002
	VRANGEPS $2, 99(R15)(R15*4), Z25, K2, Z24          // 620335425084bf6300000002
	VRANGEPS $2, 15(DX), Z25, K2, Z24                  // 6263354250820f00000002
	VRANGESD $3, X22, X2, K4, X2                       // 62b3ed0c51d603
	VRANGESD $3, X5, X2, K4, X2                        // 62f3ed0c51d503
	VRANGESD $3, X14, X2, K4, X2                       // 62d3ed0c51d603
	VRANGESD $3, X22, X31, K4, X2                      // 62b3850451d603
	VRANGESD $3, X5, X31, K4, X2                       // 62f3850451d503
	VRANGESD $3, X14, X31, K4, X2                      // 62d3850451d603
	VRANGESD $3, X22, X11, K4, X2                      // 62b3a50c51d603
	VRANGESD $3, X5, X11, K4, X2                       // 62f3a50c51d503
	VRANGESD $3, X14, X11, K4, X2                      // 62d3a50c51d603
	VRANGESD $3, X22, X2, K4, X8                       // 6233ed0c51c603
	VRANGESD $3, X5, X2, K4, X8                        // 6273ed0c51c503
	VRANGESD $3, X14, X2, K4, X8                       // 6253ed0c51c603
	VRANGESD $3, X22, X31, K4, X8                      // 6233850451c603
	VRANGESD $3, X5, X31, K4, X8                       // 6273850451c503
	VRANGESD $3, X14, X31, K4, X8                      // 6253850451c603
	VRANGESD $3, X22, X11, K4, X8                      // 6233a50c51c603
	VRANGESD $3, X5, X11, K4, X8                       // 6273a50c51c503
	VRANGESD $3, X14, X11, K4, X8                      // 6253a50c51c603
	VRANGESD $3, X22, X2, K4, X9                       // 6233ed0c51ce03
	VRANGESD $3, X5, X2, K4, X9                        // 6273ed0c51cd03
	VRANGESD $3, X14, X2, K4, X9                       // 6253ed0c51ce03
	VRANGESD $3, X22, X31, K4, X9                      // 6233850451ce03
	VRANGESD $3, X5, X31, K4, X9                       // 6273850451cd03
	VRANGESD $3, X14, X31, K4, X9                      // 6253850451ce03
	VRANGESD $3, X22, X11, K4, X9                      // 6233a50c51ce03
	VRANGESD $3, X5, X11, K4, X9                       // 6273a50c51cd03
	VRANGESD $3, X14, X11, K4, X9                      // 6253a50c51ce03
	VRANGESD $4, X18, X15, K1, X0                      // 62b3850951c204 or 62b3852951c204 or 62b3854951c204
	VRANGESD $4, X8, X15, K1, X0                       // 62d3850951c004 or 62d3852951c004 or 62d3854951c004
	VRANGESD $4, X27, X15, K1, X0                      // 6293850951c304 or 6293852951c304 or 6293854951c304
	VRANGESD $4, 7(AX)(CX*4), X15, K1, X0              // 62f385095184880700000004 or 62f385295184880700000004 or 62f385495184880700000004
	VRANGESD $4, 7(AX)(CX*1), X15, K1, X0              // 62f385095184080700000004 or 62f385295184080700000004 or 62f385495184080700000004
	VRANGESD $4, X18, X11, K1, X0                      // 62b3a50951c204 or 62b3a52951c204 or 62b3a54951c204
	VRANGESD $4, X8, X11, K1, X0                       // 62d3a50951c004 or 62d3a52951c004 or 62d3a54951c004
	VRANGESD $4, X27, X11, K1, X0                      // 6293a50951c304 or 6293a52951c304 or 6293a54951c304
	VRANGESD $4, 7(AX)(CX*4), X11, K1, X0              // 62f3a5095184880700000004 or 62f3a5295184880700000004 or 62f3a5495184880700000004
	VRANGESD $4, 7(AX)(CX*1), X11, K1, X0              // 62f3a5095184080700000004 or 62f3a5295184080700000004 or 62f3a5495184080700000004
	VRANGESD $4, X18, X0, K1, X0                       // 62b3fd0951c204 or 62b3fd2951c204 or 62b3fd4951c204
	VRANGESD $4, X8, X0, K1, X0                        // 62d3fd0951c004 or 62d3fd2951c004 or 62d3fd4951c004
	VRANGESD $4, X27, X0, K1, X0                       // 6293fd0951c304 or 6293fd2951c304 or 6293fd4951c304
	VRANGESD $4, 7(AX)(CX*4), X0, K1, X0               // 62f3fd095184880700000004 or 62f3fd295184880700000004 or 62f3fd495184880700000004
	VRANGESD $4, 7(AX)(CX*1), X0, K1, X0               // 62f3fd095184080700000004 or 62f3fd295184080700000004 or 62f3fd495184080700000004
	VRANGESD $4, X18, X15, K1, X17                     // 62a3850951ca04 or 62a3852951ca04 or 62a3854951ca04
	VRANGESD $4, X8, X15, K1, X17                      // 62c3850951c804 or 62c3852951c804 or 62c3854951c804
	VRANGESD $4, X27, X15, K1, X17                     // 6283850951cb04 or 6283852951cb04 or 6283854951cb04
	VRANGESD $4, 7(AX)(CX*4), X15, K1, X17             // 62e38509518c880700000004 or 62e38529518c880700000004 or 62e38549518c880700000004
	VRANGESD $4, 7(AX)(CX*1), X15, K1, X17             // 62e38509518c080700000004 or 62e38529518c080700000004 or 62e38549518c080700000004
	VRANGESD $4, X18, X11, K1, X17                     // 62a3a50951ca04 or 62a3a52951ca04 or 62a3a54951ca04
	VRANGESD $4, X8, X11, K1, X17                      // 62c3a50951c804 or 62c3a52951c804 or 62c3a54951c804
	VRANGESD $4, X27, X11, K1, X17                     // 6283a50951cb04 or 6283a52951cb04 or 6283a54951cb04
	VRANGESD $4, 7(AX)(CX*4), X11, K1, X17             // 62e3a509518c880700000004 or 62e3a529518c880700000004 or 62e3a549518c880700000004
	VRANGESD $4, 7(AX)(CX*1), X11, K1, X17             // 62e3a509518c080700000004 or 62e3a529518c080700000004 or 62e3a549518c080700000004
	VRANGESD $4, X18, X0, K1, X17                      // 62a3fd0951ca04 or 62a3fd2951ca04 or 62a3fd4951ca04
	VRANGESD $4, X8, X0, K1, X17                       // 62c3fd0951c804 or 62c3fd2951c804 or 62c3fd4951c804
	VRANGESD $4, X27, X0, K1, X17                      // 6283fd0951cb04 or 6283fd2951cb04 or 6283fd4951cb04
	VRANGESD $4, 7(AX)(CX*4), X0, K1, X17              // 62e3fd09518c880700000004 or 62e3fd29518c880700000004 or 62e3fd49518c880700000004
	VRANGESD $4, 7(AX)(CX*1), X0, K1, X17              // 62e3fd09518c080700000004 or 62e3fd29518c080700000004 or 62e3fd49518c080700000004
	VRANGESD $4, X18, X15, K1, X7                      // 62b3850951fa04 or 62b3852951fa04 or 62b3854951fa04
	VRANGESD $4, X8, X15, K1, X7                       // 62d3850951f804 or 62d3852951f804 or 62d3854951f804
	VRANGESD $4, X27, X15, K1, X7                      // 6293850951fb04 or 6293852951fb04 or 6293854951fb04
	VRANGESD $4, 7(AX)(CX*4), X15, K1, X7              // 62f3850951bc880700000004 or 62f3852951bc880700000004 or 62f3854951bc880700000004
	VRANGESD $4, 7(AX)(CX*1), X15, K1, X7              // 62f3850951bc080700000004 or 62f3852951bc080700000004 or 62f3854951bc080700000004
	VRANGESD $4, X18, X11, K1, X7                      // 62b3a50951fa04 or 62b3a52951fa04 or 62b3a54951fa04
	VRANGESD $4, X8, X11, K1, X7                       // 62d3a50951f804 or 62d3a52951f804 or 62d3a54951f804
	VRANGESD $4, X27, X11, K1, X7                      // 6293a50951fb04 or 6293a52951fb04 or 6293a54951fb04
	VRANGESD $4, 7(AX)(CX*4), X11, K1, X7              // 62f3a50951bc880700000004 or 62f3a52951bc880700000004 or 62f3a54951bc880700000004
	VRANGESD $4, 7(AX)(CX*1), X11, K1, X7              // 62f3a50951bc080700000004 or 62f3a52951bc080700000004 or 62f3a54951bc080700000004
	VRANGESD $4, X18, X0, K1, X7                       // 62b3fd0951fa04 or 62b3fd2951fa04 or 62b3fd4951fa04
	VRANGESD $4, X8, X0, K1, X7                        // 62d3fd0951f804 or 62d3fd2951f804 or 62d3fd4951f804
	VRANGESD $4, X27, X0, K1, X7                       // 6293fd0951fb04 or 6293fd2951fb04 or 6293fd4951fb04
	VRANGESD $4, 7(AX)(CX*4), X0, K1, X7               // 62f3fd0951bc880700000004 or 62f3fd2951bc880700000004 or 62f3fd4951bc880700000004
	VRANGESD $4, 7(AX)(CX*1), X0, K1, X7               // 62f3fd0951bc080700000004 or 62f3fd2951bc080700000004 or 62f3fd4951bc080700000004
	VRANGESS $5, X7, X15, K3, X25                      // 6263050b51cf05
	VRANGESS $5, X13, X15, K3, X25                     // 6243050b51cd05
	VRANGESS $5, X8, X15, K3, X25                      // 6243050b51c805
	VRANGESS $5, X7, X28, K3, X25                      // 62631d0351cf05
	VRANGESS $5, X13, X28, K3, X25                     // 62431d0351cd05
	VRANGESS $5, X8, X28, K3, X25                      // 62431d0351c805
	VRANGESS $5, X7, X15, K3, X3                       // 62f3050b51df05
	VRANGESS $5, X13, X15, K3, X3                      // 62d3050b51dd05
	VRANGESS $5, X8, X15, K3, X3                       // 62d3050b51d805
	VRANGESS $5, X7, X28, K3, X3                       // 62f31d0351df05
	VRANGESS $5, X13, X28, K3, X3                      // 62d31d0351dd05
	VRANGESS $5, X8, X28, K3, X3                       // 62d31d0351d805
	VRANGESS $5, X7, X15, K3, X18                      // 62e3050b51d705
	VRANGESS $5, X13, X15, K3, X18                     // 62c3050b51d505
	VRANGESS $5, X8, X15, K3, X18                      // 62c3050b51d005
	VRANGESS $5, X7, X28, K3, X18                      // 62e31d0351d705
	VRANGESS $5, X13, X28, K3, X18                     // 62c31d0351d505
	VRANGESS $5, X8, X28, K3, X18                      // 62c31d0351d005
	VRANGESS $6, X6, X22, K4, X24                      // 62634d0451c606 or 62634d2451c606 or 62634d4451c606
	VRANGESS $6, X7, X22, K4, X24                      // 62634d0451c706 or 62634d2451c706 or 62634d4451c706
	VRANGESS $6, X8, X22, K4, X24                      // 62434d0451c006 or 62434d2451c006 or 62434d4451c006
	VRANGESS $6, 7(SI)(DI*1), X22, K4, X24             // 62634d0451843e0700000006 or 62634d2451843e0700000006 or 62634d4451843e0700000006
	VRANGESS $6, 15(DX)(BX*8), X22, K4, X24            // 62634d045184da0f00000006 or 62634d245184da0f00000006 or 62634d445184da0f00000006
	VRANGESS $6, X6, X1, K4, X24                       // 6263750c51c606 or 6263752c51c606 or 6263754c51c606
	VRANGESS $6, X7, X1, K4, X24                       // 6263750c51c706 or 6263752c51c706 or 6263754c51c706
	VRANGESS $6, X8, X1, K4, X24                       // 6243750c51c006 or 6243752c51c006 or 6243754c51c006
	VRANGESS $6, 7(SI)(DI*1), X1, K4, X24              // 6263750c51843e0700000006 or 6263752c51843e0700000006 or 6263754c51843e0700000006
	VRANGESS $6, 15(DX)(BX*8), X1, K4, X24             // 6263750c5184da0f00000006 or 6263752c5184da0f00000006 or 6263754c5184da0f00000006
	VRANGESS $6, X6, X11, K4, X24                      // 6263250c51c606 or 6263252c51c606 or 6263254c51c606
	VRANGESS $6, X7, X11, K4, X24                      // 6263250c51c706 or 6263252c51c706 or 6263254c51c706
	VRANGESS $6, X8, X11, K4, X24                      // 6243250c51c006 or 6243252c51c006 or 6243254c51c006
	VRANGESS $6, 7(SI)(DI*1), X11, K4, X24             // 6263250c51843e0700000006 or 6263252c51843e0700000006 or 6263254c51843e0700000006
	VRANGESS $6, 15(DX)(BX*8), X11, K4, X24            // 6263250c5184da0f00000006 or 6263252c5184da0f00000006 or 6263254c5184da0f00000006
	VRANGESS $6, X6, X22, K4, X7                       // 62f34d0451fe06 or 62f34d2451fe06 or 62f34d4451fe06
	VRANGESS $6, X7, X22, K4, X7                       // 62f34d0451ff06 or 62f34d2451ff06 or 62f34d4451ff06
	VRANGESS $6, X8, X22, K4, X7                       // 62d34d0451f806 or 62d34d2451f806 or 62d34d4451f806
	VRANGESS $6, 7(SI)(DI*1), X22, K4, X7              // 62f34d0451bc3e0700000006 or 62f34d2451bc3e0700000006 or 62f34d4451bc3e0700000006
	VRANGESS $6, 15(DX)(BX*8), X22, K4, X7             // 62f34d0451bcda0f00000006 or 62f34d2451bcda0f00000006 or 62f34d4451bcda0f00000006
	VRANGESS $6, X6, X1, K4, X7                        // 62f3750c51fe06 or 62f3752c51fe06 or 62f3754c51fe06
	VRANGESS $6, X7, X1, K4, X7                        // 62f3750c51ff06 or 62f3752c51ff06 or 62f3754c51ff06
	VRANGESS $6, X8, X1, K4, X7                        // 62d3750c51f806 or 62d3752c51f806 or 62d3754c51f806
	VRANGESS $6, 7(SI)(DI*1), X1, K4, X7               // 62f3750c51bc3e0700000006 or 62f3752c51bc3e0700000006 or 62f3754c51bc3e0700000006
	VRANGESS $6, 15(DX)(BX*8), X1, K4, X7              // 62f3750c51bcda0f00000006 or 62f3752c51bcda0f00000006 or 62f3754c51bcda0f00000006
	VRANGESS $6, X6, X11, K4, X7                       // 62f3250c51fe06 or 62f3252c51fe06 or 62f3254c51fe06
	VRANGESS $6, X7, X11, K4, X7                       // 62f3250c51ff06 or 62f3252c51ff06 or 62f3254c51ff06
	VRANGESS $6, X8, X11, K4, X7                       // 62d3250c51f806 or 62d3252c51f806 or 62d3254c51f806
	VRANGESS $6, 7(SI)(DI*1), X11, K4, X7              // 62f3250c51bc3e0700000006 or 62f3252c51bc3e0700000006 or 62f3254c51bc3e0700000006
	VRANGESS $6, 15(DX)(BX*8), X11, K4, X7             // 62f3250c51bcda0f00000006 or 62f3252c51bcda0f00000006 or 62f3254c51bcda0f00000006
	VRANGESS $6, X6, X22, K4, X0                       // 62f34d0451c606 or 62f34d2451c606 or 62f34d4451c606
	VRANGESS $6, X7, X22, K4, X0                       // 62f34d0451c706 or 62f34d2451c706 or 62f34d4451c706
	VRANGESS $6, X8, X22, K4, X0                       // 62d34d0451c006 or 62d34d2451c006 or 62d34d4451c006
	VRANGESS $6, 7(SI)(DI*1), X22, K4, X0              // 62f34d0451843e0700000006 or 62f34d2451843e0700000006 or 62f34d4451843e0700000006
	VRANGESS $6, 15(DX)(BX*8), X22, K4, X0             // 62f34d045184da0f00000006 or 62f34d245184da0f00000006 or 62f34d445184da0f00000006
	VRANGESS $6, X6, X1, K4, X0                        // 62f3750c51c606 or 62f3752c51c606 or 62f3754c51c606
	VRANGESS $6, X7, X1, K4, X0                        // 62f3750c51c706 or 62f3752c51c706 or 62f3754c51c706
	VRANGESS $6, X8, X1, K4, X0                        // 62d3750c51c006 or 62d3752c51c006 or 62d3754c51c006
	VRANGESS $6, 7(SI)(DI*1), X1, K4, X0               // 62f3750c51843e0700000006 or 62f3752c51843e0700000006 or 62f3754c51843e0700000006
	VRANGESS $6, 15(DX)(BX*8), X1, K4, X0              // 62f3750c5184da0f00000006 or 62f3752c5184da0f00000006 or 62f3754c5184da0f00000006
	VRANGESS $6, X6, X11, K4, X0                       // 62f3250c51c606 or 62f3252c51c606 or 62f3254c51c606
	VRANGESS $6, X7, X11, K4, X0                       // 62f3250c51c706 or 62f3252c51c706 or 62f3254c51c706
	VRANGESS $6, X8, X11, K4, X0                       // 62d3250c51c006 or 62d3252c51c006 or 62d3254c51c006
	VRANGESS $6, 7(SI)(DI*1), X11, K4, X0              // 62f3250c51843e0700000006 or 62f3252c51843e0700000006 or 62f3254c51843e0700000006
	VRANGESS $6, 15(DX)(BX*8), X11, K4, X0             // 62f3250c5184da0f00000006 or 62f3252c5184da0f00000006 or 62f3254c5184da0f00000006
	VREDUCEPD $126, X8, K3, X31                        // 6243fd0b56f87e
	VREDUCEPD $126, X1, K3, X31                        // 6263fd0b56f97e
	VREDUCEPD $126, X0, K3, X31                        // 6263fd0b56f87e
	VREDUCEPD $126, 99(R15)(R15*1), K3, X31            // 6203fd0b56bc3f630000007e
	VREDUCEPD $126, (DX), K3, X31                      // 6263fd0b563a7e
	VREDUCEPD $126, X8, K3, X16                        // 62c3fd0b56c07e
	VREDUCEPD $126, X1, K3, X16                        // 62e3fd0b56c17e
	VREDUCEPD $126, X0, K3, X16                        // 62e3fd0b56c07e
	VREDUCEPD $126, 99(R15)(R15*1), K3, X16            // 6283fd0b56843f630000007e
	VREDUCEPD $126, (DX), K3, X16                      // 62e3fd0b56027e
	VREDUCEPD $126, X8, K3, X7                         // 62d3fd0b56f87e
	VREDUCEPD $126, X1, K3, X7                         // 62f3fd0b56f97e
	VREDUCEPD $126, X0, K3, X7                         // 62f3fd0b56f87e
	VREDUCEPD $126, 99(R15)(R15*1), K3, X7             // 6293fd0b56bc3f630000007e
	VREDUCEPD $126, (DX), K3, X7                       // 62f3fd0b563a7e
	VREDUCEPD $94, Y0, K3, Y5                          // 62f3fd2b56e85e
	VREDUCEPD $94, Y22, K3, Y5                         // 62b3fd2b56ee5e
	VREDUCEPD $94, Y13, K3, Y5                         // 62d3fd2b56ed5e
	VREDUCEPD $94, (R14), K3, Y5                       // 62d3fd2b562e5e
	VREDUCEPD $94, -7(DI)(R8*8), K3, Y5                // 62b3fd2b56acc7f9ffffff5e
	VREDUCEPD $94, Y0, K3, Y28                         // 6263fd2b56e05e
	VREDUCEPD $94, Y22, K3, Y28                        // 6223fd2b56e65e
	VREDUCEPD $94, Y13, K3, Y28                        // 6243fd2b56e55e
	VREDUCEPD $94, (R14), K3, Y28                      // 6243fd2b56265e
	VREDUCEPD $94, -7(DI)(R8*8), K3, Y28               // 6223fd2b56a4c7f9ffffff5e
	VREDUCEPD $94, Y0, K3, Y7                          // 62f3fd2b56f85e
	VREDUCEPD $94, Y22, K3, Y7                         // 62b3fd2b56fe5e
	VREDUCEPD $94, Y13, K3, Y7                         // 62d3fd2b56fd5e
	VREDUCEPD $94, (R14), K3, Y7                       // 62d3fd2b563e5e
	VREDUCEPD $94, -7(DI)(R8*8), K3, Y7                // 62b3fd2b56bcc7f9ffffff5e
	VREDUCEPD $121, Z3, K2, Z26                        // 6263fd4a56d379
	VREDUCEPD $121, Z0, K2, Z26                        // 6263fd4a56d079
	VREDUCEPD $121, Z3, K2, Z3                         // 62f3fd4a56db79
	VREDUCEPD $121, Z0, K2, Z3                         // 62f3fd4a56d879
	VREDUCEPD $13, Z11, K1, Z21                        // 62c3fd4956eb0d
	VREDUCEPD $13, Z25, K1, Z21                        // 6283fd4956e90d
	VREDUCEPD $13, -17(BP), K1, Z21                    // 62e3fd4956adefffffff0d
	VREDUCEPD $13, -15(R14)(R15*8), K1, Z21            // 6283fd4956acfef1ffffff0d
	VREDUCEPD $13, Z11, K1, Z13                        // 6253fd4956eb0d
	VREDUCEPD $13, Z25, K1, Z13                        // 6213fd4956e90d
	VREDUCEPD $13, -17(BP), K1, Z13                    // 6273fd4956adefffffff0d
	VREDUCEPD $13, -15(R14)(R15*8), K1, Z13            // 6213fd4956acfef1ffffff0d
	VREDUCEPS $65, X21, K2, X15                        // 62337d0a56fd41
	VREDUCEPS $65, X0, K2, X15                         // 62737d0a56f841
	VREDUCEPS $65, X28, K2, X15                        // 62137d0a56fc41
	VREDUCEPS $65, -17(BP)(SI*8), K2, X15              // 62737d0a56bcf5efffffff41
	VREDUCEPS $65, (R15), K2, X15                      // 62537d0a563f41
	VREDUCEPS $65, X21, K2, X0                         // 62b37d0a56c541
	VREDUCEPS $65, X0, K2, X0                          // 62f37d0a56c041
	VREDUCEPS $65, X28, K2, X0                         // 62937d0a56c441
	VREDUCEPS $65, -17(BP)(SI*8), K2, X0               // 62f37d0a5684f5efffffff41
	VREDUCEPS $65, (R15), K2, X0                       // 62d37d0a560741
	VREDUCEPS $65, X21, K2, X16                        // 62a37d0a56c541
	VREDUCEPS $65, X0, K2, X16                         // 62e37d0a56c041
	VREDUCEPS $65, X28, K2, X16                        // 62837d0a56c441
	VREDUCEPS $65, -17(BP)(SI*8), K2, X16              // 62e37d0a5684f5efffffff41
	VREDUCEPS $65, (R15), K2, X16                      // 62c37d0a560741
	VREDUCEPS $67, Y17, K1, Y12                        // 62337d2956e143
	VREDUCEPS $67, Y7, K1, Y12                         // 62737d2956e743
	VREDUCEPS $67, Y9, K1, Y12                         // 62537d2956e143
	VREDUCEPS $67, 99(R15)(R15*4), K1, Y12             // 62137d2956a4bf6300000043
	VREDUCEPS $67, 15(DX), K1, Y12                     // 62737d2956a20f00000043
	VREDUCEPS $67, Y17, K1, Y1                         // 62b37d2956c943
	VREDUCEPS $67, Y7, K1, Y1                          // 62f37d2956cf43
	VREDUCEPS $67, Y9, K1, Y1                          // 62d37d2956c943
	VREDUCEPS $67, 99(R15)(R15*4), K1, Y1              // 62937d29568cbf6300000043
	VREDUCEPS $67, 15(DX), K1, Y1                      // 62f37d29568a0f00000043
	VREDUCEPS $67, Y17, K1, Y14                        // 62337d2956f143
	VREDUCEPS $67, Y7, K1, Y14                         // 62737d2956f743
	VREDUCEPS $67, Y9, K1, Y14                         // 62537d2956f143
	VREDUCEPS $67, 99(R15)(R15*4), K1, Y14             // 62137d2956b4bf6300000043
	VREDUCEPS $67, 15(DX), K1, Y14                     // 62737d2956b20f00000043
	VREDUCEPS $127, Z27, K7, Z3                        // 62937d4f56db7f
	VREDUCEPS $127, Z15, K7, Z3                        // 62d37d4f56df7f
	VREDUCEPS $127, Z27, K7, Z12                       // 62137d4f56e37f
	VREDUCEPS $127, Z15, K7, Z12                       // 62537d4f56e77f
	VREDUCEPS $0, Z23, K1, Z23                         // 62a37d4956ff00
	VREDUCEPS $0, Z6, K1, Z23                          // 62e37d4956fe00
	VREDUCEPS $0, 17(SP)(BP*2), K1, Z23                // 62e37d4956bc6c1100000000
	VREDUCEPS $0, -7(DI)(R8*4), K1, Z23                // 62a37d4956bc87f9ffffff00
	VREDUCEPS $0, Z23, K1, Z5                          // 62b37d4956ef00
	VREDUCEPS $0, Z6, K1, Z5                           // 62f37d4956ee00
	VREDUCEPS $0, 17(SP)(BP*2), K1, Z5                 // 62f37d4956ac6c1100000000
	VREDUCEPS $0, -7(DI)(R8*4), K1, Z5                 // 62b37d4956ac87f9ffffff00
	VREDUCESD $97, X1, X7, K1, X22                     // 62e3c50957f161
	VREDUCESD $97, X7, X7, K1, X22                     // 62e3c50957f761
	VREDUCESD $97, X9, X7, K1, X22                     // 62c3c50957f161
	VREDUCESD $97, X1, X16, K1, X22                    // 62e3fd0157f161
	VREDUCESD $97, X7, X16, K1, X22                    // 62e3fd0157f761
	VREDUCESD $97, X9, X16, K1, X22                    // 62c3fd0157f161
	VREDUCESD $97, X1, X31, K1, X22                    // 62e3850157f161
	VREDUCESD $97, X7, X31, K1, X22                    // 62e3850157f761
	VREDUCESD $97, X9, X31, K1, X22                    // 62c3850157f161
	VREDUCESD $97, X1, X7, K1, X7                      // 62f3c50957f961
	VREDUCESD $97, X7, X7, K1, X7                      // 62f3c50957ff61
	VREDUCESD $97, X9, X7, K1, X7                      // 62d3c50957f961
	VREDUCESD $97, X1, X16, K1, X7                     // 62f3fd0157f961
	VREDUCESD $97, X7, X16, K1, X7                     // 62f3fd0157ff61
	VREDUCESD $97, X9, X16, K1, X7                     // 62d3fd0157f961
	VREDUCESD $97, X1, X31, K1, X7                     // 62f3850157f961
	VREDUCESD $97, X7, X31, K1, X7                     // 62f3850157ff61
	VREDUCESD $97, X9, X31, K1, X7                     // 62d3850157f961
	VREDUCESD $97, X1, X7, K1, X19                     // 62e3c50957d961
	VREDUCESD $97, X7, X7, K1, X19                     // 62e3c50957df61
	VREDUCESD $97, X9, X7, K1, X19                     // 62c3c50957d961
	VREDUCESD $97, X1, X16, K1, X19                    // 62e3fd0157d961
	VREDUCESD $97, X7, X16, K1, X19                    // 62e3fd0157df61
	VREDUCESD $97, X9, X16, K1, X19                    // 62c3fd0157d961
	VREDUCESD $97, X1, X31, K1, X19                    // 62e3850157d961
	VREDUCESD $97, X7, X31, K1, X19                    // 62e3850157df61
	VREDUCESD $97, X9, X31, K1, X19                    // 62c3850157d961
	VREDUCESD $81, X17, X12, K1, X15                   // 62339d0957f951 or 62339d2957f951 or 62339d4957f951
	VREDUCESD $81, X15, X12, K1, X15                   // 62539d0957ff51 or 62539d2957ff51 or 62539d4957ff51
	VREDUCESD $81, X8, X12, K1, X15                    // 62539d0957f851 or 62539d2957f851 or 62539d4957f851
	VREDUCESD $81, 7(SI)(DI*4), X12, K1, X15           // 62739d0957bcbe0700000051 or 62739d2957bcbe0700000051 or 62739d4957bcbe0700000051
	VREDUCESD $81, -7(DI)(R8*2), X12, K1, X15          // 62339d0957bc47f9ffffff51 or 62339d2957bc47f9ffffff51 or 62339d4957bc47f9ffffff51
	VREDUCESD $81, X17, X14, K1, X15                   // 62338d0957f951 or 62338d2957f951 or 62338d4957f951
	VREDUCESD $81, X15, X14, K1, X15                   // 62538d0957ff51 or 62538d2957ff51 or 62538d4957ff51
	VREDUCESD $81, X8, X14, K1, X15                    // 62538d0957f851 or 62538d2957f851 or 62538d4957f851
	VREDUCESD $81, 7(SI)(DI*4), X14, K1, X15           // 62738d0957bcbe0700000051 or 62738d2957bcbe0700000051 or 62738d4957bcbe0700000051
	VREDUCESD $81, -7(DI)(R8*2), X14, K1, X15          // 62338d0957bc47f9ffffff51 or 62338d2957bc47f9ffffff51 or 62338d4957bc47f9ffffff51
	VREDUCESD $81, X17, X5, K1, X15                    // 6233d50957f951 or 6233d52957f951 or 6233d54957f951
	VREDUCESD $81, X15, X5, K1, X15                    // 6253d50957ff51 or 6253d52957ff51 or 6253d54957ff51
	VREDUCESD $81, X8, X5, K1, X15                     // 6253d50957f851 or 6253d52957f851 or 6253d54957f851
	VREDUCESD $81, 7(SI)(DI*4), X5, K1, X15            // 6273d50957bcbe0700000051 or 6273d52957bcbe0700000051 or 6273d54957bcbe0700000051
	VREDUCESD $81, -7(DI)(R8*2), X5, K1, X15           // 6233d50957bc47f9ffffff51 or 6233d52957bc47f9ffffff51 or 6233d54957bc47f9ffffff51
	VREDUCESD $81, X17, X12, K1, X12                   // 62339d0957e151 or 62339d2957e151 or 62339d4957e151
	VREDUCESD $81, X15, X12, K1, X12                   // 62539d0957e751 or 62539d2957e751 or 62539d4957e751
	VREDUCESD $81, X8, X12, K1, X12                    // 62539d0957e051 or 62539d2957e051 or 62539d4957e051
	VREDUCESD $81, 7(SI)(DI*4), X12, K1, X12           // 62739d0957a4be0700000051 or 62739d2957a4be0700000051 or 62739d4957a4be0700000051
	VREDUCESD $81, -7(DI)(R8*2), X12, K1, X12          // 62339d0957a447f9ffffff51 or 62339d2957a447f9ffffff51 or 62339d4957a447f9ffffff51
	VREDUCESD $81, X17, X14, K1, X12                   // 62338d0957e151 or 62338d2957e151 or 62338d4957e151
	VREDUCESD $81, X15, X14, K1, X12                   // 62538d0957e751 or 62538d2957e751 or 62538d4957e751
	VREDUCESD $81, X8, X14, K1, X12                    // 62538d0957e051 or 62538d2957e051 or 62538d4957e051
	VREDUCESD $81, 7(SI)(DI*4), X14, K1, X12           // 62738d0957a4be0700000051 or 62738d2957a4be0700000051 or 62738d4957a4be0700000051
	VREDUCESD $81, -7(DI)(R8*2), X14, K1, X12          // 62338d0957a447f9ffffff51 or 62338d2957a447f9ffffff51 or 62338d4957a447f9ffffff51
	VREDUCESD $81, X17, X5, K1, X12                    // 6233d50957e151 or 6233d52957e151 or 6233d54957e151
	VREDUCESD $81, X15, X5, K1, X12                    // 6253d50957e751 or 6253d52957e751 or 6253d54957e751
	VREDUCESD $81, X8, X5, K1, X12                     // 6253d50957e051 or 6253d52957e051 or 6253d54957e051
	VREDUCESD $81, 7(SI)(DI*4), X5, K1, X12            // 6273d50957a4be0700000051 or 6273d52957a4be0700000051 or 6273d54957a4be0700000051
	VREDUCESD $81, -7(DI)(R8*2), X5, K1, X12           // 6233d50957a447f9ffffff51 or 6233d52957a447f9ffffff51 or 6233d54957a447f9ffffff51
	VREDUCESD $81, X17, X12, K1, X0                    // 62b39d0957c151 or 62b39d2957c151 or 62b39d4957c151
	VREDUCESD $81, X15, X12, K1, X0                    // 62d39d0957c751 or 62d39d2957c751 or 62d39d4957c751
	VREDUCESD $81, X8, X12, K1, X0                     // 62d39d0957c051 or 62d39d2957c051 or 62d39d4957c051
	VREDUCESD $81, 7(SI)(DI*4), X12, K1, X0            // 62f39d095784be0700000051 or 62f39d295784be0700000051 or 62f39d495784be0700000051
	VREDUCESD $81, -7(DI)(R8*2), X12, K1, X0           // 62b39d09578447f9ffffff51 or 62b39d29578447f9ffffff51 or 62b39d49578447f9ffffff51
	VREDUCESD $81, X17, X14, K1, X0                    // 62b38d0957c151 or 62b38d2957c151 or 62b38d4957c151
	VREDUCESD $81, X15, X14, K1, X0                    // 62d38d0957c751 or 62d38d2957c751 or 62d38d4957c751
	VREDUCESD $81, X8, X14, K1, X0                     // 62d38d0957c051 or 62d38d2957c051 or 62d38d4957c051
	VREDUCESD $81, 7(SI)(DI*4), X14, K1, X0            // 62f38d095784be0700000051 or 62f38d295784be0700000051 or 62f38d495784be0700000051
	VREDUCESD $81, -7(DI)(R8*2), X14, K1, X0           // 62b38d09578447f9ffffff51 or 62b38d29578447f9ffffff51 or 62b38d49578447f9ffffff51
	VREDUCESD $81, X17, X5, K1, X0                     // 62b3d50957c151 or 62b3d52957c151 or 62b3d54957c151
	VREDUCESD $81, X15, X5, K1, X0                     // 62d3d50957c751 or 62d3d52957c751 or 62d3d54957c751
	VREDUCESD $81, X8, X5, K1, X0                      // 62d3d50957c051 or 62d3d52957c051 or 62d3d54957c051
	VREDUCESD $81, 7(SI)(DI*4), X5, K1, X0             // 62f3d5095784be0700000051 or 62f3d5295784be0700000051 or 62f3d5495784be0700000051
	VREDUCESD $81, -7(DI)(R8*2), X5, K1, X0            // 62b3d509578447f9ffffff51 or 62b3d529578447f9ffffff51 or 62b3d549578447f9ffffff51
	VREDUCESS $42, X9, X13, K7, X3                     // 62d3150f57d92a
	VREDUCESS $42, X15, X13, K7, X3                    // 62d3150f57df2a
	VREDUCESS $42, X26, X13, K7, X3                    // 6293150f57da2a
	VREDUCESS $42, X9, X28, K7, X3                     // 62d31d0757d92a
	VREDUCESS $42, X15, X28, K7, X3                    // 62d31d0757df2a
	VREDUCESS $42, X26, X28, K7, X3                    // 62931d0757da2a
	VREDUCESS $42, X9, X24, K7, X3                     // 62d33d0757d92a
	VREDUCESS $42, X15, X24, K7, X3                    // 62d33d0757df2a
	VREDUCESS $42, X26, X24, K7, X3                    // 62933d0757da2a
	VREDUCESS $42, X9, X13, K7, X26                    // 6243150f57d12a
	VREDUCESS $42, X15, X13, K7, X26                   // 6243150f57d72a
	VREDUCESS $42, X26, X13, K7, X26                   // 6203150f57d22a
	VREDUCESS $42, X9, X28, K7, X26                    // 62431d0757d12a
	VREDUCESS $42, X15, X28, K7, X26                   // 62431d0757d72a
	VREDUCESS $42, X26, X28, K7, X26                   // 62031d0757d22a
	VREDUCESS $42, X9, X24, K7, X26                    // 62433d0757d12a
	VREDUCESS $42, X15, X24, K7, X26                   // 62433d0757d72a
	VREDUCESS $42, X26, X24, K7, X26                   // 62033d0757d22a
	VREDUCESS $42, X9, X13, K7, X23                    // 62c3150f57f92a
	VREDUCESS $42, X15, X13, K7, X23                   // 62c3150f57ff2a
	VREDUCESS $42, X26, X13, K7, X23                   // 6283150f57fa2a
	VREDUCESS $42, X9, X28, K7, X23                    // 62c31d0757f92a
	VREDUCESS $42, X15, X28, K7, X23                   // 62c31d0757ff2a
	VREDUCESS $42, X26, X28, K7, X23                   // 62831d0757fa2a
	VREDUCESS $42, X9, X24, K7, X23                    // 62c33d0757f92a
	VREDUCESS $42, X15, X24, K7, X23                   // 62c33d0757ff2a
	VREDUCESS $42, X26, X24, K7, X23                   // 62833d0757fa2a
	VREDUCESS $79, X7, X11, K2, X18                    // 62e3250a57d74f or 62e3252a57d74f or 62e3254a57d74f
	VREDUCESS $79, X0, X11, K2, X18                    // 62e3250a57d04f or 62e3252a57d04f or 62e3254a57d04f
	VREDUCESS $79, 99(R15)(R15*8), X11, K2, X18        // 6283250a5794ff630000004f or 6283252a5794ff630000004f or 6283254a5794ff630000004f
	VREDUCESS $79, 7(AX)(CX*8), X11, K2, X18           // 62e3250a5794c8070000004f or 62e3252a5794c8070000004f or 62e3254a5794c8070000004f
	VREDUCESS $79, X7, X31, K2, X18                    // 62e3050257d74f or 62e3052257d74f or 62e3054257d74f
	VREDUCESS $79, X0, X31, K2, X18                    // 62e3050257d04f or 62e3052257d04f or 62e3054257d04f
	VREDUCESS $79, 99(R15)(R15*8), X31, K2, X18        // 628305025794ff630000004f or 628305225794ff630000004f or 628305425794ff630000004f
	VREDUCESS $79, 7(AX)(CX*8), X31, K2, X18           // 62e305025794c8070000004f or 62e305225794c8070000004f or 62e305425794c8070000004f
	VREDUCESS $79, X7, X3, K2, X18                     // 62e3650a57d74f or 62e3652a57d74f or 62e3654a57d74f
	VREDUCESS $79, X0, X3, K2, X18                     // 62e3650a57d04f or 62e3652a57d04f or 62e3654a57d04f
	VREDUCESS $79, 99(R15)(R15*8), X3, K2, X18         // 6283650a5794ff630000004f or 6283652a5794ff630000004f or 6283654a5794ff630000004f
	VREDUCESS $79, 7(AX)(CX*8), X3, K2, X18            // 62e3650a5794c8070000004f or 62e3652a5794c8070000004f or 62e3654a5794c8070000004f
	VREDUCESS $79, X7, X11, K2, X21                    // 62e3250a57ef4f or 62e3252a57ef4f or 62e3254a57ef4f
	VREDUCESS $79, X0, X11, K2, X21                    // 62e3250a57e84f or 62e3252a57e84f or 62e3254a57e84f
	VREDUCESS $79, 99(R15)(R15*8), X11, K2, X21        // 6283250a57acff630000004f or 6283252a57acff630000004f or 6283254a57acff630000004f
	VREDUCESS $79, 7(AX)(CX*8), X11, K2, X21           // 62e3250a57acc8070000004f or 62e3252a57acc8070000004f or 62e3254a57acc8070000004f
	VREDUCESS $79, X7, X31, K2, X21                    // 62e3050257ef4f or 62e3052257ef4f or 62e3054257ef4f
	VREDUCESS $79, X0, X31, K2, X21                    // 62e3050257e84f or 62e3052257e84f or 62e3054257e84f
	VREDUCESS $79, 99(R15)(R15*8), X31, K2, X21        // 6283050257acff630000004f or 6283052257acff630000004f or 6283054257acff630000004f
	VREDUCESS $79, 7(AX)(CX*8), X31, K2, X21           // 62e3050257acc8070000004f or 62e3052257acc8070000004f or 62e3054257acc8070000004f
	VREDUCESS $79, X7, X3, K2, X21                     // 62e3650a57ef4f or 62e3652a57ef4f or 62e3654a57ef4f
	VREDUCESS $79, X0, X3, K2, X21                     // 62e3650a57e84f or 62e3652a57e84f or 62e3654a57e84f
	VREDUCESS $79, 99(R15)(R15*8), X3, K2, X21         // 6283650a57acff630000004f or 6283652a57acff630000004f or 6283654a57acff630000004f
	VREDUCESS $79, 7(AX)(CX*8), X3, K2, X21            // 62e3650a57acc8070000004f or 62e3652a57acc8070000004f or 62e3654a57acc8070000004f
	VREDUCESS $79, X7, X11, K2, X1                     // 62f3250a57cf4f or 62f3252a57cf4f or 62f3254a57cf4f
	VREDUCESS $79, X0, X11, K2, X1                     // 62f3250a57c84f or 62f3252a57c84f or 62f3254a57c84f
	VREDUCESS $79, 99(R15)(R15*8), X11, K2, X1         // 6293250a578cff630000004f or 6293252a578cff630000004f or 6293254a578cff630000004f
	VREDUCESS $79, 7(AX)(CX*8), X11, K2, X1            // 62f3250a578cc8070000004f or 62f3252a578cc8070000004f or 62f3254a578cc8070000004f
	VREDUCESS $79, X7, X31, K2, X1                     // 62f3050257cf4f or 62f3052257cf4f or 62f3054257cf4f
	VREDUCESS $79, X0, X31, K2, X1                     // 62f3050257c84f or 62f3052257c84f or 62f3054257c84f
	VREDUCESS $79, 99(R15)(R15*8), X31, K2, X1         // 62930502578cff630000004f or 62930522578cff630000004f or 62930542578cff630000004f
	VREDUCESS $79, 7(AX)(CX*8), X31, K2, X1            // 62f30502578cc8070000004f or 62f30522578cc8070000004f or 62f30542578cc8070000004f
	VREDUCESS $79, X7, X3, K2, X1                      // 62f3650a57cf4f or 62f3652a57cf4f or 62f3654a57cf4f
	VREDUCESS $79, X0, X3, K2, X1                      // 62f3650a57c84f or 62f3652a57c84f or 62f3654a57c84f
	VREDUCESS $79, 99(R15)(R15*8), X3, K2, X1          // 6293650a578cff630000004f or 6293652a578cff630000004f or 6293654a578cff630000004f
	VREDUCESS $79, 7(AX)(CX*8), X3, K2, X1             // 62f3650a578cc8070000004f or 62f3652a578cc8070000004f or 62f3654a578cc8070000004f
	VXORPD X13, X3, K5, X17                            // 62c1e50d57cd
	VXORPD X28, X3, K5, X17                            // 6281e50d57cc
	VXORPD X24, X3, K5, X17                            // 6281e50d57c8
	VXORPD -7(CX)(DX*1), X3, K5, X17                   // 62e1e50d578c11f9ffffff
	VXORPD -15(R14)(R15*4), X3, K5, X17                // 6281e50d578cbef1ffffff
	VXORPD X13, X26, K5, X17                           // 62c1ad0557cd
	VXORPD X28, X26, K5, X17                           // 6281ad0557cc
	VXORPD X24, X26, K5, X17                           // 6281ad0557c8
	VXORPD -7(CX)(DX*1), X26, K5, X17                  // 62e1ad05578c11f9ffffff
	VXORPD -15(R14)(R15*4), X26, K5, X17               // 6281ad05578cbef1ffffff
	VXORPD X13, X23, K5, X17                           // 62c1c50557cd
	VXORPD X28, X23, K5, X17                           // 6281c50557cc
	VXORPD X24, X23, K5, X17                           // 6281c50557c8
	VXORPD -7(CX)(DX*1), X23, K5, X17                  // 62e1c505578c11f9ffffff
	VXORPD -15(R14)(R15*4), X23, K5, X17               // 6281c505578cbef1ffffff
	VXORPD X13, X3, K5, X15                            // 6251e50d57fd
	VXORPD X28, X3, K5, X15                            // 6211e50d57fc
	VXORPD X24, X3, K5, X15                            // 6211e50d57f8
	VXORPD -7(CX)(DX*1), X3, K5, X15                   // 6271e50d57bc11f9ffffff
	VXORPD -15(R14)(R15*4), X3, K5, X15                // 6211e50d57bcbef1ffffff
	VXORPD X13, X26, K5, X15                           // 6251ad0557fd
	VXORPD X28, X26, K5, X15                           // 6211ad0557fc
	VXORPD X24, X26, K5, X15                           // 6211ad0557f8
	VXORPD -7(CX)(DX*1), X26, K5, X15                  // 6271ad0557bc11f9ffffff
	VXORPD -15(R14)(R15*4), X26, K5, X15               // 6211ad0557bcbef1ffffff
	VXORPD X13, X23, K5, X15                           // 6251c50557fd
	VXORPD X28, X23, K5, X15                           // 6211c50557fc
	VXORPD X24, X23, K5, X15                           // 6211c50557f8
	VXORPD -7(CX)(DX*1), X23, K5, X15                  // 6271c50557bc11f9ffffff
	VXORPD -15(R14)(R15*4), X23, K5, X15               // 6211c50557bcbef1ffffff
	VXORPD X13, X3, K5, X8                             // 6251e50d57c5
	VXORPD X28, X3, K5, X8                             // 6211e50d57c4
	VXORPD X24, X3, K5, X8                             // 6211e50d57c0
	VXORPD -7(CX)(DX*1), X3, K5, X8                    // 6271e50d578411f9ffffff
	VXORPD -15(R14)(R15*4), X3, K5, X8                 // 6211e50d5784bef1ffffff
	VXORPD X13, X26, K5, X8                            // 6251ad0557c5
	VXORPD X28, X26, K5, X8                            // 6211ad0557c4
	VXORPD X24, X26, K5, X8                            // 6211ad0557c0
	VXORPD -7(CX)(DX*1), X26, K5, X8                   // 6271ad05578411f9ffffff
	VXORPD -15(R14)(R15*4), X26, K5, X8                // 6211ad055784bef1ffffff
	VXORPD X13, X23, K5, X8                            // 6251c50557c5
	VXORPD X28, X23, K5, X8                            // 6211c50557c4
	VXORPD X24, X23, K5, X8                            // 6211c50557c0
	VXORPD -7(CX)(DX*1), X23, K5, X8                   // 6271c505578411f9ffffff
	VXORPD -15(R14)(R15*4), X23, K5, X8                // 6211c5055784bef1ffffff
	VXORPD Y5, Y20, K3, Y0                             // 62f1dd2357c5
	VXORPD Y28, Y20, K3, Y0                            // 6291dd2357c4
	VXORPD Y7, Y20, K3, Y0                             // 62f1dd2357c7
	VXORPD -7(CX), Y20, K3, Y0                         // 62f1dd235781f9ffffff
	VXORPD 15(DX)(BX*4), Y20, K3, Y0                   // 62f1dd2357849a0f000000
	VXORPD Y5, Y12, K3, Y0                             // 62f19d2b57c5
	VXORPD Y28, Y12, K3, Y0                            // 62919d2b57c4
	VXORPD Y7, Y12, K3, Y0                             // 62f19d2b57c7
	VXORPD -7(CX), Y12, K3, Y0                         // 62f19d2b5781f9ffffff
	VXORPD 15(DX)(BX*4), Y12, K3, Y0                   // 62f19d2b57849a0f000000
	VXORPD Y5, Y3, K3, Y0                              // 62f1e52b57c5
	VXORPD Y28, Y3, K3, Y0                             // 6291e52b57c4
	VXORPD Y7, Y3, K3, Y0                              // 62f1e52b57c7
	VXORPD -7(CX), Y3, K3, Y0                          // 62f1e52b5781f9ffffff
	VXORPD 15(DX)(BX*4), Y3, K3, Y0                    // 62f1e52b57849a0f000000
	VXORPD Y5, Y20, K3, Y3                             // 62f1dd2357dd
	VXORPD Y28, Y20, K3, Y3                            // 6291dd2357dc
	VXORPD Y7, Y20, K3, Y3                             // 62f1dd2357df
	VXORPD -7(CX), Y20, K3, Y3                         // 62f1dd235799f9ffffff
	VXORPD 15(DX)(BX*4), Y20, K3, Y3                   // 62f1dd23579c9a0f000000
	VXORPD Y5, Y12, K3, Y3                             // 62f19d2b57dd
	VXORPD Y28, Y12, K3, Y3                            // 62919d2b57dc
	VXORPD Y7, Y12, K3, Y3                             // 62f19d2b57df
	VXORPD -7(CX), Y12, K3, Y3                         // 62f19d2b5799f9ffffff
	VXORPD 15(DX)(BX*4), Y12, K3, Y3                   // 62f19d2b579c9a0f000000
	VXORPD Y5, Y3, K3, Y3                              // 62f1e52b57dd
	VXORPD Y28, Y3, K3, Y3                             // 6291e52b57dc
	VXORPD Y7, Y3, K3, Y3                              // 62f1e52b57df
	VXORPD -7(CX), Y3, K3, Y3                          // 62f1e52b5799f9ffffff
	VXORPD 15(DX)(BX*4), Y3, K3, Y3                    // 62f1e52b579c9a0f000000
	VXORPD Y5, Y20, K3, Y5                             // 62f1dd2357ed
	VXORPD Y28, Y20, K3, Y5                            // 6291dd2357ec
	VXORPD Y7, Y20, K3, Y5                             // 62f1dd2357ef
	VXORPD -7(CX), Y20, K3, Y5                         // 62f1dd2357a9f9ffffff
	VXORPD 15(DX)(BX*4), Y20, K3, Y5                   // 62f1dd2357ac9a0f000000
	VXORPD Y5, Y12, K3, Y5                             // 62f19d2b57ed
	VXORPD Y28, Y12, K3, Y5                            // 62919d2b57ec
	VXORPD Y7, Y12, K3, Y5                             // 62f19d2b57ef
	VXORPD -7(CX), Y12, K3, Y5                         // 62f19d2b57a9f9ffffff
	VXORPD 15(DX)(BX*4), Y12, K3, Y5                   // 62f19d2b57ac9a0f000000
	VXORPD Y5, Y3, K3, Y5                              // 62f1e52b57ed
	VXORPD Y28, Y3, K3, Y5                             // 6291e52b57ec
	VXORPD Y7, Y3, K3, Y5                              // 62f1e52b57ef
	VXORPD -7(CX), Y3, K3, Y5                          // 62f1e52b57a9f9ffffff
	VXORPD 15(DX)(BX*4), Y3, K3, Y5                    // 62f1e52b57ac9a0f000000
	VXORPD Z13, Z28, K4, Z26                           // 62419d4457d5
	VXORPD Z21, Z28, K4, Z26                           // 62219d4457d5
	VXORPD 15(R8)(R14*1), Z28, K4, Z26                 // 62019d445794300f000000
	VXORPD 15(R8)(R14*2), Z28, K4, Z26                 // 62019d445794700f000000
	VXORPD Z13, Z6, K4, Z26                            // 6241cd4c57d5
	VXORPD Z21, Z6, K4, Z26                            // 6221cd4c57d5
	VXORPD 15(R8)(R14*1), Z6, K4, Z26                  // 6201cd4c5794300f000000
	VXORPD 15(R8)(R14*2), Z6, K4, Z26                  // 6201cd4c5794700f000000
	VXORPD Z13, Z28, K4, Z14                           // 62519d4457f5
	VXORPD Z21, Z28, K4, Z14                           // 62319d4457f5
	VXORPD 15(R8)(R14*1), Z28, K4, Z14                 // 62119d4457b4300f000000
	VXORPD 15(R8)(R14*2), Z28, K4, Z14                 // 62119d4457b4700f000000
	VXORPD Z13, Z6, K4, Z14                            // 6251cd4c57f5
	VXORPD Z21, Z6, K4, Z14                            // 6231cd4c57f5
	VXORPD 15(R8)(R14*1), Z6, K4, Z14                  // 6211cd4c57b4300f000000
	VXORPD 15(R8)(R14*2), Z6, K4, Z14                  // 6211cd4c57b4700f000000
	VXORPS X11, X18, K2, X9                            // 62516c0257cb
	VXORPS X31, X18, K2, X9                            // 62116c0257cf
	VXORPS X3, X18, K2, X9                             // 62716c0257cb
	VXORPS 15(DX)(BX*1), X18, K2, X9                   // 62716c02578c1a0f000000
	VXORPS -7(CX)(DX*2), X18, K2, X9                   // 62716c02578c51f9ffffff
	VXORPS X11, X21, K2, X9                            // 6251540257cb
	VXORPS X31, X21, K2, X9                            // 6211540257cf
	VXORPS X3, X21, K2, X9                             // 6271540257cb
	VXORPS 15(DX)(BX*1), X21, K2, X9                   // 62715402578c1a0f000000
	VXORPS -7(CX)(DX*2), X21, K2, X9                   // 62715402578c51f9ffffff
	VXORPS X11, X1, K2, X9                             // 6251740a57cb
	VXORPS X31, X1, K2, X9                             // 6211740a57cf
	VXORPS X3, X1, K2, X9                              // 6271740a57cb
	VXORPS 15(DX)(BX*1), X1, K2, X9                    // 6271740a578c1a0f000000
	VXORPS -7(CX)(DX*2), X1, K2, X9                    // 6271740a578c51f9ffffff
	VXORPS X11, X18, K2, X15                           // 62516c0257fb
	VXORPS X31, X18, K2, X15                           // 62116c0257ff
	VXORPS X3, X18, K2, X15                            // 62716c0257fb
	VXORPS 15(DX)(BX*1), X18, K2, X15                  // 62716c0257bc1a0f000000
	VXORPS -7(CX)(DX*2), X18, K2, X15                  // 62716c0257bc51f9ffffff
	VXORPS X11, X21, K2, X15                           // 6251540257fb
	VXORPS X31, X21, K2, X15                           // 6211540257ff
	VXORPS X3, X21, K2, X15                            // 6271540257fb
	VXORPS 15(DX)(BX*1), X21, K2, X15                  // 6271540257bc1a0f000000
	VXORPS -7(CX)(DX*2), X21, K2, X15                  // 6271540257bc51f9ffffff
	VXORPS X11, X1, K2, X15                            // 6251740a57fb
	VXORPS X31, X1, K2, X15                            // 6211740a57ff
	VXORPS X3, X1, K2, X15                             // 6271740a57fb
	VXORPS 15(DX)(BX*1), X1, K2, X15                   // 6271740a57bc1a0f000000
	VXORPS -7(CX)(DX*2), X1, K2, X15                   // 6271740a57bc51f9ffffff
	VXORPS X11, X18, K2, X26                           // 62416c0257d3
	VXORPS X31, X18, K2, X26                           // 62016c0257d7
	VXORPS X3, X18, K2, X26                            // 62616c0257d3
	VXORPS 15(DX)(BX*1), X18, K2, X26                  // 62616c0257941a0f000000
	VXORPS -7(CX)(DX*2), X18, K2, X26                  // 62616c02579451f9ffffff
	VXORPS X11, X21, K2, X26                           // 6241540257d3
	VXORPS X31, X21, K2, X26                           // 6201540257d7
	VXORPS X3, X21, K2, X26                            // 6261540257d3
	VXORPS 15(DX)(BX*1), X21, K2, X26                  // 6261540257941a0f000000
	VXORPS -7(CX)(DX*2), X21, K2, X26                  // 62615402579451f9ffffff
	VXORPS X11, X1, K2, X26                            // 6241740a57d3
	VXORPS X31, X1, K2, X26                            // 6201740a57d7
	VXORPS X3, X1, K2, X26                             // 6261740a57d3
	VXORPS 15(DX)(BX*1), X1, K2, X26                   // 6261740a57941a0f000000
	VXORPS -7(CX)(DX*2), X1, K2, X26                   // 6261740a579451f9ffffff
	VXORPS Y17, Y12, K2, Y0                            // 62b11c2a57c1
	VXORPS Y7, Y12, K2, Y0                             // 62f11c2a57c7
	VXORPS Y9, Y12, K2, Y0                             // 62d11c2a57c1
	VXORPS 99(R15)(R15*8), Y12, K2, Y0                 // 62911c2a5784ff63000000
	VXORPS 7(AX)(CX*8), Y12, K2, Y0                    // 62f11c2a5784c807000000
	VXORPS Y17, Y1, K2, Y0                             // 62b1742a57c1
	VXORPS Y7, Y1, K2, Y0                              // 62f1742a57c7
	VXORPS Y9, Y1, K2, Y0                              // 62d1742a57c1
	VXORPS 99(R15)(R15*8), Y1, K2, Y0                  // 6291742a5784ff63000000
	VXORPS 7(AX)(CX*8), Y1, K2, Y0                     // 62f1742a5784c807000000
	VXORPS Y17, Y14, K2, Y0                            // 62b10c2a57c1
	VXORPS Y7, Y14, K2, Y0                             // 62f10c2a57c7
	VXORPS Y9, Y14, K2, Y0                             // 62d10c2a57c1
	VXORPS 99(R15)(R15*8), Y14, K2, Y0                 // 62910c2a5784ff63000000
	VXORPS 7(AX)(CX*8), Y14, K2, Y0                    // 62f10c2a5784c807000000
	VXORPS Y17, Y12, K2, Y22                           // 62a11c2a57f1
	VXORPS Y7, Y12, K2, Y22                            // 62e11c2a57f7
	VXORPS Y9, Y12, K2, Y22                            // 62c11c2a57f1
	VXORPS 99(R15)(R15*8), Y12, K2, Y22                // 62811c2a57b4ff63000000
	VXORPS 7(AX)(CX*8), Y12, K2, Y22                   // 62e11c2a57b4c807000000
	VXORPS Y17, Y1, K2, Y22                            // 62a1742a57f1
	VXORPS Y7, Y1, K2, Y22                             // 62e1742a57f7
	VXORPS Y9, Y1, K2, Y22                             // 62c1742a57f1
	VXORPS 99(R15)(R15*8), Y1, K2, Y22                 // 6281742a57b4ff63000000
	VXORPS 7(AX)(CX*8), Y1, K2, Y22                    // 62e1742a57b4c807000000
	VXORPS Y17, Y14, K2, Y22                           // 62a10c2a57f1
	VXORPS Y7, Y14, K2, Y22                            // 62e10c2a57f7
	VXORPS Y9, Y14, K2, Y22                            // 62c10c2a57f1
	VXORPS 99(R15)(R15*8), Y14, K2, Y22                // 62810c2a57b4ff63000000
	VXORPS 7(AX)(CX*8), Y14, K2, Y22                   // 62e10c2a57b4c807000000
	VXORPS Y17, Y12, K2, Y13                           // 62311c2a57e9
	VXORPS Y7, Y12, K2, Y13                            // 62711c2a57ef
	VXORPS Y9, Y12, K2, Y13                            // 62511c2a57e9
	VXORPS 99(R15)(R15*8), Y12, K2, Y13                // 62111c2a57acff63000000
	VXORPS 7(AX)(CX*8), Y12, K2, Y13                   // 62711c2a57acc807000000
	VXORPS Y17, Y1, K2, Y13                            // 6231742a57e9
	VXORPS Y7, Y1, K2, Y13                             // 6271742a57ef
	VXORPS Y9, Y1, K2, Y13                             // 6251742a57e9
	VXORPS 99(R15)(R15*8), Y1, K2, Y13                 // 6211742a57acff63000000
	VXORPS 7(AX)(CX*8), Y1, K2, Y13                    // 6271742a57acc807000000
	VXORPS Y17, Y14, K2, Y13                           // 62310c2a57e9
	VXORPS Y7, Y14, K2, Y13                            // 62710c2a57ef
	VXORPS Y9, Y14, K2, Y13                            // 62510c2a57e9
	VXORPS 99(R15)(R15*8), Y14, K2, Y13                // 62110c2a57acff63000000
	VXORPS 7(AX)(CX*8), Y14, K2, Y13                   // 62710c2a57acc807000000
	VXORPS Z21, Z3, K3, Z26                            // 6221644b57d5
	VXORPS Z13, Z3, K3, Z26                            // 6241644b57d5
	VXORPS (R14), Z3, K3, Z26                          // 6241644b5716
	VXORPS -7(DI)(R8*8), Z3, K3, Z26                   // 6221644b5794c7f9ffffff
	VXORPS Z21, Z0, K3, Z26                            // 62217c4b57d5
	VXORPS Z13, Z0, K3, Z26                            // 62417c4b57d5
	VXORPS (R14), Z0, K3, Z26                          // 62417c4b5716
	VXORPS -7(DI)(R8*8), Z0, K3, Z26                   // 62217c4b5794c7f9ffffff
	VXORPS Z21, Z3, K3, Z3                             // 62b1644b57dd
	VXORPS Z13, Z3, K3, Z3                             // 62d1644b57dd
	VXORPS (R14), Z3, K3, Z3                           // 62d1644b571e
	VXORPS -7(DI)(R8*8), Z3, K3, Z3                    // 62b1644b579cc7f9ffffff
	VXORPS Z21, Z0, K3, Z3                             // 62b17c4b57dd
	VXORPS Z13, Z0, K3, Z3                             // 62d17c4b57dd
	VXORPS (R14), Z0, K3, Z3                           // 62d17c4b571e
	VXORPS -7(DI)(R8*8), Z0, K3, Z3                    // 62b17c4b579cc7f9ffffff
	RET
