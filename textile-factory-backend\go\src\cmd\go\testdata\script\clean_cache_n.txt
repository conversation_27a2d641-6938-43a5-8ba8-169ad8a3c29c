# We're testing cache behavior, so start with a clean GOCACHE.
env GOCACHE=$WORK/cache

# Build something so that the cache gets populates
go build main.go

# Check that cache contains directories before running
exists $GOCACHE/00

# Run go clean -cache -n and ensure that directories weren't deleted
go clean -cache -n
exists $GOCACHE/00

# Re-run go clean cache without the -n flag go ensure that directories were properly removed
go clean -cache
! exists $GOCACHE/00

! go clean -cache .
stderr 'go: clean -cache cannot be used with package arguments'

-- main.go --
package main

import "fmt"

func main() {
	fmt.Println("hello!")
}
