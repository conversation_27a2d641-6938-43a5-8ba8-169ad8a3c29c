# Build
env GO111MODULE=off
go build vend/x
! stdout .
! stderr .

-- vend/dir1/dir1.go --
package dir1
-- vend/subdir/bad.go --
package subdir

import _ "r"
-- vend/subdir/good.go --
package subdir

import _ "p"
-- vend/vendor/p/p.go --
package p
-- vend/vendor/q/q.go --
package q
-- vend/vendor/vend/dir1/dir2/dir2.go --
package dir2
-- vend/x/invalid/invalid.go --
package invalid

import "vend/x/invalid/vendor/foo"
-- vend/x/vendor/p/p/p.go --
package p

import _ "notfound"
-- vend/x/vendor/p/p.go --
package p
-- vend/x/vendor/r/r.go --
package r
-- vend/x/x.go --
package x

import _ "p"
import _ "q"
import _ "r"
import _ "vend/dir1"      // not vendored
import _ "vend/dir1/dir2" // vendored
