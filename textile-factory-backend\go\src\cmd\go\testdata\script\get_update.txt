# Tests Issue #9224
# The recursive updating was trying to walk to
# former dependencies, not current ones.

[!net:github.com] skip
[!git] skip
env GO111MODULE=off

# Rewind
go get github.com/rsc/go-get-issue-9224-cmd
cd $GOPATH/src/github.com/rsc/go-get-issue-9224-lib
exec git reset --hard HEAD~
cd $GOPATH/src

# Run get
go get -u 'github.com/rsc/go-get-issue-9224-cmd'

# (Again with -d -u) Rewind
go get github.com/rsc/go-get-issue-9224-cmd
cd $GOPATH/src/github.com/rsc/go-get-issue-9224-lib
exec git reset --hard HEAD~
cd $GOPATH/src

# (Again with -d -u) Run get
go get -d -u 'github.com/rsc/go-get-issue-9224-cmd'
