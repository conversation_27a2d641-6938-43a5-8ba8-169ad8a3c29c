var __defProp = Object.defineProperty;
var __defNormalProp = (obj, key, value) => key in obj ? __defProp(obj, key, { enumerable: true, configurable: true, writable: true, value }) : obj[key] = value;
var __publicField = (obj, key, value) => {
  __defNormalProp(obj, typeof key !== "symbol" ? key + "" : key, value);
  return value;
};
if (typeof Promise !== "undefined" && !Promise.prototype.finally) {
  Promise.prototype.finally = function(callback) {
    const promise = this.constructor;
    return this.then(
      (value) => promise.resolve(callback()).then(() => value),
      (reason) => promise.resolve(callback()).then(() => {
        throw reason;
      })
    );
  };
}
;
if (typeof uni !== "undefined" && uni && uni.requireGlobal) {
  const global = uni.requireGlobal();
  ArrayBuffer = global.ArrayBuffer;
  Int8Array = global.Int8Array;
  Uint8Array = global.Uint8Array;
  Uint8ClampedArray = global.Uint8ClampedArray;
  Int16Array = global.Int16Array;
  Uint16Array = global.Uint16Array;
  Int32Array = global.Int32Array;
  Uint32Array = global.Uint32Array;
  Float32Array = global.Float32Array;
  Float64Array = global.Float64Array;
  BigInt64Array = global.BigInt64Array;
  BigUint64Array = global.BigUint64Array;
}
;
if (uni.restoreGlobal) {
  uni.restoreGlobal(Vue, weex, plus, setTimeout, clearTimeout, setInterval, clearInterval);
}
(function(vue) {
  "use strict";
  const _sfc_main$8 = /* @__PURE__ */ vue.defineComponent({
    __name: "login",
    setup(__props, { expose: __expose }) {
      __expose();
      const loginForm = vue.reactive({
        username: "",
        password: ""
      });
      const isLoading = vue.ref(false);
      const handleLogin = async () => {
        if (!loginForm.username || !loginForm.password) {
          uni.showToast({
            title: "请输入用户名和密码",
            icon: "none"
          });
          return;
        }
        isLoading.value = true;
        try {
          await new Promise((resolve) => setTimeout(resolve, 1e3));
          const mockUserResponse = {
            username: loginForm.username,
            role: loginForm.username.includes("mechanic") ? "mechanic" : "worker"
            // 简单模拟：用户名包含mechanic则为机修工，否则为织工
          };
          uni.setStorageSync("userInfo", mockUserResponse);
          uni.showToast({
            title: "登录成功",
            icon: "success"
          });
          setTimeout(() => {
            uni.switchTab({
              url: "/pages/workspace/workspace"
            });
          }, 1500);
        } catch (error) {
          uni.showToast({
            title: "登录失败",
            icon: "none"
          });
        } finally {
          isLoading.value = false;
        }
      };
      const __returned__ = { loginForm, isLoading, handleLogin };
      Object.defineProperty(__returned__, "__isScriptSetup", { enumerable: false, value: true });
      return __returned__;
    }
  });
  const _imports_0 = "/static/logo.png";
  const _export_sfc = (sfc, props) => {
    const target = sfc.__vccOpts || sfc;
    for (const [key, val] of props) {
      target[key] = val;
    }
    return target;
  };
  function _sfc_render$7(_ctx, _cache, $props, $setup, $data, $options) {
    return vue.openBlock(), vue.createElementBlock("view", { class: "login-container" }, [
      vue.createCommentVNode(" 状态栏占位 "),
      vue.createElementVNode("view", { class: "status-bar" }),
      vue.createCommentVNode(" 头部logo区域 "),
      vue.createElementVNode("view", { class: "header" }, [
        vue.createElementVNode("image", {
          class: "logo",
          src: _imports_0,
          mode: "aspectFit"
        }),
        vue.createElementVNode("text", { class: "title" }, "织厂管理系统"),
        vue.createElementVNode("text", { class: "subtitle" }, "机器异常管理平台")
      ]),
      vue.createCommentVNode(" 登录表单 "),
      vue.createElementVNode("view", { class: "form-container" }, [
        vue.createElementVNode("view", { class: "form-item" }, [
          vue.createElementVNode("view", { class: "form-label" }, "用户名"),
          vue.withDirectives(vue.createElementVNode(
            "input",
            {
              class: "form-input",
              "onUpdate:modelValue": _cache[0] || (_cache[0] = ($event) => $setup.loginForm.username = $event),
              placeholder: "请输入用户名",
              "placeholder-class": "placeholder"
            },
            null,
            512
            /* NEED_PATCH */
          ), [
            [vue.vModelText, $setup.loginForm.username]
          ])
        ]),
        vue.createElementVNode("view", { class: "form-item" }, [
          vue.createElementVNode("view", { class: "form-label" }, "密码"),
          vue.withDirectives(vue.createElementVNode(
            "input",
            {
              class: "form-input",
              "onUpdate:modelValue": _cache[1] || (_cache[1] = ($event) => $setup.loginForm.password = $event),
              placeholder: "请输入密码",
              "placeholder-class": "placeholder",
              password: ""
            },
            null,
            512
            /* NEED_PATCH */
          ), [
            [vue.vModelText, $setup.loginForm.password]
          ])
        ]),
        vue.createElementVNode("button", {
          class: vue.normalizeClass(["login-btn", { "loading": $setup.isLoading }]),
          onClick: $setup.handleLogin,
          disabled: $setup.isLoading
        }, vue.toDisplayString($setup.isLoading ? "登录中..." : "登录"), 11, ["disabled"])
      ]),
      vue.createCommentVNode(" 底部信息 "),
      vue.createElementVNode("view", { class: "footer" }, [
        vue.createElementVNode("text", { class: "footer-text" }, "织厂机器异常管理系统 v1.0.0")
      ])
    ]);
  }
  const PagesLoginLogin = /* @__PURE__ */ _export_sfc(_sfc_main$8, [["render", _sfc_render$7], ["__file", "D:/learn/uniapptest/testvue3/src/pages/login/login.vue"]]);
  function formatAppLog(type, filename, ...args) {
    if (uni.__log__) {
      uni.__log__(type, filename, ...args);
    } else {
      console[type].apply(console, [...args, filename]);
    }
  }
  const _sfc_main$7 = /* @__PURE__ */ vue.defineComponent({
    __name: "workspace",
    setup(__props, { expose: __expose }) {
      __expose();
      const userInfo = vue.ref({
        username: "",
        role: "",
        roleText: ""
      });
      const stats = vue.ref({
        todayTasks: 0,
        completedTasks: 0,
        pendingTasks: 0,
        totalTasks: 0
      });
      const quickActions = vue.ref([]);
      const reportHistory = vue.ref([
        {
          id: 1,
          machineCode: "M001",
          machineName: "织机A-01",
          reportTime: "2024-01-15 14:30",
          status: "pending",
          description: "机器异响，疑似轴承问题"
        },
        {
          id: 2,
          machineCode: "M002",
          machineName: "织机B-03",
          reportTime: "2024-01-15 10:15",
          status: "completed",
          description: "线头断裂频繁"
        },
        {
          id: 3,
          machineCode: "M003",
          machineName: "织机C-05",
          reportTime: "2024-01-14 16:45",
          status: "repairing",
          description: "温度过高，需要检查冷却系统"
        }
      ]);
      const getStatusInfo = (status) => {
        const statusMap = {
          pending: { text: "待维修", color: "#FF9800" },
          repairing: { text: "维修中", color: "#2196F3" },
          completed: { text: "已完成", color: "#4CAF50" }
        };
        return statusMap[status] || { text: "未知", color: "#999999" };
      };
      const getUserInfo = () => {
        const info = uni.getStorageSync("userInfo");
        if (info) {
          userInfo.value = {
            username: info.username,
            role: info.role,
            roleText: info.role === "worker" ? "织工" : "机修工"
          };
          if (info.role === "worker") {
            quickActions.value = [
              { icon: "📱", text: "扫码上报", action: "scan" },
              { icon: "📋", text: "上报记录", action: "history" },
              { icon: "📊", text: "工作统计", action: "stats" },
              { icon: "🔧", text: "设备状态", action: "equipment" }
            ];
          } else {
            quickActions.value = [
              { icon: "🔧", text: "维修任务", path: "/pages/mechanic/home" },
              { icon: "📝", text: "维修记录", action: "repair_history" },
              { icon: "📊", text: "工作统计", action: "stats" },
              { icon: "⚙️", text: "设备管理", action: "equipment" }
            ];
          }
        }
      };
      const getStats = () => {
        if (userInfo.value.role === "worker") {
          stats.value = {
            todayTasks: 3,
            completedTasks: 2,
            pendingTasks: 1,
            totalTasks: 15
          };
        } else {
          stats.value = {
            todayTasks: 5,
            completedTasks: 3,
            pendingTasks: 2,
            totalTasks: 28
          };
        }
      };
      const handleScanReport = () => {
        uni.scanCode({
          success: (res) => {
            formatAppLog("log", "at pages/workspace/workspace.vue:112", "扫码结果:", res.result);
            uni.navigateTo({
              url: `/pages/report/report?machineCode=${res.result}`
            });
          },
          fail: (err) => {
            formatAppLog("error", "at pages/workspace/workspace.vue:118", "扫码失败:", err);
            uni.showToast({
              title: "扫码失败",
              icon: "none"
            });
          }
        });
      };
      const viewReportDetail = (report) => {
        uni.showModal({
          title: "上报详情",
          content: `机器：${report.machineName}
时间：${report.reportTime}
状态：${getStatusInfo(report.status).text}
描述：${report.description}`,
          showCancel: false
        });
      };
      const handleLogout = () => {
        uni.showModal({
          title: "确认退出",
          content: "确定要退出登录吗？",
          success: (res) => {
            if (res.confirm) {
              uni.removeStorageSync("userInfo");
              uni.reLaunch({
                url: "/pages/login/login"
              });
            }
          }
        });
      };
      const handleQuickAction = (action) => {
        if (action.path) {
          uni.navigateTo({
            url: action.path
          });
        } else if (action.action === "scan") {
          handleScanReport();
        } else {
          uni.showToast({
            title: "功能开发中",
            icon: "none"
          });
        }
      };
      vue.onMounted(() => {
        getUserInfo();
        getStats();
      });
      const __returned__ = { userInfo, stats, quickActions, reportHistory, getStatusInfo, getUserInfo, getStats, handleScanReport, viewReportDetail, handleLogout, handleQuickAction };
      Object.defineProperty(__returned__, "__isScriptSetup", { enumerable: false, value: true });
      return __returned__;
    }
  });
  function _sfc_render$6(_ctx, _cache, $props, $setup, $data, $options) {
    return vue.openBlock(), vue.createElementBlock("view", { class: "workspace-container" }, [
      vue.createCommentVNode(" 欢迎信息 "),
      vue.createElementVNode("view", { class: "welcome-section" }, [
        vue.createElementVNode("view", { class: "welcome-content" }, [
          vue.createElementVNode("view", { class: "welcome-info" }, [
            vue.createElementVNode(
              "text",
              { class: "welcome-text" },
              "你好，" + vue.toDisplayString($setup.userInfo.username),
              1
              /* TEXT */
            ),
            vue.createElementVNode(
              "text",
              { class: "role-text" },
              vue.toDisplayString($setup.userInfo.roleText) + "工作台",
              1
              /* TEXT */
            )
          ]),
          vue.createElementVNode("view", {
            class: "logout-btn",
            onClick: $setup.handleLogout
          }, [
            vue.createElementVNode("text", { class: "logout-text" }, "退出")
          ])
        ])
      ]),
      vue.createCommentVNode(" 统计卡片 "),
      vue.createElementVNode("view", { class: "stats-section" }, [
        vue.createElementVNode("view", { class: "stats-card" }, [
          vue.createElementVNode("view", { class: "stat-item" }, [
            vue.createElementVNode(
              "text",
              { class: "stat-number" },
              vue.toDisplayString($setup.stats.todayTasks),
              1
              /* TEXT */
            ),
            vue.createElementVNode("text", { class: "stat-label" }, "今日任务")
          ]),
          vue.createElementVNode("view", { class: "stat-item" }, [
            vue.createElementVNode(
              "text",
              { class: "stat-number" },
              vue.toDisplayString($setup.stats.completedTasks),
              1
              /* TEXT */
            ),
            vue.createElementVNode("text", { class: "stat-label" }, "已完成")
          ]),
          vue.createElementVNode("view", { class: "stat-item" }, [
            vue.createElementVNode(
              "text",
              { class: "stat-number" },
              vue.toDisplayString($setup.stats.pendingTasks),
              1
              /* TEXT */
            ),
            vue.createElementVNode("text", { class: "stat-label" }, "待处理")
          ]),
          vue.createElementVNode("view", { class: "stat-item" }, [
            vue.createElementVNode(
              "text",
              { class: "stat-number" },
              vue.toDisplayString($setup.stats.totalTasks),
              1
              /* TEXT */
            ),
            vue.createElementVNode("text", { class: "stat-label" }, "总任务")
          ])
        ])
      ]),
      vue.createCommentVNode(" 快捷功能 "),
      vue.createElementVNode("view", { class: "quick-actions" }, [
        vue.createElementVNode("view", { class: "section-title" }, "快捷功能"),
        vue.createElementVNode("view", { class: "actions-grid" }, [
          (vue.openBlock(true), vue.createElementBlock(
            vue.Fragment,
            null,
            vue.renderList($setup.quickActions, (action, index) => {
              return vue.openBlock(), vue.createElementBlock("view", {
                class: "action-item",
                key: index,
                onClick: ($event) => $setup.handleQuickAction(action)
              }, [
                vue.createElementVNode(
                  "view",
                  { class: "action-icon" },
                  vue.toDisplayString(action.icon),
                  1
                  /* TEXT */
                ),
                vue.createElementVNode(
                  "text",
                  { class: "action-text" },
                  vue.toDisplayString(action.text),
                  1
                  /* TEXT */
                )
              ], 8, ["onClick"]);
            }),
            128
            /* KEYED_FRAGMENT */
          ))
        ])
      ]),
      vue.createCommentVNode(" 织工历史记录 "),
      $setup.userInfo.role === "worker" ? (vue.openBlock(), vue.createElementBlock("view", {
        key: 0,
        class: "recent-section"
      }, [
        vue.createElementVNode("view", { class: "section-header" }, [
          vue.createElementVNode("view", { class: "section-title" }, "最近上报"),
          vue.createElementVNode("text", { class: "view-all" }, "查看全部")
        ]),
        vue.createElementVNode("view", { class: "history-list" }, [
          (vue.openBlock(true), vue.createElementBlock(
            vue.Fragment,
            null,
            vue.renderList($setup.reportHistory, (item) => {
              return vue.openBlock(), vue.createElementBlock("view", {
                class: "history-item",
                key: item.id,
                onClick: ($event) => $setup.viewReportDetail(item)
              }, [
                vue.createElementVNode("view", { class: "machine-info" }, [
                  vue.createElementVNode(
                    "text",
                    { class: "machine-name" },
                    vue.toDisplayString(item.machineName),
                    1
                    /* TEXT */
                  ),
                  vue.createElementVNode(
                    "text",
                    { class: "machine-code" },
                    vue.toDisplayString(item.machineCode),
                    1
                    /* TEXT */
                  )
                ]),
                vue.createElementVNode("view", { class: "report-info" }, [
                  vue.createElementVNode(
                    "text",
                    { class: "report-time" },
                    vue.toDisplayString(item.reportTime),
                    1
                    /* TEXT */
                  ),
                  vue.createElementVNode(
                    "view",
                    {
                      class: "status-tag",
                      style: vue.normalizeStyle({ backgroundColor: $setup.getStatusInfo(item.status).color })
                    },
                    [
                      vue.createElementVNode(
                        "text",
                        { class: "status-text" },
                        vue.toDisplayString($setup.getStatusInfo(item.status).text),
                        1
                        /* TEXT */
                      )
                    ],
                    4
                    /* STYLE */
                  )
                ])
              ], 8, ["onClick"]);
            }),
            128
            /* KEYED_FRAGMENT */
          ))
        ])
      ])) : (vue.openBlock(), vue.createElementBlock(
        vue.Fragment,
        { key: 1 },
        [
          vue.createCommentVNode(" 机修工最近活动 "),
          vue.createElementVNode("view", { class: "recent-section" }, [
            vue.createElementVNode("view", { class: "section-title" }, "最近活动"),
            vue.createElementVNode("view", { class: "activity-list" }, [
              vue.createElementVNode("view", { class: "activity-item" }, [
                vue.createElementVNode("view", { class: "activity-icon" }, "🔧"),
                vue.createElementVNode("view", { class: "activity-content" }, [
                  vue.createElementVNode("text", { class: "activity-title" }, "设备维修完成"),
                  vue.createElementVNode("text", { class: "activity-time" }, "2小时前")
                ])
              ]),
              vue.createElementVNode("view", { class: "activity-item" }, [
                vue.createElementVNode("view", { class: "activity-icon" }, "📱"),
                vue.createElementVNode("view", { class: "activity-content" }, [
                  vue.createElementVNode("text", { class: "activity-title" }, "异常上报"),
                  vue.createElementVNode("text", { class: "activity-time" }, "4小时前")
                ])
              ]),
              vue.createElementVNode("view", { class: "activity-item" }, [
                vue.createElementVNode("view", { class: "activity-icon" }, "✅"),
                vue.createElementVNode("view", { class: "activity-content" }, [
                  vue.createElementVNode("text", { class: "activity-title" }, "任务完成"),
                  vue.createElementVNode("text", { class: "activity-time" }, "昨天")
                ])
              ])
            ])
          ])
        ],
        2112
        /* STABLE_FRAGMENT, DEV_ROOT_FRAGMENT */
      ))
    ]);
  }
  const PagesWorkspaceWorkspace = /* @__PURE__ */ _export_sfc(_sfc_main$7, [["render", _sfc_render$6], ["__file", "D:/learn/uniapptest/testvue3/src/pages/workspace/workspace.vue"]]);
  const _sfc_main$6 = /* @__PURE__ */ vue.defineComponent({
    __name: "profile",
    setup(__props, { expose: __expose }) {
      __expose();
      const userInfo = vue.ref({
        username: "",
        role: "",
        roleText: ""
      });
      const getUserInfo = () => {
        const info = uni.getStorageSync("userInfo");
        if (info) {
          userInfo.value = {
            username: info.username,
            role: info.role,
            roleText: info.role === "worker" ? "织工" : "机修工"
          };
        }
      };
      const handleLogout = () => {
        uni.showModal({
          title: "提示",
          content: "确定要退出登录吗？",
          success: (res) => {
            if (res.confirm) {
              uni.removeStorageSync("userInfo");
              uni.reLaunch({
                url: "/pages/login/login"
              });
            }
          }
        });
      };
      vue.onMounted(() => {
        getUserInfo();
      });
      const __returned__ = { userInfo, getUserInfo, handleLogout };
      Object.defineProperty(__returned__, "__isScriptSetup", { enumerable: false, value: true });
      return __returned__;
    }
  });
  function _sfc_render$5(_ctx, _cache, $props, $setup, $data, $options) {
    return vue.openBlock(), vue.createElementBlock("view", { class: "profile-container" }, [
      vue.createCommentVNode(" 用户信息卡片 "),
      vue.createElementVNode("view", { class: "user-card" }, [
        vue.createElementVNode("view", { class: "avatar" }, [
          vue.createElementVNode(
            "text",
            { class: "avatar-text" },
            vue.toDisplayString($setup.userInfo.username.charAt(0).toUpperCase()),
            1
            /* TEXT */
          )
        ]),
        vue.createElementVNode("view", { class: "user-info" }, [
          vue.createElementVNode(
            "text",
            { class: "username" },
            vue.toDisplayString($setup.userInfo.username),
            1
            /* TEXT */
          ),
          vue.createElementVNode(
            "text",
            { class: "role" },
            vue.toDisplayString($setup.userInfo.roleText),
            1
            /* TEXT */
          )
        ])
      ]),
      vue.createCommentVNode(" 功能菜单 "),
      vue.createElementVNode("view", { class: "menu-section" }, [
        vue.createElementVNode("view", { class: "menu-item" }, [
          vue.createElementVNode("view", { class: "menu-icon" }, "📊"),
          vue.createElementVNode("text", { class: "menu-text" }, "工作统计"),
          vue.createElementVNode("text", { class: "menu-arrow" }, ">")
        ]),
        vue.createElementVNode("view", { class: "menu-item" }, [
          vue.createElementVNode("view", { class: "menu-icon" }, "⚙️"),
          vue.createElementVNode("text", { class: "menu-text" }, "设置"),
          vue.createElementVNode("text", { class: "menu-arrow" }, ">")
        ]),
        vue.createElementVNode("view", { class: "menu-item" }, [
          vue.createElementVNode("view", { class: "menu-icon" }, "❓"),
          vue.createElementVNode("text", { class: "menu-text" }, "帮助与反馈"),
          vue.createElementVNode("text", { class: "menu-arrow" }, ">")
        ]),
        vue.createElementVNode("view", { class: "menu-item" }, [
          vue.createElementVNode("view", { class: "menu-icon" }, "ℹ️"),
          vue.createElementVNode("text", { class: "menu-text" }, "关于我们"),
          vue.createElementVNode("text", { class: "menu-arrow" }, ">")
        ])
      ]),
      vue.createCommentVNode(" 退出登录 "),
      vue.createElementVNode("view", { class: "logout-section" }, [
        vue.createElementVNode("button", {
          class: "logout-btn",
          onClick: $setup.handleLogout
        }, " 退出登录 ")
      ])
    ]);
  }
  const PagesProfileProfile = /* @__PURE__ */ _export_sfc(_sfc_main$6, [["render", _sfc_render$5], ["__file", "D:/learn/uniapptest/testvue3/src/pages/profile/profile.vue"]]);
  const API_CONFIG = {
    BASE_URL: "http://localhost:8080/api/v1",
    TIMEOUT: 1e4
  };
  let authToken = uni.getStorageSync("token") || "";
  function getAuthToken() {
    return authToken || uni.getStorageSync("token");
  }
  function clearAuth() {
    authToken = "";
    uni.removeStorageSync("token");
    uni.removeStorageSync("userInfo");
  }
  function request(options) {
    return new Promise((resolve, reject) => {
      const token = getAuthToken();
      uni.request({
        url: `${API_CONFIG.BASE_URL}${options.url}`,
        method: options.method || "GET",
        data: options.data || {},
        header: {
          "Content-Type": "application/json",
          ...token ? { "Authorization": `Bearer ${token}` } : {},
          ...options.header || {}
        },
        timeout: API_CONFIG.TIMEOUT,
        success: (res) => {
          var _a;
          if (res.statusCode === 200) {
            resolve(res.data);
          } else if (res.statusCode === 401) {
            clearAuth();
            uni.showToast({
              title: "登录已过期，请重新登录",
              icon: "none"
            });
            uni.reLaunch({
              url: "/pages/login/login"
            });
            reject(new Error("登录已过期"));
          } else {
            const errorMsg = ((_a = res.data) == null ? void 0 : _a.error) || `请求失败 (${res.statusCode})`;
            reject(new Error(errorMsg));
          }
        },
        fail: (err) => {
          formatAppLog("error", "at utils/api.js:76", "API请求失败:", err);
          let errorMsg = "网络请求失败";
          if (err.errMsg) {
            if (err.errMsg.includes("timeout")) {
              errorMsg = "请求超时，请检查网络连接";
            } else if (err.errMsg.includes("fail")) {
              errorMsg = "无法连接到服务器";
            }
          }
          reject(new Error(errorMsg));
        }
      });
    });
  }
  const API = {
    // 用户认证
    auth: {
      login: (data) => request({
        url: "/login",
        method: "POST",
        data
      }),
      getUserInfo: () => request({
        url: "/user/info"
      })
    },
    // 二维码扫描
    qr: {
      scan: (qrCode) => request({
        url: `/qr/scan/${qrCode}`
      }),
      scanForReport: (qrCode) => request({
        url: `/qr/scan/report/${qrCode}`
      }),
      scanForRepair: (qrCode) => request({
        url: `/qr/scan/repair/${qrCode}`
      })
    },
    // 机器管理
    machines: {
      getList: (params) => request({
        url: "/machines",
        data: params
      }),
      getByCode: (code) => request({
        url: `/machines/code/${code}`
      }),
      getByQRCode: (qrCode) => request({
        url: `/machines/qr/${qrCode}`
      }),
      updateStatus: (id, status) => request({
        url: `/machines/${id}/status`,
        method: "PUT",
        data: { status }
      })
    },
    // 异常管理
    anomalies: {
      create: (data) => request({
        url: "/anomalies",
        method: "POST",
        data
      }),
      getList: (params) => request({
        url: "/anomalies",
        data: params
      }),
      getDetail: (id) => request({
        url: `/anomalies/${id}`
      }),
      updateStatus: (id, status) => request({
        url: `/anomalies/${id}/status`,
        method: "PUT",
        data: { status }
      })
    },
    // 维修管理
    repairs: {
      start: (data) => request({
        url: "/repairs/start",
        method: "POST",
        data
      }),
      complete: (id, data) => request({
        url: `/repairs/${id}/complete`,
        method: "PUT",
        data
      }),
      getList: (params) => request({
        url: "/repairs",
        data: params
      }),
      getDetail: (id) => request({
        url: `/repairs/${id}`
      })
    }
  };
  class ScannerUtils {
    constructor() {
      this.scanReceiver = null;
      this.isPageActive = false;
      this.scanCallback = null;
      this.isInitialized = false;
      this.scanMode = "general";
      this.scanInterface = null;
      this.serviceConnection = null;
    }
    /**
     * 初始化扫码功能
     * @param {Function} callback 扫码成功回调函数
     * @param {Boolean} isPageActive 页面是否活跃
     * @param {String} mode 扫码模式: general/report/repair
     */
    async initScanner(callback, isPageActive = true, mode = "general") {
      formatAppLog("log", "at utils/scannerUtils.js:26", "initScaner", mode);
      this.scanCallback = callback;
      this.isPageActive = isPageActive;
      this.scanMode = mode;
      if (uni.getSystemInfoSync().platform !== "android") {
        formatAppLog("log", "at utils/scannerUtils.js:33", "非安卓平台，使用uni.scanCode");
        return this.useUniScanCode();
      }
      try {
        const sunmiResult = await this.initSunmiScanner();
        if (sunmiResult.success) {
          return sunmiResult;
        }
        formatAppLog("log", "at utils/scannerUtils.js:45", "商米扫码头初始化失败，尝试新大陆扫码头");
        return await this.initNewlandScanner();
      } catch (error) {
        formatAppLog("error", "at utils/scannerUtils.js:49", "初始化扫码功能失败：", error);
        return this.useUniScanCode();
      }
    }
    /**
     * 初始化商米扫码头
     */
    async initSunmiScanner() {
      try {
        const main = plus.android.runtimeMainActivity();
        await this.registerSunmiBroadcastReceiver(main);
        formatAppLog("log", "at utils/scannerUtils.js:64", "商米广播接收器注册成功");
        try {
          await this.bindSunmiScannerService(main);
          formatAppLog("log", "at utils/scannerUtils.js:69", "商米扫码服务绑定成功");
        } catch (serviceError) {
          formatAppLog("log", "at utils/scannerUtils.js:71", "商米扫码服务绑定失败，但广播接收器已注册，扫码功能仍可用:", serviceError);
        }
        this.isInitialized = true;
        formatAppLog("log", "at utils/scannerUtils.js:76", "商米扫码头初始化完成");
        return {
          success: true,
          message: "商米扫码功能已启用，请使用扫码枪扫描"
        };
      } catch (error) {
        formatAppLog("error", "at utils/scannerUtils.js:83", "商米扫码头初始化失败：", error);
        throw error;
      }
    }
    /**
     * 绑定商米扫码服务
     */
    async bindSunmiScannerService(main) {
      try {
        const Intent = plus.android.importClass("android.content.Intent");
        const Context = plus.android.importClass("android.content.Context");
        this.serviceConnection = plus.android.implements("android.content.ServiceConnection", {
          onServiceConnected: (name, service) => {
            formatAppLog("log", "at utils/scannerUtils.js:99", "商米扫码服务连接成功");
            try {
              const IScanInterface = plus.android.importClass("com.sunmi.scanner.IScanInterface");
              if (IScanInterface && IScanInterface.Stub) {
                this.scanInterface = IScanInterface.Stub.asInterface(service);
                formatAppLog("log", "at utils/scannerUtils.js:105", "IScanInterface获取成功");
              }
            } catch (interfaceError) {
              formatAppLog("log", "at utils/scannerUtils.js:108", "获取IScanInterface失败，但服务已连接:", interfaceError);
            }
          },
          onServiceDisconnected: (name) => {
            formatAppLog("log", "at utils/scannerUtils.js:112", "商米扫码服务断开连接");
            this.scanInterface = null;
          }
        });
        const intent = new Intent();
        intent.setPackage("com.sunmi.scanner");
        intent.setAction("com.sunmi.scanner.IScanInterface");
        try {
          main.startService(intent);
          formatAppLog("log", "at utils/scannerUtils.js:124", "商米扫码服务启动成功");
        } catch (startError) {
          formatAppLog("log", "at utils/scannerUtils.js:126", "启动服务失败，尝试直接绑定:", startError);
        }
        const BIND_AUTO_CREATE = Context.BIND_AUTO_CREATE || 1;
        const bindResult = main.bindService(intent, this.serviceConnection, BIND_AUTO_CREATE);
        if (!bindResult) {
          formatAppLog("log", "at utils/scannerUtils.js:134", "bindService返回false，可能服务不存在");
          throw new Error("绑定商米扫码服务失败：服务不可用");
        }
        formatAppLog("log", "at utils/scannerUtils.js:138", "商米扫码服务绑定请求已发送");
        return new Promise((resolve, reject) => {
          const timeout = setTimeout(() => {
            reject(new Error("服务连接超时"));
          }, 5e3);
          const originalOnServiceConnected = this.serviceConnection.onServiceConnected;
          this.serviceConnection.onServiceConnected = (name, service) => {
            clearTimeout(timeout);
            originalOnServiceConnected(name, service);
            resolve();
          };
        });
      } catch (error) {
        formatAppLog("error", "at utils/scannerUtils.js:155", "绑定商米扫码服务失败：", error);
        throw error;
      }
    }
    /**
     * 注册商米广播接收器
     */
    async registerSunmiBroadcastReceiver(main) {
      try {
        const IntentFilter = plus.android.importClass("android.content.IntentFilter");
        const BroadcastReceiver = plus.android.importClass("android.content.BroadcastReceiver");
        const filter = new IntentFilter();
        filter.addAction("com.sunmi.scanner.ACTION_DATA_CODE_RECEIVED");
        this.scanReceiver = plus.android.implements(
          "io.dcloud.feature.internal.reflect.BroadcastReceiver",
          {
            onReceive: (context, intent) => {
              formatAppLog("log", "at utils/scannerUtils.js:175", "商米扫码广播接收，isPageActive:", this.isPageActive);
              if (!this.isPageActive)
                return;
              try {
                const scanResult = intent.getStringExtra("data");
                const sourceBytes = intent.getByteArrayExtra("source_byte");
                formatAppLog("log", "at utils/scannerUtils.js:183", "商米扫码结果:", scanResult, sourceBytes);
                if (scanResult && this.scanCallback) {
                  this.handleScanResult(scanResult);
                }
              } catch (error) {
                formatAppLog("error", "at utils/scannerUtils.js:190", "处理商米广播数据时出错：", error);
                if (this.scanCallback) {
                  this.scanCallback({
                    success: false,
                    error: error.message
                  });
                }
              }
            }
          }
        );
        main.registerReceiver(this.scanReceiver, filter);
        formatAppLog("log", "at utils/scannerUtils.js:204", "商米扫码广播接收器注册成功");
      } catch (error) {
        formatAppLog("error", "at utils/scannerUtils.js:206", "注册商米广播接收器失败：", error);
        throw error;
      }
    }
    /**
     * 初始化新大陆扫码头（作为备选方案）
     */
    async initNewlandScanner() {
      try {
        const main = plus.android.runtimeMainActivity();
        await this.configureNewlandScannerBroadcast(main);
        await this.registerNewlandBroadcastReceiver(main);
        this.isInitialized = true;
        formatAppLog("log", "at utils/scannerUtils.js:225", "新大陆扫码头初始化成功");
        return {
          success: true,
          message: "新大陆扫码功能已启用，请使用扫码枪扫描"
        };
      } catch (error) {
        formatAppLog("error", "at utils/scannerUtils.js:232", "新大陆扫码头初始化失败：", error);
        throw error;
      }
    }
    /**
     * 配置新大陆扫码枪广播设置
     */
    async configureNewlandScannerBroadcast(main) {
      try {
        const Intent = plus.android.importClass("android.content.Intent");
        const intent = new Intent("com.android.scanner.service_settings");
        intent.putExtra(
          "action_barcode_broadcast",
          "com.android.server.scannerservice.broadcast"
        );
        intent.putExtra("key_barcode_broadcast", "scannerdata");
        main.sendBroadcast(intent);
        formatAppLog("log", "at utils/scannerUtils.js:252", "新大陆扫码枪广播配置完成");
      } catch (error) {
        formatAppLog("error", "at utils/scannerUtils.js:254", "配置新大陆扫码枪广播失败：", error);
        throw error;
      }
    }
    /**
     * 注册新大陆广播接收器
     */
    async registerNewlandBroadcastReceiver(main) {
      try {
        const IntentFilter = plus.android.importClass("android.content.IntentFilter");
        const filter = new IntentFilter();
        filter.addAction("com.android.server.scannerservice.broadcast");
        this.scanReceiver = plus.android.implements(
          "io.dcloud.feature.internal.reflect.BroadcastReceiver",
          {
            onReceive: (context, intent) => {
              formatAppLog("log", "at utils/scannerUtils.js:272", "新大陆扫码广播接收，isPageActive:", this.isPageActive);
              if (!this.isPageActive)
                return;
              try {
                const scanResult = intent.getStringExtra("scannerdata");
                formatAppLog("log", "at utils/scannerUtils.js:278", "新大陆扫码结果:", scanResult);
                if (scanResult && this.scanCallback) {
                  this.handleScanResult(scanResult);
                }
              } catch (error) {
                formatAppLog("error", "at utils/scannerUtils.js:285", "处理新大陆广播数据时出错：", error);
                if (this.scanCallback) {
                  this.scanCallback({
                    success: false,
                    error: error.message
                  });
                }
              }
            }
          }
        );
        main.registerReceiver(this.scanReceiver, filter);
        formatAppLog("log", "at utils/scannerUtils.js:299", "新大陆扫码广播接收器注册成功");
      } catch (error) {
        formatAppLog("error", "at utils/scannerUtils.js:301", "注册新大陆广播接收器失败：", error);
        throw error;
      }
    }
    /**
     * 使用uni.scanCode作为降级方案
     */
    useUniScanCode() {
      formatAppLog("log", "at utils/scannerUtils.js:310", "使用uni.scanCode扫码");
      uni.scanCode({
        success: (res) => {
          if (this.scanCallback) {
            this.handleScanResult(res.result);
          }
        },
        fail: (err) => {
          formatAppLog("error", "at utils/scannerUtils.js:319", "扫码失败:", err);
          if (this.scanCallback) {
            this.scanCallback({
              success: false,
              error: err.errMsg || "扫码失败"
            });
          }
        }
      });
      return {
        success: true,
        message: "使用系统扫码功能"
      };
    }
    /**
     * 设置页面活跃状态
     */
    setPageActive(isActive) {
      this.isPageActive = isActive;
    }
    /**
     * 销毁扫码功能
     */
    destroy() {
      if (this.scanReceiver && uni.getSystemInfoSync().platform === "android") {
        try {
          const main = plus.android.runtimeMainActivity();
          main.unregisterReceiver(this.scanReceiver);
          formatAppLog("log", "at utils/scannerUtils.js:350", "扫码广播接收器已注销");
        } catch (error) {
          formatAppLog("error", "at utils/scannerUtils.js:352", "注销广播接收器失败：", error);
        }
      }
      if (this.serviceConnection && uni.getSystemInfoSync().platform === "android") {
        try {
          const main = plus.android.runtimeMainActivity();
          main.unbindService(this.serviceConnection);
          formatAppLog("log", "at utils/scannerUtils.js:361", "商米扫码服务已解绑");
        } catch (error) {
          formatAppLog("error", "at utils/scannerUtils.js:363", "解绑商米扫码服务失败：", error);
        }
      }
      this.scanReceiver = null;
      this.serviceConnection = null;
      this.scanInterface = null;
      this.scanCallback = null;
      this.isInitialized = false;
    }
    /**
     * 手动触发扫码（用于按钮点击等场景）
     */
    startScan() {
      if (!this.isInitialized) {
        return this.useUniScanCode();
      }
      uni.showToast({
        title: "请使用扫码枪扫描",
        icon: "none",
        duration: 2e3
      });
    }
    /**
     * 根据扫码模式处理扫码结果
     * @param {String} scanResult 扫码结果
     */
    async handleScanResult(scanResult) {
      formatAppLog("log", "at utils/scannerUtils.js:396", "处理扫码结果:", scanResult, "模式:", this.scanMode);
      try {
        let apiResult = null;
        switch (this.scanMode) {
          case "report":
            apiResult = await API.qr.scanForReport(scanResult);
            break;
          case "repair":
            apiResult = await API.qr.scanForRepair(scanResult);
            break;
          default:
            apiResult = await API.qr.scan(scanResult);
            break;
        }
        if (this.scanCallback) {
          this.scanCallback({
            success: true,
            result: scanResult,
            scanType: "QR_CODE",
            mode: this.scanMode,
            data: apiResult
          });
        }
      } catch (error) {
        formatAppLog("error", "at utils/scannerUtils.js:428", "API调用失败:", error);
        if (this.scanCallback) {
          this.scanCallback({
            success: true,
            result: scanResult,
            scanType: "QR_CODE",
            mode: this.scanMode,
            apiError: error.message,
            data: null
          });
        }
      }
    }
    /**
     * 设置扫码模式
     * @param {String} mode 扫码模式
     */
    setScanMode(mode) {
      this.scanMode = mode;
      formatAppLog("log", "at utils/scannerUtils.js:450", "扫码模式已设置为:", mode);
    }
  }
  const scannerUtils = new ScannerUtils();
  const __default__$2 = {
    onShow() {
      formatAppLog("log", "at pages/worker/home.vue:190", "织工页面显示，激活扫码功能");
      if (typeof scannerUtils !== "undefined") {
        scannerUtils.setPageActive(true);
      }
    },
    onHide() {
      formatAppLog("log", "at pages/worker/home.vue:197", "织工页面隐藏，停用扫码功能");
      if (typeof scannerUtils !== "undefined") {
        scannerUtils.setPageActive(false);
      }
    },
    onUnload() {
      formatAppLog("log", "at pages/worker/home.vue:203", "织工页面卸载，清理扫码资源");
      if (typeof scannerUtils !== "undefined") {
        scannerUtils.destroy();
      }
    }
  };
  const _sfc_main$5 = /* @__PURE__ */ vue.defineComponent({
    ...__default__$2,
    __name: "home",
    setup(__props, { expose: __expose }) {
      __expose();
      const userInfo = vue.ref({});
      const reportHistory = vue.ref([]);
      const isLoadingHistory = vue.ref(false);
      const getStatusInfo = (status) => {
        const statusMap = {
          "待维修": { text: "待维修", color: "#FF9800" },
          "维修中": { text: "维修中", color: "#2196F3" },
          "已完成": { text: "已完成", color: "#4CAF50" }
        };
        return statusMap[status] || { text: "未知", color: "#999999" };
      };
      const handleScanReport = async () => {
        try {
          const initResult = await scannerUtils.initScanner((result) => {
            if (result.success) {
              formatAppLog("log", "at pages/worker/home.vue:29", "扫码结果:", result);
              if (result.apiError) {
                uni.showToast({
                  title: result.apiError,
                  icon: "none",
                  duration: 2e3
                });
                uni.navigateTo({
                  url: `/pages/report/report?qrCode=${result.result}`
                });
              } else if (result.data) {
                if (result.data.can_report) {
                  uni.navigateTo({
                    url: `/pages/report/report?qrCode=${result.result}&machineData=${encodeURIComponent(JSON.stringify(result.data.machine))}`
                  });
                } else {
                  uni.showModal({
                    title: "提示",
                    content: result.data.message || "该机器已有未完成的异常报告",
                    showCancel: true,
                    cancelText: "取消",
                    confirmText: "查看详情",
                    success: (res) => {
                      if (res.confirm && result.data.pending_anomaly) {
                        uni.navigateTo({
                          url: `/pages/report/detail?id=${result.data.pending_anomaly.id}`
                        });
                      }
                    }
                  });
                }
              }
            } else {
              formatAppLog("error", "at pages/worker/home.vue:69", "扫码失败:", result.error);
              uni.showToast({
                title: result.error || "扫码失败",
                icon: "none"
              });
            }
          }, true, "report");
          if (initResult.success) {
            uni.showToast({
              title: "请扫描机器二维码",
              icon: "none",
              duration: 2e3
            });
          }
        } catch (error) {
          formatAppLog("error", "at pages/worker/home.vue:85", "初始化扫码失败:", error);
          uni.showToast({
            title: "扫码功能初始化失败",
            icon: "none"
          });
        }
      };
      const viewReportDetail = (report) => {
        uni.navigateTo({
          url: `/pages/report/detail?id=${report.id}`
        });
      };
      const loadReportHistory = async () => {
        try {
          isLoadingHistory.value = true;
          const response = await API.anomalies.getList({
            page: 1,
            page_size: 5
            // 只显示最近5条
          });
          if (response && response.anomalies) {
            reportHistory.value = response.anomalies.map((anomaly) => {
              var _a, _b;
              return {
                id: anomaly.id,
                machineCode: ((_a = anomaly.machine) == null ? void 0 : _a.code) || "",
                machineName: ((_b = anomaly.machine) == null ? void 0 : _b.name) || "",
                reportTime: new Date(anomaly.created_at).toLocaleString(),
                status: anomaly.status,
                description: anomaly.description
              };
            });
          }
        } catch (error) {
          formatAppLog("error", "at pages/worker/home.vue:120", "加载历史记录失败:", error);
          uni.showToast({
            title: "加载历史记录失败",
            icon: "none"
          });
        } finally {
          isLoadingHistory.value = false;
        }
      };
      const loadUserInfo = async () => {
        try {
          const response = await API.auth.getUserInfo();
          if (response) {
            userInfo.value = response;
            uni.setStorageSync("userInfo", response);
          }
        } catch (error) {
          formatAppLog("error", "at pages/worker/home.vue:139", "获取用户信息失败:", error);
          uni.reLaunch({
            url: "/pages/login/login"
          });
        }
      };
      const handleLogout = () => {
        uni.showModal({
          title: "确认退出",
          content: "确定要退出登录吗？",
          success: (res) => {
            if (res.confirm) {
              uni.removeStorageSync("userInfo");
              uni.removeStorageSync("token");
              uni.reLaunch({
                url: "/pages/login/login"
              });
            }
          }
        });
      };
      vue.onMounted(async () => {
        scannerUtils.setPageActive(true);
        const user = uni.getStorageSync("userInfo");
        if (user) {
          userInfo.value = user;
        }
        await loadUserInfo();
        await loadReportHistory();
      });
      vue.onUnmounted(() => {
        scannerUtils.destroy();
      });
      const __returned__ = { userInfo, reportHistory, isLoadingHistory, getStatusInfo, handleScanReport, viewReportDetail, loadReportHistory, loadUserInfo, handleLogout };
      Object.defineProperty(__returned__, "__isScriptSetup", { enumerable: false, value: true });
      return __returned__;
    }
  });
  function _sfc_render$4(_ctx, _cache, $props, $setup, $data, $options) {
    var _a, _b;
    return vue.openBlock(), vue.createElementBlock("view", { class: "worker-home" }, [
      vue.createCommentVNode(" 用户信息头部 "),
      vue.createElementVNode("view", { class: "user-header" }, [
        vue.createElementVNode("view", { class: "user-info" }, [
          vue.createElementVNode("view", { class: "avatar" }, [
            vue.createElementVNode(
              "text",
              { class: "avatar-text" },
              vue.toDisplayString((_b = (_a = $setup.userInfo.username) == null ? void 0 : _a.charAt(0)) == null ? void 0 : _b.toUpperCase()),
              1
              /* TEXT */
            )
          ]),
          vue.createElementVNode("view", { class: "user-details" }, [
            vue.createElementVNode(
              "text",
              { class: "username" },
              vue.toDisplayString($setup.userInfo.username),
              1
              /* TEXT */
            ),
            vue.createElementVNode("text", { class: "role" }, "织工")
          ])
        ]),
        vue.createElementVNode("view", {
          class: "logout-btn",
          onClick: $setup.handleLogout
        }, [
          vue.createElementVNode("text", { class: "logout-text" }, "退出")
        ])
      ]),
      vue.createCommentVNode(" 快速操作区域 "),
      vue.createElementVNode("view", { class: "quick-actions" }, [
        vue.createElementVNode("view", {
          class: "scan-card",
          onClick: $setup.handleScanReport
        }, [
          vue.createElementVNode("view", { class: "scan-icon" }, [
            vue.createElementVNode("text", { class: "icon" }, "📱")
          ]),
          vue.createElementVNode("view", { class: "scan-content" }, [
            vue.createElementVNode("text", { class: "scan-title" }, "扫码上报异常"),
            vue.createElementVNode("text", { class: "scan-desc" }, "扫描机器二维码快速上报故障")
          ]),
          vue.createElementVNode("view", { class: "scan-arrow" }, [
            vue.createElementVNode("text", { class: "arrow" }, "→")
          ])
        ])
      ]),
      vue.createCommentVNode(" 统计信息 "),
      vue.createElementVNode("view", { class: "stats-section" }, [
        vue.createElementVNode("text", { class: "section-title" }, "今日统计"),
        vue.createElementVNode("view", { class: "stats-grid" }, [
          vue.createElementVNode("view", { class: "stat-item" }, [
            vue.createElementVNode("text", { class: "stat-number" }, "3"),
            vue.createElementVNode("text", { class: "stat-label" }, "今日上报")
          ]),
          vue.createElementVNode("view", { class: "stat-item" }, [
            vue.createElementVNode("text", { class: "stat-number" }, "1"),
            vue.createElementVNode("text", { class: "stat-label" }, "待维修")
          ]),
          vue.createElementVNode("view", { class: "stat-item" }, [
            vue.createElementVNode("text", { class: "stat-number" }, "2"),
            vue.createElementVNode("text", { class: "stat-label" }, "已完成")
          ])
        ])
      ]),
      vue.createCommentVNode(" 历史记录 "),
      vue.createElementVNode("view", { class: "history-section" }, [
        vue.createElementVNode("view", { class: "section-header" }, [
          vue.createElementVNode("text", { class: "section-title" }, "最近上报"),
          vue.createElementVNode(
            "text",
            {
              class: "view-all",
              onClick: $setup.loadReportHistory
            },
            vue.toDisplayString($setup.isLoadingHistory ? "加载中..." : "刷新"),
            1
            /* TEXT */
          )
        ]),
        $setup.reportHistory.length > 0 ? (vue.openBlock(), vue.createElementBlock("view", {
          key: 0,
          class: "history-list"
        }, [
          (vue.openBlock(true), vue.createElementBlock(
            vue.Fragment,
            null,
            vue.renderList($setup.reportHistory, (item) => {
              return vue.openBlock(), vue.createElementBlock("view", {
                class: "history-item",
                key: item.id,
                onClick: ($event) => $setup.viewReportDetail(item)
              }, [
                vue.createElementVNode("view", { class: "machine-info" }, [
                  vue.createElementVNode(
                    "text",
                    { class: "machine-name" },
                    vue.toDisplayString(item.machineName),
                    1
                    /* TEXT */
                  ),
                  vue.createElementVNode(
                    "text",
                    { class: "machine-code" },
                    vue.toDisplayString(item.machineCode),
                    1
                    /* TEXT */
                  )
                ]),
                vue.createElementVNode("view", { class: "report-info" }, [
                  vue.createElementVNode(
                    "text",
                    { class: "report-time" },
                    vue.toDisplayString(item.reportTime),
                    1
                    /* TEXT */
                  ),
                  vue.createElementVNode(
                    "view",
                    {
                      class: "status-tag",
                      style: vue.normalizeStyle({ backgroundColor: $setup.getStatusInfo(item.status).color })
                    },
                    [
                      vue.createElementVNode(
                        "text",
                        { class: "status-text" },
                        vue.toDisplayString($setup.getStatusInfo(item.status).text),
                        1
                        /* TEXT */
                      )
                    ],
                    4
                    /* STYLE */
                  )
                ])
              ], 8, ["onClick"]);
            }),
            128
            /* KEYED_FRAGMENT */
          ))
        ])) : !$setup.isLoadingHistory ? (vue.openBlock(), vue.createElementBlock("view", {
          key: 1,
          class: "empty-state"
        }, [
          vue.createElementVNode("text", { class: "empty-text" }, "暂无上报记录"),
          vue.createElementVNode("text", { class: "empty-desc" }, "扫码上报机器异常后，记录将显示在这里")
        ])) : vue.createCommentVNode("v-if", true),
        $setup.isLoadingHistory ? (vue.openBlock(), vue.createElementBlock("view", {
          key: 2,
          class: "loading-state"
        }, [
          vue.createElementVNode("text", { class: "loading-text" }, "加载中...")
        ])) : vue.createCommentVNode("v-if", true)
      ])
    ]);
  }
  const PagesWorkerHome = /* @__PURE__ */ _export_sfc(_sfc_main$5, [["render", _sfc_render$4], ["__file", "D:/learn/uniapptest/testvue3/src/pages/worker/home.vue"]]);
  const __default__$1 = {
    onShow() {
      formatAppLog("log", "at pages/mechanic/home.vue:233", "机修工页面显示，激活扫码功能");
      scannerUtils.setPageActive(true);
    },
    onHide() {
      formatAppLog("log", "at pages/mechanic/home.vue:237", "机修工页面隐藏，停用扫码功能");
      scannerUtils.setPageActive(false);
    },
    onUnload() {
      formatAppLog("log", "at pages/mechanic/home.vue:241", "机修工页面卸载，清理扫码资源");
      scannerUtils.destroy();
    }
  };
  const _sfc_main$4 = /* @__PURE__ */ vue.defineComponent({
    ...__default__$1,
    __name: "home",
    setup(__props, { expose: __expose }) {
      __expose();
      const userInfo = vue.ref({});
      const machineList = vue.ref([]);
      const isLoadingMachines = vue.ref(false);
      const stats = vue.ref({
        total: 0,
        normal: 0,
        warning: 0,
        error: 0,
        maintenance: 0
      });
      const getStatusInfo = (status) => {
        const statusMap = {
          "正常": { text: "正常", color: "#4CAF50", bgColor: "#E8F5E8" },
          "异常": { text: "异常", color: "#F44336", bgColor: "#FFEBEE" },
          "维修中": { text: "维修中", color: "#2196F3", bgColor: "#E3F2FD" }
        };
        return statusMap[status] || { text: "未知", color: "#999999", bgColor: "#F5F5F5" };
      };
      const handleScanRepair = async () => {
        try {
          const initResult = await scannerUtils.initScanner((result) => {
            if (result.success) {
              formatAppLog("log", "at pages/mechanic/home.vue:38", "扫码结果:", result);
              if (result.apiError) {
                uni.showToast({
                  title: result.apiError,
                  icon: "none",
                  duration: 2e3
                });
                uni.navigateTo({
                  url: `/pages/repair/repair?qrCode=${result.result}`
                });
              } else if (result.data) {
                const { machine, pending_anomalies, repairing_anomalies } = result.data;
                if (pending_anomalies.length > 0 || repairing_anomalies.length > 0) {
                  uni.navigateTo({
                    url: `/pages/repair/repair?qrCode=${result.result}&machineData=${encodeURIComponent(JSON.stringify(result.data))}`
                  });
                } else {
                  uni.showModal({
                    title: "提示",
                    content: `机器 ${machine.name} 当前状态正常，无需维修`,
                    showCancel: false
                  });
                }
              }
            } else {
              formatAppLog("error", "at pages/mechanic/home.vue:70", "扫码失败:", result.error);
              uni.showToast({
                title: result.error || "扫码失败",
                icon: "none"
              });
            }
          }, true, "repair");
          if (initResult.success) {
            uni.showToast({
              title: "请扫描机器二维码",
              icon: "none",
              duration: 2e3
            });
          }
        } catch (error) {
          formatAppLog("error", "at pages/mechanic/home.vue:86", "初始化扫码失败:", error);
          uni.showToast({
            title: "扫码功能初始化失败",
            icon: "none"
          });
        }
      };
      const viewMachineDetail = (machine) => {
        const statusInfo = getStatusInfo(machine.status);
        let content = `位置：${machine.location}
状态：${statusInfo.text}`;
        if (machine.anomaly_count > 0) {
          content += `
异常数量：${machine.anomaly_count}个`;
        }
        uni.showModal({
          title: machine.name,
          content,
          showCancel: true,
          confirmText: "查看异常",
          cancelText: "关闭",
          success: (res) => {
            if (res.confirm && machine.anomaly_count > 0) {
              uni.navigateTo({
                url: `/pages/repair/list?machineId=${machine.id}`
              });
            }
          }
        });
      };
      const startRepair = (machine) => {
        uni.navigateTo({
          url: `/pages/repair/repair?machineId=${machine.id}&machineCode=${machine.code}`
        });
      };
      const loadMachineList = async () => {
        try {
          isLoadingMachines.value = true;
          const response = await API.machines.getList({
            page: 1,
            page_size: 50
            // 加载所有机器
          });
          if (response && response.machines) {
            machineList.value = response.machines.map((machine) => ({
              ...machine,
              anomaly_count: parseInt(machine.qr_code) || 0
              // 后端临时将异常数量存在qr_code字段
            }));
            updateStats();
          }
        } catch (error) {
          formatAppLog("error", "at pages/mechanic/home.vue:147", "加载机器列表失败:", error);
          uni.showToast({
            title: "加载机器列表失败",
            icon: "none"
          });
        } finally {
          isLoadingMachines.value = false;
        }
      };
      const updateStats = () => {
        const total = machineList.value.length;
        const normal = machineList.value.filter((m) => m.status === "正常").length;
        const error = machineList.value.filter((m) => m.status === "异常").length;
        const maintenance = machineList.value.filter((m) => m.status === "维修中").length;
        stats.value = {
          total,
          normal,
          warning: 0,
          // 暂时不使用警告状态
          error,
          maintenance
        };
      };
      const loadUserInfo = async () => {
        try {
          const response = await API.auth.getUserInfo();
          if (response) {
            userInfo.value = response;
            uni.setStorageSync("userInfo", response);
          }
        } catch (error) {
          formatAppLog("error", "at pages/mechanic/home.vue:182", "获取用户信息失败:", error);
          uni.reLaunch({
            url: "/pages/login/login"
          });
        }
      };
      const handleLogout = () => {
        uni.showModal({
          title: "确认退出",
          content: "确定要退出登录吗？",
          success: (res) => {
            if (res.confirm) {
              uni.removeStorageSync("userInfo");
              uni.removeStorageSync("token");
              uni.reLaunch({
                url: "/pages/login/login"
              });
            }
          }
        });
      };
      vue.onMounted(async () => {
        scannerUtils.setPageActive(true);
        const user = uni.getStorageSync("userInfo");
        if (user) {
          userInfo.value = user;
        }
        await loadUserInfo();
        await loadMachineList();
      });
      vue.onUnmounted(() => {
        scannerUtils.destroy();
      });
      const __returned__ = { userInfo, machineList, isLoadingMachines, stats, getStatusInfo, handleScanRepair, viewMachineDetail, startRepair, loadMachineList, updateStats, loadUserInfo, handleLogout };
      Object.defineProperty(__returned__, "__isScriptSetup", { enumerable: false, value: true });
      return __returned__;
    }
  });
  function _sfc_render$3(_ctx, _cache, $props, $setup, $data, $options) {
    var _a, _b;
    return vue.openBlock(), vue.createElementBlock("view", { class: "mechanic-home" }, [
      vue.createCommentVNode(" 用户信息头部 "),
      vue.createElementVNode("view", { class: "user-header" }, [
        vue.createElementVNode("view", { class: "user-info" }, [
          vue.createElementVNode("view", { class: "avatar" }, [
            vue.createElementVNode(
              "text",
              { class: "avatar-text" },
              vue.toDisplayString((_b = (_a = $setup.userInfo.username) == null ? void 0 : _a.charAt(0)) == null ? void 0 : _b.toUpperCase()),
              1
              /* TEXT */
            )
          ]),
          vue.createElementVNode("view", { class: "user-details" }, [
            vue.createElementVNode(
              "text",
              { class: "username" },
              vue.toDisplayString($setup.userInfo.username),
              1
              /* TEXT */
            ),
            vue.createElementVNode("text", { class: "role" }, "机修工")
          ])
        ]),
        vue.createElementVNode("view", {
          class: "logout-btn",
          onClick: $setup.handleLogout
        }, [
          vue.createElementVNode("text", { class: "logout-text" }, "退出")
        ])
      ]),
      vue.createCommentVNode(" 扫码维修提示 "),
      vue.createElementVNode("view", {
        class: "scan-tip",
        onClick: $setup.handleScanRepair
      }, [
        vue.createElementVNode("view", { class: "scan-icon" }, [
          vue.createElementVNode("text", { class: "icon" }, "📱")
        ]),
        vue.createElementVNode("view", { class: "scan-text" }, [
          vue.createElementVNode("text", { class: "tip-title" }, "扫码开始维修"),
          vue.createElementVNode("text", { class: "tip-desc" }, "扫描机器二维码直接进入维修页面")
        ]),
        vue.createElementVNode("view", { class: "scan-arrow" }, [
          vue.createElementVNode("text", { class: "arrow" }, "→")
        ])
      ]),
      vue.createCommentVNode(" 统计概览 "),
      vue.createElementVNode("view", { class: "stats-overview" }, [
        vue.createElementVNode("text", { class: "section-title" }, "设备状态概览"),
        vue.createElementVNode("view", { class: "stats-grid" }, [
          vue.createElementVNode("view", { class: "stat-card" }, [
            vue.createElementVNode(
              "text",
              { class: "stat-number" },
              vue.toDisplayString($setup.stats.total),
              1
              /* TEXT */
            ),
            vue.createElementVNode("text", { class: "stat-label" }, "总设备")
          ]),
          vue.createElementVNode("view", { class: "stat-card normal" }, [
            vue.createElementVNode(
              "text",
              { class: "stat-number" },
              vue.toDisplayString($setup.stats.normal),
              1
              /* TEXT */
            ),
            vue.createElementVNode("text", { class: "stat-label" }, "正常")
          ]),
          vue.createElementVNode("view", { class: "stat-card warning" }, [
            vue.createElementVNode(
              "text",
              { class: "stat-number" },
              vue.toDisplayString($setup.stats.warning),
              1
              /* TEXT */
            ),
            vue.createElementVNode("text", { class: "stat-label" }, "警告")
          ]),
          vue.createElementVNode("view", { class: "stat-card error" }, [
            vue.createElementVNode(
              "text",
              { class: "stat-number" },
              vue.toDisplayString($setup.stats.error),
              1
              /* TEXT */
            ),
            vue.createElementVNode("text", { class: "stat-label" }, "故障")
          ])
        ])
      ]),
      vue.createCommentVNode(" 机器列表 "),
      vue.createElementVNode("view", { class: "machine-section" }, [
        vue.createElementVNode("view", { class: "section-header" }, [
          vue.createElementVNode("text", { class: "section-title" }, "设备列表"),
          vue.createElementVNode(
            "text",
            {
              class: "refresh-btn",
              onClick: $setup.loadMachineList
            },
            vue.toDisplayString($setup.isLoadingMachines ? "加载中..." : "刷新"),
            1
            /* TEXT */
          )
        ]),
        $setup.machineList.length > 0 ? (vue.openBlock(), vue.createElementBlock("view", {
          key: 0,
          class: "machine-list"
        }, [
          (vue.openBlock(true), vue.createElementBlock(
            vue.Fragment,
            null,
            vue.renderList($setup.machineList, (machine) => {
              return vue.openBlock(), vue.createElementBlock("view", {
                class: "machine-card",
                key: machine.id,
                onClick: ($event) => $setup.viewMachineDetail(machine)
              }, [
                vue.createElementVNode("view", { class: "machine-header" }, [
                  vue.createElementVNode("view", { class: "machine-info" }, [
                    vue.createElementVNode(
                      "text",
                      { class: "machine-name" },
                      vue.toDisplayString(machine.name),
                      1
                      /* TEXT */
                    ),
                    vue.createElementVNode(
                      "text",
                      { class: "machine-location" },
                      vue.toDisplayString(machine.location),
                      1
                      /* TEXT */
                    )
                  ]),
                  vue.createElementVNode(
                    "view",
                    {
                      class: "status-badge",
                      style: vue.normalizeStyle({
                        backgroundColor: $setup.getStatusInfo(machine.status).bgColor,
                        color: $setup.getStatusInfo(machine.status).color
                      })
                    },
                    [
                      vue.createElementVNode(
                        "text",
                        { class: "status-text" },
                        vue.toDisplayString($setup.getStatusInfo(machine.status).text),
                        1
                        /* TEXT */
                      )
                    ],
                    4
                    /* STYLE */
                  )
                ]),
                vue.createElementVNode("view", { class: "machine-metrics" }, [
                  vue.createElementVNode("view", { class: "metric-item" }, [
                    vue.createElementVNode("text", { class: "metric-label" }, "编号"),
                    vue.createElementVNode(
                      "text",
                      { class: "metric-value" },
                      vue.toDisplayString(machine.code),
                      1
                      /* TEXT */
                    )
                  ]),
                  vue.createElementVNode("view", { class: "metric-item" }, [
                    vue.createElementVNode("text", { class: "metric-label" }, "异常数量"),
                    vue.createElementVNode(
                      "text",
                      { class: "metric-value" },
                      vue.toDisplayString(machine.anomaly_count) + "个",
                      1
                      /* TEXT */
                    )
                  ]),
                  vue.createElementVNode("view", { class: "metric-item" }, [
                    vue.createElementVNode("text", { class: "metric-label" }, "状态"),
                    vue.createElementVNode(
                      "text",
                      { class: "metric-value" },
                      vue.toDisplayString($setup.getStatusInfo(machine.status).text),
                      1
                      /* TEXT */
                    )
                  ])
                ]),
                machine.status === "异常" && machine.anomaly_count > 0 ? (vue.openBlock(), vue.createElementBlock("view", {
                  key: 0,
                  class: "machine-actions"
                }, [
                  vue.createElementVNode("button", {
                    class: "repair-btn",
                    onClick: vue.withModifiers(($event) => $setup.startRepair(machine), ["stop"])
                  }, " 开始维修 ", 8, ["onClick"])
                ])) : vue.createCommentVNode("v-if", true)
              ], 8, ["onClick"]);
            }),
            128
            /* KEYED_FRAGMENT */
          ))
        ])) : !$setup.isLoadingMachines ? (vue.openBlock(), vue.createElementBlock("view", {
          key: 1,
          class: "empty-state"
        }, [
          vue.createElementVNode("text", { class: "empty-text" }, "暂无设备数据"),
          vue.createElementVNode("text", { class: "empty-desc" }, "请检查网络连接或联系管理员")
        ])) : vue.createCommentVNode("v-if", true),
        $setup.isLoadingMachines ? (vue.openBlock(), vue.createElementBlock("view", {
          key: 2,
          class: "loading-state"
        }, [
          vue.createElementVNode("text", { class: "loading-text" }, "加载设备列表中...")
        ])) : vue.createCommentVNode("v-if", true)
      ])
    ]);
  }
  const PagesMechanicHome = /* @__PURE__ */ _export_sfc(_sfc_main$4, [["render", _sfc_render$3], ["__file", "D:/learn/uniapptest/testvue3/src/pages/mechanic/home.vue"]]);
  const _sfc_main$3 = /* @__PURE__ */ vue.defineComponent({
    __name: "report",
    setup(__props, { expose: __expose }) {
      __expose();
      const machineInfo = vue.ref({});
      const reportForm = vue.reactive({
        machineCode: "",
        description: "",
        severity: "medium",
        // low: 轻微, medium: 中等, high: 严重, critical: 紧急
        images: [],
        remarks: ""
      });
      const severityOptions = [
        { value: "low", label: "轻微", color: "#4CAF50" },
        { value: "medium", label: "中等", color: "#FF9800" },
        { value: "high", label: "严重", color: "#F44336" },
        { value: "critical", label: "紧急", color: "#9C27B0" }
      ];
      const isSubmitting = vue.ref(false);
      const mockMachineData = {
        "M001": {
          code: "M001",
          name: "织机A-01",
          location: "A区-1号位",
          model: "TX-2000",
          installDate: "2023-03-15",
          lastMaintenance: "2024-01-10"
        },
        "M002": {
          code: "M002",
          name: "织机B-03",
          location: "B区-3号位",
          model: "TX-2000",
          installDate: "2023-04-20",
          lastMaintenance: "2024-01-12"
        },
        "M003": {
          code: "M003",
          name: "织机C-05",
          location: "C区-5号位",
          model: "TX-3000",
          installDate: "2023-05-10",
          lastMaintenance: "2024-01-14"
        }
      };
      const getSeverityInfo = (severity) => {
        return severityOptions.find((item) => item.value === severity) || severityOptions[1];
      };
      const onSeverityChange = (e) => {
        reportForm.severity = e.detail.value;
      };
      const chooseImage = () => {
        uni.chooseImage({
          count: 3,
          sizeType: ["compressed"],
          sourceType: ["camera", "album"],
          success: (res) => {
            reportForm.images = [...reportForm.images, ...res.tempFilePaths].slice(0, 3);
          },
          fail: (err) => {
            formatAppLog("error", "at pages/report/report.vue:75", "选择图片失败:", err);
            uni.showToast({
              title: "选择图片失败",
              icon: "none"
            });
          }
        });
      };
      const removeImage = (index) => {
        reportForm.images.splice(index, 1);
      };
      const previewImage = (index) => {
        uni.previewImage({
          urls: reportForm.images,
          current: index
        });
      };
      const submitReport = async () => {
        var _a;
        if (!reportForm.description.trim()) {
          uni.showToast({
            title: "请填写异常描述",
            icon: "none"
          });
          return;
        }
        isSubmitting.value = true;
        try {
          await new Promise((resolve) => setTimeout(resolve, 2e3));
          const reportData = {
            machineCode: reportForm.machineCode,
            machineName: machineInfo.value.name,
            description: reportForm.description,
            severity: reportForm.severity,
            images: reportForm.images,
            remarks: reportForm.remarks,
            reportTime: (/* @__PURE__ */ new Date()).toLocaleString(),
            reporter: ((_a = uni.getStorageSync("userInfo")) == null ? void 0 : _a.username) || "未知用户"
          };
          formatAppLog("log", "at pages/report/report.vue:125", "上报数据:", reportData);
          uni.showToast({
            title: "上报成功",
            icon: "success"
          });
          setTimeout(() => {
            uni.navigateBack();
          }, 1500);
        } catch (error) {
          formatAppLog("error", "at pages/report/report.vue:138", "上报失败:", error);
          uni.showToast({
            title: "上报失败，请重试",
            icon: "none"
          });
        } finally {
          isSubmitting.value = false;
        }
      };
      vue.onLoad((options) => {
        if (options.machineCode) {
          reportForm.machineCode = options.machineCode;
          const machine = mockMachineData[options.machineCode];
          if (machine) {
            machineInfo.value = machine;
          } else {
            machineInfo.value = {
              code: options.machineCode,
              name: `机器-${options.machineCode}`,
              location: "未知位置",
              model: "未知型号"
            };
          }
        } else {
          uni.showToast({
            title: "缺少机器信息",
            icon: "none"
          });
          setTimeout(() => {
            uni.navigateBack();
          }, 1500);
        }
      });
      const __returned__ = { machineInfo, reportForm, severityOptions, isSubmitting, mockMachineData, getSeverityInfo, onSeverityChange, chooseImage, removeImage, previewImage, submitReport };
      Object.defineProperty(__returned__, "__isScriptSetup", { enumerable: false, value: true });
      return __returned__;
    }
  });
  function _sfc_render$2(_ctx, _cache, $props, $setup, $data, $options) {
    return vue.openBlock(), vue.createElementBlock("view", { class: "report-page" }, [
      vue.createCommentVNode(" 机器信息卡片 "),
      vue.createElementVNode("view", { class: "machine-card" }, [
        vue.createElementVNode("view", { class: "card-header" }, [
          vue.createElementVNode("text", { class: "card-title" }, "机器信息"),
          vue.createElementVNode("view", { class: "machine-code" }, [
            vue.createElementVNode(
              "text",
              { class: "code-text" },
              vue.toDisplayString($setup.machineInfo.code),
              1
              /* TEXT */
            )
          ])
        ]),
        vue.createElementVNode("view", { class: "machine-details" }, [
          vue.createElementVNode("view", { class: "detail-row" }, [
            vue.createElementVNode("text", { class: "detail-label" }, "设备名称："),
            vue.createElementVNode(
              "text",
              { class: "detail-value" },
              vue.toDisplayString($setup.machineInfo.name),
              1
              /* TEXT */
            )
          ]),
          vue.createElementVNode("view", { class: "detail-row" }, [
            vue.createElementVNode("text", { class: "detail-label" }, "设备位置："),
            vue.createElementVNode(
              "text",
              { class: "detail-value" },
              vue.toDisplayString($setup.machineInfo.location),
              1
              /* TEXT */
            )
          ]),
          $setup.machineInfo.model ? (vue.openBlock(), vue.createElementBlock("view", {
            key: 0,
            class: "detail-row"
          }, [
            vue.createElementVNode("text", { class: "detail-label" }, "设备型号："),
            vue.createElementVNode(
              "text",
              { class: "detail-value" },
              vue.toDisplayString($setup.machineInfo.model),
              1
              /* TEXT */
            )
          ])) : vue.createCommentVNode("v-if", true),
          $setup.machineInfo.lastMaintenance ? (vue.openBlock(), vue.createElementBlock("view", {
            key: 1,
            class: "detail-row"
          }, [
            vue.createElementVNode("text", { class: "detail-label" }, "上次维护："),
            vue.createElementVNode(
              "text",
              { class: "detail-value" },
              vue.toDisplayString($setup.machineInfo.lastMaintenance),
              1
              /* TEXT */
            )
          ])) : vue.createCommentVNode("v-if", true)
        ])
      ]),
      vue.createCommentVNode(" 异常上报表单 "),
      vue.createElementVNode("view", { class: "form-container" }, [
        vue.createElementVNode("view", { class: "form-section" }, [
          vue.createElementVNode("text", { class: "section-title" }, "异常描述 *"),
          vue.withDirectives(vue.createElementVNode(
            "textarea",
            {
              class: "description-input",
              "onUpdate:modelValue": _cache[0] || (_cache[0] = ($event) => $setup.reportForm.description = $event),
              placeholder: "请详细描述机器异常现象，如异响、温度异常、运行不稳定等",
              "placeholder-class": "placeholder",
              maxlength: "500",
              "show-confirm-bar": "false"
            },
            null,
            512
            /* NEED_PATCH */
          ), [
            [vue.vModelText, $setup.reportForm.description]
          ]),
          vue.createElementVNode(
            "text",
            { class: "char-count" },
            vue.toDisplayString($setup.reportForm.description.length) + "/500",
            1
            /* TEXT */
          )
        ]),
        vue.createElementVNode("view", { class: "form-section" }, [
          vue.createElementVNode("text", { class: "section-title" }, "严重程度"),
          vue.createElementVNode("picker", {
            class: "severity-picker",
            value: $setup.reportForm.severity,
            range: $setup.severityOptions,
            "range-key": "label",
            onChange: $setup.onSeverityChange
          }, [
            vue.createElementVNode("view", { class: "picker-content" }, [
              vue.createElementVNode(
                "view",
                {
                  class: "severity-tag",
                  style: vue.normalizeStyle({ backgroundColor: $setup.getSeverityInfo($setup.reportForm.severity).color })
                },
                [
                  vue.createElementVNode(
                    "text",
                    { class: "severity-text" },
                    vue.toDisplayString($setup.getSeverityInfo($setup.reportForm.severity).label),
                    1
                    /* TEXT */
                  )
                ],
                4
                /* STYLE */
              ),
              vue.createElementVNode("text", { class: "picker-arrow" }, "▼")
            ])
          ], 40, ["value"])
        ]),
        vue.createElementVNode("view", { class: "form-section" }, [
          vue.createElementVNode("text", { class: "section-title" }, "现场照片 (最多3张)"),
          vue.createElementVNode("view", { class: "image-section" }, [
            vue.createElementVNode("view", { class: "image-list" }, [
              (vue.openBlock(true), vue.createElementBlock(
                vue.Fragment,
                null,
                vue.renderList($setup.reportForm.images, (image, index) => {
                  return vue.openBlock(), vue.createElementBlock("view", {
                    class: "image-item",
                    key: index,
                    onClick: ($event) => $setup.previewImage(index)
                  }, [
                    vue.createElementVNode("image", {
                      class: "image",
                      src: image,
                      mode: "aspectFill"
                    }, null, 8, ["src"]),
                    vue.createElementVNode("view", {
                      class: "image-remove",
                      onClick: vue.withModifiers(($event) => $setup.removeImage(index), ["stop"])
                    }, [
                      vue.createElementVNode("text", { class: "remove-icon" }, "×")
                    ], 8, ["onClick"])
                  ], 8, ["onClick"]);
                }),
                128
                /* KEYED_FRAGMENT */
              )),
              $setup.reportForm.images.length < 3 ? (vue.openBlock(), vue.createElementBlock("view", {
                key: 0,
                class: "image-add",
                onClick: $setup.chooseImage
              }, [
                vue.createElementVNode("text", { class: "add-icon" }, "+"),
                vue.createElementVNode("text", { class: "add-text" }, "添加照片")
              ])) : vue.createCommentVNode("v-if", true)
            ])
          ])
        ]),
        vue.createElementVNode("view", { class: "form-section" }, [
          vue.createElementVNode("text", { class: "section-title" }, "备注信息"),
          vue.withDirectives(vue.createElementVNode(
            "textarea",
            {
              class: "remarks-input",
              "onUpdate:modelValue": _cache[1] || (_cache[1] = ($event) => $setup.reportForm.remarks = $event),
              placeholder: "其他需要说明的信息（选填）",
              "placeholder-class": "placeholder",
              maxlength: "200",
              "show-confirm-bar": "false"
            },
            null,
            512
            /* NEED_PATCH */
          ), [
            [vue.vModelText, $setup.reportForm.remarks]
          ]),
          vue.createElementVNode(
            "text",
            { class: "char-count" },
            vue.toDisplayString($setup.reportForm.remarks.length) + "/200",
            1
            /* TEXT */
          )
        ])
      ]),
      vue.createCommentVNode(" 提交按钮 "),
      vue.createElementVNode("view", { class: "submit-section" }, [
        vue.createElementVNode("button", {
          class: vue.normalizeClass(["submit-btn", { "submitting": $setup.isSubmitting }]),
          onClick: $setup.submitReport,
          disabled: $setup.isSubmitting
        }, vue.toDisplayString($setup.isSubmitting ? "提交中..." : "提交异常上报"), 11, ["disabled"])
      ])
    ]);
  }
  const PagesReportReport = /* @__PURE__ */ _export_sfc(_sfc_main$3, [["render", _sfc_render$2], ["__file", "D:/learn/uniapptest/testvue3/src/pages/report/report.vue"]]);
  const _sfc_main$2 = /* @__PURE__ */ vue.defineComponent({
    __name: "repair",
    setup(__props, { expose: __expose }) {
      __expose();
      const machineInfo = vue.ref({});
      const errorInfo = vue.ref({});
      const repairForm = vue.reactive({
        startTime: "",
        endTime: "",
        repairProcess: "",
        replacedParts: "",
        repairResult: "success",
        // success: 维修成功, failed: 维修失败, pending: 需要配件
        testResult: "",
        remarks: "",
        images: []
      });
      const repairStatus = vue.ref("not_started");
      const isSubmitting = vue.ref(false);
      const repairResultOptions = [
        { value: "success", label: "维修成功", color: "#4CAF50" },
        { value: "failed", label: "维修失败", color: "#F44336" },
        { value: "pending", label: "需要配件", color: "#FF9800" }
      ];
      const mockMachineData = {
        "M001": {
          code: "M001",
          name: "织机A-01",
          location: "A区-1号位",
          model: "TX-2000",
          installDate: "2023-03-15",
          lastMaintenance: "2024-01-10"
        },
        "M002": {
          code: "M002",
          name: "织机B-03",
          location: "B区-3号位",
          model: "TX-2000",
          installDate: "2023-04-20",
          lastMaintenance: "2024-01-12"
        },
        "M003": {
          code: "M003",
          name: "织机C-05",
          location: "C区-5号位",
          model: "TX-3000",
          installDate: "2023-05-10",
          lastMaintenance: "2024-01-14"
        }
      };
      const mockErrorData = {
        "M001": {
          id: 1,
          description: "机器异响，疑似轴承问题",
          severity: "high",
          reportTime: "2024-01-15 14:30",
          reporter: "张三",
          images: ["/static/error1.jpg"]
        },
        "M003": {
          id: 3,
          description: "温度过高，需要检查冷却系统",
          severity: "critical",
          reportTime: "2024-01-14 16:45",
          reporter: "李四",
          images: []
        }
      };
      const getSeverityInfo = (severity) => {
        const severityMap = {
          low: { text: "轻微", color: "#4CAF50" },
          medium: { text: "中等", color: "#FF9800" },
          high: { text: "严重", color: "#F44336" },
          critical: { text: "紧急", color: "#9C27B0" }
        };
        return severityMap[severity] || { text: "未知", color: "#999999" };
      };
      const getRepairResultInfo = (result) => {
        return repairResultOptions.find((item) => item.value === result) || repairResultOptions[0];
      };
      const startRepair = () => {
        const now = /* @__PURE__ */ new Date();
        repairForm.startTime = now.toLocaleString();
        repairStatus.value = "in_progress";
        uni.showToast({
          title: "开始维修",
          icon: "success"
        });
      };
      const onRepairResultChange = (e) => {
        repairForm.repairResult = e.detail.value;
      };
      const chooseImage = () => {
        uni.chooseImage({
          count: 5,
          sizeType: ["compressed"],
          sourceType: ["camera", "album"],
          success: (res) => {
            repairForm.images = [...repairForm.images, ...res.tempFilePaths].slice(0, 5);
          },
          fail: (err) => {
            formatAppLog("error", "at pages/repair/repair.vue:124", "选择图片失败:", err);
            uni.showToast({
              title: "选择图片失败",
              icon: "none"
            });
          }
        });
      };
      const removeImage = (index) => {
        repairForm.images.splice(index, 1);
      };
      const previewImage = (index) => {
        uni.previewImage({
          urls: repairForm.images,
          current: index
        });
      };
      const completeRepair = async () => {
        var _a;
        if (!repairForm.repairProcess.trim()) {
          uni.showToast({
            title: "请填写维修过程",
            icon: "none"
          });
          return;
        }
        isSubmitting.value = true;
        try {
          const now = /* @__PURE__ */ new Date();
          repairForm.endTime = now.toLocaleString();
          await new Promise((resolve) => setTimeout(resolve, 2e3));
          const repairData = {
            machineCode: machineInfo.value.code,
            machineName: machineInfo.value.name,
            errorId: errorInfo.value.id,
            startTime: repairForm.startTime,
            endTime: repairForm.endTime,
            repairProcess: repairForm.repairProcess,
            replacedParts: repairForm.replacedParts,
            repairResult: repairForm.repairResult,
            testResult: repairForm.testResult,
            remarks: repairForm.remarks,
            images: repairForm.images,
            mechanic: ((_a = uni.getStorageSync("userInfo")) == null ? void 0 : _a.username) || "未知维修工"
          };
          formatAppLog("log", "at pages/repair/repair.vue:182", "维修数据:", repairData);
          repairStatus.value = "completed";
          uni.showToast({
            title: "维修完成",
            icon: "success"
          });
          setTimeout(() => {
            uni.navigateBack();
          }, 1500);
        } catch (error) {
          formatAppLog("error", "at pages/repair/repair.vue:197", "提交失败:", error);
          uni.showToast({
            title: "提交失败，请重试",
            icon: "none"
          });
        } finally {
          isSubmitting.value = false;
        }
      };
      vue.onLoad((options) => {
        if (options.machineCode) {
          const machine = mockMachineData[options.machineCode];
          if (machine) {
            machineInfo.value = machine;
          } else {
            machineInfo.value = {
              code: options.machineCode,
              name: `机器-${options.machineCode}`,
              location: "未知位置",
              model: "未知型号"
            };
          }
          const error = mockErrorData[options.machineCode];
          if (error) {
            errorInfo.value = error;
          } else {
            errorInfo.value = {
              id: 0,
              description: "暂无异常描述",
              severity: "medium",
              reportTime: "未知时间",
              reporter: "未知上报人",
              images: []
            };
          }
        } else {
          uni.showToast({
            title: "缺少机器信息",
            icon: "none"
          });
          setTimeout(() => {
            uni.navigateBack();
          }, 1500);
        }
      });
      const __returned__ = { machineInfo, errorInfo, repairForm, repairStatus, isSubmitting, repairResultOptions, mockMachineData, mockErrorData, getSeverityInfo, getRepairResultInfo, startRepair, onRepairResultChange, chooseImage, removeImage, previewImage, completeRepair };
      Object.defineProperty(__returned__, "__isScriptSetup", { enumerable: false, value: true });
      return __returned__;
    }
  });
  function _sfc_render$1(_ctx, _cache, $props, $setup, $data, $options) {
    return vue.openBlock(), vue.createElementBlock("view", { class: "repair-page" }, [
      vue.createCommentVNode(" 机器信息卡片 "),
      vue.createElementVNode("view", { class: "machine-card" }, [
        vue.createElementVNode("view", { class: "card-header" }, [
          vue.createElementVNode("text", { class: "card-title" }, "设备信息"),
          vue.createElementVNode("view", { class: "machine-code" }, [
            vue.createElementVNode(
              "text",
              { class: "code-text" },
              vue.toDisplayString($setup.machineInfo.code),
              1
              /* TEXT */
            )
          ])
        ]),
        vue.createElementVNode("view", { class: "machine-details" }, [
          vue.createElementVNode("view", { class: "detail-row" }, [
            vue.createElementVNode("text", { class: "detail-label" }, "设备名称："),
            vue.createElementVNode(
              "text",
              { class: "detail-value" },
              vue.toDisplayString($setup.machineInfo.name),
              1
              /* TEXT */
            )
          ]),
          vue.createElementVNode("view", { class: "detail-row" }, [
            vue.createElementVNode("text", { class: "detail-label" }, "设备位置："),
            vue.createElementVNode(
              "text",
              { class: "detail-value" },
              vue.toDisplayString($setup.machineInfo.location),
              1
              /* TEXT */
            )
          ]),
          $setup.machineInfo.model ? (vue.openBlock(), vue.createElementBlock("view", {
            key: 0,
            class: "detail-row"
          }, [
            vue.createElementVNode("text", { class: "detail-label" }, "设备型号："),
            vue.createElementVNode(
              "text",
              { class: "detail-value" },
              vue.toDisplayString($setup.machineInfo.model),
              1
              /* TEXT */
            )
          ])) : vue.createCommentVNode("v-if", true)
        ])
      ]),
      vue.createCommentVNode(" 异常信息卡片 "),
      vue.createElementVNode("view", { class: "error-card" }, [
        vue.createElementVNode("view", { class: "card-header" }, [
          vue.createElementVNode("text", { class: "card-title" }, "异常信息"),
          vue.createElementVNode(
            "view",
            {
              class: "severity-badge",
              style: vue.normalizeStyle({ backgroundColor: $setup.getSeverityInfo($setup.errorInfo.severity).color })
            },
            [
              vue.createElementVNode(
                "text",
                { class: "severity-text" },
                vue.toDisplayString($setup.getSeverityInfo($setup.errorInfo.severity).text),
                1
                /* TEXT */
              )
            ],
            4
            /* STYLE */
          )
        ]),
        vue.createElementVNode("view", { class: "error-details" }, [
          vue.createElementVNode("view", { class: "error-desc" }, [
            vue.createElementVNode(
              "text",
              { class: "desc-text" },
              vue.toDisplayString($setup.errorInfo.description),
              1
              /* TEXT */
            )
          ]),
          vue.createElementVNode("view", { class: "error-meta" }, [
            vue.createElementVNode(
              "text",
              { class: "meta-item" },
              "上报时间：" + vue.toDisplayString($setup.errorInfo.reportTime),
              1
              /* TEXT */
            ),
            vue.createElementVNode(
              "text",
              { class: "meta-item" },
              "上报人：" + vue.toDisplayString($setup.errorInfo.reporter),
              1
              /* TEXT */
            )
          ])
        ])
      ]),
      vue.createCommentVNode(" 维修状态 "),
      vue.createElementVNode("view", { class: "status-card" }, [
        vue.createElementVNode("view", { class: "status-header" }, [
          vue.createElementVNode("text", { class: "status-title" }, "维修状态"),
          vue.createElementVNode(
            "view",
            {
              class: vue.normalizeClass(["status-badge", $setup.repairStatus])
            },
            [
              vue.createElementVNode(
                "text",
                { class: "status-text" },
                vue.toDisplayString($setup.repairStatus === "not_started" ? "未开始" : $setup.repairStatus === "in_progress" ? "进行中" : "已完成"),
                1
                /* TEXT */
              )
            ],
            2
            /* CLASS */
          )
        ]),
        $setup.repairForm.startTime ? (vue.openBlock(), vue.createElementBlock("view", {
          key: 0,
          class: "time-info"
        }, [
          vue.createElementVNode(
            "text",
            { class: "time-item" },
            "开始时间：" + vue.toDisplayString($setup.repairForm.startTime),
            1
            /* TEXT */
          ),
          $setup.repairForm.endTime ? (vue.openBlock(), vue.createElementBlock(
            "text",
            {
              key: 0,
              class: "time-item"
            },
            "结束时间：" + vue.toDisplayString($setup.repairForm.endTime),
            1
            /* TEXT */
          )) : vue.createCommentVNode("v-if", true)
        ])) : vue.createCommentVNode("v-if", true),
        $setup.repairStatus === "not_started" ? (vue.openBlock(), vue.createElementBlock("view", {
          key: 1,
          class: "action-section"
        }, [
          vue.createElementVNode("button", {
            class: "start-btn",
            onClick: $setup.startRepair
          }, " 开始维修 ")
        ])) : vue.createCommentVNode("v-if", true)
      ]),
      vue.createCommentVNode(" 维修表单 "),
      $setup.repairStatus !== "not_started" ? (vue.openBlock(), vue.createElementBlock("view", {
        key: 0,
        class: "form-container"
      }, [
        vue.createElementVNode("view", { class: "form-section" }, [
          vue.createElementVNode("text", { class: "section-title" }, "维修过程 *"),
          vue.withDirectives(vue.createElementVNode("textarea", {
            class: "process-input",
            "onUpdate:modelValue": _cache[0] || (_cache[0] = ($event) => $setup.repairForm.repairProcess = $event),
            placeholder: "请详细记录维修过程，包括故障原因分析、维修步骤等",
            "placeholder-class": "placeholder",
            maxlength: "1000",
            "show-confirm-bar": "false",
            disabled: $setup.repairStatus === "completed"
          }, null, 8, ["disabled"]), [
            [vue.vModelText, $setup.repairForm.repairProcess]
          ]),
          vue.createElementVNode(
            "text",
            { class: "char-count" },
            vue.toDisplayString($setup.repairForm.repairProcess.length) + "/1000",
            1
            /* TEXT */
          )
        ]),
        vue.createElementVNode("view", { class: "form-section" }, [
          vue.createElementVNode("text", { class: "section-title" }, "更换配件"),
          vue.withDirectives(vue.createElementVNode("textarea", {
            class: "parts-input",
            "onUpdate:modelValue": _cache[1] || (_cache[1] = ($event) => $setup.repairForm.replacedParts = $event),
            placeholder: "记录更换的配件名称、型号、数量等（如无更换可不填）",
            "placeholder-class": "placeholder",
            maxlength: "500",
            "show-confirm-bar": "false",
            disabled: $setup.repairStatus === "completed"
          }, null, 8, ["disabled"]), [
            [vue.vModelText, $setup.repairForm.replacedParts]
          ]),
          vue.createElementVNode(
            "text",
            { class: "char-count" },
            vue.toDisplayString($setup.repairForm.replacedParts.length) + "/500",
            1
            /* TEXT */
          )
        ]),
        vue.createElementVNode("view", { class: "form-section" }, [
          vue.createElementVNode("text", { class: "section-title" }, "维修结果"),
          vue.createElementVNode("picker", {
            class: "result-picker",
            value: $setup.repairForm.repairResult,
            range: $setup.repairResultOptions,
            "range-key": "label",
            onChange: $setup.onRepairResultChange,
            disabled: $setup.repairStatus === "completed"
          }, [
            vue.createElementVNode("view", { class: "picker-content" }, [
              vue.createElementVNode(
                "view",
                {
                  class: "result-tag",
                  style: vue.normalizeStyle({ backgroundColor: $setup.getRepairResultInfo($setup.repairForm.repairResult).color })
                },
                [
                  vue.createElementVNode(
                    "text",
                    { class: "result-text" },
                    vue.toDisplayString($setup.getRepairResultInfo($setup.repairForm.repairResult).label),
                    1
                    /* TEXT */
                  )
                ],
                4
                /* STYLE */
              ),
              $setup.repairStatus !== "completed" ? (vue.openBlock(), vue.createElementBlock("text", {
                key: 0,
                class: "picker-arrow"
              }, "▼")) : vue.createCommentVNode("v-if", true)
            ])
          ], 40, ["value", "disabled"])
        ]),
        vue.createElementVNode("view", { class: "form-section" }, [
          vue.createElementVNode("text", { class: "section-title" }, "测试结果"),
          vue.withDirectives(vue.createElementVNode("textarea", {
            class: "test-input",
            "onUpdate:modelValue": _cache[2] || (_cache[2] = ($event) => $setup.repairForm.testResult = $event),
            placeholder: "记录维修后的测试情况，如运行状态、性能指标等",
            "placeholder-class": "placeholder",
            maxlength: "500",
            "show-confirm-bar": "false",
            disabled: $setup.repairStatus === "completed"
          }, null, 8, ["disabled"]), [
            [vue.vModelText, $setup.repairForm.testResult]
          ]),
          vue.createElementVNode(
            "text",
            { class: "char-count" },
            vue.toDisplayString($setup.repairForm.testResult.length) + "/500",
            1
            /* TEXT */
          )
        ]),
        vue.createElementVNode("view", { class: "form-section" }, [
          vue.createElementVNode("text", { class: "section-title" }, "维修照片 (最多5张)"),
          vue.createElementVNode("view", { class: "image-section" }, [
            vue.createElementVNode("view", { class: "image-list" }, [
              (vue.openBlock(true), vue.createElementBlock(
                vue.Fragment,
                null,
                vue.renderList($setup.repairForm.images, (image, index) => {
                  return vue.openBlock(), vue.createElementBlock("view", {
                    class: "image-item",
                    key: index,
                    onClick: ($event) => $setup.previewImage(index)
                  }, [
                    vue.createElementVNode("image", {
                      class: "image",
                      src: image,
                      mode: "aspectFill"
                    }, null, 8, ["src"]),
                    $setup.repairStatus !== "completed" ? (vue.openBlock(), vue.createElementBlock("view", {
                      key: 0,
                      class: "image-remove",
                      onClick: vue.withModifiers(($event) => $setup.removeImage(index), ["stop"])
                    }, [
                      vue.createElementVNode("text", { class: "remove-icon" }, "×")
                    ], 8, ["onClick"])) : vue.createCommentVNode("v-if", true)
                  ], 8, ["onClick"]);
                }),
                128
                /* KEYED_FRAGMENT */
              )),
              $setup.repairForm.images.length < 5 && $setup.repairStatus !== "completed" ? (vue.openBlock(), vue.createElementBlock("view", {
                key: 0,
                class: "image-add",
                onClick: $setup.chooseImage
              }, [
                vue.createElementVNode("text", { class: "add-icon" }, "+"),
                vue.createElementVNode("text", { class: "add-text" }, "添加照片")
              ])) : vue.createCommentVNode("v-if", true)
            ])
          ])
        ]),
        vue.createElementVNode("view", { class: "form-section" }, [
          vue.createElementVNode("text", { class: "section-title" }, "备注信息"),
          vue.withDirectives(vue.createElementVNode("textarea", {
            class: "remarks-input",
            "onUpdate:modelValue": _cache[3] || (_cache[3] = ($event) => $setup.repairForm.remarks = $event),
            placeholder: "其他需要说明的信息（选填）",
            "placeholder-class": "placeholder",
            maxlength: "300",
            "show-confirm-bar": "false",
            disabled: $setup.repairStatus === "completed"
          }, null, 8, ["disabled"]), [
            [vue.vModelText, $setup.repairForm.remarks]
          ]),
          vue.createElementVNode(
            "text",
            { class: "char-count" },
            vue.toDisplayString($setup.repairForm.remarks.length) + "/300",
            1
            /* TEXT */
          )
        ])
      ])) : vue.createCommentVNode("v-if", true),
      vue.createCommentVNode(" 完成按钮 "),
      $setup.repairStatus === "in_progress" ? (vue.openBlock(), vue.createElementBlock("view", {
        key: 1,
        class: "submit-section"
      }, [
        vue.createElementVNode("button", {
          class: vue.normalizeClass(["complete-btn", { "submitting": $setup.isSubmitting }]),
          onClick: $setup.completeRepair,
          disabled: $setup.isSubmitting
        }, vue.toDisplayString($setup.isSubmitting ? "提交中..." : "完成维修"), 11, ["disabled"])
      ])) : vue.createCommentVNode("v-if", true)
    ]);
  }
  const PagesRepairRepair = /* @__PURE__ */ _export_sfc(_sfc_main$2, [["render", _sfc_render$1], ["__file", "D:/learn/uniapptest/testvue3/src/pages/repair/repair.vue"]]);
  class SunmiConfig {
    /**
     * 检查是否为商米设备
     */
    static isSunmiDevice() {
      if (uni.getSystemInfoSync().platform !== "android") {
        return false;
      }
      try {
        const systemInfo = uni.getSystemInfoSync();
        if (systemInfo.brand && systemInfo.brand.toLowerCase().includes("sunmi")) {
          return true;
        }
        const main = plus.android.runtimeMainActivity();
        const packageManager = main.getPackageManager();
        const PackageManager = plus.android.importClass("android.content.pm.PackageManager");
        try {
          const packageInfo = packageManager.getPackageInfo(this.SERVICE_PACKAGE, 0);
          return packageInfo != null;
        } catch (packageError) {
          formatAppLog("log", "at utils/sunmiConfig.js:64", "商米扫码服务包不存在:", packageError);
          return false;
        }
      } catch (error) {
        formatAppLog("log", "at utils/sunmiConfig.js:68", "检查商米设备失败:", error);
        return false;
      }
    }
    /**
     * 获取设备信息
     */
    static getDeviceInfo() {
      try {
        const systemInfo = uni.getSystemInfoSync();
        return {
          platform: systemInfo.platform,
          brand: systemInfo.brand,
          model: systemInfo.model,
          system: systemInfo.system
        };
      } catch (error) {
        formatAppLog("error", "at utils/sunmiConfig.js:86", "获取设备信息失败:", error);
        return null;
      }
    }
    /**
     * 配置扫码输出方式
     * 根据商米文档，设置为广播输出模式
     */
    static async configureOutputMode() {
      try {
        formatAppLog("log", "at utils/sunmiConfig.js:99", "商米扫码头输出配置提示：");
        formatAppLog("log", "at utils/sunmiConfig.js:100", "1. 打开设备设置 -> 扫码头设置");
        formatAppLog("log", "at utils/sunmiConfig.js:101", '2. 确保"广播输出"已开启');
        formatAppLog("log", "at utils/sunmiConfig.js:102", "3. 确保监听广播为: " + this.BROADCAST_ACTION);
        return {
          success: true,
          message: "请确认设备扫码配置已正确设置"
        };
      } catch (error) {
        formatAppLog("error", "at utils/sunmiConfig.js:109", "配置扫码输出模式失败:", error);
        return {
          success: false,
          error: error.message
        };
      }
    }
    /**
     * 检查扫码头配置
     */
    static async checkScannerConfig() {
      if (!this.isSunmiDevice()) {
        return {
          success: false,
          error: "非商米设备或扫码服务不可用"
        };
      }
      try {
        const deviceInfo = this.getDeviceInfo();
        formatAppLog("log", "at utils/sunmiConfig.js:130", "设备信息:", deviceInfo);
        return {
          success: true,
          deviceInfo,
          message: "商米扫码头检查通过"
        };
      } catch (error) {
        formatAppLog("error", "at utils/sunmiConfig.js:138", "检查扫码头配置失败:", error);
        return {
          success: false,
          error: error.message
        };
      }
    }
  }
  /**
   * 扫码头类型常量
   */
  __publicField(SunmiConfig, "SCANNER_MODELS", {
    NONE: 100,
    P2LITE_V2PRO_P2PRO: 101,
    L2_NEWLAND_EM2096: 102,
    L2_ZEBRA_SE4710: 103,
    L2_HONEYWELL_N3601: 104,
    L2_HONEYWELL_N6603: 105,
    L2_ZEBRA_SE4750: 106,
    L2_ZEBRA_EM1350: 107
  });
  /**
   * 广播动作常量
   */
  __publicField(SunmiConfig, "BROADCAST_ACTION", "com.sunmi.scanner.ACTION_DATA_CODE_RECEIVED");
  /**
   * 扫码服务相关常量
   */
  __publicField(SunmiConfig, "SERVICE_PACKAGE", "com.sunmi.scanner");
  __publicField(SunmiConfig, "SERVICE_ACTION", "com.sunmi.scanner.IScanInterface");
  /**
   * 数据字段常量
   */
  __publicField(SunmiConfig, "DATA_FIELD", "data");
  __publicField(SunmiConfig, "SOURCE_BYTE_FIELD", "source_byte");
  const __default__ = {
    onShow() {
      formatAppLog("log", "at pages/test/scanner.vue:175", "扫码测试页面显示");
      if (typeof scannerUtils !== "undefined") {
        scannerUtils.setPageActive(true);
      }
    },
    onHide() {
      formatAppLog("log", "at pages/test/scanner.vue:181", "扫码测试页面隐藏");
      if (typeof scannerUtils !== "undefined") {
        scannerUtils.setPageActive(false);
      }
    }
  };
  const _sfc_main$1 = /* @__PURE__ */ vue.defineComponent({
    ...__default__,
    __name: "scanner",
    setup(__props, { expose: __expose }) {
      __expose();
      const scanResult = vue.ref("");
      const scanStatus = vue.ref("未初始化");
      const isScanning = vue.ref(false);
      const deviceInfo = vue.ref({});
      const scannerType = vue.ref("未知");
      const handleScanResult = (result) => {
        isScanning.value = false;
        if (result.success) {
          scanResult.value = result.result;
          scanStatus.value = "扫码成功";
          let message = `扫码结果: ${result.result}`;
          if (result.mode) {
            message += `
模式: ${result.mode}`;
          }
          if (result.data) {
            message += `
API数据: ${JSON.stringify(result.data, null, 2)}`;
          }
          if (result.apiError) {
            message += `
API错误: ${result.apiError}`;
          }
          uni.showModal({
            title: "扫码成功",
            content: message,
            showCancel: false
          });
        } else {
          scanStatus.value = `扫码失败: ${result.error}`;
          uni.showToast({
            title: result.error || "扫码失败",
            icon: "none"
          });
        }
      };
      const checkDevice = async () => {
        try {
          const configResult = await SunmiConfig.checkScannerConfig();
          deviceInfo.value = configResult.deviceInfo || {};
          if (configResult.success) {
            scannerType.value = SunmiConfig.isSunmiDevice() ? "商米设备" : "其他设备";
            scanStatus.value = "设备检查通过";
          } else {
            scannerType.value = "不支持的设备";
            scanStatus.value = configResult.error || "设备检查失败";
          }
        } catch (error) {
          formatAppLog("error", "at pages/test/scanner.vue:59", "检查设备失败:", error);
          scanStatus.value = "设备检查异常";
        }
      };
      const startScan = async () => {
        isScanning.value = true;
        scanStatus.value = "正在初始化扫码...";
        try {
          const initResult = await scannerUtils.initScanner(handleScanResult, true, "general");
          scanStatus.value = initResult.message;
          if (initResult.success) {
            uni.showToast({
              title: "请使用扫码枪扫描",
              icon: "none",
              duration: 3e3
            });
          }
        } catch (error) {
          isScanning.value = false;
          scanStatus.value = `初始化失败: ${error.message}`;
        }
      };
      const startReportScan = async () => {
        isScanning.value = true;
        scanStatus.value = "正在初始化上报扫码...";
        try {
          const initResult = await scannerUtils.initScanner(handleScanResult, true, "report");
          scanStatus.value = initResult.message;
          if (initResult.success) {
            uni.showToast({
              title: "上报模式已启用，请扫描机器码",
              icon: "none",
              duration: 3e3
            });
          }
        } catch (error) {
          isScanning.value = false;
          scanStatus.value = `上报模式初始化失败: ${error.message}`;
        }
      };
      const startRepairScan = async () => {
        isScanning.value = true;
        scanStatus.value = "正在初始化维修扫码...";
        try {
          const initResult = await scannerUtils.initScanner(handleScanResult, true, "repair");
          scanStatus.value = initResult.message;
          if (initResult.success) {
            uni.showToast({
              title: "维修模式已启用，请扫描机器码",
              icon: "none",
              duration: 3e3
            });
          }
        } catch (error) {
          isScanning.value = false;
          scanStatus.value = `维修模式初始化失败: ${error.message}`;
        }
      };
      const manualScan = () => {
        scannerUtils.startScan();
      };
      const clearResult = () => {
        scanResult.value = "";
        scanStatus.value = "已清除";
        isScanning.value = false;
      };
      const configureSunmi = async () => {
        try {
          const result = await SunmiConfig.configureOutputMode();
          uni.showModal({
            title: "配置提示",
            content: result.message,
            showCancel: false
          });
        } catch (error) {
          uni.showToast({
            title: "配置失败",
            icon: "none"
          });
        }
      };
      vue.onMounted(async () => {
        scannerUtils.setPageActive(true);
        await checkDevice();
        startScan();
      });
      vue.onUnmounted(() => {
        scannerUtils.destroy();
      });
      const __returned__ = { scanResult, scanStatus, isScanning, deviceInfo, scannerType, handleScanResult, checkDevice, startScan, startReportScan, startRepairScan, manualScan, clearResult, configureSunmi };
      Object.defineProperty(__returned__, "__isScriptSetup", { enumerable: false, value: true });
      return __returned__;
    }
  });
  function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
    return vue.openBlock(), vue.createElementBlock("view", { class: "scanner-test" }, [
      vue.createElementVNode("view", { class: "header" }, [
        vue.createElementVNode("text", { class: "title" }, "商米扫码功能测试")
      ]),
      vue.createCommentVNode(" 设备信息卡片 "),
      vue.createElementVNode("view", { class: "device-card" }, [
        vue.createElementVNode("text", { class: "card-title" }, "设备信息"),
        vue.createElementVNode("view", { class: "device-info" }, [
          vue.createElementVNode(
            "text",
            { class: "info-item" },
            "设备类型：" + vue.toDisplayString($setup.scannerType),
            1
            /* TEXT */
          ),
          vue.createElementVNode(
            "text",
            { class: "info-item" },
            "品牌：" + vue.toDisplayString($setup.deviceInfo.brand || "未知"),
            1
            /* TEXT */
          ),
          vue.createElementVNode(
            "text",
            { class: "info-item" },
            "型号：" + vue.toDisplayString($setup.deviceInfo.model || "未知"),
            1
            /* TEXT */
          ),
          vue.createElementVNode(
            "text",
            { class: "info-item" },
            "系统：" + vue.toDisplayString($setup.deviceInfo.system || "未知"),
            1
            /* TEXT */
          )
        ]),
        vue.createElementVNode("button", {
          class: "config-btn",
          onClick: $setup.configureSunmi
        }, "扫码配置说明")
      ]),
      vue.createCommentVNode(" 状态卡片 "),
      vue.createElementVNode("view", { class: "status-card" }, [
        vue.createElementVNode("text", { class: "status-label" }, "状态："),
        vue.createElementVNode(
          "text",
          {
            class: vue.normalizeClass(["status-text", {
              "success": $setup.scanStatus.includes("成功"),
              "error": $setup.scanStatus.includes("失败") || $setup.scanStatus.includes("异常")
            }])
          },
          vue.toDisplayString($setup.scanStatus),
          3
          /* TEXT, CLASS */
        )
      ]),
      vue.createCommentVNode(" 结果卡片 "),
      $setup.scanResult ? (vue.openBlock(), vue.createElementBlock("view", {
        key: 0,
        class: "result-card"
      }, [
        vue.createElementVNode("text", { class: "result-label" }, "扫码结果："),
        vue.createElementVNode(
          "text",
          { class: "result-text" },
          vue.toDisplayString($setup.scanResult),
          1
          /* TEXT */
        )
      ])) : vue.createCommentVNode("v-if", true),
      vue.createCommentVNode(" 按钮组 "),
      vue.createElementVNode("view", { class: "button-group" }, [
        vue.createElementVNode("button", {
          class: "scan-btn primary",
          onClick: $setup.startScan,
          disabled: $setup.isScanning
        }, vue.toDisplayString($setup.isScanning ? "扫码中..." : "通用扫码测试"), 9, ["disabled"]),
        vue.createElementVNode("button", {
          class: "scan-btn report",
          onClick: $setup.startReportScan,
          disabled: $setup.isScanning
        }, " 上报模式测试 ", 8, ["disabled"]),
        vue.createElementVNode("button", {
          class: "scan-btn repair",
          onClick: $setup.startRepairScan,
          disabled: $setup.isScanning
        }, " 维修模式测试 ", 8, ["disabled"]),
        vue.createElementVNode("button", {
          class: "scan-btn secondary",
          onClick: $setup.manualScan
        }, " 手动扫码(降级测试) "),
        $setup.scanResult ? (vue.openBlock(), vue.createElementBlock("button", {
          key: 0,
          class: "scan-btn danger",
          onClick: $setup.clearResult
        }, " 清除结果 ")) : vue.createCommentVNode("v-if", true)
      ]),
      vue.createCommentVNode(" 使用说明 "),
      vue.createElementVNode("view", { class: "info-card" }, [
        vue.createElementVNode("text", { class: "info-title" }, "使用说明："),
        vue.createElementVNode("text", { class: "info-text" }, "1. 商米PDA设备会优先使用商米扫码头"),
        vue.createElementVNode("text", { class: "info-text" }, "2. 如果商米扫码头不可用，会降级到新大陆扫码头"),
        vue.createElementVNode("text", { class: "info-text" }, "3. 最终降级方案是系统uni.scanCode"),
        vue.createElementVNode("text", { class: "info-text" }, "4. 上报/维修模式会调用后端API验证"),
        vue.createElementVNode("text", { class: "info-text" }, "5. 确保设备扫码配置中已开启广播输出")
      ])
    ]);
  }
  const PagesTestScanner = /* @__PURE__ */ _export_sfc(_sfc_main$1, [["render", _sfc_render], ["__file", "D:/learn/uniapptest/testvue3/src/pages/test/scanner.vue"]]);
  __definePage("pages/login/login", PagesLoginLogin);
  __definePage("pages/workspace/workspace", PagesWorkspaceWorkspace);
  __definePage("pages/profile/profile", PagesProfileProfile);
  __definePage("pages/worker/home", PagesWorkerHome);
  __definePage("pages/mechanic/home", PagesMechanicHome);
  __definePage("pages/report/report", PagesReportReport);
  __definePage("pages/repair/repair", PagesRepairRepair);
  __definePage("pages/test/scanner", PagesTestScanner);
  const _sfc_main = {
    onLaunch: function() {
      formatAppLog("log", "at App.vue:4", "App Launch");
      const userInfo = uni.getStorageSync("userInfo");
      if (!userInfo) {
        uni.reLaunch({
          url: "/pages/login/login"
        });
      }
    },
    onShow: function() {
      formatAppLog("log", "at App.vue:15", "App Show");
    },
    onHide: function() {
      formatAppLog("log", "at App.vue:18", "App Hide");
    }
  };
  const App = /* @__PURE__ */ _export_sfc(_sfc_main, [["__file", "D:/learn/uniapptest/testvue3/src/App.vue"]]);
  function createApp() {
    const app = vue.createVueApp(App);
    return {
      app
    };
  }
  const { app: __app__, Vuex: __Vuex__, Pinia: __Pinia__ } = createApp();
  uni.Vuex = __Vuex__;
  uni.Pinia = __Pinia__;
  __app__.provide("__globalStyles", __uniConfig.styles);
  __app__._component.mpType = "app";
  __app__._component.render = () => {
  };
  __app__.mount("#app");
})(Vue);
