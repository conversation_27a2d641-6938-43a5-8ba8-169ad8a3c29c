// Code generated by avx512test. DO NOT EDIT.

#include "../../../../../../runtime/textflag.h"

TEXT asmtest_avx512_vbmi2(SB), NOSPLIT, $0
	VPCOMPRESSB X7, K1, X15                            // 62d27d0963ff
	VPCOMPRESSB X13, K1, X15                           // 62527d0963ef
	VPCOMPRESSB X8, K1, X15                            // 62527d0963c7
	VPCOMPRESSB X7, K1, X28                            // 62927d0963fc
	VPCOMPRESSB X13, K1, X28                           // 62127d0963ec
	VPCOMPRESSB X8, K1, X28                            // 62127d0963c4
	VPCOMPRESSB X7, K1, -7(CX)(DX*1)                   // 62f27d09637c11f9
	VPCOMPRESSB X13, K1, -7(CX)(DX*1)                  // 62727d09636c11f9
	VPCOMPRESSB X8, K1, -7(CX)(DX*1)                   // 62727d09634411f9
	VPCOMPRESSB X7, K1, -15(R14)(R15*4)                // 62927d09637cbef1
	VPCOMPRESSB X13, K1, -15(R14)(R15*4)               // 62127d09636cbef1
	VPCOMPRESSB X8, K1, -15(R14)(R15*4)                // 62127d096344bef1
	VPCOMPRESSB Y5, K1, Y8                             // 62d27d2963e8
	VPCOMPRESSB Y24, K1, Y8                            // 62427d2963c0
	VPCOMPRESSB Y21, K1, Y8                            // 62c27d2963e8
	VPCOMPRESSB Y5, K1, Y11                            // 62d27d2963eb
	VPCOMPRESSB Y24, K1, Y11                           // 62427d2963c3
	VPCOMPRESSB Y21, K1, Y11                           // 62c27d2963eb
	VPCOMPRESSB Y5, K1, Y24                            // 62927d2963e8
	VPCOMPRESSB Y24, K1, Y24                           // 62027d2963c0
	VPCOMPRESSB Y21, K1, Y24                           // 62827d2963e8
	VPCOMPRESSB Y5, K1, -17(BP)(SI*8)                  // 62f27d29636cf5ef
	VPCOMPRESSB Y24, K1, -17(BP)(SI*8)                 // 62627d296344f5ef
	VPCOMPRESSB Y21, K1, -17(BP)(SI*8)                 // 62e27d29636cf5ef
	VPCOMPRESSB Y5, K1, (R15)                          // 62d27d29632f
	VPCOMPRESSB Y24, K1, (R15)                         // 62427d296307
	VPCOMPRESSB Y21, K1, (R15)                         // 62c27d29632f
	VPCOMPRESSB Z2, K1, Z5                             // 62f27d4963d5
	VPCOMPRESSB Z2, K1, Z23                            // 62b27d4963d7
	VPCOMPRESSB Z2, K1, -17(BP)                        // 62f27d496355ef
	VPCOMPRESSB Z2, K1, -15(R14)(R15*8)                // 62927d496354fef1
	VPCOMPRESSW X20, K5, X20                           // 62a2fd0d63e4
	VPCOMPRESSW X16, K5, X20                           // 62a2fd0d63c4
	VPCOMPRESSW X12, K5, X20                           // 6232fd0d63e4
	VPCOMPRESSW X20, K5, X24                           // 6282fd0d63e0
	VPCOMPRESSW X16, K5, X24                           // 6282fd0d63c0
	VPCOMPRESSW X12, K5, X24                           // 6212fd0d63e0
	VPCOMPRESSW X20, K5, X7                            // 62e2fd0d63e7
	VPCOMPRESSW X16, K5, X7                            // 62e2fd0d63c7
	VPCOMPRESSW X12, K5, X7                            // 6272fd0d63e7
	VPCOMPRESSW X20, K5, 17(SP)(BP*2)                  // 62e2fd0d63a46c11000000
	VPCOMPRESSW X16, K5, 17(SP)(BP*2)                  // 62e2fd0d63846c11000000
	VPCOMPRESSW X12, K5, 17(SP)(BP*2)                  // 6272fd0d63a46c11000000
	VPCOMPRESSW X20, K5, -7(DI)(R8*4)                  // 62a2fd0d63a487f9ffffff
	VPCOMPRESSW X16, K5, -7(DI)(R8*4)                  // 62a2fd0d638487f9ffffff
	VPCOMPRESSW X12, K5, -7(DI)(R8*4)                  // 6232fd0d63a487f9ffffff
	VPCOMPRESSW Y18, K7, Y14                           // 62c2fd2f63d6
	VPCOMPRESSW Y3, K7, Y14                            // 62d2fd2f63de
	VPCOMPRESSW Y24, K7, Y14                           // 6242fd2f63c6
	VPCOMPRESSW Y18, K7, Y18                           // 62a2fd2f63d2
	VPCOMPRESSW Y3, K7, Y18                            // 62b2fd2f63da
	VPCOMPRESSW Y24, K7, Y18                           // 6222fd2f63c2
	VPCOMPRESSW Y18, K7, Y31                           // 6282fd2f63d7
	VPCOMPRESSW Y3, K7, Y31                            // 6292fd2f63df
	VPCOMPRESSW Y24, K7, Y31                           // 6202fd2f63c7
	VPCOMPRESSW Y18, K7, -7(DI)(R8*1)                  // 62a2fd2f639407f9ffffff
	VPCOMPRESSW Y3, K7, -7(DI)(R8*1)                   // 62b2fd2f639c07f9ffffff
	VPCOMPRESSW Y24, K7, -7(DI)(R8*1)                  // 6222fd2f638407f9ffffff
	VPCOMPRESSW Y18, K7, (SP)                          // 62e2fd2f631424
	VPCOMPRESSW Y3, K7, (SP)                           // 62f2fd2f631c24
	VPCOMPRESSW Y24, K7, (SP)                          // 6262fd2f630424
	VPCOMPRESSW Z3, K7, Z26                            // 6292fd4f63da
	VPCOMPRESSW Z0, K7, Z26                            // 6292fd4f63c2
	VPCOMPRESSW Z3, K7, Z3                             // 62f2fd4f63db
	VPCOMPRESSW Z0, K7, Z3                             // 62f2fd4f63c3
	VPCOMPRESSW Z3, K7, 15(R8)(R14*8)                  // 6292fd4f639cf00f000000
	VPCOMPRESSW Z0, K7, 15(R8)(R14*8)                  // 6292fd4f6384f00f000000
	VPCOMPRESSW Z3, K7, -15(R14)(R15*2)                // 6292fd4f639c7ef1ffffff
	VPCOMPRESSW Z0, K7, -15(R14)(R15*2)                // 6292fd4f63847ef1ffffff
	VPEXPANDB X16, K1, X6                              // 62b27d0962f0
	VPEXPANDB X28, K1, X6                              // 62927d0962f4
	VPEXPANDB X8, K1, X6                               // 62d27d0962f0
	VPEXPANDB 99(R15)(R15*4), K1, X6                   // 62927d096274bf63
	VPEXPANDB 15(DX), K1, X6                           // 62f27d0962720f
	VPEXPANDB X16, K1, X22                             // 62a27d0962f0
	VPEXPANDB X28, K1, X22                             // 62827d0962f4
	VPEXPANDB X8, K1, X22                              // 62c27d0962f0
	VPEXPANDB 99(R15)(R15*4), K1, X22                  // 62827d096274bf63
	VPEXPANDB 15(DX), K1, X22                          // 62e27d0962720f
	VPEXPANDB X16, K1, X12                             // 62327d0962e0
	VPEXPANDB X28, K1, X12                             // 62127d0962e4
	VPEXPANDB X8, K1, X12                              // 62527d0962e0
	VPEXPANDB 99(R15)(R15*4), K1, X12                  // 62127d096264bf63
	VPEXPANDB 15(DX), K1, X12                          // 62727d0962620f
	VPEXPANDB Y31, K1, Y27                             // 62027d2962df
	VPEXPANDB Y3, K1, Y27                              // 62627d2962db
	VPEXPANDB Y14, K1, Y27                             // 62427d2962de
	VPEXPANDB -7(DI)(R8*1), K1, Y27                    // 62227d29625c07f9
	VPEXPANDB (SP), K1, Y27                            // 62627d29621c24
	VPEXPANDB Y31, K1, Y0                              // 62927d2962c7
	VPEXPANDB Y3, K1, Y0                               // 62f27d2962c3
	VPEXPANDB Y14, K1, Y0                              // 62d27d2962c6
	VPEXPANDB -7(DI)(R8*1), K1, Y0                     // 62b27d29624407f9
	VPEXPANDB (SP), K1, Y0                             // 62f27d29620424
	VPEXPANDB Y31, K1, Y11                             // 62127d2962df
	VPEXPANDB Y3, K1, Y11                              // 62727d2962db
	VPEXPANDB Y14, K1, Y11                             // 62527d2962de
	VPEXPANDB -7(DI)(R8*1), K1, Y11                    // 62327d29625c07f9
	VPEXPANDB (SP), K1, Y11                            // 62727d29621c24
	VPEXPANDB Z14, K1, Z15                             // 62527d4962fe
	VPEXPANDB Z27, K1, Z15                             // 62127d4962fb
	VPEXPANDB 15(R8)(R14*8), K1, Z15                   // 62127d49627cf00f
	VPEXPANDB -15(R14)(R15*2), K1, Z15                 // 62127d49627c7ef1
	VPEXPANDB Z14, K1, Z12                             // 62527d4962e6
	VPEXPANDB Z27, K1, Z12                             // 62127d4962e3
	VPEXPANDB 15(R8)(R14*8), K1, Z12                   // 62127d496264f00f
	VPEXPANDB -15(R14)(R15*2), K1, Z12                 // 62127d4962647ef1
	VPEXPANDW X2, K5, X18                              // 62e2fd0d62d2
	VPEXPANDW X24, K5, X18                             // 6282fd0d62d0
	VPEXPANDW -7(CX)(DX*1), K5, X18                    // 62e2fd0d629411f9ffffff
	VPEXPANDW -15(R14)(R15*4), K5, X18                 // 6282fd0d6294bef1ffffff
	VPEXPANDW X2, K5, X11                              // 6272fd0d62da
	VPEXPANDW X24, K5, X11                             // 6212fd0d62d8
	VPEXPANDW -7(CX)(DX*1), K5, X11                    // 6272fd0d629c11f9ffffff
	VPEXPANDW -15(R14)(R15*4), K5, X11                 // 6212fd0d629cbef1ffffff
	VPEXPANDW X2, K5, X9                               // 6272fd0d62ca
	VPEXPANDW X24, K5, X9                              // 6212fd0d62c8
	VPEXPANDW -7(CX)(DX*1), K5, X9                     // 6272fd0d628c11f9ffffff
	VPEXPANDW -15(R14)(R15*4), K5, X9                  // 6212fd0d628cbef1ffffff
	VPEXPANDW Y5, K7, Y19                              // 62e2fd2f62dd
	VPEXPANDW Y16, K7, Y19                             // 62a2fd2f62d8
	VPEXPANDW Y2, K7, Y19                              // 62e2fd2f62da
	VPEXPANDW (AX), K7, Y19                            // 62e2fd2f6218
	VPEXPANDW 7(SI), K7, Y19                           // 62e2fd2f629e07000000
	VPEXPANDW Y5, K7, Y14                              // 6272fd2f62f5
	VPEXPANDW Y16, K7, Y14                             // 6232fd2f62f0
	VPEXPANDW Y2, K7, Y14                              // 6272fd2f62f2
	VPEXPANDW (AX), K7, Y14                            // 6272fd2f6230
	VPEXPANDW 7(SI), K7, Y14                           // 6272fd2f62b607000000
	VPEXPANDW Y5, K7, Y21                              // 62e2fd2f62ed
	VPEXPANDW Y16, K7, Y21                             // 62a2fd2f62e8
	VPEXPANDW Y2, K7, Y21                              // 62e2fd2f62ea
	VPEXPANDW (AX), K7, Y21                            // 62e2fd2f6228
	VPEXPANDW 7(SI), K7, Y21                           // 62e2fd2f62ae07000000
	VPEXPANDW Z26, K7, Z6                              // 6292fd4f62f2
	VPEXPANDW Z14, K7, Z6                              // 62d2fd4f62f6
	VPEXPANDW (SI), K7, Z6                             // 62f2fd4f6236
	VPEXPANDW 7(SI)(DI*2), K7, Z6                      // 62f2fd4f62b47e07000000
	VPEXPANDW Z26, K7, Z14                             // 6212fd4f62f2
	VPEXPANDW Z14, K7, Z14                             // 6252fd4f62f6
	VPEXPANDW (SI), K7, Z14                            // 6272fd4f6236
	VPEXPANDW 7(SI)(DI*2), K7, Z14                     // 6272fd4f62b47e07000000
	VPSHLDD $47, X8, X31, K4, X26                      // 6243050471d02f
	VPSHLDD $47, X1, X31, K4, X26                      // 6263050471d12f
	VPSHLDD $47, X0, X31, K4, X26                      // 6263050471d02f
	VPSHLDD $47, 7(SI)(DI*4), X31, K4, X26             // 626305047194be070000002f
	VPSHLDD $47, -7(DI)(R8*2), X31, K4, X26            // 62230504719447f9ffffff2f
	VPSHLDD $47, X8, X16, K4, X26                      // 62437d0471d02f
	VPSHLDD $47, X1, X16, K4, X26                      // 62637d0471d12f
	VPSHLDD $47, X0, X16, K4, X26                      // 62637d0471d02f
	VPSHLDD $47, 7(SI)(DI*4), X16, K4, X26             // 62637d047194be070000002f
	VPSHLDD $47, -7(DI)(R8*2), X16, K4, X26            // 62237d04719447f9ffffff2f
	VPSHLDD $47, X8, X7, K4, X26                       // 6243450c71d02f
	VPSHLDD $47, X1, X7, K4, X26                       // 6263450c71d12f
	VPSHLDD $47, X0, X7, K4, X26                       // 6263450c71d02f
	VPSHLDD $47, 7(SI)(DI*4), X7, K4, X26              // 6263450c7194be070000002f
	VPSHLDD $47, -7(DI)(R8*2), X7, K4, X26             // 6223450c719447f9ffffff2f
	VPSHLDD $47, X8, X31, K4, X19                      // 62c3050471d82f
	VPSHLDD $47, X1, X31, K4, X19                      // 62e3050471d92f
	VPSHLDD $47, X0, X31, K4, X19                      // 62e3050471d82f
	VPSHLDD $47, 7(SI)(DI*4), X31, K4, X19             // 62e30504719cbe070000002f
	VPSHLDD $47, -7(DI)(R8*2), X31, K4, X19            // 62a30504719c47f9ffffff2f
	VPSHLDD $47, X8, X16, K4, X19                      // 62c37d0471d82f
	VPSHLDD $47, X1, X16, K4, X19                      // 62e37d0471d92f
	VPSHLDD $47, X0, X16, K4, X19                      // 62e37d0471d82f
	VPSHLDD $47, 7(SI)(DI*4), X16, K4, X19             // 62e37d04719cbe070000002f
	VPSHLDD $47, -7(DI)(R8*2), X16, K4, X19            // 62a37d04719c47f9ffffff2f
	VPSHLDD $47, X8, X7, K4, X19                       // 62c3450c71d82f
	VPSHLDD $47, X1, X7, K4, X19                       // 62e3450c71d92f
	VPSHLDD $47, X0, X7, K4, X19                       // 62e3450c71d82f
	VPSHLDD $47, 7(SI)(DI*4), X7, K4, X19              // 62e3450c719cbe070000002f
	VPSHLDD $47, -7(DI)(R8*2), X7, K4, X19             // 62a3450c719c47f9ffffff2f
	VPSHLDD $47, X8, X31, K4, X0                       // 62d3050471c02f
	VPSHLDD $47, X1, X31, K4, X0                       // 62f3050471c12f
	VPSHLDD $47, X0, X31, K4, X0                       // 62f3050471c02f
	VPSHLDD $47, 7(SI)(DI*4), X31, K4, X0              // 62f305047184be070000002f
	VPSHLDD $47, -7(DI)(R8*2), X31, K4, X0             // 62b30504718447f9ffffff2f
	VPSHLDD $47, X8, X16, K4, X0                       // 62d37d0471c02f
	VPSHLDD $47, X1, X16, K4, X0                       // 62f37d0471c12f
	VPSHLDD $47, X0, X16, K4, X0                       // 62f37d0471c02f
	VPSHLDD $47, 7(SI)(DI*4), X16, K4, X0              // 62f37d047184be070000002f
	VPSHLDD $47, -7(DI)(R8*2), X16, K4, X0             // 62b37d04718447f9ffffff2f
	VPSHLDD $47, X8, X7, K4, X0                        // 62d3450c71c02f
	VPSHLDD $47, X1, X7, K4, X0                        // 62f3450c71c12f
	VPSHLDD $47, X0, X7, K4, X0                        // 62f3450c71c02f
	VPSHLDD $47, 7(SI)(DI*4), X7, K4, X0               // 62f3450c7184be070000002f
	VPSHLDD $47, -7(DI)(R8*2), X7, K4, X0              // 62b3450c718447f9ffffff2f
	VPSHLDD $82, Y5, Y19, K1, Y3                       // 62f3652171dd52
	VPSHLDD $82, Y16, Y19, K1, Y3                      // 62b3652171d852
	VPSHLDD $82, Y2, Y19, K1, Y3                       // 62f3652171da52
	VPSHLDD $82, (AX), Y19, K1, Y3                     // 62f36521711852
	VPSHLDD $82, 7(SI), Y19, K1, Y3                    // 62f36521719e0700000052
	VPSHLDD $82, Y5, Y14, K1, Y3                       // 62f30d2971dd52
	VPSHLDD $82, Y16, Y14, K1, Y3                      // 62b30d2971d852
	VPSHLDD $82, Y2, Y14, K1, Y3                       // 62f30d2971da52
	VPSHLDD $82, (AX), Y14, K1, Y3                     // 62f30d29711852
	VPSHLDD $82, 7(SI), Y14, K1, Y3                    // 62f30d29719e0700000052
	VPSHLDD $82, Y5, Y21, K1, Y3                       // 62f3552171dd52
	VPSHLDD $82, Y16, Y21, K1, Y3                      // 62b3552171d852
	VPSHLDD $82, Y2, Y21, K1, Y3                       // 62f3552171da52
	VPSHLDD $82, (AX), Y21, K1, Y3                     // 62f35521711852
	VPSHLDD $82, 7(SI), Y21, K1, Y3                    // 62f35521719e0700000052
	VPSHLDD $82, Y5, Y19, K1, Y19                      // 62e3652171dd52
	VPSHLDD $82, Y16, Y19, K1, Y19                     // 62a3652171d852
	VPSHLDD $82, Y2, Y19, K1, Y19                      // 62e3652171da52
	VPSHLDD $82, (AX), Y19, K1, Y19                    // 62e36521711852
	VPSHLDD $82, 7(SI), Y19, K1, Y19                   // 62e36521719e0700000052
	VPSHLDD $82, Y5, Y14, K1, Y19                      // 62e30d2971dd52
	VPSHLDD $82, Y16, Y14, K1, Y19                     // 62a30d2971d852
	VPSHLDD $82, Y2, Y14, K1, Y19                      // 62e30d2971da52
	VPSHLDD $82, (AX), Y14, K1, Y19                    // 62e30d29711852
	VPSHLDD $82, 7(SI), Y14, K1, Y19                   // 62e30d29719e0700000052
	VPSHLDD $82, Y5, Y21, K1, Y19                      // 62e3552171dd52
	VPSHLDD $82, Y16, Y21, K1, Y19                     // 62a3552171d852
	VPSHLDD $82, Y2, Y21, K1, Y19                      // 62e3552171da52
	VPSHLDD $82, (AX), Y21, K1, Y19                    // 62e35521711852
	VPSHLDD $82, 7(SI), Y21, K1, Y19                   // 62e35521719e0700000052
	VPSHLDD $82, Y5, Y19, K1, Y23                      // 62e3652171fd52
	VPSHLDD $82, Y16, Y19, K1, Y23                     // 62a3652171f852
	VPSHLDD $82, Y2, Y19, K1, Y23                      // 62e3652171fa52
	VPSHLDD $82, (AX), Y19, K1, Y23                    // 62e36521713852
	VPSHLDD $82, 7(SI), Y19, K1, Y23                   // 62e3652171be0700000052
	VPSHLDD $82, Y5, Y14, K1, Y23                      // 62e30d2971fd52
	VPSHLDD $82, Y16, Y14, K1, Y23                     // 62a30d2971f852
	VPSHLDD $82, Y2, Y14, K1, Y23                      // 62e30d2971fa52
	VPSHLDD $82, (AX), Y14, K1, Y23                    // 62e30d29713852
	VPSHLDD $82, 7(SI), Y14, K1, Y23                   // 62e30d2971be0700000052
	VPSHLDD $82, Y5, Y21, K1, Y23                      // 62e3552171fd52
	VPSHLDD $82, Y16, Y21, K1, Y23                     // 62a3552171f852
	VPSHLDD $82, Y2, Y21, K1, Y23                      // 62e3552171fa52
	VPSHLDD $82, (AX), Y21, K1, Y23                    // 62e35521713852
	VPSHLDD $82, 7(SI), Y21, K1, Y23                   // 62e3552171be0700000052
	VPSHLDD $126, Z27, Z2, K3, Z21                     // 62836d4b71eb7e
	VPSHLDD $126, Z25, Z2, K3, Z21                     // 62836d4b71e97e
	VPSHLDD $126, 17(SP)(BP*1), Z2, K3, Z21            // 62e36d4b71ac2c110000007e
	VPSHLDD $126, -7(CX)(DX*8), Z2, K3, Z21            // 62e36d4b71acd1f9ffffff7e
	VPSHLDD $126, Z27, Z7, K3, Z21                     // 6283454b71eb7e
	VPSHLDD $126, Z25, Z7, K3, Z21                     // 6283454b71e97e
	VPSHLDD $126, 17(SP)(BP*1), Z7, K3, Z21            // 62e3454b71ac2c110000007e
	VPSHLDD $126, -7(CX)(DX*8), Z7, K3, Z21            // 62e3454b71acd1f9ffffff7e
	VPSHLDD $126, Z27, Z2, K3, Z9                      // 62136d4b71cb7e
	VPSHLDD $126, Z25, Z2, K3, Z9                      // 62136d4b71c97e
	VPSHLDD $126, 17(SP)(BP*1), Z2, K3, Z9             // 62736d4b718c2c110000007e
	VPSHLDD $126, -7(CX)(DX*8), Z2, K3, Z9             // 62736d4b718cd1f9ffffff7e
	VPSHLDD $126, Z27, Z7, K3, Z9                      // 6213454b71cb7e
	VPSHLDD $126, Z25, Z7, K3, Z9                      // 6213454b71c97e
	VPSHLDD $126, 17(SP)(BP*1), Z7, K3, Z9             // 6273454b718c2c110000007e
	VPSHLDD $126, -7(CX)(DX*8), Z7, K3, Z9             // 6273454b718cd1f9ffffff7e
	VPSHLDQ $94, X22, X21, K4, X15                     // 6233d50471fe5e
	VPSHLDQ $94, X7, X21, K4, X15                      // 6273d50471ff5e
	VPSHLDQ $94, X19, X21, K4, X15                     // 6233d50471fb5e
	VPSHLDQ $94, 17(SP), X21, K4, X15                  // 6273d50471bc24110000005e
	VPSHLDQ $94, -17(BP)(SI*4), X21, K4, X15           // 6273d50471bcb5efffffff5e
	VPSHLDQ $94, X22, X0, K4, X15                      // 6233fd0c71fe5e
	VPSHLDQ $94, X7, X0, K4, X15                       // 6273fd0c71ff5e
	VPSHLDQ $94, X19, X0, K4, X15                      // 6233fd0c71fb5e
	VPSHLDQ $94, 17(SP), X0, K4, X15                   // 6273fd0c71bc24110000005e
	VPSHLDQ $94, -17(BP)(SI*4), X0, K4, X15            // 6273fd0c71bcb5efffffff5e
	VPSHLDQ $94, X22, X28, K4, X15                     // 62339d0471fe5e
	VPSHLDQ $94, X7, X28, K4, X15                      // 62739d0471ff5e
	VPSHLDQ $94, X19, X28, K4, X15                     // 62339d0471fb5e
	VPSHLDQ $94, 17(SP), X28, K4, X15                  // 62739d0471bc24110000005e
	VPSHLDQ $94, -17(BP)(SI*4), X28, K4, X15           // 62739d0471bcb5efffffff5e
	VPSHLDQ $94, X22, X21, K4, X0                      // 62b3d50471c65e
	VPSHLDQ $94, X7, X21, K4, X0                       // 62f3d50471c75e
	VPSHLDQ $94, X19, X21, K4, X0                      // 62b3d50471c35e
	VPSHLDQ $94, 17(SP), X21, K4, X0                   // 62f3d504718424110000005e
	VPSHLDQ $94, -17(BP)(SI*4), X21, K4, X0            // 62f3d5047184b5efffffff5e
	VPSHLDQ $94, X22, X0, K4, X0                       // 62b3fd0c71c65e
	VPSHLDQ $94, X7, X0, K4, X0                        // 62f3fd0c71c75e
	VPSHLDQ $94, X19, X0, K4, X0                       // 62b3fd0c71c35e
	VPSHLDQ $94, 17(SP), X0, K4, X0                    // 62f3fd0c718424110000005e
	VPSHLDQ $94, -17(BP)(SI*4), X0, K4, X0             // 62f3fd0c7184b5efffffff5e
	VPSHLDQ $94, X22, X28, K4, X0                      // 62b39d0471c65e
	VPSHLDQ $94, X7, X28, K4, X0                       // 62f39d0471c75e
	VPSHLDQ $94, X19, X28, K4, X0                      // 62b39d0471c35e
	VPSHLDQ $94, 17(SP), X28, K4, X0                   // 62f39d04718424110000005e
	VPSHLDQ $94, -17(BP)(SI*4), X28, K4, X0            // 62f39d047184b5efffffff5e
	VPSHLDQ $94, X22, X21, K4, X16                     // 62a3d50471c65e
	VPSHLDQ $94, X7, X21, K4, X16                      // 62e3d50471c75e
	VPSHLDQ $94, X19, X21, K4, X16                     // 62a3d50471c35e
	VPSHLDQ $94, 17(SP), X21, K4, X16                  // 62e3d504718424110000005e
	VPSHLDQ $94, -17(BP)(SI*4), X21, K4, X16           // 62e3d5047184b5efffffff5e
	VPSHLDQ $94, X22, X0, K4, X16                      // 62a3fd0c71c65e
	VPSHLDQ $94, X7, X0, K4, X16                       // 62e3fd0c71c75e
	VPSHLDQ $94, X19, X0, K4, X16                      // 62a3fd0c71c35e
	VPSHLDQ $94, 17(SP), X0, K4, X16                   // 62e3fd0c718424110000005e
	VPSHLDQ $94, -17(BP)(SI*4), X0, K4, X16            // 62e3fd0c7184b5efffffff5e
	VPSHLDQ $94, X22, X28, K4, X16                     // 62a39d0471c65e
	VPSHLDQ $94, X7, X28, K4, X16                      // 62e39d0471c75e
	VPSHLDQ $94, X19, X28, K4, X16                     // 62a39d0471c35e
	VPSHLDQ $94, 17(SP), X28, K4, X16                  // 62e39d04718424110000005e
	VPSHLDQ $94, -17(BP)(SI*4), X28, K4, X16           // 62e39d047184b5efffffff5e
	VPSHLDQ $121, Y19, Y31, K5, Y21                    // 62a3852571eb79
	VPSHLDQ $121, Y7, Y31, K5, Y21                     // 62e3852571ef79
	VPSHLDQ $121, Y6, Y31, K5, Y21                     // 62e3852571ee79
	VPSHLDQ $121, (BX), Y31, K5, Y21                   // 62e38525712b79
	VPSHLDQ $121, -17(BP)(SI*1), Y31, K5, Y21          // 62e3852571ac35efffffff79
	VPSHLDQ $121, Y19, Y6, K5, Y21                     // 62a3cd2d71eb79
	VPSHLDQ $121, Y7, Y6, K5, Y21                      // 62e3cd2d71ef79
	VPSHLDQ $121, Y6, Y6, K5, Y21                      // 62e3cd2d71ee79
	VPSHLDQ $121, (BX), Y6, K5, Y21                    // 62e3cd2d712b79
	VPSHLDQ $121, -17(BP)(SI*1), Y6, K5, Y21           // 62e3cd2d71ac35efffffff79
	VPSHLDQ $121, Y19, Y11, K5, Y21                    // 62a3a52d71eb79
	VPSHLDQ $121, Y7, Y11, K5, Y21                     // 62e3a52d71ef79
	VPSHLDQ $121, Y6, Y11, K5, Y21                     // 62e3a52d71ee79
	VPSHLDQ $121, (BX), Y11, K5, Y21                   // 62e3a52d712b79
	VPSHLDQ $121, -17(BP)(SI*1), Y11, K5, Y21          // 62e3a52d71ac35efffffff79
	VPSHLDQ $121, Y19, Y31, K5, Y20                    // 62a3852571e379
	VPSHLDQ $121, Y7, Y31, K5, Y20                     // 62e3852571e779
	VPSHLDQ $121, Y6, Y31, K5, Y20                     // 62e3852571e679
	VPSHLDQ $121, (BX), Y31, K5, Y20                   // 62e38525712379
	VPSHLDQ $121, -17(BP)(SI*1), Y31, K5, Y20          // 62e3852571a435efffffff79
	VPSHLDQ $121, Y19, Y6, K5, Y20                     // 62a3cd2d71e379
	VPSHLDQ $121, Y7, Y6, K5, Y20                      // 62e3cd2d71e779
	VPSHLDQ $121, Y6, Y6, K5, Y20                      // 62e3cd2d71e679
	VPSHLDQ $121, (BX), Y6, K5, Y20                    // 62e3cd2d712379
	VPSHLDQ $121, -17(BP)(SI*1), Y6, K5, Y20           // 62e3cd2d71a435efffffff79
	VPSHLDQ $121, Y19, Y11, K5, Y20                    // 62a3a52d71e379
	VPSHLDQ $121, Y7, Y11, K5, Y20                     // 62e3a52d71e779
	VPSHLDQ $121, Y6, Y11, K5, Y20                     // 62e3a52d71e679
	VPSHLDQ $121, (BX), Y11, K5, Y20                   // 62e3a52d712379
	VPSHLDQ $121, -17(BP)(SI*1), Y11, K5, Y20          // 62e3a52d71a435efffffff79
	VPSHLDQ $121, Y19, Y31, K5, Y6                     // 62b3852571f379
	VPSHLDQ $121, Y7, Y31, K5, Y6                      // 62f3852571f779
	VPSHLDQ $121, Y6, Y31, K5, Y6                      // 62f3852571f679
	VPSHLDQ $121, (BX), Y31, K5, Y6                    // 62f38525713379
	VPSHLDQ $121, -17(BP)(SI*1), Y31, K5, Y6           // 62f3852571b435efffffff79
	VPSHLDQ $121, Y19, Y6, K5, Y6                      // 62b3cd2d71f379
	VPSHLDQ $121, Y7, Y6, K5, Y6                       // 62f3cd2d71f779
	VPSHLDQ $121, Y6, Y6, K5, Y6                       // 62f3cd2d71f679
	VPSHLDQ $121, (BX), Y6, K5, Y6                     // 62f3cd2d713379
	VPSHLDQ $121, -17(BP)(SI*1), Y6, K5, Y6            // 62f3cd2d71b435efffffff79
	VPSHLDQ $121, Y19, Y11, K5, Y6                     // 62b3a52d71f379
	VPSHLDQ $121, Y7, Y11, K5, Y6                      // 62f3a52d71f779
	VPSHLDQ $121, Y6, Y11, K5, Y6                      // 62f3a52d71f679
	VPSHLDQ $121, (BX), Y11, K5, Y6                    // 62f3a52d713379
	VPSHLDQ $121, -17(BP)(SI*1), Y11, K5, Y6           // 62f3a52d71b435efffffff79
	VPSHLDQ $13, Z3, Z27, K7, Z23                      // 62e3a54771fb0d
	VPSHLDQ $13, Z0, Z27, K7, Z23                      // 62e3a54771f80d
	VPSHLDQ $13, -17(BP)(SI*2), Z27, K7, Z23           // 62e3a54771bc75efffffff0d
	VPSHLDQ $13, 7(AX)(CX*2), Z27, K7, Z23             // 62e3a54771bc48070000000d
	VPSHLDQ $13, Z3, Z14, K7, Z23                      // 62e38d4f71fb0d
	VPSHLDQ $13, Z0, Z14, K7, Z23                      // 62e38d4f71f80d
	VPSHLDQ $13, -17(BP)(SI*2), Z14, K7, Z23           // 62e38d4f71bc75efffffff0d
	VPSHLDQ $13, 7(AX)(CX*2), Z14, K7, Z23             // 62e38d4f71bc48070000000d
	VPSHLDQ $13, Z3, Z27, K7, Z9                       // 6273a54771cb0d
	VPSHLDQ $13, Z0, Z27, K7, Z9                       // 6273a54771c80d
	VPSHLDQ $13, -17(BP)(SI*2), Z27, K7, Z9            // 6273a547718c75efffffff0d
	VPSHLDQ $13, 7(AX)(CX*2), Z27, K7, Z9              // 6273a547718c48070000000d
	VPSHLDQ $13, Z3, Z14, K7, Z9                       // 62738d4f71cb0d
	VPSHLDQ $13, Z0, Z14, K7, Z9                       // 62738d4f71c80d
	VPSHLDQ $13, -17(BP)(SI*2), Z14, K7, Z9            // 62738d4f718c75efffffff0d
	VPSHLDQ $13, 7(AX)(CX*2), Z14, K7, Z9              // 62738d4f718c48070000000d
	VPSHLDVD X15, X1, K7, X7                           // 62d2750f71ff
	VPSHLDVD X12, X1, K7, X7                           // 62d2750f71fc
	VPSHLDVD X0, X1, K7, X7                            // 62f2750f71f8
	VPSHLDVD 7(AX), X1, K7, X7                         // 62f2750f71b807000000
	VPSHLDVD (DI), X1, K7, X7                          // 62f2750f713f
	VPSHLDVD X15, X7, K7, X7                           // 62d2450f71ff
	VPSHLDVD X12, X7, K7, X7                           // 62d2450f71fc
	VPSHLDVD X0, X7, K7, X7                            // 62f2450f71f8
	VPSHLDVD 7(AX), X7, K7, X7                         // 62f2450f71b807000000
	VPSHLDVD (DI), X7, K7, X7                          // 62f2450f713f
	VPSHLDVD X15, X9, K7, X7                           // 62d2350f71ff
	VPSHLDVD X12, X9, K7, X7                           // 62d2350f71fc
	VPSHLDVD X0, X9, K7, X7                            // 62f2350f71f8
	VPSHLDVD 7(AX), X9, K7, X7                         // 62f2350f71b807000000
	VPSHLDVD (DI), X9, K7, X7                          // 62f2350f713f
	VPSHLDVD X15, X1, K7, X16                          // 62c2750f71c7
	VPSHLDVD X12, X1, K7, X16                          // 62c2750f71c4
	VPSHLDVD X0, X1, K7, X16                           // 62e2750f71c0
	VPSHLDVD 7(AX), X1, K7, X16                        // 62e2750f718007000000
	VPSHLDVD (DI), X1, K7, X16                         // 62e2750f7107
	VPSHLDVD X15, X7, K7, X16                          // 62c2450f71c7
	VPSHLDVD X12, X7, K7, X16                          // 62c2450f71c4
	VPSHLDVD X0, X7, K7, X16                           // 62e2450f71c0
	VPSHLDVD 7(AX), X7, K7, X16                        // 62e2450f718007000000
	VPSHLDVD (DI), X7, K7, X16                         // 62e2450f7107
	VPSHLDVD X15, X9, K7, X16                          // 62c2350f71c7
	VPSHLDVD X12, X9, K7, X16                          // 62c2350f71c4
	VPSHLDVD X0, X9, K7, X16                           // 62e2350f71c0
	VPSHLDVD 7(AX), X9, K7, X16                        // 62e2350f718007000000
	VPSHLDVD (DI), X9, K7, X16                         // 62e2350f7107
	VPSHLDVD X15, X1, K7, X31                          // 6242750f71ff
	VPSHLDVD X12, X1, K7, X31                          // 6242750f71fc
	VPSHLDVD X0, X1, K7, X31                           // 6262750f71f8
	VPSHLDVD 7(AX), X1, K7, X31                        // 6262750f71b807000000
	VPSHLDVD (DI), X1, K7, X31                         // 6262750f713f
	VPSHLDVD X15, X7, K7, X31                          // 6242450f71ff
	VPSHLDVD X12, X7, K7, X31                          // 6242450f71fc
	VPSHLDVD X0, X7, K7, X31                           // 6262450f71f8
	VPSHLDVD 7(AX), X7, K7, X31                        // 6262450f71b807000000
	VPSHLDVD (DI), X7, K7, X31                         // 6262450f713f
	VPSHLDVD X15, X9, K7, X31                          // 6242350f71ff
	VPSHLDVD X12, X9, K7, X31                          // 6242350f71fc
	VPSHLDVD X0, X9, K7, X31                           // 6262350f71f8
	VPSHLDVD 7(AX), X9, K7, X31                        // 6262350f71b807000000
	VPSHLDVD (DI), X9, K7, X31                         // 6262350f713f
	VPSHLDVD Y5, Y20, K6, Y0                           // 62f25d2671c5
	VPSHLDVD Y28, Y20, K6, Y0                          // 62925d2671c4
	VPSHLDVD Y7, Y20, K6, Y0                           // 62f25d2671c7
	VPSHLDVD 15(R8)(R14*4), Y20, K6, Y0                // 62925d267184b00f000000
	VPSHLDVD -7(CX)(DX*4), Y20, K6, Y0                 // 62f25d26718491f9ffffff
	VPSHLDVD Y5, Y12, K6, Y0                           // 62f21d2e71c5
	VPSHLDVD Y28, Y12, K6, Y0                          // 62921d2e71c4
	VPSHLDVD Y7, Y12, K6, Y0                           // 62f21d2e71c7
	VPSHLDVD 15(R8)(R14*4), Y12, K6, Y0                // 62921d2e7184b00f000000
	VPSHLDVD -7(CX)(DX*4), Y12, K6, Y0                 // 62f21d2e718491f9ffffff
	VPSHLDVD Y5, Y3, K6, Y0                            // 62f2652e71c5
	VPSHLDVD Y28, Y3, K6, Y0                           // 6292652e71c4
	VPSHLDVD Y7, Y3, K6, Y0                            // 62f2652e71c7
	VPSHLDVD 15(R8)(R14*4), Y3, K6, Y0                 // 6292652e7184b00f000000
	VPSHLDVD -7(CX)(DX*4), Y3, K6, Y0                  // 62f2652e718491f9ffffff
	VPSHLDVD Y5, Y20, K6, Y3                           // 62f25d2671dd
	VPSHLDVD Y28, Y20, K6, Y3                          // 62925d2671dc
	VPSHLDVD Y7, Y20, K6, Y3                           // 62f25d2671df
	VPSHLDVD 15(R8)(R14*4), Y20, K6, Y3                // 62925d26719cb00f000000
	VPSHLDVD -7(CX)(DX*4), Y20, K6, Y3                 // 62f25d26719c91f9ffffff
	VPSHLDVD Y5, Y12, K6, Y3                           // 62f21d2e71dd
	VPSHLDVD Y28, Y12, K6, Y3                          // 62921d2e71dc
	VPSHLDVD Y7, Y12, K6, Y3                           // 62f21d2e71df
	VPSHLDVD 15(R8)(R14*4), Y12, K6, Y3                // 62921d2e719cb00f000000
	VPSHLDVD -7(CX)(DX*4), Y12, K6, Y3                 // 62f21d2e719c91f9ffffff
	VPSHLDVD Y5, Y3, K6, Y3                            // 62f2652e71dd
	VPSHLDVD Y28, Y3, K6, Y3                           // 6292652e71dc
	VPSHLDVD Y7, Y3, K6, Y3                            // 62f2652e71df
	VPSHLDVD 15(R8)(R14*4), Y3, K6, Y3                 // 6292652e719cb00f000000
	VPSHLDVD -7(CX)(DX*4), Y3, K6, Y3                  // 62f2652e719c91f9ffffff
	VPSHLDVD Y5, Y20, K6, Y5                           // 62f25d2671ed
	VPSHLDVD Y28, Y20, K6, Y5                          // 62925d2671ec
	VPSHLDVD Y7, Y20, K6, Y5                           // 62f25d2671ef
	VPSHLDVD 15(R8)(R14*4), Y20, K6, Y5                // 62925d2671acb00f000000
	VPSHLDVD -7(CX)(DX*4), Y20, K6, Y5                 // 62f25d2671ac91f9ffffff
	VPSHLDVD Y5, Y12, K6, Y5                           // 62f21d2e71ed
	VPSHLDVD Y28, Y12, K6, Y5                          // 62921d2e71ec
	VPSHLDVD Y7, Y12, K6, Y5                           // 62f21d2e71ef
	VPSHLDVD 15(R8)(R14*4), Y12, K6, Y5                // 62921d2e71acb00f000000
	VPSHLDVD -7(CX)(DX*4), Y12, K6, Y5                 // 62f21d2e71ac91f9ffffff
	VPSHLDVD Y5, Y3, K6, Y5                            // 62f2652e71ed
	VPSHLDVD Y28, Y3, K6, Y5                           // 6292652e71ec
	VPSHLDVD Y7, Y3, K6, Y5                            // 62f2652e71ef
	VPSHLDVD 15(R8)(R14*4), Y3, K6, Y5                 // 6292652e71acb00f000000
	VPSHLDVD -7(CX)(DX*4), Y3, K6, Y5                  // 62f2652e71ac91f9ffffff
	VPSHLDVD Z22, Z8, K3, Z14                          // 62323d4b71f6
	VPSHLDVD Z25, Z8, K3, Z14                          // 62123d4b71f1
	VPSHLDVD 15(R8)(R14*1), Z8, K3, Z14                // 62123d4b71b4300f000000
	VPSHLDVD 15(R8)(R14*2), Z8, K3, Z14                // 62123d4b71b4700f000000
	VPSHLDVD Z22, Z24, K3, Z14                         // 62323d4371f6
	VPSHLDVD Z25, Z24, K3, Z14                         // 62123d4371f1
	VPSHLDVD 15(R8)(R14*1), Z24, K3, Z14               // 62123d4371b4300f000000
	VPSHLDVD 15(R8)(R14*2), Z24, K3, Z14               // 62123d4371b4700f000000
	VPSHLDVD Z22, Z8, K3, Z7                           // 62b23d4b71fe
	VPSHLDVD Z25, Z8, K3, Z7                           // 62923d4b71f9
	VPSHLDVD 15(R8)(R14*1), Z8, K3, Z7                 // 62923d4b71bc300f000000
	VPSHLDVD 15(R8)(R14*2), Z8, K3, Z7                 // 62923d4b71bc700f000000
	VPSHLDVD Z22, Z24, K3, Z7                          // 62b23d4371fe
	VPSHLDVD Z25, Z24, K3, Z7                          // 62923d4371f9
	VPSHLDVD 15(R8)(R14*1), Z24, K3, Z7                // 62923d4371bc300f000000
	VPSHLDVD 15(R8)(R14*2), Z24, K3, Z7                // 62923d4371bc700f000000
	VPSHLDVQ X3, X17, K7, X12                          // 6272f50771e3
	VPSHLDVQ X26, X17, K7, X12                         // 6212f50771e2
	VPSHLDVQ X23, X17, K7, X12                         // 6232f50771e7
	VPSHLDVQ 99(R15)(R15*1), X17, K7, X12              // 6212f50771a43f63000000
	VPSHLDVQ (DX), X17, K7, X12                        // 6272f5077122
	VPSHLDVQ X3, X15, K7, X12                          // 6272850f71e3
	VPSHLDVQ X26, X15, K7, X12                         // 6212850f71e2
	VPSHLDVQ X23, X15, K7, X12                         // 6232850f71e7
	VPSHLDVQ 99(R15)(R15*1), X15, K7, X12              // 6212850f71a43f63000000
	VPSHLDVQ (DX), X15, K7, X12                        // 6272850f7122
	VPSHLDVQ X3, X8, K7, X12                           // 6272bd0f71e3
	VPSHLDVQ X26, X8, K7, X12                          // 6212bd0f71e2
	VPSHLDVQ X23, X8, K7, X12                          // 6232bd0f71e7
	VPSHLDVQ 99(R15)(R15*1), X8, K7, X12               // 6212bd0f71a43f63000000
	VPSHLDVQ (DX), X8, K7, X12                         // 6272bd0f7122
	VPSHLDVQ X3, X17, K7, X14                          // 6272f50771f3
	VPSHLDVQ X26, X17, K7, X14                         // 6212f50771f2
	VPSHLDVQ X23, X17, K7, X14                         // 6232f50771f7
	VPSHLDVQ 99(R15)(R15*1), X17, K7, X14              // 6212f50771b43f63000000
	VPSHLDVQ (DX), X17, K7, X14                        // 6272f5077132
	VPSHLDVQ X3, X15, K7, X14                          // 6272850f71f3
	VPSHLDVQ X26, X15, K7, X14                         // 6212850f71f2
	VPSHLDVQ X23, X15, K7, X14                         // 6232850f71f7
	VPSHLDVQ 99(R15)(R15*1), X15, K7, X14              // 6212850f71b43f63000000
	VPSHLDVQ (DX), X15, K7, X14                        // 6272850f7132
	VPSHLDVQ X3, X8, K7, X14                           // 6272bd0f71f3
	VPSHLDVQ X26, X8, K7, X14                          // 6212bd0f71f2
	VPSHLDVQ X23, X8, K7, X14                          // 6232bd0f71f7
	VPSHLDVQ 99(R15)(R15*1), X8, K7, X14               // 6212bd0f71b43f63000000
	VPSHLDVQ (DX), X8, K7, X14                         // 6272bd0f7132
	VPSHLDVQ X3, X17, K7, X5                           // 62f2f50771eb
	VPSHLDVQ X26, X17, K7, X5                          // 6292f50771ea
	VPSHLDVQ X23, X17, K7, X5                          // 62b2f50771ef
	VPSHLDVQ 99(R15)(R15*1), X17, K7, X5               // 6292f50771ac3f63000000
	VPSHLDVQ (DX), X17, K7, X5                         // 62f2f507712a
	VPSHLDVQ X3, X15, K7, X5                           // 62f2850f71eb
	VPSHLDVQ X26, X15, K7, X5                          // 6292850f71ea
	VPSHLDVQ X23, X15, K7, X5                          // 62b2850f71ef
	VPSHLDVQ 99(R15)(R15*1), X15, K7, X5               // 6292850f71ac3f63000000
	VPSHLDVQ (DX), X15, K7, X5                         // 62f2850f712a
	VPSHLDVQ X3, X8, K7, X5                            // 62f2bd0f71eb
	VPSHLDVQ X26, X8, K7, X5                           // 6292bd0f71ea
	VPSHLDVQ X23, X8, K7, X5                           // 62b2bd0f71ef
	VPSHLDVQ 99(R15)(R15*1), X8, K7, X5                // 6292bd0f71ac3f63000000
	VPSHLDVQ (DX), X8, K7, X5                          // 62f2bd0f712a
	VPSHLDVQ Y17, Y12, K4, Y0                          // 62b29d2c71c1
	VPSHLDVQ Y7, Y12, K4, Y0                           // 62f29d2c71c7
	VPSHLDVQ Y9, Y12, K4, Y0                           // 62d29d2c71c1
	VPSHLDVQ (R8), Y12, K4, Y0                         // 62d29d2c7100
	VPSHLDVQ 15(DX)(BX*2), Y12, K4, Y0                 // 62f29d2c71845a0f000000
	VPSHLDVQ Y17, Y1, K4, Y0                           // 62b2f52c71c1
	VPSHLDVQ Y7, Y1, K4, Y0                            // 62f2f52c71c7
	VPSHLDVQ Y9, Y1, K4, Y0                            // 62d2f52c71c1
	VPSHLDVQ (R8), Y1, K4, Y0                          // 62d2f52c7100
	VPSHLDVQ 15(DX)(BX*2), Y1, K4, Y0                  // 62f2f52c71845a0f000000
	VPSHLDVQ Y17, Y14, K4, Y0                          // 62b28d2c71c1
	VPSHLDVQ Y7, Y14, K4, Y0                           // 62f28d2c71c7
	VPSHLDVQ Y9, Y14, K4, Y0                           // 62d28d2c71c1
	VPSHLDVQ (R8), Y14, K4, Y0                         // 62d28d2c7100
	VPSHLDVQ 15(DX)(BX*2), Y14, K4, Y0                 // 62f28d2c71845a0f000000
	VPSHLDVQ Y17, Y12, K4, Y22                         // 62a29d2c71f1
	VPSHLDVQ Y7, Y12, K4, Y22                          // 62e29d2c71f7
	VPSHLDVQ Y9, Y12, K4, Y22                          // 62c29d2c71f1
	VPSHLDVQ (R8), Y12, K4, Y22                        // 62c29d2c7130
	VPSHLDVQ 15(DX)(BX*2), Y12, K4, Y22                // 62e29d2c71b45a0f000000
	VPSHLDVQ Y17, Y1, K4, Y22                          // 62a2f52c71f1
	VPSHLDVQ Y7, Y1, K4, Y22                           // 62e2f52c71f7
	VPSHLDVQ Y9, Y1, K4, Y22                           // 62c2f52c71f1
	VPSHLDVQ (R8), Y1, K4, Y22                         // 62c2f52c7130
	VPSHLDVQ 15(DX)(BX*2), Y1, K4, Y22                 // 62e2f52c71b45a0f000000
	VPSHLDVQ Y17, Y14, K4, Y22                         // 62a28d2c71f1
	VPSHLDVQ Y7, Y14, K4, Y22                          // 62e28d2c71f7
	VPSHLDVQ Y9, Y14, K4, Y22                          // 62c28d2c71f1
	VPSHLDVQ (R8), Y14, K4, Y22                        // 62c28d2c7130
	VPSHLDVQ 15(DX)(BX*2), Y14, K4, Y22                // 62e28d2c71b45a0f000000
	VPSHLDVQ Y17, Y12, K4, Y13                         // 62329d2c71e9
	VPSHLDVQ Y7, Y12, K4, Y13                          // 62729d2c71ef
	VPSHLDVQ Y9, Y12, K4, Y13                          // 62529d2c71e9
	VPSHLDVQ (R8), Y12, K4, Y13                        // 62529d2c7128
	VPSHLDVQ 15(DX)(BX*2), Y12, K4, Y13                // 62729d2c71ac5a0f000000
	VPSHLDVQ Y17, Y1, K4, Y13                          // 6232f52c71e9
	VPSHLDVQ Y7, Y1, K4, Y13                           // 6272f52c71ef
	VPSHLDVQ Y9, Y1, K4, Y13                           // 6252f52c71e9
	VPSHLDVQ (R8), Y1, K4, Y13                         // 6252f52c7128
	VPSHLDVQ 15(DX)(BX*2), Y1, K4, Y13                 // 6272f52c71ac5a0f000000
	VPSHLDVQ Y17, Y14, K4, Y13                         // 62328d2c71e9
	VPSHLDVQ Y7, Y14, K4, Y13                          // 62728d2c71ef
	VPSHLDVQ Y9, Y14, K4, Y13                          // 62528d2c71e9
	VPSHLDVQ (R8), Y14, K4, Y13                        // 62528d2c7128
	VPSHLDVQ 15(DX)(BX*2), Y14, K4, Y13                // 62728d2c71ac5a0f000000
	VPSHLDVQ Z0, Z6, K4, Z1                            // 62f2cd4c71c8
	VPSHLDVQ Z8, Z6, K4, Z1                            // 62d2cd4c71c8
	VPSHLDVQ (R14), Z6, K4, Z1                         // 62d2cd4c710e
	VPSHLDVQ -7(DI)(R8*8), Z6, K4, Z1                  // 62b2cd4c718cc7f9ffffff
	VPSHLDVQ Z0, Z2, K4, Z1                            // 62f2ed4c71c8
	VPSHLDVQ Z8, Z2, K4, Z1                            // 62d2ed4c71c8
	VPSHLDVQ (R14), Z2, K4, Z1                         // 62d2ed4c710e
	VPSHLDVQ -7(DI)(R8*8), Z2, K4, Z1                  // 62b2ed4c718cc7f9ffffff
	VPSHLDVQ Z0, Z6, K4, Z16                           // 62e2cd4c71c0
	VPSHLDVQ Z8, Z6, K4, Z16                           // 62c2cd4c71c0
	VPSHLDVQ (R14), Z6, K4, Z16                        // 62c2cd4c7106
	VPSHLDVQ -7(DI)(R8*8), Z6, K4, Z16                 // 62a2cd4c7184c7f9ffffff
	VPSHLDVQ Z0, Z2, K4, Z16                           // 62e2ed4c71c0
	VPSHLDVQ Z8, Z2, K4, Z16                           // 62c2ed4c71c0
	VPSHLDVQ (R14), Z2, K4, Z16                        // 62c2ed4c7106
	VPSHLDVQ -7(DI)(R8*8), Z2, K4, Z16                 // 62a2ed4c7184c7f9ffffff
	VPSHLDVW X18, X9, K7, X13                          // 6232b50f70ea
	VPSHLDVW X21, X9, K7, X13                          // 6232b50f70ed
	VPSHLDVW X1, X9, K7, X13                           // 6272b50f70e9
	VPSHLDVW -17(BP)(SI*8), X9, K7, X13                // 6272b50f70acf5efffffff
	VPSHLDVW (R15), X9, K7, X13                        // 6252b50f702f
	VPSHLDVW X18, X15, K7, X13                         // 6232850f70ea
	VPSHLDVW X21, X15, K7, X13                         // 6232850f70ed
	VPSHLDVW X1, X15, K7, X13                          // 6272850f70e9
	VPSHLDVW -17(BP)(SI*8), X15, K7, X13               // 6272850f70acf5efffffff
	VPSHLDVW (R15), X15, K7, X13                       // 6252850f702f
	VPSHLDVW X18, X26, K7, X13                         // 6232ad0770ea
	VPSHLDVW X21, X26, K7, X13                         // 6232ad0770ed
	VPSHLDVW X1, X26, K7, X13                          // 6272ad0770e9
	VPSHLDVW -17(BP)(SI*8), X26, K7, X13               // 6272ad0770acf5efffffff
	VPSHLDVW (R15), X26, K7, X13                       // 6252ad07702f
	VPSHLDVW X18, X9, K7, X28                          // 6222b50f70e2
	VPSHLDVW X21, X9, K7, X28                          // 6222b50f70e5
	VPSHLDVW X1, X9, K7, X28                           // 6262b50f70e1
	VPSHLDVW -17(BP)(SI*8), X9, K7, X28                // 6262b50f70a4f5efffffff
	VPSHLDVW (R15), X9, K7, X28                        // 6242b50f7027
	VPSHLDVW X18, X15, K7, X28                         // 6222850f70e2
	VPSHLDVW X21, X15, K7, X28                         // 6222850f70e5
	VPSHLDVW X1, X15, K7, X28                          // 6262850f70e1
	VPSHLDVW -17(BP)(SI*8), X15, K7, X28               // 6262850f70a4f5efffffff
	VPSHLDVW (R15), X15, K7, X28                       // 6242850f7027
	VPSHLDVW X18, X26, K7, X28                         // 6222ad0770e2
	VPSHLDVW X21, X26, K7, X28                         // 6222ad0770e5
	VPSHLDVW X1, X26, K7, X28                          // 6262ad0770e1
	VPSHLDVW -17(BP)(SI*8), X26, K7, X28               // 6262ad0770a4f5efffffff
	VPSHLDVW (R15), X26, K7, X28                       // 6242ad077027
	VPSHLDVW X18, X9, K7, X24                          // 6222b50f70c2
	VPSHLDVW X21, X9, K7, X24                          // 6222b50f70c5
	VPSHLDVW X1, X9, K7, X24                           // 6262b50f70c1
	VPSHLDVW -17(BP)(SI*8), X9, K7, X24                // 6262b50f7084f5efffffff
	VPSHLDVW (R15), X9, K7, X24                        // 6242b50f7007
	VPSHLDVW X18, X15, K7, X24                         // 6222850f70c2
	VPSHLDVW X21, X15, K7, X24                         // 6222850f70c5
	VPSHLDVW X1, X15, K7, X24                          // 6262850f70c1
	VPSHLDVW -17(BP)(SI*8), X15, K7, X24               // 6262850f7084f5efffffff
	VPSHLDVW (R15), X15, K7, X24                       // 6242850f7007
	VPSHLDVW X18, X26, K7, X24                         // 6222ad0770c2
	VPSHLDVW X21, X26, K7, X24                         // 6222ad0770c5
	VPSHLDVW X1, X26, K7, X24                          // 6262ad0770c1
	VPSHLDVW -17(BP)(SI*8), X26, K7, X24               // 6262ad077084f5efffffff
	VPSHLDVW (R15), X26, K7, X24                       // 6242ad077007
	VPSHLDVW Y2, Y28, K2, Y31                          // 62629d2270fa
	VPSHLDVW Y21, Y28, K2, Y31                         // 62229d2270fd
	VPSHLDVW Y12, Y28, K2, Y31                         // 62429d2270fc
	VPSHLDVW 17(SP)(BP*1), Y28, K2, Y31                // 62629d2270bc2c11000000
	VPSHLDVW -7(CX)(DX*8), Y28, K2, Y31                // 62629d2270bcd1f9ffffff
	VPSHLDVW Y2, Y13, K2, Y31                          // 6262952a70fa
	VPSHLDVW Y21, Y13, K2, Y31                         // 6222952a70fd
	VPSHLDVW Y12, Y13, K2, Y31                         // 6242952a70fc
	VPSHLDVW 17(SP)(BP*1), Y13, K2, Y31                // 6262952a70bc2c11000000
	VPSHLDVW -7(CX)(DX*8), Y13, K2, Y31                // 6262952a70bcd1f9ffffff
	VPSHLDVW Y2, Y7, K2, Y31                           // 6262c52a70fa
	VPSHLDVW Y21, Y7, K2, Y31                          // 6222c52a70fd
	VPSHLDVW Y12, Y7, K2, Y31                          // 6242c52a70fc
	VPSHLDVW 17(SP)(BP*1), Y7, K2, Y31                 // 6262c52a70bc2c11000000
	VPSHLDVW -7(CX)(DX*8), Y7, K2, Y31                 // 6262c52a70bcd1f9ffffff
	VPSHLDVW Y2, Y28, K2, Y8                           // 62729d2270c2
	VPSHLDVW Y21, Y28, K2, Y8                          // 62329d2270c5
	VPSHLDVW Y12, Y28, K2, Y8                          // 62529d2270c4
	VPSHLDVW 17(SP)(BP*1), Y28, K2, Y8                 // 62729d2270842c11000000
	VPSHLDVW -7(CX)(DX*8), Y28, K2, Y8                 // 62729d227084d1f9ffffff
	VPSHLDVW Y2, Y13, K2, Y8                           // 6272952a70c2
	VPSHLDVW Y21, Y13, K2, Y8                          // 6232952a70c5
	VPSHLDVW Y12, Y13, K2, Y8                          // 6252952a70c4
	VPSHLDVW 17(SP)(BP*1), Y13, K2, Y8                 // 6272952a70842c11000000
	VPSHLDVW -7(CX)(DX*8), Y13, K2, Y8                 // 6272952a7084d1f9ffffff
	VPSHLDVW Y2, Y7, K2, Y8                            // 6272c52a70c2
	VPSHLDVW Y21, Y7, K2, Y8                           // 6232c52a70c5
	VPSHLDVW Y12, Y7, K2, Y8                           // 6252c52a70c4
	VPSHLDVW 17(SP)(BP*1), Y7, K2, Y8                  // 6272c52a70842c11000000
	VPSHLDVW -7(CX)(DX*8), Y7, K2, Y8                  // 6272c52a7084d1f9ffffff
	VPSHLDVW Y2, Y28, K2, Y1                           // 62f29d2270ca
	VPSHLDVW Y21, Y28, K2, Y1                          // 62b29d2270cd
	VPSHLDVW Y12, Y28, K2, Y1                          // 62d29d2270cc
	VPSHLDVW 17(SP)(BP*1), Y28, K2, Y1                 // 62f29d22708c2c11000000
	VPSHLDVW -7(CX)(DX*8), Y28, K2, Y1                 // 62f29d22708cd1f9ffffff
	VPSHLDVW Y2, Y13, K2, Y1                           // 62f2952a70ca
	VPSHLDVW Y21, Y13, K2, Y1                          // 62b2952a70cd
	VPSHLDVW Y12, Y13, K2, Y1                          // 62d2952a70cc
	VPSHLDVW 17(SP)(BP*1), Y13, K2, Y1                 // 62f2952a708c2c11000000
	VPSHLDVW -7(CX)(DX*8), Y13, K2, Y1                 // 62f2952a708cd1f9ffffff
	VPSHLDVW Y2, Y7, K2, Y1                            // 62f2c52a70ca
	VPSHLDVW Y21, Y7, K2, Y1                           // 62b2c52a70cd
	VPSHLDVW Y12, Y7, K2, Y1                           // 62d2c52a70cc
	VPSHLDVW 17(SP)(BP*1), Y7, K2, Y1                  // 62f2c52a708c2c11000000
	VPSHLDVW -7(CX)(DX*8), Y7, K2, Y1                  // 62f2c52a708cd1f9ffffff
	VPSHLDVW Z11, Z14, K5, Z15                         // 62528d4d70fb
	VPSHLDVW Z5, Z14, K5, Z15                          // 62728d4d70fd
	VPSHLDVW 99(R15)(R15*4), Z14, K5, Z15              // 62128d4d70bcbf63000000
	VPSHLDVW 15(DX), Z14, K5, Z15                      // 62728d4d70ba0f000000
	VPSHLDVW Z11, Z27, K5, Z15                         // 6252a54570fb
	VPSHLDVW Z5, Z27, K5, Z15                          // 6272a54570fd
	VPSHLDVW 99(R15)(R15*4), Z27, K5, Z15              // 6212a54570bcbf63000000
	VPSHLDVW 15(DX), Z27, K5, Z15                      // 6272a54570ba0f000000
	VPSHLDVW Z11, Z14, K5, Z12                         // 62528d4d70e3
	VPSHLDVW Z5, Z14, K5, Z12                          // 62728d4d70e5
	VPSHLDVW 99(R15)(R15*4), Z14, K5, Z12              // 62128d4d70a4bf63000000
	VPSHLDVW 15(DX), Z14, K5, Z12                      // 62728d4d70a20f000000
	VPSHLDVW Z11, Z27, K5, Z12                         // 6252a54570e3
	VPSHLDVW Z5, Z27, K5, Z12                          // 6272a54570e5
	VPSHLDVW 99(R15)(R15*4), Z27, K5, Z12              // 6212a54570a4bf63000000
	VPSHLDVW 15(DX), Z27, K5, Z12                      // 6272a54570a20f000000
	VPSHLDW $65, X24, X7, K3, X11                      // 6213c50b70d841
	VPSHLDW $65, X20, X7, K3, X11                      // 6233c50b70dc41
	VPSHLDW $65, X7, X7, K3, X11                       // 6273c50b70df41
	VPSHLDW $65, 7(SI)(DI*8), X7, K3, X11              // 6273c50b709cfe0700000041
	VPSHLDW $65, -15(R14), X7, K3, X11                 // 6253c50b709ef1ffffff41
	VPSHLDW $65, X24, X0, K3, X11                      // 6213fd0b70d841
	VPSHLDW $65, X20, X0, K3, X11                      // 6233fd0b70dc41
	VPSHLDW $65, X7, X0, K3, X11                       // 6273fd0b70df41
	VPSHLDW $65, 7(SI)(DI*8), X0, K3, X11              // 6273fd0b709cfe0700000041
	VPSHLDW $65, -15(R14), X0, K3, X11                 // 6253fd0b709ef1ffffff41
	VPSHLDW $65, X24, X7, K3, X31                      // 6203c50b70f841
	VPSHLDW $65, X20, X7, K3, X31                      // 6223c50b70fc41
	VPSHLDW $65, X7, X7, K3, X31                       // 6263c50b70ff41
	VPSHLDW $65, 7(SI)(DI*8), X7, K3, X31              // 6263c50b70bcfe0700000041
	VPSHLDW $65, -15(R14), X7, K3, X31                 // 6243c50b70bef1ffffff41
	VPSHLDW $65, X24, X0, K3, X31                      // 6203fd0b70f841
	VPSHLDW $65, X20, X0, K3, X31                      // 6223fd0b70fc41
	VPSHLDW $65, X7, X0, K3, X31                       // 6263fd0b70ff41
	VPSHLDW $65, 7(SI)(DI*8), X0, K3, X31              // 6263fd0b70bcfe0700000041
	VPSHLDW $65, -15(R14), X0, K3, X31                 // 6243fd0b70bef1ffffff41
	VPSHLDW $65, X24, X7, K3, X3                       // 6293c50b70d841
	VPSHLDW $65, X20, X7, K3, X3                       // 62b3c50b70dc41
	VPSHLDW $65, X7, X7, K3, X3                        // 62f3c50b70df41
	VPSHLDW $65, 7(SI)(DI*8), X7, K3, X3               // 62f3c50b709cfe0700000041
	VPSHLDW $65, -15(R14), X7, K3, X3                  // 62d3c50b709ef1ffffff41
	VPSHLDW $65, X24, X0, K3, X3                       // 6293fd0b70d841
	VPSHLDW $65, X20, X0, K3, X3                       // 62b3fd0b70dc41
	VPSHLDW $65, X7, X0, K3, X3                        // 62f3fd0b70df41
	VPSHLDW $65, 7(SI)(DI*8), X0, K3, X3               // 62f3fd0b709cfe0700000041
	VPSHLDW $65, -15(R14), X0, K3, X3                  // 62d3fd0b709ef1ffffff41
	VPSHLDW $67, Y12, Y3, K4, Y9                       // 6253e52c70cc43
	VPSHLDW $67, Y21, Y3, K4, Y9                       // 6233e52c70cd43
	VPSHLDW $67, Y14, Y3, K4, Y9                       // 6253e52c70ce43
	VPSHLDW $67, -17(BP)(SI*2), Y3, K4, Y9             // 6273e52c708c75efffffff43
	VPSHLDW $67, 7(AX)(CX*2), Y3, K4, Y9               // 6273e52c708c480700000043
	VPSHLDW $67, Y12, Y2, K4, Y9                       // 6253ed2c70cc43
	VPSHLDW $67, Y21, Y2, K4, Y9                       // 6233ed2c70cd43
	VPSHLDW $67, Y14, Y2, K4, Y9                       // 6253ed2c70ce43
	VPSHLDW $67, -17(BP)(SI*2), Y2, K4, Y9             // 6273ed2c708c75efffffff43
	VPSHLDW $67, 7(AX)(CX*2), Y2, K4, Y9               // 6273ed2c708c480700000043
	VPSHLDW $67, Y12, Y9, K4, Y9                       // 6253b52c70cc43
	VPSHLDW $67, Y21, Y9, K4, Y9                       // 6233b52c70cd43
	VPSHLDW $67, Y14, Y9, K4, Y9                       // 6253b52c70ce43
	VPSHLDW $67, -17(BP)(SI*2), Y9, K4, Y9             // 6273b52c708c75efffffff43
	VPSHLDW $67, 7(AX)(CX*2), Y9, K4, Y9               // 6273b52c708c480700000043
	VPSHLDW $67, Y12, Y3, K4, Y1                       // 62d3e52c70cc43
	VPSHLDW $67, Y21, Y3, K4, Y1                       // 62b3e52c70cd43
	VPSHLDW $67, Y14, Y3, K4, Y1                       // 62d3e52c70ce43
	VPSHLDW $67, -17(BP)(SI*2), Y3, K4, Y1             // 62f3e52c708c75efffffff43
	VPSHLDW $67, 7(AX)(CX*2), Y3, K4, Y1               // 62f3e52c708c480700000043
	VPSHLDW $67, Y12, Y2, K4, Y1                       // 62d3ed2c70cc43
	VPSHLDW $67, Y21, Y2, K4, Y1                       // 62b3ed2c70cd43
	VPSHLDW $67, Y14, Y2, K4, Y1                       // 62d3ed2c70ce43
	VPSHLDW $67, -17(BP)(SI*2), Y2, K4, Y1             // 62f3ed2c708c75efffffff43
	VPSHLDW $67, 7(AX)(CX*2), Y2, K4, Y1               // 62f3ed2c708c480700000043
	VPSHLDW $67, Y12, Y9, K4, Y1                       // 62d3b52c70cc43
	VPSHLDW $67, Y21, Y9, K4, Y1                       // 62b3b52c70cd43
	VPSHLDW $67, Y14, Y9, K4, Y1                       // 62d3b52c70ce43
	VPSHLDW $67, -17(BP)(SI*2), Y9, K4, Y1             // 62f3b52c708c75efffffff43
	VPSHLDW $67, 7(AX)(CX*2), Y9, K4, Y1               // 62f3b52c708c480700000043
	VPSHLDW $127, Z2, Z5, K2, Z13                      // 6273d54a70ea7f
	VPSHLDW $127, (CX), Z5, K2, Z13                    // 6273d54a70297f
	VPSHLDW $127, 99(R15), Z5, K2, Z13                 // 6253d54a70af630000007f
	VPSHLDW $127, Z2, Z23, K2, Z13                     // 6273c54270ea7f
	VPSHLDW $127, (CX), Z23, K2, Z13                   // 6273c54270297f
	VPSHLDW $127, 99(R15), Z23, K2, Z13                // 6253c54270af630000007f
	VPSHLDW $127, Z2, Z5, K2, Z14                      // 6273d54a70f27f
	VPSHLDW $127, (CX), Z5, K2, Z14                    // 6273d54a70317f
	VPSHLDW $127, 99(R15), Z5, K2, Z14                 // 6253d54a70b7630000007f
	VPSHLDW $127, Z2, Z23, K2, Z14                     // 6273c54270f27f
	VPSHLDW $127, (CX), Z23, K2, Z14                   // 6273c54270317f
	VPSHLDW $127, 99(R15), Z23, K2, Z14                // 6253c54270b7630000007f
	VPSHRDD $0, X21, X5, K2, X9                        // 6233550a73cd00
	VPSHRDD $0, X1, X5, K2, X9                         // 6273550a73c900
	VPSHRDD $0, X11, X5, K2, X9                        // 6253550a73cb00
	VPSHRDD $0, 7(SI)(DI*1), X5, K2, X9                // 6273550a738c3e0700000000
	VPSHRDD $0, 15(DX)(BX*8), X5, K2, X9               // 6273550a738cda0f00000000
	VPSHRDD $0, X21, X31, K2, X9                       // 6233050273cd00
	VPSHRDD $0, X1, X31, K2, X9                        // 6273050273c900
	VPSHRDD $0, X11, X31, K2, X9                       // 6253050273cb00
	VPSHRDD $0, 7(SI)(DI*1), X31, K2, X9               // 62730502738c3e0700000000
	VPSHRDD $0, 15(DX)(BX*8), X31, K2, X9              // 62730502738cda0f00000000
	VPSHRDD $0, X21, X3, K2, X9                        // 6233650a73cd00
	VPSHRDD $0, X1, X3, K2, X9                         // 6273650a73c900
	VPSHRDD $0, X11, X3, K2, X9                        // 6253650a73cb00
	VPSHRDD $0, 7(SI)(DI*1), X3, K2, X9                // 6273650a738c3e0700000000
	VPSHRDD $0, 15(DX)(BX*8), X3, K2, X9               // 6273650a738cda0f00000000
	VPSHRDD $0, X21, X5, K2, X7                        // 62b3550a73fd00
	VPSHRDD $0, X1, X5, K2, X7                         // 62f3550a73f900
	VPSHRDD $0, X11, X5, K2, X7                        // 62d3550a73fb00
	VPSHRDD $0, 7(SI)(DI*1), X5, K2, X7                // 62f3550a73bc3e0700000000
	VPSHRDD $0, 15(DX)(BX*8), X5, K2, X7               // 62f3550a73bcda0f00000000
	VPSHRDD $0, X21, X31, K2, X7                       // 62b3050273fd00
	VPSHRDD $0, X1, X31, K2, X7                        // 62f3050273f900
	VPSHRDD $0, X11, X31, K2, X7                       // 62d3050273fb00
	VPSHRDD $0, 7(SI)(DI*1), X31, K2, X7               // 62f3050273bc3e0700000000
	VPSHRDD $0, 15(DX)(BX*8), X31, K2, X7              // 62f3050273bcda0f00000000
	VPSHRDD $0, X21, X3, K2, X7                        // 62b3650a73fd00
	VPSHRDD $0, X1, X3, K2, X7                         // 62f3650a73f900
	VPSHRDD $0, X11, X3, K2, X7                        // 62d3650a73fb00
	VPSHRDD $0, 7(SI)(DI*1), X3, K2, X7                // 62f3650a73bc3e0700000000
	VPSHRDD $0, 15(DX)(BX*8), X3, K2, X7               // 62f3650a73bcda0f00000000
	VPSHRDD $0, X21, X5, K2, X14                       // 6233550a73f500
	VPSHRDD $0, X1, X5, K2, X14                        // 6273550a73f100
	VPSHRDD $0, X11, X5, K2, X14                       // 6253550a73f300
	VPSHRDD $0, 7(SI)(DI*1), X5, K2, X14               // 6273550a73b43e0700000000
	VPSHRDD $0, 15(DX)(BX*8), X5, K2, X14              // 6273550a73b4da0f00000000
	VPSHRDD $0, X21, X31, K2, X14                      // 6233050273f500
	VPSHRDD $0, X1, X31, K2, X14                       // 6273050273f100
	VPSHRDD $0, X11, X31, K2, X14                      // 6253050273f300
	VPSHRDD $0, 7(SI)(DI*1), X31, K2, X14              // 6273050273b43e0700000000
	VPSHRDD $0, 15(DX)(BX*8), X31, K2, X14             // 6273050273b4da0f00000000
	VPSHRDD $0, X21, X3, K2, X14                       // 6233650a73f500
	VPSHRDD $0, X1, X3, K2, X14                        // 6273650a73f100
	VPSHRDD $0, X11, X3, K2, X14                       // 6253650a73f300
	VPSHRDD $0, 7(SI)(DI*1), X3, K2, X14               // 6273650a73b43e0700000000
	VPSHRDD $0, 15(DX)(BX*8), X3, K2, X14              // 6273650a73b4da0f00000000
	VPSHRDD $97, Y31, Y16, K3, Y30                     // 62037d2373f761
	VPSHRDD $97, Y22, Y16, K3, Y30                     // 62237d2373f661
	VPSHRDD $97, Y6, Y16, K3, Y30                      // 62637d2373f661
	VPSHRDD $97, 15(R8)(R14*1), Y16, K3, Y30           // 62037d2373b4300f00000061
	VPSHRDD $97, 15(R8)(R14*2), Y16, K3, Y30           // 62037d2373b4700f00000061
	VPSHRDD $97, Y31, Y1, K3, Y30                      // 6203752b73f761
	VPSHRDD $97, Y22, Y1, K3, Y30                      // 6223752b73f661
	VPSHRDD $97, Y6, Y1, K3, Y30                       // 6263752b73f661
	VPSHRDD $97, 15(R8)(R14*1), Y1, K3, Y30            // 6203752b73b4300f00000061
	VPSHRDD $97, 15(R8)(R14*2), Y1, K3, Y30            // 6203752b73b4700f00000061
	VPSHRDD $97, Y31, Y30, K3, Y30                     // 62030d2373f761
	VPSHRDD $97, Y22, Y30, K3, Y30                     // 62230d2373f661
	VPSHRDD $97, Y6, Y30, K3, Y30                      // 62630d2373f661
	VPSHRDD $97, 15(R8)(R14*1), Y30, K3, Y30           // 62030d2373b4300f00000061
	VPSHRDD $97, 15(R8)(R14*2), Y30, K3, Y30           // 62030d2373b4700f00000061
	VPSHRDD $97, Y31, Y16, K3, Y26                     // 62037d2373d761
	VPSHRDD $97, Y22, Y16, K3, Y26                     // 62237d2373d661
	VPSHRDD $97, Y6, Y16, K3, Y26                      // 62637d2373d661
	VPSHRDD $97, 15(R8)(R14*1), Y16, K3, Y26           // 62037d237394300f00000061
	VPSHRDD $97, 15(R8)(R14*2), Y16, K3, Y26           // 62037d237394700f00000061
	VPSHRDD $97, Y31, Y1, K3, Y26                      // 6203752b73d761
	VPSHRDD $97, Y22, Y1, K3, Y26                      // 6223752b73d661
	VPSHRDD $97, Y6, Y1, K3, Y26                       // 6263752b73d661
	VPSHRDD $97, 15(R8)(R14*1), Y1, K3, Y26            // 6203752b7394300f00000061
	VPSHRDD $97, 15(R8)(R14*2), Y1, K3, Y26            // 6203752b7394700f00000061
	VPSHRDD $97, Y31, Y30, K3, Y26                     // 62030d2373d761
	VPSHRDD $97, Y22, Y30, K3, Y26                     // 62230d2373d661
	VPSHRDD $97, Y6, Y30, K3, Y26                      // 62630d2373d661
	VPSHRDD $97, 15(R8)(R14*1), Y30, K3, Y26           // 62030d237394300f00000061
	VPSHRDD $97, 15(R8)(R14*2), Y30, K3, Y26           // 62030d237394700f00000061
	VPSHRDD $97, Y31, Y16, K3, Y7                      // 62937d2373ff61
	VPSHRDD $97, Y22, Y16, K3, Y7                      // 62b37d2373fe61
	VPSHRDD $97, Y6, Y16, K3, Y7                       // 62f37d2373fe61
	VPSHRDD $97, 15(R8)(R14*1), Y16, K3, Y7            // 62937d2373bc300f00000061
	VPSHRDD $97, 15(R8)(R14*2), Y16, K3, Y7            // 62937d2373bc700f00000061
	VPSHRDD $97, Y31, Y1, K3, Y7                       // 6293752b73ff61
	VPSHRDD $97, Y22, Y1, K3, Y7                       // 62b3752b73fe61
	VPSHRDD $97, Y6, Y1, K3, Y7                        // 62f3752b73fe61
	VPSHRDD $97, 15(R8)(R14*1), Y1, K3, Y7             // 6293752b73bc300f00000061
	VPSHRDD $97, 15(R8)(R14*2), Y1, K3, Y7             // 6293752b73bc700f00000061
	VPSHRDD $97, Y31, Y30, K3, Y7                      // 62930d2373ff61
	VPSHRDD $97, Y22, Y30, K3, Y7                      // 62b30d2373fe61
	VPSHRDD $97, Y6, Y30, K3, Y7                       // 62f30d2373fe61
	VPSHRDD $97, 15(R8)(R14*1), Y30, K3, Y7            // 62930d2373bc300f00000061
	VPSHRDD $97, 15(R8)(R14*2), Y30, K3, Y7            // 62930d2373bc700f00000061
	VPSHRDD $81, Z28, Z26, K3, Z6                      // 62932d4373f451
	VPSHRDD $81, Z6, Z26, K3, Z6                       // 62f32d4373f651
	VPSHRDD $81, 99(R15)(R15*2), Z26, K3, Z6           // 62932d4373b47f6300000051
	VPSHRDD $81, -7(DI), Z26, K3, Z6                   // 62f32d4373b7f9ffffff51
	VPSHRDD $81, Z28, Z14, K3, Z6                      // 62930d4b73f451
	VPSHRDD $81, Z6, Z14, K3, Z6                       // 62f30d4b73f651
	VPSHRDD $81, 99(R15)(R15*2), Z14, K3, Z6           // 62930d4b73b47f6300000051
	VPSHRDD $81, -7(DI), Z14, K3, Z6                   // 62f30d4b73b7f9ffffff51
	VPSHRDD $81, Z28, Z26, K3, Z14                     // 62132d4373f451
	VPSHRDD $81, Z6, Z26, K3, Z14                      // 62732d4373f651
	VPSHRDD $81, 99(R15)(R15*2), Z26, K3, Z14          // 62132d4373b47f6300000051
	VPSHRDD $81, -7(DI), Z26, K3, Z14                  // 62732d4373b7f9ffffff51
	VPSHRDD $81, Z28, Z14, K3, Z14                     // 62130d4b73f451
	VPSHRDD $81, Z6, Z14, K3, Z14                      // 62730d4b73f651
	VPSHRDD $81, 99(R15)(R15*2), Z14, K3, Z14          // 62130d4b73b47f6300000051
	VPSHRDD $81, -7(DI), Z14, K3, Z14                  // 62730d4b73b7f9ffffff51
	VPSHRDQ $42, X14, X16, K3, X13                     // 6253fd0373ee2a
	VPSHRDQ $42, X19, X16, K3, X13                     // 6233fd0373eb2a
	VPSHRDQ $42, X8, X16, K3, X13                      // 6253fd0373e82a
	VPSHRDQ $42, -7(DI)(R8*1), X16, K3, X13            // 6233fd0373ac07f9ffffff2a
	VPSHRDQ $42, (SP), X16, K3, X13                    // 6273fd03732c242a
	VPSHRDQ $42, X14, X14, K3, X13                     // 62538d0b73ee2a
	VPSHRDQ $42, X19, X14, K3, X13                     // 62338d0b73eb2a
	VPSHRDQ $42, X8, X14, K3, X13                      // 62538d0b73e82a
	VPSHRDQ $42, -7(DI)(R8*1), X14, K3, X13            // 62338d0b73ac07f9ffffff2a
	VPSHRDQ $42, (SP), X14, K3, X13                    // 62738d0b732c242a
	VPSHRDQ $42, X14, X11, K3, X13                     // 6253a50b73ee2a
	VPSHRDQ $42, X19, X11, K3, X13                     // 6233a50b73eb2a
	VPSHRDQ $42, X8, X11, K3, X13                      // 6253a50b73e82a
	VPSHRDQ $42, -7(DI)(R8*1), X11, K3, X13            // 6233a50b73ac07f9ffffff2a
	VPSHRDQ $42, (SP), X11, K3, X13                    // 6273a50b732c242a
	VPSHRDQ $42, X14, X16, K3, X0                      // 62d3fd0373c62a
	VPSHRDQ $42, X19, X16, K3, X0                      // 62b3fd0373c32a
	VPSHRDQ $42, X8, X16, K3, X0                       // 62d3fd0373c02a
	VPSHRDQ $42, -7(DI)(R8*1), X16, K3, X0             // 62b3fd03738407f9ffffff2a
	VPSHRDQ $42, (SP), X16, K3, X0                     // 62f3fd037304242a
	VPSHRDQ $42, X14, X14, K3, X0                      // 62d38d0b73c62a
	VPSHRDQ $42, X19, X14, K3, X0                      // 62b38d0b73c32a
	VPSHRDQ $42, X8, X14, K3, X0                       // 62d38d0b73c02a
	VPSHRDQ $42, -7(DI)(R8*1), X14, K3, X0             // 62b38d0b738407f9ffffff2a
	VPSHRDQ $42, (SP), X14, K3, X0                     // 62f38d0b7304242a
	VPSHRDQ $42, X14, X11, K3, X0                      // 62d3a50b73c62a
	VPSHRDQ $42, X19, X11, K3, X0                      // 62b3a50b73c32a
	VPSHRDQ $42, X8, X11, K3, X0                       // 62d3a50b73c02a
	VPSHRDQ $42, -7(DI)(R8*1), X11, K3, X0             // 62b3a50b738407f9ffffff2a
	VPSHRDQ $42, (SP), X11, K3, X0                     // 62f3a50b7304242a
	VPSHRDQ $42, X14, X16, K3, X30                     // 6243fd0373f62a
	VPSHRDQ $42, X19, X16, K3, X30                     // 6223fd0373f32a
	VPSHRDQ $42, X8, X16, K3, X30                      // 6243fd0373f02a
	VPSHRDQ $42, -7(DI)(R8*1), X16, K3, X30            // 6223fd0373b407f9ffffff2a
	VPSHRDQ $42, (SP), X16, K3, X30                    // 6263fd037334242a
	VPSHRDQ $42, X14, X14, K3, X30                     // 62438d0b73f62a
	VPSHRDQ $42, X19, X14, K3, X30                     // 62238d0b73f32a
	VPSHRDQ $42, X8, X14, K3, X30                      // 62438d0b73f02a
	VPSHRDQ $42, -7(DI)(R8*1), X14, K3, X30            // 62238d0b73b407f9ffffff2a
	VPSHRDQ $42, (SP), X14, K3, X30                    // 62638d0b7334242a
	VPSHRDQ $42, X14, X11, K3, X30                     // 6243a50b73f62a
	VPSHRDQ $42, X19, X11, K3, X30                     // 6223a50b73f32a
	VPSHRDQ $42, X8, X11, K3, X30                      // 6243a50b73f02a
	VPSHRDQ $42, -7(DI)(R8*1), X11, K3, X30            // 6223a50b73b407f9ffffff2a
	VPSHRDQ $42, (SP), X11, K3, X30                    // 6263a50b7334242a
	VPSHRDQ $79, Y24, Y28, K2, Y21                     // 62839d2273e84f
	VPSHRDQ $79, Y13, Y28, K2, Y21                     // 62c39d2273ed4f
	VPSHRDQ $79, Y20, Y28, K2, Y21                     // 62a39d2273ec4f
	VPSHRDQ $79, (R14), Y28, K2, Y21                   // 62c39d22732e4f
	VPSHRDQ $79, -7(DI)(R8*8), Y28, K2, Y21            // 62a39d2273acc7f9ffffff4f
	VPSHRDQ $79, Y24, Y20, K2, Y21                     // 6283dd2273e84f
	VPSHRDQ $79, Y13, Y20, K2, Y21                     // 62c3dd2273ed4f
	VPSHRDQ $79, Y20, Y20, K2, Y21                     // 62a3dd2273ec4f
	VPSHRDQ $79, (R14), Y20, K2, Y21                   // 62c3dd22732e4f
	VPSHRDQ $79, -7(DI)(R8*8), Y20, K2, Y21            // 62a3dd2273acc7f9ffffff4f
	VPSHRDQ $79, Y24, Y14, K2, Y21                     // 62838d2a73e84f
	VPSHRDQ $79, Y13, Y14, K2, Y21                     // 62c38d2a73ed4f
	VPSHRDQ $79, Y20, Y14, K2, Y21                     // 62a38d2a73ec4f
	VPSHRDQ $79, (R14), Y14, K2, Y21                   // 62c38d2a732e4f
	VPSHRDQ $79, -7(DI)(R8*8), Y14, K2, Y21            // 62a38d2a73acc7f9ffffff4f
	VPSHRDQ $79, Y24, Y28, K2, Y7                      // 62939d2273f84f
	VPSHRDQ $79, Y13, Y28, K2, Y7                      // 62d39d2273fd4f
	VPSHRDQ $79, Y20, Y28, K2, Y7                      // 62b39d2273fc4f
	VPSHRDQ $79, (R14), Y28, K2, Y7                    // 62d39d22733e4f
	VPSHRDQ $79, -7(DI)(R8*8), Y28, K2, Y7             // 62b39d2273bcc7f9ffffff4f
	VPSHRDQ $79, Y24, Y20, K2, Y7                      // 6293dd2273f84f
	VPSHRDQ $79, Y13, Y20, K2, Y7                      // 62d3dd2273fd4f
	VPSHRDQ $79, Y20, Y20, K2, Y7                      // 62b3dd2273fc4f
	VPSHRDQ $79, (R14), Y20, K2, Y7                    // 62d3dd22733e4f
	VPSHRDQ $79, -7(DI)(R8*8), Y20, K2, Y7             // 62b3dd2273bcc7f9ffffff4f
	VPSHRDQ $79, Y24, Y14, K2, Y7                      // 62938d2a73f84f
	VPSHRDQ $79, Y13, Y14, K2, Y7                      // 62d38d2a73fd4f
	VPSHRDQ $79, Y20, Y14, K2, Y7                      // 62b38d2a73fc4f
	VPSHRDQ $79, (R14), Y14, K2, Y7                    // 62d38d2a733e4f
	VPSHRDQ $79, -7(DI)(R8*8), Y14, K2, Y7             // 62b38d2a73bcc7f9ffffff4f
	VPSHRDQ $79, Y24, Y28, K2, Y0                      // 62939d2273c04f
	VPSHRDQ $79, Y13, Y28, K2, Y0                      // 62d39d2273c54f
	VPSHRDQ $79, Y20, Y28, K2, Y0                      // 62b39d2273c44f
	VPSHRDQ $79, (R14), Y28, K2, Y0                    // 62d39d2273064f
	VPSHRDQ $79, -7(DI)(R8*8), Y28, K2, Y0             // 62b39d227384c7f9ffffff4f
	VPSHRDQ $79, Y24, Y20, K2, Y0                      // 6293dd2273c04f
	VPSHRDQ $79, Y13, Y20, K2, Y0                      // 62d3dd2273c54f
	VPSHRDQ $79, Y20, Y20, K2, Y0                      // 62b3dd2273c44f
	VPSHRDQ $79, (R14), Y20, K2, Y0                    // 62d3dd2273064f
	VPSHRDQ $79, -7(DI)(R8*8), Y20, K2, Y0             // 62b3dd227384c7f9ffffff4f
	VPSHRDQ $79, Y24, Y14, K2, Y0                      // 62938d2a73c04f
	VPSHRDQ $79, Y13, Y14, K2, Y0                      // 62d38d2a73c54f
	VPSHRDQ $79, Y20, Y14, K2, Y0                      // 62b38d2a73c44f
	VPSHRDQ $79, (R14), Y14, K2, Y0                    // 62d38d2a73064f
	VPSHRDQ $79, -7(DI)(R8*8), Y14, K2, Y0             // 62b38d2a7384c7f9ffffff4f
	VPSHRDQ $64, Z3, Z26, K1, Z13                      // 6273ad4173eb40
	VPSHRDQ $64, Z0, Z26, K1, Z13                      // 6273ad4173e840
	VPSHRDQ $64, -7(CX)(DX*1), Z26, K1, Z13            // 6273ad4173ac11f9ffffff40
	VPSHRDQ $64, -15(R14)(R15*4), Z26, K1, Z13         // 6213ad4173acbef1ffffff40
	VPSHRDQ $64, Z3, Z3, K1, Z13                       // 6273e54973eb40
	VPSHRDQ $64, Z0, Z3, K1, Z13                       // 6273e54973e840
	VPSHRDQ $64, -7(CX)(DX*1), Z3, K1, Z13             // 6273e54973ac11f9ffffff40
	VPSHRDQ $64, -15(R14)(R15*4), Z3, K1, Z13          // 6213e54973acbef1ffffff40
	VPSHRDQ $64, Z3, Z26, K1, Z21                      // 62e3ad4173eb40
	VPSHRDQ $64, Z0, Z26, K1, Z21                      // 62e3ad4173e840
	VPSHRDQ $64, -7(CX)(DX*1), Z26, K1, Z21            // 62e3ad4173ac11f9ffffff40
	VPSHRDQ $64, -15(R14)(R15*4), Z26, K1, Z21         // 6283ad4173acbef1ffffff40
	VPSHRDQ $64, Z3, Z3, K1, Z21                       // 62e3e54973eb40
	VPSHRDQ $64, Z0, Z3, K1, Z21                       // 62e3e54973e840
	VPSHRDQ $64, -7(CX)(DX*1), Z3, K1, Z21             // 62e3e54973ac11f9ffffff40
	VPSHRDQ $64, -15(R14)(R15*4), Z3, K1, Z21          // 6283e54973acbef1ffffff40
	VPSHRDVD X23, X12, K2, X8                          // 62321d0a73c7
	VPSHRDVD X11, X12, K2, X8                          // 62521d0a73c3
	VPSHRDVD X31, X12, K2, X8                          // 62121d0a73c7
	VPSHRDVD -7(CX), X12, K2, X8                       // 62721d0a7381f9ffffff
	VPSHRDVD 15(DX)(BX*4), X12, K2, X8                 // 62721d0a73849a0f000000
	VPSHRDVD X23, X16, K2, X8                          // 62327d0273c7
	VPSHRDVD X11, X16, K2, X8                          // 62527d0273c3
	VPSHRDVD X31, X16, K2, X8                          // 62127d0273c7
	VPSHRDVD -7(CX), X16, K2, X8                       // 62727d027381f9ffffff
	VPSHRDVD 15(DX)(BX*4), X16, K2, X8                 // 62727d0273849a0f000000
	VPSHRDVD X23, X23, K2, X8                          // 6232450273c7
	VPSHRDVD X11, X23, K2, X8                          // 6252450273c3
	VPSHRDVD X31, X23, K2, X8                          // 6212450273c7
	VPSHRDVD -7(CX), X23, K2, X8                       // 627245027381f9ffffff
	VPSHRDVD 15(DX)(BX*4), X23, K2, X8                 // 6272450273849a0f000000
	VPSHRDVD X23, X12, K2, X26                         // 62221d0a73d7
	VPSHRDVD X11, X12, K2, X26                         // 62421d0a73d3
	VPSHRDVD X31, X12, K2, X26                         // 62021d0a73d7
	VPSHRDVD -7(CX), X12, K2, X26                      // 62621d0a7391f9ffffff
	VPSHRDVD 15(DX)(BX*4), X12, K2, X26                // 62621d0a73949a0f000000
	VPSHRDVD X23, X16, K2, X26                         // 62227d0273d7
	VPSHRDVD X11, X16, K2, X26                         // 62427d0273d3
	VPSHRDVD X31, X16, K2, X26                         // 62027d0273d7
	VPSHRDVD -7(CX), X16, K2, X26                      // 62627d027391f9ffffff
	VPSHRDVD 15(DX)(BX*4), X16, K2, X26                // 62627d0273949a0f000000
	VPSHRDVD X23, X23, K2, X26                         // 6222450273d7
	VPSHRDVD X11, X23, K2, X26                         // 6242450273d3
	VPSHRDVD X31, X23, K2, X26                         // 6202450273d7
	VPSHRDVD -7(CX), X23, K2, X26                      // 626245027391f9ffffff
	VPSHRDVD 15(DX)(BX*4), X23, K2, X26                // 6262450273949a0f000000
	VPSHRDVD X23, X12, K2, X23                         // 62a21d0a73ff
	VPSHRDVD X11, X12, K2, X23                         // 62c21d0a73fb
	VPSHRDVD X31, X12, K2, X23                         // 62821d0a73ff
	VPSHRDVD -7(CX), X12, K2, X23                      // 62e21d0a73b9f9ffffff
	VPSHRDVD 15(DX)(BX*4), X12, K2, X23                // 62e21d0a73bc9a0f000000
	VPSHRDVD X23, X16, K2, X23                         // 62a27d0273ff
	VPSHRDVD X11, X16, K2, X23                         // 62c27d0273fb
	VPSHRDVD X31, X16, K2, X23                         // 62827d0273ff
	VPSHRDVD -7(CX), X16, K2, X23                      // 62e27d0273b9f9ffffff
	VPSHRDVD 15(DX)(BX*4), X16, K2, X23                // 62e27d0273bc9a0f000000
	VPSHRDVD X23, X23, K2, X23                         // 62a2450273ff
	VPSHRDVD X11, X23, K2, X23                         // 62c2450273fb
	VPSHRDVD X31, X23, K2, X23                         // 6282450273ff
	VPSHRDVD -7(CX), X23, K2, X23                      // 62e2450273b9f9ffffff
	VPSHRDVD 15(DX)(BX*4), X23, K2, X23                // 62e2450273bc9a0f000000
	VPSHRDVD Y22, Y26, K1, Y14                         // 62322d2173f6
	VPSHRDVD Y3, Y26, K1, Y14                          // 62722d2173f3
	VPSHRDVD Y15, Y26, K1, Y14                         // 62522d2173f7
	VPSHRDVD 99(R15)(R15*4), Y26, K1, Y14              // 62122d2173b4bf63000000
	VPSHRDVD 15(DX), Y26, K1, Y14                      // 62722d2173b20f000000
	VPSHRDVD Y22, Y30, K1, Y14                         // 62320d2173f6
	VPSHRDVD Y3, Y30, K1, Y14                          // 62720d2173f3
	VPSHRDVD Y15, Y30, K1, Y14                         // 62520d2173f7
	VPSHRDVD 99(R15)(R15*4), Y30, K1, Y14              // 62120d2173b4bf63000000
	VPSHRDVD 15(DX), Y30, K1, Y14                      // 62720d2173b20f000000
	VPSHRDVD Y22, Y12, K1, Y14                         // 62321d2973f6
	VPSHRDVD Y3, Y12, K1, Y14                          // 62721d2973f3
	VPSHRDVD Y15, Y12, K1, Y14                         // 62521d2973f7
	VPSHRDVD 99(R15)(R15*4), Y12, K1, Y14              // 62121d2973b4bf63000000
	VPSHRDVD 15(DX), Y12, K1, Y14                      // 62721d2973b20f000000
	VPSHRDVD Y22, Y26, K1, Y21                         // 62a22d2173ee
	VPSHRDVD Y3, Y26, K1, Y21                          // 62e22d2173eb
	VPSHRDVD Y15, Y26, K1, Y21                         // 62c22d2173ef
	VPSHRDVD 99(R15)(R15*4), Y26, K1, Y21              // 62822d2173acbf63000000
	VPSHRDVD 15(DX), Y26, K1, Y21                      // 62e22d2173aa0f000000
	VPSHRDVD Y22, Y30, K1, Y21                         // 62a20d2173ee
	VPSHRDVD Y3, Y30, K1, Y21                          // 62e20d2173eb
	VPSHRDVD Y15, Y30, K1, Y21                         // 62c20d2173ef
	VPSHRDVD 99(R15)(R15*4), Y30, K1, Y21              // 62820d2173acbf63000000
	VPSHRDVD 15(DX), Y30, K1, Y21                      // 62e20d2173aa0f000000
	VPSHRDVD Y22, Y12, K1, Y21                         // 62a21d2973ee
	VPSHRDVD Y3, Y12, K1, Y21                          // 62e21d2973eb
	VPSHRDVD Y15, Y12, K1, Y21                         // 62c21d2973ef
	VPSHRDVD 99(R15)(R15*4), Y12, K1, Y21              // 62821d2973acbf63000000
	VPSHRDVD 15(DX), Y12, K1, Y21                      // 62e21d2973aa0f000000
	VPSHRDVD Y22, Y26, K1, Y1                          // 62b22d2173ce
	VPSHRDVD Y3, Y26, K1, Y1                           // 62f22d2173cb
	VPSHRDVD Y15, Y26, K1, Y1                          // 62d22d2173cf
	VPSHRDVD 99(R15)(R15*4), Y26, K1, Y1               // 62922d21738cbf63000000
	VPSHRDVD 15(DX), Y26, K1, Y1                       // 62f22d21738a0f000000
	VPSHRDVD Y22, Y30, K1, Y1                          // 62b20d2173ce
	VPSHRDVD Y3, Y30, K1, Y1                           // 62f20d2173cb
	VPSHRDVD Y15, Y30, K1, Y1                          // 62d20d2173cf
	VPSHRDVD 99(R15)(R15*4), Y30, K1, Y1               // 62920d21738cbf63000000
	VPSHRDVD 15(DX), Y30, K1, Y1                       // 62f20d21738a0f000000
	VPSHRDVD Y22, Y12, K1, Y1                          // 62b21d2973ce
	VPSHRDVD Y3, Y12, K1, Y1                           // 62f21d2973cb
	VPSHRDVD Y15, Y12, K1, Y1                          // 62d21d2973cf
	VPSHRDVD 99(R15)(R15*4), Y12, K1, Y1               // 62921d29738cbf63000000
	VPSHRDVD 15(DX), Y12, K1, Y1                       // 62f21d29738a0f000000
	VPSHRDVD Z3, Z11, K7, Z21                          // 62e2254f73eb
	VPSHRDVD Z12, Z11, K7, Z21                         // 62c2254f73ec
	VPSHRDVD 15(DX)(BX*1), Z11, K7, Z21                // 62e2254f73ac1a0f000000
	VPSHRDVD -7(CX)(DX*2), Z11, K7, Z21                // 62e2254f73ac51f9ffffff
	VPSHRDVD Z3, Z25, K7, Z21                          // 62e2354773eb
	VPSHRDVD Z12, Z25, K7, Z21                         // 62c2354773ec
	VPSHRDVD 15(DX)(BX*1), Z25, K7, Z21                // 62e2354773ac1a0f000000
	VPSHRDVD -7(CX)(DX*2), Z25, K7, Z21                // 62e2354773ac51f9ffffff
	VPSHRDVD Z3, Z11, K7, Z13                          // 6272254f73eb
	VPSHRDVD Z12, Z11, K7, Z13                         // 6252254f73ec
	VPSHRDVD 15(DX)(BX*1), Z11, K7, Z13                // 6272254f73ac1a0f000000
	VPSHRDVD -7(CX)(DX*2), Z11, K7, Z13                // 6272254f73ac51f9ffffff
	VPSHRDVD Z3, Z25, K7, Z13                          // 6272354773eb
	VPSHRDVD Z12, Z25, K7, Z13                         // 6252354773ec
	VPSHRDVD 15(DX)(BX*1), Z25, K7, Z13                // 6272354773ac1a0f000000
	VPSHRDVD -7(CX)(DX*2), Z25, K7, Z13                // 6272354773ac51f9ffffff
	VPSHRDVQ X20, X11, K1, X24                         // 6222a50973c4
	VPSHRDVQ X5, X11, K1, X24                          // 6262a50973c5
	VPSHRDVQ X25, X11, K1, X24                         // 6202a50973c1
	VPSHRDVQ 99(R15)(R15*8), X11, K1, X24              // 6202a5097384ff63000000
	VPSHRDVQ 7(AX)(CX*8), X11, K1, X24                 // 6262a5097384c807000000
	VPSHRDVQ X20, X23, K1, X24                         // 6222c50173c4
	VPSHRDVQ X5, X23, K1, X24                          // 6262c50173c5
	VPSHRDVQ X25, X23, K1, X24                         // 6202c50173c1
	VPSHRDVQ 99(R15)(R15*8), X23, K1, X24              // 6202c5017384ff63000000
	VPSHRDVQ 7(AX)(CX*8), X23, K1, X24                 // 6262c5017384c807000000
	VPSHRDVQ X20, X2, K1, X24                          // 6222ed0973c4
	VPSHRDVQ X5, X2, K1, X24                           // 6262ed0973c5
	VPSHRDVQ X25, X2, K1, X24                          // 6202ed0973c1
	VPSHRDVQ 99(R15)(R15*8), X2, K1, X24               // 6202ed097384ff63000000
	VPSHRDVQ 7(AX)(CX*8), X2, K1, X24                  // 6262ed097384c807000000
	VPSHRDVQ X20, X11, K1, X14                         // 6232a50973f4
	VPSHRDVQ X5, X11, K1, X14                          // 6272a50973f5
	VPSHRDVQ X25, X11, K1, X14                         // 6212a50973f1
	VPSHRDVQ 99(R15)(R15*8), X11, K1, X14              // 6212a50973b4ff63000000
	VPSHRDVQ 7(AX)(CX*8), X11, K1, X14                 // 6272a50973b4c807000000
	VPSHRDVQ X20, X23, K1, X14                         // 6232c50173f4
	VPSHRDVQ X5, X23, K1, X14                          // 6272c50173f5
	VPSHRDVQ X25, X23, K1, X14                         // 6212c50173f1
	VPSHRDVQ 99(R15)(R15*8), X23, K1, X14              // 6212c50173b4ff63000000
	VPSHRDVQ 7(AX)(CX*8), X23, K1, X14                 // 6272c50173b4c807000000
	VPSHRDVQ X20, X2, K1, X14                          // 6232ed0973f4
	VPSHRDVQ X5, X2, K1, X14                           // 6272ed0973f5
	VPSHRDVQ X25, X2, K1, X14                          // 6212ed0973f1
	VPSHRDVQ 99(R15)(R15*8), X2, K1, X14               // 6212ed0973b4ff63000000
	VPSHRDVQ 7(AX)(CX*8), X2, K1, X14                  // 6272ed0973b4c807000000
	VPSHRDVQ X20, X11, K1, X0                          // 62b2a50973c4
	VPSHRDVQ X5, X11, K1, X0                           // 62f2a50973c5
	VPSHRDVQ X25, X11, K1, X0                          // 6292a50973c1
	VPSHRDVQ 99(R15)(R15*8), X11, K1, X0               // 6292a5097384ff63000000
	VPSHRDVQ 7(AX)(CX*8), X11, K1, X0                  // 62f2a5097384c807000000
	VPSHRDVQ X20, X23, K1, X0                          // 62b2c50173c4
	VPSHRDVQ X5, X23, K1, X0                           // 62f2c50173c5
	VPSHRDVQ X25, X23, K1, X0                          // 6292c50173c1
	VPSHRDVQ 99(R15)(R15*8), X23, K1, X0               // 6292c5017384ff63000000
	VPSHRDVQ 7(AX)(CX*8), X23, K1, X0                  // 62f2c5017384c807000000
	VPSHRDVQ X20, X2, K1, X0                           // 62b2ed0973c4
	VPSHRDVQ X5, X2, K1, X0                            // 62f2ed0973c5
	VPSHRDVQ X25, X2, K1, X0                           // 6292ed0973c1
	VPSHRDVQ 99(R15)(R15*8), X2, K1, X0                // 6292ed097384ff63000000
	VPSHRDVQ 7(AX)(CX*8), X2, K1, X0                   // 62f2ed097384c807000000
	VPSHRDVQ Y21, Y5, K1, Y1                           // 62b2d52973cd
	VPSHRDVQ Y7, Y5, K1, Y1                            // 62f2d52973cf
	VPSHRDVQ Y30, Y5, K1, Y1                           // 6292d52973ce
	VPSHRDVQ (CX), Y5, K1, Y1                          // 62f2d5297309
	VPSHRDVQ 99(R15), Y5, K1, Y1                       // 62d2d529738f63000000
	VPSHRDVQ Y21, Y17, K1, Y1                          // 62b2f52173cd
	VPSHRDVQ Y7, Y17, K1, Y1                           // 62f2f52173cf
	VPSHRDVQ Y30, Y17, K1, Y1                          // 6292f52173ce
	VPSHRDVQ (CX), Y17, K1, Y1                         // 62f2f5217309
	VPSHRDVQ 99(R15), Y17, K1, Y1                      // 62d2f521738f63000000
	VPSHRDVQ Y21, Y13, K1, Y1                          // 62b2952973cd
	VPSHRDVQ Y7, Y13, K1, Y1                           // 62f2952973cf
	VPSHRDVQ Y30, Y13, K1, Y1                          // 6292952973ce
	VPSHRDVQ (CX), Y13, K1, Y1                         // 62f295297309
	VPSHRDVQ 99(R15), Y13, K1, Y1                      // 62d29529738f63000000
	VPSHRDVQ Y21, Y5, K1, Y27                          // 6222d52973dd
	VPSHRDVQ Y7, Y5, K1, Y27                           // 6262d52973df
	VPSHRDVQ Y30, Y5, K1, Y27                          // 6202d52973de
	VPSHRDVQ (CX), Y5, K1, Y27                         // 6262d5297319
	VPSHRDVQ 99(R15), Y5, K1, Y27                      // 6242d529739f63000000
	VPSHRDVQ Y21, Y17, K1, Y27                         // 6222f52173dd
	VPSHRDVQ Y7, Y17, K1, Y27                          // 6262f52173df
	VPSHRDVQ Y30, Y17, K1, Y27                         // 6202f52173de
	VPSHRDVQ (CX), Y17, K1, Y27                        // 6262f5217319
	VPSHRDVQ 99(R15), Y17, K1, Y27                     // 6242f521739f63000000
	VPSHRDVQ Y21, Y13, K1, Y27                         // 6222952973dd
	VPSHRDVQ Y7, Y13, K1, Y27                          // 6262952973df
	VPSHRDVQ Y30, Y13, K1, Y27                         // 6202952973de
	VPSHRDVQ (CX), Y13, K1, Y27                        // 626295297319
	VPSHRDVQ 99(R15), Y13, K1, Y27                     // 62429529739f63000000
	VPSHRDVQ Y21, Y5, K1, Y19                          // 62a2d52973dd
	VPSHRDVQ Y7, Y5, K1, Y19                           // 62e2d52973df
	VPSHRDVQ Y30, Y5, K1, Y19                          // 6282d52973de
	VPSHRDVQ (CX), Y5, K1, Y19                         // 62e2d5297319
	VPSHRDVQ 99(R15), Y5, K1, Y19                      // 62c2d529739f63000000
	VPSHRDVQ Y21, Y17, K1, Y19                         // 62a2f52173dd
	VPSHRDVQ Y7, Y17, K1, Y19                          // 62e2f52173df
	VPSHRDVQ Y30, Y17, K1, Y19                         // 6282f52173de
	VPSHRDVQ (CX), Y17, K1, Y19                        // 62e2f5217319
	VPSHRDVQ 99(R15), Y17, K1, Y19                     // 62c2f521739f63000000
	VPSHRDVQ Y21, Y13, K1, Y19                         // 62a2952973dd
	VPSHRDVQ Y7, Y13, K1, Y19                          // 62e2952973df
	VPSHRDVQ Y30, Y13, K1, Y19                         // 6282952973de
	VPSHRDVQ (CX), Y13, K1, Y19                        // 62e295297319
	VPSHRDVQ 99(R15), Y13, K1, Y19                     // 62c29529739f63000000
	VPSHRDVQ Z23, Z23, K1, Z27                         // 6222c54173df
	VPSHRDVQ Z6, Z23, K1, Z27                          // 6262c54173de
	VPSHRDVQ -17(BP), Z23, K1, Z27                     // 6262c541739defffffff
	VPSHRDVQ -15(R14)(R15*8), Z23, K1, Z27             // 6202c541739cfef1ffffff
	VPSHRDVQ Z23, Z5, K1, Z27                          // 6222d54973df
	VPSHRDVQ Z6, Z5, K1, Z27                           // 6262d54973de
	VPSHRDVQ -17(BP), Z5, K1, Z27                      // 6262d549739defffffff
	VPSHRDVQ -15(R14)(R15*8), Z5, K1, Z27              // 6202d549739cfef1ffffff
	VPSHRDVQ Z23, Z23, K1, Z15                         // 6232c54173ff
	VPSHRDVQ Z6, Z23, K1, Z15                          // 6272c54173fe
	VPSHRDVQ -17(BP), Z23, K1, Z15                     // 6272c54173bdefffffff
	VPSHRDVQ -15(R14)(R15*8), Z23, K1, Z15             // 6212c54173bcfef1ffffff
	VPSHRDVQ Z23, Z5, K1, Z15                          // 6232d54973ff
	VPSHRDVQ Z6, Z5, K1, Z15                           // 6272d54973fe
	VPSHRDVQ -17(BP), Z5, K1, Z15                      // 6272d54973bdefffffff
	VPSHRDVQ -15(R14)(R15*8), Z5, K1, Z15              // 6212d54973bcfef1ffffff
	VPSHRDVW X2, X2, K7, X0                            // 62f2ed0f72c2
	VPSHRDVW X31, X2, K7, X0                           // 6292ed0f72c7
	VPSHRDVW X11, X2, K7, X0                           // 62d2ed0f72c3
	VPSHRDVW (AX), X2, K7, X0                          // 62f2ed0f7200
	VPSHRDVW 7(SI), X2, K7, X0                         // 62f2ed0f728607000000
	VPSHRDVW X2, X8, K7, X0                            // 62f2bd0f72c2
	VPSHRDVW X31, X8, K7, X0                           // 6292bd0f72c7
	VPSHRDVW X11, X8, K7, X0                           // 62d2bd0f72c3
	VPSHRDVW (AX), X8, K7, X0                          // 62f2bd0f7200
	VPSHRDVW 7(SI), X8, K7, X0                         // 62f2bd0f728607000000
	VPSHRDVW X2, X9, K7, X0                            // 62f2b50f72c2
	VPSHRDVW X31, X9, K7, X0                           // 6292b50f72c7
	VPSHRDVW X11, X9, K7, X0                           // 62d2b50f72c3
	VPSHRDVW (AX), X9, K7, X0                          // 62f2b50f7200
	VPSHRDVW 7(SI), X9, K7, X0                         // 62f2b50f728607000000
	VPSHRDVW X2, X2, K7, X9                            // 6272ed0f72ca
	VPSHRDVW X31, X2, K7, X9                           // 6212ed0f72cf
	VPSHRDVW X11, X2, K7, X9                           // 6252ed0f72cb
	VPSHRDVW (AX), X2, K7, X9                          // 6272ed0f7208
	VPSHRDVW 7(SI), X2, K7, X9                         // 6272ed0f728e07000000
	VPSHRDVW X2, X8, K7, X9                            // 6272bd0f72ca
	VPSHRDVW X31, X8, K7, X9                           // 6212bd0f72cf
	VPSHRDVW X11, X8, K7, X9                           // 6252bd0f72cb
	VPSHRDVW (AX), X8, K7, X9                          // 6272bd0f7208
	VPSHRDVW 7(SI), X8, K7, X9                         // 6272bd0f728e07000000
	VPSHRDVW X2, X9, K7, X9                            // 6272b50f72ca
	VPSHRDVW X31, X9, K7, X9                           // 6212b50f72cf
	VPSHRDVW X11, X9, K7, X9                           // 6252b50f72cb
	VPSHRDVW (AX), X9, K7, X9                          // 6272b50f7208
	VPSHRDVW 7(SI), X9, K7, X9                         // 6272b50f728e07000000
	VPSHRDVW X2, X2, K7, X13                           // 6272ed0f72ea
	VPSHRDVW X31, X2, K7, X13                          // 6212ed0f72ef
	VPSHRDVW X11, X2, K7, X13                          // 6252ed0f72eb
	VPSHRDVW (AX), X2, K7, X13                         // 6272ed0f7228
	VPSHRDVW 7(SI), X2, K7, X13                        // 6272ed0f72ae07000000
	VPSHRDVW X2, X8, K7, X13                           // 6272bd0f72ea
	VPSHRDVW X31, X8, K7, X13                          // 6212bd0f72ef
	VPSHRDVW X11, X8, K7, X13                          // 6252bd0f72eb
	VPSHRDVW (AX), X8, K7, X13                         // 6272bd0f7228
	VPSHRDVW 7(SI), X8, K7, X13                        // 6272bd0f72ae07000000
	VPSHRDVW X2, X9, K7, X13                           // 6272b50f72ea
	VPSHRDVW X31, X9, K7, X13                          // 6212b50f72ef
	VPSHRDVW X11, X9, K7, X13                          // 6252b50f72eb
	VPSHRDVW (AX), X9, K7, X13                         // 6272b50f7228
	VPSHRDVW 7(SI), X9, K7, X13                        // 6272b50f72ae07000000
	VPSHRDVW Y5, Y8, K2, Y13                           // 6272bd2a72ed
	VPSHRDVW Y24, Y8, K2, Y13                          // 6212bd2a72e8
	VPSHRDVW Y21, Y8, K2, Y13                          // 6232bd2a72ed
	VPSHRDVW 99(R15)(R15*2), Y8, K2, Y13               // 6212bd2a72ac7f63000000
	VPSHRDVW -7(DI), Y8, K2, Y13                       // 6272bd2a72aff9ffffff
	VPSHRDVW Y5, Y11, K2, Y13                          // 6272a52a72ed
	VPSHRDVW Y24, Y11, K2, Y13                         // 6212a52a72e8
	VPSHRDVW Y21, Y11, K2, Y13                         // 6232a52a72ed
	VPSHRDVW 99(R15)(R15*2), Y11, K2, Y13              // 6212a52a72ac7f63000000
	VPSHRDVW -7(DI), Y11, K2, Y13                      // 6272a52a72aff9ffffff
	VPSHRDVW Y5, Y24, K2, Y13                          // 6272bd2272ed
	VPSHRDVW Y24, Y24, K2, Y13                         // 6212bd2272e8
	VPSHRDVW Y21, Y24, K2, Y13                         // 6232bd2272ed
	VPSHRDVW 99(R15)(R15*2), Y24, K2, Y13              // 6212bd2272ac7f63000000
	VPSHRDVW -7(DI), Y24, K2, Y13                      // 6272bd2272aff9ffffff
	VPSHRDVW Y5, Y8, K2, Y18                           // 62e2bd2a72d5
	VPSHRDVW Y24, Y8, K2, Y18                          // 6282bd2a72d0
	VPSHRDVW Y21, Y8, K2, Y18                          // 62a2bd2a72d5
	VPSHRDVW 99(R15)(R15*2), Y8, K2, Y18               // 6282bd2a72947f63000000
	VPSHRDVW -7(DI), Y8, K2, Y18                       // 62e2bd2a7297f9ffffff
	VPSHRDVW Y5, Y11, K2, Y18                          // 62e2a52a72d5
	VPSHRDVW Y24, Y11, K2, Y18                         // 6282a52a72d0
	VPSHRDVW Y21, Y11, K2, Y18                         // 62a2a52a72d5
	VPSHRDVW 99(R15)(R15*2), Y11, K2, Y18              // 6282a52a72947f63000000
	VPSHRDVW -7(DI), Y11, K2, Y18                      // 62e2a52a7297f9ffffff
	VPSHRDVW Y5, Y24, K2, Y18                          // 62e2bd2272d5
	VPSHRDVW Y24, Y24, K2, Y18                         // 6282bd2272d0
	VPSHRDVW Y21, Y24, K2, Y18                         // 62a2bd2272d5
	VPSHRDVW 99(R15)(R15*2), Y24, K2, Y18              // 6282bd2272947f63000000
	VPSHRDVW -7(DI), Y24, K2, Y18                      // 62e2bd227297f9ffffff
	VPSHRDVW Y5, Y8, K2, Y24                           // 6262bd2a72c5
	VPSHRDVW Y24, Y8, K2, Y24                          // 6202bd2a72c0
	VPSHRDVW Y21, Y8, K2, Y24                          // 6222bd2a72c5
	VPSHRDVW 99(R15)(R15*2), Y8, K2, Y24               // 6202bd2a72847f63000000
	VPSHRDVW -7(DI), Y8, K2, Y24                       // 6262bd2a7287f9ffffff
	VPSHRDVW Y5, Y11, K2, Y24                          // 6262a52a72c5
	VPSHRDVW Y24, Y11, K2, Y24                         // 6202a52a72c0
	VPSHRDVW Y21, Y11, K2, Y24                         // 6222a52a72c5
	VPSHRDVW 99(R15)(R15*2), Y11, K2, Y24              // 6202a52a72847f63000000
	VPSHRDVW -7(DI), Y11, K2, Y24                      // 6262a52a7287f9ffffff
	VPSHRDVW Y5, Y24, K2, Y24                          // 6262bd2272c5
	VPSHRDVW Y24, Y24, K2, Y24                         // 6202bd2272c0
	VPSHRDVW Y21, Y24, K2, Y24                         // 6222bd2272c5
	VPSHRDVW 99(R15)(R15*2), Y24, K2, Y24              // 6202bd2272847f63000000
	VPSHRDVW -7(DI), Y24, K2, Y24                      // 6262bd227287f9ffffff
	VPSHRDVW Z16, Z21, K4, Z8                          // 6232d54472c0
	VPSHRDVW Z13, Z21, K4, Z8                          // 6252d54472c5
	VPSHRDVW 17(SP)(BP*2), Z21, K4, Z8                 // 6272d54472846c11000000
	VPSHRDVW -7(DI)(R8*4), Z21, K4, Z8                 // 6232d544728487f9ffffff
	VPSHRDVW Z16, Z5, K4, Z8                           // 6232d54c72c0
	VPSHRDVW Z13, Z5, K4, Z8                           // 6252d54c72c5
	VPSHRDVW 17(SP)(BP*2), Z5, K4, Z8                  // 6272d54c72846c11000000
	VPSHRDVW -7(DI)(R8*4), Z5, K4, Z8                  // 6232d54c728487f9ffffff
	VPSHRDVW Z16, Z21, K4, Z28                         // 6222d54472e0
	VPSHRDVW Z13, Z21, K4, Z28                         // 6242d54472e5
	VPSHRDVW 17(SP)(BP*2), Z21, K4, Z28                // 6262d54472a46c11000000
	VPSHRDVW -7(DI)(R8*4), Z21, K4, Z28                // 6222d54472a487f9ffffff
	VPSHRDVW Z16, Z5, K4, Z28                          // 6222d54c72e0
	VPSHRDVW Z13, Z5, K4, Z28                          // 6242d54c72e5
	VPSHRDVW 17(SP)(BP*2), Z5, K4, Z28                 // 6262d54c72a46c11000000
	VPSHRDVW -7(DI)(R8*4), Z5, K4, Z28                 // 6222d54c72a487f9ffffff
	VPSHRDW $27, X15, X0, K1, X22                      // 62c3fd0972f71b
	VPSHRDW $27, X11, X0, K1, X22                      // 62c3fd0972f31b
	VPSHRDW $27, X0, X0, K1, X22                       // 62e3fd0972f01b
	VPSHRDW $27, (BX), X0, K1, X22                     // 62e3fd0972331b
	VPSHRDW $27, -17(BP)(SI*1), X0, K1, X22            // 62e3fd0972b435efffffff1b
	VPSHRDW $27, X15, X17, K1, X22                     // 62c3f50172f71b
	VPSHRDW $27, X11, X17, K1, X22                     // 62c3f50172f31b
	VPSHRDW $27, X0, X17, K1, X22                      // 62e3f50172f01b
	VPSHRDW $27, (BX), X17, K1, X22                    // 62e3f50172331b
	VPSHRDW $27, -17(BP)(SI*1), X17, K1, X22           // 62e3f50172b435efffffff1b
	VPSHRDW $27, X15, X7, K1, X22                      // 62c3c50972f71b
	VPSHRDW $27, X11, X7, K1, X22                      // 62c3c50972f31b
	VPSHRDW $27, X0, X7, K1, X22                       // 62e3c50972f01b
	VPSHRDW $27, (BX), X7, K1, X22                     // 62e3c50972331b
	VPSHRDW $27, -17(BP)(SI*1), X7, K1, X22            // 62e3c50972b435efffffff1b
	VPSHRDW $27, X15, X0, K1, X5                       // 62d3fd0972ef1b
	VPSHRDW $27, X11, X0, K1, X5                       // 62d3fd0972eb1b
	VPSHRDW $27, X0, X0, K1, X5                        // 62f3fd0972e81b
	VPSHRDW $27, (BX), X0, K1, X5                      // 62f3fd09722b1b
	VPSHRDW $27, -17(BP)(SI*1), X0, K1, X5             // 62f3fd0972ac35efffffff1b
	VPSHRDW $27, X15, X17, K1, X5                      // 62d3f50172ef1b
	VPSHRDW $27, X11, X17, K1, X5                      // 62d3f50172eb1b
	VPSHRDW $27, X0, X17, K1, X5                       // 62f3f50172e81b
	VPSHRDW $27, (BX), X17, K1, X5                     // 62f3f501722b1b
	VPSHRDW $27, -17(BP)(SI*1), X17, K1, X5            // 62f3f50172ac35efffffff1b
	VPSHRDW $27, X15, X7, K1, X5                       // 62d3c50972ef1b
	VPSHRDW $27, X11, X7, K1, X5                       // 62d3c50972eb1b
	VPSHRDW $27, X0, X7, K1, X5                        // 62f3c50972e81b
	VPSHRDW $27, (BX), X7, K1, X5                      // 62f3c509722b1b
	VPSHRDW $27, -17(BP)(SI*1), X7, K1, X5             // 62f3c50972ac35efffffff1b
	VPSHRDW $27, X15, X0, K1, X14                      // 6253fd0972f71b
	VPSHRDW $27, X11, X0, K1, X14                      // 6253fd0972f31b
	VPSHRDW $27, X0, X0, K1, X14                       // 6273fd0972f01b
	VPSHRDW $27, (BX), X0, K1, X14                     // 6273fd0972331b
	VPSHRDW $27, -17(BP)(SI*1), X0, K1, X14            // 6273fd0972b435efffffff1b
	VPSHRDW $27, X15, X17, K1, X14                     // 6253f50172f71b
	VPSHRDW $27, X11, X17, K1, X14                     // 6253f50172f31b
	VPSHRDW $27, X0, X17, K1, X14                      // 6273f50172f01b
	VPSHRDW $27, (BX), X17, K1, X14                    // 6273f50172331b
	VPSHRDW $27, -17(BP)(SI*1), X17, K1, X14           // 6273f50172b435efffffff1b
	VPSHRDW $27, X15, X7, K1, X14                      // 6253c50972f71b
	VPSHRDW $27, X11, X7, K1, X14                      // 6253c50972f31b
	VPSHRDW $27, X0, X7, K1, X14                       // 6273c50972f01b
	VPSHRDW $27, (BX), X7, K1, X14                     // 6273c50972331b
	VPSHRDW $27, -17(BP)(SI*1), X7, K1, X14            // 6273c50972b435efffffff1b
	VPSHRDW $47, Y7, Y9, K3, Y16                       // 62e3b52b72c72f
	VPSHRDW $47, Y6, Y9, K3, Y16                       // 62e3b52b72c62f
	VPSHRDW $47, Y26, Y9, K3, Y16                      // 6283b52b72c22f
	VPSHRDW $47, -7(CX)(DX*1), Y9, K3, Y16             // 62e3b52b728411f9ffffff2f
	VPSHRDW $47, -15(R14)(R15*4), Y9, K3, Y16          // 6283b52b7284bef1ffffff2f
	VPSHRDW $47, Y7, Y6, K3, Y16                       // 62e3cd2b72c72f
	VPSHRDW $47, Y6, Y6, K3, Y16                       // 62e3cd2b72c62f
	VPSHRDW $47, Y26, Y6, K3, Y16                      // 6283cd2b72c22f
	VPSHRDW $47, -7(CX)(DX*1), Y6, K3, Y16             // 62e3cd2b728411f9ffffff2f
	VPSHRDW $47, -15(R14)(R15*4), Y6, K3, Y16          // 6283cd2b7284bef1ffffff2f
	VPSHRDW $47, Y7, Y3, K3, Y16                       // 62e3e52b72c72f
	VPSHRDW $47, Y6, Y3, K3, Y16                       // 62e3e52b72c62f
	VPSHRDW $47, Y26, Y3, K3, Y16                      // 6283e52b72c22f
	VPSHRDW $47, -7(CX)(DX*1), Y3, K3, Y16             // 62e3e52b728411f9ffffff2f
	VPSHRDW $47, -15(R14)(R15*4), Y3, K3, Y16          // 6283e52b7284bef1ffffff2f
	VPSHRDW $47, Y7, Y9, K3, Y9                        // 6273b52b72cf2f
	VPSHRDW $47, Y6, Y9, K3, Y9                        // 6273b52b72ce2f
	VPSHRDW $47, Y26, Y9, K3, Y9                       // 6213b52b72ca2f
	VPSHRDW $47, -7(CX)(DX*1), Y9, K3, Y9              // 6273b52b728c11f9ffffff2f
	VPSHRDW $47, -15(R14)(R15*4), Y9, K3, Y9           // 6213b52b728cbef1ffffff2f
	VPSHRDW $47, Y7, Y6, K3, Y9                        // 6273cd2b72cf2f
	VPSHRDW $47, Y6, Y6, K3, Y9                        // 6273cd2b72ce2f
	VPSHRDW $47, Y26, Y6, K3, Y9                       // 6213cd2b72ca2f
	VPSHRDW $47, -7(CX)(DX*1), Y6, K3, Y9              // 6273cd2b728c11f9ffffff2f
	VPSHRDW $47, -15(R14)(R15*4), Y6, K3, Y9           // 6213cd2b728cbef1ffffff2f
	VPSHRDW $47, Y7, Y3, K3, Y9                        // 6273e52b72cf2f
	VPSHRDW $47, Y6, Y3, K3, Y9                        // 6273e52b72ce2f
	VPSHRDW $47, Y26, Y3, K3, Y9                       // 6213e52b72ca2f
	VPSHRDW $47, -7(CX)(DX*1), Y3, K3, Y9              // 6273e52b728c11f9ffffff2f
	VPSHRDW $47, -15(R14)(R15*4), Y3, K3, Y9           // 6213e52b728cbef1ffffff2f
	VPSHRDW $47, Y7, Y9, K3, Y13                       // 6273b52b72ef2f
	VPSHRDW $47, Y6, Y9, K3, Y13                       // 6273b52b72ee2f
	VPSHRDW $47, Y26, Y9, K3, Y13                      // 6213b52b72ea2f
	VPSHRDW $47, -7(CX)(DX*1), Y9, K3, Y13             // 6273b52b72ac11f9ffffff2f
	VPSHRDW $47, -15(R14)(R15*4), Y9, K3, Y13          // 6213b52b72acbef1ffffff2f
	VPSHRDW $47, Y7, Y6, K3, Y13                       // 6273cd2b72ef2f
	VPSHRDW $47, Y6, Y6, K3, Y13                       // 6273cd2b72ee2f
	VPSHRDW $47, Y26, Y6, K3, Y13                      // 6213cd2b72ea2f
	VPSHRDW $47, -7(CX)(DX*1), Y6, K3, Y13             // 6273cd2b72ac11f9ffffff2f
	VPSHRDW $47, -15(R14)(R15*4), Y6, K3, Y13          // 6213cd2b72acbef1ffffff2f
	VPSHRDW $47, Y7, Y3, K3, Y13                       // 6273e52b72ef2f
	VPSHRDW $47, Y6, Y3, K3, Y13                       // 6273e52b72ee2f
	VPSHRDW $47, Y26, Y3, K3, Y13                      // 6213e52b72ea2f
	VPSHRDW $47, -7(CX)(DX*1), Y3, K3, Y13             // 6273e52b72ac11f9ffffff2f
	VPSHRDW $47, -15(R14)(R15*4), Y3, K3, Y13          // 6213e52b72acbef1ffffff2f
	VPSHRDW $82, Z6, Z22, K4, Z12                      // 6273cd4472e652
	VPSHRDW $82, Z8, Z22, K4, Z12                      // 6253cd4472e052
	VPSHRDW $82, 15(R8), Z22, K4, Z12                  // 6253cd4472a00f00000052
	VPSHRDW $82, (BP), Z22, K4, Z12                    // 6273cd4472650052
	VPSHRDW $82, Z6, Z11, K4, Z12                      // 6273a54c72e652
	VPSHRDW $82, Z8, Z11, K4, Z12                      // 6253a54c72e052
	VPSHRDW $82, 15(R8), Z11, K4, Z12                  // 6253a54c72a00f00000052
	VPSHRDW $82, (BP), Z11, K4, Z12                    // 6273a54c72650052
	VPSHRDW $82, Z6, Z22, K4, Z27                      // 6263cd4472de52
	VPSHRDW $82, Z8, Z22, K4, Z27                      // 6243cd4472d852
	VPSHRDW $82, 15(R8), Z22, K4, Z27                  // 6243cd4472980f00000052
	VPSHRDW $82, (BP), Z22, K4, Z27                    // 6263cd44725d0052
	VPSHRDW $82, Z6, Z11, K4, Z27                      // 6263a54c72de52
	VPSHRDW $82, Z8, Z11, K4, Z27                      // 6243a54c72d852
	VPSHRDW $82, 15(R8), Z11, K4, Z27                  // 6243a54c72980f00000052
	VPSHRDW $82, (BP), Z11, K4, Z27                    // 6263a54c725d0052
	RET
