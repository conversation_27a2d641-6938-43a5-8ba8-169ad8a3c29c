[!net:rsc.io] skip
[!net:github.com] skip
[!git] skip

env GO111MODULE=off

cd rsc.io/go-get-issue-10952

exec git init
exec git add foo.go
exec git config user.name Gopher
exec git config user.email <EMAIL>
exec git commit -a -m 'initial commit'
exec git remote add origin https://github.com/golang/go-get-issue-10952

exec git status

! go get -x -u rsc.io/go-get-issue-10952
stderr '^package rsc.io/go-get-issue-10952: rsc\.io/go-get-issue-10952 is a custom import path for https://github.com/rsc/go-get-issue-10952, but .* is checked out from https://github.com/golang/go-get-issue-10952$'

-- rsc.io/go-get-issue-10952/foo.go --
// Junk package to test go get.
package foo
