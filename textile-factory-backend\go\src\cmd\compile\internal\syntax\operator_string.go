// Code generated by "stringer -type Operator -linecomment tokens.go"; DO NOT EDIT.

package syntax

import "strconv"

func _() {
	// An "invalid array index" compiler error signifies that the constant values have changed.
	// Re-run the stringer command to generate them again.
	var x [1]struct{}
	_ = x[Def-1]
	_ = x[Not-2]
	_ = x[Recv-3]
	_ = x[Tilde-4]
	_ = x[OrOr-5]
	_ = x[AndAnd-6]
	_ = x[Eql-7]
	_ = x[Neq-8]
	_ = x[Lss-9]
	_ = x[Leq-10]
	_ = x[Gtr-11]
	_ = x[Geq-12]
	_ = x[Add-13]
	_ = x[Sub-14]
	_ = x[Or-15]
	_ = x[Xor-16]
	_ = x[Mul-17]
	_ = x[Div-18]
	_ = x[Rem-19]
	_ = x[And-20]
	_ = x[AndNot-21]
	_ = x[Shl-22]
	_ = x[Shr-23]
}

const _Operator_name = ":!<-~||&&==!=<<=>>=+-|^*/%&&^<<>>"

var _Operator_index = [...]uint8{0, 1, 2, 4, 5, 7, 9, 11, 13, 14, 16, 17, 19, 20, 21, 22, 23, 24, 25, 26, 27, 29, 31, 33}

func (i Operator) String() string {
	i -= 1
	if i >= Operator(len(_Operator_index)-1) {
		return "Operator(" + strconv.FormatInt(int64(i+1), 10) + ")"
	}
	return _Operator_name[_Operator_index[i]:_Operator_index[i+1]]
}
