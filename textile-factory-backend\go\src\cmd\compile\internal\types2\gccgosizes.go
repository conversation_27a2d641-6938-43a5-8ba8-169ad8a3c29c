// Copyright 2019 The Go Authors. All rights reserved.
// Use of this source code is governed by a BSD-style
// license that can be found in the LICENSE file.

// This is a copy of the file generated during the gccgo build process.
// Last update 2019-01-22.

package types2

var gccgoArchSizes = map[string]*StdSizes{
	"386":         {4, 4},
	"alpha":       {8, 8},
	"amd64":       {8, 8},
	"amd64p32":    {4, 8},
	"arm":         {4, 8},
	"armbe":       {4, 8},
	"arm64":       {8, 8},
	"arm64be":     {8, 8},
	"ia64":        {8, 8},
	"loong64":     {8, 8},
	"m68k":        {4, 2},
	"mips":        {4, 8},
	"mipsle":      {4, 8},
	"mips64":      {8, 8},
	"mips64le":    {8, 8},
	"mips64p32":   {4, 8},
	"mips64p32le": {4, 8},
	"nios2":       {4, 8},
	"ppc":         {4, 8},
	"ppc64":       {8, 8},
	"ppc64le":     {8, 8},
	"riscv":       {4, 8},
	"riscv64":     {8, 8},
	"s390":        {4, 8},
	"s390x":       {8, 8},
	"sh":          {4, 8},
	"shbe":        {4, 8},
	"sparc":       {4, 8},
	"sparc64":     {8, 8},
	"wasm":        {8, 8},
}
