env GO111MODULE=off

# Test that the user can override default code generation flags.

[compiler:gccgo] skip  # gccgo does not use -gcflags
[!cgo] skip
[!GOOS:linux] skip  # test only works if c-archive implies -shared
[short] skip

env GOCACHE=$WORK/gocache  # Looking for compile commands, so need a clean cache.
go build -x -n -buildmode=c-archive -gcflags=all=-shared=false ./override.go
stderr '^.*/compile (.* )?-shared (.* )?-shared=false'

-- override.go --
package main

import "C"

//export GoFunc
func GoFunc() {}

func main() {}
