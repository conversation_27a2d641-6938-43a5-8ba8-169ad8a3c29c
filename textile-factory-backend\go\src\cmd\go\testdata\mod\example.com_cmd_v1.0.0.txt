example.com/cmd contains main packages.

v1.0.0 is the latest non-retracted version. Other versions contain errors or
detectable problems.

-- .info --
{"Version":"v1.0.0"}
-- .mod --
module example.com/cmd

go 1.16
-- go.mod --
module example.com/cmd

go 1.16
-- a/a.go --
package main

import "fmt"

func main() { fmt.Println("a@v1.0.0") }
-- b/b.go --
package main

import "fmt"

func main() { fmt.Println("b@v1.0.0") }
-- err/err.go --
package err

var X = DoesNotCompile
