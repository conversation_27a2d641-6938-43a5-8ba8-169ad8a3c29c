# go list shows patterns and files
go list -f '{{.EmbedPatterns}}'
stdout '\[x\*t\*t\]'
go list -f '{{.EmbedFiles}}'
stdout '\[x.txt\]'
go list -test -f '{{.TestEmbedPatterns}}'
stdout '\[y\*t\*t\]'
go list -test -f '{{.TestEmbedFiles}}'
stdout '\[y.txt\]'
go list -test -f '{{.XTestEmbedPatterns}}'
stdout '\[z\*t\*t\]'
go list -test -f '{{.XTestEmbedFiles}}'
stdout '\[z.txt\]'

# build embeds x.txt
go build -x
stderr 'x.txt'

# build uses cache correctly
go build -x
! stderr 'x.txt'
cp x.txt2 x.txt
go build -x
stderr 'x.txt'

# build rejects invalid names
cp x.go2 x.go
go build -x
cp x.txt .git
! go build -x
stderr '^x.go:5:12: pattern [*]t: cannot embed file [.]git: invalid name [.]git$'
rm .git

# build rejects symlinks
[symlink] symlink x.tzt -> x.txt
[symlink] ! go build -x
[symlink] stderr 'pattern [*]t: cannot embed irregular file x.tzt'
[symlink] rm x.tzt

# build rejects empty directories
mkdir t
! go build -x
stderr '^x.go:5:12: pattern [*]t: cannot embed directory t: contains no embeddable files$'

# build ignores symlinks and invalid names in directories
cp x.txt t/.git
! go build -x
stderr '^x.go:5:12: pattern [*]t: cannot embed directory t: contains no embeddable files$'
go list -e -f '{{.Incomplete}}'
stdout 'true'
[symlink] symlink t/x.link -> ../x.txt
[symlink] ! go build -x
[symlink] stderr '^x.go:5:12: pattern [*]t: cannot embed directory t: contains no embeddable files$'

cp x.txt t/x.txt
go build -x

# build reports errors with positions in imported packages
rm t/x.txt
! go build m/use
stderr '^x.go:5:12: pattern [*]t: cannot embed directory t: contains no embeddable files$'

# all still ignores .git and symlinks
cp x.go3 x.go
! go build -x
stderr '^x.go:5:12: pattern all:t: cannot embed directory t: contains no embeddable files$'

# all finds dot files and underscore files
cp x.txt t/.x.txt
go build -x
rm t/.x.txt
cp x.txt t/_x.txt
go build -x

-- x.go --
package p

import "embed"

//go:embed x*t*t
var X embed.FS

-- x_test.go --
package p

import "embed"

//go:embed y*t*t
var Y string

-- x_x_test.go --
package p_test

import "embed"

//go:embed z*t*t
var Z string

-- x.go2 --
package p

import "embed"

//go:embed *t
var X embed.FS

-- x.go3 --
package p

import "embed"

//go:embed all:t
var X embed.FS

-- x.txt --
hello

-- y.txt --
-- z.txt --
-- x.txt2 --
not hello

-- use/use.go --
package use

import _ "m"
-- go.mod --
module m

go 1.16
