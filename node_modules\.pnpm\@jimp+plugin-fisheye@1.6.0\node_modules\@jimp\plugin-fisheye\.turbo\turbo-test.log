

> @jimp/plugin-fisheye@1.1.2 test /Users/<USER>/Documents/jimp/plugins/plugin-fisheye
> vitest "--watch=false"


[7m[1m[36m RUN [39m[22m[27m [36mv2.0.5[39m [90m/Users/<USER>/Documents/jimp/plugins/plugin-fisheye[39m

[?25l [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (2)[22m
   [32m✓[39m Fisheye[2m (2)[22m
     [32m✓[39m should create fisheye lens to image
     [32m✓[39m should create fisheye lens to image with radius
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (2)[22m
   [32m✓[39m Fisheye[2m (2)[22m
     [32m✓[39m should create fisheye lens to image
     [32m✓[39m should create fisheye lens to image with radius

[2m Test Files [22m [1m[32m1 passed[39m[22m[90m (1)[39m
[2m      Tests [22m [1m[32m2 passed[39m[22m[90m (2)[39m
[2m   Start at [22m 01:33:44
[2m   Duration [22m 2.06s[2m (transform 724ms, setup 0ms, collect 1.41s, tests 4ms, environment 0ms, prepare 145ms)[22m

[?25h[?25h
