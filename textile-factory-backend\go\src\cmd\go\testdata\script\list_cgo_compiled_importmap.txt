# Regression test for https://golang.org/issue/46462.
#
# The "runtime/cgo" import found in synthesized .go files (reported in
# the CompiledGoFiles field) should have a corresponding entry in the
# ImportMap field when a runtime/cgo variant (such as a test variant)
# will be used.

[short] skip  # -compiled can be slow (because it compiles things)
[!cgo] skip
[GOOS:darwin] skip # net package does not import "C" on Darwin
[GOOS:windows] skip # net package does not import "C" on Windows
[GOOS:plan9] skip # net package does not import "C" on Plan 9

env CGO_ENABLED=1
env GOFLAGS=-tags=netcgo  # Force net to use cgo


# "runtime/cgo [runtime.test]" appears in the test dependencies of "runtime",
# because "runtime/cgo" itself depends on "runtime"

go list -deps -test -compiled -f '{{if eq .ImportPath "net [runtime.test]"}}{{printf "%q" .Imports}}{{end}}' runtime

	# Control case: the explicitly-imported package "sync" is a test variant,
	# because "sync" depends on "runtime".
stdout '"sync \[runtime\.test\]"'
! stdout '"sync"'

	# Experiment: the implicitly-imported package "runtime/cgo" is also a test variant,
	# because "runtime/cgo" also depends on "runtime".
stdout '"runtime/cgo \[runtime\.test\]"'
! stdout '"runtime/cgo"'


# Because the import of "runtime/cgo" in the cgo-generated file actually refers
# to "runtime/cgo [runtime.test]", the latter should be listed in the ImportMap.
# BUG(#46462): Today, it is not.

go list -deps -test -compiled -f '{{if eq .ImportPath "net [runtime.test]"}}{{printf "%q" .ImportMap}}{{end}}' runtime

stdout '"sync":"sync \[runtime\.test\]"'                # control
stdout '"runtime/cgo":"runtime/cgo \[runtime\.test\]"'  # experiment
