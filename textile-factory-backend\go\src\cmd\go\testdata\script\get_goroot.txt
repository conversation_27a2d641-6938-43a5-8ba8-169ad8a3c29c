[!net:github.com] skip
env GO111MODULE=off

# Issue 4186. go get cannot be used to download packages to $GOROOT.
# Test that without GOPATH set, go get should fail.

# Fails because GOROOT=GOPATH
env GOPATH=$WORK/tmp
env GOROOT=$WORK/tmp
! go get -d github.com/golang/example/hello
stderr 'warning: GOPATH set to GOROOT'
stderr '\$GOPATH must not be set to \$GOROOT'

# Fails because GOROOT=GOPATH after cleaning.
env GOPATH=$WORK/tmp/
env GOROOT=$WORK/tmp
! go get -d github.com/golang/example/hello
stderr 'warning: GOPATH set to GOROOT'
stderr '\$GOPATH must not be set to \$GOROOT'

env GOPATH=$WORK/tmp
env GOROOT=$WORK/tmp/
! go get -d github.com/golang/example/hello
stderr 'warning: GOPATH set to GOROOT'
stderr '\$GOPATH must not be set to \$GOROOT'

# Make a home directory
mkdir $WORK/home/<USER>

# Fails because GOROOT=$HOME/go so default GOPATH unset.
[GOOS:windows] env USERPROFILE=$WORK/home
[GOOS:plan9] env home=$WORK/home
[!GOOS:windows] [!GOOS:plan9] env HOME=$WORK/home
env GOPATH=
env GOROOT=$WORK/home/<USER>
! go get -d github.com/golang/example/hello
stderr '\$GOPATH not set'

[GOOS:windows] env USERPROFILE=$WORK/home/
[GOOS:plan9] env home=$WORK/home/
[!GOOS:windows] [!GOOS:plan9] env HOME=$WORK/home/
env GOPATH=
env GOROOT=$WORK/home/<USER>
! go get -d github.com/golang/example/hello
stderr '\$GOPATH not set'

[GOOS:windows] env USERPROFILE=$WORK/home
[GOOS:plan9] env home=$WORK/home
[!GOOS:windows] [!GOOS:plan9] env HOME=$WORK/home
env GOPATH=
env GOROOT=$WORK/home/<USER>/
! go get -d github.com/golang/example/hello
stderr '\$GOPATH not set'
