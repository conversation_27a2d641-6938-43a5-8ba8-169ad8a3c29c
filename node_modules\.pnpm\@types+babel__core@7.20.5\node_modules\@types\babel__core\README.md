# Installation
> `npm install --save @types/babel__core`

# Summary
This package contains type definitions for @babel/core (https://github.com/babel/babel/tree/master/packages/babel-core).

# Details
Files were exported from https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/babel__core.

### Additional Details
 * Last updated: Mon, 20 Nov 2023 23:36:23 GMT
 * Dependencies: [@babel/parser](https://npmjs.com/package/@babel/parser), [@babel/types](https://npmjs.com/package/@babel/types), [@types/babel__generator](https://npmjs.com/package/@types/babel__generator), [@types/babel__template](https://npmjs.com/package/@types/babel__template), [@types/babel__traverse](https://npmjs.com/package/@types/babel__traverse)

# Credits
These definitions were written by [<PERSON>](https://github.com/yortus), [<PERSON>](https://github.com/marvinhagemeister), [<PERSON>](https://github.com/mg<PERSON>enhoff), [<PERSON>](https://github.com/Jessidhia), and [Ifiok Jr.](https://github.com/ifiokjr).
