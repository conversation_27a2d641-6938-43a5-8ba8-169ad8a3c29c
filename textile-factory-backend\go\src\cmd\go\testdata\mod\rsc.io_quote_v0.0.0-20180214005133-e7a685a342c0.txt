rsc.io/quote@e7a685a342

-- .mod --
module "rsc.io/quote"
-- .info --
{"Version":"v0.0.0-20180214005133-e7a685a342c0","Name":"e7a685a342c001acc3eb7f5eafa82980480042c7","Short":"e7a685a342c0","Time":"2018-02-14T00:51:33Z"}
-- go.mod --
module "rsc.io/quote"
-- quote.go --
// Copyright 2018 The Go Authors. All rights reserved.
// Use of this source code is governed by a BSD-style
// license that can be found in the LICENSE file.

// Package quote collects pithy sayings.
package quote // import "rsc.io/quote"

// Hello returns a greeting.
func Hello() string {
	return "Hello, world."
}

// Glass returns a useful phrase for world travelers.
func Glass() string {
	// See http://www.oocities.org/nodotus/hbglass.html.
	return "I can eat glass and it doesn't hurt me."
}

// Go returns a Go proverb.
func Go() string {
	return "Don't communicate by sharing memory, share memory by communicating."
}
-- quote_test.go --
// Copyright 2018 The Go Authors. All rights reserved.
// Use of this source code is governed by a BSD-style
// license that can be found in the LICENSE file.

package quote

import "testing"

func TestHello(t *testing.T) {
	hello := "Hello, world."
	if out := Hello(); out != hello {
		t.Errorf("Hello() = %q, want %q", out, hello)
	}
}

func TestGlass(t *testing.T) {
	glass := "I can eat glass and it doesn't hurt me."
	if out := Glass(); out != glass {
		t.Errorf("Glass() = %q, want %q", out, glass)
	}
}

func TestGo(t *testing.T) {
	go1 := "Don't communicate by sharing memory. Share memory by communicating."
	if out := Go(); out != go1 {
		t.Errorf("Go() = %q, want %q", out, go1)
	}
}
