# This file is autogenerated, do not edit; changes may be undone by the next 'dep ensure'.


[[projects]]
  branch = "master"
  name = "bazil.org/fuse"
  packages = [".","fs","fuseutil"]
  revision = "371fbbdaa8987b715bdd21d6adc4c9b20155f748"

[[projects]]
  branch = "master"
  name = "github.com/NYTimes/gziphandler"
  packages = ["."]
  revision = "97ae7fbaf81620fe97840685304a78a306a39c64"

[[projects]]
  branch = "master"
  name = "github.com/golang/protobuf"
  packages = ["proto"]
  revision = "1643683e1b54a9e88ad26d98f81400c8c9d9f4f9"

[[projects]]
  branch = "master"
  name = "github.com/russross/blackfriday"
  packages = ["."]
  revision = "6d1ef893fcb01b4f50cb6e57ed7df3e2e627b6b2"

[[projects]]
  branch = "master"
  name = "golang.org/x/crypto"
  packages = ["acme","acme/autocert","hkdf"]
  revision = "13931e22f9e72ea58bb73048bc752b48c6d4d4ac"

[[projects]]
  branch = "master"
  name = "golang.org/x/net"
  packages = ["context"]
  revision = "4b14673ba32bee7f5ac0f990a48f033919fd418b"

[[projects]]
  branch = "master"
  name = "golang.org/x/text"
  packages = ["cases","internal","internal/gen","internal/tag","internal/triegen","internal/ucd","language","runes","secure/bidirule","secure/precis","transform","unicode/bidi","unicode/cldr","unicode/norm","unicode/rangetable","width"]
  revision = "6eab0e8f74e86c598ec3b6fad4888e0c11482d48"

[[projects]]
  branch = "v2"
  name = "gopkg.in/yaml.v2"
  packages = ["."]
  revision = "eb3733d160e74a9c7e442f435eb3bea458e1d19f"

[solve-meta]
  analyzer-name = "dep"
  analyzer-version = 1
  inputs-digest = "2246e647ba1c78b0b9f948f9fb072fff1467284fb138709c063e99736f646b90"
  solver-name = "gps-cdcl"
  solver-version = 1
