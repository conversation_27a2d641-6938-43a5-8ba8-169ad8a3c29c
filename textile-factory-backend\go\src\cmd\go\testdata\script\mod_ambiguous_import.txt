env GO111MODULE=on

cd $WORK

# An import provided by two different modules should be flagged as an error.
! go build ./importx
stderr '^importx[/\\]importx.go:2:8: ambiguous import: found package example.com/a/x in multiple modules:\n\texample.com/a v0.1.0 \('$WORK'[/\\]a[/\\]x\)\n\texample.com/a/x v0.1.0 \('$WORK'[/\\]ax\)$'

# However, it should not be an error if that import is unused.
go build ./importy

# An import provided by both the main module and the vendor directory
# should be flagged as an error only when -mod=vendor is set.
mkdir vendor/example.com/m/importy
cp $WORK/importy/importy.go vendor/example.com/m/importy/importy.go
go build example.com/m/importy
! go build -mod=vendor example.com/m/importy
stderr '^ambiguous import: found package example.com/m/importy in multiple directories:\n\t'$WORK'[/\\]importy\n\t'$WORK'[/\\]vendor[/\\]example.com[/\\]m[/\\]importy$'

-- $WORK/go.mod --
module example.com/m
go 1.13
require (
	example.com/a v0.1.0
	example.com/a/x v0.1.0
)
replace (
	example.com/a v0.1.0 => ./a
	example.com/a/x v0.1.0 => ./ax
)
-- $WORK/importx/importx.go --
package importx
import _ "example.com/a/x"
-- $WORK/importy/importy.go --
package importy
import _ "example.com/a/y"
-- $WORK/a/go.mod --
module example.com/a
go 1.14
-- $WORK/a/x/x.go --
package x
-- $WORK/a/y/y.go --
package y
-- $WORK/ax/go.mod --
module example.com/a/x
go 1.14
-- $WORK/ax/x.go --
package x
