#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*) basedir=`cygpath -w "$basedir"`;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/mnt/d/learn/uniapptest/testvue3/node_modules/.pnpm/pixelmatch@5.3.0/node_modules/pixelmatch/bin/node_modules:/mnt/d/learn/uniapptest/testvue3/node_modules/.pnpm/pixelmatch@5.3.0/node_modules/pixelmatch/node_modules:/mnt/d/learn/uniapptest/testvue3/node_modules/.pnpm/pixelmatch@5.3.0/node_modules:/mnt/d/learn/uniapptest/testvue3/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/mnt/d/learn/uniapptest/testvue3/node_modules/.pnpm/pixelmatch@5.3.0/node_modules/pixelmatch/bin/node_modules:/mnt/d/learn/uniapptest/testvue3/node_modules/.pnpm/pixelmatch@5.3.0/node_modules/pixelmatch/node_modules:/mnt/d/learn/uniapptest/testvue3/node_modules/.pnpm/pixelmatch@5.3.0/node_modules:/mnt/d/learn/uniapptest/testvue3/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../../bin/pixelmatch" "$@"
else
  exec node  "$basedir/../../bin/pixelmatch" "$@"
fi
