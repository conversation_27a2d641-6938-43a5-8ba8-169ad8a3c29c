/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.repair-page {
  min-height: 100vh;
  background: #f5f5f5;
  padding: 0.9375rem;
  padding-bottom: 3.75rem;
}
.machine-card,
.error-card,
.status-card {
  background: #ffffff;
  border-radius: 0.5rem;
  padding: 0.9375rem;
  margin-bottom: 0.9375rem;
  box-shadow: 0 0.125rem 0.625rem rgba(0, 0, 0, 0.08);
}
.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.75rem;
}
.card-title {
  font-size: 1rem;
  font-weight: bold;
  color: #333333;
}
.machine-code {
  background: linear-gradient(135deg, #2196F3 0%, #1976D2 100%);
  padding: 0.25rem 0.5rem;
  border-radius: 0.625rem;
}
.code-text {
  font-size: 0.75rem;
  color: #ffffff;
  font-weight: 500;
}
.machine-details {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}
.detail-row {
  display: flex;
  align-items: center;
}
.detail-label {
  font-size: 0.8125rem;
  color: #666666;
  width: 5rem;
}
.detail-value {
  font-size: 0.8125rem;
  color: #333333;
  flex: 1;
}
.severity-badge,
.status-badge {
  padding: 0.25rem 0.5rem;
  border-radius: 0.625rem;
}
.severity-text,
.status-text {
  font-size: 0.6875rem;
  color: #ffffff;
  font-weight: 500;
}
.status-badge.not_started {
  background: #999999;
}
.status-badge.in_progress {
  background: #2196F3;
}
.status-badge.completed {
  background: #4CAF50;
}
.error-details {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}
.error-desc {
  padding: 0.625rem;
  background: #f8f8f8;
  border-radius: 0.375rem;
}
.desc-text {
  font-size: 0.8125rem;
  color: #333333;
  line-height: 1.5;
}
.error-meta {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}
.meta-item {
  font-size: 0.75rem;
  color: #666666;
}
.status-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.625rem;
}
.status-title {
  font-size: 1rem;
  font-weight: bold;
  color: #333333;
}
.time-info {
  display: flex;
  flex-direction: column;
  gap: 0.375rem;
  margin-bottom: 0.625rem;
}
.time-item {
  font-size: 0.75rem;
  color: #666666;
}
.action-section {
  display: flex;
  justify-content: center;
}
.start-btn {
  background: linear-gradient(135deg, #4CAF50 0%, #388E3C 100%);
  color: #ffffff;
  border: none;
  border-radius: 0.375rem;
  padding: 0.625rem 1.875rem;
  font-size: 0.875rem;
  font-weight: bold;
  box-shadow: 0 0.125rem 0.46875rem rgba(76, 175, 80, 0.3);
}
.start-btn:active {
  transform: scale(0.95);
}
.form-container {
  display: flex;
  flex-direction: column;
  gap: 0.9375rem;
}
.form-section {
  background: #ffffff;
  border-radius: 0.5rem;
  padding: 0.9375rem;
  box-shadow: 0 0.0625rem 0.3125rem rgba(0, 0, 0, 0.05);
}
.section-title {
  font-size: 0.875rem;
  font-weight: bold;
  color: #333333;
  margin-bottom: 0.625rem;
  display: block;
}
.process-input,
.parts-input,
.test-input,
.remarks-input {
  width: 100%;
  min-height: 6.25rem;
  border: 0.0625rem solid #e0e0e0;
  border-radius: 0.375rem;
  padding: 0.625rem;
  font-size: 0.8125rem;
  color: #333333;
  background: #fafafa;
  line-height: 1.5;
}
.process-input:focus,
.parts-input:focus,
.test-input:focus,
.remarks-input:focus {
  border-color: #2196F3;
  background: #ffffff;
}
.process-input[disabled],
.parts-input[disabled],
.test-input[disabled],
.remarks-input[disabled] {
  background: #f5f5f5;
  color: #999999;
}
.parts-input,
.test-input,
.remarks-input {
  min-height: 3.75rem;
}
.placeholder {
  color: #999999;
}
.char-count {
  font-size: 0.6875rem;
  color: #999999;
  text-align: right;
  margin-top: 0.375rem;
}
.result-picker {
  width: 100%;
}
.picker-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.625rem;
  border: 0.0625rem solid #e0e0e0;
  border-radius: 0.375rem;
  background: #fafafa;
}
.result-tag {
  padding: 0.25rem 0.5rem;
  border-radius: 0.625rem;
}
.result-text {
  font-size: 0.75rem;
  color: #ffffff;
  font-weight: 500;
}
.picker-arrow {
  font-size: 0.625rem;
  color: #999999;
}
.image-section {
  margin-top: 0.625rem;
}
.image-list {
  display: flex;
  flex-wrap: wrap;
  gap: 0.625rem;
}
.image-item {
  position: relative;
  width: 6.25rem;
  height: 6.25rem;
  border-radius: 0.375rem;
  overflow: hidden;
}
.image {
  width: 100%;
  height: 100%;
}
.image-remove {
  position: absolute;
  top: 0.25rem;
  right: 0.25rem;
  width: 1.25rem;
  height: 1.25rem;
  background: rgba(0, 0, 0, 0.6);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}
.remove-icon {
  font-size: 0.75rem;
  color: #ffffff;
  font-weight: bold;
}
.image-add {
  width: 6.25rem;
  height: 6.25rem;
  border: 0.0625rem dashed #cccccc;
  border-radius: 0.375rem;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background: #fafafa;
}
.image-add:active {
  background: #f0f0f0;
}
.add-icon {
  font-size: 1.5rem;
  color: #cccccc;
  margin-bottom: 0.25rem;
}
.add-text {
  font-size: 0.6875rem;
  color: #999999;
}
.submit-section {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: #ffffff;
  padding: 0.9375rem;
  box-shadow: 0 -0.0625rem 0.3125rem rgba(0, 0, 0, 0.1);
}
.complete-btn {
  width: 100%;
  height: 2.75rem;
  background: linear-gradient(135deg, #2196F3 0%, #1976D2 100%);
  color: #ffffff;
  border: none;
  border-radius: 0.375rem;
  font-size: 1rem;
  font-weight: bold;
  box-shadow: 0 0.1875rem 0.625rem rgba(33, 150, 243, 0.3);
}
.complete-btn:active {
  transform: translateY(0.0625rem);
}
.complete-btn.submitting {
  background: #cccccc;
  box-shadow: none;
}
.complete-btn[disabled] {
  background: #cccccc;
  box-shadow: none;
}