rsc.io/QUOTE v1.5.2

-- .mod --
module rsc.io/QUOTE

require rsc.io/quote v1.5.2
-- .info --
{"Version":"v1.5.2","Name":"","Short":"","Time":"2018-07-15T16:25:34Z"}
-- go.mod --
module rsc.io/QUOTE

require rsc.io/quote v1.5.2
-- QUOTE/quote.go --
// Copyright 2018 The Go Authors. All rights reserved.
// Use of this source code is governed by a BSD-style
// license that can be found in the LICENSE file.

// PACKAGE QUOTE COLLECTS LOUD SAYINGS.
package QUOTE

import (
	"strings"

	"rsc.io/quote"
)

// HELLO RETURNS A GREETING.
func HELLO() string {
	return strings.ToUpper(quote.Hello())
}

// GLASS RETURNS A USEFUL PHRASE FOR WORLD TRAVELERS.
func GLASS() string {
	return strings.ToUpper(quote.GLASS())
}

// GO RETURNS A GO PROVERB.
func GO() string {
	return strings.ToUpper(quote.GO())
}

// OPT RETURNS AN OPTIMIZATION TRUTH.
func OPT() string {
	return strings.ToUpper(quote.OPT())
}
-- QUOTE/quote_test.go --
// Copyright 2018 The Go Authors. All rights reserved.
// Use of this source code is governed by a BSD-style
// license that can be found in the LICENSE file.

package QUOTE

import (
	"os"
	"testing"
)

func init() {
	os.Setenv("LC_ALL", "en")
}

func TestHELLO(t *testing.T) {
	hello := "HELLO, WORLD"
	if out := HELLO(); out != hello {
		t.Errorf("HELLO() = %q, want %q", out, hello)
	}
}

func TestGLASS(t *testing.T) {
	glass := "I CAN EAT GLASS AND IT DOESN'T HURT ME."
	if out := GLASS(); out != glass {
		t.Errorf("GLASS() = %q, want %q", out, glass)
	}
}

func TestGO(t *testing.T) {
	go1 := "DON'T COMMUNICATE BY SHARING MEMORY, SHARE MEMORY BY COMMUNICATING."
	if out := GO(); out != go1 {
		t.Errorf("GO() = %q, want %q", out, go1)
	}
}

func TestOPT(t *testing.T) {
	opt := "IF A PROGRAM IS TOO SLOW, IT MUST HAVE A LOOP."
	if out := OPT(); out != opt {
		t.Errorf("OPT() = %q, want %q", out, opt)
	}
}
