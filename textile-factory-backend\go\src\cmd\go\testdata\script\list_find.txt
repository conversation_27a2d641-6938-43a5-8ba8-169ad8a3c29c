env GO111MODULE=off

# go list -find should not report imports

go list -f {{.Incomplete}} x/y/z...  # should probably exit non-zero but never has
stdout true
go list -find -f '{{.Incomplete}} {{.Imports}}' x/y/z...
stdout '^false \[\]'

# go list -find -compiled should use cached sources the second time it's run.
# It might not find the same cached sources as "go build", but the sources
# should be identical. "go build" derives action IDs (which are used as cache
# keys) from dependencies' action IDs. "go list -find" won't know what the
# dependencies are, so it's can't construct the same action IDs.
[short] skip
go list -find -compiled net
go list -find -compiled -x net
! stderr 'cgo'

-- x/y/z/z.go --
package z
import "does/not/exist"
