rsc.io/sampler@v1.0.0

-- .mod --
module "rsc.io/sampler"
-- .info --
{"Version":"v1.0.0","Name":"60bef405c52117ad21d2adb10872b95cf17f8fca","Short":"60bef405c521","Time":"2018-02-13T18:05:54Z"}
-- go.mod --
module "rsc.io/sampler"
-- sampler.go --
// Copyright 2018 The Go Authors. All rights reserved.
// Use of this source code is governed by a BSD-style
// license that can be found in the LICENSE file.

// Package sampler shows simple texts.
package sampler // import "rsc.io/sampler"

// Hello returns a greeting.
func Hello() string {
	return "Hello, world."
}
