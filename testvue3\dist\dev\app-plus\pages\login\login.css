/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.login-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  flex-direction: column;
  padding: 0 1.25rem;
}
.status-bar {
  height: var(--status-bar-height);
}
.header {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 2.5rem 0;
}
.header .logo {
  width: 3.75rem;
  height: 3.75rem;
  margin-bottom: 1.25rem;
}
.header .title {
  font-size: 1.5rem;
  font-weight: bold;
  color: #ffffff;
  margin-bottom: 0.625rem;
}
.header .subtitle {
  font-size: 0.875rem;
  color: rgba(255, 255, 255, 0.8);
}
.form-container {
  background: #ffffff;
  border-radius: 0.625rem;
  padding: 1.875rem 1.25rem;
  margin-bottom: 1.25rem;
  box-shadow: 0 0.3125rem 0.9375rem rgba(0, 0, 0, 0.1);
}
.form-item {
  margin-bottom: 1.25rem;
}
.form-item:last-child {
  margin-bottom: 0;
}
.form-label {
  font-size: 0.875rem;
  color: #333333;
  margin-bottom: 0.625rem;
  font-weight: 500;
}
.form-input {
  width: 100%;
  height: 2.75rem;
  border: 0.0625rem solid #e0e0e0;
  border-radius: 0.375rem;
  padding: 0 0.75rem;
  font-size: 0.875rem;
  color: #333333;
  background: #fafafa;
  box-sizing: border-box;
}
.form-input:focus {
  border-color: #2196F3;
  background: #ffffff;
}
.placeholder {
  color: #999999;
}
.login-btn {
  width: 100%;
  height: 2.75rem;
  background: linear-gradient(135deg, #2196F3 0%, #1976D2 100%);
  color: #ffffff;
  border: none;
  border-radius: 0.375rem;
  font-size: 1rem;
  font-weight: bold;
  margin-top: 1.25rem;
  box-shadow: 0 0.1875rem 0.625rem rgba(33, 150, 243, 0.3);
}
.login-btn:active {
  transform: translateY(0.0625rem);
}
.login-btn.loading {
  background: #cccccc;
  box-shadow: none;
}
.login-btn[disabled] {
  background: #cccccc;
  box-shadow: none;
}
.footer {
  text-align: center;
  padding: 1.25rem 0;
}
.footer-text {
  font-size: 0.75rem;
  color: rgba(255, 255, 255, 0.6);
}