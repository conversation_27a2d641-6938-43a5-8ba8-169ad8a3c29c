[short] skip
[!cgo] skip
[compiler:gccgo] skip # gccgo has no cover tool

# Test coverage on cgo code. This test case has an external
# test that tests the code and an in-package test file with
# no test cases.

go test -short -cover cgocover3
stdout  'coverage:.*[1-9][0-9.]+%'
! stderr '[^0-9]0\.0%'

-- go.mod --
module cgocover3

go 1.16
-- p.go --
package p

/*
void
f(void)
{
}
*/
import "C"

var b bool

func F() {
	if b {
		for {
		}
	}
	C.f()
}
-- p_test.go --
package p
-- x_test.go --
package p_test

import (
	. "cgocover3"
	"testing"
)

func TestF(t *testing.T) {
	F()
}
