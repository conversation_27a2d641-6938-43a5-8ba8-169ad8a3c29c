// Code generated by go tool dist; DO NOT EDIT.

package main

const defaultPkgConfig = `pkg-config`
func defaultCC(goos, goarch string) string {
	switch goos+`/`+goarch {
	}
	switch goos {
	case "darwin", "ios", "freebsd", "openbsd":
		return "clang"
	}
	return "gcc"
}
func defaultCXX(goos, goarch string) string {
	switch goos+`/`+goarch {
	}
	switch goos {
	case "darwin", "ios", "freebsd", "openbsd":
		return "clang++"
	}
	return "g++"
}
