# Check that files and their imports are not included in 'go list' output
# when they are excluded by build constraints.

# Linux and cgo files should be included when building in that configuration.
env GOOS=linux
env GOARCH=amd64
env CGO_ENABLED=1
go list -f '{{range .GoFiles}}{{.}} {{end}}'
stdout '^cgotag.go empty.go suffix_linux.go tag.go $'
go list -f '{{range .CgoFiles}}{{.}} {{end}}'
stdout '^cgoimport.go $'
go list -f '{{range .Imports}}{{.}} {{end}}'
stdout '^C cgoimport cgotag suffix tag $'

# Disabling cgo should exclude cgo files and their imports.
env CGO_ENABLED=0
go list -f '{{range .GoFiles}}{{.}} {{end}}'
stdout 'empty.go suffix_linux.go tag.go'
go list -f '{{range .CgoFiles}}{{.}} {{end}}'
! stdout .
go list -f '{{range .Imports}}{{.}} {{end}}'
stdout '^suffix tag $'

# Changing OS should exclude linux sources.
env GOOS=darwin
go list -f '{{range .GoFiles}}{{.}} {{end}}'
stdout '^empty.go $'
go list -f '{{range .Imports}}{{.}} {{end}}'
stdout '^$'

# Enabling a tag should include files that require it.
go list -tags=extra -f '{{range .GoFiles}}{{.}} {{end}}'
stdout '^empty.go extra.go $'
go list -tags=extra -f '{{range .Imports}}{{.}} {{end}}'
stdout '^extra $'

# Packages that require a tag should not be listed unless the tag is on.
! go list ./tagonly
go list -tags=extra ./tagonly
stdout m/tagonly

-- go.mod --
module m

go 1.13

-- empty.go --
package p

-- extra.go --
// +build extra

package p

import _ "extra"

-- suffix_linux.go --
package p

import _ "suffix"

-- tag.go --
// +build linux

package p

import _ "tag"

-- cgotag.go --
// +build cgo

package p

import _ "cgotag"

-- cgoimport.go --
package p

import "C"

import _ "cgoimport"

-- tagonly/tagonly.go --
// +build extra

package tagonly
