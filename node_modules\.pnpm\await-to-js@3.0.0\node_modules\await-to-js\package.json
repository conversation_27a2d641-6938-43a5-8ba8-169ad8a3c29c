{"name": "await-to-js", "version": "3.0.0", "description": "Async/await wrapper for easy error handling in js", "keywords": ["node", "async", "await", "async await"], "main": "dist/await-to-js.umd.js", "module": "dist/await-to-js.es5.js", "typings": "dist/types/await-to-js.d.ts", "homepage": "https://github.com/scopsy/await-to-js#readme", "files": ["dist"], "author": "<PERSON><PERSON> <<EMAIL>>, <PERSON><PERSON> <<EMAIL>>", "repository": {"type": "git", "url": "git+https://github.com/scopsy/await-to-js.git"}, "license": "MIT", "engines": {"node": ">=6.0.0"}, "scripts": {"lint": "tslint -t codeFrame 'src/**/*.ts' 'test/**/*.ts'", "prebuild": "<PERSON><PERSON><PERSON> dist", "build": "tsc && rollup -c && rimraf compiled && typedoc --out dist/docs --target es6 --theme minimal src", "start": "tsc -w & rollup -c -w", "test": "jest", "test:watch": "jest --watch", "test:prod": "npm run lint && npm run test -- --coverage --no-cache", "deploy-docs": "ts-node tools/gh-pages-publish", "report-coverage": "cat ./coverage/lcov.info | coveralls", "commit": "git-cz", "semantic-release": "semantic-release pre && npm publish && semantic-release post", "semantic-release-prepare": "ts-node tools/semantic-release-prepare", "precommit": "lint-staged"}, "lint-staged": {"{src,test}/**/*.ts": ["git add"]}, "config": {"commitizen": {"path": "node_modules/cz-conventional-changelog"}, "validate-commit-msg": {"types": "conventional-commit-types", "helpMessage": "Use \"npm run commit\" instead, we use conventional-changelog format :) (https://github.com/commitizen/cz-cli)"}}, "jest": {"transform": {".(ts|tsx)": "<rootDir>/node_modules/ts-jest/preprocessor.js"}, "testRegex": "(/__tests__/.*|\\.(test|spec))\\.(ts|tsx|js)$", "moduleFileExtensions": ["ts", "tsx", "js"], "coveragePathIgnorePatterns": ["/node_modules/", "/test/"], "coverageThreshold": {"global": {"branches": 90, "functions": 95, "lines": 95, "statements": 95}}}, "devDependencies": {"@types/jest": "^21.1.0", "@types/node": "^8.0.0", "colors": "^1.1.2", "commitizen": "^2.9.6", "coveralls": "^2.13.1", "cross-env": "^5.0.1", "cz-conventional-changelog": "^2.0.0", "husky": "^0.14.0", "jest": "^21.0.0", "lint-staged": "^4.0.0", "lodash.camelcase": "^4.3.0", "prompt": "^1.0.0", "replace-in-file": "^3.0.0-beta.2", "rimraf": "^2.6.1", "rollup": "^0.50.0", "rollup-plugin-commonjs": "^8.0.2", "rollup-plugin-node-resolve": "^3.0.0", "rollup-plugin-sourcemaps": "^0.4.2", "semantic-release": "^8.0.0", "ts-jest": "^21.0.0", "ts-node": "^3.0.6", "tslint": "^5.4.3", "tslint-config-standard": "^6.0.0", "typedoc": "^0.8.0", "typescript": "^2.3.4", "validate-commit-msg": "^2.12.2"}}