[short] skip
[compiler:gccgo] skip

# coverdep2/p1's xtest imports coverdep2/p2 which imports coverdep2/p1.
# Make sure that coverage on coverdep2/p2 recompiles coverdep2/p2.

go test -short -cover coverdep2/p1
stdout 'coverage: 100.0% of statements' # expect 100.0% coverage

-- go.mod --
module coverdep2

go 1.16
-- p1/p.go --
package p1

func F() int { return 1 }
-- p1/p_test.go --
package p1_test

import (
	"coverdep2/p2"
	"testing"
)

func Test(t *testing.T) {
	p2.F()
}
-- p2/p2.go --
package p2

import "coverdep2/p1"

func F() {
	p1.F()
}
