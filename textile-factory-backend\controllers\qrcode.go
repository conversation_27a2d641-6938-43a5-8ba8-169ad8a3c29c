package controllers

import (
	"net/http"
	"textile-factory-backend/config"
	"textile-factory-backend/models"
	"github.com/gin-gonic/gin"
)

func ScanQRCode(c *gin.Context) {
	qrCode := c.Param("qr_code")
	
	var machine models.Machine
	result := config.DB.Where("qr_code = ?", qrCode).First(&machine)
	
	if result.Error != nil {
		c.JSON(http.StatusNotFound, gin.H{
			"error": "二维码无效或机器不存在",
			"code":  "MACHINE_NOT_FOUND",
		})
		return
	}
	
	// 获取机器的当前异常数量
	var anomalyCount int64
	config.DB.Model(&models.Anomaly{}).
		Where("machine_id = ? AND status != '已完成'", machine.ID).
		Count(&anomalyCount)
	
	// 获取最新的异常记录
	var latestAnomaly models.Anomaly
	config.DB.Where("machine_id = ?", machine.ID).
		Preload("User").
		Order("created_at DESC").
		First(&latestAnomaly)
	
	response := gin.H{
		"machine":       machine,
		"anomaly_count": anomalyCount,
	}
	
	if latestAnomaly.ID > 0 {
		response["latest_anomaly"] = latestAnomaly
	}
	
	c.JSON(http.StatusOK, response)
}

func ScanForReport(c *gin.Context) {
	qrCode := c.Param("qr_code")
	
	var machine models.Machine
	result := config.DB.Where("qr_code = ?", qrCode).First(&machine)
	
	if result.Error != nil {
		c.JSON(http.StatusNotFound, gin.H{
			"error": "二维码无效或机器不存在",
			"code":  "MACHINE_NOT_FOUND",
		})
		return
	}
	
	// 检查是否有未完成的异常
	var pendingAnomaly models.Anomaly
	err := config.DB.Where("machine_id = ? AND status IN ('待维修', '维修中')", machine.ID).
		Preload("User").
		Order("created_at DESC").
		First(&pendingAnomaly).Error
	
	response := gin.H{
		"machine": machine,
		"can_report": true,
		"message": "可以上报异常",
	}
	
	if err == nil {
		response["can_report"] = false
		response["message"] = "该机器已有未完成的异常报告"
		response["pending_anomaly"] = pendingAnomaly
	}
	
	c.JSON(http.StatusOK, response)
}

func ScanForRepair(c *gin.Context) {
	qrCode := c.Param("qr_code")
	
	var machine models.Machine
	result := config.DB.Where("qr_code = ?", qrCode).First(&machine)
	
	if result.Error != nil {
		c.JSON(http.StatusNotFound, gin.H{
			"error": "二维码无效或机器不存在",
			"code":  "MACHINE_NOT_FOUND",
		})
		return
	}
	
	// 查找待维修的异常
	var pendingAnomalies []models.Anomaly
	config.DB.Where("machine_id = ? AND status = '待维修'", machine.ID).
		Preload("User").
		Order("created_at ASC").
		Find(&pendingAnomalies)
	
	// 查找维修中的异常
	var repairingAnomalies []models.Anomaly
	config.DB.Where("machine_id = ? AND status = '维修中'", machine.ID).
		Preload("User").
		Order("created_at ASC").
		Find(&repairingAnomalies)
	
	response := gin.H{
		"machine": machine,
		"pending_anomalies": pendingAnomalies,
		"repairing_anomalies": repairingAnomalies,
	}
	
	if len(pendingAnomalies) == 0 && len(repairingAnomalies) == 0 {
		response["message"] = "该机器暂无待维修或维修中的异常"
	} else {
		response["message"] = "发现异常记录，可以进行维修操作"
	}
	
	c.JSON(http.StatusOK, response)
}