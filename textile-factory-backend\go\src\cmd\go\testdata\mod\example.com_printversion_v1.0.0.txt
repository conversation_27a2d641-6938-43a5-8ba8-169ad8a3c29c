example.com/printversion v1.0.0

-- .mod --
module example.com/printversion

require example.com/version v1.0.0
replace example.com/version v1.0.0 => ../oops v0.0.0
exclude example.com/version v1.1.0
-- .info --
{"Version":"v1.0.0"}
-- go.mod --
module example.com/printversion

require example.com/version v1.0.0
replace example.com/version v1.0.0 => ../oops v0.0.0
exclude example.com/version v1.0.1
-- printversion.go --
package main

import (
	"fmt"
	"os"
	"runtime/debug"

	_ "example.com/version"
)

func main() {
	info, _ := debug.ReadBuildInfo()
	fmt.Fprintf(os.Stdout, "path is %s\n", info.Path)
	fmt.Fprintf(os.Stdout, "main is %s %s\n", info.Main.Path, info.Main.Version)
	if r := info.Main.Replace; r != nil {
		fmt.Fprintf(os.Stdout, "\t(replaced by %s %s)\n", r.Path, r.Version)
	}
	for _, m := range info.Deps {
		fmt.Fprintf(os.Stdout, "using %s %s\n", m.Path, m.Version)
		if r := m.Replace; r != nil {
			fmt.Fprintf(os.Stdout, "\t(replaced by %s %s)\n", r.Path, r.Version)
		}
	}
}
