hash: ead3ea293a6143fe41069ebec814bf197d8c43a92cc7666b1f7e21a419b46feb
updated: 2016-06-20T21:53:35.420817456Z
imports:
- name: github.com/BurntSushi/toml
  version: f0aeabca5a127c4078abb8c8d64298b147264b55
- name: github.com/cpuguy83/go-md2man
  version: a65d4d2de4d5f7c74868dfa9b202a3c8be315aaa
  subpackages:
  - md2man
- name: github.com/fsnotify/fsnotify
  version: 30411dbcefb7a1da7e84f75530ad3abe4011b4f8
- name: github.com/hashicorp/hcl
  version: da486364306ed66c218be9b7953e19173447c18b
  subpackages:
  - hcl/ast
  - hcl/parser
  - hcl/token
  - json/parser
  - hcl/scanner
  - hcl/strconv
  - json/scanner
  - json/token
- name: github.com/inconshreveable/mousetrap
  version: 76626ae9c91c4f2a10f34cad8ce83ea42c93bb75
- name: github.com/magiconair/properties
  version: c265cfa48dda6474e208715ca93e987829f572f8
- name: github.com/mitchellh/mapstructure
  version: d2dd0262208475919e1a362f675cfc0e7c10e905
- name: github.com/russross/blackfriday
  version: 1d6b8e9301e720b08a8938b8c25c018285885438
- name: github.com/shurcooL/sanitized_anchor_name
  version: 10ef21a441db47d8b13ebcc5fd2310f636973c77
- name: github.com/spf13/cast
  version: 27b586b42e29bec072fe7379259cc719e1289da6
- name: github.com/spf13/jwalterweatherman
  version: 33c24e77fb80341fe7130ee7c594256ff08ccc46
- name: github.com/spf13/pflag
  version: dabebe21bf790f782ea4c7bbd2efc430de182afd
- name: github.com/spf13/viper
  version: c1ccc378a054ea8d4e38d8c67f6938d4760b53dd
- name: golang.org/x/sys
  version: 62bee037599929a6e9146f29d10dd5208c43507d
  subpackages:
  - unix
- name: gopkg.in/yaml.v2
  version: a83829b6f1293c91addabc89d0571c246397bbf4
- name: github.com/spf13/cobra
  repo: https://github.com/dnephin/cobra
  subpackages:
  - doc
  version: v1.3
devImports: []
