/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.workspace-container {
  min-height: 100vh;
  background: #f5f5f5;
  padding: 0.625rem;
}
.welcome-section {
  background: linear-gradient(135deg, #2196F3 0%, #1976D2 100%);
  border-radius: 0.625rem;
  padding: 1.25rem;
  margin-bottom: 0.9375rem;
  box-shadow: 0 0.25rem 0.78125rem rgba(33, 150, 243, 0.3);
}
.welcome-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.welcome-info {
  display: flex;
  flex-direction: column;
}
.welcome-text {
  display: block;
  font-size: 1.125rem;
  font-weight: bold;
  color: #ffffff;
  margin-bottom: 0.3125rem;
}
.role-text {
  display: block;
  font-size: 0.875rem;
  color: rgba(255, 255, 255, 0.8);
}
.logout-btn {
  padding: 0.5rem 0.75rem;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 0.625rem;
}
.logout-btn:active {
  background: rgba(255, 255, 255, 0.3);
}
.logout-text {
  font-size: 0.75rem;
  color: #ffffff;
  font-weight: 500;
}
.stats-section {
  margin-bottom: 0.9375rem;
}
.stats-card {
  background: #ffffff;
  border-radius: 0.625rem;
  padding: 0.9375rem;
  display: flex;
  justify-content: space-between;
  box-shadow: 0 0.125rem 0.46875rem rgba(0, 0, 0, 0.1);
}
.stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  flex: 1;
}
.stat-number {
  font-size: 1.5rem;
  font-weight: bold;
  color: #2196F3;
  margin-bottom: 0.3125rem;
}
.stat-label {
  font-size: 0.75rem;
  color: #666666;
}
.quick-actions {
  margin-bottom: 0.9375rem;
}
.section-title {
  font-size: 1rem;
  font-weight: bold;
  color: #333333;
  margin-bottom: 0.625rem;
  padding: 0 0.3125rem;
}
.actions-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 0.625rem;
}
.action-item {
  background: #ffffff;
  border-radius: 0.5rem;
  padding: 0.9375rem 0.625rem;
  display: flex;
  flex-direction: column;
  align-items: center;
  box-shadow: 0 0.125rem 0.46875rem rgba(0, 0, 0, 0.1);
}
.action-item:active {
  transform: scale(0.95);
  background: #f8f8f8;
}
.action-icon {
  font-size: 1.5rem;
  margin-bottom: 0.46875rem;
}
.action-text {
  font-size: 0.75rem;
  color: #333333;
  text-align: center;
}
.recent-section {
  margin-bottom: 0.9375rem;
}
.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.625rem;
  padding: 0 0.3125rem;
}
.view-all {
  font-size: 0.75rem;
  color: #2196F3;
}
.history-list {
  background: #ffffff;
  border-radius: 0.625rem;
  overflow: hidden;
  box-shadow: 0 0.125rem 0.46875rem rgba(0, 0, 0, 0.1);
}
.history-item {
  padding: 0.9375rem;
  border-bottom: 0.03125rem solid #f0f0f0;
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.history-item:last-child {
  border-bottom: none;
}
.history-item:active {
  background: #f8f8f8;
}
.machine-info {
  display: flex;
  flex-direction: column;
}
.machine-name {
  font-size: 0.875rem;
  font-weight: 500;
  color: #333333;
  margin-bottom: 0.25rem;
}
.machine-code {
  font-size: 0.75rem;
  color: #666666;
}
.report-info {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
}
.report-time {
  font-size: 0.75rem;
  color: #666666;
  margin-bottom: 0.25rem;
}
.status-tag {
  padding: 0.25rem 0.5rem;
  border-radius: 0.375rem;
}
.status-text {
  font-size: 0.625rem;
  color: #ffffff;
  font-weight: 500;
}
.activity-list {
  background: #ffffff;
  border-radius: 0.625rem;
  overflow: hidden;
  box-shadow: 0 0.125rem 0.46875rem rgba(0, 0, 0, 0.1);
}
.activity-item {
  display: flex;
  align-items: center;
  padding: 0.9375rem;
  border-bottom: 0.03125rem solid #f0f0f0;
}
.activity-item:last-child {
  border-bottom: none;
}
.activity-icon {
  width: 1.875rem;
  height: 1.875rem;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1rem;
  margin-right: 0.9375rem;
}
.activity-content {
  flex: 1;
}
.activity-title {
  display: block;
  font-size: 0.875rem;
  color: #333333;
  margin-bottom: 0.25rem;
}
.activity-time {
  display: block;
  font-size: 0.75rem;
  color: #999999;
}