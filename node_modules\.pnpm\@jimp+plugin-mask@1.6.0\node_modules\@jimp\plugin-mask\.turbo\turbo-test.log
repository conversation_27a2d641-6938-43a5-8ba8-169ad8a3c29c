

> @jimp/plugin-mask@1.1.2 test /Users/<USER>/Documents/jimp/plugins/plugin-mask
> vitest "--watch=false"


[7m[1m[36m RUN [39m[22m[27m [36mv2.0.5[39m [90m/Users/<USER>/Documents/jimp/plugins/plugin-mask[39m

[?25l [90m·[39m [2msrc/[22mindex[2m.test.ts[22m[2m (8)[22m
   [90m·[39m Mask[2m (8)[22m
     [90m·[39m Affect opaque image with a gray mask with the same size
     [90m·[39m Affect opaque image with a gray mask with the same size, blited
     [90m·[39m Affect opaque image with a gray mask with the same size, blited negative
     [90m·[39m Affect opaque image with a smaller gray mask
     [90m·[39m Affect opaque image with a smaller gray mask, blited
     [90m·[39m Affect alpha image with a bigger gray mask
     [90m·[39m Affect alpha image with a bigger gray mask, blited
     [90m·[39m Affect opaque image with a colored mask
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (8)[22m
   [32m✓[39m Mask[2m (8)[22m
     [32m✓[39m Affect opaque image with a gray mask with the same size
     [32m✓[39m Affect opaque image with a gray mask with the same size, blited
     [32m✓[39m Affect opaque image with a gray mask with the same size, blited negative
     [32m✓[39m Affect opaque image with a smaller gray mask
     [32m✓[39m Affect opaque image with a smaller gray mask, blited
     [32m✓[39m Affect alpha image with a bigger gray mask
     [32m✓[39m Affect alpha image with a bigger gray mask, blited
     [32m✓[39m Affect opaque image with a colored mask
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (8)[22m
   [32m✓[39m Mask[2m (8)[22m
     [32m✓[39m Affect opaque image with a gray mask with the same size
     [32m✓[39m Affect opaque image with a gray mask with the same size, blited
     [32m✓[39m Affect opaque image with a gray mask with the same size, blited negative
     [32m✓[39m Affect opaque image with a smaller gray mask
     [32m✓[39m Affect opaque image with a smaller gray mask, blited
     [32m✓[39m Affect alpha image with a bigger gray mask
     [32m✓[39m Affect alpha image with a bigger gray mask, blited
     [32m✓[39m Affect opaque image with a colored mask

[2m Test Files [22m [1m[32m1 passed[39m[22m[90m (1)[39m
[2m      Tests [22m [1m[32m8 passed[39m[22m[90m (8)[39m
[2m   Start at [22m 01:33:48
[2m   Duration [22m 1.46s[2m (transform 350ms, setup 0ms, collect 598ms, tests 6ms, environment 3ms, prepare 241ms)[22m

[?25h[?25h
