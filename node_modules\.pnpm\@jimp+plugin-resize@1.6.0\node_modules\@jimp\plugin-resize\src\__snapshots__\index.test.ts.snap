// Vitest Snapshot v1, https://vitest.dev/guide/snapshot.html

exports[`Resize images > max contrast 8x8 > to BEZIER 2x5 1`] = `
Visualization:

■▩
■▩
■▩
□D
□D

Data:

00-00-00ᶠᶠ DF-DF-DFᶠᶠ
00-00-00ᶠᶠ DF-DF-DFᶠᶠ
00-00-00ᶠᶠ DF-DF-DFᶠᶠ
FF-FF-FFᶠᶠ 20-20-20ᶠᶠ
FF-FF-FFᶠᶠ 20-20-20ᶠᶠ
`;

exports[`Resize images > max contrast 8x8 > to BEZIER 4x4 1`] = `
Visualization:

■■D□
■■D□
DD3▩
□□▩■

Data:

00-00-00ᶠᶠ 00-00-00ᶠᶠ C0-C0-C0ᶠᶠ FF-FF-FFᶠᶠ
00-00-00ᶠᶠ 00-00-00ᶠᶠ C0-C0-C0ᶠᶠ FF-FF-FFᶠᶠ
C0-C0-C0ᶠᶠ C0-C0-C0ᶠᶠ 60-60-60ᶠᶠ 40-40-40ᶠᶠ
FF-FF-FFᶠᶠ FF-FF-FFᶠᶠ 40-40-40ᶠᶠ 00-00-00ᶠᶠ
`;

exports[`Resize images > max contrast 8x8 > to BICUBIC 2x5 1`] = `
Visualization:

■▩
■▩
■▩
□D
□D

Data:

00-00-00ᶠᶠ DF-DF-DFᶠᶠ
00-00-00ᶠᶠ DF-DF-DFᶠᶠ
00-00-00ᶠᶠ DF-DF-DFᶠᶠ
FF-FF-FFᶠᶠ 20-20-20ᶠᶠ
FF-FF-FFᶠᶠ 20-20-20ᶠᶠ
`;

exports[`Resize images > max contrast 8x8 > to BICUBIC 4x4 1`] = `
Visualization:

■■▥□
■■▥□
▥▥!▩
□□▩■

Data:

00-00-00ᶠᶠ 00-00-00ᶠᶠ BF-BF-BFᶠᶠ FF-FF-FFᶠᶠ
00-00-00ᶠᶠ 00-00-00ᶠᶠ BF-BF-BFᶠᶠ FF-FF-FFᶠᶠ
BF-BF-BFᶠᶠ BF-BF-BFᶠᶠ 5F-5F-5Fᶠᶠ 40-40-40ᶠᶠ
FF-FF-FFᶠᶠ FF-FF-FFᶠᶠ 40-40-40ᶠᶠ 00-00-00ᶠᶠ
`;

exports[`Resize images > max contrast 8x8 > to BILINEAR 2x5 1`] = `
Visualization:

■□
■□
3C
□■
□■

Data:

00-00-00ᶠᶠ FF-FF-FFᶠᶠ
00-00-00ᶠᶠ FF-FF-FFᶠᶠ
33-33-33ᶠᶠ CC-CC-CCᶠᶠ
FF-FF-FFᶠᶠ 00-00-00ᶠᶠ
FF-FF-FFᶠᶠ 00-00-00ᶠᶠ
`;

exports[`Resize images > max contrast 8x8 > to BILINEAR 4x4 1`] = `
Visualization:

■■□□
■■□□
□□■■
□□■■

Data:

00-00-00ᶠᶠ 00-00-00ᶠᶠ FF-FF-FFᶠᶠ FF-FF-FFᶠᶠ
00-00-00ᶠᶠ 00-00-00ᶠᶠ FF-FF-FFᶠᶠ FF-FF-FFᶠᶠ
FF-FF-FFᶠᶠ FF-FF-FFᶠᶠ 00-00-00ᶠᶠ 00-00-00ᶠᶠ
FF-FF-FFᶠᶠ FF-FF-FFᶠᶠ 00-00-00ᶠᶠ 00-00-00ᶠᶠ
`;

exports[`Resize images > max contrast 8x8 > to HERMITE 2x5 1`] = `
Visualization:

■▩
■▩
■▩
□D
□D

Data:

00-00-00ᶠᶠ DF-DF-DFᶠᶠ
00-00-00ᶠᶠ DF-DF-DFᶠᶠ
00-00-00ᶠᶠ DF-DF-DFᶠᶠ
FF-FF-FFᶠᶠ 20-20-20ᶠᶠ
FF-FF-FFᶠᶠ 20-20-20ᶠᶠ
`;

exports[`Resize images > max contrast 8x8 > to HERMITE 4x4 1`] = `
Visualization:

■■D□
■■D□
DD3▩
□□▩■

Data:

00-00-00ᶠᶠ 00-00-00ᶠᶠ C0-C0-C0ᶠᶠ FF-FF-FFᶠᶠ
00-00-00ᶠᶠ 00-00-00ᶠᶠ C0-C0-C0ᶠᶠ FF-FF-FFᶠᶠ
C0-C0-C0ᶠᶠ C0-C0-C0ᶠᶠ 60-60-60ᶠᶠ 40-40-40ᶠᶠ
FF-FF-FFᶠᶠ FF-FF-FFᶠᶠ 40-40-40ᶠᶠ 00-00-00ᶠᶠ
`;

exports[`Resize images > max contrast 8x8 > to NEAREST_NEIGHBOR 2x5 1`] = `
Visualization:

■□
■□
■□
□■
□■

Data:

00-00-00ᶠᶠ FF-FF-FFᶠᶠ
00-00-00ᶠᶠ FF-FF-FFᶠᶠ
00-00-00ᶠᶠ FF-FF-FFᶠᶠ
FF-FF-FFᶠᶠ 00-00-00ᶠᶠ
FF-FF-FFᶠᶠ 00-00-00ᶠᶠ
`;

exports[`Resize images > max contrast 8x8 > to NEAREST_NEIGHBOR 4x4 1`] = `
Visualization:

■■□□
■■□□
□□■■
□□■■

Data:

00-00-00ᶠᶠ 00-00-00ᶠᶠ FF-FF-FFᶠᶠ FF-FF-FFᶠᶠ
00-00-00ᶠᶠ 00-00-00ᶠᶠ FF-FF-FFᶠᶠ FF-FF-FFᶠᶠ
FF-FF-FFᶠᶠ FF-FF-FFᶠᶠ 00-00-00ᶠᶠ 00-00-00ᶠᶠ
FF-FF-FFᶠᶠ FF-FF-FFᶠᶠ 00-00-00ᶠᶠ 00-00-00ᶠᶠ
`;

exports[`Resize images > max contrast 8x8 > to default 2x5 1`] = `
Visualization:

■□
■□
▦▦
□■
□■

Data:

00-00-00ᶠᶠ FF-FF-FFᶠᶠ
00-00-00ᶠᶠ FF-FF-FFᶠᶠ
80-80-80ᶠᶠ 80-80-80ᶠᶠ
FF-FF-FFᶠᶠ 00-00-00ᶠᶠ
FF-FF-FFᶠᶠ 00-00-00ᶠᶠ
`;

exports[`Resize images > max contrast 8x8 > to default 4x4 1`] = `
Visualization:

■■□□
■■□□
□□■■
□□■■

Data:

00-00-00ᶠᶠ 00-00-00ᶠᶠ FF-FF-FFᶠᶠ FF-FF-FFᶠᶠ
00-00-00ᶠᶠ 00-00-00ᶠᶠ FF-FF-FFᶠᶠ FF-FF-FFᶠᶠ
FF-FF-FFᶠᶠ FF-FF-FFᶠᶠ 00-00-00ᶠᶠ 00-00-00ᶠᶠ
FF-FF-FFᶠᶠ FF-FF-FFᶠᶠ 00-00-00ᶠᶠ 00-00-00ᶠᶠ
`;

exports[`Resize images > max contrast 12x12 with dots > to BEZIER 6x6 1`] = `
Visualization:

■■■D□□
■4◇D◇□
■■■D□□
DDD3▩▩
□▴◇▩3■
□D▩▩▴■

Data:

00-00-00ᶠᶠ 00-00-00ᶠᶠ 00-00-00ᶠᶠ C0-C0-C0ᶠᶠ FF-FF-FFᶠᶠ FF-FF-FFᶠᶠ
00-00-00ᶠᶠ 44-44-44ᶠᶠ 1D-1D-1Dᶠᶠ C0-C0-C0ᶠᶠ 9F-9F-9Fᶠᶠ FF-FF-FFᶠᶠ
00-00-00ᶠᶠ 00-00-00ᶠᶠ 00-00-00ᶠᶠ C0-C0-C0ᶠᶠ FF-FF-FFᶠᶠ FF-FF-FFᶠᶠ
C0-C0-C0ᶠᶠ C0-C0-C0ᶠᶠ C0-C0-C0ᶠᶠ 60-60-60ᶠᶠ 40-40-40ᶠᶠ 40-40-40ᶠᶠ
FF-FF-FFᶠᶠ F0-F0-F0ᶠᶠ F9-F9-F9ᶠᶠ 40-40-40ᶠᶠ 15-15-15ᶠᶠ 00-00-00ᶠᶠ
FF-FF-FFᶠᶠ CA-CA-CAᶠᶠ E9-E9-E9ᶠᶠ 40-40-40ᶠᶠ 4B-4B-4Bᶠᶠ 00-00-00ᶠᶠ
`;

exports[`Resize images > max contrast 12x12 with dots > to BICUBIC 6x6 1`] = `
Visualization:

■■■▥□□
■3D▥3□
■■■▥□□
▥▥▥!▩▩
□ED▩◇■
□3▴▩▩■

Data:

00-00-00ᶠᶠ 00-00-00ᶠᶠ 00-00-00ᶠᶠ BF-BF-BFᶠᶠ FF-FF-FFᶠᶠ FF-FF-FFᶠᶠ
00-00-00ᶠᶠ 47-47-47ᶠᶠ 20-20-20ᶠᶠ BF-BF-BFᶠᶠ 97-97-97ᶠᶠ FF-FF-FFᶠᶠ
00-00-00ᶠᶠ 00-00-00ᶠᶠ 00-00-00ᶠᶠ BF-BF-BFᶠᶠ FF-FF-FFᶠᶠ FF-FF-FFᶠᶠ
BF-BF-BFᶠᶠ BF-BF-BFᶠᶠ BF-BF-BFᶠᶠ 5F-5F-5Fᶠᶠ 40-40-40ᶠᶠ 40-40-40ᶠᶠ
FF-FF-FFᶠᶠ EE-EE-EEᶠᶠ F7-F7-F7ᶠᶠ 40-40-40ᶠᶠ 18-18-18ᶠᶠ 00-00-00ᶠᶠ
FF-FF-FFᶠᶠ C9-C9-C9ᶠᶠ E6-E6-E6ᶠᶠ 40-40-40ᶠᶠ 4E-4E-4Eᶠᶠ 00-00-00ᶠᶠ
`;

exports[`Resize images > max contrast 12x12 with dots > to BILINEAR 6x6 1`] = `
Visualization:

■■■□□□
■■■□■□
■■■□□□
□□□■■■
□□□■■■
□□□■■■

Data:

00-00-00ᶠᶠ 00-00-00ᶠᶠ 00-00-00ᶠᶠ FF-FF-FFᶠᶠ FF-FF-FFᶠᶠ FF-FF-FFᶠᶠ
00-00-00ᶠᶠ 00-00-00ᶠᶠ 00-00-00ᶠᶠ FF-FF-FFᶠᶠ 00-00-00ᶠᶠ FF-FF-FFᶠᶠ
00-00-00ᶠᶠ 00-00-00ᶠᶠ 00-00-00ᶠᶠ FF-FF-FFᶠᶠ FF-FF-FFᶠᶠ FF-FF-FFᶠᶠ
FF-FF-FFᶠᶠ FF-FF-FFᶠᶠ FF-FF-FFᶠᶠ 00-00-00ᶠᶠ 00-00-00ᶠᶠ 00-00-00ᶠᶠ
FF-FF-FFᶠᶠ FF-FF-FFᶠᶠ FF-FF-FFᶠᶠ 00-00-00ᶠᶠ 00-00-00ᶠᶠ 00-00-00ᶠᶠ
FF-FF-FFᶠᶠ FF-FF-FFᶠᶠ FF-FF-FFᶠᶠ 00-00-00ᶠᶠ 00-00-00ᶠᶠ 00-00-00ᶠᶠ
`;

exports[`Resize images > max contrast 12x12 with dots > to HERMITE 6x6 1`] = `
Visualization:

■■■D□□
■▩▴D3□
■■■D□□
DDD3▩▩
□▩▴▩1■
□▩▴▩D■

Data:

00-00-00ᶠᶠ 00-00-00ᶠᶠ 00-00-00ᶠᶠ C0-C0-C0ᶠᶠ FF-FF-FFᶠᶠ FF-FF-FFᶠᶠ
00-00-00ᶠᶠ 40-40-40ᶠᶠ 19-19-19ᶠᶠ C0-C0-C0ᶠᶠ A6-A6-A6ᶠᶠ FF-FF-FFᶠᶠ
00-00-00ᶠᶠ 00-00-00ᶠᶠ 00-00-00ᶠᶠ C0-C0-C0ᶠᶠ FF-FF-FFᶠᶠ FF-FF-FFᶠᶠ
C0-C0-C0ᶠᶠ C0-C0-C0ᶠᶠ C0-C0-C0ᶠᶠ 60-60-60ᶠᶠ 40-40-40ᶠᶠ 40-40-40ᶠᶠ
FF-FF-FFᶠᶠ F3-F3-F3ᶠᶠ FA-FA-FAᶠᶠ 40-40-40ᶠᶠ 11-11-11ᶠᶠ 00-00-00ᶠᶠ
FF-FF-FFᶠᶠ CB-CB-CBᶠᶠ EB-EB-EBᶠᶠ 40-40-40ᶠᶠ 48-48-48ᶠᶠ 00-00-00ᶠᶠ
`;

exports[`Resize images > max contrast 12x12 with dots > to NEAREST_NEIGHBOR 6x6 1`] = `
Visualization:

■■■□□□
■■■□■□
■■■□□□
□□□■■■
□□□■■■
□□□■■■

Data:

00-00-00ᶠᶠ 00-00-00ᶠᶠ 00-00-00ᶠᶠ FF-FF-FFᶠᶠ FF-FF-FFᶠᶠ FF-FF-FFᶠᶠ
00-00-00ᶠᶠ 00-00-00ᶠᶠ 00-00-00ᶠᶠ FF-FF-FFᶠᶠ 00-00-00ᶠᶠ FF-FF-FFᶠᶠ
00-00-00ᶠᶠ 00-00-00ᶠᶠ 00-00-00ᶠᶠ FF-FF-FFᶠᶠ FF-FF-FFᶠᶠ FF-FF-FFᶠᶠ
FF-FF-FFᶠᶠ FF-FF-FFᶠᶠ FF-FF-FFᶠᶠ 00-00-00ᶠᶠ 00-00-00ᶠᶠ 00-00-00ᶠᶠ
FF-FF-FFᶠᶠ FF-FF-FFᶠᶠ FF-FF-FFᶠᶠ 00-00-00ᶠᶠ 00-00-00ᶠᶠ 00-00-00ᶠᶠ
FF-FF-FFᶠᶠ FF-FF-FFᶠᶠ FF-FF-FFᶠᶠ 00-00-00ᶠᶠ 00-00-00ᶠᶠ 00-00-00ᶠᶠ
`;

exports[`Resize images > max contrast 12x12 with dots > to default 6x6 1`] = `
Visualization:

■■■□□□
■▩■□▥□
■■■□□□
□□□■■■
□▥□■▩■
□□□■■■

Data:

00-00-00ᶠᶠ 00-00-00ᶠᶠ 00-00-00ᶠᶠ FF-FF-FFᶠᶠ FF-FF-FFᶠᶠ FF-FF-FFᶠᶠ
00-00-00ᶠᶠ 40-40-40ᶠᶠ 00-00-00ᶠᶠ FF-FF-FFᶠᶠ BF-BF-BFᶠᶠ FF-FF-FFᶠᶠ
00-00-00ᶠᶠ 00-00-00ᶠᶠ 00-00-00ᶠᶠ FF-FF-FFᶠᶠ FF-FF-FFᶠᶠ FF-FF-FFᶠᶠ
FF-FF-FFᶠᶠ FF-FF-FFᶠᶠ FF-FF-FFᶠᶠ 00-00-00ᶠᶠ 00-00-00ᶠᶠ 00-00-00ᶠᶠ
FF-FF-FFᶠᶠ BF-BF-BFᶠᶠ FF-FF-FFᶠᶠ 00-00-00ᶠᶠ 40-40-40ᶠᶠ 00-00-00ᶠᶠ
FF-FF-FFᶠᶠ FF-FF-FFᶠᶠ FF-FF-FFᶠᶠ 00-00-00ᶠᶠ 00-00-00ᶠᶠ 00-00-00ᶠᶠ
`;

exports[`Resize images > mutch contrast 4x4 > to BEZIER 6x6 1`] = `
Visualization:

▩D▩▦▥▩
D▩D▦▩D
▩D▩▦▥▩
▦▦▦▦▦▦
▥▩▥▦▩D
▩D▩▦D▩

Data:

40-40-40ᶠᶠ 34-34-34ᶠᶠ 40-40-40ᶠᶠ 80-80-80ᶠᶠ BF-BF-BFᶠᶠ CB-CB-CBᶠᶠ
34-34-34ᶠᶠ 26-26-26ᶠᶠ 34-34-34ᶠᶠ 80-80-80ᶠᶠ CB-CB-CBᶠᶠ D9-D9-D9ᶠᶠ
40-40-40ᶠᶠ 34-34-34ᶠᶠ 40-40-40ᶠᶠ 80-80-80ᶠᶠ BF-BF-BFᶠᶠ CB-CB-CBᶠᶠ
80-80-80ᶠᶠ 80-80-80ᶠᶠ 80-80-80ᶠᶠ 80-80-80ᶠᶠ 80-80-80ᶠᶠ 80-80-80ᶠᶠ
BF-BF-BFᶠᶠ CB-CB-CBᶠᶠ BF-BF-BFᶠᶠ 80-80-80ᶠᶠ 40-40-40ᶠᶠ 34-34-34ᶠᶠ
CB-CB-CBᶠᶠ D9-D9-D9ᶠᶠ CB-CB-CBᶠᶠ 80-80-80ᶠᶠ 34-34-34ᶠᶠ 26-26-26ᶠᶠ
`;

exports[`Resize images > mutch contrast 4x4 > to BICUBIC 6x6 1`] = `
Visualization:

▩▩▩D▥3
▩▩▩D3▴
▩▩▩D▥3
DDDDDD
▥3▥D▩▩
3▴3D▩▩

Data:

40-40-40ᶠᶠ 30-30-30ᶠᶠ 40-40-40ᶠᶠ 7F-7F-7Fᶠᶠ BF-BF-BFᶠᶠ CE-CE-CEᶠᶠ
30-30-30ᶠᶠ 1C-1C-1Cᶠᶠ 30-30-30ᶠᶠ 7F-7F-7Fᶠᶠ CE-CE-CEᶠᶠ E1-E1-E1ᶠᶠ
40-40-40ᶠᶠ 30-30-30ᶠᶠ 40-40-40ᶠᶠ 7F-7F-7Fᶠᶠ BF-BF-BFᶠᶠ CE-CE-CEᶠᶠ
7F-7F-7Fᶠᶠ 7F-7F-7Fᶠᶠ 7F-7F-7Fᶠᶠ 7F-7F-7Fᶠᶠ 7F-7F-7Fᶠᶠ 7F-7F-7Fᶠᶠ
BF-BF-BFᶠᶠ CE-CE-CEᶠᶠ BF-BF-BFᶠᶠ 7F-7F-7Fᶠᶠ 40-40-40ᶠᶠ 30-30-30ᶠᶠ
CE-CE-CEᶠᶠ E1-E1-E1ᶠᶠ CE-CE-CEᶠᶠ 7F-7F-7Fᶠᶠ 30-30-30ᶠᶠ 1C-1C-1Cᶠᶠ
`;

exports[`Resize images > mutch contrast 4x4 > to BILINEAR 6x6 1`] = `
Visualization:

▩▩3▥▥▥
▩▩3▥▥▥
33▴◇◇◇
▥▥◇▩▩▩
▥▥◇▩▩▩
▥▥◇▩▩▩

Data:

40-40-40ᶠᶠ 40-40-40ᶠᶠ 6A-6A-6Aᶠᶠ BF-BF-BFᶠᶠ BF-BF-BFᶠᶠ BF-BF-BFᶠᶠ
40-40-40ᶠᶠ 40-40-40ᶠᶠ 6A-6A-6Aᶠᶠ BF-BF-BFᶠᶠ BF-BF-BFᶠᶠ BF-BF-BFᶠᶠ
6A-6A-6Aᶠᶠ 6A-6A-6Aᶠᶠ 78-78-78ᶠᶠ 95-95-95ᶠᶠ 95-95-95ᶠᶠ 95-95-95ᶠᶠ
BF-BF-BFᶠᶠ BF-BF-BFᶠᶠ 95-95-95ᶠᶠ 40-40-40ᶠᶠ 40-40-40ᶠᶠ 40-40-40ᶠᶠ
BF-BF-BFᶠᶠ BF-BF-BFᶠᶠ 95-95-95ᶠᶠ 40-40-40ᶠᶠ 40-40-40ᶠᶠ 40-40-40ᶠᶠ
BF-BF-BFᶠᶠ BF-BF-BFᶠᶠ 95-95-95ᶠᶠ 40-40-40ᶠᶠ 40-40-40ᶠᶠ 40-40-40ᶠᶠ
`;

exports[`Resize images > mutch contrast 4x4 > to HERMITE 6x6 1`] = `
Visualization:

▩3▩▦▥◇
3D3▦◇▩
▩3▩▦▥◇
▦▦▦▦▦▦
▥◇▥▦▩3
◇▩◇▦3D

Data:

40-40-40ᶠᶠ 38-38-38ᶠᶠ 40-40-40ᶠᶠ 80-80-80ᶠᶠ BF-BF-BFᶠᶠ C7-C7-C7ᶠᶠ
38-38-38ᶠᶠ 2F-2F-2Fᶠᶠ 38-38-38ᶠᶠ 80-80-80ᶠᶠ C7-C7-C7ᶠᶠ D0-D0-D0ᶠᶠ
40-40-40ᶠᶠ 38-38-38ᶠᶠ 40-40-40ᶠᶠ 80-80-80ᶠᶠ BF-BF-BFᶠᶠ C7-C7-C7ᶠᶠ
80-80-80ᶠᶠ 80-80-80ᶠᶠ 80-80-80ᶠᶠ 80-80-80ᶠᶠ 80-80-80ᶠᶠ 80-80-80ᶠᶠ
BF-BF-BFᶠᶠ C7-C7-C7ᶠᶠ BF-BF-BFᶠᶠ 80-80-80ᶠᶠ 40-40-40ᶠᶠ 38-38-38ᶠᶠ
C7-C7-C7ᶠᶠ D0-D0-D0ᶠᶠ C7-C7-C7ᶠᶠ 80-80-80ᶠᶠ 38-38-38ᶠᶠ 2F-2F-2Fᶠᶠ
`;

exports[`Resize images > mutch contrast 4x4 > to NEAREST_NEIGHBOR 6x6 1`] = `
Visualization:

▩▩▩▥▥▥
▩▩▩▥▥▥
▩▩▩▥▥▥
▥▥▥▩▩▩
▥▥▥▩▩▩
▥▥▥▩▩▩

Data:

40-40-40ᶠᶠ 40-40-40ᶠᶠ 40-40-40ᶠᶠ BF-BF-BFᶠᶠ BF-BF-BFᶠᶠ BF-BF-BFᶠᶠ
40-40-40ᶠᶠ 40-40-40ᶠᶠ 40-40-40ᶠᶠ BF-BF-BFᶠᶠ BF-BF-BFᶠᶠ BF-BF-BFᶠᶠ
40-40-40ᶠᶠ 40-40-40ᶠᶠ 40-40-40ᶠᶠ BF-BF-BFᶠᶠ BF-BF-BFᶠᶠ BF-BF-BFᶠᶠ
BF-BF-BFᶠᶠ BF-BF-BFᶠᶠ BF-BF-BFᶠᶠ 40-40-40ᶠᶠ 40-40-40ᶠᶠ 40-40-40ᶠᶠ
BF-BF-BFᶠᶠ BF-BF-BFᶠᶠ BF-BF-BFᶠᶠ 40-40-40ᶠᶠ 40-40-40ᶠᶠ 40-40-40ᶠᶠ
BF-BF-BFᶠᶠ BF-BF-BFᶠᶠ BF-BF-BFᶠᶠ 40-40-40ᶠᶠ 40-40-40ᶠᶠ 40-40-40ᶠᶠ
`;

exports[`Resize images > mutch contrast 4x4 > to default 6x6 1`] = `
Visualization:

▩▩▩◇▥▥
▩▩▩◇▥▥
▩▩▩◇▥▥
◇◇◇▴33
▥▥▥3▩▩
▥▥▥3▩▩

Data:

40-40-40ᶠᶠ 40-40-40ᶠᶠ 40-40-40ᶠᶠ 95-95-95ᶠᶠ BF-BF-BFᶠᶠ BF-BF-BFᶠᶠ
40-40-40ᶠᶠ 40-40-40ᶠᶠ 40-40-40ᶠᶠ 95-95-95ᶠᶠ BF-BF-BFᶠᶠ BF-BF-BFᶠᶠ
40-40-40ᶠᶠ 40-40-40ᶠᶠ 40-40-40ᶠᶠ 95-95-95ᶠᶠ BF-BF-BFᶠᶠ BF-BF-BFᶠᶠ
95-95-95ᶠᶠ 95-95-95ᶠᶠ 95-95-95ᶠᶠ 78-78-78ᶠᶠ 6A-6A-6Aᶠᶠ 6A-6A-6Aᶠᶠ
BF-BF-BFᶠᶠ BF-BF-BFᶠᶠ BF-BF-BFᶠᶠ 6A-6A-6Aᶠᶠ 40-40-40ᶠᶠ 40-40-40ᶠᶠ
BF-BF-BFᶠᶠ BF-BF-BFᶠᶠ BF-BF-BFᶠᶠ 6A-6A-6Aᶠᶠ 40-40-40ᶠᶠ 40-40-40ᶠᶠ
`;
