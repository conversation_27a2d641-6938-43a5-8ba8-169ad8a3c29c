env GO111MODULE=off
[short] skip # rebuilds std for alternate architecture

cd mycmd
go build mycmd

# cross-compile install with implicit GOBIN=$GOPATH/bin can make subdirectory
env GOARCH=386
[GOAR<PERSON>:386] env GOARCH=amd64
env GOOS=linux
go install mycmd
exists $GOPATH/bin/linux_$GOARCH/mycmd

# cross-compile install with explicit GOBIN cannot make subdirectory
env GOBIN=$WORK/bin
! go install mycmd
! exists $GOBIN/linux_$GOARCH

# The install directory for a cross-compiled standard command should include GOARCH.
go list -f '{{.Target}}'  cmd/pack
stdout ${GOROOT}[/\\]pkg[/\\]tool[/\\]${GOOS}_${GOARCH}[/\\]pack$

-- mycmd/x.go --
package main
func main() {}
