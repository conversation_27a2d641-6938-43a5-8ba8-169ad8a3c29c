# Test in GOPATH mode first.
env GO111MODULE=off
cd m

# Import comment matches
go build -n works.go

# Import comment mismatch
! go build -n wrongplace.go
stderr 'wrongplace expects import "my/x"'

# Import comment syntax error
! go build -n bad.go
stderr 'cannot parse import comment'

# Import comment conflict
! go build -n conflict.go
stderr 'found import comments'


# Test in module mode.
# We ignore import comments, so these commands should succeed.
env GO111MODULE=on

# Import comment matches
go build -n works.go

# Import comment mismatch
go build -n wrongplace.go

# Import comment syntax error
go build -n bad.go

# Import comment conflict
go build -n conflict.go

-- m/go.mod --
module m

go 1.16
-- m/bad.go --
package p

import "m/bad"
-- m/conflict.go --
package p

import "m/conflict"
-- m/works.go --
package p

import _ "m/works/x"
-- m/wrongplace.go --
package p

import "m/wrongplace"
-- m/bad/bad.go --
package bad // import
-- m/conflict/a.go --
package conflict // import "a"
-- m/conflict/b.go --
package conflict /* import "b" */
-- m/works/x/x.go --
package x // import "m/works/x"
-- m/works/x/x1.go --
package x // important! not an import comment
-- m/wrongplace/x.go --
package x // import "my/x"
