[!symlink] skip

symlink $WORK/gopath/src/sym -> $WORK/gopath/src/tree
symlink $WORK/gopath/src/tree/squirrel -> $WORK/gopath/src/dir2 # this symlink should not be followed
cd sym
go list ./...
cmp stdout $WORK/gopath/src/want_list.txt
-- tree/go.mod --
module example.com/tree

go 1.20
-- tree/tree.go --
package tree
-- tree/branch/branch.go --
package branch
-- dir2/squirrel.go --
package squirrel
-- want_list.txt --
example.com/tree
example.com/tree/branch
