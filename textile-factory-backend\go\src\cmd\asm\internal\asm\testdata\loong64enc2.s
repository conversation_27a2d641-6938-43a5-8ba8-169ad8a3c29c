// Copyright 2022 The Go Authors. All rights reserved.
// Use of this source code is governed by a BSD-style
// license that can be found in the LICENSE file.

#include "../../../../../runtime/textflag.h"

TEXT asmtest(SB),DUPOK|NOSPLIT,$0
	MOVB	R4, R5			// 85e04000a5e04800
	MOVWU	R4, R5			// 85804100a5804500
	MOVW	$74565, R4		// 4402001484148d03
	MOVW	$4097, R4		// 2400001484048003
	MOVV	$74565, R4		// 4402001484148d03
	MOVV	$4097, R4		// 2400001484048003
	AND	$-1, R4, R5		// 1efcbf0285f81400
	AND	$-1, R4			// 1efcbf0284f81400
	MOVW	$-1, F4			// 1efcbf02c4a71401
	MOVW	$1, F4			// 1e048002c4a71401
	TEQ	$4, R4, R5		// 8508005c04002a00
	TEQ	$4, R4			// 0408005c04002a00
	TNE	$4, R4, R5		// 8508005804002a00
	TNE	$4, R4			// 0408005804002a00
	ADD	$65536, R4, R5		// 1e02001485781000
	ADD	$4096, R4, R5		// 3e00001485781000
	ADD	$65536, R4		// 1e02001484781000
	ADD	$4096, R4		// 3e00001484781000
	ADDV	$65536, R4, R5		// 1e02001485f81000
	ADDV	$4096, R4, R5		// 3e00001485f81000
	ADDV	$65536, R4		// 1e02001484f81000
	ADDV	$4096, R4		// 3e00001484f81000
	AND	$65536, R4, R5		// 1e02001485f81400
	AND	$4096, R4, R5		// 3e00001485f81400
	AND	$65536, R4		// 1e02001484f81400
	AND	$4096, R4		// 3e00001484f81400
	SGT	$65536, R4, R5		// 1e02001485781200
	SGT	$4096, R4, R5		// 3e00001485781200
	SGT	$65536, R4		// 1e02001484781200
	SGT	$4096, R4		// 3e00001484781200
	SGTU	$65536, R4, R5		// 1e02001485f81200
	SGTU	$4096, R4, R5		// 3e00001485f81200
	SGTU	$65536, R4		// 1e02001484f81200
	SGTU	$4096, R4		// 3e00001484f81200
	ADDU	$65536, R4, R5		// 1e02001485781000
	ADDU	$4096, R4, R5		// 3e00001485781000
	ADDU	$65536, R4		// 1e02001484781000
	ADDU	$4096, R4		// 3e00001484781000
	ADDVU	$65536, R4, R5		// 1e02001485f81000
	ADDVU	$4096, R4, R5		// 3e00001485f81000
	ADDVU	$65536, R4		// 1e02001484f81000
	ADDVU	$4096, R4		// 3e00001484f81000
	OR	$65536, R4, R5		// 1e02001485781500
	OR	$4096, R4, R5		// 3e00001485781500
	OR	$65536, R4		// 1e02001484781500
	OR	$4096, R4		// 3e00001484781500
	OR	$-1, R4, R5		// 1efcbf0285781500
	OR	$-1, R4			// 1efcbf0284781500
	XOR	$65536, R4, R5		// 1e02001485f81500
	XOR	$4096, R4, R5		// 3e00001485f81500
	XOR	$65536, R4		// 1e02001484f81500
	XOR	$4096, R4		// 3e00001484f81500
	XOR	$-1, R4, R5		// 1efcbf0285f81500
	XOR	$-1, R4			// 1efcbf0284f81500
	MOVH	R4, R5			// 85c04000a5c04800

	// relocation instructions
	MOVW	R4, name(SB)		// 1e00001ac4038029
	MOVWU	R4, name(SB)		// 1e00001ac4038029
	MOVV	R4, name(SB)		// 1e00001ac403c029
	MOVB	R4, name(SB)		// 1e00001ac4030029
	MOVBU	R4, name(SB)		// 1e00001ac4030029
	MOVF	F4, name(SB)		// 1e00001ac403402b
	MOVD	F4, name(SB)		// 1e00001ac403c02b
	MOVW	name(SB), R4		// 1e00001ac4038028
	MOVWU	name(SB), R4		// 1e00001ac403802a
	MOVV	name(SB), R4		// 1e00001ac403c028
	MOVB	name(SB), R4		// 1e00001ac4030028
	MOVBU	name(SB), R4		// 1e00001ac403002a
	MOVF	name(SB), F4		// 1e00001ac403002b
	MOVD	name(SB), F4		// 1e00001ac403802b
	MOVH	R4, name(SB)		// 1e00001ac4034029
	MOVH	name(SB), R4		// 1e00001ac4034028
	MOVHU	R4, name(SB)		// 1e00001ac4034029
	MOVHU	name(SB), R4		// 1e00001ac403402a
