# Test GOEXPERIMENT variable

# go env shows default empty GOEXPERIMENT
go env
stdout GOEXPERIMENT=

# go env shows valid experiments
env GOEXPERIMENT=fieldtrack,staticlockranking
go env GOEXPERIMENT
stdout '.*fieldtrack.*staticlockranking.*'
go env
stdout 'GOEXPERIMENT=.*fieldtrack.*staticlockranking.*'

# go env rejects unknown experiments
env GOEXPERIMENT=bad
! go env GOEXPERIMENT
stderr 'unknown GOEXPERIMENT bad'
