import type * as ts from 'typescript';
import type { createPluginCallbackSync } from './languageServicePluginCommon';
/**
 * Creates and returns a TS Service Plugin using Volar primitives.
 *
 * See https://github.com/microsoft/TypeScript/wiki/Writing-a-Language-Service-Plugin for
 * more information.
 */
export declare function createLanguageServicePlugin(createPluginCallback: createPluginCallbackSync): ts.server.PluginModuleFactory;
