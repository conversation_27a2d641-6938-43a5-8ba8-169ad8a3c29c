# Test listing with overlays

# Overlay in an existing directory
go list -overlay overlay.json  -f '{{.GoFiles}}' .
stdout '^\[f.go\]$'

# Overlays in a non-existing directory
go list -overlay overlay.json -f '{{.GoFiles}}' ./dir
stdout '^\[g.go\]$'

# Overlays in an existing directory with already existing files
go list -overlay overlay.json -f '{{.GoFiles}}' ./dir2
stdout '^\[h.go i.go\]$'

# Overlay that removes a file from a directory
! go list ./dir3 # contains a file without a package statement
go list -overlay overlay.json -f '{{.GoFiles}}' ./dir3 # overlay removes that file

# Walking through an overlay
go list -overlay overlay.json ./...
cmp stdout want-list.txt

# TODO(#39958): assembly files, C files, files that require cgo preprocessing

-- want-list.txt --
m
m/dir
m/dir2
m/dir3
-- go.mod --
// TODO(#39958): Support and test overlays including go.mod itself (especially if mod=readonly)
module m

go 1.16

-- dir2/h.go --
package dir2

-- dir3/good.go --
package dir3
-- dir3/bad.go --
// no package statement
-- overlay.json --
{
    "Replace": {
        "f.go": "overlay/f_go",
        "dir/g.go": "overlay/dir_g_go",
        "dir2/i.go": "overlay/dir2_i_go",
        "dir3/bad.go": ""
    }
}
-- overlay/f_go --
package m

func f() {
}
-- overlay/dir_g_go --
package m

func g() {
}
-- overlay/dir2_i_go --
package dir2
