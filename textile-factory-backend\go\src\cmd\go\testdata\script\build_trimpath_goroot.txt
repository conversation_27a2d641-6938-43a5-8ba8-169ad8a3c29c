# Regression test for https://go.dev/issue/51461 and https://go.dev/issue/51483.
#
# When built with -trimpath, runtime.GOROOT() returned the bogus string "go"
# if GOROOT was not set explicitly in the environment.
# It should instead return the empty string, since we know that we don't
# have a valid path to return.
#
# TODO(#51483): when runtime.GOROOT() returns the empty string,
# go/build should default to 'go env GOROOT' instead.

env GOROOT_FINAL=

[trimpath] env GOROOT=
[trimpath] ! go env GOROOT
[trimpath] stderr '^go: cannot find GOROOT directory: ''go'' binary is trimmed and GOROOT is not set$'
[trimpath] env GOROOT=$TESTGO_GOROOT

[short] stop

# With GOROOT still set but GOROOT_FINAL unset, 'go build' and 'go test -c'
# should cause runtime.GOROOT() to report either the correct GOROOT
# (without -trimpath) or no GOROOT at all (with -trimpath).

go build -o example.exe .
go build -trimpath -o example-trimpath.exe .
go test -c -o example.test.exe .
go test -trimpath -c -o example.test-trimpath.exe .

env GOROOT=

exec ./example.exe
stdout '^GOROOT '$TESTGO_GOROOT'$'
stdout '^runtime '$TESTGO_GOROOT${/}src${/}runtime'$'

! exec ./example-trimpath.exe
stdout '^GOROOT $'
stderr 'cannot find package "runtime" in any of:\n\t\(\$GOROOT not set\)\n\t'$WORK${/}gopath${/}src${/}runtime' \(from \$GOPATH\)\n\z'

exec ./example.test.exe -test.v
stdout '^GOROOT '$TESTGO_GOROOT'$'
stdout '^runtime '$TESTGO_GOROOT${/}src${/}runtime'$'

! exec ./example.test-trimpath.exe -test.v
stdout '^GOROOT $'
stderr 'cannot find package "runtime" in any of:\n\t\(\$GOROOT not set\)\n\t'$WORK${/}gopath${/}src${/}runtime' \(from \$GOPATH\)$'

# If a correct GOROOT is baked in to the 'go' command itself, 'go run' and
# 'go test' should not implicitly set GOROOT in the process environment
# (because that could mask an unexpected production dependency on the GOROOT
# environment variable), but 'go generate' should (because the generator may
# reasonably expect to be able to locate the GOROOT for which it is generating
# code).

[trimpath] stop
[mismatched-goroot] stop

! go run -trimpath .
stdout '^GOROOT $'
stderr 'cannot find package "runtime" in any of:\n\t\(\$GOROOT not set\)\n\t'$WORK${/}gopath${/}src${/}runtime' \(from \$GOPATH\)\nexit status 1\n\z'

! go test -trimpath -v .
stdout '^GOROOT $'
stdout 'cannot find package "runtime" in any of:\n\t\(\$GOROOT not set\)\n\t'$WORK${/}gopath${/}src${/}runtime' \(from \$GOPATH\)$'

env GOFLAGS=-trimpath
go generate .
stdout '^GOROOT '$TESTGO_GOROOT'$'
stdout '^runtime '$TESTGO_GOROOT${/}src${/}runtime'$'

-- go.mod --
module example

go 1.19
-- main.go --
package main

//go:generate go run .

import (
	"fmt"
	"go/build"
	"os"
	"runtime"
)

func main() {
	fmt.Println("GOROOT", runtime.GOROOT())

	p, err := build.Default.Import("runtime", "", build.FindOnly)
	if err != nil {
		fmt.Fprintln(os.Stderr, err)
		os.Exit(1)
	}
	fmt.Println("runtime", p.Dir)
}
-- main_test.go --
package main

import "testing"

func TestMain(*testing.M) {
	main()
}
