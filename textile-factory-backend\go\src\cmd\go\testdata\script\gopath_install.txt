# Regression test for 'go install' locations in GOPATH mode.
env GO111MODULE=off
[short] skip

# Without $GOBIN set, binaries should be installed into the GOPATH bin directory.
env GOBIN=
rm $GOPATH/bin/go-cmd-test$GOEXE
go install go-cmd-test
exists $GOPATH/bin/go-cmd-test$GOEXE

# With $GOBIN set, binaries should be installed to $GOBIN.
env GOBIN=$WORK/bin1
mkdir -p $GOBIN
go install go-cmd-test
exists $GOBIN/go-cmd-test$GOEXE

# Issue 11065: installing to the current directory should create an executable.
cd go-cmd-test
env GOBIN=$PWD
go install
exists ./go-cmd-test$GOEXE
cd ..

# Without $GOBIN set, installing a program outside $GOPATH should fail
# (there is nowhere to install it).
env GOPATH= # reset to default ($HOME/go, which does not exist)
env GOBIN=
! go install go-cmd-test/helloworld.go
stderr '^go: no install location for \.go files listed on command line \(<PERSON><PERSON><PERSON> not set\)$'

# With $GOBIN set, should install there.
env GOBIN=$WORK/bin1
go install go-cmd-test/helloworld.go
exists $GOBIN/helloworld$GOEXE

# We can't assume that we can write to GOROOT, because it may not be writable.
# However, we can check its install location using 'go list'.
# cmd/fix should be installed to GOROOT/pkg, not GOPATH/bin.
env GOPATH=$PWD
go list -f '{{.Target}}' cmd/fix
stdout $GOROOT'[/\\]pkg[/\\]tool[/\\]'$GOOS'_'$GOARCH'[/\\]fix'$GOEXE'$'

# GOBIN should not affect toolchain install locations.
env GOBIN=$WORK/bin1
go list -f '{{.Target}}' cmd/fix
stdout $GOROOT'[/\\]pkg[/\\]tool[/\\]'$GOOS'_'$GOARCH'[/\\]fix'$GOEXE'$'

-- go-cmd-test/helloworld.go --
package main

func main() {
	println("hello world")
}
