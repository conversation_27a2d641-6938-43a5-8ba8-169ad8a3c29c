! go list -export ./...
stderr '^# example.com/p2\np2'${/}'main\.go:7:.*'
! stderr '^go build '

go list -f '{{with .Error}}{{.}}{{end}}' -e -export ./...
! stderr '.'
stdout '^# example.com/p2\np2'${/}'main\.go:7:.*'

go list -e -export -json=Error ./...
stdout '"Err": "# example.com/p2'

-- go.mod --
module example.com
-- p1/p1.go --
package p1

const Name = "p1"
-- p2/main.go --
package main

import "fmt"
import "example.com/p1"

func main() {
	fmt.Println(p1.Name == 5)
}
