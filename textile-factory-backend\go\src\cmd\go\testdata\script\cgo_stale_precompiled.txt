# Regression test for https://go.dev/issue/47215 and https://go.dev/issue/50183:
# A mismatched $GOROOT_FINAL or missing $CC caused the C dependencies of the net
# package to appear stale, and it could not be rebuilt due to a missing $CC.

[!cgo] skip

# This test may start with the runtime/cgo package already stale.
# Explicitly rebuild it to ensure that it is cached.
# (See https://go.dev/issue/50892.)
#
# If running in non-short mode, explicitly vary CGO_CFLAGS
# as a control case (to ensure that our regexps do catch rebuilds).

[!short] env GOCACHE=$WORK/cache
[!short] env CGO_CFLAGS=-DTestScript_cgo_stale_precompiled=true
go build -x runtime/cgo
[!short] stderr '[/\\]cgo'$GOEXE'["]? .* -importpath runtime/cgo'

# https://go.dev/issue/50183: a mismatched GOROOT_FINAL caused net to be stale.
env oldGOROOT_FINAL=$GOROOT_FINAL
env GOROOT_FINAL=$WORK${/}goroot
go build -x runtime/cgo
! stderr '[/\\]cgo'$GOEXE'["]? .* -importpath runtime/cgo'

env GOROOT_FINAL=$oldGOROOT_FINAL

# https://go.dev/issue/47215: a missing $(go env CC) caused the precompiled net
# to be stale. But as of https://go.dev/cl/452457 the precompiled libraries are
# no longer installed anyway! Since we're requiring a C compiler in order to
# build and use cgo libraries in the standard library, we should make sure it
# matches what's in the cache.
[!abscc] env CGO_ENABLED=1
[!abscc] [!GOOS:plan9] env PATH=''  # Guaranteed not to include $(go env CC)!
[!abscc] [GOOS:plan9] env path=''
[!abscc] ! go build -x runtime/cgo
[!abscc] stderr 'C compiler .* not found'
