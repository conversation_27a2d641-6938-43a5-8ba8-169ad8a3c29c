  src/cmd/compile/internal/ssa/testdata/hist.go
55:	func test() {
57:		l := line{point{1 + zero, 2 + zero}, point{3 + zero, 4 + zero}}
58:		tinycall()                // this forces l etc to stack
59:		dx := l.end.x - l.begin.x //gdb-dbg=(l.begin.x,l.end.y)//gdb-opt=(l,dx/O,dy/O)
l.begin.x = 1
l.end.y = 4
60:		dy := l.end.y - l.begin.y //gdb-opt=(dx,dy/O)
61:		sink = dx + dy            //gdb-opt=(dx,dy)
63:		hist := make([]int, 7)                                //gdb-opt=(dx/O,dy/O) // TODO sink is missing if this code is in 'test' instead of 'main'
64:		var reader io.Reader = strings.NewReader(cannedInput) //gdb-dbg=(hist/A) // TODO cannedInput/A is missing if this code is in 'test' instead of 'main'
hist = {array = <A>, len = 7, cap = 7}
65:		if len(os.Args) > 1 {
73:		scanner := bufio.NewScanner(reader)
74:		for scanner.Scan() { //gdb-opt=(scanner/A)
75:			s := scanner.Text()
76:			i, err := strconv.ParseInt(s, 10, 64)
77:			if err != nil { //gdb-dbg=(i) //gdb-opt=(err,hist,i)
i = 1
81:			hist = ensure(int(i), hist)
82:			hist[int(i)]++
74:		for scanner.Scan() { //gdb-opt=(scanner/A)
75:			s := scanner.Text()
76:			i, err := strconv.ParseInt(s, 10, 64)
77:			if err != nil { //gdb-dbg=(i) //gdb-opt=(err,hist,i)
i = 1
81:			hist = ensure(int(i), hist)
82:			hist[int(i)]++
74:		for scanner.Scan() { //gdb-opt=(scanner/A)
75:			s := scanner.Text()
76:			i, err := strconv.ParseInt(s, 10, 64)
77:			if err != nil { //gdb-dbg=(i) //gdb-opt=(err,hist,i)
i = 1
81:			hist = ensure(int(i), hist)
82:			hist[int(i)]++
74:		for scanner.Scan() { //gdb-opt=(scanner/A)
75:			s := scanner.Text()
76:			i, err := strconv.ParseInt(s, 10, 64)
77:			if err != nil { //gdb-dbg=(i) //gdb-opt=(err,hist,i)
i = 2
81:			hist = ensure(int(i), hist)
82:			hist[int(i)]++
74:		for scanner.Scan() { //gdb-opt=(scanner/A)
75:			s := scanner.Text()
76:			i, err := strconv.ParseInt(s, 10, 64)
77:			if err != nil { //gdb-dbg=(i) //gdb-opt=(err,hist,i)
i = 2
81:			hist = ensure(int(i), hist)
82:			hist[int(i)]++
74:		for scanner.Scan() { //gdb-opt=(scanner/A)
75:			s := scanner.Text()
76:			i, err := strconv.ParseInt(s, 10, 64)
77:			if err != nil { //gdb-dbg=(i) //gdb-opt=(err,hist,i)
i = 2
81:			hist = ensure(int(i), hist)
82:			hist[int(i)]++
74:		for scanner.Scan() { //gdb-opt=(scanner/A)
75:			s := scanner.Text()
76:			i, err := strconv.ParseInt(s, 10, 64)
77:			if err != nil { //gdb-dbg=(i) //gdb-opt=(err,hist,i)
i = 4
81:			hist = ensure(int(i), hist)
82:			hist[int(i)]++
74:		for scanner.Scan() { //gdb-opt=(scanner/A)
75:			s := scanner.Text()
76:			i, err := strconv.ParseInt(s, 10, 64)
77:			if err != nil { //gdb-dbg=(i) //gdb-opt=(err,hist,i)
i = 4
81:			hist = ensure(int(i), hist)
82:			hist[int(i)]++
74:		for scanner.Scan() { //gdb-opt=(scanner/A)
75:			s := scanner.Text()
76:			i, err := strconv.ParseInt(s, 10, 64)
77:			if err != nil { //gdb-dbg=(i) //gdb-opt=(err,hist,i)
i = 5
81:			hist = ensure(int(i), hist)
82:			hist[int(i)]++
74:		for scanner.Scan() { //gdb-opt=(scanner/A)
84:		t := 0
85:		n := 0
86:		for i, a := range hist {
87:			if a == 0 { //gdb-opt=(a,n,t)
88:				continue
86:		for i, a := range hist {
87:			if a == 0 { //gdb-opt=(a,n,t)
90:			t += i * a
91:			n += a
92:			fmt.Fprintf(os.Stderr, "%d\t%d\t%d\t%d\t%d\n", i, a, n, i*a, t) //gdb-dbg=(n,i,t)
n = 3
i = 1
t = 3
86:		for i, a := range hist {
87:			if a == 0 { //gdb-opt=(a,n,t)
90:			t += i * a
91:			n += a
92:			fmt.Fprintf(os.Stderr, "%d\t%d\t%d\t%d\t%d\n", i, a, n, i*a, t) //gdb-dbg=(n,i,t)
n = 6
i = 2
t = 9
86:		for i, a := range hist {
87:			if a == 0 { //gdb-opt=(a,n,t)
88:				continue
86:		for i, a := range hist {
87:			if a == 0 { //gdb-opt=(a,n,t)
90:			t += i * a
91:			n += a
92:			fmt.Fprintf(os.Stderr, "%d\t%d\t%d\t%d\t%d\n", i, a, n, i*a, t) //gdb-dbg=(n,i,t)
n = 8
i = 4
t = 17
86:		for i, a := range hist {
87:			if a == 0 { //gdb-opt=(a,n,t)
90:			t += i * a
91:			n += a
92:			fmt.Fprintf(os.Stderr, "%d\t%d\t%d\t%d\t%d\n", i, a, n, i*a, t) //gdb-dbg=(n,i,t)
n = 9
i = 5
t = 22
86:		for i, a := range hist {
87:			if a == 0 { //gdb-opt=(a,n,t)
88:				continue
86:		for i, a := range hist {
99:	}
