// Code generated by avx512test. DO NOT EDIT.

#include "../../../../../../runtime/textflag.h"

TEXT asmtest_avx512_bitalg(SB), NOSPLIT, $0
	VPOPCNTB X14, K4, X16                              // 62c27d0c54c6
	VPOPCNTB X19, K4, X16                              // 62a27d0c54c3
	VPOPCNTB X8, K4, X16                               // 62c27d0c54c0
	VPOPCNTB 15(R8)(R14*1), K4, X16                    // 62827d0c5484300f000000
	VPOPCNTB 15(R8)(R14*2), K4, X16                    // 62827d0c5484700f000000
	VPOPCNTB X14, K4, X14                              // 62527d0c54f6
	VPOPCNTB X19, K4, X14                              // 62327d0c54f3
	VPOPCNTB X8, K4, X14                               // 62527d0c54f0
	VPOPCNTB 15(R8)(R14*1), K4, X14                    // 62127d0c54b4300f000000
	VPOPCNTB 15(R8)(R14*2), K4, X14                    // 62127d0c54b4700f000000
	VPOPCNTB X14, K4, X11                              // 62527d0c54de
	VPOPCNTB X19, K4, X11                              // 62327d0c54db
	VPOPCNTB X8, K4, X11                               // 62527d0c54d8
	VPOPCNTB 15(R8)(R14*1), K4, X11                    // 62127d0c549c300f000000
	VPOPCNTB 15(R8)(R14*2), K4, X11                    // 62127d0c549c700f000000
	VPOPCNTB Y14, K4, Y24                              // 62427d2c54c6
	VPOPCNTB Y21, K4, Y24                              // 62227d2c54c5
	VPOPCNTB Y1, K4, Y24                               // 62627d2c54c1
	VPOPCNTB 15(R8)(R14*8), K4, Y24                    // 62027d2c5484f00f000000
	VPOPCNTB -15(R14)(R15*2), K4, Y24                  // 62027d2c54847ef1ffffff
	VPOPCNTB Y14, K4, Y13                              // 62527d2c54ee
	VPOPCNTB Y21, K4, Y13                              // 62327d2c54ed
	VPOPCNTB Y1, K4, Y13                               // 62727d2c54e9
	VPOPCNTB 15(R8)(R14*8), K4, Y13                    // 62127d2c54acf00f000000
	VPOPCNTB -15(R14)(R15*2), K4, Y13                  // 62127d2c54ac7ef1ffffff
	VPOPCNTB Y14, K4, Y20                              // 62c27d2c54e6
	VPOPCNTB Y21, K4, Y20                              // 62a27d2c54e5
	VPOPCNTB Y1, K4, Y20                               // 62e27d2c54e1
	VPOPCNTB 15(R8)(R14*8), K4, Y20                    // 62827d2c54a4f00f000000
	VPOPCNTB -15(R14)(R15*2), K4, Y20                  // 62827d2c54a47ef1ffffff
	VPOPCNTB Z18, K7, Z13                              // 62327d4f54ea
	VPOPCNTB Z8, K7, Z13                               // 62527d4f54e8
	VPOPCNTB 17(SP)(BP*8), K7, Z13                     // 62727d4f54acec11000000
	VPOPCNTB 17(SP)(BP*4), K7, Z13                     // 62727d4f54acac11000000
	VPOPCNTW X20, K3, X11                              // 6232fd0b54dc
	VPOPCNTW X5, K3, X11                               // 6272fd0b54dd
	VPOPCNTW X25, K3, X11                              // 6212fd0b54d9
	VPOPCNTW (CX), K3, X11                             // 6272fd0b5419
	VPOPCNTW 99(R15), K3, X11                          // 6252fd0b549f63000000
	VPOPCNTW X20, K3, X23                              // 62a2fd0b54fc
	VPOPCNTW X5, K3, X23                               // 62e2fd0b54fd
	VPOPCNTW X25, K3, X23                              // 6282fd0b54f9
	VPOPCNTW (CX), K3, X23                             // 62e2fd0b5439
	VPOPCNTW 99(R15), K3, X23                          // 62c2fd0b54bf63000000
	VPOPCNTW X20, K3, X2                               // 62b2fd0b54d4
	VPOPCNTW X5, K3, X2                                // 62f2fd0b54d5
	VPOPCNTW X25, K3, X2                               // 6292fd0b54d1
	VPOPCNTW (CX), K3, X2                              // 62f2fd0b5411
	VPOPCNTW 99(R15), K3, X2                           // 62d2fd0b549763000000
	VPOPCNTW Y13, K3, Y21                              // 62c2fd2b54ed
	VPOPCNTW Y18, K3, Y21                              // 62a2fd2b54ea
	VPOPCNTW Y24, K3, Y21                              // 6282fd2b54e8
	VPOPCNTW (SI), K3, Y21                             // 62e2fd2b542e
	VPOPCNTW 7(SI)(DI*2), K3, Y21                      // 62e2fd2b54ac7e07000000
	VPOPCNTW Y13, K3, Y7                               // 62d2fd2b54fd
	VPOPCNTW Y18, K3, Y7                               // 62b2fd2b54fa
	VPOPCNTW Y24, K3, Y7                               // 6292fd2b54f8
	VPOPCNTW (SI), K3, Y7                              // 62f2fd2b543e
	VPOPCNTW 7(SI)(DI*2), K3, Y7                       // 62f2fd2b54bc7e07000000
	VPOPCNTW Y13, K3, Y30                              // 6242fd2b54f5
	VPOPCNTW Y18, K3, Y30                              // 6222fd2b54f2
	VPOPCNTW Y24, K3, Y30                              // 6202fd2b54f0
	VPOPCNTW (SI), K3, Y30                             // 6262fd2b5436
	VPOPCNTW 7(SI)(DI*2), K3, Y30                      // 6262fd2b54b47e07000000
	VPOPCNTW Z28, K3, Z12                              // 6212fd4b54e4
	VPOPCNTW Z13, K3, Z12                              // 6252fd4b54e5
	VPOPCNTW 7(AX), K3, Z12                            // 6272fd4b54a007000000
	VPOPCNTW (DI), K3, Z12                             // 6272fd4b5427
	VPOPCNTW Z28, K3, Z16                              // 6282fd4b54c4
	VPOPCNTW Z13, K3, Z16                              // 62c2fd4b54c5
	VPOPCNTW 7(AX), K3, Z16                            // 62e2fd4b548007000000
	VPOPCNTW (DI), K3, Z16                             // 62e2fd4b5407
	VPSHUFBITQMB X24, X7, K6, K0                       // 6292450e8fc0
	VPSHUFBITQMB X7, X7, K6, K0                        // 62f2450e8fc7
	VPSHUFBITQMB X0, X7, K6, K0                        // 62f2450e8fc0
	VPSHUFBITQMB (R8), X7, K6, K0                      // 62d2450e8f00
	VPSHUFBITQMB 15(DX)(BX*2), X7, K6, K0              // 62f2450e8f845a0f000000
	VPSHUFBITQMB X24, X13, K6, K0                      // 6292150e8fc0
	VPSHUFBITQMB X7, X13, K6, K0                       // 62f2150e8fc7
	VPSHUFBITQMB X0, X13, K6, K0                       // 62f2150e8fc0
	VPSHUFBITQMB (R8), X13, K6, K0                     // 62d2150e8f00
	VPSHUFBITQMB 15(DX)(BX*2), X13, K6, K0             // 62f2150e8f845a0f000000
	VPSHUFBITQMB X24, X8, K6, K0                       // 62923d0e8fc0
	VPSHUFBITQMB X7, X8, K6, K0                        // 62f23d0e8fc7
	VPSHUFBITQMB X0, X8, K6, K0                        // 62f23d0e8fc0
	VPSHUFBITQMB (R8), X8, K6, K0                      // 62d23d0e8f00
	VPSHUFBITQMB 15(DX)(BX*2), X8, K6, K0              // 62f23d0e8f845a0f000000
	VPSHUFBITQMB X24, X7, K6, K5                       // 6292450e8fe8
	VPSHUFBITQMB X7, X7, K6, K5                        // 62f2450e8fef
	VPSHUFBITQMB X0, X7, K6, K5                        // 62f2450e8fe8
	VPSHUFBITQMB (R8), X7, K6, K5                      // 62d2450e8f28
	VPSHUFBITQMB 15(DX)(BX*2), X7, K6, K5              // 62f2450e8fac5a0f000000
	VPSHUFBITQMB X24, X13, K6, K5                      // 6292150e8fe8
	VPSHUFBITQMB X7, X13, K6, K5                       // 62f2150e8fef
	VPSHUFBITQMB X0, X13, K6, K5                       // 62f2150e8fe8
	VPSHUFBITQMB (R8), X13, K6, K5                     // 62d2150e8f28
	VPSHUFBITQMB 15(DX)(BX*2), X13, K6, K5             // 62f2150e8fac5a0f000000
	VPSHUFBITQMB X24, X8, K6, K5                       // 62923d0e8fe8
	VPSHUFBITQMB X7, X8, K6, K5                        // 62f23d0e8fef
	VPSHUFBITQMB X0, X8, K6, K5                        // 62f23d0e8fe8
	VPSHUFBITQMB (R8), X8, K6, K5                      // 62d23d0e8f28
	VPSHUFBITQMB 15(DX)(BX*2), X8, K6, K5              // 62f23d0e8fac5a0f000000
	VPSHUFBITQMB Y14, Y2, K3, K6                       // 62d26d2b8ff6
	VPSHUFBITQMB Y8, Y2, K3, K6                        // 62d26d2b8ff0
	VPSHUFBITQMB Y20, Y2, K3, K6                       // 62b26d2b8ff4
	VPSHUFBITQMB -17(BP), Y2, K3, K6                   // 62f26d2b8fb5efffffff
	VPSHUFBITQMB -15(R14)(R15*8), Y2, K3, K6           // 62926d2b8fb4fef1ffffff
	VPSHUFBITQMB Y14, Y7, K3, K6                       // 62d2452b8ff6
	VPSHUFBITQMB Y8, Y7, K3, K6                        // 62d2452b8ff0
	VPSHUFBITQMB Y20, Y7, K3, K6                       // 62b2452b8ff4
	VPSHUFBITQMB -17(BP), Y7, K3, K6                   // 62f2452b8fb5efffffff
	VPSHUFBITQMB -15(R14)(R15*8), Y7, K3, K6           // 6292452b8fb4fef1ffffff
	VPSHUFBITQMB Y14, Y21, K3, K6                      // 62d255238ff6
	VPSHUFBITQMB Y8, Y21, K3, K6                       // 62d255238ff0
	VPSHUFBITQMB Y20, Y21, K3, K6                      // 62b255238ff4
	VPSHUFBITQMB -17(BP), Y21, K3, K6                  // 62f255238fb5efffffff
	VPSHUFBITQMB -15(R14)(R15*8), Y21, K3, K6          // 629255238fb4fef1ffffff
	VPSHUFBITQMB Y14, Y2, K3, K5                       // 62d26d2b8fee
	VPSHUFBITQMB Y8, Y2, K3, K5                        // 62d26d2b8fe8
	VPSHUFBITQMB Y20, Y2, K3, K5                       // 62b26d2b8fec
	VPSHUFBITQMB -17(BP), Y2, K3, K5                   // 62f26d2b8fadefffffff
	VPSHUFBITQMB -15(R14)(R15*8), Y2, K3, K5           // 62926d2b8facfef1ffffff
	VPSHUFBITQMB Y14, Y7, K3, K5                       // 62d2452b8fee
	VPSHUFBITQMB Y8, Y7, K3, K5                        // 62d2452b8fe8
	VPSHUFBITQMB Y20, Y7, K3, K5                       // 62b2452b8fec
	VPSHUFBITQMB -17(BP), Y7, K3, K5                   // 62f2452b8fadefffffff
	VPSHUFBITQMB -15(R14)(R15*8), Y7, K3, K5           // 6292452b8facfef1ffffff
	VPSHUFBITQMB Y14, Y21, K3, K5                      // 62d255238fee
	VPSHUFBITQMB Y8, Y21, K3, K5                       // 62d255238fe8
	VPSHUFBITQMB Y20, Y21, K3, K5                      // 62b255238fec
	VPSHUFBITQMB -17(BP), Y21, K3, K5                  // 62f255238fadefffffff
	VPSHUFBITQMB -15(R14)(R15*8), Y21, K3, K5          // 629255238facfef1ffffff
	VPSHUFBITQMB Z3, Z6, K7, K1                        // 62f24d4f8fcb
	VPSHUFBITQMB Z21, Z6, K7, K1                       // 62b24d4f8fcd
	VPSHUFBITQMB -15(R14)(R15*1), Z6, K7, K1           // 62924d4f8f8c3ef1ffffff
	VPSHUFBITQMB -15(BX), Z6, K7, K1                   // 62f24d4f8f8bf1ffffff
	VPSHUFBITQMB Z3, Z25, K7, K1                       // 62f235478fcb
	VPSHUFBITQMB Z21, Z25, K7, K1                      // 62b235478fcd
	VPSHUFBITQMB -15(R14)(R15*1), Z25, K7, K1          // 629235478f8c3ef1ffffff
	VPSHUFBITQMB -15(BX), Z25, K7, K1                  // 62f235478f8bf1ffffff
	VPSHUFBITQMB Z3, Z6, K7, K5                        // 62f24d4f8feb
	VPSHUFBITQMB Z21, Z6, K7, K5                       // 62b24d4f8fed
	VPSHUFBITQMB -15(R14)(R15*1), Z6, K7, K5           // 62924d4f8fac3ef1ffffff
	VPSHUFBITQMB -15(BX), Z6, K7, K5                   // 62f24d4f8fabf1ffffff
	VPSHUFBITQMB Z3, Z25, K7, K5                       // 62f235478feb
	VPSHUFBITQMB Z21, Z25, K7, K5                      // 62b235478fed
	VPSHUFBITQMB -15(R14)(R15*1), Z25, K7, K5          // 629235478fac3ef1ffffff
	VPSHUFBITQMB -15(BX), Z25, K7, K5                  // 62f235478fabf1ffffff
	RET
