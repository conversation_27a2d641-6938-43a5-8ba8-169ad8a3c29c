env GO111MODULE=on

# We should not create a go.mod file unless the user ran 'go mod init' explicitly.
# However, we should suggest 'go mod init' if we can find an alternate config file.
cd $WORK/test/x
! go list .
stderr 'found Gopkg.lock in .*[/\\]test'
stderr '\s*cd \.\. && go mod init'

# The command we suggested should succeed.
cd ..
go mod init
go list -m all
stdout '^m$'

# In Plan 9, directories are automatically created in /n.
# For example, /n/Gopkg.lock always exists, but it's a directory.
# Test that we ignore directories when trying to find alternate config files.
cd $WORK/gopkgdir/x
! go list .
stderr '^go: go.mod file not found in current directory or any parent directory; see ''go help modules''$'
! stderr 'Gopkg.lock'

-- $WORK/test/Gopkg.lock --
-- $WORK/test/x/x.go --
package x // import "m/x"
-- $WORK/gopkgdir/Gopkg.lock/README.txt --
../Gopkg.lock is a directory, not a file.
-- $WORK/gopkgdir/x/x.go --
package x // import "m/x"
