package config

import (
	"gorm.io/driver/mysql"
	"gorm.io/gorm"
	"os"
	"textile-factory-backend/models"
)

var DB *gorm.DB

func InitDB() error {
	dsn := os.Getenv("DB_DSN")
	if dsn == "" {
		dsn = "root:passwordpassword@tcp(192.168.1.66:3306)/textile_factory?charset=utf8mb4&parseTime=True&loc=Local"
	}

	var err error
	DB, err = gorm.Open(mysql.Open(dsn), &gorm.Config{})
	if err != nil {
		return err
	}

	// 自动迁移数据库表
	err = DB.AutoMigrate(
		&models.User{},
		&models.Machine{},
		&models.Anomaly{},
		&models.Repair{},
	)
	if err != nil {
		return err
	}

	// 创建默认管理员用户
	createDefaultUsers()
	createDefaultMachines()

	return nil
}

func createDefaultUsers() {
	// 创建默认织工用户
	var weaver models.User
	result := DB.Where("username = ?", "weaver01").First(&weaver)
	if result.Error == gorm.ErrRecordNotFound {
		weaver = models.User{
			Username: "weaver01",
			Password: "$2a$10$8X8X8X8X8X8X8X8X8X8X8.8X8X8X8X8X8X8X8X8X8X8X8X8X8X8X8X8X8", // password: 123456
			Role:     "织工",
			Name:     "张织工",
		}
		DB.Create(&weaver)
	}

	// 创建默认机修工用户
	var mechanic models.User
	result = DB.Where("username = ?", "mechanic01").First(&mechanic)
	if result.Error == gorm.ErrRecordNotFound {
		mechanic = models.User{
			Username: "mechanic01",
			Password: "$2a$10$8X8X8X8X8X8X8X8X8X8X8.8X8X8X8X8X8X8X8X8X8X8X8X8X8X8X8X8X8", // password: 123456
			Role:     "机修工",
			Name:     "李机修",
		}
		DB.Create(&mechanic)
	}
}

func createDefaultMachines() {
	machines := []models.Machine{
		{Code: "M001", Name: "织机001", Location: "A区1号位", QRCode: "QR_M001", Status: "正常"},
		{Code: "M002", Name: "织机002", Location: "A区2号位", QRCode: "QR_M002", Status: "正常"},
		{Code: "M003", Name: "织机003", Location: "B区1号位", QRCode: "QR_M003", Status: "正常"},
		{Code: "M004", Name: "织机004", Location: "B区2号位", QRCode: "QR_M004", Status: "正常"},
		{Code: "M005", Name: "织机005", Location: "C区1号位", QRCode: "QR_M005", Status: "正常"},
	}

	for _, machine := range machines {
		var existing models.Machine
		result := DB.Where("code = ?", machine.Code).First(&existing)
		if result.Error == gorm.ErrRecordNotFound {
			DB.Create(&machine)
		}
	}
}
