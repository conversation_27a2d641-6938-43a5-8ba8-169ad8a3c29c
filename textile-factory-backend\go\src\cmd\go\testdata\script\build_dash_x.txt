[short] skip
[!cgo] skip

[!exec:/usr/bin/env] skip
[!exec:bash] skip

mkdir $WORK/tmp/cache
env GOCACHE=$WORK/tmp/cache

# Before building our test main.go, ensure that an up-to-date copy of
# runtime/cgo is present in the cache. If it isn't, the 'go build' step below
# will fail with "can't open import". See golang.org/issue/29004.
#
# (The fix in golang.org/issue/29004 didn't completely fix the underlying issue:
# cmd/go/internal/load adds a bunch of implicit dependencies
# based on various heuristics, and, due to a bug described in
# https://golang.org/issue/31544#issuecomment-490607180,
# those implicit dependencies are not added early enough during
# loading to properly affect the import graph.)
go build runtime/cgo

go build -x -o main main.go
cp stderr commands.txt
cat header.txt commands.txt
cp stdout test.sh

exec ./main
cmp stderr hello.txt
rm ./main

exec /usr/bin/env bash -x test.sh
exec ./main
cmp stderr hello.txt

grep '^WORK=(.*)\n' commands.txt

-- main.go --
package main

import "C"

func main() {
	print("hello\n")
}
-- header.txt --
set -e
-- hello.txt --
hello
