

> @jimp/plugin-threshold@1.1.2 test /Users/<USER>/Documents/jimp/plugins/plugin-threshold
> vitest "--watch=false" "-u"


[7m[1m[36m RUN [39m[22m[27m [36mv2.0.5[39m [90m/Users/<USER>/Documents/jimp/plugins/plugin-threshold[39m

[?25l [90m·[39m [2msrc/[22mindex[2m.test.ts[22m[2m (1)[22m
   [90m·[39m Threshold[2m (1)[22m
     [90m·[39m defines default threshold for lighter backgrounds
[?25l[?25l[?25l[?25l[?25l[?25l[?25l[?25l[?25l[?25l[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.test.ts[22m[2m (1)[22m
   [33m❯[39m Threshold[2m (1)[22m
     [33m⠙[39m defines default threshold for lighter backgrounds
[?25l[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.test.ts[22m[2m (1)[22m
   [33m❯[39m Threshold[2m (1)[22m
     [33m⠹[39m defines default threshold for lighter backgrounds
[?25l[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.test.ts[22m[2m (1)[22m
   [33m❯[39m Threshold[2m (1)[22m
     [33m⠸[39m defines default threshold for lighter backgrounds
[?25l[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.test.ts[22m[2m (1)[22m
   [33m❯[39m Threshold[2m (1)[22m
     [33m⠼[39m defines default threshold for lighter backgrounds
[?25l[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.test.ts[22m[2m (1)[22m
   [33m❯[39m Threshold[2m (1)[22m
     [33m⠴[39m defines default threshold for lighter backgrounds
[?25l[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.test.ts[22m[2m (1)[22m
   [33m❯[39m Threshold[2m (1)[22m
     [33m⠦[39m defines default threshold for lighter backgrounds
[?25l[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.test.ts[22m[2m (1)[22m
   [33m❯[39m Threshold[2m (1)[22m
     [33m⠧[39m defines default threshold for lighter backgrounds
[?25l[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.test.ts[22m[2m (1)[22m
   [33m❯[39m Threshold[2m (1)[22m
     [33m⠇[39m defines default threshold for lighter backgrounds
[?25l[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.test.ts[22m[2m (1)[22m
   [33m❯[39m Threshold[2m (1)[22m
     [33m⠏[39m defines default threshold for lighter backgrounds
[?25l[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.test.ts[22m[2m (1)[22m
   [33m❯[39m Threshold[2m (1)[22m
     [33m⠋[39m defines default threshold for lighter backgrounds
[?25l[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.test.ts[22m[2m (1)[22m
   [33m❯[39m Threshold[2m (1)[22m
     [33m⠙[39m defines default threshold for lighter backgrounds
[?25l[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.test.ts[22m[2m (1)[22m
   [33m❯[39m Threshold[2m (1)[22m
     [33m⠹[39m defines default threshold for lighter backgrounds
[?25l[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.test.ts[22m[2m (1)[22m
   [33m❯[39m Threshold[2m (1)[22m
     [33m⠸[39m defines default threshold for lighter backgrounds
[?25l[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.test.ts[22m[2m (1)[22m
   [33m❯[39m Threshold[2m (1)[22m
     [33m⠼[39m defines default threshold for lighter backgrounds
[?25l[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.test.ts[22m[2m (1)[22m
   [33m❯[39m Threshold[2m (1)[22m
     [33m⠴[39m defines default threshold for lighter backgrounds
[?25l[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.test.ts[22m[2m (1)[22m
   [33m❯[39m Threshold[2m (1)[22m
     [33m⠦[39m defines default threshold for lighter backgrounds
[?25l[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.test.ts[22m[2m (1)[22m
   [33m❯[39m Threshold[2m (1)[22m
     [33m⠧[39m defines default threshold for lighter backgrounds
[?25l[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.test.ts[22m[2m (1)[22m
   [33m❯[39m Threshold[2m (1)[22m
     [33m⠇[39m defines default threshold for lighter backgrounds
[?25l[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.test.ts[22m[2m (1)[22m
   [33m❯[39m Threshold[2m (1)[22m
     [33m⠏[39m defines default threshold for lighter backgrounds
[?25l[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.test.ts[22m[2m (1)[22m
   [33m❯[39m Threshold[2m (1)[22m
     [33m⠋[39m defines default threshold for lighter backgrounds
[?25l[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.test.ts[22m[2m (1)[22m
   [33m❯[39m Threshold[2m (1)[22m
     [33m⠙[39m defines default threshold for lighter backgrounds
[?25l[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.test.ts[22m[2m (1)[22m
   [33m❯[39m Threshold[2m (1)[22m
     [33m⠹[39m defines default threshold for lighter backgrounds
[?25l[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.test.ts[22m[2m (1)[22m
   [33m❯[39m Threshold[2m (1)[22m
     [33m⠸[39m defines default threshold for lighter backgrounds
[?25l[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.test.ts[22m[2m (1)[22m
   [33m❯[39m Threshold[2m (1)[22m
     [33m⠼[39m defines default threshold for lighter backgrounds
[?25l[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.test.ts[22m[2m (1)[22m
   [33m❯[39m Threshold[2m (1)[22m
     [33m⠴[39m defines default threshold for lighter backgrounds
[?25l[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.test.ts[22m[2m (1)[22m
   [33m❯[39m Threshold[2m (1)[22m
     [33m⠦[39m defines default threshold for lighter backgrounds
[?25l[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.test.ts[22m[2m (1)[22m
   [33m❯[39m Threshold[2m (1)[22m
     [33m⠧[39m defines default threshold for lighter backgrounds
[?25l[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.test.ts[22m[2m (1)[22m
   [33m❯[39m Threshold[2m (1)[22m
     [33m⠇[39m defines default threshold for lighter backgrounds
[?25l[2K[1A[2K[1A[2K[1A[2K[G [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (1)[22m[33m 651[2mms[22m[39m
   [32m✓[39m Threshold[2m (1)[22m[33m 651[2mms[22m[39m
     [32m✓[39m defines default threshold for lighter backgrounds[33m 650[2mms[22m[39m
[2K[1A[2K[1A[2K[1A[2K[G [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (1)[22m[33m 651[2mms[22m[39m
   [32m✓[39m Threshold[2m (1)[22m[33m 651[2mms[22m[39m
     [32m✓[39m defines default threshold for lighter backgrounds[33m 650[2mms[22m[39m

[2m Test Files [22m [1m[32m1 passed[39m[22m[90m (1)[39m
[2m      Tests [22m [1m[32m1 passed[39m[22m[90m (1)[39m
[2m   Start at [22m 01:23:31
[2m   Duration [22m 1.36s[2m (transform 157ms, setup 0ms, collect 243ms, tests 651ms, environment 0ms, prepare 88ms)[22m

[?25h[?25h
