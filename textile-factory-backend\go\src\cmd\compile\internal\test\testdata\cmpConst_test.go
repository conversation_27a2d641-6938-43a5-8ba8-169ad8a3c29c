// Code generated by gen/cmpConstGen.go. DO NOT EDIT.

package main

import (
	"reflect"
	"runtime"
	"testing"
)

// results show the expected result for the elements left of, equal to and right of the index.
type result struct{ l, e, r bool }

var (
	eq = result{l: false, e: true, r: false}
	ne = result{l: true, e: false, r: true}
	lt = result{l: true, e: false, r: false}
	le = result{l: true, e: true, r: false}
	gt = result{l: false, e: false, r: true}
	ge = result{l: false, e: true, r: true}
)

// uint64 tests
var uint64_vals = []uint64{
	0,
	1,
	126,
	127,
	128,
	254,
	255,
	256,
	32766,
	32767,
	32768,
	65534,
	65535,
	65536,
	2147483646,
	2147483647,
	2147483648,
	4278190080,
	4294967294,
	4294967295,
	4294967296,
	1095216660480,
	9223372036854775806,
	9223372036854775807,
	9223372036854775808,
	18374686479671623680,
	18446744073709551614,
	18446744073709551615,
}

func lt_0_uint64(x uint64) bool                    { return x < 0 }
func le_0_uint64(x uint64) bool                    { return x <= 0 }
func gt_0_uint64(x uint64) bool                    { return x > 0 }
func ge_0_uint64(x uint64) bool                    { return x >= 0 }
func eq_0_uint64(x uint64) bool                    { return x == 0 }
func ne_0_uint64(x uint64) bool                    { return x != 0 }
func lt_1_uint64(x uint64) bool                    { return x < 1 }
func le_1_uint64(x uint64) bool                    { return x <= 1 }
func gt_1_uint64(x uint64) bool                    { return x > 1 }
func ge_1_uint64(x uint64) bool                    { return x >= 1 }
func eq_1_uint64(x uint64) bool                    { return x == 1 }
func ne_1_uint64(x uint64) bool                    { return x != 1 }
func lt_126_uint64(x uint64) bool                  { return x < 126 }
func le_126_uint64(x uint64) bool                  { return x <= 126 }
func gt_126_uint64(x uint64) bool                  { return x > 126 }
func ge_126_uint64(x uint64) bool                  { return x >= 126 }
func eq_126_uint64(x uint64) bool                  { return x == 126 }
func ne_126_uint64(x uint64) bool                  { return x != 126 }
func lt_127_uint64(x uint64) bool                  { return x < 127 }
func le_127_uint64(x uint64) bool                  { return x <= 127 }
func gt_127_uint64(x uint64) bool                  { return x > 127 }
func ge_127_uint64(x uint64) bool                  { return x >= 127 }
func eq_127_uint64(x uint64) bool                  { return x == 127 }
func ne_127_uint64(x uint64) bool                  { return x != 127 }
func lt_128_uint64(x uint64) bool                  { return x < 128 }
func le_128_uint64(x uint64) bool                  { return x <= 128 }
func gt_128_uint64(x uint64) bool                  { return x > 128 }
func ge_128_uint64(x uint64) bool                  { return x >= 128 }
func eq_128_uint64(x uint64) bool                  { return x == 128 }
func ne_128_uint64(x uint64) bool                  { return x != 128 }
func lt_254_uint64(x uint64) bool                  { return x < 254 }
func le_254_uint64(x uint64) bool                  { return x <= 254 }
func gt_254_uint64(x uint64) bool                  { return x > 254 }
func ge_254_uint64(x uint64) bool                  { return x >= 254 }
func eq_254_uint64(x uint64) bool                  { return x == 254 }
func ne_254_uint64(x uint64) bool                  { return x != 254 }
func lt_255_uint64(x uint64) bool                  { return x < 255 }
func le_255_uint64(x uint64) bool                  { return x <= 255 }
func gt_255_uint64(x uint64) bool                  { return x > 255 }
func ge_255_uint64(x uint64) bool                  { return x >= 255 }
func eq_255_uint64(x uint64) bool                  { return x == 255 }
func ne_255_uint64(x uint64) bool                  { return x != 255 }
func lt_256_uint64(x uint64) bool                  { return x < 256 }
func le_256_uint64(x uint64) bool                  { return x <= 256 }
func gt_256_uint64(x uint64) bool                  { return x > 256 }
func ge_256_uint64(x uint64) bool                  { return x >= 256 }
func eq_256_uint64(x uint64) bool                  { return x == 256 }
func ne_256_uint64(x uint64) bool                  { return x != 256 }
func lt_32766_uint64(x uint64) bool                { return x < 32766 }
func le_32766_uint64(x uint64) bool                { return x <= 32766 }
func gt_32766_uint64(x uint64) bool                { return x > 32766 }
func ge_32766_uint64(x uint64) bool                { return x >= 32766 }
func eq_32766_uint64(x uint64) bool                { return x == 32766 }
func ne_32766_uint64(x uint64) bool                { return x != 32766 }
func lt_32767_uint64(x uint64) bool                { return x < 32767 }
func le_32767_uint64(x uint64) bool                { return x <= 32767 }
func gt_32767_uint64(x uint64) bool                { return x > 32767 }
func ge_32767_uint64(x uint64) bool                { return x >= 32767 }
func eq_32767_uint64(x uint64) bool                { return x == 32767 }
func ne_32767_uint64(x uint64) bool                { return x != 32767 }
func lt_32768_uint64(x uint64) bool                { return x < 32768 }
func le_32768_uint64(x uint64) bool                { return x <= 32768 }
func gt_32768_uint64(x uint64) bool                { return x > 32768 }
func ge_32768_uint64(x uint64) bool                { return x >= 32768 }
func eq_32768_uint64(x uint64) bool                { return x == 32768 }
func ne_32768_uint64(x uint64) bool                { return x != 32768 }
func lt_65534_uint64(x uint64) bool                { return x < 65534 }
func le_65534_uint64(x uint64) bool                { return x <= 65534 }
func gt_65534_uint64(x uint64) bool                { return x > 65534 }
func ge_65534_uint64(x uint64) bool                { return x >= 65534 }
func eq_65534_uint64(x uint64) bool                { return x == 65534 }
func ne_65534_uint64(x uint64) bool                { return x != 65534 }
func lt_65535_uint64(x uint64) bool                { return x < 65535 }
func le_65535_uint64(x uint64) bool                { return x <= 65535 }
func gt_65535_uint64(x uint64) bool                { return x > 65535 }
func ge_65535_uint64(x uint64) bool                { return x >= 65535 }
func eq_65535_uint64(x uint64) bool                { return x == 65535 }
func ne_65535_uint64(x uint64) bool                { return x != 65535 }
func lt_65536_uint64(x uint64) bool                { return x < 65536 }
func le_65536_uint64(x uint64) bool                { return x <= 65536 }
func gt_65536_uint64(x uint64) bool                { return x > 65536 }
func ge_65536_uint64(x uint64) bool                { return x >= 65536 }
func eq_65536_uint64(x uint64) bool                { return x == 65536 }
func ne_65536_uint64(x uint64) bool                { return x != 65536 }
func lt_2147483646_uint64(x uint64) bool           { return x < 2147483646 }
func le_2147483646_uint64(x uint64) bool           { return x <= 2147483646 }
func gt_2147483646_uint64(x uint64) bool           { return x > 2147483646 }
func ge_2147483646_uint64(x uint64) bool           { return x >= 2147483646 }
func eq_2147483646_uint64(x uint64) bool           { return x == 2147483646 }
func ne_2147483646_uint64(x uint64) bool           { return x != 2147483646 }
func lt_2147483647_uint64(x uint64) bool           { return x < 2147483647 }
func le_2147483647_uint64(x uint64) bool           { return x <= 2147483647 }
func gt_2147483647_uint64(x uint64) bool           { return x > 2147483647 }
func ge_2147483647_uint64(x uint64) bool           { return x >= 2147483647 }
func eq_2147483647_uint64(x uint64) bool           { return x == 2147483647 }
func ne_2147483647_uint64(x uint64) bool           { return x != 2147483647 }
func lt_2147483648_uint64(x uint64) bool           { return x < 2147483648 }
func le_2147483648_uint64(x uint64) bool           { return x <= 2147483648 }
func gt_2147483648_uint64(x uint64) bool           { return x > 2147483648 }
func ge_2147483648_uint64(x uint64) bool           { return x >= 2147483648 }
func eq_2147483648_uint64(x uint64) bool           { return x == 2147483648 }
func ne_2147483648_uint64(x uint64) bool           { return x != 2147483648 }
func lt_4278190080_uint64(x uint64) bool           { return x < 4278190080 }
func le_4278190080_uint64(x uint64) bool           { return x <= 4278190080 }
func gt_4278190080_uint64(x uint64) bool           { return x > 4278190080 }
func ge_4278190080_uint64(x uint64) bool           { return x >= 4278190080 }
func eq_4278190080_uint64(x uint64) bool           { return x == 4278190080 }
func ne_4278190080_uint64(x uint64) bool           { return x != 4278190080 }
func lt_4294967294_uint64(x uint64) bool           { return x < 4294967294 }
func le_4294967294_uint64(x uint64) bool           { return x <= 4294967294 }
func gt_4294967294_uint64(x uint64) bool           { return x > 4294967294 }
func ge_4294967294_uint64(x uint64) bool           { return x >= 4294967294 }
func eq_4294967294_uint64(x uint64) bool           { return x == 4294967294 }
func ne_4294967294_uint64(x uint64) bool           { return x != 4294967294 }
func lt_4294967295_uint64(x uint64) bool           { return x < 4294967295 }
func le_4294967295_uint64(x uint64) bool           { return x <= 4294967295 }
func gt_4294967295_uint64(x uint64) bool           { return x > 4294967295 }
func ge_4294967295_uint64(x uint64) bool           { return x >= 4294967295 }
func eq_4294967295_uint64(x uint64) bool           { return x == 4294967295 }
func ne_4294967295_uint64(x uint64) bool           { return x != 4294967295 }
func lt_4294967296_uint64(x uint64) bool           { return x < 4294967296 }
func le_4294967296_uint64(x uint64) bool           { return x <= 4294967296 }
func gt_4294967296_uint64(x uint64) bool           { return x > 4294967296 }
func ge_4294967296_uint64(x uint64) bool           { return x >= 4294967296 }
func eq_4294967296_uint64(x uint64) bool           { return x == 4294967296 }
func ne_4294967296_uint64(x uint64) bool           { return x != 4294967296 }
func lt_1095216660480_uint64(x uint64) bool        { return x < 1095216660480 }
func le_1095216660480_uint64(x uint64) bool        { return x <= 1095216660480 }
func gt_1095216660480_uint64(x uint64) bool        { return x > 1095216660480 }
func ge_1095216660480_uint64(x uint64) bool        { return x >= 1095216660480 }
func eq_1095216660480_uint64(x uint64) bool        { return x == 1095216660480 }
func ne_1095216660480_uint64(x uint64) bool        { return x != 1095216660480 }
func lt_9223372036854775806_uint64(x uint64) bool  { return x < 9223372036854775806 }
func le_9223372036854775806_uint64(x uint64) bool  { return x <= 9223372036854775806 }
func gt_9223372036854775806_uint64(x uint64) bool  { return x > 9223372036854775806 }
func ge_9223372036854775806_uint64(x uint64) bool  { return x >= 9223372036854775806 }
func eq_9223372036854775806_uint64(x uint64) bool  { return x == 9223372036854775806 }
func ne_9223372036854775806_uint64(x uint64) bool  { return x != 9223372036854775806 }
func lt_9223372036854775807_uint64(x uint64) bool  { return x < 9223372036854775807 }
func le_9223372036854775807_uint64(x uint64) bool  { return x <= 9223372036854775807 }
func gt_9223372036854775807_uint64(x uint64) bool  { return x > 9223372036854775807 }
func ge_9223372036854775807_uint64(x uint64) bool  { return x >= 9223372036854775807 }
func eq_9223372036854775807_uint64(x uint64) bool  { return x == 9223372036854775807 }
func ne_9223372036854775807_uint64(x uint64) bool  { return x != 9223372036854775807 }
func lt_9223372036854775808_uint64(x uint64) bool  { return x < 9223372036854775808 }
func le_9223372036854775808_uint64(x uint64) bool  { return x <= 9223372036854775808 }
func gt_9223372036854775808_uint64(x uint64) bool  { return x > 9223372036854775808 }
func ge_9223372036854775808_uint64(x uint64) bool  { return x >= 9223372036854775808 }
func eq_9223372036854775808_uint64(x uint64) bool  { return x == 9223372036854775808 }
func ne_9223372036854775808_uint64(x uint64) bool  { return x != 9223372036854775808 }
func lt_18374686479671623680_uint64(x uint64) bool { return x < 18374686479671623680 }
func le_18374686479671623680_uint64(x uint64) bool { return x <= 18374686479671623680 }
func gt_18374686479671623680_uint64(x uint64) bool { return x > 18374686479671623680 }
func ge_18374686479671623680_uint64(x uint64) bool { return x >= 18374686479671623680 }
func eq_18374686479671623680_uint64(x uint64) bool { return x == 18374686479671623680 }
func ne_18374686479671623680_uint64(x uint64) bool { return x != 18374686479671623680 }
func lt_18446744073709551614_uint64(x uint64) bool { return x < 18446744073709551614 }
func le_18446744073709551614_uint64(x uint64) bool { return x <= 18446744073709551614 }
func gt_18446744073709551614_uint64(x uint64) bool { return x > 18446744073709551614 }
func ge_18446744073709551614_uint64(x uint64) bool { return x >= 18446744073709551614 }
func eq_18446744073709551614_uint64(x uint64) bool { return x == 18446744073709551614 }
func ne_18446744073709551614_uint64(x uint64) bool { return x != 18446744073709551614 }
func lt_18446744073709551615_uint64(x uint64) bool { return x < 18446744073709551615 }
func le_18446744073709551615_uint64(x uint64) bool { return x <= 18446744073709551615 }
func gt_18446744073709551615_uint64(x uint64) bool { return x > 18446744073709551615 }
func ge_18446744073709551615_uint64(x uint64) bool { return x >= 18446744073709551615 }
func eq_18446744073709551615_uint64(x uint64) bool { return x == 18446744073709551615 }
func ne_18446744073709551615_uint64(x uint64) bool { return x != 18446744073709551615 }

var uint64_tests = []struct {
	idx int    // index of the constant used
	exp result // expected results
	fn  func(uint64) bool
}{
	{idx: 0, exp: lt, fn: lt_0_uint64},
	{idx: 0, exp: le, fn: le_0_uint64},
	{idx: 0, exp: gt, fn: gt_0_uint64},
	{idx: 0, exp: ge, fn: ge_0_uint64},
	{idx: 0, exp: eq, fn: eq_0_uint64},
	{idx: 0, exp: ne, fn: ne_0_uint64},
	{idx: 1, exp: lt, fn: lt_1_uint64},
	{idx: 1, exp: le, fn: le_1_uint64},
	{idx: 1, exp: gt, fn: gt_1_uint64},
	{idx: 1, exp: ge, fn: ge_1_uint64},
	{idx: 1, exp: eq, fn: eq_1_uint64},
	{idx: 1, exp: ne, fn: ne_1_uint64},
	{idx: 2, exp: lt, fn: lt_126_uint64},
	{idx: 2, exp: le, fn: le_126_uint64},
	{idx: 2, exp: gt, fn: gt_126_uint64},
	{idx: 2, exp: ge, fn: ge_126_uint64},
	{idx: 2, exp: eq, fn: eq_126_uint64},
	{idx: 2, exp: ne, fn: ne_126_uint64},
	{idx: 3, exp: lt, fn: lt_127_uint64},
	{idx: 3, exp: le, fn: le_127_uint64},
	{idx: 3, exp: gt, fn: gt_127_uint64},
	{idx: 3, exp: ge, fn: ge_127_uint64},
	{idx: 3, exp: eq, fn: eq_127_uint64},
	{idx: 3, exp: ne, fn: ne_127_uint64},
	{idx: 4, exp: lt, fn: lt_128_uint64},
	{idx: 4, exp: le, fn: le_128_uint64},
	{idx: 4, exp: gt, fn: gt_128_uint64},
	{idx: 4, exp: ge, fn: ge_128_uint64},
	{idx: 4, exp: eq, fn: eq_128_uint64},
	{idx: 4, exp: ne, fn: ne_128_uint64},
	{idx: 5, exp: lt, fn: lt_254_uint64},
	{idx: 5, exp: le, fn: le_254_uint64},
	{idx: 5, exp: gt, fn: gt_254_uint64},
	{idx: 5, exp: ge, fn: ge_254_uint64},
	{idx: 5, exp: eq, fn: eq_254_uint64},
	{idx: 5, exp: ne, fn: ne_254_uint64},
	{idx: 6, exp: lt, fn: lt_255_uint64},
	{idx: 6, exp: le, fn: le_255_uint64},
	{idx: 6, exp: gt, fn: gt_255_uint64},
	{idx: 6, exp: ge, fn: ge_255_uint64},
	{idx: 6, exp: eq, fn: eq_255_uint64},
	{idx: 6, exp: ne, fn: ne_255_uint64},
	{idx: 7, exp: lt, fn: lt_256_uint64},
	{idx: 7, exp: le, fn: le_256_uint64},
	{idx: 7, exp: gt, fn: gt_256_uint64},
	{idx: 7, exp: ge, fn: ge_256_uint64},
	{idx: 7, exp: eq, fn: eq_256_uint64},
	{idx: 7, exp: ne, fn: ne_256_uint64},
	{idx: 8, exp: lt, fn: lt_32766_uint64},
	{idx: 8, exp: le, fn: le_32766_uint64},
	{idx: 8, exp: gt, fn: gt_32766_uint64},
	{idx: 8, exp: ge, fn: ge_32766_uint64},
	{idx: 8, exp: eq, fn: eq_32766_uint64},
	{idx: 8, exp: ne, fn: ne_32766_uint64},
	{idx: 9, exp: lt, fn: lt_32767_uint64},
	{idx: 9, exp: le, fn: le_32767_uint64},
	{idx: 9, exp: gt, fn: gt_32767_uint64},
	{idx: 9, exp: ge, fn: ge_32767_uint64},
	{idx: 9, exp: eq, fn: eq_32767_uint64},
	{idx: 9, exp: ne, fn: ne_32767_uint64},
	{idx: 10, exp: lt, fn: lt_32768_uint64},
	{idx: 10, exp: le, fn: le_32768_uint64},
	{idx: 10, exp: gt, fn: gt_32768_uint64},
	{idx: 10, exp: ge, fn: ge_32768_uint64},
	{idx: 10, exp: eq, fn: eq_32768_uint64},
	{idx: 10, exp: ne, fn: ne_32768_uint64},
	{idx: 11, exp: lt, fn: lt_65534_uint64},
	{idx: 11, exp: le, fn: le_65534_uint64},
	{idx: 11, exp: gt, fn: gt_65534_uint64},
	{idx: 11, exp: ge, fn: ge_65534_uint64},
	{idx: 11, exp: eq, fn: eq_65534_uint64},
	{idx: 11, exp: ne, fn: ne_65534_uint64},
	{idx: 12, exp: lt, fn: lt_65535_uint64},
	{idx: 12, exp: le, fn: le_65535_uint64},
	{idx: 12, exp: gt, fn: gt_65535_uint64},
	{idx: 12, exp: ge, fn: ge_65535_uint64},
	{idx: 12, exp: eq, fn: eq_65535_uint64},
	{idx: 12, exp: ne, fn: ne_65535_uint64},
	{idx: 13, exp: lt, fn: lt_65536_uint64},
	{idx: 13, exp: le, fn: le_65536_uint64},
	{idx: 13, exp: gt, fn: gt_65536_uint64},
	{idx: 13, exp: ge, fn: ge_65536_uint64},
	{idx: 13, exp: eq, fn: eq_65536_uint64},
	{idx: 13, exp: ne, fn: ne_65536_uint64},
	{idx: 14, exp: lt, fn: lt_2147483646_uint64},
	{idx: 14, exp: le, fn: le_2147483646_uint64},
	{idx: 14, exp: gt, fn: gt_2147483646_uint64},
	{idx: 14, exp: ge, fn: ge_2147483646_uint64},
	{idx: 14, exp: eq, fn: eq_2147483646_uint64},
	{idx: 14, exp: ne, fn: ne_2147483646_uint64},
	{idx: 15, exp: lt, fn: lt_2147483647_uint64},
	{idx: 15, exp: le, fn: le_2147483647_uint64},
	{idx: 15, exp: gt, fn: gt_2147483647_uint64},
	{idx: 15, exp: ge, fn: ge_2147483647_uint64},
	{idx: 15, exp: eq, fn: eq_2147483647_uint64},
	{idx: 15, exp: ne, fn: ne_2147483647_uint64},
	{idx: 16, exp: lt, fn: lt_2147483648_uint64},
	{idx: 16, exp: le, fn: le_2147483648_uint64},
	{idx: 16, exp: gt, fn: gt_2147483648_uint64},
	{idx: 16, exp: ge, fn: ge_2147483648_uint64},
	{idx: 16, exp: eq, fn: eq_2147483648_uint64},
	{idx: 16, exp: ne, fn: ne_2147483648_uint64},
	{idx: 17, exp: lt, fn: lt_4278190080_uint64},
	{idx: 17, exp: le, fn: le_4278190080_uint64},
	{idx: 17, exp: gt, fn: gt_4278190080_uint64},
	{idx: 17, exp: ge, fn: ge_4278190080_uint64},
	{idx: 17, exp: eq, fn: eq_4278190080_uint64},
	{idx: 17, exp: ne, fn: ne_4278190080_uint64},
	{idx: 18, exp: lt, fn: lt_4294967294_uint64},
	{idx: 18, exp: le, fn: le_4294967294_uint64},
	{idx: 18, exp: gt, fn: gt_4294967294_uint64},
	{idx: 18, exp: ge, fn: ge_4294967294_uint64},
	{idx: 18, exp: eq, fn: eq_4294967294_uint64},
	{idx: 18, exp: ne, fn: ne_4294967294_uint64},
	{idx: 19, exp: lt, fn: lt_4294967295_uint64},
	{idx: 19, exp: le, fn: le_4294967295_uint64},
	{idx: 19, exp: gt, fn: gt_4294967295_uint64},
	{idx: 19, exp: ge, fn: ge_4294967295_uint64},
	{idx: 19, exp: eq, fn: eq_4294967295_uint64},
	{idx: 19, exp: ne, fn: ne_4294967295_uint64},
	{idx: 20, exp: lt, fn: lt_4294967296_uint64},
	{idx: 20, exp: le, fn: le_4294967296_uint64},
	{idx: 20, exp: gt, fn: gt_4294967296_uint64},
	{idx: 20, exp: ge, fn: ge_4294967296_uint64},
	{idx: 20, exp: eq, fn: eq_4294967296_uint64},
	{idx: 20, exp: ne, fn: ne_4294967296_uint64},
	{idx: 21, exp: lt, fn: lt_1095216660480_uint64},
	{idx: 21, exp: le, fn: le_1095216660480_uint64},
	{idx: 21, exp: gt, fn: gt_1095216660480_uint64},
	{idx: 21, exp: ge, fn: ge_1095216660480_uint64},
	{idx: 21, exp: eq, fn: eq_1095216660480_uint64},
	{idx: 21, exp: ne, fn: ne_1095216660480_uint64},
	{idx: 22, exp: lt, fn: lt_9223372036854775806_uint64},
	{idx: 22, exp: le, fn: le_9223372036854775806_uint64},
	{idx: 22, exp: gt, fn: gt_9223372036854775806_uint64},
	{idx: 22, exp: ge, fn: ge_9223372036854775806_uint64},
	{idx: 22, exp: eq, fn: eq_9223372036854775806_uint64},
	{idx: 22, exp: ne, fn: ne_9223372036854775806_uint64},
	{idx: 23, exp: lt, fn: lt_9223372036854775807_uint64},
	{idx: 23, exp: le, fn: le_9223372036854775807_uint64},
	{idx: 23, exp: gt, fn: gt_9223372036854775807_uint64},
	{idx: 23, exp: ge, fn: ge_9223372036854775807_uint64},
	{idx: 23, exp: eq, fn: eq_9223372036854775807_uint64},
	{idx: 23, exp: ne, fn: ne_9223372036854775807_uint64},
	{idx: 24, exp: lt, fn: lt_9223372036854775808_uint64},
	{idx: 24, exp: le, fn: le_9223372036854775808_uint64},
	{idx: 24, exp: gt, fn: gt_9223372036854775808_uint64},
	{idx: 24, exp: ge, fn: ge_9223372036854775808_uint64},
	{idx: 24, exp: eq, fn: eq_9223372036854775808_uint64},
	{idx: 24, exp: ne, fn: ne_9223372036854775808_uint64},
	{idx: 25, exp: lt, fn: lt_18374686479671623680_uint64},
	{idx: 25, exp: le, fn: le_18374686479671623680_uint64},
	{idx: 25, exp: gt, fn: gt_18374686479671623680_uint64},
	{idx: 25, exp: ge, fn: ge_18374686479671623680_uint64},
	{idx: 25, exp: eq, fn: eq_18374686479671623680_uint64},
	{idx: 25, exp: ne, fn: ne_18374686479671623680_uint64},
	{idx: 26, exp: lt, fn: lt_18446744073709551614_uint64},
	{idx: 26, exp: le, fn: le_18446744073709551614_uint64},
	{idx: 26, exp: gt, fn: gt_18446744073709551614_uint64},
	{idx: 26, exp: ge, fn: ge_18446744073709551614_uint64},
	{idx: 26, exp: eq, fn: eq_18446744073709551614_uint64},
	{idx: 26, exp: ne, fn: ne_18446744073709551614_uint64},
	{idx: 27, exp: lt, fn: lt_18446744073709551615_uint64},
	{idx: 27, exp: le, fn: le_18446744073709551615_uint64},
	{idx: 27, exp: gt, fn: gt_18446744073709551615_uint64},
	{idx: 27, exp: ge, fn: ge_18446744073709551615_uint64},
	{idx: 27, exp: eq, fn: eq_18446744073709551615_uint64},
	{idx: 27, exp: ne, fn: ne_18446744073709551615_uint64},
}

// uint32 tests
var uint32_vals = []uint32{
	0,
	1,
	126,
	127,
	128,
	254,
	255,
	256,
	32766,
	32767,
	32768,
	65534,
	65535,
	65536,
	2147483646,
	2147483647,
	2147483648,
	4278190080,
	4294967294,
	4294967295,
}

func lt_0_uint32(x uint32) bool          { return x < 0 }
func le_0_uint32(x uint32) bool          { return x <= 0 }
func gt_0_uint32(x uint32) bool          { return x > 0 }
func ge_0_uint32(x uint32) bool          { return x >= 0 }
func eq_0_uint32(x uint32) bool          { return x == 0 }
func ne_0_uint32(x uint32) bool          { return x != 0 }
func lt_1_uint32(x uint32) bool          { return x < 1 }
func le_1_uint32(x uint32) bool          { return x <= 1 }
func gt_1_uint32(x uint32) bool          { return x > 1 }
func ge_1_uint32(x uint32) bool          { return x >= 1 }
func eq_1_uint32(x uint32) bool          { return x == 1 }
func ne_1_uint32(x uint32) bool          { return x != 1 }
func lt_126_uint32(x uint32) bool        { return x < 126 }
func le_126_uint32(x uint32) bool        { return x <= 126 }
func gt_126_uint32(x uint32) bool        { return x > 126 }
func ge_126_uint32(x uint32) bool        { return x >= 126 }
func eq_126_uint32(x uint32) bool        { return x == 126 }
func ne_126_uint32(x uint32) bool        { return x != 126 }
func lt_127_uint32(x uint32) bool        { return x < 127 }
func le_127_uint32(x uint32) bool        { return x <= 127 }
func gt_127_uint32(x uint32) bool        { return x > 127 }
func ge_127_uint32(x uint32) bool        { return x >= 127 }
func eq_127_uint32(x uint32) bool        { return x == 127 }
func ne_127_uint32(x uint32) bool        { return x != 127 }
func lt_128_uint32(x uint32) bool        { return x < 128 }
func le_128_uint32(x uint32) bool        { return x <= 128 }
func gt_128_uint32(x uint32) bool        { return x > 128 }
func ge_128_uint32(x uint32) bool        { return x >= 128 }
func eq_128_uint32(x uint32) bool        { return x == 128 }
func ne_128_uint32(x uint32) bool        { return x != 128 }
func lt_254_uint32(x uint32) bool        { return x < 254 }
func le_254_uint32(x uint32) bool        { return x <= 254 }
func gt_254_uint32(x uint32) bool        { return x > 254 }
func ge_254_uint32(x uint32) bool        { return x >= 254 }
func eq_254_uint32(x uint32) bool        { return x == 254 }
func ne_254_uint32(x uint32) bool        { return x != 254 }
func lt_255_uint32(x uint32) bool        { return x < 255 }
func le_255_uint32(x uint32) bool        { return x <= 255 }
func gt_255_uint32(x uint32) bool        { return x > 255 }
func ge_255_uint32(x uint32) bool        { return x >= 255 }
func eq_255_uint32(x uint32) bool        { return x == 255 }
func ne_255_uint32(x uint32) bool        { return x != 255 }
func lt_256_uint32(x uint32) bool        { return x < 256 }
func le_256_uint32(x uint32) bool        { return x <= 256 }
func gt_256_uint32(x uint32) bool        { return x > 256 }
func ge_256_uint32(x uint32) bool        { return x >= 256 }
func eq_256_uint32(x uint32) bool        { return x == 256 }
func ne_256_uint32(x uint32) bool        { return x != 256 }
func lt_32766_uint32(x uint32) bool      { return x < 32766 }
func le_32766_uint32(x uint32) bool      { return x <= 32766 }
func gt_32766_uint32(x uint32) bool      { return x > 32766 }
func ge_32766_uint32(x uint32) bool      { return x >= 32766 }
func eq_32766_uint32(x uint32) bool      { return x == 32766 }
func ne_32766_uint32(x uint32) bool      { return x != 32766 }
func lt_32767_uint32(x uint32) bool      { return x < 32767 }
func le_32767_uint32(x uint32) bool      { return x <= 32767 }
func gt_32767_uint32(x uint32) bool      { return x > 32767 }
func ge_32767_uint32(x uint32) bool      { return x >= 32767 }
func eq_32767_uint32(x uint32) bool      { return x == 32767 }
func ne_32767_uint32(x uint32) bool      { return x != 32767 }
func lt_32768_uint32(x uint32) bool      { return x < 32768 }
func le_32768_uint32(x uint32) bool      { return x <= 32768 }
func gt_32768_uint32(x uint32) bool      { return x > 32768 }
func ge_32768_uint32(x uint32) bool      { return x >= 32768 }
func eq_32768_uint32(x uint32) bool      { return x == 32768 }
func ne_32768_uint32(x uint32) bool      { return x != 32768 }
func lt_65534_uint32(x uint32) bool      { return x < 65534 }
func le_65534_uint32(x uint32) bool      { return x <= 65534 }
func gt_65534_uint32(x uint32) bool      { return x > 65534 }
func ge_65534_uint32(x uint32) bool      { return x >= 65534 }
func eq_65534_uint32(x uint32) bool      { return x == 65534 }
func ne_65534_uint32(x uint32) bool      { return x != 65534 }
func lt_65535_uint32(x uint32) bool      { return x < 65535 }
func le_65535_uint32(x uint32) bool      { return x <= 65535 }
func gt_65535_uint32(x uint32) bool      { return x > 65535 }
func ge_65535_uint32(x uint32) bool      { return x >= 65535 }
func eq_65535_uint32(x uint32) bool      { return x == 65535 }
func ne_65535_uint32(x uint32) bool      { return x != 65535 }
func lt_65536_uint32(x uint32) bool      { return x < 65536 }
func le_65536_uint32(x uint32) bool      { return x <= 65536 }
func gt_65536_uint32(x uint32) bool      { return x > 65536 }
func ge_65536_uint32(x uint32) bool      { return x >= 65536 }
func eq_65536_uint32(x uint32) bool      { return x == 65536 }
func ne_65536_uint32(x uint32) bool      { return x != 65536 }
func lt_2147483646_uint32(x uint32) bool { return x < 2147483646 }
func le_2147483646_uint32(x uint32) bool { return x <= 2147483646 }
func gt_2147483646_uint32(x uint32) bool { return x > 2147483646 }
func ge_2147483646_uint32(x uint32) bool { return x >= 2147483646 }
func eq_2147483646_uint32(x uint32) bool { return x == 2147483646 }
func ne_2147483646_uint32(x uint32) bool { return x != 2147483646 }
func lt_2147483647_uint32(x uint32) bool { return x < 2147483647 }
func le_2147483647_uint32(x uint32) bool { return x <= 2147483647 }
func gt_2147483647_uint32(x uint32) bool { return x > 2147483647 }
func ge_2147483647_uint32(x uint32) bool { return x >= 2147483647 }
func eq_2147483647_uint32(x uint32) bool { return x == 2147483647 }
func ne_2147483647_uint32(x uint32) bool { return x != 2147483647 }
func lt_2147483648_uint32(x uint32) bool { return x < 2147483648 }
func le_2147483648_uint32(x uint32) bool { return x <= 2147483648 }
func gt_2147483648_uint32(x uint32) bool { return x > 2147483648 }
func ge_2147483648_uint32(x uint32) bool { return x >= 2147483648 }
func eq_2147483648_uint32(x uint32) bool { return x == 2147483648 }
func ne_2147483648_uint32(x uint32) bool { return x != 2147483648 }
func lt_4278190080_uint32(x uint32) bool { return x < 4278190080 }
func le_4278190080_uint32(x uint32) bool { return x <= 4278190080 }
func gt_4278190080_uint32(x uint32) bool { return x > 4278190080 }
func ge_4278190080_uint32(x uint32) bool { return x >= 4278190080 }
func eq_4278190080_uint32(x uint32) bool { return x == 4278190080 }
func ne_4278190080_uint32(x uint32) bool { return x != 4278190080 }
func lt_4294967294_uint32(x uint32) bool { return x < 4294967294 }
func le_4294967294_uint32(x uint32) bool { return x <= 4294967294 }
func gt_4294967294_uint32(x uint32) bool { return x > 4294967294 }
func ge_4294967294_uint32(x uint32) bool { return x >= 4294967294 }
func eq_4294967294_uint32(x uint32) bool { return x == 4294967294 }
func ne_4294967294_uint32(x uint32) bool { return x != 4294967294 }
func lt_4294967295_uint32(x uint32) bool { return x < 4294967295 }
func le_4294967295_uint32(x uint32) bool { return x <= 4294967295 }
func gt_4294967295_uint32(x uint32) bool { return x > 4294967295 }
func ge_4294967295_uint32(x uint32) bool { return x >= 4294967295 }
func eq_4294967295_uint32(x uint32) bool { return x == 4294967295 }
func ne_4294967295_uint32(x uint32) bool { return x != 4294967295 }

var uint32_tests = []struct {
	idx int    // index of the constant used
	exp result // expected results
	fn  func(uint32) bool
}{
	{idx: 0, exp: lt, fn: lt_0_uint32},
	{idx: 0, exp: le, fn: le_0_uint32},
	{idx: 0, exp: gt, fn: gt_0_uint32},
	{idx: 0, exp: ge, fn: ge_0_uint32},
	{idx: 0, exp: eq, fn: eq_0_uint32},
	{idx: 0, exp: ne, fn: ne_0_uint32},
	{idx: 1, exp: lt, fn: lt_1_uint32},
	{idx: 1, exp: le, fn: le_1_uint32},
	{idx: 1, exp: gt, fn: gt_1_uint32},
	{idx: 1, exp: ge, fn: ge_1_uint32},
	{idx: 1, exp: eq, fn: eq_1_uint32},
	{idx: 1, exp: ne, fn: ne_1_uint32},
	{idx: 2, exp: lt, fn: lt_126_uint32},
	{idx: 2, exp: le, fn: le_126_uint32},
	{idx: 2, exp: gt, fn: gt_126_uint32},
	{idx: 2, exp: ge, fn: ge_126_uint32},
	{idx: 2, exp: eq, fn: eq_126_uint32},
	{idx: 2, exp: ne, fn: ne_126_uint32},
	{idx: 3, exp: lt, fn: lt_127_uint32},
	{idx: 3, exp: le, fn: le_127_uint32},
	{idx: 3, exp: gt, fn: gt_127_uint32},
	{idx: 3, exp: ge, fn: ge_127_uint32},
	{idx: 3, exp: eq, fn: eq_127_uint32},
	{idx: 3, exp: ne, fn: ne_127_uint32},
	{idx: 4, exp: lt, fn: lt_128_uint32},
	{idx: 4, exp: le, fn: le_128_uint32},
	{idx: 4, exp: gt, fn: gt_128_uint32},
	{idx: 4, exp: ge, fn: ge_128_uint32},
	{idx: 4, exp: eq, fn: eq_128_uint32},
	{idx: 4, exp: ne, fn: ne_128_uint32},
	{idx: 5, exp: lt, fn: lt_254_uint32},
	{idx: 5, exp: le, fn: le_254_uint32},
	{idx: 5, exp: gt, fn: gt_254_uint32},
	{idx: 5, exp: ge, fn: ge_254_uint32},
	{idx: 5, exp: eq, fn: eq_254_uint32},
	{idx: 5, exp: ne, fn: ne_254_uint32},
	{idx: 6, exp: lt, fn: lt_255_uint32},
	{idx: 6, exp: le, fn: le_255_uint32},
	{idx: 6, exp: gt, fn: gt_255_uint32},
	{idx: 6, exp: ge, fn: ge_255_uint32},
	{idx: 6, exp: eq, fn: eq_255_uint32},
	{idx: 6, exp: ne, fn: ne_255_uint32},
	{idx: 7, exp: lt, fn: lt_256_uint32},
	{idx: 7, exp: le, fn: le_256_uint32},
	{idx: 7, exp: gt, fn: gt_256_uint32},
	{idx: 7, exp: ge, fn: ge_256_uint32},
	{idx: 7, exp: eq, fn: eq_256_uint32},
	{idx: 7, exp: ne, fn: ne_256_uint32},
	{idx: 8, exp: lt, fn: lt_32766_uint32},
	{idx: 8, exp: le, fn: le_32766_uint32},
	{idx: 8, exp: gt, fn: gt_32766_uint32},
	{idx: 8, exp: ge, fn: ge_32766_uint32},
	{idx: 8, exp: eq, fn: eq_32766_uint32},
	{idx: 8, exp: ne, fn: ne_32766_uint32},
	{idx: 9, exp: lt, fn: lt_32767_uint32},
	{idx: 9, exp: le, fn: le_32767_uint32},
	{idx: 9, exp: gt, fn: gt_32767_uint32},
	{idx: 9, exp: ge, fn: ge_32767_uint32},
	{idx: 9, exp: eq, fn: eq_32767_uint32},
	{idx: 9, exp: ne, fn: ne_32767_uint32},
	{idx: 10, exp: lt, fn: lt_32768_uint32},
	{idx: 10, exp: le, fn: le_32768_uint32},
	{idx: 10, exp: gt, fn: gt_32768_uint32},
	{idx: 10, exp: ge, fn: ge_32768_uint32},
	{idx: 10, exp: eq, fn: eq_32768_uint32},
	{idx: 10, exp: ne, fn: ne_32768_uint32},
	{idx: 11, exp: lt, fn: lt_65534_uint32},
	{idx: 11, exp: le, fn: le_65534_uint32},
	{idx: 11, exp: gt, fn: gt_65534_uint32},
	{idx: 11, exp: ge, fn: ge_65534_uint32},
	{idx: 11, exp: eq, fn: eq_65534_uint32},
	{idx: 11, exp: ne, fn: ne_65534_uint32},
	{idx: 12, exp: lt, fn: lt_65535_uint32},
	{idx: 12, exp: le, fn: le_65535_uint32},
	{idx: 12, exp: gt, fn: gt_65535_uint32},
	{idx: 12, exp: ge, fn: ge_65535_uint32},
	{idx: 12, exp: eq, fn: eq_65535_uint32},
	{idx: 12, exp: ne, fn: ne_65535_uint32},
	{idx: 13, exp: lt, fn: lt_65536_uint32},
	{idx: 13, exp: le, fn: le_65536_uint32},
	{idx: 13, exp: gt, fn: gt_65536_uint32},
	{idx: 13, exp: ge, fn: ge_65536_uint32},
	{idx: 13, exp: eq, fn: eq_65536_uint32},
	{idx: 13, exp: ne, fn: ne_65536_uint32},
	{idx: 14, exp: lt, fn: lt_2147483646_uint32},
	{idx: 14, exp: le, fn: le_2147483646_uint32},
	{idx: 14, exp: gt, fn: gt_2147483646_uint32},
	{idx: 14, exp: ge, fn: ge_2147483646_uint32},
	{idx: 14, exp: eq, fn: eq_2147483646_uint32},
	{idx: 14, exp: ne, fn: ne_2147483646_uint32},
	{idx: 15, exp: lt, fn: lt_2147483647_uint32},
	{idx: 15, exp: le, fn: le_2147483647_uint32},
	{idx: 15, exp: gt, fn: gt_2147483647_uint32},
	{idx: 15, exp: ge, fn: ge_2147483647_uint32},
	{idx: 15, exp: eq, fn: eq_2147483647_uint32},
	{idx: 15, exp: ne, fn: ne_2147483647_uint32},
	{idx: 16, exp: lt, fn: lt_2147483648_uint32},
	{idx: 16, exp: le, fn: le_2147483648_uint32},
	{idx: 16, exp: gt, fn: gt_2147483648_uint32},
	{idx: 16, exp: ge, fn: ge_2147483648_uint32},
	{idx: 16, exp: eq, fn: eq_2147483648_uint32},
	{idx: 16, exp: ne, fn: ne_2147483648_uint32},
	{idx: 17, exp: lt, fn: lt_4278190080_uint32},
	{idx: 17, exp: le, fn: le_4278190080_uint32},
	{idx: 17, exp: gt, fn: gt_4278190080_uint32},
	{idx: 17, exp: ge, fn: ge_4278190080_uint32},
	{idx: 17, exp: eq, fn: eq_4278190080_uint32},
	{idx: 17, exp: ne, fn: ne_4278190080_uint32},
	{idx: 18, exp: lt, fn: lt_4294967294_uint32},
	{idx: 18, exp: le, fn: le_4294967294_uint32},
	{idx: 18, exp: gt, fn: gt_4294967294_uint32},
	{idx: 18, exp: ge, fn: ge_4294967294_uint32},
	{idx: 18, exp: eq, fn: eq_4294967294_uint32},
	{idx: 18, exp: ne, fn: ne_4294967294_uint32},
	{idx: 19, exp: lt, fn: lt_4294967295_uint32},
	{idx: 19, exp: le, fn: le_4294967295_uint32},
	{idx: 19, exp: gt, fn: gt_4294967295_uint32},
	{idx: 19, exp: ge, fn: ge_4294967295_uint32},
	{idx: 19, exp: eq, fn: eq_4294967295_uint32},
	{idx: 19, exp: ne, fn: ne_4294967295_uint32},
}

// uint16 tests
var uint16_vals = []uint16{
	0,
	1,
	126,
	127,
	128,
	254,
	255,
	256,
	32766,
	32767,
	32768,
	65534,
	65535,
}

func lt_0_uint16(x uint16) bool     { return x < 0 }
func le_0_uint16(x uint16) bool     { return x <= 0 }
func gt_0_uint16(x uint16) bool     { return x > 0 }
func ge_0_uint16(x uint16) bool     { return x >= 0 }
func eq_0_uint16(x uint16) bool     { return x == 0 }
func ne_0_uint16(x uint16) bool     { return x != 0 }
func lt_1_uint16(x uint16) bool     { return x < 1 }
func le_1_uint16(x uint16) bool     { return x <= 1 }
func gt_1_uint16(x uint16) bool     { return x > 1 }
func ge_1_uint16(x uint16) bool     { return x >= 1 }
func eq_1_uint16(x uint16) bool     { return x == 1 }
func ne_1_uint16(x uint16) bool     { return x != 1 }
func lt_126_uint16(x uint16) bool   { return x < 126 }
func le_126_uint16(x uint16) bool   { return x <= 126 }
func gt_126_uint16(x uint16) bool   { return x > 126 }
func ge_126_uint16(x uint16) bool   { return x >= 126 }
func eq_126_uint16(x uint16) bool   { return x == 126 }
func ne_126_uint16(x uint16) bool   { return x != 126 }
func lt_127_uint16(x uint16) bool   { return x < 127 }
func le_127_uint16(x uint16) bool   { return x <= 127 }
func gt_127_uint16(x uint16) bool   { return x > 127 }
func ge_127_uint16(x uint16) bool   { return x >= 127 }
func eq_127_uint16(x uint16) bool   { return x == 127 }
func ne_127_uint16(x uint16) bool   { return x != 127 }
func lt_128_uint16(x uint16) bool   { return x < 128 }
func le_128_uint16(x uint16) bool   { return x <= 128 }
func gt_128_uint16(x uint16) bool   { return x > 128 }
func ge_128_uint16(x uint16) bool   { return x >= 128 }
func eq_128_uint16(x uint16) bool   { return x == 128 }
func ne_128_uint16(x uint16) bool   { return x != 128 }
func lt_254_uint16(x uint16) bool   { return x < 254 }
func le_254_uint16(x uint16) bool   { return x <= 254 }
func gt_254_uint16(x uint16) bool   { return x > 254 }
func ge_254_uint16(x uint16) bool   { return x >= 254 }
func eq_254_uint16(x uint16) bool   { return x == 254 }
func ne_254_uint16(x uint16) bool   { return x != 254 }
func lt_255_uint16(x uint16) bool   { return x < 255 }
func le_255_uint16(x uint16) bool   { return x <= 255 }
func gt_255_uint16(x uint16) bool   { return x > 255 }
func ge_255_uint16(x uint16) bool   { return x >= 255 }
func eq_255_uint16(x uint16) bool   { return x == 255 }
func ne_255_uint16(x uint16) bool   { return x != 255 }
func lt_256_uint16(x uint16) bool   { return x < 256 }
func le_256_uint16(x uint16) bool   { return x <= 256 }
func gt_256_uint16(x uint16) bool   { return x > 256 }
func ge_256_uint16(x uint16) bool   { return x >= 256 }
func eq_256_uint16(x uint16) bool   { return x == 256 }
func ne_256_uint16(x uint16) bool   { return x != 256 }
func lt_32766_uint16(x uint16) bool { return x < 32766 }
func le_32766_uint16(x uint16) bool { return x <= 32766 }
func gt_32766_uint16(x uint16) bool { return x > 32766 }
func ge_32766_uint16(x uint16) bool { return x >= 32766 }
func eq_32766_uint16(x uint16) bool { return x == 32766 }
func ne_32766_uint16(x uint16) bool { return x != 32766 }
func lt_32767_uint16(x uint16) bool { return x < 32767 }
func le_32767_uint16(x uint16) bool { return x <= 32767 }
func gt_32767_uint16(x uint16) bool { return x > 32767 }
func ge_32767_uint16(x uint16) bool { return x >= 32767 }
func eq_32767_uint16(x uint16) bool { return x == 32767 }
func ne_32767_uint16(x uint16) bool { return x != 32767 }
func lt_32768_uint16(x uint16) bool { return x < 32768 }
func le_32768_uint16(x uint16) bool { return x <= 32768 }
func gt_32768_uint16(x uint16) bool { return x > 32768 }
func ge_32768_uint16(x uint16) bool { return x >= 32768 }
func eq_32768_uint16(x uint16) bool { return x == 32768 }
func ne_32768_uint16(x uint16) bool { return x != 32768 }
func lt_65534_uint16(x uint16) bool { return x < 65534 }
func le_65534_uint16(x uint16) bool { return x <= 65534 }
func gt_65534_uint16(x uint16) bool { return x > 65534 }
func ge_65534_uint16(x uint16) bool { return x >= 65534 }
func eq_65534_uint16(x uint16) bool { return x == 65534 }
func ne_65534_uint16(x uint16) bool { return x != 65534 }
func lt_65535_uint16(x uint16) bool { return x < 65535 }
func le_65535_uint16(x uint16) bool { return x <= 65535 }
func gt_65535_uint16(x uint16) bool { return x > 65535 }
func ge_65535_uint16(x uint16) bool { return x >= 65535 }
func eq_65535_uint16(x uint16) bool { return x == 65535 }
func ne_65535_uint16(x uint16) bool { return x != 65535 }

var uint16_tests = []struct {
	idx int    // index of the constant used
	exp result // expected results
	fn  func(uint16) bool
}{
	{idx: 0, exp: lt, fn: lt_0_uint16},
	{idx: 0, exp: le, fn: le_0_uint16},
	{idx: 0, exp: gt, fn: gt_0_uint16},
	{idx: 0, exp: ge, fn: ge_0_uint16},
	{idx: 0, exp: eq, fn: eq_0_uint16},
	{idx: 0, exp: ne, fn: ne_0_uint16},
	{idx: 1, exp: lt, fn: lt_1_uint16},
	{idx: 1, exp: le, fn: le_1_uint16},
	{idx: 1, exp: gt, fn: gt_1_uint16},
	{idx: 1, exp: ge, fn: ge_1_uint16},
	{idx: 1, exp: eq, fn: eq_1_uint16},
	{idx: 1, exp: ne, fn: ne_1_uint16},
	{idx: 2, exp: lt, fn: lt_126_uint16},
	{idx: 2, exp: le, fn: le_126_uint16},
	{idx: 2, exp: gt, fn: gt_126_uint16},
	{idx: 2, exp: ge, fn: ge_126_uint16},
	{idx: 2, exp: eq, fn: eq_126_uint16},
	{idx: 2, exp: ne, fn: ne_126_uint16},
	{idx: 3, exp: lt, fn: lt_127_uint16},
	{idx: 3, exp: le, fn: le_127_uint16},
	{idx: 3, exp: gt, fn: gt_127_uint16},
	{idx: 3, exp: ge, fn: ge_127_uint16},
	{idx: 3, exp: eq, fn: eq_127_uint16},
	{idx: 3, exp: ne, fn: ne_127_uint16},
	{idx: 4, exp: lt, fn: lt_128_uint16},
	{idx: 4, exp: le, fn: le_128_uint16},
	{idx: 4, exp: gt, fn: gt_128_uint16},
	{idx: 4, exp: ge, fn: ge_128_uint16},
	{idx: 4, exp: eq, fn: eq_128_uint16},
	{idx: 4, exp: ne, fn: ne_128_uint16},
	{idx: 5, exp: lt, fn: lt_254_uint16},
	{idx: 5, exp: le, fn: le_254_uint16},
	{idx: 5, exp: gt, fn: gt_254_uint16},
	{idx: 5, exp: ge, fn: ge_254_uint16},
	{idx: 5, exp: eq, fn: eq_254_uint16},
	{idx: 5, exp: ne, fn: ne_254_uint16},
	{idx: 6, exp: lt, fn: lt_255_uint16},
	{idx: 6, exp: le, fn: le_255_uint16},
	{idx: 6, exp: gt, fn: gt_255_uint16},
	{idx: 6, exp: ge, fn: ge_255_uint16},
	{idx: 6, exp: eq, fn: eq_255_uint16},
	{idx: 6, exp: ne, fn: ne_255_uint16},
	{idx: 7, exp: lt, fn: lt_256_uint16},
	{idx: 7, exp: le, fn: le_256_uint16},
	{idx: 7, exp: gt, fn: gt_256_uint16},
	{idx: 7, exp: ge, fn: ge_256_uint16},
	{idx: 7, exp: eq, fn: eq_256_uint16},
	{idx: 7, exp: ne, fn: ne_256_uint16},
	{idx: 8, exp: lt, fn: lt_32766_uint16},
	{idx: 8, exp: le, fn: le_32766_uint16},
	{idx: 8, exp: gt, fn: gt_32766_uint16},
	{idx: 8, exp: ge, fn: ge_32766_uint16},
	{idx: 8, exp: eq, fn: eq_32766_uint16},
	{idx: 8, exp: ne, fn: ne_32766_uint16},
	{idx: 9, exp: lt, fn: lt_32767_uint16},
	{idx: 9, exp: le, fn: le_32767_uint16},
	{idx: 9, exp: gt, fn: gt_32767_uint16},
	{idx: 9, exp: ge, fn: ge_32767_uint16},
	{idx: 9, exp: eq, fn: eq_32767_uint16},
	{idx: 9, exp: ne, fn: ne_32767_uint16},
	{idx: 10, exp: lt, fn: lt_32768_uint16},
	{idx: 10, exp: le, fn: le_32768_uint16},
	{idx: 10, exp: gt, fn: gt_32768_uint16},
	{idx: 10, exp: ge, fn: ge_32768_uint16},
	{idx: 10, exp: eq, fn: eq_32768_uint16},
	{idx: 10, exp: ne, fn: ne_32768_uint16},
	{idx: 11, exp: lt, fn: lt_65534_uint16},
	{idx: 11, exp: le, fn: le_65534_uint16},
	{idx: 11, exp: gt, fn: gt_65534_uint16},
	{idx: 11, exp: ge, fn: ge_65534_uint16},
	{idx: 11, exp: eq, fn: eq_65534_uint16},
	{idx: 11, exp: ne, fn: ne_65534_uint16},
	{idx: 12, exp: lt, fn: lt_65535_uint16},
	{idx: 12, exp: le, fn: le_65535_uint16},
	{idx: 12, exp: gt, fn: gt_65535_uint16},
	{idx: 12, exp: ge, fn: ge_65535_uint16},
	{idx: 12, exp: eq, fn: eq_65535_uint16},
	{idx: 12, exp: ne, fn: ne_65535_uint16},
}

// uint8 tests
var uint8_vals = []uint8{
	0,
	1,
	126,
	127,
	128,
	254,
	255,
}

func lt_0_uint8(x uint8) bool   { return x < 0 }
func le_0_uint8(x uint8) bool   { return x <= 0 }
func gt_0_uint8(x uint8) bool   { return x > 0 }
func ge_0_uint8(x uint8) bool   { return x >= 0 }
func eq_0_uint8(x uint8) bool   { return x == 0 }
func ne_0_uint8(x uint8) bool   { return x != 0 }
func lt_1_uint8(x uint8) bool   { return x < 1 }
func le_1_uint8(x uint8) bool   { return x <= 1 }
func gt_1_uint8(x uint8) bool   { return x > 1 }
func ge_1_uint8(x uint8) bool   { return x >= 1 }
func eq_1_uint8(x uint8) bool   { return x == 1 }
func ne_1_uint8(x uint8) bool   { return x != 1 }
func lt_126_uint8(x uint8) bool { return x < 126 }
func le_126_uint8(x uint8) bool { return x <= 126 }
func gt_126_uint8(x uint8) bool { return x > 126 }
func ge_126_uint8(x uint8) bool { return x >= 126 }
func eq_126_uint8(x uint8) bool { return x == 126 }
func ne_126_uint8(x uint8) bool { return x != 126 }
func lt_127_uint8(x uint8) bool { return x < 127 }
func le_127_uint8(x uint8) bool { return x <= 127 }
func gt_127_uint8(x uint8) bool { return x > 127 }
func ge_127_uint8(x uint8) bool { return x >= 127 }
func eq_127_uint8(x uint8) bool { return x == 127 }
func ne_127_uint8(x uint8) bool { return x != 127 }
func lt_128_uint8(x uint8) bool { return x < 128 }
func le_128_uint8(x uint8) bool { return x <= 128 }
func gt_128_uint8(x uint8) bool { return x > 128 }
func ge_128_uint8(x uint8) bool { return x >= 128 }
func eq_128_uint8(x uint8) bool { return x == 128 }
func ne_128_uint8(x uint8) bool { return x != 128 }
func lt_254_uint8(x uint8) bool { return x < 254 }
func le_254_uint8(x uint8) bool { return x <= 254 }
func gt_254_uint8(x uint8) bool { return x > 254 }
func ge_254_uint8(x uint8) bool { return x >= 254 }
func eq_254_uint8(x uint8) bool { return x == 254 }
func ne_254_uint8(x uint8) bool { return x != 254 }
func lt_255_uint8(x uint8) bool { return x < 255 }
func le_255_uint8(x uint8) bool { return x <= 255 }
func gt_255_uint8(x uint8) bool { return x > 255 }
func ge_255_uint8(x uint8) bool { return x >= 255 }
func eq_255_uint8(x uint8) bool { return x == 255 }
func ne_255_uint8(x uint8) bool { return x != 255 }

var uint8_tests = []struct {
	idx int    // index of the constant used
	exp result // expected results
	fn  func(uint8) bool
}{
	{idx: 0, exp: lt, fn: lt_0_uint8},
	{idx: 0, exp: le, fn: le_0_uint8},
	{idx: 0, exp: gt, fn: gt_0_uint8},
	{idx: 0, exp: ge, fn: ge_0_uint8},
	{idx: 0, exp: eq, fn: eq_0_uint8},
	{idx: 0, exp: ne, fn: ne_0_uint8},
	{idx: 1, exp: lt, fn: lt_1_uint8},
	{idx: 1, exp: le, fn: le_1_uint8},
	{idx: 1, exp: gt, fn: gt_1_uint8},
	{idx: 1, exp: ge, fn: ge_1_uint8},
	{idx: 1, exp: eq, fn: eq_1_uint8},
	{idx: 1, exp: ne, fn: ne_1_uint8},
	{idx: 2, exp: lt, fn: lt_126_uint8},
	{idx: 2, exp: le, fn: le_126_uint8},
	{idx: 2, exp: gt, fn: gt_126_uint8},
	{idx: 2, exp: ge, fn: ge_126_uint8},
	{idx: 2, exp: eq, fn: eq_126_uint8},
	{idx: 2, exp: ne, fn: ne_126_uint8},
	{idx: 3, exp: lt, fn: lt_127_uint8},
	{idx: 3, exp: le, fn: le_127_uint8},
	{idx: 3, exp: gt, fn: gt_127_uint8},
	{idx: 3, exp: ge, fn: ge_127_uint8},
	{idx: 3, exp: eq, fn: eq_127_uint8},
	{idx: 3, exp: ne, fn: ne_127_uint8},
	{idx: 4, exp: lt, fn: lt_128_uint8},
	{idx: 4, exp: le, fn: le_128_uint8},
	{idx: 4, exp: gt, fn: gt_128_uint8},
	{idx: 4, exp: ge, fn: ge_128_uint8},
	{idx: 4, exp: eq, fn: eq_128_uint8},
	{idx: 4, exp: ne, fn: ne_128_uint8},
	{idx: 5, exp: lt, fn: lt_254_uint8},
	{idx: 5, exp: le, fn: le_254_uint8},
	{idx: 5, exp: gt, fn: gt_254_uint8},
	{idx: 5, exp: ge, fn: ge_254_uint8},
	{idx: 5, exp: eq, fn: eq_254_uint8},
	{idx: 5, exp: ne, fn: ne_254_uint8},
	{idx: 6, exp: lt, fn: lt_255_uint8},
	{idx: 6, exp: le, fn: le_255_uint8},
	{idx: 6, exp: gt, fn: gt_255_uint8},
	{idx: 6, exp: ge, fn: ge_255_uint8},
	{idx: 6, exp: eq, fn: eq_255_uint8},
	{idx: 6, exp: ne, fn: ne_255_uint8},
}

// int64 tests
var int64_vals = []int64{
	-9223372036854775808,
	-9223372036854775807,
	-2147483649,
	-2147483648,
	-2147483647,
	-32769,
	-32768,
	-32767,
	-129,
	-128,
	-127,
	-1,
	0,
	1,
	126,
	127,
	128,
	254,
	255,
	256,
	32766,
	32767,
	32768,
	65534,
	65535,
	65536,
	2147483646,
	2147483647,
	2147483648,
	4278190080,
	4294967294,
	4294967295,
	4294967296,
	1095216660480,
	9223372036854775806,
	9223372036854775807,
}

func lt_neg9223372036854775808_int64(x int64) bool { return x < -9223372036854775808 }
func le_neg9223372036854775808_int64(x int64) bool { return x <= -9223372036854775808 }
func gt_neg9223372036854775808_int64(x int64) bool { return x > -9223372036854775808 }
func ge_neg9223372036854775808_int64(x int64) bool { return x >= -9223372036854775808 }
func eq_neg9223372036854775808_int64(x int64) bool { return x == -9223372036854775808 }
func ne_neg9223372036854775808_int64(x int64) bool { return x != -9223372036854775808 }
func lt_neg9223372036854775807_int64(x int64) bool { return x < -9223372036854775807 }
func le_neg9223372036854775807_int64(x int64) bool { return x <= -9223372036854775807 }
func gt_neg9223372036854775807_int64(x int64) bool { return x > -9223372036854775807 }
func ge_neg9223372036854775807_int64(x int64) bool { return x >= -9223372036854775807 }
func eq_neg9223372036854775807_int64(x int64) bool { return x == -9223372036854775807 }
func ne_neg9223372036854775807_int64(x int64) bool { return x != -9223372036854775807 }
func lt_neg2147483649_int64(x int64) bool          { return x < -2147483649 }
func le_neg2147483649_int64(x int64) bool          { return x <= -2147483649 }
func gt_neg2147483649_int64(x int64) bool          { return x > -2147483649 }
func ge_neg2147483649_int64(x int64) bool          { return x >= -2147483649 }
func eq_neg2147483649_int64(x int64) bool          { return x == -2147483649 }
func ne_neg2147483649_int64(x int64) bool          { return x != -2147483649 }
func lt_neg2147483648_int64(x int64) bool          { return x < -2147483648 }
func le_neg2147483648_int64(x int64) bool          { return x <= -2147483648 }
func gt_neg2147483648_int64(x int64) bool          { return x > -2147483648 }
func ge_neg2147483648_int64(x int64) bool          { return x >= -2147483648 }
func eq_neg2147483648_int64(x int64) bool          { return x == -2147483648 }
func ne_neg2147483648_int64(x int64) bool          { return x != -2147483648 }
func lt_neg2147483647_int64(x int64) bool          { return x < -2147483647 }
func le_neg2147483647_int64(x int64) bool          { return x <= -2147483647 }
func gt_neg2147483647_int64(x int64) bool          { return x > -2147483647 }
func ge_neg2147483647_int64(x int64) bool          { return x >= -2147483647 }
func eq_neg2147483647_int64(x int64) bool          { return x == -2147483647 }
func ne_neg2147483647_int64(x int64) bool          { return x != -2147483647 }
func lt_neg32769_int64(x int64) bool               { return x < -32769 }
func le_neg32769_int64(x int64) bool               { return x <= -32769 }
func gt_neg32769_int64(x int64) bool               { return x > -32769 }
func ge_neg32769_int64(x int64) bool               { return x >= -32769 }
func eq_neg32769_int64(x int64) bool               { return x == -32769 }
func ne_neg32769_int64(x int64) bool               { return x != -32769 }
func lt_neg32768_int64(x int64) bool               { return x < -32768 }
func le_neg32768_int64(x int64) bool               { return x <= -32768 }
func gt_neg32768_int64(x int64) bool               { return x > -32768 }
func ge_neg32768_int64(x int64) bool               { return x >= -32768 }
func eq_neg32768_int64(x int64) bool               { return x == -32768 }
func ne_neg32768_int64(x int64) bool               { return x != -32768 }
func lt_neg32767_int64(x int64) bool               { return x < -32767 }
func le_neg32767_int64(x int64) bool               { return x <= -32767 }
func gt_neg32767_int64(x int64) bool               { return x > -32767 }
func ge_neg32767_int64(x int64) bool               { return x >= -32767 }
func eq_neg32767_int64(x int64) bool               { return x == -32767 }
func ne_neg32767_int64(x int64) bool               { return x != -32767 }
func lt_neg129_int64(x int64) bool                 { return x < -129 }
func le_neg129_int64(x int64) bool                 { return x <= -129 }
func gt_neg129_int64(x int64) bool                 { return x > -129 }
func ge_neg129_int64(x int64) bool                 { return x >= -129 }
func eq_neg129_int64(x int64) bool                 { return x == -129 }
func ne_neg129_int64(x int64) bool                 { return x != -129 }
func lt_neg128_int64(x int64) bool                 { return x < -128 }
func le_neg128_int64(x int64) bool                 { return x <= -128 }
func gt_neg128_int64(x int64) bool                 { return x > -128 }
func ge_neg128_int64(x int64) bool                 { return x >= -128 }
func eq_neg128_int64(x int64) bool                 { return x == -128 }
func ne_neg128_int64(x int64) bool                 { return x != -128 }
func lt_neg127_int64(x int64) bool                 { return x < -127 }
func le_neg127_int64(x int64) bool                 { return x <= -127 }
func gt_neg127_int64(x int64) bool                 { return x > -127 }
func ge_neg127_int64(x int64) bool                 { return x >= -127 }
func eq_neg127_int64(x int64) bool                 { return x == -127 }
func ne_neg127_int64(x int64) bool                 { return x != -127 }
func lt_neg1_int64(x int64) bool                   { return x < -1 }
func le_neg1_int64(x int64) bool                   { return x <= -1 }
func gt_neg1_int64(x int64) bool                   { return x > -1 }
func ge_neg1_int64(x int64) bool                   { return x >= -1 }
func eq_neg1_int64(x int64) bool                   { return x == -1 }
func ne_neg1_int64(x int64) bool                   { return x != -1 }
func lt_0_int64(x int64) bool                      { return x < 0 }
func le_0_int64(x int64) bool                      { return x <= 0 }
func gt_0_int64(x int64) bool                      { return x > 0 }
func ge_0_int64(x int64) bool                      { return x >= 0 }
func eq_0_int64(x int64) bool                      { return x == 0 }
func ne_0_int64(x int64) bool                      { return x != 0 }
func lt_1_int64(x int64) bool                      { return x < 1 }
func le_1_int64(x int64) bool                      { return x <= 1 }
func gt_1_int64(x int64) bool                      { return x > 1 }
func ge_1_int64(x int64) bool                      { return x >= 1 }
func eq_1_int64(x int64) bool                      { return x == 1 }
func ne_1_int64(x int64) bool                      { return x != 1 }
func lt_126_int64(x int64) bool                    { return x < 126 }
func le_126_int64(x int64) bool                    { return x <= 126 }
func gt_126_int64(x int64) bool                    { return x > 126 }
func ge_126_int64(x int64) bool                    { return x >= 126 }
func eq_126_int64(x int64) bool                    { return x == 126 }
func ne_126_int64(x int64) bool                    { return x != 126 }
func lt_127_int64(x int64) bool                    { return x < 127 }
func le_127_int64(x int64) bool                    { return x <= 127 }
func gt_127_int64(x int64) bool                    { return x > 127 }
func ge_127_int64(x int64) bool                    { return x >= 127 }
func eq_127_int64(x int64) bool                    { return x == 127 }
func ne_127_int64(x int64) bool                    { return x != 127 }
func lt_128_int64(x int64) bool                    { return x < 128 }
func le_128_int64(x int64) bool                    { return x <= 128 }
func gt_128_int64(x int64) bool                    { return x > 128 }
func ge_128_int64(x int64) bool                    { return x >= 128 }
func eq_128_int64(x int64) bool                    { return x == 128 }
func ne_128_int64(x int64) bool                    { return x != 128 }
func lt_254_int64(x int64) bool                    { return x < 254 }
func le_254_int64(x int64) bool                    { return x <= 254 }
func gt_254_int64(x int64) bool                    { return x > 254 }
func ge_254_int64(x int64) bool                    { return x >= 254 }
func eq_254_int64(x int64) bool                    { return x == 254 }
func ne_254_int64(x int64) bool                    { return x != 254 }
func lt_255_int64(x int64) bool                    { return x < 255 }
func le_255_int64(x int64) bool                    { return x <= 255 }
func gt_255_int64(x int64) bool                    { return x > 255 }
func ge_255_int64(x int64) bool                    { return x >= 255 }
func eq_255_int64(x int64) bool                    { return x == 255 }
func ne_255_int64(x int64) bool                    { return x != 255 }
func lt_256_int64(x int64) bool                    { return x < 256 }
func le_256_int64(x int64) bool                    { return x <= 256 }
func gt_256_int64(x int64) bool                    { return x > 256 }
func ge_256_int64(x int64) bool                    { return x >= 256 }
func eq_256_int64(x int64) bool                    { return x == 256 }
func ne_256_int64(x int64) bool                    { return x != 256 }
func lt_32766_int64(x int64) bool                  { return x < 32766 }
func le_32766_int64(x int64) bool                  { return x <= 32766 }
func gt_32766_int64(x int64) bool                  { return x > 32766 }
func ge_32766_int64(x int64) bool                  { return x >= 32766 }
func eq_32766_int64(x int64) bool                  { return x == 32766 }
func ne_32766_int64(x int64) bool                  { return x != 32766 }
func lt_32767_int64(x int64) bool                  { return x < 32767 }
func le_32767_int64(x int64) bool                  { return x <= 32767 }
func gt_32767_int64(x int64) bool                  { return x > 32767 }
func ge_32767_int64(x int64) bool                  { return x >= 32767 }
func eq_32767_int64(x int64) bool                  { return x == 32767 }
func ne_32767_int64(x int64) bool                  { return x != 32767 }
func lt_32768_int64(x int64) bool                  { return x < 32768 }
func le_32768_int64(x int64) bool                  { return x <= 32768 }
func gt_32768_int64(x int64) bool                  { return x > 32768 }
func ge_32768_int64(x int64) bool                  { return x >= 32768 }
func eq_32768_int64(x int64) bool                  { return x == 32768 }
func ne_32768_int64(x int64) bool                  { return x != 32768 }
func lt_65534_int64(x int64) bool                  { return x < 65534 }
func le_65534_int64(x int64) bool                  { return x <= 65534 }
func gt_65534_int64(x int64) bool                  { return x > 65534 }
func ge_65534_int64(x int64) bool                  { return x >= 65534 }
func eq_65534_int64(x int64) bool                  { return x == 65534 }
func ne_65534_int64(x int64) bool                  { return x != 65534 }
func lt_65535_int64(x int64) bool                  { return x < 65535 }
func le_65535_int64(x int64) bool                  { return x <= 65535 }
func gt_65535_int64(x int64) bool                  { return x > 65535 }
func ge_65535_int64(x int64) bool                  { return x >= 65535 }
func eq_65535_int64(x int64) bool                  { return x == 65535 }
func ne_65535_int64(x int64) bool                  { return x != 65535 }
func lt_65536_int64(x int64) bool                  { return x < 65536 }
func le_65536_int64(x int64) bool                  { return x <= 65536 }
func gt_65536_int64(x int64) bool                  { return x > 65536 }
func ge_65536_int64(x int64) bool                  { return x >= 65536 }
func eq_65536_int64(x int64) bool                  { return x == 65536 }
func ne_65536_int64(x int64) bool                  { return x != 65536 }
func lt_2147483646_int64(x int64) bool             { return x < 2147483646 }
func le_2147483646_int64(x int64) bool             { return x <= 2147483646 }
func gt_2147483646_int64(x int64) bool             { return x > 2147483646 }
func ge_2147483646_int64(x int64) bool             { return x >= 2147483646 }
func eq_2147483646_int64(x int64) bool             { return x == 2147483646 }
func ne_2147483646_int64(x int64) bool             { return x != 2147483646 }
func lt_2147483647_int64(x int64) bool             { return x < 2147483647 }
func le_2147483647_int64(x int64) bool             { return x <= 2147483647 }
func gt_2147483647_int64(x int64) bool             { return x > 2147483647 }
func ge_2147483647_int64(x int64) bool             { return x >= 2147483647 }
func eq_2147483647_int64(x int64) bool             { return x == 2147483647 }
func ne_2147483647_int64(x int64) bool             { return x != 2147483647 }
func lt_2147483648_int64(x int64) bool             { return x < 2147483648 }
func le_2147483648_int64(x int64) bool             { return x <= 2147483648 }
func gt_2147483648_int64(x int64) bool             { return x > 2147483648 }
func ge_2147483648_int64(x int64) bool             { return x >= 2147483648 }
func eq_2147483648_int64(x int64) bool             { return x == 2147483648 }
func ne_2147483648_int64(x int64) bool             { return x != 2147483648 }
func lt_4278190080_int64(x int64) bool             { return x < 4278190080 }
func le_4278190080_int64(x int64) bool             { return x <= 4278190080 }
func gt_4278190080_int64(x int64) bool             { return x > 4278190080 }
func ge_4278190080_int64(x int64) bool             { return x >= 4278190080 }
func eq_4278190080_int64(x int64) bool             { return x == 4278190080 }
func ne_4278190080_int64(x int64) bool             { return x != 4278190080 }
func lt_4294967294_int64(x int64) bool             { return x < 4294967294 }
func le_4294967294_int64(x int64) bool             { return x <= 4294967294 }
func gt_4294967294_int64(x int64) bool             { return x > 4294967294 }
func ge_4294967294_int64(x int64) bool             { return x >= 4294967294 }
func eq_4294967294_int64(x int64) bool             { return x == 4294967294 }
func ne_4294967294_int64(x int64) bool             { return x != 4294967294 }
func lt_4294967295_int64(x int64) bool             { return x < 4294967295 }
func le_4294967295_int64(x int64) bool             { return x <= 4294967295 }
func gt_4294967295_int64(x int64) bool             { return x > 4294967295 }
func ge_4294967295_int64(x int64) bool             { return x >= 4294967295 }
func eq_4294967295_int64(x int64) bool             { return x == 4294967295 }
func ne_4294967295_int64(x int64) bool             { return x != 4294967295 }
func lt_4294967296_int64(x int64) bool             { return x < 4294967296 }
func le_4294967296_int64(x int64) bool             { return x <= 4294967296 }
func gt_4294967296_int64(x int64) bool             { return x > 4294967296 }
func ge_4294967296_int64(x int64) bool             { return x >= 4294967296 }
func eq_4294967296_int64(x int64) bool             { return x == 4294967296 }
func ne_4294967296_int64(x int64) bool             { return x != 4294967296 }
func lt_1095216660480_int64(x int64) bool          { return x < 1095216660480 }
func le_1095216660480_int64(x int64) bool          { return x <= 1095216660480 }
func gt_1095216660480_int64(x int64) bool          { return x > 1095216660480 }
func ge_1095216660480_int64(x int64) bool          { return x >= 1095216660480 }
func eq_1095216660480_int64(x int64) bool          { return x == 1095216660480 }
func ne_1095216660480_int64(x int64) bool          { return x != 1095216660480 }
func lt_9223372036854775806_int64(x int64) bool    { return x < 9223372036854775806 }
func le_9223372036854775806_int64(x int64) bool    { return x <= 9223372036854775806 }
func gt_9223372036854775806_int64(x int64) bool    { return x > 9223372036854775806 }
func ge_9223372036854775806_int64(x int64) bool    { return x >= 9223372036854775806 }
func eq_9223372036854775806_int64(x int64) bool    { return x == 9223372036854775806 }
func ne_9223372036854775806_int64(x int64) bool    { return x != 9223372036854775806 }
func lt_9223372036854775807_int64(x int64) bool    { return x < 9223372036854775807 }
func le_9223372036854775807_int64(x int64) bool    { return x <= 9223372036854775807 }
func gt_9223372036854775807_int64(x int64) bool    { return x > 9223372036854775807 }
func ge_9223372036854775807_int64(x int64) bool    { return x >= 9223372036854775807 }
func eq_9223372036854775807_int64(x int64) bool    { return x == 9223372036854775807 }
func ne_9223372036854775807_int64(x int64) bool    { return x != 9223372036854775807 }

var int64_tests = []struct {
	idx int    // index of the constant used
	exp result // expected results
	fn  func(int64) bool
}{
	{idx: 0, exp: lt, fn: lt_neg9223372036854775808_int64},
	{idx: 0, exp: le, fn: le_neg9223372036854775808_int64},
	{idx: 0, exp: gt, fn: gt_neg9223372036854775808_int64},
	{idx: 0, exp: ge, fn: ge_neg9223372036854775808_int64},
	{idx: 0, exp: eq, fn: eq_neg9223372036854775808_int64},
	{idx: 0, exp: ne, fn: ne_neg9223372036854775808_int64},
	{idx: 1, exp: lt, fn: lt_neg9223372036854775807_int64},
	{idx: 1, exp: le, fn: le_neg9223372036854775807_int64},
	{idx: 1, exp: gt, fn: gt_neg9223372036854775807_int64},
	{idx: 1, exp: ge, fn: ge_neg9223372036854775807_int64},
	{idx: 1, exp: eq, fn: eq_neg9223372036854775807_int64},
	{idx: 1, exp: ne, fn: ne_neg9223372036854775807_int64},
	{idx: 2, exp: lt, fn: lt_neg2147483649_int64},
	{idx: 2, exp: le, fn: le_neg2147483649_int64},
	{idx: 2, exp: gt, fn: gt_neg2147483649_int64},
	{idx: 2, exp: ge, fn: ge_neg2147483649_int64},
	{idx: 2, exp: eq, fn: eq_neg2147483649_int64},
	{idx: 2, exp: ne, fn: ne_neg2147483649_int64},
	{idx: 3, exp: lt, fn: lt_neg2147483648_int64},
	{idx: 3, exp: le, fn: le_neg2147483648_int64},
	{idx: 3, exp: gt, fn: gt_neg2147483648_int64},
	{idx: 3, exp: ge, fn: ge_neg2147483648_int64},
	{idx: 3, exp: eq, fn: eq_neg2147483648_int64},
	{idx: 3, exp: ne, fn: ne_neg2147483648_int64},
	{idx: 4, exp: lt, fn: lt_neg2147483647_int64},
	{idx: 4, exp: le, fn: le_neg2147483647_int64},
	{idx: 4, exp: gt, fn: gt_neg2147483647_int64},
	{idx: 4, exp: ge, fn: ge_neg2147483647_int64},
	{idx: 4, exp: eq, fn: eq_neg2147483647_int64},
	{idx: 4, exp: ne, fn: ne_neg2147483647_int64},
	{idx: 5, exp: lt, fn: lt_neg32769_int64},
	{idx: 5, exp: le, fn: le_neg32769_int64},
	{idx: 5, exp: gt, fn: gt_neg32769_int64},
	{idx: 5, exp: ge, fn: ge_neg32769_int64},
	{idx: 5, exp: eq, fn: eq_neg32769_int64},
	{idx: 5, exp: ne, fn: ne_neg32769_int64},
	{idx: 6, exp: lt, fn: lt_neg32768_int64},
	{idx: 6, exp: le, fn: le_neg32768_int64},
	{idx: 6, exp: gt, fn: gt_neg32768_int64},
	{idx: 6, exp: ge, fn: ge_neg32768_int64},
	{idx: 6, exp: eq, fn: eq_neg32768_int64},
	{idx: 6, exp: ne, fn: ne_neg32768_int64},
	{idx: 7, exp: lt, fn: lt_neg32767_int64},
	{idx: 7, exp: le, fn: le_neg32767_int64},
	{idx: 7, exp: gt, fn: gt_neg32767_int64},
	{idx: 7, exp: ge, fn: ge_neg32767_int64},
	{idx: 7, exp: eq, fn: eq_neg32767_int64},
	{idx: 7, exp: ne, fn: ne_neg32767_int64},
	{idx: 8, exp: lt, fn: lt_neg129_int64},
	{idx: 8, exp: le, fn: le_neg129_int64},
	{idx: 8, exp: gt, fn: gt_neg129_int64},
	{idx: 8, exp: ge, fn: ge_neg129_int64},
	{idx: 8, exp: eq, fn: eq_neg129_int64},
	{idx: 8, exp: ne, fn: ne_neg129_int64},
	{idx: 9, exp: lt, fn: lt_neg128_int64},
	{idx: 9, exp: le, fn: le_neg128_int64},
	{idx: 9, exp: gt, fn: gt_neg128_int64},
	{idx: 9, exp: ge, fn: ge_neg128_int64},
	{idx: 9, exp: eq, fn: eq_neg128_int64},
	{idx: 9, exp: ne, fn: ne_neg128_int64},
	{idx: 10, exp: lt, fn: lt_neg127_int64},
	{idx: 10, exp: le, fn: le_neg127_int64},
	{idx: 10, exp: gt, fn: gt_neg127_int64},
	{idx: 10, exp: ge, fn: ge_neg127_int64},
	{idx: 10, exp: eq, fn: eq_neg127_int64},
	{idx: 10, exp: ne, fn: ne_neg127_int64},
	{idx: 11, exp: lt, fn: lt_neg1_int64},
	{idx: 11, exp: le, fn: le_neg1_int64},
	{idx: 11, exp: gt, fn: gt_neg1_int64},
	{idx: 11, exp: ge, fn: ge_neg1_int64},
	{idx: 11, exp: eq, fn: eq_neg1_int64},
	{idx: 11, exp: ne, fn: ne_neg1_int64},
	{idx: 12, exp: lt, fn: lt_0_int64},
	{idx: 12, exp: le, fn: le_0_int64},
	{idx: 12, exp: gt, fn: gt_0_int64},
	{idx: 12, exp: ge, fn: ge_0_int64},
	{idx: 12, exp: eq, fn: eq_0_int64},
	{idx: 12, exp: ne, fn: ne_0_int64},
	{idx: 13, exp: lt, fn: lt_1_int64},
	{idx: 13, exp: le, fn: le_1_int64},
	{idx: 13, exp: gt, fn: gt_1_int64},
	{idx: 13, exp: ge, fn: ge_1_int64},
	{idx: 13, exp: eq, fn: eq_1_int64},
	{idx: 13, exp: ne, fn: ne_1_int64},
	{idx: 14, exp: lt, fn: lt_126_int64},
	{idx: 14, exp: le, fn: le_126_int64},
	{idx: 14, exp: gt, fn: gt_126_int64},
	{idx: 14, exp: ge, fn: ge_126_int64},
	{idx: 14, exp: eq, fn: eq_126_int64},
	{idx: 14, exp: ne, fn: ne_126_int64},
	{idx: 15, exp: lt, fn: lt_127_int64},
	{idx: 15, exp: le, fn: le_127_int64},
	{idx: 15, exp: gt, fn: gt_127_int64},
	{idx: 15, exp: ge, fn: ge_127_int64},
	{idx: 15, exp: eq, fn: eq_127_int64},
	{idx: 15, exp: ne, fn: ne_127_int64},
	{idx: 16, exp: lt, fn: lt_128_int64},
	{idx: 16, exp: le, fn: le_128_int64},
	{idx: 16, exp: gt, fn: gt_128_int64},
	{idx: 16, exp: ge, fn: ge_128_int64},
	{idx: 16, exp: eq, fn: eq_128_int64},
	{idx: 16, exp: ne, fn: ne_128_int64},
	{idx: 17, exp: lt, fn: lt_254_int64},
	{idx: 17, exp: le, fn: le_254_int64},
	{idx: 17, exp: gt, fn: gt_254_int64},
	{idx: 17, exp: ge, fn: ge_254_int64},
	{idx: 17, exp: eq, fn: eq_254_int64},
	{idx: 17, exp: ne, fn: ne_254_int64},
	{idx: 18, exp: lt, fn: lt_255_int64},
	{idx: 18, exp: le, fn: le_255_int64},
	{idx: 18, exp: gt, fn: gt_255_int64},
	{idx: 18, exp: ge, fn: ge_255_int64},
	{idx: 18, exp: eq, fn: eq_255_int64},
	{idx: 18, exp: ne, fn: ne_255_int64},
	{idx: 19, exp: lt, fn: lt_256_int64},
	{idx: 19, exp: le, fn: le_256_int64},
	{idx: 19, exp: gt, fn: gt_256_int64},
	{idx: 19, exp: ge, fn: ge_256_int64},
	{idx: 19, exp: eq, fn: eq_256_int64},
	{idx: 19, exp: ne, fn: ne_256_int64},
	{idx: 20, exp: lt, fn: lt_32766_int64},
	{idx: 20, exp: le, fn: le_32766_int64},
	{idx: 20, exp: gt, fn: gt_32766_int64},
	{idx: 20, exp: ge, fn: ge_32766_int64},
	{idx: 20, exp: eq, fn: eq_32766_int64},
	{idx: 20, exp: ne, fn: ne_32766_int64},
	{idx: 21, exp: lt, fn: lt_32767_int64},
	{idx: 21, exp: le, fn: le_32767_int64},
	{idx: 21, exp: gt, fn: gt_32767_int64},
	{idx: 21, exp: ge, fn: ge_32767_int64},
	{idx: 21, exp: eq, fn: eq_32767_int64},
	{idx: 21, exp: ne, fn: ne_32767_int64},
	{idx: 22, exp: lt, fn: lt_32768_int64},
	{idx: 22, exp: le, fn: le_32768_int64},
	{idx: 22, exp: gt, fn: gt_32768_int64},
	{idx: 22, exp: ge, fn: ge_32768_int64},
	{idx: 22, exp: eq, fn: eq_32768_int64},
	{idx: 22, exp: ne, fn: ne_32768_int64},
	{idx: 23, exp: lt, fn: lt_65534_int64},
	{idx: 23, exp: le, fn: le_65534_int64},
	{idx: 23, exp: gt, fn: gt_65534_int64},
	{idx: 23, exp: ge, fn: ge_65534_int64},
	{idx: 23, exp: eq, fn: eq_65534_int64},
	{idx: 23, exp: ne, fn: ne_65534_int64},
	{idx: 24, exp: lt, fn: lt_65535_int64},
	{idx: 24, exp: le, fn: le_65535_int64},
	{idx: 24, exp: gt, fn: gt_65535_int64},
	{idx: 24, exp: ge, fn: ge_65535_int64},
	{idx: 24, exp: eq, fn: eq_65535_int64},
	{idx: 24, exp: ne, fn: ne_65535_int64},
	{idx: 25, exp: lt, fn: lt_65536_int64},
	{idx: 25, exp: le, fn: le_65536_int64},
	{idx: 25, exp: gt, fn: gt_65536_int64},
	{idx: 25, exp: ge, fn: ge_65536_int64},
	{idx: 25, exp: eq, fn: eq_65536_int64},
	{idx: 25, exp: ne, fn: ne_65536_int64},
	{idx: 26, exp: lt, fn: lt_2147483646_int64},
	{idx: 26, exp: le, fn: le_2147483646_int64},
	{idx: 26, exp: gt, fn: gt_2147483646_int64},
	{idx: 26, exp: ge, fn: ge_2147483646_int64},
	{idx: 26, exp: eq, fn: eq_2147483646_int64},
	{idx: 26, exp: ne, fn: ne_2147483646_int64},
	{idx: 27, exp: lt, fn: lt_2147483647_int64},
	{idx: 27, exp: le, fn: le_2147483647_int64},
	{idx: 27, exp: gt, fn: gt_2147483647_int64},
	{idx: 27, exp: ge, fn: ge_2147483647_int64},
	{idx: 27, exp: eq, fn: eq_2147483647_int64},
	{idx: 27, exp: ne, fn: ne_2147483647_int64},
	{idx: 28, exp: lt, fn: lt_2147483648_int64},
	{idx: 28, exp: le, fn: le_2147483648_int64},
	{idx: 28, exp: gt, fn: gt_2147483648_int64},
	{idx: 28, exp: ge, fn: ge_2147483648_int64},
	{idx: 28, exp: eq, fn: eq_2147483648_int64},
	{idx: 28, exp: ne, fn: ne_2147483648_int64},
	{idx: 29, exp: lt, fn: lt_4278190080_int64},
	{idx: 29, exp: le, fn: le_4278190080_int64},
	{idx: 29, exp: gt, fn: gt_4278190080_int64},
	{idx: 29, exp: ge, fn: ge_4278190080_int64},
	{idx: 29, exp: eq, fn: eq_4278190080_int64},
	{idx: 29, exp: ne, fn: ne_4278190080_int64},
	{idx: 30, exp: lt, fn: lt_4294967294_int64},
	{idx: 30, exp: le, fn: le_4294967294_int64},
	{idx: 30, exp: gt, fn: gt_4294967294_int64},
	{idx: 30, exp: ge, fn: ge_4294967294_int64},
	{idx: 30, exp: eq, fn: eq_4294967294_int64},
	{idx: 30, exp: ne, fn: ne_4294967294_int64},
	{idx: 31, exp: lt, fn: lt_4294967295_int64},
	{idx: 31, exp: le, fn: le_4294967295_int64},
	{idx: 31, exp: gt, fn: gt_4294967295_int64},
	{idx: 31, exp: ge, fn: ge_4294967295_int64},
	{idx: 31, exp: eq, fn: eq_4294967295_int64},
	{idx: 31, exp: ne, fn: ne_4294967295_int64},
	{idx: 32, exp: lt, fn: lt_4294967296_int64},
	{idx: 32, exp: le, fn: le_4294967296_int64},
	{idx: 32, exp: gt, fn: gt_4294967296_int64},
	{idx: 32, exp: ge, fn: ge_4294967296_int64},
	{idx: 32, exp: eq, fn: eq_4294967296_int64},
	{idx: 32, exp: ne, fn: ne_4294967296_int64},
	{idx: 33, exp: lt, fn: lt_1095216660480_int64},
	{idx: 33, exp: le, fn: le_1095216660480_int64},
	{idx: 33, exp: gt, fn: gt_1095216660480_int64},
	{idx: 33, exp: ge, fn: ge_1095216660480_int64},
	{idx: 33, exp: eq, fn: eq_1095216660480_int64},
	{idx: 33, exp: ne, fn: ne_1095216660480_int64},
	{idx: 34, exp: lt, fn: lt_9223372036854775806_int64},
	{idx: 34, exp: le, fn: le_9223372036854775806_int64},
	{idx: 34, exp: gt, fn: gt_9223372036854775806_int64},
	{idx: 34, exp: ge, fn: ge_9223372036854775806_int64},
	{idx: 34, exp: eq, fn: eq_9223372036854775806_int64},
	{idx: 34, exp: ne, fn: ne_9223372036854775806_int64},
	{idx: 35, exp: lt, fn: lt_9223372036854775807_int64},
	{idx: 35, exp: le, fn: le_9223372036854775807_int64},
	{idx: 35, exp: gt, fn: gt_9223372036854775807_int64},
	{idx: 35, exp: ge, fn: ge_9223372036854775807_int64},
	{idx: 35, exp: eq, fn: eq_9223372036854775807_int64},
	{idx: 35, exp: ne, fn: ne_9223372036854775807_int64},
}

// int32 tests
var int32_vals = []int32{
	-2147483648,
	-2147483647,
	-32769,
	-32768,
	-32767,
	-129,
	-128,
	-127,
	-1,
	0,
	1,
	126,
	127,
	128,
	254,
	255,
	256,
	32766,
	32767,
	32768,
	65534,
	65535,
	65536,
	2147483646,
	2147483647,
}

func lt_neg2147483648_int32(x int32) bool { return x < -2147483648 }
func le_neg2147483648_int32(x int32) bool { return x <= -2147483648 }
func gt_neg2147483648_int32(x int32) bool { return x > -2147483648 }
func ge_neg2147483648_int32(x int32) bool { return x >= -2147483648 }
func eq_neg2147483648_int32(x int32) bool { return x == -2147483648 }
func ne_neg2147483648_int32(x int32) bool { return x != -2147483648 }
func lt_neg2147483647_int32(x int32) bool { return x < -2147483647 }
func le_neg2147483647_int32(x int32) bool { return x <= -2147483647 }
func gt_neg2147483647_int32(x int32) bool { return x > -2147483647 }
func ge_neg2147483647_int32(x int32) bool { return x >= -2147483647 }
func eq_neg2147483647_int32(x int32) bool { return x == -2147483647 }
func ne_neg2147483647_int32(x int32) bool { return x != -2147483647 }
func lt_neg32769_int32(x int32) bool      { return x < -32769 }
func le_neg32769_int32(x int32) bool      { return x <= -32769 }
func gt_neg32769_int32(x int32) bool      { return x > -32769 }
func ge_neg32769_int32(x int32) bool      { return x >= -32769 }
func eq_neg32769_int32(x int32) bool      { return x == -32769 }
func ne_neg32769_int32(x int32) bool      { return x != -32769 }
func lt_neg32768_int32(x int32) bool      { return x < -32768 }
func le_neg32768_int32(x int32) bool      { return x <= -32768 }
func gt_neg32768_int32(x int32) bool      { return x > -32768 }
func ge_neg32768_int32(x int32) bool      { return x >= -32768 }
func eq_neg32768_int32(x int32) bool      { return x == -32768 }
func ne_neg32768_int32(x int32) bool      { return x != -32768 }
func lt_neg32767_int32(x int32) bool      { return x < -32767 }
func le_neg32767_int32(x int32) bool      { return x <= -32767 }
func gt_neg32767_int32(x int32) bool      { return x > -32767 }
func ge_neg32767_int32(x int32) bool      { return x >= -32767 }
func eq_neg32767_int32(x int32) bool      { return x == -32767 }
func ne_neg32767_int32(x int32) bool      { return x != -32767 }
func lt_neg129_int32(x int32) bool        { return x < -129 }
func le_neg129_int32(x int32) bool        { return x <= -129 }
func gt_neg129_int32(x int32) bool        { return x > -129 }
func ge_neg129_int32(x int32) bool        { return x >= -129 }
func eq_neg129_int32(x int32) bool        { return x == -129 }
func ne_neg129_int32(x int32) bool        { return x != -129 }
func lt_neg128_int32(x int32) bool        { return x < -128 }
func le_neg128_int32(x int32) bool        { return x <= -128 }
func gt_neg128_int32(x int32) bool        { return x > -128 }
func ge_neg128_int32(x int32) bool        { return x >= -128 }
func eq_neg128_int32(x int32) bool        { return x == -128 }
func ne_neg128_int32(x int32) bool        { return x != -128 }
func lt_neg127_int32(x int32) bool        { return x < -127 }
func le_neg127_int32(x int32) bool        { return x <= -127 }
func gt_neg127_int32(x int32) bool        { return x > -127 }
func ge_neg127_int32(x int32) bool        { return x >= -127 }
func eq_neg127_int32(x int32) bool        { return x == -127 }
func ne_neg127_int32(x int32) bool        { return x != -127 }
func lt_neg1_int32(x int32) bool          { return x < -1 }
func le_neg1_int32(x int32) bool          { return x <= -1 }
func gt_neg1_int32(x int32) bool          { return x > -1 }
func ge_neg1_int32(x int32) bool          { return x >= -1 }
func eq_neg1_int32(x int32) bool          { return x == -1 }
func ne_neg1_int32(x int32) bool          { return x != -1 }
func lt_0_int32(x int32) bool             { return x < 0 }
func le_0_int32(x int32) bool             { return x <= 0 }
func gt_0_int32(x int32) bool             { return x > 0 }
func ge_0_int32(x int32) bool             { return x >= 0 }
func eq_0_int32(x int32) bool             { return x == 0 }
func ne_0_int32(x int32) bool             { return x != 0 }
func lt_1_int32(x int32) bool             { return x < 1 }
func le_1_int32(x int32) bool             { return x <= 1 }
func gt_1_int32(x int32) bool             { return x > 1 }
func ge_1_int32(x int32) bool             { return x >= 1 }
func eq_1_int32(x int32) bool             { return x == 1 }
func ne_1_int32(x int32) bool             { return x != 1 }
func lt_126_int32(x int32) bool           { return x < 126 }
func le_126_int32(x int32) bool           { return x <= 126 }
func gt_126_int32(x int32) bool           { return x > 126 }
func ge_126_int32(x int32) bool           { return x >= 126 }
func eq_126_int32(x int32) bool           { return x == 126 }
func ne_126_int32(x int32) bool           { return x != 126 }
func lt_127_int32(x int32) bool           { return x < 127 }
func le_127_int32(x int32) bool           { return x <= 127 }
func gt_127_int32(x int32) bool           { return x > 127 }
func ge_127_int32(x int32) bool           { return x >= 127 }
func eq_127_int32(x int32) bool           { return x == 127 }
func ne_127_int32(x int32) bool           { return x != 127 }
func lt_128_int32(x int32) bool           { return x < 128 }
func le_128_int32(x int32) bool           { return x <= 128 }
func gt_128_int32(x int32) bool           { return x > 128 }
func ge_128_int32(x int32) bool           { return x >= 128 }
func eq_128_int32(x int32) bool           { return x == 128 }
func ne_128_int32(x int32) bool           { return x != 128 }
func lt_254_int32(x int32) bool           { return x < 254 }
func le_254_int32(x int32) bool           { return x <= 254 }
func gt_254_int32(x int32) bool           { return x > 254 }
func ge_254_int32(x int32) bool           { return x >= 254 }
func eq_254_int32(x int32) bool           { return x == 254 }
func ne_254_int32(x int32) bool           { return x != 254 }
func lt_255_int32(x int32) bool           { return x < 255 }
func le_255_int32(x int32) bool           { return x <= 255 }
func gt_255_int32(x int32) bool           { return x > 255 }
func ge_255_int32(x int32) bool           { return x >= 255 }
func eq_255_int32(x int32) bool           { return x == 255 }
func ne_255_int32(x int32) bool           { return x != 255 }
func lt_256_int32(x int32) bool           { return x < 256 }
func le_256_int32(x int32) bool           { return x <= 256 }
func gt_256_int32(x int32) bool           { return x > 256 }
func ge_256_int32(x int32) bool           { return x >= 256 }
func eq_256_int32(x int32) bool           { return x == 256 }
func ne_256_int32(x int32) bool           { return x != 256 }
func lt_32766_int32(x int32) bool         { return x < 32766 }
func le_32766_int32(x int32) bool         { return x <= 32766 }
func gt_32766_int32(x int32) bool         { return x > 32766 }
func ge_32766_int32(x int32) bool         { return x >= 32766 }
func eq_32766_int32(x int32) bool         { return x == 32766 }
func ne_32766_int32(x int32) bool         { return x != 32766 }
func lt_32767_int32(x int32) bool         { return x < 32767 }
func le_32767_int32(x int32) bool         { return x <= 32767 }
func gt_32767_int32(x int32) bool         { return x > 32767 }
func ge_32767_int32(x int32) bool         { return x >= 32767 }
func eq_32767_int32(x int32) bool         { return x == 32767 }
func ne_32767_int32(x int32) bool         { return x != 32767 }
func lt_32768_int32(x int32) bool         { return x < 32768 }
func le_32768_int32(x int32) bool         { return x <= 32768 }
func gt_32768_int32(x int32) bool         { return x > 32768 }
func ge_32768_int32(x int32) bool         { return x >= 32768 }
func eq_32768_int32(x int32) bool         { return x == 32768 }
func ne_32768_int32(x int32) bool         { return x != 32768 }
func lt_65534_int32(x int32) bool         { return x < 65534 }
func le_65534_int32(x int32) bool         { return x <= 65534 }
func gt_65534_int32(x int32) bool         { return x > 65534 }
func ge_65534_int32(x int32) bool         { return x >= 65534 }
func eq_65534_int32(x int32) bool         { return x == 65534 }
func ne_65534_int32(x int32) bool         { return x != 65534 }
func lt_65535_int32(x int32) bool         { return x < 65535 }
func le_65535_int32(x int32) bool         { return x <= 65535 }
func gt_65535_int32(x int32) bool         { return x > 65535 }
func ge_65535_int32(x int32) bool         { return x >= 65535 }
func eq_65535_int32(x int32) bool         { return x == 65535 }
func ne_65535_int32(x int32) bool         { return x != 65535 }
func lt_65536_int32(x int32) bool         { return x < 65536 }
func le_65536_int32(x int32) bool         { return x <= 65536 }
func gt_65536_int32(x int32) bool         { return x > 65536 }
func ge_65536_int32(x int32) bool         { return x >= 65536 }
func eq_65536_int32(x int32) bool         { return x == 65536 }
func ne_65536_int32(x int32) bool         { return x != 65536 }
func lt_2147483646_int32(x int32) bool    { return x < 2147483646 }
func le_2147483646_int32(x int32) bool    { return x <= 2147483646 }
func gt_2147483646_int32(x int32) bool    { return x > 2147483646 }
func ge_2147483646_int32(x int32) bool    { return x >= 2147483646 }
func eq_2147483646_int32(x int32) bool    { return x == 2147483646 }
func ne_2147483646_int32(x int32) bool    { return x != 2147483646 }
func lt_2147483647_int32(x int32) bool    { return x < 2147483647 }
func le_2147483647_int32(x int32) bool    { return x <= 2147483647 }
func gt_2147483647_int32(x int32) bool    { return x > 2147483647 }
func ge_2147483647_int32(x int32) bool    { return x >= 2147483647 }
func eq_2147483647_int32(x int32) bool    { return x == 2147483647 }
func ne_2147483647_int32(x int32) bool    { return x != 2147483647 }

var int32_tests = []struct {
	idx int    // index of the constant used
	exp result // expected results
	fn  func(int32) bool
}{
	{idx: 0, exp: lt, fn: lt_neg2147483648_int32},
	{idx: 0, exp: le, fn: le_neg2147483648_int32},
	{idx: 0, exp: gt, fn: gt_neg2147483648_int32},
	{idx: 0, exp: ge, fn: ge_neg2147483648_int32},
	{idx: 0, exp: eq, fn: eq_neg2147483648_int32},
	{idx: 0, exp: ne, fn: ne_neg2147483648_int32},
	{idx: 1, exp: lt, fn: lt_neg2147483647_int32},
	{idx: 1, exp: le, fn: le_neg2147483647_int32},
	{idx: 1, exp: gt, fn: gt_neg2147483647_int32},
	{idx: 1, exp: ge, fn: ge_neg2147483647_int32},
	{idx: 1, exp: eq, fn: eq_neg2147483647_int32},
	{idx: 1, exp: ne, fn: ne_neg2147483647_int32},
	{idx: 2, exp: lt, fn: lt_neg32769_int32},
	{idx: 2, exp: le, fn: le_neg32769_int32},
	{idx: 2, exp: gt, fn: gt_neg32769_int32},
	{idx: 2, exp: ge, fn: ge_neg32769_int32},
	{idx: 2, exp: eq, fn: eq_neg32769_int32},
	{idx: 2, exp: ne, fn: ne_neg32769_int32},
	{idx: 3, exp: lt, fn: lt_neg32768_int32},
	{idx: 3, exp: le, fn: le_neg32768_int32},
	{idx: 3, exp: gt, fn: gt_neg32768_int32},
	{idx: 3, exp: ge, fn: ge_neg32768_int32},
	{idx: 3, exp: eq, fn: eq_neg32768_int32},
	{idx: 3, exp: ne, fn: ne_neg32768_int32},
	{idx: 4, exp: lt, fn: lt_neg32767_int32},
	{idx: 4, exp: le, fn: le_neg32767_int32},
	{idx: 4, exp: gt, fn: gt_neg32767_int32},
	{idx: 4, exp: ge, fn: ge_neg32767_int32},
	{idx: 4, exp: eq, fn: eq_neg32767_int32},
	{idx: 4, exp: ne, fn: ne_neg32767_int32},
	{idx: 5, exp: lt, fn: lt_neg129_int32},
	{idx: 5, exp: le, fn: le_neg129_int32},
	{idx: 5, exp: gt, fn: gt_neg129_int32},
	{idx: 5, exp: ge, fn: ge_neg129_int32},
	{idx: 5, exp: eq, fn: eq_neg129_int32},
	{idx: 5, exp: ne, fn: ne_neg129_int32},
	{idx: 6, exp: lt, fn: lt_neg128_int32},
	{idx: 6, exp: le, fn: le_neg128_int32},
	{idx: 6, exp: gt, fn: gt_neg128_int32},
	{idx: 6, exp: ge, fn: ge_neg128_int32},
	{idx: 6, exp: eq, fn: eq_neg128_int32},
	{idx: 6, exp: ne, fn: ne_neg128_int32},
	{idx: 7, exp: lt, fn: lt_neg127_int32},
	{idx: 7, exp: le, fn: le_neg127_int32},
	{idx: 7, exp: gt, fn: gt_neg127_int32},
	{idx: 7, exp: ge, fn: ge_neg127_int32},
	{idx: 7, exp: eq, fn: eq_neg127_int32},
	{idx: 7, exp: ne, fn: ne_neg127_int32},
	{idx: 8, exp: lt, fn: lt_neg1_int32},
	{idx: 8, exp: le, fn: le_neg1_int32},
	{idx: 8, exp: gt, fn: gt_neg1_int32},
	{idx: 8, exp: ge, fn: ge_neg1_int32},
	{idx: 8, exp: eq, fn: eq_neg1_int32},
	{idx: 8, exp: ne, fn: ne_neg1_int32},
	{idx: 9, exp: lt, fn: lt_0_int32},
	{idx: 9, exp: le, fn: le_0_int32},
	{idx: 9, exp: gt, fn: gt_0_int32},
	{idx: 9, exp: ge, fn: ge_0_int32},
	{idx: 9, exp: eq, fn: eq_0_int32},
	{idx: 9, exp: ne, fn: ne_0_int32},
	{idx: 10, exp: lt, fn: lt_1_int32},
	{idx: 10, exp: le, fn: le_1_int32},
	{idx: 10, exp: gt, fn: gt_1_int32},
	{idx: 10, exp: ge, fn: ge_1_int32},
	{idx: 10, exp: eq, fn: eq_1_int32},
	{idx: 10, exp: ne, fn: ne_1_int32},
	{idx: 11, exp: lt, fn: lt_126_int32},
	{idx: 11, exp: le, fn: le_126_int32},
	{idx: 11, exp: gt, fn: gt_126_int32},
	{idx: 11, exp: ge, fn: ge_126_int32},
	{idx: 11, exp: eq, fn: eq_126_int32},
	{idx: 11, exp: ne, fn: ne_126_int32},
	{idx: 12, exp: lt, fn: lt_127_int32},
	{idx: 12, exp: le, fn: le_127_int32},
	{idx: 12, exp: gt, fn: gt_127_int32},
	{idx: 12, exp: ge, fn: ge_127_int32},
	{idx: 12, exp: eq, fn: eq_127_int32},
	{idx: 12, exp: ne, fn: ne_127_int32},
	{idx: 13, exp: lt, fn: lt_128_int32},
	{idx: 13, exp: le, fn: le_128_int32},
	{idx: 13, exp: gt, fn: gt_128_int32},
	{idx: 13, exp: ge, fn: ge_128_int32},
	{idx: 13, exp: eq, fn: eq_128_int32},
	{idx: 13, exp: ne, fn: ne_128_int32},
	{idx: 14, exp: lt, fn: lt_254_int32},
	{idx: 14, exp: le, fn: le_254_int32},
	{idx: 14, exp: gt, fn: gt_254_int32},
	{idx: 14, exp: ge, fn: ge_254_int32},
	{idx: 14, exp: eq, fn: eq_254_int32},
	{idx: 14, exp: ne, fn: ne_254_int32},
	{idx: 15, exp: lt, fn: lt_255_int32},
	{idx: 15, exp: le, fn: le_255_int32},
	{idx: 15, exp: gt, fn: gt_255_int32},
	{idx: 15, exp: ge, fn: ge_255_int32},
	{idx: 15, exp: eq, fn: eq_255_int32},
	{idx: 15, exp: ne, fn: ne_255_int32},
	{idx: 16, exp: lt, fn: lt_256_int32},
	{idx: 16, exp: le, fn: le_256_int32},
	{idx: 16, exp: gt, fn: gt_256_int32},
	{idx: 16, exp: ge, fn: ge_256_int32},
	{idx: 16, exp: eq, fn: eq_256_int32},
	{idx: 16, exp: ne, fn: ne_256_int32},
	{idx: 17, exp: lt, fn: lt_32766_int32},
	{idx: 17, exp: le, fn: le_32766_int32},
	{idx: 17, exp: gt, fn: gt_32766_int32},
	{idx: 17, exp: ge, fn: ge_32766_int32},
	{idx: 17, exp: eq, fn: eq_32766_int32},
	{idx: 17, exp: ne, fn: ne_32766_int32},
	{idx: 18, exp: lt, fn: lt_32767_int32},
	{idx: 18, exp: le, fn: le_32767_int32},
	{idx: 18, exp: gt, fn: gt_32767_int32},
	{idx: 18, exp: ge, fn: ge_32767_int32},
	{idx: 18, exp: eq, fn: eq_32767_int32},
	{idx: 18, exp: ne, fn: ne_32767_int32},
	{idx: 19, exp: lt, fn: lt_32768_int32},
	{idx: 19, exp: le, fn: le_32768_int32},
	{idx: 19, exp: gt, fn: gt_32768_int32},
	{idx: 19, exp: ge, fn: ge_32768_int32},
	{idx: 19, exp: eq, fn: eq_32768_int32},
	{idx: 19, exp: ne, fn: ne_32768_int32},
	{idx: 20, exp: lt, fn: lt_65534_int32},
	{idx: 20, exp: le, fn: le_65534_int32},
	{idx: 20, exp: gt, fn: gt_65534_int32},
	{idx: 20, exp: ge, fn: ge_65534_int32},
	{idx: 20, exp: eq, fn: eq_65534_int32},
	{idx: 20, exp: ne, fn: ne_65534_int32},
	{idx: 21, exp: lt, fn: lt_65535_int32},
	{idx: 21, exp: le, fn: le_65535_int32},
	{idx: 21, exp: gt, fn: gt_65535_int32},
	{idx: 21, exp: ge, fn: ge_65535_int32},
	{idx: 21, exp: eq, fn: eq_65535_int32},
	{idx: 21, exp: ne, fn: ne_65535_int32},
	{idx: 22, exp: lt, fn: lt_65536_int32},
	{idx: 22, exp: le, fn: le_65536_int32},
	{idx: 22, exp: gt, fn: gt_65536_int32},
	{idx: 22, exp: ge, fn: ge_65536_int32},
	{idx: 22, exp: eq, fn: eq_65536_int32},
	{idx: 22, exp: ne, fn: ne_65536_int32},
	{idx: 23, exp: lt, fn: lt_2147483646_int32},
	{idx: 23, exp: le, fn: le_2147483646_int32},
	{idx: 23, exp: gt, fn: gt_2147483646_int32},
	{idx: 23, exp: ge, fn: ge_2147483646_int32},
	{idx: 23, exp: eq, fn: eq_2147483646_int32},
	{idx: 23, exp: ne, fn: ne_2147483646_int32},
	{idx: 24, exp: lt, fn: lt_2147483647_int32},
	{idx: 24, exp: le, fn: le_2147483647_int32},
	{idx: 24, exp: gt, fn: gt_2147483647_int32},
	{idx: 24, exp: ge, fn: ge_2147483647_int32},
	{idx: 24, exp: eq, fn: eq_2147483647_int32},
	{idx: 24, exp: ne, fn: ne_2147483647_int32},
}

// int16 tests
var int16_vals = []int16{
	-32768,
	-32767,
	-129,
	-128,
	-127,
	-1,
	0,
	1,
	126,
	127,
	128,
	254,
	255,
	256,
	32766,
	32767,
}

func lt_neg32768_int16(x int16) bool { return x < -32768 }
func le_neg32768_int16(x int16) bool { return x <= -32768 }
func gt_neg32768_int16(x int16) bool { return x > -32768 }
func ge_neg32768_int16(x int16) bool { return x >= -32768 }
func eq_neg32768_int16(x int16) bool { return x == -32768 }
func ne_neg32768_int16(x int16) bool { return x != -32768 }
func lt_neg32767_int16(x int16) bool { return x < -32767 }
func le_neg32767_int16(x int16) bool { return x <= -32767 }
func gt_neg32767_int16(x int16) bool { return x > -32767 }
func ge_neg32767_int16(x int16) bool { return x >= -32767 }
func eq_neg32767_int16(x int16) bool { return x == -32767 }
func ne_neg32767_int16(x int16) bool { return x != -32767 }
func lt_neg129_int16(x int16) bool   { return x < -129 }
func le_neg129_int16(x int16) bool   { return x <= -129 }
func gt_neg129_int16(x int16) bool   { return x > -129 }
func ge_neg129_int16(x int16) bool   { return x >= -129 }
func eq_neg129_int16(x int16) bool   { return x == -129 }
func ne_neg129_int16(x int16) bool   { return x != -129 }
func lt_neg128_int16(x int16) bool   { return x < -128 }
func le_neg128_int16(x int16) bool   { return x <= -128 }
func gt_neg128_int16(x int16) bool   { return x > -128 }
func ge_neg128_int16(x int16) bool   { return x >= -128 }
func eq_neg128_int16(x int16) bool   { return x == -128 }
func ne_neg128_int16(x int16) bool   { return x != -128 }
func lt_neg127_int16(x int16) bool   { return x < -127 }
func le_neg127_int16(x int16) bool   { return x <= -127 }
func gt_neg127_int16(x int16) bool   { return x > -127 }
func ge_neg127_int16(x int16) bool   { return x >= -127 }
func eq_neg127_int16(x int16) bool   { return x == -127 }
func ne_neg127_int16(x int16) bool   { return x != -127 }
func lt_neg1_int16(x int16) bool     { return x < -1 }
func le_neg1_int16(x int16) bool     { return x <= -1 }
func gt_neg1_int16(x int16) bool     { return x > -1 }
func ge_neg1_int16(x int16) bool     { return x >= -1 }
func eq_neg1_int16(x int16) bool     { return x == -1 }
func ne_neg1_int16(x int16) bool     { return x != -1 }
func lt_0_int16(x int16) bool        { return x < 0 }
func le_0_int16(x int16) bool        { return x <= 0 }
func gt_0_int16(x int16) bool        { return x > 0 }
func ge_0_int16(x int16) bool        { return x >= 0 }
func eq_0_int16(x int16) bool        { return x == 0 }
func ne_0_int16(x int16) bool        { return x != 0 }
func lt_1_int16(x int16) bool        { return x < 1 }
func le_1_int16(x int16) bool        { return x <= 1 }
func gt_1_int16(x int16) bool        { return x > 1 }
func ge_1_int16(x int16) bool        { return x >= 1 }
func eq_1_int16(x int16) bool        { return x == 1 }
func ne_1_int16(x int16) bool        { return x != 1 }
func lt_126_int16(x int16) bool      { return x < 126 }
func le_126_int16(x int16) bool      { return x <= 126 }
func gt_126_int16(x int16) bool      { return x > 126 }
func ge_126_int16(x int16) bool      { return x >= 126 }
func eq_126_int16(x int16) bool      { return x == 126 }
func ne_126_int16(x int16) bool      { return x != 126 }
func lt_127_int16(x int16) bool      { return x < 127 }
func le_127_int16(x int16) bool      { return x <= 127 }
func gt_127_int16(x int16) bool      { return x > 127 }
func ge_127_int16(x int16) bool      { return x >= 127 }
func eq_127_int16(x int16) bool      { return x == 127 }
func ne_127_int16(x int16) bool      { return x != 127 }
func lt_128_int16(x int16) bool      { return x < 128 }
func le_128_int16(x int16) bool      { return x <= 128 }
func gt_128_int16(x int16) bool      { return x > 128 }
func ge_128_int16(x int16) bool      { return x >= 128 }
func eq_128_int16(x int16) bool      { return x == 128 }
func ne_128_int16(x int16) bool      { return x != 128 }
func lt_254_int16(x int16) bool      { return x < 254 }
func le_254_int16(x int16) bool      { return x <= 254 }
func gt_254_int16(x int16) bool      { return x > 254 }
func ge_254_int16(x int16) bool      { return x >= 254 }
func eq_254_int16(x int16) bool      { return x == 254 }
func ne_254_int16(x int16) bool      { return x != 254 }
func lt_255_int16(x int16) bool      { return x < 255 }
func le_255_int16(x int16) bool      { return x <= 255 }
func gt_255_int16(x int16) bool      { return x > 255 }
func ge_255_int16(x int16) bool      { return x >= 255 }
func eq_255_int16(x int16) bool      { return x == 255 }
func ne_255_int16(x int16) bool      { return x != 255 }
func lt_256_int16(x int16) bool      { return x < 256 }
func le_256_int16(x int16) bool      { return x <= 256 }
func gt_256_int16(x int16) bool      { return x > 256 }
func ge_256_int16(x int16) bool      { return x >= 256 }
func eq_256_int16(x int16) bool      { return x == 256 }
func ne_256_int16(x int16) bool      { return x != 256 }
func lt_32766_int16(x int16) bool    { return x < 32766 }
func le_32766_int16(x int16) bool    { return x <= 32766 }
func gt_32766_int16(x int16) bool    { return x > 32766 }
func ge_32766_int16(x int16) bool    { return x >= 32766 }
func eq_32766_int16(x int16) bool    { return x == 32766 }
func ne_32766_int16(x int16) bool    { return x != 32766 }
func lt_32767_int16(x int16) bool    { return x < 32767 }
func le_32767_int16(x int16) bool    { return x <= 32767 }
func gt_32767_int16(x int16) bool    { return x > 32767 }
func ge_32767_int16(x int16) bool    { return x >= 32767 }
func eq_32767_int16(x int16) bool    { return x == 32767 }
func ne_32767_int16(x int16) bool    { return x != 32767 }

var int16_tests = []struct {
	idx int    // index of the constant used
	exp result // expected results
	fn  func(int16) bool
}{
	{idx: 0, exp: lt, fn: lt_neg32768_int16},
	{idx: 0, exp: le, fn: le_neg32768_int16},
	{idx: 0, exp: gt, fn: gt_neg32768_int16},
	{idx: 0, exp: ge, fn: ge_neg32768_int16},
	{idx: 0, exp: eq, fn: eq_neg32768_int16},
	{idx: 0, exp: ne, fn: ne_neg32768_int16},
	{idx: 1, exp: lt, fn: lt_neg32767_int16},
	{idx: 1, exp: le, fn: le_neg32767_int16},
	{idx: 1, exp: gt, fn: gt_neg32767_int16},
	{idx: 1, exp: ge, fn: ge_neg32767_int16},
	{idx: 1, exp: eq, fn: eq_neg32767_int16},
	{idx: 1, exp: ne, fn: ne_neg32767_int16},
	{idx: 2, exp: lt, fn: lt_neg129_int16},
	{idx: 2, exp: le, fn: le_neg129_int16},
	{idx: 2, exp: gt, fn: gt_neg129_int16},
	{idx: 2, exp: ge, fn: ge_neg129_int16},
	{idx: 2, exp: eq, fn: eq_neg129_int16},
	{idx: 2, exp: ne, fn: ne_neg129_int16},
	{idx: 3, exp: lt, fn: lt_neg128_int16},
	{idx: 3, exp: le, fn: le_neg128_int16},
	{idx: 3, exp: gt, fn: gt_neg128_int16},
	{idx: 3, exp: ge, fn: ge_neg128_int16},
	{idx: 3, exp: eq, fn: eq_neg128_int16},
	{idx: 3, exp: ne, fn: ne_neg128_int16},
	{idx: 4, exp: lt, fn: lt_neg127_int16},
	{idx: 4, exp: le, fn: le_neg127_int16},
	{idx: 4, exp: gt, fn: gt_neg127_int16},
	{idx: 4, exp: ge, fn: ge_neg127_int16},
	{idx: 4, exp: eq, fn: eq_neg127_int16},
	{idx: 4, exp: ne, fn: ne_neg127_int16},
	{idx: 5, exp: lt, fn: lt_neg1_int16},
	{idx: 5, exp: le, fn: le_neg1_int16},
	{idx: 5, exp: gt, fn: gt_neg1_int16},
	{idx: 5, exp: ge, fn: ge_neg1_int16},
	{idx: 5, exp: eq, fn: eq_neg1_int16},
	{idx: 5, exp: ne, fn: ne_neg1_int16},
	{idx: 6, exp: lt, fn: lt_0_int16},
	{idx: 6, exp: le, fn: le_0_int16},
	{idx: 6, exp: gt, fn: gt_0_int16},
	{idx: 6, exp: ge, fn: ge_0_int16},
	{idx: 6, exp: eq, fn: eq_0_int16},
	{idx: 6, exp: ne, fn: ne_0_int16},
	{idx: 7, exp: lt, fn: lt_1_int16},
	{idx: 7, exp: le, fn: le_1_int16},
	{idx: 7, exp: gt, fn: gt_1_int16},
	{idx: 7, exp: ge, fn: ge_1_int16},
	{idx: 7, exp: eq, fn: eq_1_int16},
	{idx: 7, exp: ne, fn: ne_1_int16},
	{idx: 8, exp: lt, fn: lt_126_int16},
	{idx: 8, exp: le, fn: le_126_int16},
	{idx: 8, exp: gt, fn: gt_126_int16},
	{idx: 8, exp: ge, fn: ge_126_int16},
	{idx: 8, exp: eq, fn: eq_126_int16},
	{idx: 8, exp: ne, fn: ne_126_int16},
	{idx: 9, exp: lt, fn: lt_127_int16},
	{idx: 9, exp: le, fn: le_127_int16},
	{idx: 9, exp: gt, fn: gt_127_int16},
	{idx: 9, exp: ge, fn: ge_127_int16},
	{idx: 9, exp: eq, fn: eq_127_int16},
	{idx: 9, exp: ne, fn: ne_127_int16},
	{idx: 10, exp: lt, fn: lt_128_int16},
	{idx: 10, exp: le, fn: le_128_int16},
	{idx: 10, exp: gt, fn: gt_128_int16},
	{idx: 10, exp: ge, fn: ge_128_int16},
	{idx: 10, exp: eq, fn: eq_128_int16},
	{idx: 10, exp: ne, fn: ne_128_int16},
	{idx: 11, exp: lt, fn: lt_254_int16},
	{idx: 11, exp: le, fn: le_254_int16},
	{idx: 11, exp: gt, fn: gt_254_int16},
	{idx: 11, exp: ge, fn: ge_254_int16},
	{idx: 11, exp: eq, fn: eq_254_int16},
	{idx: 11, exp: ne, fn: ne_254_int16},
	{idx: 12, exp: lt, fn: lt_255_int16},
	{idx: 12, exp: le, fn: le_255_int16},
	{idx: 12, exp: gt, fn: gt_255_int16},
	{idx: 12, exp: ge, fn: ge_255_int16},
	{idx: 12, exp: eq, fn: eq_255_int16},
	{idx: 12, exp: ne, fn: ne_255_int16},
	{idx: 13, exp: lt, fn: lt_256_int16},
	{idx: 13, exp: le, fn: le_256_int16},
	{idx: 13, exp: gt, fn: gt_256_int16},
	{idx: 13, exp: ge, fn: ge_256_int16},
	{idx: 13, exp: eq, fn: eq_256_int16},
	{idx: 13, exp: ne, fn: ne_256_int16},
	{idx: 14, exp: lt, fn: lt_32766_int16},
	{idx: 14, exp: le, fn: le_32766_int16},
	{idx: 14, exp: gt, fn: gt_32766_int16},
	{idx: 14, exp: ge, fn: ge_32766_int16},
	{idx: 14, exp: eq, fn: eq_32766_int16},
	{idx: 14, exp: ne, fn: ne_32766_int16},
	{idx: 15, exp: lt, fn: lt_32767_int16},
	{idx: 15, exp: le, fn: le_32767_int16},
	{idx: 15, exp: gt, fn: gt_32767_int16},
	{idx: 15, exp: ge, fn: ge_32767_int16},
	{idx: 15, exp: eq, fn: eq_32767_int16},
	{idx: 15, exp: ne, fn: ne_32767_int16},
}

// int8 tests
var int8_vals = []int8{
	-128,
	-127,
	-1,
	0,
	1,
	126,
	127,
}

func lt_neg128_int8(x int8) bool { return x < -128 }
func le_neg128_int8(x int8) bool { return x <= -128 }
func gt_neg128_int8(x int8) bool { return x > -128 }
func ge_neg128_int8(x int8) bool { return x >= -128 }
func eq_neg128_int8(x int8) bool { return x == -128 }
func ne_neg128_int8(x int8) bool { return x != -128 }
func lt_neg127_int8(x int8) bool { return x < -127 }
func le_neg127_int8(x int8) bool { return x <= -127 }
func gt_neg127_int8(x int8) bool { return x > -127 }
func ge_neg127_int8(x int8) bool { return x >= -127 }
func eq_neg127_int8(x int8) bool { return x == -127 }
func ne_neg127_int8(x int8) bool { return x != -127 }
func lt_neg1_int8(x int8) bool   { return x < -1 }
func le_neg1_int8(x int8) bool   { return x <= -1 }
func gt_neg1_int8(x int8) bool   { return x > -1 }
func ge_neg1_int8(x int8) bool   { return x >= -1 }
func eq_neg1_int8(x int8) bool   { return x == -1 }
func ne_neg1_int8(x int8) bool   { return x != -1 }
func lt_0_int8(x int8) bool      { return x < 0 }
func le_0_int8(x int8) bool      { return x <= 0 }
func gt_0_int8(x int8) bool      { return x > 0 }
func ge_0_int8(x int8) bool      { return x >= 0 }
func eq_0_int8(x int8) bool      { return x == 0 }
func ne_0_int8(x int8) bool      { return x != 0 }
func lt_1_int8(x int8) bool      { return x < 1 }
func le_1_int8(x int8) bool      { return x <= 1 }
func gt_1_int8(x int8) bool      { return x > 1 }
func ge_1_int8(x int8) bool      { return x >= 1 }
func eq_1_int8(x int8) bool      { return x == 1 }
func ne_1_int8(x int8) bool      { return x != 1 }
func lt_126_int8(x int8) bool    { return x < 126 }
func le_126_int8(x int8) bool    { return x <= 126 }
func gt_126_int8(x int8) bool    { return x > 126 }
func ge_126_int8(x int8) bool    { return x >= 126 }
func eq_126_int8(x int8) bool    { return x == 126 }
func ne_126_int8(x int8) bool    { return x != 126 }
func lt_127_int8(x int8) bool    { return x < 127 }
func le_127_int8(x int8) bool    { return x <= 127 }
func gt_127_int8(x int8) bool    { return x > 127 }
func ge_127_int8(x int8) bool    { return x >= 127 }
func eq_127_int8(x int8) bool    { return x == 127 }
func ne_127_int8(x int8) bool    { return x != 127 }

var int8_tests = []struct {
	idx int    // index of the constant used
	exp result // expected results
	fn  func(int8) bool
}{
	{idx: 0, exp: lt, fn: lt_neg128_int8},
	{idx: 0, exp: le, fn: le_neg128_int8},
	{idx: 0, exp: gt, fn: gt_neg128_int8},
	{idx: 0, exp: ge, fn: ge_neg128_int8},
	{idx: 0, exp: eq, fn: eq_neg128_int8},
	{idx: 0, exp: ne, fn: ne_neg128_int8},
	{idx: 1, exp: lt, fn: lt_neg127_int8},
	{idx: 1, exp: le, fn: le_neg127_int8},
	{idx: 1, exp: gt, fn: gt_neg127_int8},
	{idx: 1, exp: ge, fn: ge_neg127_int8},
	{idx: 1, exp: eq, fn: eq_neg127_int8},
	{idx: 1, exp: ne, fn: ne_neg127_int8},
	{idx: 2, exp: lt, fn: lt_neg1_int8},
	{idx: 2, exp: le, fn: le_neg1_int8},
	{idx: 2, exp: gt, fn: gt_neg1_int8},
	{idx: 2, exp: ge, fn: ge_neg1_int8},
	{idx: 2, exp: eq, fn: eq_neg1_int8},
	{idx: 2, exp: ne, fn: ne_neg1_int8},
	{idx: 3, exp: lt, fn: lt_0_int8},
	{idx: 3, exp: le, fn: le_0_int8},
	{idx: 3, exp: gt, fn: gt_0_int8},
	{idx: 3, exp: ge, fn: ge_0_int8},
	{idx: 3, exp: eq, fn: eq_0_int8},
	{idx: 3, exp: ne, fn: ne_0_int8},
	{idx: 4, exp: lt, fn: lt_1_int8},
	{idx: 4, exp: le, fn: le_1_int8},
	{idx: 4, exp: gt, fn: gt_1_int8},
	{idx: 4, exp: ge, fn: ge_1_int8},
	{idx: 4, exp: eq, fn: eq_1_int8},
	{idx: 4, exp: ne, fn: ne_1_int8},
	{idx: 5, exp: lt, fn: lt_126_int8},
	{idx: 5, exp: le, fn: le_126_int8},
	{idx: 5, exp: gt, fn: gt_126_int8},
	{idx: 5, exp: ge, fn: ge_126_int8},
	{idx: 5, exp: eq, fn: eq_126_int8},
	{idx: 5, exp: ne, fn: ne_126_int8},
	{idx: 6, exp: lt, fn: lt_127_int8},
	{idx: 6, exp: le, fn: le_127_int8},
	{idx: 6, exp: gt, fn: gt_127_int8},
	{idx: 6, exp: ge, fn: ge_127_int8},
	{idx: 6, exp: eq, fn: eq_127_int8},
	{idx: 6, exp: ne, fn: ne_127_int8},
}

// TestComparisonsConst tests results for comparison operations against constants.
func TestComparisonsConst(t *testing.T) {
	for i, test := range uint64_tests {
		for j, x := range uint64_vals {
			want := test.exp.l
			if j == test.idx {
				want = test.exp.e
			} else if j > test.idx {
				want = test.exp.r
			}
			if test.fn(x) != want {
				fn := runtime.FuncForPC(reflect.ValueOf(test.fn).Pointer()).Name()
				t.Errorf("test failed: %v(%v) != %v [type=uint64 i=%v j=%v idx=%v]", fn, x, want, i, j, test.idx)
			}
		}
	}
	for i, test := range uint32_tests {
		for j, x := range uint32_vals {
			want := test.exp.l
			if j == test.idx {
				want = test.exp.e
			} else if j > test.idx {
				want = test.exp.r
			}
			if test.fn(x) != want {
				fn := runtime.FuncForPC(reflect.ValueOf(test.fn).Pointer()).Name()
				t.Errorf("test failed: %v(%v) != %v [type=uint32 i=%v j=%v idx=%v]", fn, x, want, i, j, test.idx)
			}
		}
	}
	for i, test := range uint16_tests {
		for j, x := range uint16_vals {
			want := test.exp.l
			if j == test.idx {
				want = test.exp.e
			} else if j > test.idx {
				want = test.exp.r
			}
			if test.fn(x) != want {
				fn := runtime.FuncForPC(reflect.ValueOf(test.fn).Pointer()).Name()
				t.Errorf("test failed: %v(%v) != %v [type=uint16 i=%v j=%v idx=%v]", fn, x, want, i, j, test.idx)
			}
		}
	}
	for i, test := range uint8_tests {
		for j, x := range uint8_vals {
			want := test.exp.l
			if j == test.idx {
				want = test.exp.e
			} else if j > test.idx {
				want = test.exp.r
			}
			if test.fn(x) != want {
				fn := runtime.FuncForPC(reflect.ValueOf(test.fn).Pointer()).Name()
				t.Errorf("test failed: %v(%v) != %v [type=uint8 i=%v j=%v idx=%v]", fn, x, want, i, j, test.idx)
			}
		}
	}
	for i, test := range int64_tests {
		for j, x := range int64_vals {
			want := test.exp.l
			if j == test.idx {
				want = test.exp.e
			} else if j > test.idx {
				want = test.exp.r
			}
			if test.fn(x) != want {
				fn := runtime.FuncForPC(reflect.ValueOf(test.fn).Pointer()).Name()
				t.Errorf("test failed: %v(%v) != %v [type=int64 i=%v j=%v idx=%v]", fn, x, want, i, j, test.idx)
			}
		}
	}
	for i, test := range int32_tests {
		for j, x := range int32_vals {
			want := test.exp.l
			if j == test.idx {
				want = test.exp.e
			} else if j > test.idx {
				want = test.exp.r
			}
			if test.fn(x) != want {
				fn := runtime.FuncForPC(reflect.ValueOf(test.fn).Pointer()).Name()
				t.Errorf("test failed: %v(%v) != %v [type=int32 i=%v j=%v idx=%v]", fn, x, want, i, j, test.idx)
			}
		}
	}
	for i, test := range int16_tests {
		for j, x := range int16_vals {
			want := test.exp.l
			if j == test.idx {
				want = test.exp.e
			} else if j > test.idx {
				want = test.exp.r
			}
			if test.fn(x) != want {
				fn := runtime.FuncForPC(reflect.ValueOf(test.fn).Pointer()).Name()
				t.Errorf("test failed: %v(%v) != %v [type=int16 i=%v j=%v idx=%v]", fn, x, want, i, j, test.idx)
			}
		}
	}
	for i, test := range int8_tests {
		for j, x := range int8_vals {
			want := test.exp.l
			if j == test.idx {
				want = test.exp.e
			} else if j > test.idx {
				want = test.exp.r
			}
			if test.fn(x) != want {
				fn := runtime.FuncForPC(reflect.ValueOf(test.fn).Pointer()).Name()
				t.Errorf("test failed: %v(%v) != %v [type=int8 i=%v j=%v idx=%v]", fn, x, want, i, j, test.idx)
			}
		}
	}
}
