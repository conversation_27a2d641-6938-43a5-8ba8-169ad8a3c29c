# This test checks that the CC environment variable may contain quotes and
# spaces. Arguments are normally split on spaces, tabs, newlines. If an
# argument contains these characters, the entire argument may be quoted
# with single or double quotes. This is the same as -gcflags and similar
# options.

[short] skip
[!exec:clang] [!exec:gcc] skip
[!cgo] skip

env GOENV=$WORK/go.env
mkdir 'program files'
go build -o 'program files' './which cc/which cc.go'
[exec:clang] env CC='"'$PWD${/}program' 'files${/}which' 'cc"' 'clang
[!exec:clang] env CC='"'$PWD${/}program' 'files${/}which' 'cc"' 'gcc
go env CC
stdout 'program files[/\\]which cc" (clang|gcc)$'
go env -w CC=$CC
env CC=
go env CC
stdout 'program files[/\\]which cc" (clang|gcc)$'

go run .
stdout 1

-- go.mod --
module test

go 1.17
-- which cc/which cc.go --
package main

import (
	"fmt"
	"os"
	"os/exec"
)

func main() {
	args := append([]string{"-DWRAPPER_WAS_USED=1"}, os.Args[2:]...)
	cmd := exec.Command(os.Args[1], args...)
	cmd.Stdout = os.Stdout
	cmd.Stderr = os.Stderr
	if err := cmd.Run(); err != nil {
		fmt.Fprintln(os.Stderr, err)
		os.Exit(1)
	}
}
-- hello.go --
package main

// int x = WRAPPER_WAS_USED;
import "C"
import "fmt"

func main() {
	fmt.Println(C.x)
}
