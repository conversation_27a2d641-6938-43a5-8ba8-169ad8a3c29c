# Issue #42565

[!cgo] skip

# We can't build package bad, which uses #cgo LDFLAGS.
cd bad
! go build
stderr no-such-warning

# We can build package ok with the same flags in CGO_LDFLAGS.
env CGO_LDFLAGS=-Wno-such-warning -Wno-unknown-warning-option
cd ../ok
go build

# Build a main program that actually uses LDFLAGS.
cd ..
go build -ldflags=-v

# Because we passed -v the Go linker should print the external linker
# command which should include the flag we passed in CGO_LDFLAGS.
stderr no-such-warning

-- go.mod --
module ldflag

-- bad/bad.go --
package bad

// #cgo LDFLAGS: -Wno-such-warning -Wno-unknown-warning
import "C"

func F() {}
-- ok/ok.go --
package ok

import "C"

func F() {}
-- main.go --
package main

import _ "ldflag/ok"

func main() {}
