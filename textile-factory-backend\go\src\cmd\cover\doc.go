// Copyright 2013 The Go Authors. All rights reserved.
// Use of this source code is governed by a BSD-style
// license that can be found in the LICENSE file.

/*
Cover is a program for analyzing the coverage profiles generated by
'go test -coverprofile=cover.out'.

Cover is also used by 'go test -cover' to rewrite the source code with
annotations to track which parts of each function are executed (this
is referred to "instrumentation"). Cover can operate in "legacy mode"
on a single Go source file at a time, or when invoked by the Go tool
it will process all the source files in a single package at a time
(package-scope instrumentation is enabled via "-pkgcfg" option,

When generated instrumented code, the cover tool computes approximate
basic block information by studying the source. It is thus more
portable than binary-rewriting coverage tools, but also a little less
capable. For instance, it does not probe inside && and || expressions,
and can be mildly confused by single statements with multiple function
literals.

When computing coverage of a package that uses cgo, the cover tool
must be applied to the output of cgo preprocessing, not the input,
because cover deletes comments that are significant to cgo.

For usage information, please see:

	go help testflag
	go tool cover -help
*/
package main
