# 织厂机器异常管理系统后端API

## 项目结构
```
textile-factory-backend/
├── main.go              # 主入口文件
├── go.mod              # Go模块文件
├── config/
│   └── database.go     # 数据库配置
├── models/
│   └── models.go       # 数据模型
├── controllers/
│   ├── auth.go         # 用户认证控制器
│   ├── machine.go      # 机器管理控制器
│   ├── anomaly.go      # 异常管理控制器
│   ├── repair.go       # 维修管理控制器
│   └── qrcode.go       # 二维码扫描控制器
├── middleware/
│   └── auth.go         # 认证中间件
├── utils/
│   └── auth.go         # 认证工具
└── routes/
    └── routes.go       # 路由配置
```

## 环境要求
- Go 1.21+
- MySQL 5.7+

## 环境变量
```bash
DB_DSN=root:password@tcp(localhost:3306)/textile_factory?charset=utf8mb4&parseTime=True&loc=Local
```

## 安装依赖
```bash
go mod tidy
```

## 运行项目
```bash
go run main.go
```

## API接口文档

### 认证接口

#### 登录
- POST `/api/v1/login`
- Body: `{"username": "weaver01", "password": "123456"}`

#### 获取用户信息
- GET `/api/v1/user/info`
- Headers: `Authorization: Bearer <token>`

### 二维码扫描接口

#### 通用扫码
- GET `/api/v1/qr/scan/:qr_code`

#### 上报扫码
- GET `/api/v1/qr/scan/report/:qr_code`

#### 维修扫码
- GET `/api/v1/qr/scan/repair/:qr_code`

### 机器管理接口

#### 获取机器列表
- GET `/api/v1/machines?page=1&page_size=10&status=正常`

#### 根据编号获取机器
- GET `/api/v1/machines/code/:code`

#### 根据二维码获取机器
- GET `/api/v1/machines/qr/:qr_code`

#### 更新机器状态
- PUT `/api/v1/machines/:id/status`
- Body: `{"status": "正常"}`

### 异常管理接口

#### 创建异常报告
- POST `/api/v1/anomalies`
- Body: `{"machine_id": 1, "description": "异常描述", "severity": "高", "remark": "备注"}`

#### 获取异常列表
- GET `/api/v1/anomalies?page=1&page_size=10&status=待维修&machine_id=1`

#### 获取异常详情
- GET `/api/v1/anomalies/:id`

#### 更新异常状态
- PUT `/api/v1/anomalies/:id/status`
- Body: `{"status": "已完成"}`

### 维修管理接口（仅机修工）

#### 开始维修
- POST `/api/v1/repairs/start`
- Body: `{"anomaly_id": 1, "process": "维修过程", "parts": "更换部件"}`

#### 完成维修
- PUT `/api/v1/repairs/:id/complete`
- Body: `{"process": "维修过程", "parts": "更换部件", "duration": 120}`

#### 获取维修列表
- GET `/api/v1/repairs?page=1&page_size=10&status=进行中`

#### 获取维修详情
- GET `/api/v1/repairs/:id`

## 默认测试账号
- 织工: username: `weaver01`, password: `123456`
- 机修工: username: `mechanic01`, password: `123456`

## 数据库表结构

### 用户表 (users)
- id, username, password, role, name, created_at, updated_at

### 机器表 (machines)
- id, code, name, location, status, qr_code, created_at, updated_at

### 异常表 (anomalies)
- id, machine_id, user_id, description, severity, status, remark, created_at, updated_at

### 维修表 (repairs)
- id, anomaly_id, mechanic_id, start_time, end_time, process, parts, duration, status, created_at, updated_at