# Test go build -pgo=auto flag.

[short] skip 'compiles and links executables'

# use default.pgo for a single main package
go build -n -pgo=auto -o a1.exe ./a/a1
stderr 'compile.*-pgoprofile=.*default\.pgo.*a1.go'

# check that pgo applied to dependencies
stderr 'compile.*-p test/dep.*-pgoprofile=.*default\.pgo'

# check that pgo appears in build info
# N.B. we can't start the stdout check with -pgo because the script assumes that
# if the first arg starts with - it is a grep flag.
stderr 'build\\t-pgo=.*default\.pgo'

# use default.pgo for ... with a single main package
go build -n -pgo=auto ./a/...
stderr 'compile.*-pgoprofile=.*default\.pgo.*a1.go'

# check that pgo appears in build info
stderr 'build\\t-pgo=.*default\.pgo'

# build succeeds without PGO when default.pgo file is absent
go build -n -pgo=auto -o nopgo.exe ./nopgo
stderr 'compile.*nopgo.go'
! stderr 'compile.*-pgoprofile'

# check that pgo doesn't appear in build info
! stderr 'build\\t-pgo='

# other build-related commands
go install -a -n -pgo=auto ./a/a1
stderr 'compile.*-pgoprofile=.*default\.pgo.*a1.go'

go run -a -n -pgo=auto ./a/a1
stderr 'compile.*-pgoprofile=.*default\.pgo.*a1.go'

go test -a -n -pgo=auto ./a/a1
stderr 'compile.*-pgoprofile=.*default\.pgo.*a1.go.*a1_test.go'
stderr 'compile.*-pgoprofile=.*default\.pgo.*external_test.go'

# go list commands should succeed as usual
go list -pgo=auto ./a/a1

go list -test -pgo=auto ./a/a1

go list -deps -pgo=auto ./a/a1

# -pgo=auto is the default. Commands without explicit -pgo=auto
# should work as -pgo=auto.
go build -a -n -o a1.exe ./a/a1
stderr 'compile.*-pgoprofile=.*default\.pgo.*a1.go'
stderr 'compile.*-p test/dep.*-pgoprofile=.*default\.pgo'

# check that pgo appears in build info
stderr 'build\\t-pgo=.*default\.pgo'

go build -a -n -o nopgo.exe ./nopgo
stderr 'compile.*nopgo.go'
! stderr 'compile.*-pgoprofile'

# check that pgo doesn't appear in build info
! stderr 'build\\t-pgo='

# -pgo=off should turn off PGO.
go build -a -n -pgo=off -o a1.exe ./a/a1
stderr 'compile.*a1.go'
! stderr 'compile.*-pgoprofile'

# check that pgo doesn't appear in build info
! stderr 'build\\t-pgo='

-- go.mod --
module test
go 1.20
-- a/a1/a1.go --
package main
import _ "test/dep"
func main() {}
-- a/a1/a1_test.go --
package main
import "testing"
func TestA(*testing.T) {}
-- a/a1/external_test.go --
package main_test
import "testing"
func TestExternal(*testing.T) {}
-- a/a1/default.pgo --
-- nopgo/nopgo.go --
package main
func main() {}
-- dep/dep.go --
package dep
