// Code generated by avx512test. DO NOT EDIT.

#include "../../../../../../runtime/textflag.h"

TEXT asmtest_avx512_4fmaps(SB), NOSPLIT, $0
	V4FMADDPS 17(SP), [Z0-Z3], K2, Z0                  // 62f27f4a9a842411000000
	V4FMADDPS -17(BP)(SI*4), [Z0-Z3], K2, Z0           // 62f27f4a9a84b5efffffff
	V4FMADDPS 17(SP), [Z10-Z13], K2, Z0                // 62f22f4a9a842411000000
	V4FMADDPS -17(BP)(SI*4), [Z10-Z13], K2, Z0         // 62f22f4a9a84b5efffffff
	V4FMADDPS 17(SP), [Z20-Z23], K2, Z0                // 62f25f429a842411000000
	V4FMADDPS -17(BP)(SI*4), [Z20-Z23], K2, Z0         // 62f25f429a84b5efffffff
	V4FMADDPS 17(SP), [Z0-Z3], K2, Z8                  // 62727f4a9a842411000000
	V4FMADDPS -17(BP)(SI*4), [Z0-Z3], K2, Z8           // 62727f4a9a84b5efffffff
	V4FMADDPS 17(SP), [Z10-Z13], K2, Z8                // 62722f4a9a842411000000
	V4FMADDPS -17(BP)(SI*4), [Z10-Z13], K2, Z8         // 62722f4a9a84b5efffffff
	V4FMADDPS 17(SP), [Z20-Z23], K2, Z8                // 62725f429a842411000000
	V4FMADDPS -17(BP)(SI*4), [Z20-Z23], K2, Z8         // 62725f429a84b5efffffff
	V4FMADDSS 7(AX), [X0-X3], K5, X22                  // 62e27f0d9bb007000000 or 62e27f2d9bb007000000 or 62e27f4d9bb007000000
	V4FMADDSS (DI), [X0-X3], K5, X22                   // 62e27f0d9b37 or 62e27f2d9b37 or 62e27f4d9b37
	V4FMADDSS 7(AX), [X10-X13], K5, X22                // 62e22f0d9bb007000000 or 62e22f2d9bb007000000 or 62e22f4d9bb007000000
	V4FMADDSS (DI), [X10-X13], K5, X22                 // 62e22f0d9b37 or 62e22f2d9b37 or 62e22f4d9b37
	V4FMADDSS 7(AX), [X20-X23], K5, X22                // 62e25f059bb007000000 or 62e25f259bb007000000 or 62e25f459bb007000000
	V4FMADDSS (DI), [X20-X23], K5, X22                 // 62e25f059b37 or 62e25f259b37 or 62e25f459b37
	V4FMADDSS 7(AX), [X0-X3], K5, X30                  // 62627f0d9bb007000000 or 62627f2d9bb007000000 or 62627f4d9bb007000000
	V4FMADDSS (DI), [X0-X3], K5, X30                   // 62627f0d9b37 or 62627f2d9b37 or 62627f4d9b37
	V4FMADDSS 7(AX), [X10-X13], K5, X30                // 62622f0d9bb007000000 or 62622f2d9bb007000000 or 62622f4d9bb007000000
	V4FMADDSS (DI), [X10-X13], K5, X30                 // 62622f0d9b37 or 62622f2d9b37 or 62622f4d9b37
	V4FMADDSS 7(AX), [X20-X23], K5, X30                // 62625f059bb007000000 or 62625f259bb007000000 or 62625f459bb007000000
	V4FMADDSS (DI), [X20-X23], K5, X30                 // 62625f059b37 or 62625f259b37 or 62625f459b37
	V4FMADDSS 7(AX), [X0-X3], K5, X3                   // 62f27f0d9b9807000000 or 62f27f2d9b9807000000 or 62f27f4d9b9807000000
	V4FMADDSS (DI), [X0-X3], K5, X3                    // 62f27f0d9b1f or 62f27f2d9b1f or 62f27f4d9b1f
	V4FMADDSS 7(AX), [X10-X13], K5, X3                 // 62f22f0d9b9807000000 or 62f22f2d9b9807000000 or 62f22f4d9b9807000000
	V4FMADDSS (DI), [X10-X13], K5, X3                  // 62f22f0d9b1f or 62f22f2d9b1f or 62f22f4d9b1f
	V4FMADDSS 7(AX), [X20-X23], K5, X3                 // 62f25f059b9807000000 or 62f25f259b9807000000 or 62f25f459b9807000000
	V4FMADDSS (DI), [X20-X23], K5, X3                  // 62f25f059b1f or 62f25f259b1f or 62f25f459b1f
	V4FNMADDPS 99(R15)(R15*1), [Z1-Z4], K3, Z15        // 6212774baabc3f63000000
	V4FNMADDPS (DX), [Z1-Z4], K3, Z15                  // 6272774baa3a
	V4FNMADDPS 99(R15)(R15*1), [Z11-Z14], K3, Z15      // 6212274baabc3f63000000
	V4FNMADDPS (DX), [Z11-Z14], K3, Z15                // 6272274baa3a
	V4FNMADDPS 99(R15)(R15*1), [Z21-Z24], K3, Z15      // 62125743aabc3f63000000
	V4FNMADDPS (DX), [Z21-Z24], K3, Z15                // 62725743aa3a
	V4FNMADDPS 99(R15)(R15*1), [Z1-Z4], K3, Z12        // 6212774baaa43f63000000
	V4FNMADDPS (DX), [Z1-Z4], K3, Z12                  // 6272774baa22
	V4FNMADDPS 99(R15)(R15*1), [Z11-Z14], K3, Z12      // 6212274baaa43f63000000
	V4FNMADDPS (DX), [Z11-Z14], K3, Z12                // 6272274baa22
	V4FNMADDPS 99(R15)(R15*1), [Z21-Z24], K3, Z12      // 62125743aaa43f63000000
	V4FNMADDPS (DX), [Z21-Z24], K3, Z12                // 62725743aa22
	V4FNMADDSS -17(BP)(SI*8), [X1-X4], K4, X11         // 6272770cab9cf5efffffff or 6272772cab9cf5efffffff or 6272774cab9cf5efffffff
	V4FNMADDSS (R15), [X1-X4], K4, X11                 // 6252770cab1f or 6252772cab1f or 6252774cab1f
	V4FNMADDSS -17(BP)(SI*8), [X11-X14], K4, X11       // 6272270cab9cf5efffffff or 6272272cab9cf5efffffff or 6272274cab9cf5efffffff
	V4FNMADDSS (R15), [X11-X14], K4, X11               // 6252270cab1f or 6252272cab1f or 6252274cab1f
	V4FNMADDSS -17(BP)(SI*8), [X21-X24], K4, X11       // 62725704ab9cf5efffffff or 62725724ab9cf5efffffff or 62725744ab9cf5efffffff
	V4FNMADDSS (R15), [X21-X24], K4, X11               // 62525704ab1f or 62525724ab1f or 62525744ab1f
	V4FNMADDSS -17(BP)(SI*8), [X1-X4], K4, X15         // 6272770cabbcf5efffffff or 6272772cabbcf5efffffff or 6272774cabbcf5efffffff
	V4FNMADDSS (R15), [X1-X4], K4, X15                 // 6252770cab3f or 6252772cab3f or 6252774cab3f
	V4FNMADDSS -17(BP)(SI*8), [X11-X14], K4, X15       // 6272270cabbcf5efffffff or 6272272cabbcf5efffffff or 6272274cabbcf5efffffff
	V4FNMADDSS (R15), [X11-X14], K4, X15               // 6252270cab3f or 6252272cab3f or 6252274cab3f
	V4FNMADDSS -17(BP)(SI*8), [X21-X24], K4, X15       // 62725704abbcf5efffffff or 62725724abbcf5efffffff or 62725744abbcf5efffffff
	V4FNMADDSS (R15), [X21-X24], K4, X15               // 62525704ab3f or 62525724ab3f or 62525744ab3f
	V4FNMADDSS -17(BP)(SI*8), [X1-X4], K4, X30         // 6262770cabb4f5efffffff or 6262772cabb4f5efffffff or 6262774cabb4f5efffffff
	V4FNMADDSS (R15), [X1-X4], K4, X30                 // 6242770cab37 or 6242772cab37 or 6242774cab37
	V4FNMADDSS -17(BP)(SI*8), [X11-X14], K4, X30       // 6262270cabb4f5efffffff or 6262272cabb4f5efffffff or 6262274cabb4f5efffffff
	V4FNMADDSS (R15), [X11-X14], K4, X30               // 6242270cab37 or 6242272cab37 or 6242274cab37
	V4FNMADDSS -17(BP)(SI*8), [X21-X24], K4, X30       // 62625704abb4f5efffffff or 62625724abb4f5efffffff or 62625744abb4f5efffffff
	V4FNMADDSS (R15), [X21-X24], K4, X30               // 62425704ab37 or 62425724ab37 or 62425744ab37
	RET
