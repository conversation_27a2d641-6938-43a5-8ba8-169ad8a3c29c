# Per https://golang.org/ref/spec#Source_code_representation:
# a compiler may ignore a UTF-8-encoded byte order mark (U+FEFF)
# if it is the first Unicode code point in the source text.

go list -f 'Imports: {{.Imports}} EmbedFiles: {{.EmbedFiles}}' .
stdout '^Imports: \[embed m/hello\] EmbedFiles: \[.*file\]$'

-- go.mod --
module m

go 1.16
-- m.go --
﻿package main

import (
	_ "embed"

	"m/hello"
)

//go:embed file
var s string

-- hello/hello.go --
package hello

-- file --
