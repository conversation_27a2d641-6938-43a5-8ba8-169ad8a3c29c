env GO111MODULE=off
[short] skip

# This test matches mod_list_bad_import, but in GOPATH mode.
# Please keep them in sync.

env GO111MODULE=off
cd example.com

# Without -e, listing an otherwise-valid package with an unsatisfied direct import should fail.
# BUG: Today it succeeds.
go list -f '{{if .Error}}error{{end}} {{if .Incomplete}}incomplete{{end}} {{range .DepsErrors}}bad dep: {{.Err}}{{end}}' example.com/direct
! stdout ^error
stdout 'incomplete'
stdout 'bad dep: .*example.com[/\\]notfound'

# Listing with -deps should also fail.
! go list -deps example.com/direct
stderr example.com[/\\]notfound

# But -e -deps should succeed.
go list -e -deps example.com/direct
stdout example.com/notfound


# Listing an otherwise-valid package that imports some *other* package with an
# unsatisfied import should also fail.
# BUG: Today, it succeeds.
go list -f '{{if .Error}}error{{end}} {{if .Incomplete}}incomplete{{end}} {{range .DepsErrors}}bad dep: {{.Err}}{{end}}' example.com/indirect
! stdout ^error
stdout incomplete
stdout 'bad dep: .*example.com[/\\]notfound'

# Again, -deps should fail.
! go list -deps example.com/indirect
stderr example.com[/\\]notfound

# But -deps -e should succeed.
go list -e -deps example.com/indirect
stdout example.com/notfound


# Listing the missing dependency directly should fail outright...
! go list -f '{{if .Error}}error{{end}} {{if .Incomplete}}incomplete{{end}}' example.com/notfound
stderr 'no Go files in .*example.com[/\\]notfound'
! stdout error
! stdout incomplete

# ...but listing with -e should succeed.
go list -e -f '{{if .Error}}error{{end}} {{if .Incomplete}}incomplete{{end}}' example.com/notfound
stdout error
stdout incomplete


# The pattern "all" should match only packages that actually exist,
# ignoring those whose existence is merely implied by imports.
go list -e -f '{{.ImportPath}}' all
stdout example.com/direct
stdout example.com/indirect
! stdout example.com/notfound


-- example.com/direct/direct.go --
package direct
import _ "example.com/notfound"

-- example.com/indirect/indirect.go --
package indirect
import _ "example.com/direct"

-- example.com/notfound/README --
This directory intentionally left blank.
