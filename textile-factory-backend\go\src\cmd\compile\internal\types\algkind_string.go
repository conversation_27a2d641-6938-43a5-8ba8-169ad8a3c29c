// Code generated by "stringer -type AlgKind -trimprefix A alg.go"; DO NOT EDIT.

package types

import "strconv"

func _() {
	// An "invalid array index" compiler error signifies that the constant values have changed.
	// Re-run the stringer command to generate them again.
	var x [1]struct{}
	_ = x[ANOEQ-0]
	_ = x[AMEM0-1]
	_ = x[AMEM8-2]
	_ = x[AMEM16-3]
	_ = x[AMEM32-4]
	_ = x[AMEM64-5]
	_ = x[AMEM128-6]
	_ = x[ASTRING-7]
	_ = x[AINTER-8]
	_ = x[ANILINTER-9]
	_ = x[AFLOAT32-10]
	_ = x[AFLOAT64-11]
	_ = x[ACPLX64-12]
	_ = x[ACPLX128-13]
	_ = x[AMEM-100]
	_ = x[ASPECIAL - -1]
}

const (
	_AlgKind_name_0 = "SPECIALNOEQMEM0MEM8MEM16MEM32MEM64MEM128STRINGINTERNILINTERFLOAT32FLOAT64CPLX64CPLX128"
	_AlgKind_name_1 = "MEM"
)

var (
	_AlgKind_index_0 = [...]uint8{0, 7, 11, 15, 19, 24, 29, 34, 40, 46, 51, 59, 66, 73, 79, 86}
)

func (i AlgKind) String() string {
	switch {
	case -1 <= i && i <= 13:
		i -= -1
		return _AlgKind_name_0[_AlgKind_index_0[i]:_AlgKind_index_0[i+1]]
	case i == 100:
		return _AlgKind_name_1
	default:
		return "AlgKind(" + strconv.FormatInt(int64(i), 10) + ")"
	}
}
