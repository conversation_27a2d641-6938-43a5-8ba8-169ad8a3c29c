// Copyright 2017 The Go Authors. All rights reserved.
// Use of this source code is governed by a BSD-style
// license that can be found in the LICENSE file.

package p

var (
	i0 uint8
	b0 byte

	i1 *uint8
	b1 *byte

	i2 **uint8
	b2 **byte

	i3 ***uint8
	b3 ***byte

	i4 ****uint8
	b4 ****byte

	i5 *****uint8
	b5 *****byte

	i6 ******uint8
	b6 ******byte

	i7 *******uint8
	b7 *******byte

	i8 ********uint8
	b8 ********byte
)
