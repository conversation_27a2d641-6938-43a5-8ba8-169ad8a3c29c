# golang.org/issue/14176#issuecomment-179895769
# golang.org/issue/14192
# -I arguments to compiler could end up not in GOPATH order,
# leading to unexpected import resolution in the compiler.

env GO111MODULE=off
env GOPATH=$WORK/p1${:}$WORK/p2
mkdir $WORK/p1/src/foo $WORK/p2/src/baz
mkdir $WORK/p2/pkg/${GOOS}_${GOARCH} $WORK/p1/src/bar
cp foo.go $WORK/p1/src/foo/foo.go
cp baz.go $WORK/p2/src/baz/baz.go
cp foo.a $WORK/p2/pkg/${GOOS}_${GOARCH}/foo.a
cp bar.go $WORK/p1/src/bar/bar.go

go install -x bar

# add in baz.a to the mix
mkdir $WORK/p1/pkg/${GOOS}_${GOARCH}
cp baz.a $WORK/p1/pkg/${GOOS}_${GOARCH}/baz.a
env GOPATH=$WORK/p1${:}$WORK/p2
go install -x bar
env GOPATH=$WORK/p2${:}$WORK/p1
go install -x bar

-- foo.go --
package foo
-- baz.go --
package baz
-- foo.a --
bad
-- baz.a --
bad
-- bar.go --
package bar
import _ "baz"
import _ "foo"
