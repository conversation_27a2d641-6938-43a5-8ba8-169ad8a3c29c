// Code generated by avx512test. DO NOT EDIT.

#include "../../../../../../runtime/textflag.h"

TEXT asmtest_avx512_4vnniw(SB), NOSPLIT, $0
	VP4DPWSSD 7(SI)(DI*1), [Z2-Z5], K4, Z17            // 62e26f4c528c3e07000000
	VP4DPWSSD 15(DX)(BX*8), [Z2-Z5], K4, Z17           // 62e26f4c528cda0f000000
	VP4DPWSSD 7(SI)(DI*1), [Z12-Z15], K4, Z17          // 62e21f4c528c3e07000000
	VP4DPWSSD 15(DX)(BX*8), [Z12-Z15], K4, Z17         // 62e21f4c528cda0f000000
	VP4DPWSSD 7(SI)(DI*1), [Z22-Z25], K4, Z17          // 62e24f44528c3e07000000
	VP4DPWSSD 15(DX)(BX*8), [Z22-Z25], K4, Z17         // 62e24f44528cda0f000000
	VP4DPWSSD 7(SI)(DI*1), [Z2-Z5], K4, Z23            // 62e26f4c52bc3e07000000
	VP4DPWSSD 15(DX)(BX*8), [Z2-Z5], K4, Z23           // 62e26f4c52bcda0f000000
	VP4DPWSSD 7(SI)(DI*1), [Z12-Z15], K4, Z23          // 62e21f4c52bc3e07000000
	VP4DPWSSD 15(DX)(BX*8), [Z12-Z15], K4, Z23         // 62e21f4c52bcda0f000000
	VP4DPWSSD 7(SI)(DI*1), [Z22-Z25], K4, Z23          // 62e24f4452bc3e07000000
	VP4DPWSSD 15(DX)(BX*8), [Z22-Z25], K4, Z23         // 62e24f4452bcda0f000000
	VP4DPWSSDS -7(DI)(R8*1), [Z4-Z7], K1, Z31          // 62225f4953bc07f9ffffff
	VP4DPWSSDS (SP), [Z4-Z7], K1, Z31                  // 62625f49533c24
	VP4DPWSSDS -7(DI)(R8*1), [Z14-Z17], K1, Z31        // 62220f4953bc07f9ffffff
	VP4DPWSSDS (SP), [Z14-Z17], K1, Z31                // 62620f49533c24
	VP4DPWSSDS -7(DI)(R8*1), [Z24-Z27], K1, Z31        // 62223f4153bc07f9ffffff
	VP4DPWSSDS (SP), [Z24-Z27], K1, Z31                // 62623f41533c24
	VP4DPWSSDS -7(DI)(R8*1), [Z4-Z7], K1, Z0           // 62b25f49538407f9ffffff
	VP4DPWSSDS (SP), [Z4-Z7], K1, Z0                   // 62f25f49530424
	VP4DPWSSDS -7(DI)(R8*1), [Z14-Z17], K1, Z0         // 62b20f49538407f9ffffff
	VP4DPWSSDS (SP), [Z14-Z17], K1, Z0                 // 62f20f49530424
	VP4DPWSSDS -7(DI)(R8*1), [Z24-Z27], K1, Z0         // 62b23f41538407f9ffffff
	VP4DPWSSDS (SP), [Z24-Z27], K1, Z0                 // 62f23f41530424
	RET
