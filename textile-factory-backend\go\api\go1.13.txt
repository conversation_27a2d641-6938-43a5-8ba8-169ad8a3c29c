pkg bytes, func ToValidUTF8([]uint8, []uint8) []uint8
pkg crypto/ed25519, const PrivateKeySize = 64
pkg crypto/ed25519, const PrivateKeySize ideal-int
pkg crypto/ed25519, const PublicKeySize = 32
pkg crypto/ed25519, const PublicKeySize ideal-int
pkg crypto/ed25519, const SeedSize = 32
pkg crypto/ed25519, const SeedSize ideal-int
pkg crypto/ed25519, const SignatureSize = 64
pkg crypto/ed25519, const SignatureSize ideal-int
pkg crypto/ed25519, func GenerateKey(io.Reader) (PublicKey, PrivateKey, error)
pkg crypto/ed25519, func NewKeyFromSeed([]uint8) PrivateKey
pkg crypto/ed25519, func Sign(PrivateKey, []uint8) []uint8
pkg crypto/ed25519, func Verify(PublicKey, []uint8, []uint8) bool
pkg crypto/ed25519, method (PrivateKey) Public() crypto.PublicKey
pkg crypto/ed25519, method (PrivateKey) Seed() []uint8
pkg crypto/ed25519, method (PrivateKey) Sign(io.Reader, []uint8, crypto.SignerOpts) ([]uint8, error)
pkg crypto/ed25519, type PrivateKey []uint8
pkg crypto/ed25519, type PublicKey []uint8
pkg crypto/tls, const Ed25519 = 2055
pkg crypto/tls, const Ed25519 SignatureScheme
pkg crypto/x509, const Ed25519 = 4
pkg crypto/x509, const Ed25519 PublicKeyAlgorithm
pkg crypto/x509, const PureEd25519 = 16
pkg crypto/x509, const PureEd25519 SignatureAlgorithm
pkg database/sql, method (*Conn) Raw(func(interface{}) error) error
pkg database/sql, method (*NullInt32) Scan(interface{}) error
pkg database/sql, method (NullInt32) Value() (driver.Value, error)
pkg database/sql, method (*NullTime) Scan(interface{}) error
pkg database/sql, method (NullTime) Value() (driver.Value, error)
pkg database/sql, type NullInt32 struct
pkg database/sql, type NullInt32 struct, Int32 int32
pkg database/sql, type NullInt32 struct, Valid bool
pkg database/sql, type NullTime struct
pkg database/sql, type NullTime struct, Time time.Time
pkg database/sql, type NullTime struct, Valid bool
pkg debug/dwarf, method (*UnsupportedType) Common() *CommonType
pkg debug/dwarf, method (*UnsupportedType) Size() int64
pkg debug/dwarf, method (*UnsupportedType) String() string
pkg debug/dwarf, type UnsupportedType struct
pkg debug/dwarf, type UnsupportedType struct, embedded CommonType
pkg debug/dwarf, type UnsupportedType struct, Tag Tag
pkg debug/elf, type Symbol struct, Library string
pkg debug/elf, type Symbol struct, Version string
pkg encoding/csv, method (*ParseError) Unwrap() error
pkg encoding/json, method (*MarshalerError) Unwrap() error
pkg errors, func As(error, interface{}) bool
pkg errors, func Is(error, error) bool
pkg errors, func Unwrap(error) error
pkg go/constant, func Make(interface{}) Value
pkg go/constant, func Val(Value) interface{}
pkg go/token, func IsExported(string) bool
pkg go/token, func IsIdentifier(string) bool
pkg go/token, func IsKeyword(string) bool
pkg go/types, func CheckExpr(*token.FileSet, *Package, token.Pos, ast.Expr, *Info) error
pkg log, func Writer() io.Writer
pkg log/syslog (netbsd-arm64-cgo), const LOG_ALERT = 1
pkg log/syslog (netbsd-arm64-cgo), const LOG_ALERT Priority
pkg log/syslog (netbsd-arm64-cgo), const LOG_AUTH = 32
pkg log/syslog (netbsd-arm64-cgo), const LOG_AUTH Priority
pkg log/syslog (netbsd-arm64-cgo), const LOG_AUTHPRIV = 80
pkg log/syslog (netbsd-arm64-cgo), const LOG_AUTHPRIV Priority
pkg log/syslog (netbsd-arm64-cgo), const LOG_CRIT = 2
pkg log/syslog (netbsd-arm64-cgo), const LOG_CRIT Priority
pkg log/syslog (netbsd-arm64-cgo), const LOG_CRON = 72
pkg log/syslog (netbsd-arm64-cgo), const LOG_CRON Priority
pkg log/syslog (netbsd-arm64-cgo), const LOG_DAEMON = 24
pkg log/syslog (netbsd-arm64-cgo), const LOG_DAEMON Priority
pkg log/syslog (netbsd-arm64-cgo), const LOG_DEBUG = 7
pkg log/syslog (netbsd-arm64-cgo), const LOG_DEBUG Priority
pkg log/syslog (netbsd-arm64-cgo), const LOG_EMERG = 0
pkg log/syslog (netbsd-arm64-cgo), const LOG_EMERG Priority
pkg log/syslog (netbsd-arm64-cgo), const LOG_ERR = 3
pkg log/syslog (netbsd-arm64-cgo), const LOG_ERR Priority
pkg log/syslog (netbsd-arm64-cgo), const LOG_FTP = 88
pkg log/syslog (netbsd-arm64-cgo), const LOG_FTP Priority
pkg log/syslog (netbsd-arm64-cgo), const LOG_INFO = 6
pkg log/syslog (netbsd-arm64-cgo), const LOG_INFO Priority
pkg log/syslog (netbsd-arm64-cgo), const LOG_KERN = 0
pkg log/syslog (netbsd-arm64-cgo), const LOG_KERN Priority
pkg log/syslog (netbsd-arm64-cgo), const LOG_LOCAL0 = 128
pkg log/syslog (netbsd-arm64-cgo), const LOG_LOCAL0 Priority
pkg log/syslog (netbsd-arm64-cgo), const LOG_LOCAL1 = 136
pkg log/syslog (netbsd-arm64-cgo), const LOG_LOCAL1 Priority
pkg log/syslog (netbsd-arm64-cgo), const LOG_LOCAL2 = 144
pkg log/syslog (netbsd-arm64-cgo), const LOG_LOCAL2 Priority
pkg log/syslog (netbsd-arm64-cgo), const LOG_LOCAL3 = 152
pkg log/syslog (netbsd-arm64-cgo), const LOG_LOCAL3 Priority
pkg log/syslog (netbsd-arm64-cgo), const LOG_LOCAL4 = 160
pkg log/syslog (netbsd-arm64-cgo), const LOG_LOCAL4 Priority
pkg log/syslog (netbsd-arm64-cgo), const LOG_LOCAL5 = 168
pkg log/syslog (netbsd-arm64-cgo), const LOG_LOCAL5 Priority
pkg log/syslog (netbsd-arm64-cgo), const LOG_LOCAL6 = 176
pkg log/syslog (netbsd-arm64-cgo), const LOG_LOCAL6 Priority
pkg log/syslog (netbsd-arm64-cgo), const LOG_LOCAL7 = 184
pkg log/syslog (netbsd-arm64-cgo), const LOG_LOCAL7 Priority
pkg log/syslog (netbsd-arm64-cgo), const LOG_LPR = 48
pkg log/syslog (netbsd-arm64-cgo), const LOG_LPR Priority
pkg log/syslog (netbsd-arm64-cgo), const LOG_MAIL = 16
pkg log/syslog (netbsd-arm64-cgo), const LOG_MAIL Priority
pkg log/syslog (netbsd-arm64-cgo), const LOG_NEWS = 56
pkg log/syslog (netbsd-arm64-cgo), const LOG_NEWS Priority
pkg log/syslog (netbsd-arm64-cgo), const LOG_NOTICE = 5
pkg log/syslog (netbsd-arm64-cgo), const LOG_NOTICE Priority
pkg log/syslog (netbsd-arm64-cgo), const LOG_SYSLOG = 40
pkg log/syslog (netbsd-arm64-cgo), const LOG_SYSLOG Priority
pkg log/syslog (netbsd-arm64-cgo), const LOG_USER = 8
pkg log/syslog (netbsd-arm64-cgo), const LOG_USER Priority
pkg log/syslog (netbsd-arm64-cgo), const LOG_UUCP = 64
pkg log/syslog (netbsd-arm64-cgo), const LOG_UUCP Priority
pkg log/syslog (netbsd-arm64-cgo), const LOG_WARNING = 4
pkg log/syslog (netbsd-arm64-cgo), const LOG_WARNING Priority
pkg log/syslog (netbsd-arm64-cgo), func Dial(string, string, Priority, string) (*Writer, error)
pkg log/syslog (netbsd-arm64-cgo), func NewLogger(Priority, int) (*log.Logger, error)
pkg log/syslog (netbsd-arm64-cgo), func New(Priority, string) (*Writer, error)
pkg log/syslog (netbsd-arm64-cgo), method (*Writer) Alert(string) error
pkg log/syslog (netbsd-arm64-cgo), method (*Writer) Close() error
pkg log/syslog (netbsd-arm64-cgo), method (*Writer) Crit(string) error
pkg log/syslog (netbsd-arm64-cgo), method (*Writer) Debug(string) error
pkg log/syslog (netbsd-arm64-cgo), method (*Writer) Emerg(string) error
pkg log/syslog (netbsd-arm64-cgo), method (*Writer) Err(string) error
pkg log/syslog (netbsd-arm64-cgo), method (*Writer) Info(string) error
pkg log/syslog (netbsd-arm64-cgo), method (*Writer) Notice(string) error
pkg log/syslog (netbsd-arm64-cgo), method (*Writer) Warning(string) error
pkg log/syslog (netbsd-arm64-cgo), method (*Writer) Write([]uint8) (int, error)
pkg log/syslog (netbsd-arm64-cgo), type Priority int
pkg log/syslog (netbsd-arm64-cgo), type Writer struct
pkg log/syslog (netbsd-arm64), const LOG_ALERT = 1
pkg log/syslog (netbsd-arm64), const LOG_ALERT Priority
pkg log/syslog (netbsd-arm64), const LOG_AUTH = 32
pkg log/syslog (netbsd-arm64), const LOG_AUTH Priority
pkg log/syslog (netbsd-arm64), const LOG_AUTHPRIV = 80
pkg log/syslog (netbsd-arm64), const LOG_AUTHPRIV Priority
pkg log/syslog (netbsd-arm64), const LOG_CRIT = 2
pkg log/syslog (netbsd-arm64), const LOG_CRIT Priority
pkg log/syslog (netbsd-arm64), const LOG_CRON = 72
pkg log/syslog (netbsd-arm64), const LOG_CRON Priority
pkg log/syslog (netbsd-arm64), const LOG_DAEMON = 24
pkg log/syslog (netbsd-arm64), const LOG_DAEMON Priority
pkg log/syslog (netbsd-arm64), const LOG_DEBUG = 7
pkg log/syslog (netbsd-arm64), const LOG_DEBUG Priority
pkg log/syslog (netbsd-arm64), const LOG_EMERG = 0
pkg log/syslog (netbsd-arm64), const LOG_EMERG Priority
pkg log/syslog (netbsd-arm64), const LOG_ERR = 3
pkg log/syslog (netbsd-arm64), const LOG_ERR Priority
pkg log/syslog (netbsd-arm64), const LOG_FTP = 88
pkg log/syslog (netbsd-arm64), const LOG_FTP Priority
pkg log/syslog (netbsd-arm64), const LOG_INFO = 6
pkg log/syslog (netbsd-arm64), const LOG_INFO Priority
pkg log/syslog (netbsd-arm64), const LOG_KERN = 0
pkg log/syslog (netbsd-arm64), const LOG_KERN Priority
pkg log/syslog (netbsd-arm64), const LOG_LOCAL0 = 128
pkg log/syslog (netbsd-arm64), const LOG_LOCAL0 Priority
pkg log/syslog (netbsd-arm64), const LOG_LOCAL1 = 136
pkg log/syslog (netbsd-arm64), const LOG_LOCAL1 Priority
pkg log/syslog (netbsd-arm64), const LOG_LOCAL2 = 144
pkg log/syslog (netbsd-arm64), const LOG_LOCAL2 Priority
pkg log/syslog (netbsd-arm64), const LOG_LOCAL3 = 152
pkg log/syslog (netbsd-arm64), const LOG_LOCAL3 Priority
pkg log/syslog (netbsd-arm64), const LOG_LOCAL4 = 160
pkg log/syslog (netbsd-arm64), const LOG_LOCAL4 Priority
pkg log/syslog (netbsd-arm64), const LOG_LOCAL5 = 168
pkg log/syslog (netbsd-arm64), const LOG_LOCAL5 Priority
pkg log/syslog (netbsd-arm64), const LOG_LOCAL6 = 176
pkg log/syslog (netbsd-arm64), const LOG_LOCAL6 Priority
pkg log/syslog (netbsd-arm64), const LOG_LOCAL7 = 184
pkg log/syslog (netbsd-arm64), const LOG_LOCAL7 Priority
pkg log/syslog (netbsd-arm64), const LOG_LPR = 48
pkg log/syslog (netbsd-arm64), const LOG_LPR Priority
pkg log/syslog (netbsd-arm64), const LOG_MAIL = 16
pkg log/syslog (netbsd-arm64), const LOG_MAIL Priority
pkg log/syslog (netbsd-arm64), const LOG_NEWS = 56
pkg log/syslog (netbsd-arm64), const LOG_NEWS Priority
pkg log/syslog (netbsd-arm64), const LOG_NOTICE = 5
pkg log/syslog (netbsd-arm64), const LOG_NOTICE Priority
pkg log/syslog (netbsd-arm64), const LOG_SYSLOG = 40
pkg log/syslog (netbsd-arm64), const LOG_SYSLOG Priority
pkg log/syslog (netbsd-arm64), const LOG_USER = 8
pkg log/syslog (netbsd-arm64), const LOG_USER Priority
pkg log/syslog (netbsd-arm64), const LOG_UUCP = 64
pkg log/syslog (netbsd-arm64), const LOG_UUCP Priority
pkg log/syslog (netbsd-arm64), const LOG_WARNING = 4
pkg log/syslog (netbsd-arm64), const LOG_WARNING Priority
pkg log/syslog (netbsd-arm64), func Dial(string, string, Priority, string) (*Writer, error)
pkg log/syslog (netbsd-arm64), func NewLogger(Priority, int) (*log.Logger, error)
pkg log/syslog (netbsd-arm64), func New(Priority, string) (*Writer, error)
pkg log/syslog (netbsd-arm64), method (*Writer) Alert(string) error
pkg log/syslog (netbsd-arm64), method (*Writer) Close() error
pkg log/syslog (netbsd-arm64), method (*Writer) Crit(string) error
pkg log/syslog (netbsd-arm64), method (*Writer) Debug(string) error
pkg log/syslog (netbsd-arm64), method (*Writer) Emerg(string) error
pkg log/syslog (netbsd-arm64), method (*Writer) Err(string) error
pkg log/syslog (netbsd-arm64), method (*Writer) Info(string) error
pkg log/syslog (netbsd-arm64), method (*Writer) Notice(string) error
pkg log/syslog (netbsd-arm64), method (*Writer) Warning(string) error
pkg log/syslog (netbsd-arm64), method (*Writer) Write([]uint8) (int, error)
pkg log/syslog (netbsd-arm64), type Priority int
pkg log/syslog (netbsd-arm64), type Writer struct
pkg math/big, method (*Int) TrailingZeroBits() uint
pkg math/big, method (*Rat) SetUint64(uint64) *Rat
pkg net/http, const SameSiteNoneMode = 4
pkg net/http, const SameSiteNoneMode SameSite
pkg net/http, const StatusEarlyHints = 103
pkg net/http, const StatusEarlyHints ideal-int
pkg net/http, func NewRequestWithContext(context.Context, string, string, io.Reader) (*Request, error)
pkg net/http, method (Header) Clone() Header
pkg net/http, method (*Request) Clone(context.Context) *Request
pkg net/http, method (*Transport) Clone() *Transport
pkg net/http, type Server struct, BaseContext func(net.Listener) context.Context
pkg net/http, type Server struct, ConnContext func(context.Context, net.Conn) context.Context
pkg net/http, type Transport struct, ForceAttemptHTTP2 bool
pkg net/http, type Transport struct, ReadBufferSize int
pkg net/http, type Transport struct, WriteBufferSize int
pkg net, method (*DNSConfigError) Unwrap() error
pkg net, method (*OpError) Unwrap() error
pkg net, type DNSError struct, IsNotFound bool
pkg net, type ListenConfig struct, KeepAlive time.Duration
pkg net/url, method (*Error) Unwrap() error
pkg os/exec, method (*Cmd) String() string
pkg os/exec, method (*Error) Unwrap() error
pkg os, func UserConfigDir() (string, error)
pkg os, method (*LinkError) Unwrap() error
pkg os, method (*PathError) Unwrap() error
pkg os, method (*SyscallError) Unwrap() error
pkg os (netbsd-arm64-cgo), const DevNull = "/dev/null"
pkg os (netbsd-arm64-cgo), const O_APPEND = 8
pkg os (netbsd-arm64-cgo), const O_CREATE = 512
pkg os (netbsd-arm64-cgo), const O_EXCL = 2048
pkg os (netbsd-arm64-cgo), const O_SYNC = 128
pkg os (netbsd-arm64-cgo), const O_TRUNC = 1024
pkg os (netbsd-arm64-cgo), const PathListSeparator = 58
pkg os (netbsd-arm64-cgo), const PathSeparator = 47
pkg os (netbsd-arm64), const DevNull = "/dev/null"
pkg os (netbsd-arm64), const O_APPEND = 8
pkg os (netbsd-arm64), const O_CREATE = 512
pkg os (netbsd-arm64), const O_EXCL = 2048
pkg os (netbsd-arm64), const O_SYNC = 128
pkg os (netbsd-arm64), const O_TRUNC = 1024
pkg os (netbsd-arm64), const PathListSeparator = 58
pkg os (netbsd-arm64), const PathSeparator = 47
pkg path/filepath (netbsd-arm64-cgo), const ListSeparator = 58
pkg path/filepath (netbsd-arm64-cgo), const Separator = 47
pkg path/filepath (netbsd-arm64), const ListSeparator = 58
pkg path/filepath (netbsd-arm64), const Separator = 47
pkg reflect, method (Value) IsZero() bool
pkg runtime (netbsd-arm64-cgo), const GOARCH = "arm64"
pkg runtime (netbsd-arm64-cgo), const GOOS = "netbsd"
pkg runtime (netbsd-arm64), const GOARCH = "arm64"
pkg runtime (netbsd-arm64), const GOOS = "netbsd"
pkg strings, func ToValidUTF8(string, string) string
pkg syscall, method (Errno) Is(error) bool
pkg syscall (netbsd-arm64-cgo), const AF_APPLETALK = 16
pkg syscall (netbsd-arm64-cgo), const AF_APPLETALK ideal-int
pkg syscall (netbsd-arm64-cgo), const AF_ARP = 28
pkg syscall (netbsd-arm64-cgo), const AF_ARP ideal-int
pkg syscall (netbsd-arm64-cgo), const AF_BLUETOOTH = 31
pkg syscall (netbsd-arm64-cgo), const AF_BLUETOOTH ideal-int
pkg syscall (netbsd-arm64-cgo), const AF_CCITT = 10
pkg syscall (netbsd-arm64-cgo), const AF_CCITT ideal-int
pkg syscall (netbsd-arm64-cgo), const AF_CHAOS = 5
pkg syscall (netbsd-arm64-cgo), const AF_CHAOS ideal-int
pkg syscall (netbsd-arm64-cgo), const AF_CNT = 21
pkg syscall (netbsd-arm64-cgo), const AF_CNT ideal-int
pkg syscall (netbsd-arm64-cgo), const AF_COIP = 20
pkg syscall (netbsd-arm64-cgo), const AF_COIP ideal-int
pkg syscall (netbsd-arm64-cgo), const AF_DATAKIT = 9
pkg syscall (netbsd-arm64-cgo), const AF_DATAKIT ideal-int
pkg syscall (netbsd-arm64-cgo), const AF_DECnet = 12
pkg syscall (netbsd-arm64-cgo), const AF_DECnet ideal-int
pkg syscall (netbsd-arm64-cgo), const AF_DLI = 13
pkg syscall (netbsd-arm64-cgo), const AF_DLI ideal-int
pkg syscall (netbsd-arm64-cgo), const AF_E164 = 26
pkg syscall (netbsd-arm64-cgo), const AF_E164 ideal-int
pkg syscall (netbsd-arm64-cgo), const AF_ECMA = 8
pkg syscall (netbsd-arm64-cgo), const AF_ECMA ideal-int
pkg syscall (netbsd-arm64-cgo), const AF_HYLINK = 15
pkg syscall (netbsd-arm64-cgo), const AF_HYLINK ideal-int
pkg syscall (netbsd-arm64-cgo), const AF_IEEE80211 = 32
pkg syscall (netbsd-arm64-cgo), const AF_IEEE80211 ideal-int
pkg syscall (netbsd-arm64-cgo), const AF_IMPLINK = 3
pkg syscall (netbsd-arm64-cgo), const AF_IMPLINK ideal-int
pkg syscall (netbsd-arm64-cgo), const AF_INET6 = 24
pkg syscall (netbsd-arm64-cgo), const AF_IPX = 23
pkg syscall (netbsd-arm64-cgo), const AF_IPX ideal-int
pkg syscall (netbsd-arm64-cgo), const AF_ISDN = 26
pkg syscall (netbsd-arm64-cgo), const AF_ISDN ideal-int
pkg syscall (netbsd-arm64-cgo), const AF_ISO = 7
pkg syscall (netbsd-arm64-cgo), const AF_ISO ideal-int
pkg syscall (netbsd-arm64-cgo), const AF_LAT = 14
pkg syscall (netbsd-arm64-cgo), const AF_LAT ideal-int
pkg syscall (netbsd-arm64-cgo), const AF_LINK = 18
pkg syscall (netbsd-arm64-cgo), const AF_LINK ideal-int
pkg syscall (netbsd-arm64-cgo), const AF_LOCAL = 1
pkg syscall (netbsd-arm64-cgo), const AF_LOCAL ideal-int
pkg syscall (netbsd-arm64-cgo), const AF_MAX = 35
pkg syscall (netbsd-arm64-cgo), const AF_MAX ideal-int
pkg syscall (netbsd-arm64-cgo), const AF_MPLS = 33
pkg syscall (netbsd-arm64-cgo), const AF_MPLS ideal-int
pkg syscall (netbsd-arm64-cgo), const AF_NATM = 27
pkg syscall (netbsd-arm64-cgo), const AF_NATM ideal-int
pkg syscall (netbsd-arm64-cgo), const AF_NS = 6
pkg syscall (netbsd-arm64-cgo), const AF_NS ideal-int
pkg syscall (netbsd-arm64-cgo), const AF_OROUTE = 17
pkg syscall (netbsd-arm64-cgo), const AF_OROUTE ideal-int
pkg syscall (netbsd-arm64-cgo), const AF_OSI = 7
pkg syscall (netbsd-arm64-cgo), const AF_OSI ideal-int
pkg syscall (netbsd-arm64-cgo), const AF_PUP = 4
pkg syscall (netbsd-arm64-cgo), const AF_PUP ideal-int
pkg syscall (netbsd-arm64-cgo), const AF_ROUTE = 34
pkg syscall (netbsd-arm64-cgo), const AF_ROUTE ideal-int
pkg syscall (netbsd-arm64-cgo), const AF_SNA = 11
pkg syscall (netbsd-arm64-cgo), const AF_SNA ideal-int
pkg syscall (netbsd-arm64-cgo), const ARPHRD_ARCNET = 7
pkg syscall (netbsd-arm64-cgo), const ARPHRD_ARCNET ideal-int
pkg syscall (netbsd-arm64-cgo), const ARPHRD_ETHER = 1
pkg syscall (netbsd-arm64-cgo), const ARPHRD_ETHER ideal-int
pkg syscall (netbsd-arm64-cgo), const ARPHRD_FRELAY = 15
pkg syscall (netbsd-arm64-cgo), const ARPHRD_FRELAY ideal-int
pkg syscall (netbsd-arm64-cgo), const ARPHRD_IEEE1394 = 24
pkg syscall (netbsd-arm64-cgo), const ARPHRD_IEEE1394 ideal-int
pkg syscall (netbsd-arm64-cgo), const ARPHRD_IEEE802 = 6
pkg syscall (netbsd-arm64-cgo), const ARPHRD_IEEE802 ideal-int
pkg syscall (netbsd-arm64-cgo), const ARPHRD_STRIP = 23
pkg syscall (netbsd-arm64-cgo), const ARPHRD_STRIP ideal-int
pkg syscall (netbsd-arm64-cgo), const B0 = 0
pkg syscall (netbsd-arm64-cgo), const B0 ideal-int
pkg syscall (netbsd-arm64-cgo), const B110 = 110
pkg syscall (netbsd-arm64-cgo), const B110 ideal-int
pkg syscall (netbsd-arm64-cgo), const B115200 = 115200
pkg syscall (netbsd-arm64-cgo), const B115200 ideal-int
pkg syscall (netbsd-arm64-cgo), const B1200 = 1200
pkg syscall (netbsd-arm64-cgo), const B1200 ideal-int
pkg syscall (netbsd-arm64-cgo), const B134 = 134
pkg syscall (netbsd-arm64-cgo), const B134 ideal-int
pkg syscall (netbsd-arm64-cgo), const B14400 = 14400
pkg syscall (netbsd-arm64-cgo), const B14400 ideal-int
pkg syscall (netbsd-arm64-cgo), const B150 = 150
pkg syscall (netbsd-arm64-cgo), const B150 ideal-int
pkg syscall (netbsd-arm64-cgo), const B1800 = 1800
pkg syscall (netbsd-arm64-cgo), const B1800 ideal-int
pkg syscall (netbsd-arm64-cgo), const B19200 = 19200
pkg syscall (netbsd-arm64-cgo), const B19200 ideal-int
pkg syscall (netbsd-arm64-cgo), const B200 = 200
pkg syscall (netbsd-arm64-cgo), const B200 ideal-int
pkg syscall (netbsd-arm64-cgo), const B230400 = 230400
pkg syscall (netbsd-arm64-cgo), const B230400 ideal-int
pkg syscall (netbsd-arm64-cgo), const B2400 = 2400
pkg syscall (netbsd-arm64-cgo), const B2400 ideal-int
pkg syscall (netbsd-arm64-cgo), const B28800 = 28800
pkg syscall (netbsd-arm64-cgo), const B28800 ideal-int
pkg syscall (netbsd-arm64-cgo), const B300 = 300
pkg syscall (netbsd-arm64-cgo), const B300 ideal-int
pkg syscall (netbsd-arm64-cgo), const B38400 = 38400
pkg syscall (netbsd-arm64-cgo), const B38400 ideal-int
pkg syscall (netbsd-arm64-cgo), const B460800 = 460800
pkg syscall (netbsd-arm64-cgo), const B460800 ideal-int
pkg syscall (netbsd-arm64-cgo), const B4800 = 4800
pkg syscall (netbsd-arm64-cgo), const B4800 ideal-int
pkg syscall (netbsd-arm64-cgo), const B50 = 50
pkg syscall (netbsd-arm64-cgo), const B50 ideal-int
pkg syscall (netbsd-arm64-cgo), const B57600 = 57600
pkg syscall (netbsd-arm64-cgo), const B57600 ideal-int
pkg syscall (netbsd-arm64-cgo), const B600 = 600
pkg syscall (netbsd-arm64-cgo), const B600 ideal-int
pkg syscall (netbsd-arm64-cgo), const B7200 = 7200
pkg syscall (netbsd-arm64-cgo), const B7200 ideal-int
pkg syscall (netbsd-arm64-cgo), const B75 = 75
pkg syscall (netbsd-arm64-cgo), const B75 ideal-int
pkg syscall (netbsd-arm64-cgo), const B76800 = 76800
pkg syscall (netbsd-arm64-cgo), const B76800 ideal-int
pkg syscall (netbsd-arm64-cgo), const B921600 = 921600
pkg syscall (netbsd-arm64-cgo), const B921600 ideal-int
pkg syscall (netbsd-arm64-cgo), const B9600 = 9600
pkg syscall (netbsd-arm64-cgo), const B9600 ideal-int
pkg syscall (netbsd-arm64-cgo), const BIOCFEEDBACK = 2147762813
pkg syscall (netbsd-arm64-cgo), const BIOCFEEDBACK ideal-int
pkg syscall (netbsd-arm64-cgo), const BIOCFLUSH = 536887912
pkg syscall (netbsd-arm64-cgo), const BIOCFLUSH ideal-int
pkg syscall (netbsd-arm64-cgo), const BIOCGBLEN = 1074020966
pkg syscall (netbsd-arm64-cgo), const BIOCGBLEN ideal-int
pkg syscall (netbsd-arm64-cgo), const BIOCGDLT = 1074020970
pkg syscall (netbsd-arm64-cgo), const BIOCGDLT ideal-int
pkg syscall (netbsd-arm64-cgo), const BIOCGDLTLIST = 3222291063
pkg syscall (netbsd-arm64-cgo), const BIOCGDLTLIST ideal-int
pkg syscall (netbsd-arm64-cgo), const BIOCGETIF = 1083196011
pkg syscall (netbsd-arm64-cgo), const BIOCGETIF ideal-int
pkg syscall (netbsd-arm64-cgo), const BIOCGFEEDBACK = 1074020988
pkg syscall (netbsd-arm64-cgo), const BIOCGFEEDBACK ideal-int
pkg syscall (netbsd-arm64-cgo), const BIOCGHDRCMPLT = 1074020980
pkg syscall (netbsd-arm64-cgo), const BIOCGHDRCMPLT ideal-int
pkg syscall (netbsd-arm64-cgo), const BIOCGRTIMEOUT = 1074807419
pkg syscall (netbsd-arm64-cgo), const BIOCGRTIMEOUT ideal-int
pkg syscall (netbsd-arm64-cgo), const BIOCGSEESENT = 1074020984
pkg syscall (netbsd-arm64-cgo), const BIOCGSEESENT ideal-int
pkg syscall (netbsd-arm64-cgo), const BIOCGSTATS = 1082147439
pkg syscall (netbsd-arm64-cgo), const BIOCGSTATS ideal-int
pkg syscall (netbsd-arm64-cgo), const BIOCGSTATSOLD = 1074283119
pkg syscall (netbsd-arm64-cgo), const BIOCGSTATSOLD ideal-int
pkg syscall (netbsd-arm64-cgo), const BIOCIMMEDIATE = 2147762800
pkg syscall (netbsd-arm64-cgo), const BIOCIMMEDIATE ideal-int
pkg syscall (netbsd-arm64-cgo), const BIOCPROMISC = 536887913
pkg syscall (netbsd-arm64-cgo), const BIOCPROMISC ideal-int
pkg syscall (netbsd-arm64-cgo), const BIOCSBLEN = 3221504614
pkg syscall (netbsd-arm64-cgo), const BIOCSBLEN ideal-int
pkg syscall (netbsd-arm64-cgo), const BIOCSDLT = 2147762806
pkg syscall (netbsd-arm64-cgo), const BIOCSDLT ideal-int
pkg syscall (netbsd-arm64-cgo), const BIOCSETF = 2148549223
pkg syscall (netbsd-arm64-cgo), const BIOCSETF ideal-int
pkg syscall (netbsd-arm64-cgo), const BIOCSETIF = 2156937836
pkg syscall (netbsd-arm64-cgo), const BIOCSETIF ideal-int
pkg syscall (netbsd-arm64-cgo), const BIOCSFEEDBACK = 2147762813
pkg syscall (netbsd-arm64-cgo), const BIOCSFEEDBACK ideal-int
pkg syscall (netbsd-arm64-cgo), const BIOCSHDRCMPLT = 2147762805
pkg syscall (netbsd-arm64-cgo), const BIOCSHDRCMPLT ideal-int
pkg syscall (netbsd-arm64-cgo), const BIOCSRTIMEOUT = 2148549242
pkg syscall (netbsd-arm64-cgo), const BIOCSRTIMEOUT ideal-int
pkg syscall (netbsd-arm64-cgo), const BIOCSSEESENT = 2147762809
pkg syscall (netbsd-arm64-cgo), const BIOCSSEESENT ideal-int
pkg syscall (netbsd-arm64-cgo), const BIOCSTCPF = 2148549234
pkg syscall (netbsd-arm64-cgo), const BIOCSTCPF ideal-int
pkg syscall (netbsd-arm64-cgo), const BIOCSUDPF = 2148549235
pkg syscall (netbsd-arm64-cgo), const BIOCSUDPF ideal-int
pkg syscall (netbsd-arm64-cgo), const BIOCVERSION = 1074020977
pkg syscall (netbsd-arm64-cgo), const BIOCVERSION ideal-int
pkg syscall (netbsd-arm64-cgo), const BPF_A = 16
pkg syscall (netbsd-arm64-cgo), const BPF_ABS = 32
pkg syscall (netbsd-arm64-cgo), const BPF_ABS ideal-int
pkg syscall (netbsd-arm64-cgo), const BPF_ADD = 0
pkg syscall (netbsd-arm64-cgo), const BPF_ADD ideal-int
pkg syscall (netbsd-arm64-cgo), const BPF_A ideal-int
pkg syscall (netbsd-arm64-cgo), const BPF_ALIGNMENT32 = 4
pkg syscall (netbsd-arm64-cgo), const BPF_ALIGNMENT32 ideal-int
pkg syscall (netbsd-arm64-cgo), const BPF_ALIGNMENT = 8
pkg syscall (netbsd-arm64-cgo), const BPF_ALIGNMENT ideal-int
pkg syscall (netbsd-arm64-cgo), const BPF_ALU = 4
pkg syscall (netbsd-arm64-cgo), const BPF_ALU ideal-int
pkg syscall (netbsd-arm64-cgo), const BPF_AND = 80
pkg syscall (netbsd-arm64-cgo), const BPF_AND ideal-int
pkg syscall (netbsd-arm64-cgo), const BPF_B = 16
pkg syscall (netbsd-arm64-cgo), const BPF_B ideal-int
pkg syscall (netbsd-arm64-cgo), const BPF_DFLTBUFSIZE = 1048576
pkg syscall (netbsd-arm64-cgo), const BPF_DFLTBUFSIZE ideal-int
pkg syscall (netbsd-arm64-cgo), const BPF_DIV = 48
pkg syscall (netbsd-arm64-cgo), const BPF_DIV ideal-int
pkg syscall (netbsd-arm64-cgo), const BPF_H = 8
pkg syscall (netbsd-arm64-cgo), const BPF_H ideal-int
pkg syscall (netbsd-arm64-cgo), const BPF_IMM = 0
pkg syscall (netbsd-arm64-cgo), const BPF_IMM ideal-int
pkg syscall (netbsd-arm64-cgo), const BPF_IND = 64
pkg syscall (netbsd-arm64-cgo), const BPF_IND ideal-int
pkg syscall (netbsd-arm64-cgo), const BPF_JA = 0
pkg syscall (netbsd-arm64-cgo), const BPF_JA ideal-int
pkg syscall (netbsd-arm64-cgo), const BPF_JEQ = 16
pkg syscall (netbsd-arm64-cgo), const BPF_JEQ ideal-int
pkg syscall (netbsd-arm64-cgo), const BPF_JGE = 48
pkg syscall (netbsd-arm64-cgo), const BPF_JGE ideal-int
pkg syscall (netbsd-arm64-cgo), const BPF_JGT = 32
pkg syscall (netbsd-arm64-cgo), const BPF_JGT ideal-int
pkg syscall (netbsd-arm64-cgo), const BPF_JMP = 5
pkg syscall (netbsd-arm64-cgo), const BPF_JMP ideal-int
pkg syscall (netbsd-arm64-cgo), const BPF_JSET = 64
pkg syscall (netbsd-arm64-cgo), const BPF_JSET ideal-int
pkg syscall (netbsd-arm64-cgo), const BPF_K = 0
pkg syscall (netbsd-arm64-cgo), const BPF_K ideal-int
pkg syscall (netbsd-arm64-cgo), const BPF_LD = 0
pkg syscall (netbsd-arm64-cgo), const BPF_LD ideal-int
pkg syscall (netbsd-arm64-cgo), const BPF_LDX = 1
pkg syscall (netbsd-arm64-cgo), const BPF_LDX ideal-int
pkg syscall (netbsd-arm64-cgo), const BPF_LEN = 128
pkg syscall (netbsd-arm64-cgo), const BPF_LEN ideal-int
pkg syscall (netbsd-arm64-cgo), const BPF_LSH = 96
pkg syscall (netbsd-arm64-cgo), const BPF_LSH ideal-int
pkg syscall (netbsd-arm64-cgo), const BPF_MAJOR_VERSION = 1
pkg syscall (netbsd-arm64-cgo), const BPF_MAJOR_VERSION ideal-int
pkg syscall (netbsd-arm64-cgo), const BPF_MAXBUFSIZE = 16777216
pkg syscall (netbsd-arm64-cgo), const BPF_MAXBUFSIZE ideal-int
pkg syscall (netbsd-arm64-cgo), const BPF_MAXINSNS = 512
pkg syscall (netbsd-arm64-cgo), const BPF_MAXINSNS ideal-int
pkg syscall (netbsd-arm64-cgo), const BPF_MEM = 96
pkg syscall (netbsd-arm64-cgo), const BPF_MEM ideal-int
pkg syscall (netbsd-arm64-cgo), const BPF_MEMWORDS = 16
pkg syscall (netbsd-arm64-cgo), const BPF_MEMWORDS ideal-int
pkg syscall (netbsd-arm64-cgo), const BPF_MINBUFSIZE = 32
pkg syscall (netbsd-arm64-cgo), const BPF_MINBUFSIZE ideal-int
pkg syscall (netbsd-arm64-cgo), const BPF_MINOR_VERSION = 1
pkg syscall (netbsd-arm64-cgo), const BPF_MINOR_VERSION ideal-int
pkg syscall (netbsd-arm64-cgo), const BPF_MISC = 7
pkg syscall (netbsd-arm64-cgo), const BPF_MISC ideal-int
pkg syscall (netbsd-arm64-cgo), const BPF_MSH = 160
pkg syscall (netbsd-arm64-cgo), const BPF_MSH ideal-int
pkg syscall (netbsd-arm64-cgo), const BPF_MUL = 32
pkg syscall (netbsd-arm64-cgo), const BPF_MUL ideal-int
pkg syscall (netbsd-arm64-cgo), const BPF_NEG = 128
pkg syscall (netbsd-arm64-cgo), const BPF_NEG ideal-int
pkg syscall (netbsd-arm64-cgo), const BPF_OR = 64
pkg syscall (netbsd-arm64-cgo), const BPF_OR ideal-int
pkg syscall (netbsd-arm64-cgo), const BPF_RELEASE = 199606
pkg syscall (netbsd-arm64-cgo), const BPF_RELEASE ideal-int
pkg syscall (netbsd-arm64-cgo), const BPF_RET = 6
pkg syscall (netbsd-arm64-cgo), const BPF_RET ideal-int
pkg syscall (netbsd-arm64-cgo), const BPF_RSH = 112
pkg syscall (netbsd-arm64-cgo), const BPF_RSH ideal-int
pkg syscall (netbsd-arm64-cgo), const BPF_ST = 2
pkg syscall (netbsd-arm64-cgo), const BPF_ST ideal-int
pkg syscall (netbsd-arm64-cgo), const BPF_STX = 3
pkg syscall (netbsd-arm64-cgo), const BPF_STX ideal-int
pkg syscall (netbsd-arm64-cgo), const BPF_SUB = 16
pkg syscall (netbsd-arm64-cgo), const BPF_SUB ideal-int
pkg syscall (netbsd-arm64-cgo), const BPF_TAX = 0
pkg syscall (netbsd-arm64-cgo), const BPF_TAX ideal-int
pkg syscall (netbsd-arm64-cgo), const BPF_TXA = 128
pkg syscall (netbsd-arm64-cgo), const BPF_TXA ideal-int
pkg syscall (netbsd-arm64-cgo), const BPF_W = 0
pkg syscall (netbsd-arm64-cgo), const BPF_W ideal-int
pkg syscall (netbsd-arm64-cgo), const BPF_X = 8
pkg syscall (netbsd-arm64-cgo), const BPF_X ideal-int
pkg syscall (netbsd-arm64-cgo), const BRKINT = 2
pkg syscall (netbsd-arm64-cgo), const BRKINT ideal-int
pkg syscall (netbsd-arm64-cgo), const CFLUSH = 15
pkg syscall (netbsd-arm64-cgo), const CFLUSH ideal-int
pkg syscall (netbsd-arm64-cgo), const CLOCAL = 32768
pkg syscall (netbsd-arm64-cgo), const CLOCAL ideal-int
pkg syscall (netbsd-arm64-cgo), const CLONE_CSIGNAL = 255
pkg syscall (netbsd-arm64-cgo), const CLONE_CSIGNAL ideal-int
pkg syscall (netbsd-arm64-cgo), const CLONE_FILES = 1024
pkg syscall (netbsd-arm64-cgo), const CLONE_FILES ideal-int
pkg syscall (netbsd-arm64-cgo), const CLONE_FS = 512
pkg syscall (netbsd-arm64-cgo), const CLONE_FS ideal-int
pkg syscall (netbsd-arm64-cgo), const CLONE_PID = 4096
pkg syscall (netbsd-arm64-cgo), const CLONE_PID ideal-int
pkg syscall (netbsd-arm64-cgo), const CLONE_PTRACE = 8192
pkg syscall (netbsd-arm64-cgo), const CLONE_PTRACE ideal-int
pkg syscall (netbsd-arm64-cgo), const CLONE_SIGHAND = 2048
pkg syscall (netbsd-arm64-cgo), const CLONE_SIGHAND ideal-int
pkg syscall (netbsd-arm64-cgo), const CLONE_VFORK = 16384
pkg syscall (netbsd-arm64-cgo), const CLONE_VFORK ideal-int
pkg syscall (netbsd-arm64-cgo), const CLONE_VM = 256
pkg syscall (netbsd-arm64-cgo), const CLONE_VM ideal-int
pkg syscall (netbsd-arm64-cgo), const CREAD = 2048
pkg syscall (netbsd-arm64-cgo), const CREAD ideal-int
pkg syscall (netbsd-arm64-cgo), const CS5 = 0
pkg syscall (netbsd-arm64-cgo), const CS5 ideal-int
pkg syscall (netbsd-arm64-cgo), const CS6 = 256
pkg syscall (netbsd-arm64-cgo), const CS6 ideal-int
pkg syscall (netbsd-arm64-cgo), const CS7 = 512
pkg syscall (netbsd-arm64-cgo), const CS7 ideal-int
pkg syscall (netbsd-arm64-cgo), const CS8 = 768
pkg syscall (netbsd-arm64-cgo), const CS8 ideal-int
pkg syscall (netbsd-arm64-cgo), const CSIZE = 768
pkg syscall (netbsd-arm64-cgo), const CSIZE ideal-int
pkg syscall (netbsd-arm64-cgo), const CSTART = 17
pkg syscall (netbsd-arm64-cgo), const CSTART ideal-int
pkg syscall (netbsd-arm64-cgo), const CSTATUS = 20
pkg syscall (netbsd-arm64-cgo), const CSTATUS ideal-int
pkg syscall (netbsd-arm64-cgo), const CSTOP = 19
pkg syscall (netbsd-arm64-cgo), const CSTOPB = 1024
pkg syscall (netbsd-arm64-cgo), const CSTOPB ideal-int
pkg syscall (netbsd-arm64-cgo), const CSTOP ideal-int
pkg syscall (netbsd-arm64-cgo), const CSUSP = 26
pkg syscall (netbsd-arm64-cgo), const CSUSP ideal-int
pkg syscall (netbsd-arm64-cgo), const CTL_MAXNAME = 12
pkg syscall (netbsd-arm64-cgo), const CTL_MAXNAME ideal-int
pkg syscall (netbsd-arm64-cgo), const CTL_NET = 4
pkg syscall (netbsd-arm64-cgo), const CTL_NET ideal-int
pkg syscall (netbsd-arm64-cgo), const CTL_QUERY = -2
pkg syscall (netbsd-arm64-cgo), const CTL_QUERY ideal-int
pkg syscall (netbsd-arm64-cgo), const DIOCBSFLUSH = 536896632
pkg syscall (netbsd-arm64-cgo), const DIOCBSFLUSH ideal-int
pkg syscall (netbsd-arm64-cgo), const DLT_A429 = 184
pkg syscall (netbsd-arm64-cgo), const DLT_A429 ideal-int
pkg syscall (netbsd-arm64-cgo), const DLT_A653_ICM = 185
pkg syscall (netbsd-arm64-cgo), const DLT_A653_ICM ideal-int
pkg syscall (netbsd-arm64-cgo), const DLT_AIRONET_HEADER = 120
pkg syscall (netbsd-arm64-cgo), const DLT_AIRONET_HEADER ideal-int
pkg syscall (netbsd-arm64-cgo), const DLT_AOS = 222
pkg syscall (netbsd-arm64-cgo), const DLT_AOS ideal-int
pkg syscall (netbsd-arm64-cgo), const DLT_APPLE_IP_OVER_IEEE1394 = 138
pkg syscall (netbsd-arm64-cgo), const DLT_APPLE_IP_OVER_IEEE1394 ideal-int
pkg syscall (netbsd-arm64-cgo), const DLT_ARCNET = 7
pkg syscall (netbsd-arm64-cgo), const DLT_ARCNET ideal-int
pkg syscall (netbsd-arm64-cgo), const DLT_ARCNET_LINUX = 129
pkg syscall (netbsd-arm64-cgo), const DLT_ARCNET_LINUX ideal-int
pkg syscall (netbsd-arm64-cgo), const DLT_ATM_CLIP = 19
pkg syscall (netbsd-arm64-cgo), const DLT_ATM_CLIP ideal-int
pkg syscall (netbsd-arm64-cgo), const DLT_ATM_RFC1483 = 11
pkg syscall (netbsd-arm64-cgo), const DLT_ATM_RFC1483 ideal-int
pkg syscall (netbsd-arm64-cgo), const DLT_AURORA = 126
pkg syscall (netbsd-arm64-cgo), const DLT_AURORA ideal-int
pkg syscall (netbsd-arm64-cgo), const DLT_AX25 = 3
pkg syscall (netbsd-arm64-cgo), const DLT_AX25 ideal-int
pkg syscall (netbsd-arm64-cgo), const DLT_AX25_KISS = 202
pkg syscall (netbsd-arm64-cgo), const DLT_AX25_KISS ideal-int
pkg syscall (netbsd-arm64-cgo), const DLT_BACNET_MS_TP = 165
pkg syscall (netbsd-arm64-cgo), const DLT_BACNET_MS_TP ideal-int
pkg syscall (netbsd-arm64-cgo), const DLT_BLUETOOTH_HCI_H4 = 187
pkg syscall (netbsd-arm64-cgo), const DLT_BLUETOOTH_HCI_H4 ideal-int
pkg syscall (netbsd-arm64-cgo), const DLT_BLUETOOTH_HCI_H4_WITH_PHDR = 201
pkg syscall (netbsd-arm64-cgo), const DLT_BLUETOOTH_HCI_H4_WITH_PHDR ideal-int
pkg syscall (netbsd-arm64-cgo), const DLT_CAN20B = 190
pkg syscall (netbsd-arm64-cgo), const DLT_CAN20B ideal-int
pkg syscall (netbsd-arm64-cgo), const DLT_CAN_SOCKETCAN = 227
pkg syscall (netbsd-arm64-cgo), const DLT_CAN_SOCKETCAN ideal-int
pkg syscall (netbsd-arm64-cgo), const DLT_CHAOS = 5
pkg syscall (netbsd-arm64-cgo), const DLT_CHAOS ideal-int
pkg syscall (netbsd-arm64-cgo), const DLT_C_HDLC = 104
pkg syscall (netbsd-arm64-cgo), const DLT_C_HDLC ideal-int
pkg syscall (netbsd-arm64-cgo), const DLT_C_HDLC_WITH_DIR = 205
pkg syscall (netbsd-arm64-cgo), const DLT_C_HDLC_WITH_DIR ideal-int
pkg syscall (netbsd-arm64-cgo), const DLT_CISCO_IOS = 118
pkg syscall (netbsd-arm64-cgo), const DLT_CISCO_IOS ideal-int
pkg syscall (netbsd-arm64-cgo), const DLT_DECT = 221
pkg syscall (netbsd-arm64-cgo), const DLT_DECT ideal-int
pkg syscall (netbsd-arm64-cgo), const DLT_DOCSIS = 143
pkg syscall (netbsd-arm64-cgo), const DLT_DOCSIS ideal-int
pkg syscall (netbsd-arm64-cgo), const DLT_ECONET = 115
pkg syscall (netbsd-arm64-cgo), const DLT_ECONET ideal-int
pkg syscall (netbsd-arm64-cgo), const DLT_EN10MB = 1
pkg syscall (netbsd-arm64-cgo), const DLT_EN10MB ideal-int
pkg syscall (netbsd-arm64-cgo), const DLT_EN3MB = 2
pkg syscall (netbsd-arm64-cgo), const DLT_EN3MB ideal-int
pkg syscall (netbsd-arm64-cgo), const DLT_ENC = 109
pkg syscall (netbsd-arm64-cgo), const DLT_ENC ideal-int
pkg syscall (netbsd-arm64-cgo), const DLT_ERF = 197
pkg syscall (netbsd-arm64-cgo), const DLT_ERF_ETH = 175
pkg syscall (netbsd-arm64-cgo), const DLT_ERF_ETH ideal-int
pkg syscall (netbsd-arm64-cgo), const DLT_ERF ideal-int
pkg syscall (netbsd-arm64-cgo), const DLT_ERF_POS = 176
pkg syscall (netbsd-arm64-cgo), const DLT_ERF_POS ideal-int
pkg syscall (netbsd-arm64-cgo), const DLT_FC_2 = 224
pkg syscall (netbsd-arm64-cgo), const DLT_FC_2 ideal-int
pkg syscall (netbsd-arm64-cgo), const DLT_FC_2_WITH_FRAME_DELIMS = 225
pkg syscall (netbsd-arm64-cgo), const DLT_FC_2_WITH_FRAME_DELIMS ideal-int
pkg syscall (netbsd-arm64-cgo), const DLT_FDDI = 10
pkg syscall (netbsd-arm64-cgo), const DLT_FDDI ideal-int
pkg syscall (netbsd-arm64-cgo), const DLT_FLEXRAY = 210
pkg syscall (netbsd-arm64-cgo), const DLT_FLEXRAY ideal-int
pkg syscall (netbsd-arm64-cgo), const DLT_FRELAY = 107
pkg syscall (netbsd-arm64-cgo), const DLT_FRELAY ideal-int
pkg syscall (netbsd-arm64-cgo), const DLT_FRELAY_WITH_DIR = 206
pkg syscall (netbsd-arm64-cgo), const DLT_FRELAY_WITH_DIR ideal-int
pkg syscall (netbsd-arm64-cgo), const DLT_GCOM_SERIAL = 173
pkg syscall (netbsd-arm64-cgo), const DLT_GCOM_SERIAL ideal-int
pkg syscall (netbsd-arm64-cgo), const DLT_GCOM_T1E1 = 172
pkg syscall (netbsd-arm64-cgo), const DLT_GCOM_T1E1 ideal-int
pkg syscall (netbsd-arm64-cgo), const DLT_GPF_F = 171
pkg syscall (netbsd-arm64-cgo), const DLT_GPF_F ideal-int
pkg syscall (netbsd-arm64-cgo), const DLT_GPF_T = 170
pkg syscall (netbsd-arm64-cgo), const DLT_GPF_T ideal-int
pkg syscall (netbsd-arm64-cgo), const DLT_GPRS_LLC = 169
pkg syscall (netbsd-arm64-cgo), const DLT_GPRS_LLC ideal-int
pkg syscall (netbsd-arm64-cgo), const DLT_GSMTAP_ABIS = 218
pkg syscall (netbsd-arm64-cgo), const DLT_GSMTAP_ABIS ideal-int
pkg syscall (netbsd-arm64-cgo), const DLT_GSMTAP_UM = 217
pkg syscall (netbsd-arm64-cgo), const DLT_GSMTAP_UM ideal-int
pkg syscall (netbsd-arm64-cgo), const DLT_HDLC = 16
pkg syscall (netbsd-arm64-cgo), const DLT_HDLC ideal-int
pkg syscall (netbsd-arm64-cgo), const DLT_HHDLC = 121
pkg syscall (netbsd-arm64-cgo), const DLT_HHDLC ideal-int
pkg syscall (netbsd-arm64-cgo), const DLT_HIPPI = 15
pkg syscall (netbsd-arm64-cgo), const DLT_HIPPI ideal-int
pkg syscall (netbsd-arm64-cgo), const DLT_IBM_SN = 146
pkg syscall (netbsd-arm64-cgo), const DLT_IBM_SN ideal-int
pkg syscall (netbsd-arm64-cgo), const DLT_IBM_SP = 145
pkg syscall (netbsd-arm64-cgo), const DLT_IBM_SP ideal-int
pkg syscall (netbsd-arm64-cgo), const DLT_IEEE802_11 = 105
pkg syscall (netbsd-arm64-cgo), const DLT_IEEE802_11 ideal-int
pkg syscall (netbsd-arm64-cgo), const DLT_IEEE802_11_RADIO = 127
pkg syscall (netbsd-arm64-cgo), const DLT_IEEE802_11_RADIO_AVS = 163
pkg syscall (netbsd-arm64-cgo), const DLT_IEEE802_11_RADIO_AVS ideal-int
pkg syscall (netbsd-arm64-cgo), const DLT_IEEE802_11_RADIO ideal-int
pkg syscall (netbsd-arm64-cgo), const DLT_IEEE802_15_4 = 195
pkg syscall (netbsd-arm64-cgo), const DLT_IEEE802_15_4 ideal-int
pkg syscall (netbsd-arm64-cgo), const DLT_IEEE802_15_4_LINUX = 191
pkg syscall (netbsd-arm64-cgo), const DLT_IEEE802_15_4_LINUX ideal-int
pkg syscall (netbsd-arm64-cgo), const DLT_IEEE802_15_4_NONASK_PHY = 215
pkg syscall (netbsd-arm64-cgo), const DLT_IEEE802_15_4_NONASK_PHY ideal-int
pkg syscall (netbsd-arm64-cgo), const DLT_IEEE802_16_MAC_CPS = 188
pkg syscall (netbsd-arm64-cgo), const DLT_IEEE802_16_MAC_CPS ideal-int
pkg syscall (netbsd-arm64-cgo), const DLT_IEEE802_16_MAC_CPS_RADIO = 193
pkg syscall (netbsd-arm64-cgo), const DLT_IEEE802_16_MAC_CPS_RADIO ideal-int
pkg syscall (netbsd-arm64-cgo), const DLT_IEEE802 = 6
pkg syscall (netbsd-arm64-cgo), const DLT_IEEE802 ideal-int
pkg syscall (netbsd-arm64-cgo), const DLT_IPMB = 199
pkg syscall (netbsd-arm64-cgo), const DLT_IPMB ideal-int
pkg syscall (netbsd-arm64-cgo), const DLT_IPMB_LINUX = 209
pkg syscall (netbsd-arm64-cgo), const DLT_IPMB_LINUX ideal-int
pkg syscall (netbsd-arm64-cgo), const DLT_IPNET = 226
pkg syscall (netbsd-arm64-cgo), const DLT_IPNET ideal-int
pkg syscall (netbsd-arm64-cgo), const DLT_IP_OVER_FC = 122
pkg syscall (netbsd-arm64-cgo), const DLT_IP_OVER_FC ideal-int
pkg syscall (netbsd-arm64-cgo), const DLT_IPV4 = 228
pkg syscall (netbsd-arm64-cgo), const DLT_IPV4 ideal-int
pkg syscall (netbsd-arm64-cgo), const DLT_IPV6 = 229
pkg syscall (netbsd-arm64-cgo), const DLT_IPV6 ideal-int
pkg syscall (netbsd-arm64-cgo), const DLT_JUNIPER_ATM1 = 137
pkg syscall (netbsd-arm64-cgo), const DLT_JUNIPER_ATM1 ideal-int
pkg syscall (netbsd-arm64-cgo), const DLT_JUNIPER_ATM2 = 135
pkg syscall (netbsd-arm64-cgo), const DLT_JUNIPER_ATM2 ideal-int
pkg syscall (netbsd-arm64-cgo), const DLT_JUNIPER_CHDLC = 181
pkg syscall (netbsd-arm64-cgo), const DLT_JUNIPER_CHDLC ideal-int
pkg syscall (netbsd-arm64-cgo), const DLT_JUNIPER_ES = 132
pkg syscall (netbsd-arm64-cgo), const DLT_JUNIPER_ES ideal-int
pkg syscall (netbsd-arm64-cgo), const DLT_JUNIPER_ETHER = 178
pkg syscall (netbsd-arm64-cgo), const DLT_JUNIPER_ETHER ideal-int
pkg syscall (netbsd-arm64-cgo), const DLT_JUNIPER_FRELAY = 180
pkg syscall (netbsd-arm64-cgo), const DLT_JUNIPER_FRELAY ideal-int
pkg syscall (netbsd-arm64-cgo), const DLT_JUNIPER_GGSN = 133
pkg syscall (netbsd-arm64-cgo), const DLT_JUNIPER_GGSN ideal-int
pkg syscall (netbsd-arm64-cgo), const DLT_JUNIPER_ISM = 194
pkg syscall (netbsd-arm64-cgo), const DLT_JUNIPER_ISM ideal-int
pkg syscall (netbsd-arm64-cgo), const DLT_JUNIPER_MFR = 134
pkg syscall (netbsd-arm64-cgo), const DLT_JUNIPER_MFR ideal-int
pkg syscall (netbsd-arm64-cgo), const DLT_JUNIPER_MLFR = 131
pkg syscall (netbsd-arm64-cgo), const DLT_JUNIPER_MLFR ideal-int
pkg syscall (netbsd-arm64-cgo), const DLT_JUNIPER_MLPPP = 130
pkg syscall (netbsd-arm64-cgo), const DLT_JUNIPER_MLPPP ideal-int
pkg syscall (netbsd-arm64-cgo), const DLT_JUNIPER_MONITOR = 164
pkg syscall (netbsd-arm64-cgo), const DLT_JUNIPER_MONITOR ideal-int
pkg syscall (netbsd-arm64-cgo), const DLT_JUNIPER_PIC_PEER = 174
pkg syscall (netbsd-arm64-cgo), const DLT_JUNIPER_PIC_PEER ideal-int
pkg syscall (netbsd-arm64-cgo), const DLT_JUNIPER_PPP = 179
pkg syscall (netbsd-arm64-cgo), const DLT_JUNIPER_PPP ideal-int
pkg syscall (netbsd-arm64-cgo), const DLT_JUNIPER_PPPOE = 167
pkg syscall (netbsd-arm64-cgo), const DLT_JUNIPER_PPPOE_ATM = 168
pkg syscall (netbsd-arm64-cgo), const DLT_JUNIPER_PPPOE_ATM ideal-int
pkg syscall (netbsd-arm64-cgo), const DLT_JUNIPER_PPPOE ideal-int
pkg syscall (netbsd-arm64-cgo), const DLT_JUNIPER_SERVICES = 136
pkg syscall (netbsd-arm64-cgo), const DLT_JUNIPER_SERVICES ideal-int
pkg syscall (netbsd-arm64-cgo), const DLT_JUNIPER_ST = 200
pkg syscall (netbsd-arm64-cgo), const DLT_JUNIPER_ST ideal-int
pkg syscall (netbsd-arm64-cgo), const DLT_JUNIPER_VP = 183
pkg syscall (netbsd-arm64-cgo), const DLT_JUNIPER_VP ideal-int
pkg syscall (netbsd-arm64-cgo), const DLT_LAPB_WITH_DIR = 207
pkg syscall (netbsd-arm64-cgo), const DLT_LAPB_WITH_DIR ideal-int
pkg syscall (netbsd-arm64-cgo), const DLT_LAPD = 203
pkg syscall (netbsd-arm64-cgo), const DLT_LAPD ideal-int
pkg syscall (netbsd-arm64-cgo), const DLT_LIN = 212
pkg syscall (netbsd-arm64-cgo), const DLT_LIN ideal-int
pkg syscall (netbsd-arm64-cgo), const DLT_LINUX_EVDEV = 216
pkg syscall (netbsd-arm64-cgo), const DLT_LINUX_EVDEV ideal-int
pkg syscall (netbsd-arm64-cgo), const DLT_LINUX_IRDA = 144
pkg syscall (netbsd-arm64-cgo), const DLT_LINUX_IRDA ideal-int
pkg syscall (netbsd-arm64-cgo), const DLT_LINUX_LAPD = 177
pkg syscall (netbsd-arm64-cgo), const DLT_LINUX_LAPD ideal-int
pkg syscall (netbsd-arm64-cgo), const DLT_LINUX_SLL = 113
pkg syscall (netbsd-arm64-cgo), const DLT_LINUX_SLL ideal-int
pkg syscall (netbsd-arm64-cgo), const DLT_LOOP = 108
pkg syscall (netbsd-arm64-cgo), const DLT_LOOP ideal-int
pkg syscall (netbsd-arm64-cgo), const DLT_LTALK = 114
pkg syscall (netbsd-arm64-cgo), const DLT_LTALK ideal-int
pkg syscall (netbsd-arm64-cgo), const DLT_MFR = 182
pkg syscall (netbsd-arm64-cgo), const DLT_MFR ideal-int
pkg syscall (netbsd-arm64-cgo), const DLT_MOST = 211
pkg syscall (netbsd-arm64-cgo), const DLT_MOST ideal-int
pkg syscall (netbsd-arm64-cgo), const DLT_MPLS = 219
pkg syscall (netbsd-arm64-cgo), const DLT_MPLS ideal-int
pkg syscall (netbsd-arm64-cgo), const DLT_MTP2 = 140
pkg syscall (netbsd-arm64-cgo), const DLT_MTP2 ideal-int
pkg syscall (netbsd-arm64-cgo), const DLT_MTP2_WITH_PHDR = 139
pkg syscall (netbsd-arm64-cgo), const DLT_MTP2_WITH_PHDR ideal-int
pkg syscall (netbsd-arm64-cgo), const DLT_MTP3 = 141
pkg syscall (netbsd-arm64-cgo), const DLT_MTP3 ideal-int
pkg syscall (netbsd-arm64-cgo), const DLT_NULL = 0
pkg syscall (netbsd-arm64-cgo), const DLT_NULL ideal-int
pkg syscall (netbsd-arm64-cgo), const DLT_PCI_EXP = 125
pkg syscall (netbsd-arm64-cgo), const DLT_PCI_EXP ideal-int
pkg syscall (netbsd-arm64-cgo), const DLT_PFLOG = 117
pkg syscall (netbsd-arm64-cgo), const DLT_PFLOG ideal-int
pkg syscall (netbsd-arm64-cgo), const DLT_PFSYNC = 18
pkg syscall (netbsd-arm64-cgo), const DLT_PFSYNC ideal-int
pkg syscall (netbsd-arm64-cgo), const DLT_PPI = 192
pkg syscall (netbsd-arm64-cgo), const DLT_PPI ideal-int
pkg syscall (netbsd-arm64-cgo), const DLT_PPP = 9
pkg syscall (netbsd-arm64-cgo), const DLT_PPP_BSDOS = 14
pkg syscall (netbsd-arm64-cgo), const DLT_PPP_BSDOS ideal-int
pkg syscall (netbsd-arm64-cgo), const DLT_PPP_ETHER = 51
pkg syscall (netbsd-arm64-cgo), const DLT_PPP_ETHER ideal-int
pkg syscall (netbsd-arm64-cgo), const DLT_PPP ideal-int
pkg syscall (netbsd-arm64-cgo), const DLT_PPP_PPPD = 166
pkg syscall (netbsd-arm64-cgo), const DLT_PPP_PPPD ideal-int
pkg syscall (netbsd-arm64-cgo), const DLT_PPP_SERIAL = 50
pkg syscall (netbsd-arm64-cgo), const DLT_PPP_SERIAL ideal-int
pkg syscall (netbsd-arm64-cgo), const DLT_PPP_WITH_DIR = 204
pkg syscall (netbsd-arm64-cgo), const DLT_PPP_WITH_DIR ideal-int
pkg syscall (netbsd-arm64-cgo), const DLT_PRISM_HEADER = 119
pkg syscall (netbsd-arm64-cgo), const DLT_PRISM_HEADER ideal-int
pkg syscall (netbsd-arm64-cgo), const DLT_PRONET = 4
pkg syscall (netbsd-arm64-cgo), const DLT_PRONET ideal-int
pkg syscall (netbsd-arm64-cgo), const DLT_RAIF1 = 198
pkg syscall (netbsd-arm64-cgo), const DLT_RAIF1 ideal-int
pkg syscall (netbsd-arm64-cgo), const DLT_RAW = 12
pkg syscall (netbsd-arm64-cgo), const DLT_RAWAF_MASK = 35913728
pkg syscall (netbsd-arm64-cgo), const DLT_RAWAF_MASK ideal-int
pkg syscall (netbsd-arm64-cgo), const DLT_RAW ideal-int
pkg syscall (netbsd-arm64-cgo), const DLT_RIO = 124
pkg syscall (netbsd-arm64-cgo), const DLT_RIO ideal-int
pkg syscall (netbsd-arm64-cgo), const DLT_SCCP = 142
pkg syscall (netbsd-arm64-cgo), const DLT_SCCP ideal-int
pkg syscall (netbsd-arm64-cgo), const DLT_SITA = 196
pkg syscall (netbsd-arm64-cgo), const DLT_SITA ideal-int
pkg syscall (netbsd-arm64-cgo), const DLT_SLIP = 8
pkg syscall (netbsd-arm64-cgo), const DLT_SLIP_BSDOS = 13
pkg syscall (netbsd-arm64-cgo), const DLT_SLIP_BSDOS ideal-int
pkg syscall (netbsd-arm64-cgo), const DLT_SLIP ideal-int
pkg syscall (netbsd-arm64-cgo), const DLT_SUNATM = 123
pkg syscall (netbsd-arm64-cgo), const DLT_SUNATM ideal-int
pkg syscall (netbsd-arm64-cgo), const DLT_SYMANTEC_FIREWALL = 99
pkg syscall (netbsd-arm64-cgo), const DLT_SYMANTEC_FIREWALL ideal-int
pkg syscall (netbsd-arm64-cgo), const DLT_TZSP = 128
pkg syscall (netbsd-arm64-cgo), const DLT_TZSP ideal-int
pkg syscall (netbsd-arm64-cgo), const DLT_USB = 186
pkg syscall (netbsd-arm64-cgo), const DLT_USB ideal-int
pkg syscall (netbsd-arm64-cgo), const DLT_USB_LINUX = 189
pkg syscall (netbsd-arm64-cgo), const DLT_USB_LINUX ideal-int
pkg syscall (netbsd-arm64-cgo), const DLT_USB_LINUX_MMAPPED = 220
pkg syscall (netbsd-arm64-cgo), const DLT_USB_LINUX_MMAPPED ideal-int
pkg syscall (netbsd-arm64-cgo), const DLT_WIHART = 223
pkg syscall (netbsd-arm64-cgo), const DLT_WIHART ideal-int
pkg syscall (netbsd-arm64-cgo), const DLT_X2E_SERIAL = 213
pkg syscall (netbsd-arm64-cgo), const DLT_X2E_SERIAL ideal-int
pkg syscall (netbsd-arm64-cgo), const DLT_X2E_XORAYA = 214
pkg syscall (netbsd-arm64-cgo), const DLT_X2E_XORAYA ideal-int
pkg syscall (netbsd-arm64-cgo), const DT_BLK = 6
pkg syscall (netbsd-arm64-cgo), const DT_BLK ideal-int
pkg syscall (netbsd-arm64-cgo), const DT_CHR = 2
pkg syscall (netbsd-arm64-cgo), const DT_CHR ideal-int
pkg syscall (netbsd-arm64-cgo), const DT_DIR = 4
pkg syscall (netbsd-arm64-cgo), const DT_DIR ideal-int
pkg syscall (netbsd-arm64-cgo), const DT_FIFO = 1
pkg syscall (netbsd-arm64-cgo), const DT_FIFO ideal-int
pkg syscall (netbsd-arm64-cgo), const DT_LNK = 10
pkg syscall (netbsd-arm64-cgo), const DT_LNK ideal-int
pkg syscall (netbsd-arm64-cgo), const DT_REG = 8
pkg syscall (netbsd-arm64-cgo), const DT_REG ideal-int
pkg syscall (netbsd-arm64-cgo), const DT_SOCK = 12
pkg syscall (netbsd-arm64-cgo), const DT_SOCK ideal-int
pkg syscall (netbsd-arm64-cgo), const DT_UNKNOWN = 0
pkg syscall (netbsd-arm64-cgo), const DT_UNKNOWN ideal-int
pkg syscall (netbsd-arm64-cgo), const DT_WHT = 14
pkg syscall (netbsd-arm64-cgo), const DT_WHT ideal-int
pkg syscall (netbsd-arm64-cgo), const E2BIG = 7
pkg syscall (netbsd-arm64-cgo), const EACCES = 13
pkg syscall (netbsd-arm64-cgo), const EADDRINUSE = 48
pkg syscall (netbsd-arm64-cgo), const EADDRNOTAVAIL = 49
pkg syscall (netbsd-arm64-cgo), const EAFNOSUPPORT = 47
pkg syscall (netbsd-arm64-cgo), const EAGAIN = 35
pkg syscall (netbsd-arm64-cgo), const EALREADY = 37
pkg syscall (netbsd-arm64-cgo), const EAUTH = 80
pkg syscall (netbsd-arm64-cgo), const EAUTH Errno
pkg syscall (netbsd-arm64-cgo), const EBADF = 9
pkg syscall (netbsd-arm64-cgo), const EBADMSG = 88
pkg syscall (netbsd-arm64-cgo), const EBADMSG Errno
pkg syscall (netbsd-arm64-cgo), const EBADRPC = 72
pkg syscall (netbsd-arm64-cgo), const EBADRPC Errno
pkg syscall (netbsd-arm64-cgo), const EBUSY = 16
pkg syscall (netbsd-arm64-cgo), const ECANCELED = 87
pkg syscall (netbsd-arm64-cgo), const ECHILD = 10
pkg syscall (netbsd-arm64-cgo), const ECHO = 8
pkg syscall (netbsd-arm64-cgo), const ECHOCTL = 64
pkg syscall (netbsd-arm64-cgo), const ECHOCTL ideal-int
pkg syscall (netbsd-arm64-cgo), const ECHOE = 2
pkg syscall (netbsd-arm64-cgo), const ECHOE ideal-int
pkg syscall (netbsd-arm64-cgo), const ECHO ideal-int
pkg syscall (netbsd-arm64-cgo), const ECHOK = 4
pkg syscall (netbsd-arm64-cgo), const ECHOKE = 1
pkg syscall (netbsd-arm64-cgo), const ECHOKE ideal-int
pkg syscall (netbsd-arm64-cgo), const ECHOK ideal-int
pkg syscall (netbsd-arm64-cgo), const ECHONL = 16
pkg syscall (netbsd-arm64-cgo), const ECHONL ideal-int
pkg syscall (netbsd-arm64-cgo), const ECHOPRT = 32
pkg syscall (netbsd-arm64-cgo), const ECHOPRT ideal-int
pkg syscall (netbsd-arm64-cgo), const ECONNABORTED = 53
pkg syscall (netbsd-arm64-cgo), const ECONNREFUSED = 61
pkg syscall (netbsd-arm64-cgo), const ECONNRESET = 54
pkg syscall (netbsd-arm64-cgo), const EDEADLK = 11
pkg syscall (netbsd-arm64-cgo), const EDESTADDRREQ = 39
pkg syscall (netbsd-arm64-cgo), const EDOM = 33
pkg syscall (netbsd-arm64-cgo), const EDQUOT = 69
pkg syscall (netbsd-arm64-cgo), const EEXIST = 17
pkg syscall (netbsd-arm64-cgo), const EFAULT = 14
pkg syscall (netbsd-arm64-cgo), const EFBIG = 27
pkg syscall (netbsd-arm64-cgo), const EFTYPE = 79
pkg syscall (netbsd-arm64-cgo), const EFTYPE Errno
pkg syscall (netbsd-arm64-cgo), const EHOSTDOWN = 64
pkg syscall (netbsd-arm64-cgo), const EHOSTUNREACH = 65
pkg syscall (netbsd-arm64-cgo), const EIDRM = 82
pkg syscall (netbsd-arm64-cgo), const EILSEQ = 85
pkg syscall (netbsd-arm64-cgo), const EINPROGRESS = 36
pkg syscall (netbsd-arm64-cgo), const EINTR = 4
pkg syscall (netbsd-arm64-cgo), const EINVAL = 22
pkg syscall (netbsd-arm64-cgo), const EIO = 5
pkg syscall (netbsd-arm64-cgo), const EISCONN = 56
pkg syscall (netbsd-arm64-cgo), const EISDIR = 21
pkg syscall (netbsd-arm64-cgo), const ELAST = 96
pkg syscall (netbsd-arm64-cgo), const ELAST Errno
pkg syscall (netbsd-arm64-cgo), const ELOOP = 62
pkg syscall (netbsd-arm64-cgo), const EMFILE = 24
pkg syscall (netbsd-arm64-cgo), const EMLINK = 31
pkg syscall (netbsd-arm64-cgo), const EMSGSIZE = 40
pkg syscall (netbsd-arm64-cgo), const EMUL_LINUX = 1
pkg syscall (netbsd-arm64-cgo), const EMUL_LINUX32 = 5
pkg syscall (netbsd-arm64-cgo), const EMUL_LINUX32 ideal-int
pkg syscall (netbsd-arm64-cgo), const EMUL_LINUX ideal-int
pkg syscall (netbsd-arm64-cgo), const EMUL_MAXID = 6
pkg syscall (netbsd-arm64-cgo), const EMUL_MAXID ideal-int
pkg syscall (netbsd-arm64-cgo), const EMULTIHOP = 94
pkg syscall (netbsd-arm64-cgo), const EMULTIHOP Errno
pkg syscall (netbsd-arm64-cgo), const ENAMETOOLONG = 63
pkg syscall (netbsd-arm64-cgo), const ENEEDAUTH = 81
pkg syscall (netbsd-arm64-cgo), const ENEEDAUTH Errno
pkg syscall (netbsd-arm64-cgo), const ENETDOWN = 50
pkg syscall (netbsd-arm64-cgo), const ENETRESET = 52
pkg syscall (netbsd-arm64-cgo), const ENETUNREACH = 51
pkg syscall (netbsd-arm64-cgo), const ENFILE = 23
pkg syscall (netbsd-arm64-cgo), const ENOATTR = 93
pkg syscall (netbsd-arm64-cgo), const ENOATTR Errno
pkg syscall (netbsd-arm64-cgo), const ENOBUFS = 55
pkg syscall (netbsd-arm64-cgo), const ENODATA = 89
pkg syscall (netbsd-arm64-cgo), const ENODATA Errno
pkg syscall (netbsd-arm64-cgo), const ENODEV = 19
pkg syscall (netbsd-arm64-cgo), const ENOEXEC = 8
pkg syscall (netbsd-arm64-cgo), const ENOLCK = 77
pkg syscall (netbsd-arm64-cgo), const ENOLINK = 95
pkg syscall (netbsd-arm64-cgo), const ENOLINK Errno
pkg syscall (netbsd-arm64-cgo), const ENOMEM = 12
pkg syscall (netbsd-arm64-cgo), const ENOMSG = 83
pkg syscall (netbsd-arm64-cgo), const ENOPROTOOPT = 42
pkg syscall (netbsd-arm64-cgo), const ENOSPC = 28
pkg syscall (netbsd-arm64-cgo), const ENOSR = 90
pkg syscall (netbsd-arm64-cgo), const ENOSR Errno
pkg syscall (netbsd-arm64-cgo), const ENOSTR = 91
pkg syscall (netbsd-arm64-cgo), const ENOSTR Errno
pkg syscall (netbsd-arm64-cgo), const ENOSYS = 78
pkg syscall (netbsd-arm64-cgo), const ENOTBLK = 15
pkg syscall (netbsd-arm64-cgo), const ENOTCONN = 57
pkg syscall (netbsd-arm64-cgo), const ENOTDIR = 20
pkg syscall (netbsd-arm64-cgo), const ENOTEMPTY = 66
pkg syscall (netbsd-arm64-cgo), const ENOTSOCK = 38
pkg syscall (netbsd-arm64-cgo), const ENOTSUP = 86
pkg syscall (netbsd-arm64-cgo), const ENOTTY = 25
pkg syscall (netbsd-arm64-cgo), const ENXIO = 6
pkg syscall (netbsd-arm64-cgo), const EOPNOTSUPP = 45
pkg syscall (netbsd-arm64-cgo), const EOVERFLOW = 84
pkg syscall (netbsd-arm64-cgo), const EPERM = 1
pkg syscall (netbsd-arm64-cgo), const EPFNOSUPPORT = 46
pkg syscall (netbsd-arm64-cgo), const EPIPE = 32
pkg syscall (netbsd-arm64-cgo), const EPROCLIM = 67
pkg syscall (netbsd-arm64-cgo), const EPROCLIM Errno
pkg syscall (netbsd-arm64-cgo), const EPROCUNAVAIL = 76
pkg syscall (netbsd-arm64-cgo), const EPROCUNAVAIL Errno
pkg syscall (netbsd-arm64-cgo), const EPROGMISMATCH = 75
pkg syscall (netbsd-arm64-cgo), const EPROGMISMATCH Errno
pkg syscall (netbsd-arm64-cgo), const EPROGUNAVAIL = 74
pkg syscall (netbsd-arm64-cgo), const EPROGUNAVAIL Errno
pkg syscall (netbsd-arm64-cgo), const EPROTO = 96
pkg syscall (netbsd-arm64-cgo), const EPROTO Errno
pkg syscall (netbsd-arm64-cgo), const EPROTONOSUPPORT = 43
pkg syscall (netbsd-arm64-cgo), const EPROTOTYPE = 41
pkg syscall (netbsd-arm64-cgo), const ERANGE = 34
pkg syscall (netbsd-arm64-cgo), const EREMOTE = 71
pkg syscall (netbsd-arm64-cgo), const EROFS = 30
pkg syscall (netbsd-arm64-cgo), const ERPCMISMATCH = 73
pkg syscall (netbsd-arm64-cgo), const ERPCMISMATCH Errno
pkg syscall (netbsd-arm64-cgo), const ESHUTDOWN = 58
pkg syscall (netbsd-arm64-cgo), const ESOCKTNOSUPPORT = 44
pkg syscall (netbsd-arm64-cgo), const ESPIPE = 29
pkg syscall (netbsd-arm64-cgo), const ESRCH = 3
pkg syscall (netbsd-arm64-cgo), const ESTALE = 70
pkg syscall (netbsd-arm64-cgo), const ETHER_ADDR_LEN = 6
pkg syscall (netbsd-arm64-cgo), const ETHER_ADDR_LEN ideal-int
pkg syscall (netbsd-arm64-cgo), const ETHERCAP_JUMBO_MTU = 4
pkg syscall (netbsd-arm64-cgo), const ETHERCAP_JUMBO_MTU ideal-int
pkg syscall (netbsd-arm64-cgo), const ETHERCAP_VLAN_HWTAGGING = 2
pkg syscall (netbsd-arm64-cgo), const ETHERCAP_VLAN_HWTAGGING ideal-int
pkg syscall (netbsd-arm64-cgo), const ETHERCAP_VLAN_MTU = 1
pkg syscall (netbsd-arm64-cgo), const ETHERCAP_VLAN_MTU ideal-int
pkg syscall (netbsd-arm64-cgo), const ETHER_CRC_LEN = 4
pkg syscall (netbsd-arm64-cgo), const ETHER_CRC_LEN ideal-int
pkg syscall (netbsd-arm64-cgo), const ETHER_CRC_POLY_BE = 79764918
pkg syscall (netbsd-arm64-cgo), const ETHER_CRC_POLY_BE ideal-int
pkg syscall (netbsd-arm64-cgo), const ETHER_CRC_POLY_LE = 3988292384
pkg syscall (netbsd-arm64-cgo), const ETHER_CRC_POLY_LE ideal-int
pkg syscall (netbsd-arm64-cgo), const ETHER_HDR_LEN = 14
pkg syscall (netbsd-arm64-cgo), const ETHER_HDR_LEN ideal-int
pkg syscall (netbsd-arm64-cgo), const ETHER_MAX_LEN = 1518
pkg syscall (netbsd-arm64-cgo), const ETHER_MAX_LEN ideal-int
pkg syscall (netbsd-arm64-cgo), const ETHER_MAX_LEN_JUMBO = 9018
pkg syscall (netbsd-arm64-cgo), const ETHER_MAX_LEN_JUMBO ideal-int
pkg syscall (netbsd-arm64-cgo), const ETHERMIN = 46
pkg syscall (netbsd-arm64-cgo), const ETHERMIN ideal-int
pkg syscall (netbsd-arm64-cgo), const ETHER_MIN_LEN = 64
pkg syscall (netbsd-arm64-cgo), const ETHER_MIN_LEN ideal-int
pkg syscall (netbsd-arm64-cgo), const ETHERMTU = 1500
pkg syscall (netbsd-arm64-cgo), const ETHERMTU ideal-int
pkg syscall (netbsd-arm64-cgo), const ETHERMTU_JUMBO = 9000
pkg syscall (netbsd-arm64-cgo), const ETHERMTU_JUMBO ideal-int
pkg syscall (netbsd-arm64-cgo), const ETHER_PPPOE_ENCAP_LEN = 8
pkg syscall (netbsd-arm64-cgo), const ETHER_PPPOE_ENCAP_LEN ideal-int
pkg syscall (netbsd-arm64-cgo), const ETHERTYPE_8023 = 4
pkg syscall (netbsd-arm64-cgo), const ETHERTYPE_8023 ideal-int
pkg syscall (netbsd-arm64-cgo), const ETHERTYPE_AARP = 33011
pkg syscall (netbsd-arm64-cgo), const ETHERTYPE_AARP ideal-int
pkg syscall (netbsd-arm64-cgo), const ETHERTYPE_ACCTON = 33680
pkg syscall (netbsd-arm64-cgo), const ETHERTYPE_ACCTON ideal-int
pkg syscall (netbsd-arm64-cgo), const ETHERTYPE_AEONIC = 32822
pkg syscall (netbsd-arm64-cgo), const ETHERTYPE_AEONIC ideal-int
pkg syscall (netbsd-arm64-cgo), const ETHERTYPE_ALPHA = 33098
pkg syscall (netbsd-arm64-cgo), const ETHERTYPE_ALPHA ideal-int
pkg syscall (netbsd-arm64-cgo), const ETHERTYPE_AMBER = 24584
pkg syscall (netbsd-arm64-cgo), const ETHERTYPE_AMBER ideal-int
pkg syscall (netbsd-arm64-cgo), const ETHERTYPE_AMOEBA = 33093
pkg syscall (netbsd-arm64-cgo), const ETHERTYPE_AMOEBA ideal-int
pkg syscall (netbsd-arm64-cgo), const ETHERTYPE_APOLLO = 33015
pkg syscall (netbsd-arm64-cgo), const ETHERTYPE_APOLLODOMAIN = 32793
pkg syscall (netbsd-arm64-cgo), const ETHERTYPE_APOLLODOMAIN ideal-int
pkg syscall (netbsd-arm64-cgo), const ETHERTYPE_APOLLO ideal-int
pkg syscall (netbsd-arm64-cgo), const ETHERTYPE_APPLETALK = 32923
pkg syscall (netbsd-arm64-cgo), const ETHERTYPE_APPLETALK ideal-int
pkg syscall (netbsd-arm64-cgo), const ETHERTYPE_APPLITEK = 32967
pkg syscall (netbsd-arm64-cgo), const ETHERTYPE_APPLITEK ideal-int
pkg syscall (netbsd-arm64-cgo), const ETHERTYPE_ARGONAUT = 32826
pkg syscall (netbsd-arm64-cgo), const ETHERTYPE_ARGONAUT ideal-int
pkg syscall (netbsd-arm64-cgo), const ETHERTYPE_ARP = 2054
pkg syscall (netbsd-arm64-cgo), const ETHERTYPE_ARP ideal-int
pkg syscall (netbsd-arm64-cgo), const ETHERTYPE_AT = 32923
pkg syscall (netbsd-arm64-cgo), const ETHERTYPE_ATALK = 32923
pkg syscall (netbsd-arm64-cgo), const ETHERTYPE_ATALK ideal-int
pkg syscall (netbsd-arm64-cgo), const ETHERTYPE_AT ideal-int
pkg syscall (netbsd-arm64-cgo), const ETHERTYPE_ATOMIC = 34527
pkg syscall (netbsd-arm64-cgo), const ETHERTYPE_ATOMIC ideal-int
pkg syscall (netbsd-arm64-cgo), const ETHERTYPE_ATT = 32873
pkg syscall (netbsd-arm64-cgo), const ETHERTYPE_ATT ideal-int
pkg syscall (netbsd-arm64-cgo), const ETHERTYPE_ATTSTANFORD = 32776
pkg syscall (netbsd-arm64-cgo), const ETHERTYPE_ATTSTANFORD ideal-int
pkg syscall (netbsd-arm64-cgo), const ETHERTYPE_AUTOPHON = 32874
pkg syscall (netbsd-arm64-cgo), const ETHERTYPE_AUTOPHON ideal-int
pkg syscall (netbsd-arm64-cgo), const ETHERTYPE_AXIS = 34902
pkg syscall (netbsd-arm64-cgo), const ETHERTYPE_AXIS ideal-int
pkg syscall (netbsd-arm64-cgo), const ETHERTYPE_BCLOOP = 36867
pkg syscall (netbsd-arm64-cgo), const ETHERTYPE_BCLOOP ideal-int
pkg syscall (netbsd-arm64-cgo), const ETHERTYPE_BOFL = 33026
pkg syscall (netbsd-arm64-cgo), const ETHERTYPE_BOFL ideal-int
pkg syscall (netbsd-arm64-cgo), const ETHERTYPE_CABLETRON = 28724
pkg syscall (netbsd-arm64-cgo), const ETHERTYPE_CABLETRON ideal-int
pkg syscall (netbsd-arm64-cgo), const ETHERTYPE_CHAOS = 2052
pkg syscall (netbsd-arm64-cgo), const ETHERTYPE_CHAOS ideal-int
pkg syscall (netbsd-arm64-cgo), const ETHERTYPE_COMDESIGN = 32876
pkg syscall (netbsd-arm64-cgo), const ETHERTYPE_COMDESIGN ideal-int
pkg syscall (netbsd-arm64-cgo), const ETHERTYPE_COMPUGRAPHIC = 32877
pkg syscall (netbsd-arm64-cgo), const ETHERTYPE_COMPUGRAPHIC ideal-int
pkg syscall (netbsd-arm64-cgo), const ETHERTYPE_COUNTERPOINT = 32866
pkg syscall (netbsd-arm64-cgo), const ETHERTYPE_COUNTERPOINT ideal-int
pkg syscall (netbsd-arm64-cgo), const ETHERTYPE_CRONUS = 32772
pkg syscall (netbsd-arm64-cgo), const ETHERTYPE_CRONUS ideal-int
pkg syscall (netbsd-arm64-cgo), const ETHERTYPE_CRONUSVLN = 32771
pkg syscall (netbsd-arm64-cgo), const ETHERTYPE_CRONUSVLN ideal-int
pkg syscall (netbsd-arm64-cgo), const ETHERTYPE_DCA = 4660
pkg syscall (netbsd-arm64-cgo), const ETHERTYPE_DCA ideal-int
pkg syscall (netbsd-arm64-cgo), const ETHERTYPE_DDE = 32891
pkg syscall (netbsd-arm64-cgo), const ETHERTYPE_DDE ideal-int
pkg syscall (netbsd-arm64-cgo), const ETHERTYPE_DEBNI = 43690
pkg syscall (netbsd-arm64-cgo), const ETHERTYPE_DEBNI ideal-int
pkg syscall (netbsd-arm64-cgo), const ETHERTYPE_DECAM = 32840
pkg syscall (netbsd-arm64-cgo), const ETHERTYPE_DECAM ideal-int
pkg syscall (netbsd-arm64-cgo), const ETHERTYPE_DECCUST = 24582
pkg syscall (netbsd-arm64-cgo), const ETHERTYPE_DECCUST ideal-int
pkg syscall (netbsd-arm64-cgo), const ETHERTYPE_DECDIAG = 24581
pkg syscall (netbsd-arm64-cgo), const ETHERTYPE_DECDIAG ideal-int
pkg syscall (netbsd-arm64-cgo), const ETHERTYPE_DECDNS = 32828
pkg syscall (netbsd-arm64-cgo), const ETHERTYPE_DECDNS ideal-int
pkg syscall (netbsd-arm64-cgo), const ETHERTYPE_DECDTS = 32830
pkg syscall (netbsd-arm64-cgo), const ETHERTYPE_DECDTS ideal-int
pkg syscall (netbsd-arm64-cgo), const ETHERTYPE_DECEXPER = 24576
pkg syscall (netbsd-arm64-cgo), const ETHERTYPE_DECEXPER ideal-int
pkg syscall (netbsd-arm64-cgo), const ETHERTYPE_DECLAST = 32833
pkg syscall (netbsd-arm64-cgo), const ETHERTYPE_DECLAST ideal-int
pkg syscall (netbsd-arm64-cgo), const ETHERTYPE_DECLTM = 32831
pkg syscall (netbsd-arm64-cgo), const ETHERTYPE_DECLTM ideal-int
pkg syscall (netbsd-arm64-cgo), const ETHERTYPE_DECMUMPS = 24585
pkg syscall (netbsd-arm64-cgo), const ETHERTYPE_DECMUMPS ideal-int
pkg syscall (netbsd-arm64-cgo), const ETHERTYPE_DECNETBIOS = 32832
pkg syscall (netbsd-arm64-cgo), const ETHERTYPE_DECNETBIOS ideal-int
pkg syscall (netbsd-arm64-cgo), const ETHERTYPE_DELTACON = 34526
pkg syscall (netbsd-arm64-cgo), const ETHERTYPE_DELTACON ideal-int
pkg syscall (netbsd-arm64-cgo), const ETHERTYPE_DIDDLE = 17185
pkg syscall (netbsd-arm64-cgo), const ETHERTYPE_DIDDLE ideal-int
pkg syscall (netbsd-arm64-cgo), const ETHERTYPE_DLOG1 = 1632
pkg syscall (netbsd-arm64-cgo), const ETHERTYPE_DLOG1 ideal-int
pkg syscall (netbsd-arm64-cgo), const ETHERTYPE_DLOG2 = 1633
pkg syscall (netbsd-arm64-cgo), const ETHERTYPE_DLOG2 ideal-int
pkg syscall (netbsd-arm64-cgo), const ETHERTYPE_DN = 24579
pkg syscall (netbsd-arm64-cgo), const ETHERTYPE_DN ideal-int
pkg syscall (netbsd-arm64-cgo), const ETHERTYPE_DOGFIGHT = 6537
pkg syscall (netbsd-arm64-cgo), const ETHERTYPE_DOGFIGHT ideal-int
pkg syscall (netbsd-arm64-cgo), const ETHERTYPE_DSMD = 32825
pkg syscall (netbsd-arm64-cgo), const ETHERTYPE_DSMD ideal-int
pkg syscall (netbsd-arm64-cgo), const ETHERTYPE_ECMA = 2051
pkg syscall (netbsd-arm64-cgo), const ETHERTYPE_ECMA ideal-int
pkg syscall (netbsd-arm64-cgo), const ETHERTYPE_ENCRYPT = 32829
pkg syscall (netbsd-arm64-cgo), const ETHERTYPE_ENCRYPT ideal-int
pkg syscall (netbsd-arm64-cgo), const ETHERTYPE_ES = 32861
pkg syscall (netbsd-arm64-cgo), const ETHERTYPE_ES ideal-int
pkg syscall (netbsd-arm64-cgo), const ETHERTYPE_EXCELAN = 32784
pkg syscall (netbsd-arm64-cgo), const ETHERTYPE_EXCELAN ideal-int
pkg syscall (netbsd-arm64-cgo), const ETHERTYPE_EXPERDATA = 32841
pkg syscall (netbsd-arm64-cgo), const ETHERTYPE_EXPERDATA ideal-int
pkg syscall (netbsd-arm64-cgo), const ETHERTYPE_FLIP = 33094
pkg syscall (netbsd-arm64-cgo), const ETHERTYPE_FLIP ideal-int
pkg syscall (netbsd-arm64-cgo), const ETHERTYPE_FLOWCONTROL = 34824
pkg syscall (netbsd-arm64-cgo), const ETHERTYPE_FLOWCONTROL ideal-int
pkg syscall (netbsd-arm64-cgo), const ETHERTYPE_FRARP = 2056
pkg syscall (netbsd-arm64-cgo), const ETHERTYPE_FRARP ideal-int
pkg syscall (netbsd-arm64-cgo), const ETHERTYPE_GENDYN = 32872
pkg syscall (netbsd-arm64-cgo), const ETHERTYPE_GENDYN ideal-int
pkg syscall (netbsd-arm64-cgo), const ETHERTYPE_HAYES = 33072
pkg syscall (netbsd-arm64-cgo), const ETHERTYPE_HAYES ideal-int
pkg syscall (netbsd-arm64-cgo), const ETHERTYPE_HIPPI_FP = 33152
pkg syscall (netbsd-arm64-cgo), const ETHERTYPE_HIPPI_FP ideal-int
pkg syscall (netbsd-arm64-cgo), const ETHERTYPE_HITACHI = 34848
pkg syscall (netbsd-arm64-cgo), const ETHERTYPE_HITACHI ideal-int
pkg syscall (netbsd-arm64-cgo), const ETHERTYPE_HP = 32773
pkg syscall (netbsd-arm64-cgo), const ETHERTYPE_HP ideal-int
pkg syscall (netbsd-arm64-cgo), const ETHERTYPE_IEEEPUP = 2560
pkg syscall (netbsd-arm64-cgo), const ETHERTYPE_IEEEPUPAT = 2561
pkg syscall (netbsd-arm64-cgo), const ETHERTYPE_IEEEPUPAT ideal-int
pkg syscall (netbsd-arm64-cgo), const ETHERTYPE_IEEEPUP ideal-int
pkg syscall (netbsd-arm64-cgo), const ETHERTYPE_IMLBL = 19522
pkg syscall (netbsd-arm64-cgo), const ETHERTYPE_IMLBLDIAG = 16972
pkg syscall (netbsd-arm64-cgo), const ETHERTYPE_IMLBLDIAG ideal-int
pkg syscall (netbsd-arm64-cgo), const ETHERTYPE_IMLBL ideal-int
pkg syscall (netbsd-arm64-cgo), const ETHERTYPE_IP = 2048
pkg syscall (netbsd-arm64-cgo), const ETHERTYPE_IPAS = 34668
pkg syscall (netbsd-arm64-cgo), const ETHERTYPE_IPAS ideal-int
pkg syscall (netbsd-arm64-cgo), const ETHERTYPE_IP ideal-int
pkg syscall (netbsd-arm64-cgo), const ETHERTYPE_IPV6 = 34525
pkg syscall (netbsd-arm64-cgo), const ETHERTYPE_IPV6 ideal-int
pkg syscall (netbsd-arm64-cgo), const ETHERTYPE_IPX = 33079
pkg syscall (netbsd-arm64-cgo), const ETHERTYPE_IPX ideal-int
pkg syscall (netbsd-arm64-cgo), const ETHERTYPE_IPXNEW = 32823
pkg syscall (netbsd-arm64-cgo), const ETHERTYPE_IPXNEW ideal-int
pkg syscall (netbsd-arm64-cgo), const ETHERTYPE_KALPANA = 34178
pkg syscall (netbsd-arm64-cgo), const ETHERTYPE_KALPANA ideal-int
pkg syscall (netbsd-arm64-cgo), const ETHERTYPE_LANBRIDGE = 32824
pkg syscall (netbsd-arm64-cgo), const ETHERTYPE_LANBRIDGE ideal-int
pkg syscall (netbsd-arm64-cgo), const ETHERTYPE_LANPROBE = 34952
pkg syscall (netbsd-arm64-cgo), const ETHERTYPE_LANPROBE ideal-int
pkg syscall (netbsd-arm64-cgo), const ETHERTYPE_LAT = 24580
pkg syscall (netbsd-arm64-cgo), const ETHERTYPE_LAT ideal-int
pkg syscall (netbsd-arm64-cgo), const ETHERTYPE_LBACK = 36864
pkg syscall (netbsd-arm64-cgo), const ETHERTYPE_LBACK ideal-int
pkg syscall (netbsd-arm64-cgo), const ETHER_TYPE_LEN = 2
pkg syscall (netbsd-arm64-cgo), const ETHER_TYPE_LEN ideal-int
pkg syscall (netbsd-arm64-cgo), const ETHERTYPE_LITTLE = 32864
pkg syscall (netbsd-arm64-cgo), const ETHERTYPE_LITTLE ideal-int
pkg syscall (netbsd-arm64-cgo), const ETHERTYPE_LOGICRAFT = 33096
pkg syscall (netbsd-arm64-cgo), const ETHERTYPE_LOGICRAFT ideal-int
pkg syscall (netbsd-arm64-cgo), const ETHERTYPE_LOOPBACK = 36864
pkg syscall (netbsd-arm64-cgo), const ETHERTYPE_LOOPBACK ideal-int
pkg syscall (netbsd-arm64-cgo), const ETHERTYPE_MATRA = 32890
pkg syscall (netbsd-arm64-cgo), const ETHERTYPE_MATRA ideal-int
pkg syscall (netbsd-arm64-cgo), const ETHERTYPE_MAX = 65535
pkg syscall (netbsd-arm64-cgo), const ETHERTYPE_MAX ideal-int
pkg syscall (netbsd-arm64-cgo), const ETHERTYPE_MERIT = 32892
pkg syscall (netbsd-arm64-cgo), const ETHERTYPE_MERIT ideal-int
pkg syscall (netbsd-arm64-cgo), const ETHERTYPE_MICP = 34618
pkg syscall (netbsd-arm64-cgo), const ETHERTYPE_MICP ideal-int
pkg syscall (netbsd-arm64-cgo), const ETHERTYPE_MOPDL = 24577
pkg syscall (netbsd-arm64-cgo), const ETHERTYPE_MOPDL ideal-int
pkg syscall (netbsd-arm64-cgo), const ETHERTYPE_MOPRC = 24578
pkg syscall (netbsd-arm64-cgo), const ETHERTYPE_MOPRC ideal-int
pkg syscall (netbsd-arm64-cgo), const ETHERTYPE_MOTOROLA = 33165
pkg syscall (netbsd-arm64-cgo), const ETHERTYPE_MOTOROLA ideal-int
pkg syscall (netbsd-arm64-cgo), const ETHERTYPE_MPLS = 34887
pkg syscall (netbsd-arm64-cgo), const ETHERTYPE_MPLS ideal-int
pkg syscall (netbsd-arm64-cgo), const ETHERTYPE_MPLS_MCAST = 34888
pkg syscall (netbsd-arm64-cgo), const ETHERTYPE_MPLS_MCAST ideal-int
pkg syscall (netbsd-arm64-cgo), const ETHERTYPE_MUMPS = 33087
pkg syscall (netbsd-arm64-cgo), const ETHERTYPE_MUMPS ideal-int
pkg syscall (netbsd-arm64-cgo), const ETHERTYPE_NBPCC = 15364
pkg syscall (netbsd-arm64-cgo), const ETHERTYPE_NBPCC ideal-int
pkg syscall (netbsd-arm64-cgo), const ETHERTYPE_NBPCLAIM = 15369
pkg syscall (netbsd-arm64-cgo), const ETHERTYPE_NBPCLAIM ideal-int
pkg syscall (netbsd-arm64-cgo), const ETHERTYPE_NBPCLREQ = 15365
pkg syscall (netbsd-arm64-cgo), const ETHERTYPE_NBPCLREQ ideal-int
pkg syscall (netbsd-arm64-cgo), const ETHERTYPE_NBPCLRSP = 15366
pkg syscall (netbsd-arm64-cgo), const ETHERTYPE_NBPCLRSP ideal-int
pkg syscall (netbsd-arm64-cgo), const ETHERTYPE_NBPCREQ = 15362
pkg syscall (netbsd-arm64-cgo), const ETHERTYPE_NBPCREQ ideal-int
pkg syscall (netbsd-arm64-cgo), const ETHERTYPE_NBPCRSP = 15363
pkg syscall (netbsd-arm64-cgo), const ETHERTYPE_NBPCRSP ideal-int
pkg syscall (netbsd-arm64-cgo), const ETHERTYPE_NBPDG = 15367
pkg syscall (netbsd-arm64-cgo), const ETHERTYPE_NBPDGB = 15368
pkg syscall (netbsd-arm64-cgo), const ETHERTYPE_NBPDGB ideal-int
pkg syscall (netbsd-arm64-cgo), const ETHERTYPE_NBPDG ideal-int
pkg syscall (netbsd-arm64-cgo), const ETHERTYPE_NBPDLTE = 15370
pkg syscall (netbsd-arm64-cgo), const ETHERTYPE_NBPDLTE ideal-int
pkg syscall (netbsd-arm64-cgo), const ETHERTYPE_NBPRAR = 15372
pkg syscall (netbsd-arm64-cgo), const ETHERTYPE_NBPRAR ideal-int
pkg syscall (netbsd-arm64-cgo), const ETHERTYPE_NBPRAS = 15371
pkg syscall (netbsd-arm64-cgo), const ETHERTYPE_NBPRAS ideal-int
pkg syscall (netbsd-arm64-cgo), const ETHERTYPE_NBPRST = 15373
pkg syscall (netbsd-arm64-cgo), const ETHERTYPE_NBPRST ideal-int
pkg syscall (netbsd-arm64-cgo), const ETHERTYPE_NBPSCD = 15361
pkg syscall (netbsd-arm64-cgo), const ETHERTYPE_NBPSCD ideal-int
pkg syscall (netbsd-arm64-cgo), const ETHERTYPE_NBPVCD = 15360
pkg syscall (netbsd-arm64-cgo), const ETHERTYPE_NBPVCD ideal-int
pkg syscall (netbsd-arm64-cgo), const ETHERTYPE_NBS = 2050
pkg syscall (netbsd-arm64-cgo), const ETHERTYPE_NBS ideal-int
pkg syscall (netbsd-arm64-cgo), const ETHERTYPE_NCD = 33097
pkg syscall (netbsd-arm64-cgo), const ETHERTYPE_NCD ideal-int
pkg syscall (netbsd-arm64-cgo), const ETHERTYPE_NESTAR = 32774
pkg syscall (netbsd-arm64-cgo), const ETHERTYPE_NESTAR ideal-int
pkg syscall (netbsd-arm64-cgo), const ETHERTYPE_NETBEUI = 33169
pkg syscall (netbsd-arm64-cgo), const ETHERTYPE_NETBEUI ideal-int
pkg syscall (netbsd-arm64-cgo), const ETHERTYPE_NOVELL = 33080
pkg syscall (netbsd-arm64-cgo), const ETHERTYPE_NOVELL ideal-int
pkg syscall (netbsd-arm64-cgo), const ETHERTYPE_NS = 1536
pkg syscall (netbsd-arm64-cgo), const ETHERTYPE_NSAT = 1537
pkg syscall (netbsd-arm64-cgo), const ETHERTYPE_NSAT ideal-int
pkg syscall (netbsd-arm64-cgo), const ETHERTYPE_NSCOMPAT = 2055
pkg syscall (netbsd-arm64-cgo), const ETHERTYPE_NSCOMPAT ideal-int
pkg syscall (netbsd-arm64-cgo), const ETHERTYPE_NS ideal-int
pkg syscall (netbsd-arm64-cgo), const ETHERTYPE_NTRAILER = 16
pkg syscall (netbsd-arm64-cgo), const ETHERTYPE_NTRAILER ideal-int
pkg syscall (netbsd-arm64-cgo), const ETHERTYPE_OS9 = 28679
pkg syscall (netbsd-arm64-cgo), const ETHERTYPE_OS9 ideal-int
pkg syscall (netbsd-arm64-cgo), const ETHERTYPE_OS9NET = 28681
pkg syscall (netbsd-arm64-cgo), const ETHERTYPE_OS9NET ideal-int
pkg syscall (netbsd-arm64-cgo), const ETHERTYPE_PACER = 32966
pkg syscall (netbsd-arm64-cgo), const ETHERTYPE_PACER ideal-int
pkg syscall (netbsd-arm64-cgo), const ETHERTYPE_PAE = 34958
pkg syscall (netbsd-arm64-cgo), const ETHERTYPE_PAE ideal-int
pkg syscall (netbsd-arm64-cgo), const ETHERTYPE_PCS = 16962
pkg syscall (netbsd-arm64-cgo), const ETHERTYPE_PCS ideal-int
pkg syscall (netbsd-arm64-cgo), const ETHERTYPE_PLANNING = 32836
pkg syscall (netbsd-arm64-cgo), const ETHERTYPE_PLANNING ideal-int
pkg syscall (netbsd-arm64-cgo), const ETHERTYPE_PPP = 34827
pkg syscall (netbsd-arm64-cgo), const ETHERTYPE_PPP ideal-int
pkg syscall (netbsd-arm64-cgo), const ETHERTYPE_PPPOE = 34916
pkg syscall (netbsd-arm64-cgo), const ETHERTYPE_PPPOEDISC = 34915
pkg syscall (netbsd-arm64-cgo), const ETHERTYPE_PPPOEDISC ideal-int
pkg syscall (netbsd-arm64-cgo), const ETHERTYPE_PPPOE ideal-int
pkg syscall (netbsd-arm64-cgo), const ETHERTYPE_PRIMENTS = 28721
pkg syscall (netbsd-arm64-cgo), const ETHERTYPE_PRIMENTS ideal-int
pkg syscall (netbsd-arm64-cgo), const ETHERTYPE_PUP = 512
pkg syscall (netbsd-arm64-cgo), const ETHERTYPE_PUPAT = 512
pkg syscall (netbsd-arm64-cgo), const ETHERTYPE_PUPAT ideal-int
pkg syscall (netbsd-arm64-cgo), const ETHERTYPE_PUP ideal-int
pkg syscall (netbsd-arm64-cgo), const ETHERTYPE_RACAL = 28720
pkg syscall (netbsd-arm64-cgo), const ETHERTYPE_RACAL ideal-int
pkg syscall (netbsd-arm64-cgo), const ETHERTYPE_RATIONAL = 33104
pkg syscall (netbsd-arm64-cgo), const ETHERTYPE_RATIONAL ideal-int
pkg syscall (netbsd-arm64-cgo), const ETHERTYPE_RAWFR = 25945
pkg syscall (netbsd-arm64-cgo), const ETHERTYPE_RAWFR ideal-int
pkg syscall (netbsd-arm64-cgo), const ETHERTYPE_RCL = 6549
pkg syscall (netbsd-arm64-cgo), const ETHERTYPE_RCL ideal-int
pkg syscall (netbsd-arm64-cgo), const ETHERTYPE_RDP = 34617
pkg syscall (netbsd-arm64-cgo), const ETHERTYPE_RDP ideal-int
pkg syscall (netbsd-arm64-cgo), const ETHERTYPE_RETIX = 33010
pkg syscall (netbsd-arm64-cgo), const ETHERTYPE_RETIX ideal-int
pkg syscall (netbsd-arm64-cgo), const ETHERTYPE_REVARP = 32821
pkg syscall (netbsd-arm64-cgo), const ETHERTYPE_REVARP ideal-int
pkg syscall (netbsd-arm64-cgo), const ETHERTYPE_SCA = 24583
pkg syscall (netbsd-arm64-cgo), const ETHERTYPE_SCA ideal-int
pkg syscall (netbsd-arm64-cgo), const ETHERTYPE_SECTRA = 34523
pkg syscall (netbsd-arm64-cgo), const ETHERTYPE_SECTRA ideal-int
pkg syscall (netbsd-arm64-cgo), const ETHERTYPE_SECUREDATA = 34669
pkg syscall (netbsd-arm64-cgo), const ETHERTYPE_SECUREDATA ideal-int
pkg syscall (netbsd-arm64-cgo), const ETHERTYPE_SG_BOUNCE = 32790
pkg syscall (netbsd-arm64-cgo), const ETHERTYPE_SG_BOUNCE ideal-int
pkg syscall (netbsd-arm64-cgo), const ETHERTYPE_SG_DIAG = 32787
pkg syscall (netbsd-arm64-cgo), const ETHERTYPE_SG_DIAG ideal-int
pkg syscall (netbsd-arm64-cgo), const ETHERTYPE_SGITW = 33150
pkg syscall (netbsd-arm64-cgo), const ETHERTYPE_SGITW ideal-int
pkg syscall (netbsd-arm64-cgo), const ETHERTYPE_SG_NETGAMES = 32788
pkg syscall (netbsd-arm64-cgo), const ETHERTYPE_SG_NETGAMES ideal-int
pkg syscall (netbsd-arm64-cgo), const ETHERTYPE_SG_RESV = 32789
pkg syscall (netbsd-arm64-cgo), const ETHERTYPE_SG_RESV ideal-int
pkg syscall (netbsd-arm64-cgo), const ETHERTYPE_SIMNET = 21000
pkg syscall (netbsd-arm64-cgo), const ETHERTYPE_SIMNET ideal-int
pkg syscall (netbsd-arm64-cgo), const ETHERTYPE_SLOWPROTOCOLS = 34825
pkg syscall (netbsd-arm64-cgo), const ETHERTYPE_SLOWPROTOCOLS ideal-int
pkg syscall (netbsd-arm64-cgo), const ETHERTYPE_SNA = 32981
pkg syscall (netbsd-arm64-cgo), const ETHERTYPE_SNA ideal-int
pkg syscall (netbsd-arm64-cgo), const ETHERTYPE_SNMP = 33100
pkg syscall (netbsd-arm64-cgo), const ETHERTYPE_SNMP ideal-int
pkg syscall (netbsd-arm64-cgo), const ETHERTYPE_SONIX = 64245
pkg syscall (netbsd-arm64-cgo), const ETHERTYPE_SONIX ideal-int
pkg syscall (netbsd-arm64-cgo), const ETHERTYPE_SPIDER = 32927
pkg syscall (netbsd-arm64-cgo), const ETHERTYPE_SPIDER ideal-int
pkg syscall (netbsd-arm64-cgo), const ETHERTYPE_SPRITE = 1280
pkg syscall (netbsd-arm64-cgo), const ETHERTYPE_SPRITE ideal-int
pkg syscall (netbsd-arm64-cgo), const ETHERTYPE_STP = 33153
pkg syscall (netbsd-arm64-cgo), const ETHERTYPE_STP ideal-int
pkg syscall (netbsd-arm64-cgo), const ETHERTYPE_TALARIS = 33067
pkg syscall (netbsd-arm64-cgo), const ETHERTYPE_TALARIS ideal-int
pkg syscall (netbsd-arm64-cgo), const ETHERTYPE_TALARISMC = 34091
pkg syscall (netbsd-arm64-cgo), const ETHERTYPE_TALARISMC ideal-int
pkg syscall (netbsd-arm64-cgo), const ETHERTYPE_TCPCOMP = 34667
pkg syscall (netbsd-arm64-cgo), const ETHERTYPE_TCPCOMP ideal-int
pkg syscall (netbsd-arm64-cgo), const ETHERTYPE_TCPSM = 36866
pkg syscall (netbsd-arm64-cgo), const ETHERTYPE_TCPSM ideal-int
pkg syscall (netbsd-arm64-cgo), const ETHERTYPE_TEC = 33103
pkg syscall (netbsd-arm64-cgo), const ETHERTYPE_TEC ideal-int
pkg syscall (netbsd-arm64-cgo), const ETHERTYPE_TIGAN = 32815
pkg syscall (netbsd-arm64-cgo), const ETHERTYPE_TIGAN ideal-int
pkg syscall (netbsd-arm64-cgo), const ETHERTYPE_TRAIL = 4096
pkg syscall (netbsd-arm64-cgo), const ETHERTYPE_TRAIL ideal-int
pkg syscall (netbsd-arm64-cgo), const ETHERTYPE_TRANSETHER = 25944
pkg syscall (netbsd-arm64-cgo), const ETHERTYPE_TRANSETHER ideal-int
pkg syscall (netbsd-arm64-cgo), const ETHERTYPE_TYMSHARE = 32814
pkg syscall (netbsd-arm64-cgo), const ETHERTYPE_TYMSHARE ideal-int
pkg syscall (netbsd-arm64-cgo), const ETHERTYPE_UBBST = 28677
pkg syscall (netbsd-arm64-cgo), const ETHERTYPE_UBBST ideal-int
pkg syscall (netbsd-arm64-cgo), const ETHERTYPE_UBDEBUG = 2304
pkg syscall (netbsd-arm64-cgo), const ETHERTYPE_UBDEBUG ideal-int
pkg syscall (netbsd-arm64-cgo), const ETHERTYPE_UBDIAGLOOP = 28674
pkg syscall (netbsd-arm64-cgo), const ETHERTYPE_UBDIAGLOOP ideal-int
pkg syscall (netbsd-arm64-cgo), const ETHERTYPE_UBDL = 28672
pkg syscall (netbsd-arm64-cgo), const ETHERTYPE_UBDL ideal-int
pkg syscall (netbsd-arm64-cgo), const ETHERTYPE_UBNIU = 28673
pkg syscall (netbsd-arm64-cgo), const ETHERTYPE_UBNIU ideal-int
pkg syscall (netbsd-arm64-cgo), const ETHERTYPE_UBNMC = 28675
pkg syscall (netbsd-arm64-cgo), const ETHERTYPE_UBNMC ideal-int
pkg syscall (netbsd-arm64-cgo), const ETHERTYPE_VALID = 5632
pkg syscall (netbsd-arm64-cgo), const ETHERTYPE_VALID ideal-int
pkg syscall (netbsd-arm64-cgo), const ETHERTYPE_VARIAN = 32989
pkg syscall (netbsd-arm64-cgo), const ETHERTYPE_VARIAN ideal-int
pkg syscall (netbsd-arm64-cgo), const ETHERTYPE_VAXELN = 32827
pkg syscall (netbsd-arm64-cgo), const ETHERTYPE_VAXELN ideal-int
pkg syscall (netbsd-arm64-cgo), const ETHERTYPE_VEECO = 32871
pkg syscall (netbsd-arm64-cgo), const ETHERTYPE_VEECO ideal-int
pkg syscall (netbsd-arm64-cgo), const ETHERTYPE_VEXP = 32859
pkg syscall (netbsd-arm64-cgo), const ETHERTYPE_VEXP ideal-int
pkg syscall (netbsd-arm64-cgo), const ETHERTYPE_VGLAB = 33073
pkg syscall (netbsd-arm64-cgo), const ETHERTYPE_VGLAB ideal-int
pkg syscall (netbsd-arm64-cgo), const ETHERTYPE_VINES = 2989
pkg syscall (netbsd-arm64-cgo), const ETHERTYPE_VINESECHO = 2991
pkg syscall (netbsd-arm64-cgo), const ETHERTYPE_VINESECHO ideal-int
pkg syscall (netbsd-arm64-cgo), const ETHERTYPE_VINES ideal-int
pkg syscall (netbsd-arm64-cgo), const ETHERTYPE_VINESLOOP = 2990
pkg syscall (netbsd-arm64-cgo), const ETHERTYPE_VINESLOOP ideal-int
pkg syscall (netbsd-arm64-cgo), const ETHERTYPE_VITAL = 65280
pkg syscall (netbsd-arm64-cgo), const ETHERTYPE_VITAL ideal-int
pkg syscall (netbsd-arm64-cgo), const ETHERTYPE_VLAN = 33024
pkg syscall (netbsd-arm64-cgo), const ETHERTYPE_VLAN ideal-int
pkg syscall (netbsd-arm64-cgo), const ETHERTYPE_VLTLMAN = 32896
pkg syscall (netbsd-arm64-cgo), const ETHERTYPE_VLTLMAN ideal-int
pkg syscall (netbsd-arm64-cgo), const ETHERTYPE_VPROD = 32860
pkg syscall (netbsd-arm64-cgo), const ETHERTYPE_VPROD ideal-int
pkg syscall (netbsd-arm64-cgo), const ETHERTYPE_VURESERVED = 33095
pkg syscall (netbsd-arm64-cgo), const ETHERTYPE_VURESERVED ideal-int
pkg syscall (netbsd-arm64-cgo), const ETHERTYPE_WATERLOO = 33072
pkg syscall (netbsd-arm64-cgo), const ETHERTYPE_WATERLOO ideal-int
pkg syscall (netbsd-arm64-cgo), const ETHERTYPE_WELLFLEET = 33027
pkg syscall (netbsd-arm64-cgo), const ETHERTYPE_WELLFLEET ideal-int
pkg syscall (netbsd-arm64-cgo), const ETHERTYPE_X25 = 2053
pkg syscall (netbsd-arm64-cgo), const ETHERTYPE_X25 ideal-int
pkg syscall (netbsd-arm64-cgo), const ETHERTYPE_X75 = 2049
pkg syscall (netbsd-arm64-cgo), const ETHERTYPE_X75 ideal-int
pkg syscall (netbsd-arm64-cgo), const ETHERTYPE_XNSSM = 36865
pkg syscall (netbsd-arm64-cgo), const ETHERTYPE_XNSSM ideal-int
pkg syscall (netbsd-arm64-cgo), const ETHERTYPE_XTP = 33149
pkg syscall (netbsd-arm64-cgo), const ETHERTYPE_XTP ideal-int
pkg syscall (netbsd-arm64-cgo), const ETHER_VLAN_ENCAP_LEN = 4
pkg syscall (netbsd-arm64-cgo), const ETHER_VLAN_ENCAP_LEN ideal-int
pkg syscall (netbsd-arm64-cgo), const ETIME = 92
pkg syscall (netbsd-arm64-cgo), const ETIMEDOUT = 60
pkg syscall (netbsd-arm64-cgo), const ETIME Errno
pkg syscall (netbsd-arm64-cgo), const ETOOMANYREFS = 59
pkg syscall (netbsd-arm64-cgo), const ETXTBSY = 26
pkg syscall (netbsd-arm64-cgo), const EUSERS = 68
pkg syscall (netbsd-arm64-cgo), const EV_ADD = 1
pkg syscall (netbsd-arm64-cgo), const EV_ADD ideal-int
pkg syscall (netbsd-arm64-cgo), const EV_CLEAR = 32
pkg syscall (netbsd-arm64-cgo), const EV_CLEAR ideal-int
pkg syscall (netbsd-arm64-cgo), const EV_DELETE = 2
pkg syscall (netbsd-arm64-cgo), const EV_DELETE ideal-int
pkg syscall (netbsd-arm64-cgo), const EV_DISABLE = 8
pkg syscall (netbsd-arm64-cgo), const EV_DISABLE ideal-int
pkg syscall (netbsd-arm64-cgo), const EV_ENABLE = 4
pkg syscall (netbsd-arm64-cgo), const EV_ENABLE ideal-int
pkg syscall (netbsd-arm64-cgo), const EV_EOF = 32768
pkg syscall (netbsd-arm64-cgo), const EV_EOF ideal-int
pkg syscall (netbsd-arm64-cgo), const EV_ERROR = 16384
pkg syscall (netbsd-arm64-cgo), const EV_ERROR ideal-int
pkg syscall (netbsd-arm64-cgo), const EVFILT_AIO = 2
pkg syscall (netbsd-arm64-cgo), const EVFILT_AIO ideal-int
pkg syscall (netbsd-arm64-cgo), const EVFILT_PROC = 4
pkg syscall (netbsd-arm64-cgo), const EVFILT_PROC ideal-int
pkg syscall (netbsd-arm64-cgo), const EVFILT_READ = 0
pkg syscall (netbsd-arm64-cgo), const EVFILT_READ ideal-int
pkg syscall (netbsd-arm64-cgo), const EVFILT_SIGNAL = 5
pkg syscall (netbsd-arm64-cgo), const EVFILT_SIGNAL ideal-int
pkg syscall (netbsd-arm64-cgo), const EVFILT_SYSCOUNT = 7
pkg syscall (netbsd-arm64-cgo), const EVFILT_SYSCOUNT ideal-int
pkg syscall (netbsd-arm64-cgo), const EVFILT_TIMER = 6
pkg syscall (netbsd-arm64-cgo), const EVFILT_TIMER ideal-int
pkg syscall (netbsd-arm64-cgo), const EVFILT_VNODE = 3
pkg syscall (netbsd-arm64-cgo), const EVFILT_VNODE ideal-int
pkg syscall (netbsd-arm64-cgo), const EVFILT_WRITE = 1
pkg syscall (netbsd-arm64-cgo), const EVFILT_WRITE ideal-int
pkg syscall (netbsd-arm64-cgo), const EV_FLAG1 = 8192
pkg syscall (netbsd-arm64-cgo), const EV_FLAG1 ideal-int
pkg syscall (netbsd-arm64-cgo), const EV_ONESHOT = 16
pkg syscall (netbsd-arm64-cgo), const EV_ONESHOT ideal-int
pkg syscall (netbsd-arm64-cgo), const EV_SYSFLAGS = 61440
pkg syscall (netbsd-arm64-cgo), const EV_SYSFLAGS ideal-int
pkg syscall (netbsd-arm64-cgo), const EWOULDBLOCK = 35
pkg syscall (netbsd-arm64-cgo), const EXDEV = 18
pkg syscall (netbsd-arm64-cgo), const EXTA = 19200
pkg syscall (netbsd-arm64-cgo), const EXTA ideal-int
pkg syscall (netbsd-arm64-cgo), const EXTB = 38400
pkg syscall (netbsd-arm64-cgo), const EXTB ideal-int
pkg syscall (netbsd-arm64-cgo), const EXTPROC = 2048
pkg syscall (netbsd-arm64-cgo), const EXTPROC ideal-int
pkg syscall (netbsd-arm64-cgo), const F_CLOSEM = 10
pkg syscall (netbsd-arm64-cgo), const F_CLOSEM ideal-int
pkg syscall (netbsd-arm64-cgo), const FD_CLOEXEC = 1
pkg syscall (netbsd-arm64-cgo), const FD_CLOEXEC ideal-int
pkg syscall (netbsd-arm64-cgo), const FD_SETSIZE = 256
pkg syscall (netbsd-arm64-cgo), const FD_SETSIZE ideal-int
pkg syscall (netbsd-arm64-cgo), const F_DUPFD = 0
pkg syscall (netbsd-arm64-cgo), const F_DUPFD_CLOEXEC = 12
pkg syscall (netbsd-arm64-cgo), const F_DUPFD_CLOEXEC ideal-int
pkg syscall (netbsd-arm64-cgo), const F_DUPFD ideal-int
pkg syscall (netbsd-arm64-cgo), const F_FSCTL = -2147483648
pkg syscall (netbsd-arm64-cgo), const F_FSCTL ideal-int
pkg syscall (netbsd-arm64-cgo), const F_FSDIRMASK = 1879048192
pkg syscall (netbsd-arm64-cgo), const F_FSDIRMASK ideal-int
pkg syscall (netbsd-arm64-cgo), const F_FSIN = 268435456
pkg syscall (netbsd-arm64-cgo), const F_FSIN ideal-int
pkg syscall (netbsd-arm64-cgo), const F_FSINOUT = 805306368
pkg syscall (netbsd-arm64-cgo), const F_FSINOUT ideal-int
pkg syscall (netbsd-arm64-cgo), const F_FSOUT = 536870912
pkg syscall (netbsd-arm64-cgo), const F_FSOUT ideal-int
pkg syscall (netbsd-arm64-cgo), const F_FSPRIV = 32768
pkg syscall (netbsd-arm64-cgo), const F_FSPRIV ideal-int
pkg syscall (netbsd-arm64-cgo), const F_FSVOID = 1073741824
pkg syscall (netbsd-arm64-cgo), const F_FSVOID ideal-int
pkg syscall (netbsd-arm64-cgo), const F_GETFD = 1
pkg syscall (netbsd-arm64-cgo), const F_GETFD ideal-int
pkg syscall (netbsd-arm64-cgo), const F_GETFL = 3
pkg syscall (netbsd-arm64-cgo), const F_GETFL ideal-int
pkg syscall (netbsd-arm64-cgo), const F_GETLK = 7
pkg syscall (netbsd-arm64-cgo), const F_GETLK ideal-int
pkg syscall (netbsd-arm64-cgo), const F_GETNOSIGPIPE = 13
pkg syscall (netbsd-arm64-cgo), const F_GETNOSIGPIPE ideal-int
pkg syscall (netbsd-arm64-cgo), const F_GETOWN = 5
pkg syscall (netbsd-arm64-cgo), const F_GETOWN ideal-int
pkg syscall (netbsd-arm64-cgo), const FLUSHO = 8388608
pkg syscall (netbsd-arm64-cgo), const FLUSHO ideal-int
pkg syscall (netbsd-arm64-cgo), const F_MAXFD = 11
pkg syscall (netbsd-arm64-cgo), const F_MAXFD ideal-int
pkg syscall (netbsd-arm64-cgo), const F_OK = 0
pkg syscall (netbsd-arm64-cgo), const F_OK ideal-int
pkg syscall (netbsd-arm64-cgo), const F_PARAM_MASK = 4095
pkg syscall (netbsd-arm64-cgo), const F_PARAM_MASK ideal-int
pkg syscall (netbsd-arm64-cgo), const F_PARAM_MAX = 4095
pkg syscall (netbsd-arm64-cgo), const F_PARAM_MAX ideal-int
pkg syscall (netbsd-arm64-cgo), const F_RDLCK = 1
pkg syscall (netbsd-arm64-cgo), const F_RDLCK ideal-int
pkg syscall (netbsd-arm64-cgo), const F_SETFD = 2
pkg syscall (netbsd-arm64-cgo), const F_SETFD ideal-int
pkg syscall (netbsd-arm64-cgo), const F_SETFL = 4
pkg syscall (netbsd-arm64-cgo), const F_SETFL ideal-int
pkg syscall (netbsd-arm64-cgo), const F_SETLK = 8
pkg syscall (netbsd-arm64-cgo), const F_SETLK ideal-int
pkg syscall (netbsd-arm64-cgo), const F_SETLKW = 9
pkg syscall (netbsd-arm64-cgo), const F_SETLKW ideal-int
pkg syscall (netbsd-arm64-cgo), const F_SETNOSIGPIPE = 14
pkg syscall (netbsd-arm64-cgo), const F_SETNOSIGPIPE ideal-int
pkg syscall (netbsd-arm64-cgo), const F_SETOWN = 6
pkg syscall (netbsd-arm64-cgo), const F_SETOWN ideal-int
pkg syscall (netbsd-arm64-cgo), const F_UNLCK = 2
pkg syscall (netbsd-arm64-cgo), const F_UNLCK ideal-int
pkg syscall (netbsd-arm64-cgo), const F_WRLCK = 3
pkg syscall (netbsd-arm64-cgo), const F_WRLCK ideal-int
pkg syscall (netbsd-arm64-cgo), const HUPCL = 16384
pkg syscall (netbsd-arm64-cgo), const HUPCL ideal-int
pkg syscall (netbsd-arm64-cgo), const ICANON = 256
pkg syscall (netbsd-arm64-cgo), const ICANON ideal-int
pkg syscall (netbsd-arm64-cgo), const ICMP6_FILTER = 18
pkg syscall (netbsd-arm64-cgo), const ICMP6_FILTER ideal-int
pkg syscall (netbsd-arm64-cgo), const ICRNL = 256
pkg syscall (netbsd-arm64-cgo), const ICRNL ideal-int
pkg syscall (netbsd-arm64-cgo), const IEXTEN = 1024
pkg syscall (netbsd-arm64-cgo), const IEXTEN ideal-int
pkg syscall (netbsd-arm64-cgo), const IFAN_ARRIVAL = 0
pkg syscall (netbsd-arm64-cgo), const IFAN_ARRIVAL ideal-int
pkg syscall (netbsd-arm64-cgo), const IFAN_DEPARTURE = 1
pkg syscall (netbsd-arm64-cgo), const IFAN_DEPARTURE ideal-int
pkg syscall (netbsd-arm64-cgo), const IFA_ROUTE = 1
pkg syscall (netbsd-arm64-cgo), const IFA_ROUTE ideal-int
pkg syscall (netbsd-arm64-cgo), const IFF_ALLMULTI = 512
pkg syscall (netbsd-arm64-cgo), const IFF_ALLMULTI ideal-int
pkg syscall (netbsd-arm64-cgo), const IFF_CANTCHANGE = 36690
pkg syscall (netbsd-arm64-cgo), const IFF_CANTCHANGE ideal-int
pkg syscall (netbsd-arm64-cgo), const IFF_DEBUG = 4
pkg syscall (netbsd-arm64-cgo), const IFF_DEBUG ideal-int
pkg syscall (netbsd-arm64-cgo), const IFF_LINK0 = 4096
pkg syscall (netbsd-arm64-cgo), const IFF_LINK0 ideal-int
pkg syscall (netbsd-arm64-cgo), const IFF_LINK1 = 8192
pkg syscall (netbsd-arm64-cgo), const IFF_LINK1 ideal-int
pkg syscall (netbsd-arm64-cgo), const IFF_LINK2 = 16384
pkg syscall (netbsd-arm64-cgo), const IFF_LINK2 ideal-int
pkg syscall (netbsd-arm64-cgo), const IFF_LOOPBACK = 8
pkg syscall (netbsd-arm64-cgo), const IFF_MULTICAST = 32768
pkg syscall (netbsd-arm64-cgo), const IFF_NOARP = 128
pkg syscall (netbsd-arm64-cgo), const IFF_NOARP ideal-int
pkg syscall (netbsd-arm64-cgo), const IFF_NOTRAILERS = 32
pkg syscall (netbsd-arm64-cgo), const IFF_NOTRAILERS ideal-int
pkg syscall (netbsd-arm64-cgo), const IFF_OACTIVE = 1024
pkg syscall (netbsd-arm64-cgo), const IFF_OACTIVE ideal-int
pkg syscall (netbsd-arm64-cgo), const IFF_POINTOPOINT = 16
pkg syscall (netbsd-arm64-cgo), const IFF_POINTOPOINT ideal-int
pkg syscall (netbsd-arm64-cgo), const IFF_PROMISC = 256
pkg syscall (netbsd-arm64-cgo), const IFF_PROMISC ideal-int
pkg syscall (netbsd-arm64-cgo), const IFF_RUNNING = 64
pkg syscall (netbsd-arm64-cgo), const IFF_RUNNING ideal-int
pkg syscall (netbsd-arm64-cgo), const IFF_SIMPLEX = 2048
pkg syscall (netbsd-arm64-cgo), const IFF_SIMPLEX ideal-int
pkg syscall (netbsd-arm64-cgo), const IFNAMSIZ = 16
pkg syscall (netbsd-arm64-cgo), const IFNAMSIZ ideal-int
pkg syscall (netbsd-arm64-cgo), const IFT_1822 = 2
pkg syscall (netbsd-arm64-cgo), const IFT_1822 ideal-int
pkg syscall (netbsd-arm64-cgo), const IFT_A12MPPSWITCH = 130
pkg syscall (netbsd-arm64-cgo), const IFT_A12MPPSWITCH ideal-int
pkg syscall (netbsd-arm64-cgo), const IFT_AAL2 = 187
pkg syscall (netbsd-arm64-cgo), const IFT_AAL2 ideal-int
pkg syscall (netbsd-arm64-cgo), const IFT_AAL5 = 49
pkg syscall (netbsd-arm64-cgo), const IFT_AAL5 ideal-int
pkg syscall (netbsd-arm64-cgo), const IFT_ADSL = 94
pkg syscall (netbsd-arm64-cgo), const IFT_ADSL ideal-int
pkg syscall (netbsd-arm64-cgo), const IFT_AFLANE8023 = 59
pkg syscall (netbsd-arm64-cgo), const IFT_AFLANE8023 ideal-int
pkg syscall (netbsd-arm64-cgo), const IFT_AFLANE8025 = 60
pkg syscall (netbsd-arm64-cgo), const IFT_AFLANE8025 ideal-int
pkg syscall (netbsd-arm64-cgo), const IFT_ARAP = 88
pkg syscall (netbsd-arm64-cgo), const IFT_ARAP ideal-int
pkg syscall (netbsd-arm64-cgo), const IFT_ARCNET = 35
pkg syscall (netbsd-arm64-cgo), const IFT_ARCNET ideal-int
pkg syscall (netbsd-arm64-cgo), const IFT_ARCNETPLUS = 36
pkg syscall (netbsd-arm64-cgo), const IFT_ARCNETPLUS ideal-int
pkg syscall (netbsd-arm64-cgo), const IFT_ASYNC = 84
pkg syscall (netbsd-arm64-cgo), const IFT_ASYNC ideal-int
pkg syscall (netbsd-arm64-cgo), const IFT_ATM = 37
pkg syscall (netbsd-arm64-cgo), const IFT_ATMDXI = 105
pkg syscall (netbsd-arm64-cgo), const IFT_ATMDXI ideal-int
pkg syscall (netbsd-arm64-cgo), const IFT_ATMFUNI = 106
pkg syscall (netbsd-arm64-cgo), const IFT_ATMFUNI ideal-int
pkg syscall (netbsd-arm64-cgo), const IFT_ATM ideal-int
pkg syscall (netbsd-arm64-cgo), const IFT_ATMIMA = 107
pkg syscall (netbsd-arm64-cgo), const IFT_ATMIMA ideal-int
pkg syscall (netbsd-arm64-cgo), const IFT_ATMLOGICAL = 80
pkg syscall (netbsd-arm64-cgo), const IFT_ATMLOGICAL ideal-int
pkg syscall (netbsd-arm64-cgo), const IFT_ATMRADIO = 189
pkg syscall (netbsd-arm64-cgo), const IFT_ATMRADIO ideal-int
pkg syscall (netbsd-arm64-cgo), const IFT_ATMSUBINTERFACE = 134
pkg syscall (netbsd-arm64-cgo), const IFT_ATMSUBINTERFACE ideal-int
pkg syscall (netbsd-arm64-cgo), const IFT_ATMVCIENDPT = 194
pkg syscall (netbsd-arm64-cgo), const IFT_ATMVCIENDPT ideal-int
pkg syscall (netbsd-arm64-cgo), const IFT_ATMVIRTUAL = 149
pkg syscall (netbsd-arm64-cgo), const IFT_ATMVIRTUAL ideal-int
pkg syscall (netbsd-arm64-cgo), const IFT_BGPPOLICYACCOUNTING = 162
pkg syscall (netbsd-arm64-cgo), const IFT_BGPPOLICYACCOUNTING ideal-int
pkg syscall (netbsd-arm64-cgo), const IFT_BRIDGE = 209
pkg syscall (netbsd-arm64-cgo), const IFT_BRIDGE ideal-int
pkg syscall (netbsd-arm64-cgo), const IFT_BSC = 83
pkg syscall (netbsd-arm64-cgo), const IFT_BSC ideal-int
pkg syscall (netbsd-arm64-cgo), const IFT_CARP = 248
pkg syscall (netbsd-arm64-cgo), const IFT_CARP ideal-int
pkg syscall (netbsd-arm64-cgo), const IFT_CCTEMUL = 61
pkg syscall (netbsd-arm64-cgo), const IFT_CCTEMUL ideal-int
pkg syscall (netbsd-arm64-cgo), const IFT_CEPT = 19
pkg syscall (netbsd-arm64-cgo), const IFT_CEPT ideal-int
pkg syscall (netbsd-arm64-cgo), const IFT_CES = 133
pkg syscall (netbsd-arm64-cgo), const IFT_CES ideal-int
pkg syscall (netbsd-arm64-cgo), const IFT_CHANNEL = 70
pkg syscall (netbsd-arm64-cgo), const IFT_CHANNEL ideal-int
pkg syscall (netbsd-arm64-cgo), const IFT_CNR = 85
pkg syscall (netbsd-arm64-cgo), const IFT_CNR ideal-int
pkg syscall (netbsd-arm64-cgo), const IFT_COFFEE = 132
pkg syscall (netbsd-arm64-cgo), const IFT_COFFEE ideal-int
pkg syscall (netbsd-arm64-cgo), const IFT_COMPOSITELINK = 155
pkg syscall (netbsd-arm64-cgo), const IFT_COMPOSITELINK ideal-int
pkg syscall (netbsd-arm64-cgo), const IFT_DCN = 141
pkg syscall (netbsd-arm64-cgo), const IFT_DCN ideal-int
pkg syscall (netbsd-arm64-cgo), const IFT_DIGITALPOWERLINE = 138
pkg syscall (netbsd-arm64-cgo), const IFT_DIGITALPOWERLINE ideal-int
pkg syscall (netbsd-arm64-cgo), const IFT_DIGITALWRAPPEROVERHEADCHANNEL = 186
pkg syscall (netbsd-arm64-cgo), const IFT_DIGITALWRAPPEROVERHEADCHANNEL ideal-int
pkg syscall (netbsd-arm64-cgo), const IFT_DLSW = 74
pkg syscall (netbsd-arm64-cgo), const IFT_DLSW ideal-int
pkg syscall (netbsd-arm64-cgo), const IFT_DOCSCABLEDOWNSTREAM = 128
pkg syscall (netbsd-arm64-cgo), const IFT_DOCSCABLEDOWNSTREAM ideal-int
pkg syscall (netbsd-arm64-cgo), const IFT_DOCSCABLEMACLAYER = 127
pkg syscall (netbsd-arm64-cgo), const IFT_DOCSCABLEMACLAYER ideal-int
pkg syscall (netbsd-arm64-cgo), const IFT_DOCSCABLEUPSTREAM = 129
pkg syscall (netbsd-arm64-cgo), const IFT_DOCSCABLEUPSTREAMCHANNEL = 205
pkg syscall (netbsd-arm64-cgo), const IFT_DOCSCABLEUPSTREAMCHANNEL ideal-int
pkg syscall (netbsd-arm64-cgo), const IFT_DOCSCABLEUPSTREAM ideal-int
pkg syscall (netbsd-arm64-cgo), const IFT_DS0 = 81
pkg syscall (netbsd-arm64-cgo), const IFT_DS0BUNDLE = 82
pkg syscall (netbsd-arm64-cgo), const IFT_DS0BUNDLE ideal-int
pkg syscall (netbsd-arm64-cgo), const IFT_DS0 ideal-int
pkg syscall (netbsd-arm64-cgo), const IFT_DS1FDL = 170
pkg syscall (netbsd-arm64-cgo), const IFT_DS1FDL ideal-int
pkg syscall (netbsd-arm64-cgo), const IFT_DS3 = 30
pkg syscall (netbsd-arm64-cgo), const IFT_DS3 ideal-int
pkg syscall (netbsd-arm64-cgo), const IFT_DTM = 140
pkg syscall (netbsd-arm64-cgo), const IFT_DTM ideal-int
pkg syscall (netbsd-arm64-cgo), const IFT_DVBASILN = 172
pkg syscall (netbsd-arm64-cgo), const IFT_DVBASILN ideal-int
pkg syscall (netbsd-arm64-cgo), const IFT_DVBASIOUT = 173
pkg syscall (netbsd-arm64-cgo), const IFT_DVBASIOUT ideal-int
pkg syscall (netbsd-arm64-cgo), const IFT_DVBRCCDOWNSTREAM = 147
pkg syscall (netbsd-arm64-cgo), const IFT_DVBRCCDOWNSTREAM ideal-int
pkg syscall (netbsd-arm64-cgo), const IFT_DVBRCCMACLAYER = 146
pkg syscall (netbsd-arm64-cgo), const IFT_DVBRCCMACLAYER ideal-int
pkg syscall (netbsd-arm64-cgo), const IFT_DVBRCCUPSTREAM = 148
pkg syscall (netbsd-arm64-cgo), const IFT_DVBRCCUPSTREAM ideal-int
pkg syscall (netbsd-arm64-cgo), const IFT_ECONET = 206
pkg syscall (netbsd-arm64-cgo), const IFT_ECONET ideal-int
pkg syscall (netbsd-arm64-cgo), const IFT_EON = 25
pkg syscall (netbsd-arm64-cgo), const IFT_EON ideal-int
pkg syscall (netbsd-arm64-cgo), const IFT_EPLRS = 87
pkg syscall (netbsd-arm64-cgo), const IFT_EPLRS ideal-int
pkg syscall (netbsd-arm64-cgo), const IFT_ESCON = 73
pkg syscall (netbsd-arm64-cgo), const IFT_ESCON ideal-int
pkg syscall (netbsd-arm64-cgo), const IFT_ETHER = 6
pkg syscall (netbsd-arm64-cgo), const IFT_ETHER ideal-int
pkg syscall (netbsd-arm64-cgo), const IFT_FAITH = 242
pkg syscall (netbsd-arm64-cgo), const IFT_FAITH ideal-int
pkg syscall (netbsd-arm64-cgo), const IFT_FAST = 125
pkg syscall (netbsd-arm64-cgo), const IFT_FASTETHER = 62
pkg syscall (netbsd-arm64-cgo), const IFT_FASTETHERFX = 69
pkg syscall (netbsd-arm64-cgo), const IFT_FASTETHERFX ideal-int
pkg syscall (netbsd-arm64-cgo), const IFT_FASTETHER ideal-int
pkg syscall (netbsd-arm64-cgo), const IFT_FAST ideal-int
pkg syscall (netbsd-arm64-cgo), const IFT_FDDI = 15
pkg syscall (netbsd-arm64-cgo), const IFT_FDDI ideal-int
pkg syscall (netbsd-arm64-cgo), const IFT_FIBRECHANNEL = 56
pkg syscall (netbsd-arm64-cgo), const IFT_FIBRECHANNEL ideal-int
pkg syscall (netbsd-arm64-cgo), const IFT_FRAMERELAYINTERCONNECT = 58
pkg syscall (netbsd-arm64-cgo), const IFT_FRAMERELAYINTERCONNECT ideal-int
pkg syscall (netbsd-arm64-cgo), const IFT_FRAMERELAYMPI = 92
pkg syscall (netbsd-arm64-cgo), const IFT_FRAMERELAYMPI ideal-int
pkg syscall (netbsd-arm64-cgo), const IFT_FRDLCIENDPT = 193
pkg syscall (netbsd-arm64-cgo), const IFT_FRDLCIENDPT ideal-int
pkg syscall (netbsd-arm64-cgo), const IFT_FRELAY = 32
pkg syscall (netbsd-arm64-cgo), const IFT_FRELAYDCE = 44
pkg syscall (netbsd-arm64-cgo), const IFT_FRELAYDCE ideal-int
pkg syscall (netbsd-arm64-cgo), const IFT_FRELAY ideal-int
pkg syscall (netbsd-arm64-cgo), const IFT_FRF16MFRBUNDLE = 163
pkg syscall (netbsd-arm64-cgo), const IFT_FRF16MFRBUNDLE ideal-int
pkg syscall (netbsd-arm64-cgo), const IFT_FRFORWARD = 158
pkg syscall (netbsd-arm64-cgo), const IFT_FRFORWARD ideal-int
pkg syscall (netbsd-arm64-cgo), const IFT_G703AT2MB = 67
pkg syscall (netbsd-arm64-cgo), const IFT_G703AT2MB ideal-int
pkg syscall (netbsd-arm64-cgo), const IFT_G703AT64K = 66
pkg syscall (netbsd-arm64-cgo), const IFT_G703AT64K ideal-int
pkg syscall (netbsd-arm64-cgo), const IFT_GIF = 240
pkg syscall (netbsd-arm64-cgo), const IFT_GIF ideal-int
pkg syscall (netbsd-arm64-cgo), const IFT_GIGABITETHERNET = 117
pkg syscall (netbsd-arm64-cgo), const IFT_GIGABITETHERNET ideal-int
pkg syscall (netbsd-arm64-cgo), const IFT_GR303IDT = 178
pkg syscall (netbsd-arm64-cgo), const IFT_GR303IDT ideal-int
pkg syscall (netbsd-arm64-cgo), const IFT_GR303RDT = 177
pkg syscall (netbsd-arm64-cgo), const IFT_GR303RDT ideal-int
pkg syscall (netbsd-arm64-cgo), const IFT_H323GATEKEEPER = 164
pkg syscall (netbsd-arm64-cgo), const IFT_H323GATEKEEPER ideal-int
pkg syscall (netbsd-arm64-cgo), const IFT_H323PROXY = 165
pkg syscall (netbsd-arm64-cgo), const IFT_H323PROXY ideal-int
pkg syscall (netbsd-arm64-cgo), const IFT_HDH1822 = 3
pkg syscall (netbsd-arm64-cgo), const IFT_HDH1822 ideal-int
pkg syscall (netbsd-arm64-cgo), const IFT_HDLC = 118
pkg syscall (netbsd-arm64-cgo), const IFT_HDLC ideal-int
pkg syscall (netbsd-arm64-cgo), const IFT_HDSL2 = 168
pkg syscall (netbsd-arm64-cgo), const IFT_HDSL2 ideal-int
pkg syscall (netbsd-arm64-cgo), const IFT_HIPERLAN2 = 183
pkg syscall (netbsd-arm64-cgo), const IFT_HIPERLAN2 ideal-int
pkg syscall (netbsd-arm64-cgo), const IFT_HIPPI = 47
pkg syscall (netbsd-arm64-cgo), const IFT_HIPPI ideal-int
pkg syscall (netbsd-arm64-cgo), const IFT_HIPPIINTERFACE = 57
pkg syscall (netbsd-arm64-cgo), const IFT_HIPPIINTERFACE ideal-int
pkg syscall (netbsd-arm64-cgo), const IFT_HOSTPAD = 90
pkg syscall (netbsd-arm64-cgo), const IFT_HOSTPAD ideal-int
pkg syscall (netbsd-arm64-cgo), const IFT_HSSI = 46
pkg syscall (netbsd-arm64-cgo), const IFT_HSSI ideal-int
pkg syscall (netbsd-arm64-cgo), const IFT_HY = 14
pkg syscall (netbsd-arm64-cgo), const IFT_HY ideal-int
pkg syscall (netbsd-arm64-cgo), const IFT_IBM370PARCHAN = 72
pkg syscall (netbsd-arm64-cgo), const IFT_IBM370PARCHAN ideal-int
pkg syscall (netbsd-arm64-cgo), const IFT_IDSL = 154
pkg syscall (netbsd-arm64-cgo), const IFT_IDSL ideal-int
pkg syscall (netbsd-arm64-cgo), const IFT_IEEE1394 = 144
pkg syscall (netbsd-arm64-cgo), const IFT_IEEE1394 ideal-int
pkg syscall (netbsd-arm64-cgo), const IFT_IEEE80211 = 71
pkg syscall (netbsd-arm64-cgo), const IFT_IEEE80211 ideal-int
pkg syscall (netbsd-arm64-cgo), const IFT_IEEE80212 = 55
pkg syscall (netbsd-arm64-cgo), const IFT_IEEE80212 ideal-int
pkg syscall (netbsd-arm64-cgo), const IFT_IEEE8023ADLAG = 161
pkg syscall (netbsd-arm64-cgo), const IFT_IEEE8023ADLAG ideal-int
pkg syscall (netbsd-arm64-cgo), const IFT_IFGSN = 145
pkg syscall (netbsd-arm64-cgo), const IFT_IFGSN ideal-int
pkg syscall (netbsd-arm64-cgo), const IFT_IMT = 190
pkg syscall (netbsd-arm64-cgo), const IFT_IMT ideal-int
pkg syscall (netbsd-arm64-cgo), const IFT_INFINIBAND = 199
pkg syscall (netbsd-arm64-cgo), const IFT_INFINIBAND ideal-int
pkg syscall (netbsd-arm64-cgo), const IFT_INTERLEAVE = 124
pkg syscall (netbsd-arm64-cgo), const IFT_INTERLEAVE ideal-int
pkg syscall (netbsd-arm64-cgo), const IFT_IP = 126
pkg syscall (netbsd-arm64-cgo), const IFT_IPFORWARD = 142
pkg syscall (netbsd-arm64-cgo), const IFT_IPFORWARD ideal-int
pkg syscall (netbsd-arm64-cgo), const IFT_IP ideal-int
pkg syscall (netbsd-arm64-cgo), const IFT_IPOVERATM = 114
pkg syscall (netbsd-arm64-cgo), const IFT_IPOVERATM ideal-int
pkg syscall (netbsd-arm64-cgo), const IFT_IPOVERCDLC = 109
pkg syscall (netbsd-arm64-cgo), const IFT_IPOVERCDLC ideal-int
pkg syscall (netbsd-arm64-cgo), const IFT_IPOVERCLAW = 110
pkg syscall (netbsd-arm64-cgo), const IFT_IPOVERCLAW ideal-int
pkg syscall (netbsd-arm64-cgo), const IFT_IPSWITCH = 78
pkg syscall (netbsd-arm64-cgo), const IFT_IPSWITCH ideal-int
pkg syscall (netbsd-arm64-cgo), const IFT_ISDN = 63
pkg syscall (netbsd-arm64-cgo), const IFT_ISDNBASIC = 20
pkg syscall (netbsd-arm64-cgo), const IFT_ISDNBASIC ideal-int
pkg syscall (netbsd-arm64-cgo), const IFT_ISDN ideal-int
pkg syscall (netbsd-arm64-cgo), const IFT_ISDNPRIMARY = 21
pkg syscall (netbsd-arm64-cgo), const IFT_ISDNPRIMARY ideal-int
pkg syscall (netbsd-arm64-cgo), const IFT_ISDNS = 75
pkg syscall (netbsd-arm64-cgo), const IFT_ISDNS ideal-int
pkg syscall (netbsd-arm64-cgo), const IFT_ISDNU = 76
pkg syscall (netbsd-arm64-cgo), const IFT_ISDNU ideal-int
pkg syscall (netbsd-arm64-cgo), const IFT_ISO88022LLC = 41
pkg syscall (netbsd-arm64-cgo), const IFT_ISO88022LLC ideal-int
pkg syscall (netbsd-arm64-cgo), const IFT_ISO88023 = 7
pkg syscall (netbsd-arm64-cgo), const IFT_ISO88023 ideal-int
pkg syscall (netbsd-arm64-cgo), const IFT_ISO88024 = 8
pkg syscall (netbsd-arm64-cgo), const IFT_ISO88024 ideal-int
pkg syscall (netbsd-arm64-cgo), const IFT_ISO88025 = 9
pkg syscall (netbsd-arm64-cgo), const IFT_ISO88025CRFPINT = 98
pkg syscall (netbsd-arm64-cgo), const IFT_ISO88025CRFPINT ideal-int
pkg syscall (netbsd-arm64-cgo), const IFT_ISO88025DTR = 86
pkg syscall (netbsd-arm64-cgo), const IFT_ISO88025DTR ideal-int
pkg syscall (netbsd-arm64-cgo), const IFT_ISO88025FIBER = 115
pkg syscall (netbsd-arm64-cgo), const IFT_ISO88025FIBER ideal-int
pkg syscall (netbsd-arm64-cgo), const IFT_ISO88025 ideal-int
pkg syscall (netbsd-arm64-cgo), const IFT_ISO88026 = 10
pkg syscall (netbsd-arm64-cgo), const IFT_ISO88026 ideal-int
pkg syscall (netbsd-arm64-cgo), const IFT_ISUP = 179
pkg syscall (netbsd-arm64-cgo), const IFT_ISUP ideal-int
pkg syscall (netbsd-arm64-cgo), const IFT_L2VLAN = 135
pkg syscall (netbsd-arm64-cgo), const IFT_L2VLAN ideal-int
pkg syscall (netbsd-arm64-cgo), const IFT_L3IPVLAN = 136
pkg syscall (netbsd-arm64-cgo), const IFT_L3IPVLAN ideal-int
pkg syscall (netbsd-arm64-cgo), const IFT_L3IPXVLAN = 137
pkg syscall (netbsd-arm64-cgo), const IFT_L3IPXVLAN ideal-int
pkg syscall (netbsd-arm64-cgo), const IFT_LAPB = 16
pkg syscall (netbsd-arm64-cgo), const IFT_LAPB ideal-int
pkg syscall (netbsd-arm64-cgo), const IFT_LAPD = 77
pkg syscall (netbsd-arm64-cgo), const IFT_LAPD ideal-int
pkg syscall (netbsd-arm64-cgo), const IFT_LAPF = 119
pkg syscall (netbsd-arm64-cgo), const IFT_LAPF ideal-int
pkg syscall (netbsd-arm64-cgo), const IFT_LINEGROUP = 210
pkg syscall (netbsd-arm64-cgo), const IFT_LINEGROUP ideal-int
pkg syscall (netbsd-arm64-cgo), const IFT_LOCALTALK = 42
pkg syscall (netbsd-arm64-cgo), const IFT_LOCALTALK ideal-int
pkg syscall (netbsd-arm64-cgo), const IFT_LOOP = 24
pkg syscall (netbsd-arm64-cgo), const IFT_LOOP ideal-int
pkg syscall (netbsd-arm64-cgo), const IFT_MEDIAMAILOVERIP = 139
pkg syscall (netbsd-arm64-cgo), const IFT_MEDIAMAILOVERIP ideal-int
pkg syscall (netbsd-arm64-cgo), const IFT_MFSIGLINK = 167
pkg syscall (netbsd-arm64-cgo), const IFT_MFSIGLINK ideal-int
pkg syscall (netbsd-arm64-cgo), const IFT_MIOX25 = 38
pkg syscall (netbsd-arm64-cgo), const IFT_MIOX25 ideal-int
pkg syscall (netbsd-arm64-cgo), const IFT_MODEM = 48
pkg syscall (netbsd-arm64-cgo), const IFT_MODEM ideal-int
pkg syscall (netbsd-arm64-cgo), const IFT_MPC = 113
pkg syscall (netbsd-arm64-cgo), const IFT_MPC ideal-int
pkg syscall (netbsd-arm64-cgo), const IFT_MPLS = 166
pkg syscall (netbsd-arm64-cgo), const IFT_MPLS ideal-int
pkg syscall (netbsd-arm64-cgo), const IFT_MPLSTUNNEL = 150
pkg syscall (netbsd-arm64-cgo), const IFT_MPLSTUNNEL ideal-int
pkg syscall (netbsd-arm64-cgo), const IFT_MSDSL = 143
pkg syscall (netbsd-arm64-cgo), const IFT_MSDSL ideal-int
pkg syscall (netbsd-arm64-cgo), const IFT_MVL = 191
pkg syscall (netbsd-arm64-cgo), const IFT_MVL ideal-int
pkg syscall (netbsd-arm64-cgo), const IFT_MYRINET = 99
pkg syscall (netbsd-arm64-cgo), const IFT_MYRINET ideal-int
pkg syscall (netbsd-arm64-cgo), const IFT_NFAS = 175
pkg syscall (netbsd-arm64-cgo), const IFT_NFAS ideal-int
pkg syscall (netbsd-arm64-cgo), const IFT_NSIP = 27
pkg syscall (netbsd-arm64-cgo), const IFT_NSIP ideal-int
pkg syscall (netbsd-arm64-cgo), const IFT_OPTICALCHANNEL = 195
pkg syscall (netbsd-arm64-cgo), const IFT_OPTICALCHANNEL ideal-int
pkg syscall (netbsd-arm64-cgo), const IFT_OPTICALTRANSPORT = 196
pkg syscall (netbsd-arm64-cgo), const IFT_OPTICALTRANSPORT ideal-int
pkg syscall (netbsd-arm64-cgo), const IFT_OTHER = 1
pkg syscall (netbsd-arm64-cgo), const IFT_OTHER ideal-int
pkg syscall (netbsd-arm64-cgo), const IFT_P10 = 12
pkg syscall (netbsd-arm64-cgo), const IFT_P10 ideal-int
pkg syscall (netbsd-arm64-cgo), const IFT_P80 = 13
pkg syscall (netbsd-arm64-cgo), const IFT_P80 ideal-int
pkg syscall (netbsd-arm64-cgo), const IFT_PARA = 34
pkg syscall (netbsd-arm64-cgo), const IFT_PARA ideal-int
pkg syscall (netbsd-arm64-cgo), const IFT_PFLOG = 245
pkg syscall (netbsd-arm64-cgo), const IFT_PFLOG ideal-int
pkg syscall (netbsd-arm64-cgo), const IFT_PFSYNC = 246
pkg syscall (netbsd-arm64-cgo), const IFT_PFSYNC ideal-int
pkg syscall (netbsd-arm64-cgo), const IFT_PLC = 174
pkg syscall (netbsd-arm64-cgo), const IFT_PLC ideal-int
pkg syscall (netbsd-arm64-cgo), const IFT_PON155 = 207
pkg syscall (netbsd-arm64-cgo), const IFT_PON155 ideal-int
pkg syscall (netbsd-arm64-cgo), const IFT_PON622 = 208
pkg syscall (netbsd-arm64-cgo), const IFT_PON622 ideal-int
pkg syscall (netbsd-arm64-cgo), const IFT_POS = 171
pkg syscall (netbsd-arm64-cgo), const IFT_POS ideal-int
pkg syscall (netbsd-arm64-cgo), const IFT_PPP = 23
pkg syscall (netbsd-arm64-cgo), const IFT_PPP ideal-int
pkg syscall (netbsd-arm64-cgo), const IFT_PPPMULTILINKBUNDLE = 108
pkg syscall (netbsd-arm64-cgo), const IFT_PPPMULTILINKBUNDLE ideal-int
pkg syscall (netbsd-arm64-cgo), const IFT_PROPATM = 197
pkg syscall (netbsd-arm64-cgo), const IFT_PROPATM ideal-int
pkg syscall (netbsd-arm64-cgo), const IFT_PROPBWAP2MP = 184
pkg syscall (netbsd-arm64-cgo), const IFT_PROPBWAP2MP ideal-int
pkg syscall (netbsd-arm64-cgo), const IFT_PROPCNLS = 89
pkg syscall (netbsd-arm64-cgo), const IFT_PROPCNLS ideal-int
pkg syscall (netbsd-arm64-cgo), const IFT_PROPDOCSWIRELESSDOWNSTREAM = 181
pkg syscall (netbsd-arm64-cgo), const IFT_PROPDOCSWIRELESSDOWNSTREAM ideal-int
pkg syscall (netbsd-arm64-cgo), const IFT_PROPDOCSWIRELESSMACLAYER = 180
pkg syscall (netbsd-arm64-cgo), const IFT_PROPDOCSWIRELESSMACLAYER ideal-int
pkg syscall (netbsd-arm64-cgo), const IFT_PROPDOCSWIRELESSUPSTREAM = 182
pkg syscall (netbsd-arm64-cgo), const IFT_PROPDOCSWIRELESSUPSTREAM ideal-int
pkg syscall (netbsd-arm64-cgo), const IFT_PROPMUX = 54
pkg syscall (netbsd-arm64-cgo), const IFT_PROPMUX ideal-int
pkg syscall (netbsd-arm64-cgo), const IFT_PROPVIRTUAL = 53
pkg syscall (netbsd-arm64-cgo), const IFT_PROPVIRTUAL ideal-int
pkg syscall (netbsd-arm64-cgo), const IFT_PROPWIRELESSP2P = 157
pkg syscall (netbsd-arm64-cgo), const IFT_PROPWIRELESSP2P ideal-int
pkg syscall (netbsd-arm64-cgo), const IFT_PTPSERIAL = 22
pkg syscall (netbsd-arm64-cgo), const IFT_PTPSERIAL ideal-int
pkg syscall (netbsd-arm64-cgo), const IFT_PVC = 241
pkg syscall (netbsd-arm64-cgo), const IFT_PVC ideal-int
pkg syscall (netbsd-arm64-cgo), const IFT_Q2931 = 201
pkg syscall (netbsd-arm64-cgo), const IFT_Q2931 ideal-int
pkg syscall (netbsd-arm64-cgo), const IFT_QLLC = 68
pkg syscall (netbsd-arm64-cgo), const IFT_QLLC ideal-int
pkg syscall (netbsd-arm64-cgo), const IFT_RADIOMAC = 188
pkg syscall (netbsd-arm64-cgo), const IFT_RADIOMAC ideal-int
pkg syscall (netbsd-arm64-cgo), const IFT_RADSL = 95
pkg syscall (netbsd-arm64-cgo), const IFT_RADSL ideal-int
pkg syscall (netbsd-arm64-cgo), const IFT_REACHDSL = 192
pkg syscall (netbsd-arm64-cgo), const IFT_REACHDSL ideal-int
pkg syscall (netbsd-arm64-cgo), const IFT_RFC1483 = 159
pkg syscall (netbsd-arm64-cgo), const IFT_RFC1483 ideal-int
pkg syscall (netbsd-arm64-cgo), const IFT_RS232 = 33
pkg syscall (netbsd-arm64-cgo), const IFT_RS232 ideal-int
pkg syscall (netbsd-arm64-cgo), const IFT_RSRB = 79
pkg syscall (netbsd-arm64-cgo), const IFT_RSRB ideal-int
pkg syscall (netbsd-arm64-cgo), const IFT_SDLC = 17
pkg syscall (netbsd-arm64-cgo), const IFT_SDLC ideal-int
pkg syscall (netbsd-arm64-cgo), const IFT_SDSL = 96
pkg syscall (netbsd-arm64-cgo), const IFT_SDSL ideal-int
pkg syscall (netbsd-arm64-cgo), const IFT_SHDSL = 169
pkg syscall (netbsd-arm64-cgo), const IFT_SHDSL ideal-int
pkg syscall (netbsd-arm64-cgo), const IFT_SIP = 31
pkg syscall (netbsd-arm64-cgo), const IFT_SIP ideal-int
pkg syscall (netbsd-arm64-cgo), const IFT_SIPSIG = 204
pkg syscall (netbsd-arm64-cgo), const IFT_SIPSIG ideal-int
pkg syscall (netbsd-arm64-cgo), const IFT_SIPTG = 203
pkg syscall (netbsd-arm64-cgo), const IFT_SIPTG ideal-int
pkg syscall (netbsd-arm64-cgo), const IFT_SLIP = 28
pkg syscall (netbsd-arm64-cgo), const IFT_SLIP ideal-int
pkg syscall (netbsd-arm64-cgo), const IFT_SMDSDXI = 43
pkg syscall (netbsd-arm64-cgo), const IFT_SMDSDXI ideal-int
pkg syscall (netbsd-arm64-cgo), const IFT_SMDSICIP = 52
pkg syscall (netbsd-arm64-cgo), const IFT_SMDSICIP ideal-int
pkg syscall (netbsd-arm64-cgo), const IFT_SONET = 39
pkg syscall (netbsd-arm64-cgo), const IFT_SONET ideal-int
pkg syscall (netbsd-arm64-cgo), const IFT_SONETOVERHEADCHANNEL = 185
pkg syscall (netbsd-arm64-cgo), const IFT_SONETOVERHEADCHANNEL ideal-int
pkg syscall (netbsd-arm64-cgo), const IFT_SONETPATH = 50
pkg syscall (netbsd-arm64-cgo), const IFT_SONETPATH ideal-int
pkg syscall (netbsd-arm64-cgo), const IFT_SONETVT = 51
pkg syscall (netbsd-arm64-cgo), const IFT_SONETVT ideal-int
pkg syscall (netbsd-arm64-cgo), const IFT_SRP = 151
pkg syscall (netbsd-arm64-cgo), const IFT_SRP ideal-int
pkg syscall (netbsd-arm64-cgo), const IFT_SS7SIGLINK = 156
pkg syscall (netbsd-arm64-cgo), const IFT_SS7SIGLINK ideal-int
pkg syscall (netbsd-arm64-cgo), const IFT_STACKTOSTACK = 111
pkg syscall (netbsd-arm64-cgo), const IFT_STACKTOSTACK ideal-int
pkg syscall (netbsd-arm64-cgo), const IFT_STARLAN = 11
pkg syscall (netbsd-arm64-cgo), const IFT_STARLAN ideal-int
pkg syscall (netbsd-arm64-cgo), const IFT_STF = 215
pkg syscall (netbsd-arm64-cgo), const IFT_STF ideal-int
pkg syscall (netbsd-arm64-cgo), const IFT_T1 = 18
pkg syscall (netbsd-arm64-cgo), const IFT_T1 ideal-int
pkg syscall (netbsd-arm64-cgo), const IFT_TDLC = 116
pkg syscall (netbsd-arm64-cgo), const IFT_TDLC ideal-int
pkg syscall (netbsd-arm64-cgo), const IFT_TELINK = 200
pkg syscall (netbsd-arm64-cgo), const IFT_TELINK ideal-int
pkg syscall (netbsd-arm64-cgo), const IFT_TERMPAD = 91
pkg syscall (netbsd-arm64-cgo), const IFT_TERMPAD ideal-int
pkg syscall (netbsd-arm64-cgo), const IFT_TR008 = 176
pkg syscall (netbsd-arm64-cgo), const IFT_TR008 ideal-int
pkg syscall (netbsd-arm64-cgo), const IFT_TRANSPHDLC = 123
pkg syscall (netbsd-arm64-cgo), const IFT_TRANSPHDLC ideal-int
pkg syscall (netbsd-arm64-cgo), const IFT_TUNNEL = 131
pkg syscall (netbsd-arm64-cgo), const IFT_TUNNEL ideal-int
pkg syscall (netbsd-arm64-cgo), const IFT_ULTRA = 29
pkg syscall (netbsd-arm64-cgo), const IFT_ULTRA ideal-int
pkg syscall (netbsd-arm64-cgo), const IFT_USB = 160
pkg syscall (netbsd-arm64-cgo), const IFT_USB ideal-int
pkg syscall (netbsd-arm64-cgo), const IFT_V11 = 64
pkg syscall (netbsd-arm64-cgo), const IFT_V11 ideal-int
pkg syscall (netbsd-arm64-cgo), const IFT_V35 = 45
pkg syscall (netbsd-arm64-cgo), const IFT_V35 ideal-int
pkg syscall (netbsd-arm64-cgo), const IFT_V36 = 65
pkg syscall (netbsd-arm64-cgo), const IFT_V36 ideal-int
pkg syscall (netbsd-arm64-cgo), const IFT_V37 = 120
pkg syscall (netbsd-arm64-cgo), const IFT_V37 ideal-int
pkg syscall (netbsd-arm64-cgo), const IFT_VDSL = 97
pkg syscall (netbsd-arm64-cgo), const IFT_VDSL ideal-int
pkg syscall (netbsd-arm64-cgo), const IFT_VIRTUALIPADDRESS = 112
pkg syscall (netbsd-arm64-cgo), const IFT_VIRTUALIPADDRESS ideal-int
pkg syscall (netbsd-arm64-cgo), const IFT_VIRTUALTG = 202
pkg syscall (netbsd-arm64-cgo), const IFT_VIRTUALTG ideal-int
pkg syscall (netbsd-arm64-cgo), const IFT_VOICEDID = 213
pkg syscall (netbsd-arm64-cgo), const IFT_VOICEDID ideal-int
pkg syscall (netbsd-arm64-cgo), const IFT_VOICEEM = 100
pkg syscall (netbsd-arm64-cgo), const IFT_VOICEEMFGD = 211
pkg syscall (netbsd-arm64-cgo), const IFT_VOICEEMFGD ideal-int
pkg syscall (netbsd-arm64-cgo), const IFT_VOICEEM ideal-int
pkg syscall (netbsd-arm64-cgo), const IFT_VOICEENCAP = 103
pkg syscall (netbsd-arm64-cgo), const IFT_VOICEENCAP ideal-int
pkg syscall (netbsd-arm64-cgo), const IFT_VOICEFGDEANA = 212
pkg syscall (netbsd-arm64-cgo), const IFT_VOICEFGDEANA ideal-int
pkg syscall (netbsd-arm64-cgo), const IFT_VOICEFXO = 101
pkg syscall (netbsd-arm64-cgo), const IFT_VOICEFXO ideal-int
pkg syscall (netbsd-arm64-cgo), const IFT_VOICEFXS = 102
pkg syscall (netbsd-arm64-cgo), const IFT_VOICEFXS ideal-int
pkg syscall (netbsd-arm64-cgo), const IFT_VOICEOVERATM = 152
pkg syscall (netbsd-arm64-cgo), const IFT_VOICEOVERATM ideal-int
pkg syscall (netbsd-arm64-cgo), const IFT_VOICEOVERCABLE = 198
pkg syscall (netbsd-arm64-cgo), const IFT_VOICEOVERCABLE ideal-int
pkg syscall (netbsd-arm64-cgo), const IFT_VOICEOVERFRAMERELAY = 153
pkg syscall (netbsd-arm64-cgo), const IFT_VOICEOVERFRAMERELAY ideal-int
pkg syscall (netbsd-arm64-cgo), const IFT_VOICEOVERIP = 104
pkg syscall (netbsd-arm64-cgo), const IFT_VOICEOVERIP ideal-int
pkg syscall (netbsd-arm64-cgo), const IFT_X213 = 93
pkg syscall (netbsd-arm64-cgo), const IFT_X213 ideal-int
pkg syscall (netbsd-arm64-cgo), const IFT_X25 = 5
pkg syscall (netbsd-arm64-cgo), const IFT_X25DDN = 4
pkg syscall (netbsd-arm64-cgo), const IFT_X25DDN ideal-int
pkg syscall (netbsd-arm64-cgo), const IFT_X25HUNTGROUP = 122
pkg syscall (netbsd-arm64-cgo), const IFT_X25HUNTGROUP ideal-int
pkg syscall (netbsd-arm64-cgo), const IFT_X25 ideal-int
pkg syscall (netbsd-arm64-cgo), const IFT_X25MLP = 121
pkg syscall (netbsd-arm64-cgo), const IFT_X25MLP ideal-int
pkg syscall (netbsd-arm64-cgo), const IFT_X25PLE = 40
pkg syscall (netbsd-arm64-cgo), const IFT_X25PLE ideal-int
pkg syscall (netbsd-arm64-cgo), const IFT_XETHER = 26
pkg syscall (netbsd-arm64-cgo), const IFT_XETHER ideal-int
pkg syscall (netbsd-arm64-cgo), const IGNBRK = 1
pkg syscall (netbsd-arm64-cgo), const IGNBRK ideal-int
pkg syscall (netbsd-arm64-cgo), const IGNCR = 128
pkg syscall (netbsd-arm64-cgo), const IGNCR ideal-int
pkg syscall (netbsd-arm64-cgo), const IGNPAR = 4
pkg syscall (netbsd-arm64-cgo), const IGNPAR ideal-int
pkg syscall (netbsd-arm64-cgo), const IMAXBEL = 8192
pkg syscall (netbsd-arm64-cgo), const IMAXBEL ideal-int
pkg syscall (netbsd-arm64-cgo), const IN_CLASSA_HOST = 16777215
pkg syscall (netbsd-arm64-cgo), const IN_CLASSA_HOST ideal-int
pkg syscall (netbsd-arm64-cgo), const IN_CLASSA_MAX = 128
pkg syscall (netbsd-arm64-cgo), const IN_CLASSA_MAX ideal-int
pkg syscall (netbsd-arm64-cgo), const IN_CLASSA_NET = 4278190080
pkg syscall (netbsd-arm64-cgo), const IN_CLASSA_NET ideal-int
pkg syscall (netbsd-arm64-cgo), const IN_CLASSA_NSHIFT = 24
pkg syscall (netbsd-arm64-cgo), const IN_CLASSA_NSHIFT ideal-int
pkg syscall (netbsd-arm64-cgo), const IN_CLASSB_HOST = 65535
pkg syscall (netbsd-arm64-cgo), const IN_CLASSB_HOST ideal-int
pkg syscall (netbsd-arm64-cgo), const IN_CLASSB_MAX = 65536
pkg syscall (netbsd-arm64-cgo), const IN_CLASSB_MAX ideal-int
pkg syscall (netbsd-arm64-cgo), const IN_CLASSB_NET = 4294901760
pkg syscall (netbsd-arm64-cgo), const IN_CLASSB_NET ideal-int
pkg syscall (netbsd-arm64-cgo), const IN_CLASSB_NSHIFT = 16
pkg syscall (netbsd-arm64-cgo), const IN_CLASSB_NSHIFT ideal-int
pkg syscall (netbsd-arm64-cgo), const IN_CLASSC_HOST = 255
pkg syscall (netbsd-arm64-cgo), const IN_CLASSC_HOST ideal-int
pkg syscall (netbsd-arm64-cgo), const IN_CLASSC_NET = 4294967040
pkg syscall (netbsd-arm64-cgo), const IN_CLASSC_NET ideal-int
pkg syscall (netbsd-arm64-cgo), const IN_CLASSC_NSHIFT = 8
pkg syscall (netbsd-arm64-cgo), const IN_CLASSC_NSHIFT ideal-int
pkg syscall (netbsd-arm64-cgo), const IN_CLASSD_HOST = 268435455
pkg syscall (netbsd-arm64-cgo), const IN_CLASSD_HOST ideal-int
pkg syscall (netbsd-arm64-cgo), const IN_CLASSD_NET = 4026531840
pkg syscall (netbsd-arm64-cgo), const IN_CLASSD_NET ideal-int
pkg syscall (netbsd-arm64-cgo), const IN_CLASSD_NSHIFT = 28
pkg syscall (netbsd-arm64-cgo), const IN_CLASSD_NSHIFT ideal-int
pkg syscall (netbsd-arm64-cgo), const INLCR = 64
pkg syscall (netbsd-arm64-cgo), const INLCR ideal-int
pkg syscall (netbsd-arm64-cgo), const IN_LOOPBACKNET = 127
pkg syscall (netbsd-arm64-cgo), const IN_LOOPBACKNET ideal-int
pkg syscall (netbsd-arm64-cgo), const INPCK = 16
pkg syscall (netbsd-arm64-cgo), const INPCK ideal-int
pkg syscall (netbsd-arm64-cgo), const IP_ADD_MEMBERSHIP = 12
pkg syscall (netbsd-arm64-cgo), const IP_DEFAULT_MULTICAST_LOOP = 1
pkg syscall (netbsd-arm64-cgo), const IP_DEFAULT_MULTICAST_LOOP ideal-int
pkg syscall (netbsd-arm64-cgo), const IP_DEFAULT_MULTICAST_TTL = 1
pkg syscall (netbsd-arm64-cgo), const IP_DEFAULT_MULTICAST_TTL ideal-int
pkg syscall (netbsd-arm64-cgo), const IP_DF = 16384
pkg syscall (netbsd-arm64-cgo), const IP_DF ideal-int
pkg syscall (netbsd-arm64-cgo), const IP_DROP_MEMBERSHIP = 13
pkg syscall (netbsd-arm64-cgo), const IP_EF = 32768
pkg syscall (netbsd-arm64-cgo), const IP_EF ideal-int
pkg syscall (netbsd-arm64-cgo), const IP_ERRORMTU = 21
pkg syscall (netbsd-arm64-cgo), const IP_ERRORMTU ideal-int
pkg syscall (netbsd-arm64-cgo), const IP_HDRINCL = 2
pkg syscall (netbsd-arm64-cgo), const IP_HDRINCL ideal-int
pkg syscall (netbsd-arm64-cgo), const IP_IPSEC_POLICY = 22
pkg syscall (netbsd-arm64-cgo), const IP_IPSEC_POLICY ideal-int
pkg syscall (netbsd-arm64-cgo), const IP_MAX_MEMBERSHIPS = 20
pkg syscall (netbsd-arm64-cgo), const IP_MAX_MEMBERSHIPS ideal-int
pkg syscall (netbsd-arm64-cgo), const IP_MAXPACKET = 65535
pkg syscall (netbsd-arm64-cgo), const IP_MAXPACKET ideal-int
pkg syscall (netbsd-arm64-cgo), const IP_MF = 8192
pkg syscall (netbsd-arm64-cgo), const IP_MF ideal-int
pkg syscall (netbsd-arm64-cgo), const IP_MINFRAGSIZE = 69
pkg syscall (netbsd-arm64-cgo), const IP_MINFRAGSIZE ideal-int
pkg syscall (netbsd-arm64-cgo), const IP_MINTTL = 24
pkg syscall (netbsd-arm64-cgo), const IP_MINTTL ideal-int
pkg syscall (netbsd-arm64-cgo), const IP_MSS = 576
pkg syscall (netbsd-arm64-cgo), const IP_MSS ideal-int
pkg syscall (netbsd-arm64-cgo), const IP_MULTICAST_IF = 9
pkg syscall (netbsd-arm64-cgo), const IP_MULTICAST_LOOP = 11
pkg syscall (netbsd-arm64-cgo), const IP_MULTICAST_TTL = 10
pkg syscall (netbsd-arm64-cgo), const IP_OFFMASK = 8191
pkg syscall (netbsd-arm64-cgo), const IP_OFFMASK ideal-int
pkg syscall (netbsd-arm64-cgo), const IP_OPTIONS = 1
pkg syscall (netbsd-arm64-cgo), const IP_OPTIONS ideal-int
pkg syscall (netbsd-arm64-cgo), const IP_PORTRANGE = 19
pkg syscall (netbsd-arm64-cgo), const IP_PORTRANGE_DEFAULT = 0
pkg syscall (netbsd-arm64-cgo), const IP_PORTRANGE_DEFAULT ideal-int
pkg syscall (netbsd-arm64-cgo), const IP_PORTRANGE_HIGH = 1
pkg syscall (netbsd-arm64-cgo), const IP_PORTRANGE_HIGH ideal-int
pkg syscall (netbsd-arm64-cgo), const IP_PORTRANGE ideal-int
pkg syscall (netbsd-arm64-cgo), const IP_PORTRANGE_LOW = 2
pkg syscall (netbsd-arm64-cgo), const IP_PORTRANGE_LOW ideal-int
pkg syscall (netbsd-arm64-cgo), const IPPROTO_AH = 51
pkg syscall (netbsd-arm64-cgo), const IPPROTO_AH ideal-int
pkg syscall (netbsd-arm64-cgo), const IPPROTO_CARP = 112
pkg syscall (netbsd-arm64-cgo), const IPPROTO_CARP ideal-int
pkg syscall (netbsd-arm64-cgo), const IPPROTO_DONE = 257
pkg syscall (netbsd-arm64-cgo), const IPPROTO_DONE ideal-int
pkg syscall (netbsd-arm64-cgo), const IPPROTO_DSTOPTS = 60
pkg syscall (netbsd-arm64-cgo), const IPPROTO_DSTOPTS ideal-int
pkg syscall (netbsd-arm64-cgo), const IPPROTO_EGP = 8
pkg syscall (netbsd-arm64-cgo), const IPPROTO_EGP ideal-int
pkg syscall (netbsd-arm64-cgo), const IPPROTO_ENCAP = 98
pkg syscall (netbsd-arm64-cgo), const IPPROTO_ENCAP ideal-int
pkg syscall (netbsd-arm64-cgo), const IPPROTO_EON = 80
pkg syscall (netbsd-arm64-cgo), const IPPROTO_EON ideal-int
pkg syscall (netbsd-arm64-cgo), const IPPROTO_ESP = 50
pkg syscall (netbsd-arm64-cgo), const IPPROTO_ESP ideal-int
pkg syscall (netbsd-arm64-cgo), const IPPROTO_ETHERIP = 97
pkg syscall (netbsd-arm64-cgo), const IPPROTO_ETHERIP ideal-int
pkg syscall (netbsd-arm64-cgo), const IPPROTO_FRAGMENT = 44
pkg syscall (netbsd-arm64-cgo), const IPPROTO_FRAGMENT ideal-int
pkg syscall (netbsd-arm64-cgo), const IPPROTO_GGP = 3
pkg syscall (netbsd-arm64-cgo), const IPPROTO_GGP ideal-int
pkg syscall (netbsd-arm64-cgo), const IPPROTO_GRE = 47
pkg syscall (netbsd-arm64-cgo), const IPPROTO_GRE ideal-int
pkg syscall (netbsd-arm64-cgo), const IPPROTO_HOPOPTS = 0
pkg syscall (netbsd-arm64-cgo), const IPPROTO_HOPOPTS ideal-int
pkg syscall (netbsd-arm64-cgo), const IPPROTO_ICMP = 1
pkg syscall (netbsd-arm64-cgo), const IPPROTO_ICMP ideal-int
pkg syscall (netbsd-arm64-cgo), const IPPROTO_ICMPV6 = 58
pkg syscall (netbsd-arm64-cgo), const IPPROTO_ICMPV6 ideal-int
pkg syscall (netbsd-arm64-cgo), const IPPROTO_IDP = 22
pkg syscall (netbsd-arm64-cgo), const IPPROTO_IDP ideal-int
pkg syscall (netbsd-arm64-cgo), const IPPROTO_IGMP = 2
pkg syscall (netbsd-arm64-cgo), const IPPROTO_IGMP ideal-int
pkg syscall (netbsd-arm64-cgo), const IPPROTO_IPCOMP = 108
pkg syscall (netbsd-arm64-cgo), const IPPROTO_IPCOMP ideal-int
pkg syscall (netbsd-arm64-cgo), const IPPROTO_IPIP = 4
pkg syscall (netbsd-arm64-cgo), const IPPROTO_IPIP ideal-int
pkg syscall (netbsd-arm64-cgo), const IPPROTO_IPV4 = 4
pkg syscall (netbsd-arm64-cgo), const IPPROTO_IPV4 ideal-int
pkg syscall (netbsd-arm64-cgo), const IPPROTO_IPV6_ICMP = 58
pkg syscall (netbsd-arm64-cgo), const IPPROTO_IPV6_ICMP ideal-int
pkg syscall (netbsd-arm64-cgo), const IPPROTO_MAX = 256
pkg syscall (netbsd-arm64-cgo), const IPPROTO_MAXID = 52
pkg syscall (netbsd-arm64-cgo), const IPPROTO_MAX ideal-int
pkg syscall (netbsd-arm64-cgo), const IPPROTO_MAXID ideal-int
pkg syscall (netbsd-arm64-cgo), const IPPROTO_MOBILE = 55
pkg syscall (netbsd-arm64-cgo), const IPPROTO_MOBILE ideal-int
pkg syscall (netbsd-arm64-cgo), const IPPROTO_NONE = 59
pkg syscall (netbsd-arm64-cgo), const IPPROTO_NONE ideal-int
pkg syscall (netbsd-arm64-cgo), const IPPROTO_PFSYNC = 240
pkg syscall (netbsd-arm64-cgo), const IPPROTO_PFSYNC ideal-int
pkg syscall (netbsd-arm64-cgo), const IPPROTO_PIM = 103
pkg syscall (netbsd-arm64-cgo), const IPPROTO_PIM ideal-int
pkg syscall (netbsd-arm64-cgo), const IPPROTO_PUP = 12
pkg syscall (netbsd-arm64-cgo), const IPPROTO_PUP ideal-int
pkg syscall (netbsd-arm64-cgo), const IPPROTO_RAW = 255
pkg syscall (netbsd-arm64-cgo), const IPPROTO_RAW ideal-int
pkg syscall (netbsd-arm64-cgo), const IPPROTO_ROUTING = 43
pkg syscall (netbsd-arm64-cgo), const IPPROTO_ROUTING ideal-int
pkg syscall (netbsd-arm64-cgo), const IPPROTO_RSVP = 46
pkg syscall (netbsd-arm64-cgo), const IPPROTO_RSVP ideal-int
pkg syscall (netbsd-arm64-cgo), const IPPROTO_TP = 29
pkg syscall (netbsd-arm64-cgo), const IPPROTO_TP ideal-int
pkg syscall (netbsd-arm64-cgo), const IPPROTO_VRRP = 112
pkg syscall (netbsd-arm64-cgo), const IPPROTO_VRRP ideal-int
pkg syscall (netbsd-arm64-cgo), const IP_RECVDSTADDR = 7
pkg syscall (netbsd-arm64-cgo), const IP_RECVDSTADDR ideal-int
pkg syscall (netbsd-arm64-cgo), const IP_RECVIF = 20
pkg syscall (netbsd-arm64-cgo), const IP_RECVIF ideal-int
pkg syscall (netbsd-arm64-cgo), const IP_RECVOPTS = 5
pkg syscall (netbsd-arm64-cgo), const IP_RECVOPTS ideal-int
pkg syscall (netbsd-arm64-cgo), const IP_RECVRETOPTS = 6
pkg syscall (netbsd-arm64-cgo), const IP_RECVRETOPTS ideal-int
pkg syscall (netbsd-arm64-cgo), const IP_RECVTTL = 23
pkg syscall (netbsd-arm64-cgo), const IP_RECVTTL ideal-int
pkg syscall (netbsd-arm64-cgo), const IP_RETOPTS = 8
pkg syscall (netbsd-arm64-cgo), const IP_RETOPTS ideal-int
pkg syscall (netbsd-arm64-cgo), const IP_RF = 32768
pkg syscall (netbsd-arm64-cgo), const IP_RF ideal-int
pkg syscall (netbsd-arm64-cgo), const IP_TOS = 3
pkg syscall (netbsd-arm64-cgo), const IP_TTL = 4
pkg syscall (netbsd-arm64-cgo), const IPV6_CHECKSUM = 26
pkg syscall (netbsd-arm64-cgo), const IPV6_CHECKSUM ideal-int
pkg syscall (netbsd-arm64-cgo), const IPV6_DEFAULT_MULTICAST_HOPS = 1
pkg syscall (netbsd-arm64-cgo), const IPV6_DEFAULT_MULTICAST_HOPS ideal-int
pkg syscall (netbsd-arm64-cgo), const IPV6_DEFAULT_MULTICAST_LOOP = 1
pkg syscall (netbsd-arm64-cgo), const IPV6_DEFAULT_MULTICAST_LOOP ideal-int
pkg syscall (netbsd-arm64-cgo), const IPV6_DEFHLIM = 64
pkg syscall (netbsd-arm64-cgo), const IPV6_DEFHLIM ideal-int
pkg syscall (netbsd-arm64-cgo), const IPV6_DONTFRAG = 62
pkg syscall (netbsd-arm64-cgo), const IPV6_DONTFRAG ideal-int
pkg syscall (netbsd-arm64-cgo), const IPV6_DSTOPTS = 50
pkg syscall (netbsd-arm64-cgo), const IPV6_DSTOPTS ideal-int
pkg syscall (netbsd-arm64-cgo), const IPV6_FAITH = 29
pkg syscall (netbsd-arm64-cgo), const IPV6_FAITH ideal-int
pkg syscall (netbsd-arm64-cgo), const IPV6_FLOWINFO_MASK = 4294967055
pkg syscall (netbsd-arm64-cgo), const IPV6_FLOWINFO_MASK ideal-int
pkg syscall (netbsd-arm64-cgo), const IPV6_FLOWLABEL_MASK = 4294905600
pkg syscall (netbsd-arm64-cgo), const IPV6_FLOWLABEL_MASK ideal-int
pkg syscall (netbsd-arm64-cgo), const IPV6_FRAGTTL = 120
pkg syscall (netbsd-arm64-cgo), const IPV6_FRAGTTL ideal-int
pkg syscall (netbsd-arm64-cgo), const IPV6_HLIMDEC = 1
pkg syscall (netbsd-arm64-cgo), const IPV6_HLIMDEC ideal-int
pkg syscall (netbsd-arm64-cgo), const IPV6_HOPLIMIT = 47
pkg syscall (netbsd-arm64-cgo), const IPV6_HOPLIMIT ideal-int
pkg syscall (netbsd-arm64-cgo), const IPV6_HOPOPTS = 49
pkg syscall (netbsd-arm64-cgo), const IPV6_HOPOPTS ideal-int
pkg syscall (netbsd-arm64-cgo), const IPV6_IPSEC_POLICY = 28
pkg syscall (netbsd-arm64-cgo), const IPV6_IPSEC_POLICY ideal-int
pkg syscall (netbsd-arm64-cgo), const IPV6_JOIN_GROUP = 12
pkg syscall (netbsd-arm64-cgo), const IPV6_LEAVE_GROUP = 13
pkg syscall (netbsd-arm64-cgo), const IPV6_MAXHLIM = 255
pkg syscall (netbsd-arm64-cgo), const IPV6_MAXHLIM ideal-int
pkg syscall (netbsd-arm64-cgo), const IPV6_MAXPACKET = 65535
pkg syscall (netbsd-arm64-cgo), const IPV6_MAXPACKET ideal-int
pkg syscall (netbsd-arm64-cgo), const IPV6_MMTU = 1280
pkg syscall (netbsd-arm64-cgo), const IPV6_MMTU ideal-int
pkg syscall (netbsd-arm64-cgo), const IPV6_MULTICAST_HOPS = 10
pkg syscall (netbsd-arm64-cgo), const IPV6_MULTICAST_IF = 9
pkg syscall (netbsd-arm64-cgo), const IPV6_MULTICAST_LOOP = 11
pkg syscall (netbsd-arm64-cgo), const IPV6_NEXTHOP = 48
pkg syscall (netbsd-arm64-cgo), const IPV6_NEXTHOP ideal-int
pkg syscall (netbsd-arm64-cgo), const IPV6_PATHMTU = 44
pkg syscall (netbsd-arm64-cgo), const IPV6_PATHMTU ideal-int
pkg syscall (netbsd-arm64-cgo), const IPV6_PKTINFO = 46
pkg syscall (netbsd-arm64-cgo), const IPV6_PKTINFO ideal-int
pkg syscall (netbsd-arm64-cgo), const IPV6_PORTRANGE = 14
pkg syscall (netbsd-arm64-cgo), const IPV6_PORTRANGE_DEFAULT = 0
pkg syscall (netbsd-arm64-cgo), const IPV6_PORTRANGE_DEFAULT ideal-int
pkg syscall (netbsd-arm64-cgo), const IPV6_PORTRANGE_HIGH = 1
pkg syscall (netbsd-arm64-cgo), const IPV6_PORTRANGE_HIGH ideal-int
pkg syscall (netbsd-arm64-cgo), const IPV6_PORTRANGE ideal-int
pkg syscall (netbsd-arm64-cgo), const IPV6_PORTRANGE_LOW = 2
pkg syscall (netbsd-arm64-cgo), const IPV6_PORTRANGE_LOW ideal-int
pkg syscall (netbsd-arm64-cgo), const IPV6_RECVDSTOPTS = 40
pkg syscall (netbsd-arm64-cgo), const IPV6_RECVDSTOPTS ideal-int
pkg syscall (netbsd-arm64-cgo), const IPV6_RECVHOPLIMIT = 37
pkg syscall (netbsd-arm64-cgo), const IPV6_RECVHOPLIMIT ideal-int
pkg syscall (netbsd-arm64-cgo), const IPV6_RECVHOPOPTS = 39
pkg syscall (netbsd-arm64-cgo), const IPV6_RECVHOPOPTS ideal-int
pkg syscall (netbsd-arm64-cgo), const IPV6_RECVPATHMTU = 43
pkg syscall (netbsd-arm64-cgo), const IPV6_RECVPATHMTU ideal-int
pkg syscall (netbsd-arm64-cgo), const IPV6_RECVPKTINFO = 36
pkg syscall (netbsd-arm64-cgo), const IPV6_RECVPKTINFO ideal-int
pkg syscall (netbsd-arm64-cgo), const IPV6_RECVRTHDR = 38
pkg syscall (netbsd-arm64-cgo), const IPV6_RECVRTHDR ideal-int
pkg syscall (netbsd-arm64-cgo), const IPV6_RECVTCLASS = 57
pkg syscall (netbsd-arm64-cgo), const IPV6_RECVTCLASS ideal-int
pkg syscall (netbsd-arm64-cgo), const IPV6_RTHDR = 51
pkg syscall (netbsd-arm64-cgo), const IPV6_RTHDRDSTOPTS = 35
pkg syscall (netbsd-arm64-cgo), const IPV6_RTHDRDSTOPTS ideal-int
pkg syscall (netbsd-arm64-cgo), const IPV6_RTHDR ideal-int
pkg syscall (netbsd-arm64-cgo), const IPV6_RTHDR_LOOSE = 0
pkg syscall (netbsd-arm64-cgo), const IPV6_RTHDR_LOOSE ideal-int
pkg syscall (netbsd-arm64-cgo), const IPV6_RTHDR_STRICT = 1
pkg syscall (netbsd-arm64-cgo), const IPV6_RTHDR_STRICT ideal-int
pkg syscall (netbsd-arm64-cgo), const IPV6_RTHDR_TYPE_0 = 0
pkg syscall (netbsd-arm64-cgo), const IPV6_RTHDR_TYPE_0 ideal-int
pkg syscall (netbsd-arm64-cgo), const IPV6_SOCKOPT_RESERVED1 = 3
pkg syscall (netbsd-arm64-cgo), const IPV6_SOCKOPT_RESERVED1 ideal-int
pkg syscall (netbsd-arm64-cgo), const IPV6_TCLASS = 61
pkg syscall (netbsd-arm64-cgo), const IPV6_TCLASS ideal-int
pkg syscall (netbsd-arm64-cgo), const IPV6_UNICAST_HOPS = 4
pkg syscall (netbsd-arm64-cgo), const IPV6_USE_MIN_MTU = 42
pkg syscall (netbsd-arm64-cgo), const IPV6_USE_MIN_MTU ideal-int
pkg syscall (netbsd-arm64-cgo), const IPV6_V6ONLY = 27
pkg syscall (netbsd-arm64-cgo), const IPV6_VERSION = 96
pkg syscall (netbsd-arm64-cgo), const IPV6_VERSION ideal-int
pkg syscall (netbsd-arm64-cgo), const IPV6_VERSION_MASK = 240
pkg syscall (netbsd-arm64-cgo), const IPV6_VERSION_MASK ideal-int
pkg syscall (netbsd-arm64-cgo), const ISIG = 128
pkg syscall (netbsd-arm64-cgo), const ISIG ideal-int
pkg syscall (netbsd-arm64-cgo), const ISTRIP = 32
pkg syscall (netbsd-arm64-cgo), const ISTRIP ideal-int
pkg syscall (netbsd-arm64-cgo), const IXANY = 2048
pkg syscall (netbsd-arm64-cgo), const IXANY ideal-int
pkg syscall (netbsd-arm64-cgo), const IXOFF = 1024
pkg syscall (netbsd-arm64-cgo), const IXOFF ideal-int
pkg syscall (netbsd-arm64-cgo), const IXON = 512
pkg syscall (netbsd-arm64-cgo), const IXON ideal-int
pkg syscall (netbsd-arm64-cgo), const LOCK_EX = 2
pkg syscall (netbsd-arm64-cgo), const LOCK_EX ideal-int
pkg syscall (netbsd-arm64-cgo), const LOCK_NB = 4
pkg syscall (netbsd-arm64-cgo), const LOCK_NB ideal-int
pkg syscall (netbsd-arm64-cgo), const LOCK_SH = 1
pkg syscall (netbsd-arm64-cgo), const LOCK_SH ideal-int
pkg syscall (netbsd-arm64-cgo), const LOCK_UN = 8
pkg syscall (netbsd-arm64-cgo), const LOCK_UN ideal-int
pkg syscall (netbsd-arm64-cgo), const MADV_DONTNEED = 4
pkg syscall (netbsd-arm64-cgo), const MADV_DONTNEED ideal-int
pkg syscall (netbsd-arm64-cgo), const MADV_FREE = 6
pkg syscall (netbsd-arm64-cgo), const MADV_FREE ideal-int
pkg syscall (netbsd-arm64-cgo), const MADV_NORMAL = 0
pkg syscall (netbsd-arm64-cgo), const MADV_NORMAL ideal-int
pkg syscall (netbsd-arm64-cgo), const MADV_RANDOM = 1
pkg syscall (netbsd-arm64-cgo), const MADV_RANDOM ideal-int
pkg syscall (netbsd-arm64-cgo), const MADV_SEQUENTIAL = 2
pkg syscall (netbsd-arm64-cgo), const MADV_SEQUENTIAL ideal-int
pkg syscall (netbsd-arm64-cgo), const MADV_SPACEAVAIL = 5
pkg syscall (netbsd-arm64-cgo), const MADV_SPACEAVAIL ideal-int
pkg syscall (netbsd-arm64-cgo), const MADV_WILLNEED = 3
pkg syscall (netbsd-arm64-cgo), const MADV_WILLNEED ideal-int
pkg syscall (netbsd-arm64-cgo), const MAP_ALIGNMENT_16MB = 402653184
pkg syscall (netbsd-arm64-cgo), const MAP_ALIGNMENT_16MB ideal-int
pkg syscall (netbsd-arm64-cgo), const MAP_ALIGNMENT_1TB = 671088640
pkg syscall (netbsd-arm64-cgo), const MAP_ALIGNMENT_1TB ideal-int
pkg syscall (netbsd-arm64-cgo), const MAP_ALIGNMENT_256TB = 805306368
pkg syscall (netbsd-arm64-cgo), const MAP_ALIGNMENT_256TB ideal-int
pkg syscall (netbsd-arm64-cgo), const MAP_ALIGNMENT_4GB = 536870912
pkg syscall (netbsd-arm64-cgo), const MAP_ALIGNMENT_4GB ideal-int
pkg syscall (netbsd-arm64-cgo), const MAP_ALIGNMENT_64KB = 268435456
pkg syscall (netbsd-arm64-cgo), const MAP_ALIGNMENT_64KB ideal-int
pkg syscall (netbsd-arm64-cgo), const MAP_ALIGNMENT_64PB = 939524096
pkg syscall (netbsd-arm64-cgo), const MAP_ALIGNMENT_64PB ideal-int
pkg syscall (netbsd-arm64-cgo), const MAP_ALIGNMENT_MASK = -16777216
pkg syscall (netbsd-arm64-cgo), const MAP_ALIGNMENT_MASK ideal-int
pkg syscall (netbsd-arm64-cgo), const MAP_ALIGNMENT_SHIFT = 24
pkg syscall (netbsd-arm64-cgo), const MAP_ALIGNMENT_SHIFT ideal-int
pkg syscall (netbsd-arm64-cgo), const MAP_ANON = 4096
pkg syscall (netbsd-arm64-cgo), const MAP_ANON ideal-int
pkg syscall (netbsd-arm64-cgo), const MAP_FILE = 0
pkg syscall (netbsd-arm64-cgo), const MAP_FILE ideal-int
pkg syscall (netbsd-arm64-cgo), const MAP_FIXED = 16
pkg syscall (netbsd-arm64-cgo), const MAP_FIXED ideal-int
pkg syscall (netbsd-arm64-cgo), const MAP_HASSEMAPHORE = 512
pkg syscall (netbsd-arm64-cgo), const MAP_HASSEMAPHORE ideal-int
pkg syscall (netbsd-arm64-cgo), const MAP_INHERIT = 128
pkg syscall (netbsd-arm64-cgo), const MAP_INHERIT_COPY = 1
pkg syscall (netbsd-arm64-cgo), const MAP_INHERIT_COPY ideal-int
pkg syscall (netbsd-arm64-cgo), const MAP_INHERIT_DEFAULT = 1
pkg syscall (netbsd-arm64-cgo), const MAP_INHERIT_DEFAULT ideal-int
pkg syscall (netbsd-arm64-cgo), const MAP_INHERIT_DONATE_COPY = 3
pkg syscall (netbsd-arm64-cgo), const MAP_INHERIT_DONATE_COPY ideal-int
pkg syscall (netbsd-arm64-cgo), const MAP_INHERIT ideal-int
pkg syscall (netbsd-arm64-cgo), const MAP_INHERIT_NONE = 2
pkg syscall (netbsd-arm64-cgo), const MAP_INHERIT_NONE ideal-int
pkg syscall (netbsd-arm64-cgo), const MAP_INHERIT_SHARE = 0
pkg syscall (netbsd-arm64-cgo), const MAP_INHERIT_SHARE ideal-int
pkg syscall (netbsd-arm64-cgo), const MAP_NORESERVE = 64
pkg syscall (netbsd-arm64-cgo), const MAP_NORESERVE ideal-int
pkg syscall (netbsd-arm64-cgo), const MAP_PRIVATE = 2
pkg syscall (netbsd-arm64-cgo), const MAP_PRIVATE ideal-int
pkg syscall (netbsd-arm64-cgo), const MAP_RENAME = 32
pkg syscall (netbsd-arm64-cgo), const MAP_RENAME ideal-int
pkg syscall (netbsd-arm64-cgo), const MAP_SHARED = 1
pkg syscall (netbsd-arm64-cgo), const MAP_SHARED ideal-int
pkg syscall (netbsd-arm64-cgo), const MAP_STACK = 8192
pkg syscall (netbsd-arm64-cgo), const MAP_STACK ideal-int
pkg syscall (netbsd-arm64-cgo), const MAP_TRYFIXED = 1024
pkg syscall (netbsd-arm64-cgo), const MAP_TRYFIXED ideal-int
pkg syscall (netbsd-arm64-cgo), const MAP_WIRED = 2048
pkg syscall (netbsd-arm64-cgo), const MAP_WIRED ideal-int
pkg syscall (netbsd-arm64-cgo), const MCL_CURRENT = 1
pkg syscall (netbsd-arm64-cgo), const MCL_CURRENT ideal-int
pkg syscall (netbsd-arm64-cgo), const MCL_FUTURE = 2
pkg syscall (netbsd-arm64-cgo), const MCL_FUTURE ideal-int
pkg syscall (netbsd-arm64-cgo), const MS_ASYNC = 1
pkg syscall (netbsd-arm64-cgo), const MS_ASYNC ideal-int
pkg syscall (netbsd-arm64-cgo), const MSG_BCAST = 256
pkg syscall (netbsd-arm64-cgo), const MSG_BCAST ideal-int
pkg syscall (netbsd-arm64-cgo), const MSG_CMSG_CLOEXEC = 2048
pkg syscall (netbsd-arm64-cgo), const MSG_CMSG_CLOEXEC ideal-int
pkg syscall (netbsd-arm64-cgo), const MSG_CONTROLMBUF = 33554432
pkg syscall (netbsd-arm64-cgo), const MSG_CONTROLMBUF ideal-int
pkg syscall (netbsd-arm64-cgo), const MSG_CTRUNC = 32
pkg syscall (netbsd-arm64-cgo), const MSG_CTRUNC ideal-int
pkg syscall (netbsd-arm64-cgo), const MSG_DONTROUTE = 4
pkg syscall (netbsd-arm64-cgo), const MSG_DONTROUTE ideal-int
pkg syscall (netbsd-arm64-cgo), const MSG_DONTWAIT = 128
pkg syscall (netbsd-arm64-cgo), const MSG_DONTWAIT ideal-int
pkg syscall (netbsd-arm64-cgo), const MSG_EOR = 8
pkg syscall (netbsd-arm64-cgo), const MSG_EOR ideal-int
pkg syscall (netbsd-arm64-cgo), const MSG_IOVUSRSPACE = 67108864
pkg syscall (netbsd-arm64-cgo), const MSG_IOVUSRSPACE ideal-int
pkg syscall (netbsd-arm64-cgo), const MSG_LENUSRSPACE = 134217728
pkg syscall (netbsd-arm64-cgo), const MSG_LENUSRSPACE ideal-int
pkg syscall (netbsd-arm64-cgo), const MSG_MCAST = 512
pkg syscall (netbsd-arm64-cgo), const MSG_MCAST ideal-int
pkg syscall (netbsd-arm64-cgo), const MSG_NAMEMBUF = 16777216
pkg syscall (netbsd-arm64-cgo), const MSG_NAMEMBUF ideal-int
pkg syscall (netbsd-arm64-cgo), const MSG_NBIO = 4096
pkg syscall (netbsd-arm64-cgo), const MSG_NBIO ideal-int
pkg syscall (netbsd-arm64-cgo), const MSG_NOSIGNAL = 1024
pkg syscall (netbsd-arm64-cgo), const MSG_NOSIGNAL ideal-int
pkg syscall (netbsd-arm64-cgo), const MSG_OOB = 1
pkg syscall (netbsd-arm64-cgo), const MSG_OOB ideal-int
pkg syscall (netbsd-arm64-cgo), const MSG_PEEK = 2
pkg syscall (netbsd-arm64-cgo), const MSG_PEEK ideal-int
pkg syscall (netbsd-arm64-cgo), const MSG_TRUNC = 16
pkg syscall (netbsd-arm64-cgo), const MSG_TRUNC ideal-int
pkg syscall (netbsd-arm64-cgo), const MSG_USERFLAGS = 16777215
pkg syscall (netbsd-arm64-cgo), const MSG_USERFLAGS ideal-int
pkg syscall (netbsd-arm64-cgo), const MSG_WAITALL = 64
pkg syscall (netbsd-arm64-cgo), const MSG_WAITALL ideal-int
pkg syscall (netbsd-arm64-cgo), const MS_INVALIDATE = 2
pkg syscall (netbsd-arm64-cgo), const MS_INVALIDATE ideal-int
pkg syscall (netbsd-arm64-cgo), const MS_SYNC = 4
pkg syscall (netbsd-arm64-cgo), const MS_SYNC ideal-int
pkg syscall (netbsd-arm64-cgo), const NAME_MAX = 511
pkg syscall (netbsd-arm64-cgo), const NAME_MAX ideal-int
pkg syscall (netbsd-arm64-cgo), const NET_RT_DUMP = 1
pkg syscall (netbsd-arm64-cgo), const NET_RT_DUMP ideal-int
pkg syscall (netbsd-arm64-cgo), const NET_RT_FLAGS = 2
pkg syscall (netbsd-arm64-cgo), const NET_RT_FLAGS ideal-int
pkg syscall (netbsd-arm64-cgo), const NET_RT_IFLIST = 5
pkg syscall (netbsd-arm64-cgo), const NET_RT_IFLIST ideal-int
pkg syscall (netbsd-arm64-cgo), const NET_RT_MAXID = 6
pkg syscall (netbsd-arm64-cgo), const NET_RT_MAXID ideal-int
pkg syscall (netbsd-arm64-cgo), const NET_RT_OIFLIST = 4
pkg syscall (netbsd-arm64-cgo), const NET_RT_OIFLIST ideal-int
pkg syscall (netbsd-arm64-cgo), const NET_RT_OOIFLIST = 3
pkg syscall (netbsd-arm64-cgo), const NET_RT_OOIFLIST ideal-int
pkg syscall (netbsd-arm64-cgo), const NOFLSH = 2147483648
pkg syscall (netbsd-arm64-cgo), const NOFLSH ideal-int
pkg syscall (netbsd-arm64-cgo), const NOTE_ATTRIB = 8
pkg syscall (netbsd-arm64-cgo), const NOTE_ATTRIB ideal-int
pkg syscall (netbsd-arm64-cgo), const NOTE_CHILD = 4
pkg syscall (netbsd-arm64-cgo), const NOTE_CHILD ideal-int
pkg syscall (netbsd-arm64-cgo), const NOTE_DELETE = 1
pkg syscall (netbsd-arm64-cgo), const NOTE_DELETE ideal-int
pkg syscall (netbsd-arm64-cgo), const NOTE_EXEC = 536870912
pkg syscall (netbsd-arm64-cgo), const NOTE_EXEC ideal-int
pkg syscall (netbsd-arm64-cgo), const NOTE_EXIT = 2147483648
pkg syscall (netbsd-arm64-cgo), const NOTE_EXIT ideal-int
pkg syscall (netbsd-arm64-cgo), const NOTE_EXTEND = 4
pkg syscall (netbsd-arm64-cgo), const NOTE_EXTEND ideal-int
pkg syscall (netbsd-arm64-cgo), const NOTE_FORK = 1073741824
pkg syscall (netbsd-arm64-cgo), const NOTE_FORK ideal-int
pkg syscall (netbsd-arm64-cgo), const NOTE_LINK = 16
pkg syscall (netbsd-arm64-cgo), const NOTE_LINK ideal-int
pkg syscall (netbsd-arm64-cgo), const NOTE_LOWAT = 1
pkg syscall (netbsd-arm64-cgo), const NOTE_LOWAT ideal-int
pkg syscall (netbsd-arm64-cgo), const NOTE_PCTRLMASK = 4026531840
pkg syscall (netbsd-arm64-cgo), const NOTE_PCTRLMASK ideal-int
pkg syscall (netbsd-arm64-cgo), const NOTE_PDATAMASK = 1048575
pkg syscall (netbsd-arm64-cgo), const NOTE_PDATAMASK ideal-int
pkg syscall (netbsd-arm64-cgo), const NOTE_RENAME = 32
pkg syscall (netbsd-arm64-cgo), const NOTE_RENAME ideal-int
pkg syscall (netbsd-arm64-cgo), const NOTE_REVOKE = 64
pkg syscall (netbsd-arm64-cgo), const NOTE_REVOKE ideal-int
pkg syscall (netbsd-arm64-cgo), const NOTE_TRACK = 1
pkg syscall (netbsd-arm64-cgo), const NOTE_TRACKERR = 2
pkg syscall (netbsd-arm64-cgo), const NOTE_TRACKERR ideal-int
pkg syscall (netbsd-arm64-cgo), const NOTE_TRACK ideal-int
pkg syscall (netbsd-arm64-cgo), const NOTE_WRITE = 2
pkg syscall (netbsd-arm64-cgo), const NOTE_WRITE ideal-int
pkg syscall (netbsd-arm64-cgo), const O_ACCMODE = 3
pkg syscall (netbsd-arm64-cgo), const O_ACCMODE ideal-int
pkg syscall (netbsd-arm64-cgo), const O_ALT_IO = 262144
pkg syscall (netbsd-arm64-cgo), const O_ALT_IO ideal-int
pkg syscall (netbsd-arm64-cgo), const O_APPEND = 8
pkg syscall (netbsd-arm64-cgo), const O_ASYNC = 64
pkg syscall (netbsd-arm64-cgo), const O_CLOEXEC = 4194304
pkg syscall (netbsd-arm64-cgo), const O_CREAT = 512
pkg syscall (netbsd-arm64-cgo), const OCRNL = 16
pkg syscall (netbsd-arm64-cgo), const OCRNL ideal-int
pkg syscall (netbsd-arm64-cgo), const O_DIRECT = 524288
pkg syscall (netbsd-arm64-cgo), const O_DIRECT ideal-int
pkg syscall (netbsd-arm64-cgo), const O_DIRECTORY = 2097152
pkg syscall (netbsd-arm64-cgo), const O_DIRECTORY ideal-int
pkg syscall (netbsd-arm64-cgo), const O_DSYNC = 65536
pkg syscall (netbsd-arm64-cgo), const O_DSYNC ideal-int
pkg syscall (netbsd-arm64-cgo), const O_EXCL = 2048
pkg syscall (netbsd-arm64-cgo), const O_EXLOCK = 32
pkg syscall (netbsd-arm64-cgo), const O_EXLOCK ideal-int
pkg syscall (netbsd-arm64-cgo), const OFIOGETBMAP = 3221513850
pkg syscall (netbsd-arm64-cgo), const OFIOGETBMAP ideal-int
pkg syscall (netbsd-arm64-cgo), const O_FSYNC = 128
pkg syscall (netbsd-arm64-cgo), const O_FSYNC ideal-int
pkg syscall (netbsd-arm64-cgo), const O_NDELAY = 4
pkg syscall (netbsd-arm64-cgo), const O_NDELAY ideal-int
pkg syscall (netbsd-arm64-cgo), const ONLCR = 2
pkg syscall (netbsd-arm64-cgo), const ONLCR ideal-int
pkg syscall (netbsd-arm64-cgo), const ONLRET = 64
pkg syscall (netbsd-arm64-cgo), const ONLRET ideal-int
pkg syscall (netbsd-arm64-cgo), const ONOCR = 32
pkg syscall (netbsd-arm64-cgo), const ONOCR ideal-int
pkg syscall (netbsd-arm64-cgo), const O_NOCTTY = 32768
pkg syscall (netbsd-arm64-cgo), const ONOEOT = 8
pkg syscall (netbsd-arm64-cgo), const ONOEOT ideal-int
pkg syscall (netbsd-arm64-cgo), const O_NOFOLLOW = 256
pkg syscall (netbsd-arm64-cgo), const O_NOFOLLOW ideal-int
pkg syscall (netbsd-arm64-cgo), const O_NONBLOCK = 4
pkg syscall (netbsd-arm64-cgo), const O_NOSIGPIPE = 16777216
pkg syscall (netbsd-arm64-cgo), const O_NOSIGPIPE ideal-int
pkg syscall (netbsd-arm64-cgo), const OPOST = 1
pkg syscall (netbsd-arm64-cgo), const OPOST ideal-int
pkg syscall (netbsd-arm64-cgo), const O_RSYNC = 131072
pkg syscall (netbsd-arm64-cgo), const O_RSYNC ideal-int
pkg syscall (netbsd-arm64-cgo), const O_SHLOCK = 16
pkg syscall (netbsd-arm64-cgo), const O_SHLOCK ideal-int
pkg syscall (netbsd-arm64-cgo), const O_SYNC = 128
pkg syscall (netbsd-arm64-cgo), const O_TRUNC = 1024
pkg syscall (netbsd-arm64-cgo), const PARENB = 4096
pkg syscall (netbsd-arm64-cgo), const PARENB ideal-int
pkg syscall (netbsd-arm64-cgo), const PARMRK = 8
pkg syscall (netbsd-arm64-cgo), const PARMRK ideal-int
pkg syscall (netbsd-arm64-cgo), const PARODD = 8192
pkg syscall (netbsd-arm64-cgo), const PARODD ideal-int
pkg syscall (netbsd-arm64-cgo), const PENDIN = 536870912
pkg syscall (netbsd-arm64-cgo), const PENDIN ideal-int
pkg syscall (netbsd-arm64-cgo), const PRI_IOFLUSH = 124
pkg syscall (netbsd-arm64-cgo), const PRI_IOFLUSH ideal-int
pkg syscall (netbsd-arm64-cgo), const PRIO_PGRP = 1
pkg syscall (netbsd-arm64-cgo), const PRIO_PGRP ideal-int
pkg syscall (netbsd-arm64-cgo), const PRIO_PROCESS = 0
pkg syscall (netbsd-arm64-cgo), const PRIO_PROCESS ideal-int
pkg syscall (netbsd-arm64-cgo), const PRIO_USER = 2
pkg syscall (netbsd-arm64-cgo), const PRIO_USER ideal-int
pkg syscall (netbsd-arm64-cgo), const PROT_EXEC = 4
pkg syscall (netbsd-arm64-cgo), const PROT_EXEC ideal-int
pkg syscall (netbsd-arm64-cgo), const PROT_NONE = 0
pkg syscall (netbsd-arm64-cgo), const PROT_NONE ideal-int
pkg syscall (netbsd-arm64-cgo), const PROT_READ = 1
pkg syscall (netbsd-arm64-cgo), const PROT_READ ideal-int
pkg syscall (netbsd-arm64-cgo), const PROT_WRITE = 2
pkg syscall (netbsd-arm64-cgo), const PROT_WRITE ideal-int
pkg syscall (netbsd-arm64-cgo), const PTRACE_CONT = 7
pkg syscall (netbsd-arm64-cgo), const PTRACE_CONT ideal-int
pkg syscall (netbsd-arm64-cgo), const PTRACE_KILL = 8
pkg syscall (netbsd-arm64-cgo), const PTRACE_KILL ideal-int
pkg syscall (netbsd-arm64-cgo), const PTRACE_TRACEME = 0
pkg syscall (netbsd-arm64-cgo), const PTRACE_TRACEME ideal-int
pkg syscall (netbsd-arm64-cgo), const RLIM_INFINITY = 9223372036854775807
pkg syscall (netbsd-arm64-cgo), const RLIM_INFINITY ideal-int
pkg syscall (netbsd-arm64-cgo), const RLIMIT_AS = 10
pkg syscall (netbsd-arm64-cgo), const RLIMIT_AS ideal-int
pkg syscall (netbsd-arm64-cgo), const RLIMIT_CORE = 4
pkg syscall (netbsd-arm64-cgo), const RLIMIT_CORE ideal-int
pkg syscall (netbsd-arm64-cgo), const RLIMIT_CPU = 0
pkg syscall (netbsd-arm64-cgo), const RLIMIT_CPU ideal-int
pkg syscall (netbsd-arm64-cgo), const RLIMIT_DATA = 2
pkg syscall (netbsd-arm64-cgo), const RLIMIT_DATA ideal-int
pkg syscall (netbsd-arm64-cgo), const RLIMIT_FSIZE = 1
pkg syscall (netbsd-arm64-cgo), const RLIMIT_FSIZE ideal-int
pkg syscall (netbsd-arm64-cgo), const RLIMIT_NOFILE = 8
pkg syscall (netbsd-arm64-cgo), const RLIMIT_NOFILE ideal-int
pkg syscall (netbsd-arm64-cgo), const RLIMIT_STACK = 3
pkg syscall (netbsd-arm64-cgo), const RLIMIT_STACK ideal-int
pkg syscall (netbsd-arm64-cgo), const RTA_AUTHOR = 64
pkg syscall (netbsd-arm64-cgo), const RTA_AUTHOR ideal-int
pkg syscall (netbsd-arm64-cgo), const RTA_BRD = 128
pkg syscall (netbsd-arm64-cgo), const RTA_BRD ideal-int
pkg syscall (netbsd-arm64-cgo), const RTA_DST = 1
pkg syscall (netbsd-arm64-cgo), const RTA_DST ideal-int
pkg syscall (netbsd-arm64-cgo), const RTA_GATEWAY = 2
pkg syscall (netbsd-arm64-cgo), const RTA_GATEWAY ideal-int
pkg syscall (netbsd-arm64-cgo), const RTA_GENMASK = 8
pkg syscall (netbsd-arm64-cgo), const RTA_GENMASK ideal-int
pkg syscall (netbsd-arm64-cgo), const RTA_IFA = 32
pkg syscall (netbsd-arm64-cgo), const RTA_IFA ideal-int
pkg syscall (netbsd-arm64-cgo), const RTA_IFP = 16
pkg syscall (netbsd-arm64-cgo), const RTA_IFP ideal-int
pkg syscall (netbsd-arm64-cgo), const RTA_NETMASK = 4
pkg syscall (netbsd-arm64-cgo), const RTA_NETMASK ideal-int
pkg syscall (netbsd-arm64-cgo), const RTA_TAG = 256
pkg syscall (netbsd-arm64-cgo), const RTA_TAG ideal-int
pkg syscall (netbsd-arm64-cgo), const RTAX_AUTHOR = 6
pkg syscall (netbsd-arm64-cgo), const RTAX_AUTHOR ideal-int
pkg syscall (netbsd-arm64-cgo), const RTAX_BRD = 7
pkg syscall (netbsd-arm64-cgo), const RTAX_BRD ideal-int
pkg syscall (netbsd-arm64-cgo), const RTAX_DST = 0
pkg syscall (netbsd-arm64-cgo), const RTAX_DST ideal-int
pkg syscall (netbsd-arm64-cgo), const RTAX_GATEWAY = 1
pkg syscall (netbsd-arm64-cgo), const RTAX_GATEWAY ideal-int
pkg syscall (netbsd-arm64-cgo), const RTAX_GENMASK = 3
pkg syscall (netbsd-arm64-cgo), const RTAX_GENMASK ideal-int
pkg syscall (netbsd-arm64-cgo), const RTAX_IFA = 5
pkg syscall (netbsd-arm64-cgo), const RTAX_IFA ideal-int
pkg syscall (netbsd-arm64-cgo), const RTAX_IFP = 4
pkg syscall (netbsd-arm64-cgo), const RTAX_IFP ideal-int
pkg syscall (netbsd-arm64-cgo), const RTAX_MAX = 9
pkg syscall (netbsd-arm64-cgo), const RTAX_MAX ideal-int
pkg syscall (netbsd-arm64-cgo), const RTAX_NETMASK = 2
pkg syscall (netbsd-arm64-cgo), const RTAX_NETMASK ideal-int
pkg syscall (netbsd-arm64-cgo), const RTAX_TAG = 8
pkg syscall (netbsd-arm64-cgo), const RTAX_TAG ideal-int
pkg syscall (netbsd-arm64-cgo), const RTF_ANNOUNCE = 131072
pkg syscall (netbsd-arm64-cgo), const RTF_ANNOUNCE ideal-int
pkg syscall (netbsd-arm64-cgo), const RTF_BLACKHOLE = 4096
pkg syscall (netbsd-arm64-cgo), const RTF_BLACKHOLE ideal-int
pkg syscall (netbsd-arm64-cgo), const RTF_CLONED = 8192
pkg syscall (netbsd-arm64-cgo), const RTF_CLONED ideal-int
pkg syscall (netbsd-arm64-cgo), const RTF_CLONING = 256
pkg syscall (netbsd-arm64-cgo), const RTF_CLONING ideal-int
pkg syscall (netbsd-arm64-cgo), const RTF_DONE = 64
pkg syscall (netbsd-arm64-cgo), const RTF_DONE ideal-int
pkg syscall (netbsd-arm64-cgo), const RTF_DYNAMIC = 16
pkg syscall (netbsd-arm64-cgo), const RTF_DYNAMIC ideal-int
pkg syscall (netbsd-arm64-cgo), const RTF_GATEWAY = 2
pkg syscall (netbsd-arm64-cgo), const RTF_GATEWAY ideal-int
pkg syscall (netbsd-arm64-cgo), const RTF_HOST = 4
pkg syscall (netbsd-arm64-cgo), const RTF_HOST ideal-int
pkg syscall (netbsd-arm64-cgo), const RTF_LLINFO = 1024
pkg syscall (netbsd-arm64-cgo), const RTF_LLINFO ideal-int
pkg syscall (netbsd-arm64-cgo), const RTF_MASK = 128
pkg syscall (netbsd-arm64-cgo), const RTF_MASK ideal-int
pkg syscall (netbsd-arm64-cgo), const RTF_MODIFIED = 32
pkg syscall (netbsd-arm64-cgo), const RTF_MODIFIED ideal-int
pkg syscall (netbsd-arm64-cgo), const RTF_PROTO1 = 32768
pkg syscall (netbsd-arm64-cgo), const RTF_PROTO1 ideal-int
pkg syscall (netbsd-arm64-cgo), const RTF_PROTO2 = 16384
pkg syscall (netbsd-arm64-cgo), const RTF_PROTO2 ideal-int
pkg syscall (netbsd-arm64-cgo), const RTF_REJECT = 8
pkg syscall (netbsd-arm64-cgo), const RTF_REJECT ideal-int
pkg syscall (netbsd-arm64-cgo), const RTF_SRC = 65536
pkg syscall (netbsd-arm64-cgo), const RTF_SRC ideal-int
pkg syscall (netbsd-arm64-cgo), const RTF_STATIC = 2048
pkg syscall (netbsd-arm64-cgo), const RTF_STATIC ideal-int
pkg syscall (netbsd-arm64-cgo), const RTF_UP = 1
pkg syscall (netbsd-arm64-cgo), const RTF_UP ideal-int
pkg syscall (netbsd-arm64-cgo), const RTF_XRESOLVE = 512
pkg syscall (netbsd-arm64-cgo), const RTF_XRESOLVE ideal-int
pkg syscall (netbsd-arm64-cgo), const RTM_ADD = 1
pkg syscall (netbsd-arm64-cgo), const RTM_ADD ideal-int
pkg syscall (netbsd-arm64-cgo), const RTM_CHANGE = 3
pkg syscall (netbsd-arm64-cgo), const RTM_CHANGE ideal-int
pkg syscall (netbsd-arm64-cgo), const RTM_CHGADDR = 21
pkg syscall (netbsd-arm64-cgo), const RTM_CHGADDR ideal-int
pkg syscall (netbsd-arm64-cgo), const RTM_DELADDR = 13
pkg syscall (netbsd-arm64-cgo), const RTM_DELADDR ideal-int
pkg syscall (netbsd-arm64-cgo), const RTM_DELETE = 2
pkg syscall (netbsd-arm64-cgo), const RTM_DELETE ideal-int
pkg syscall (netbsd-arm64-cgo), const RTM_GET = 4
pkg syscall (netbsd-arm64-cgo), const RTM_GET ideal-int
pkg syscall (netbsd-arm64-cgo), const RTM_IEEE80211 = 17
pkg syscall (netbsd-arm64-cgo), const RTM_IEEE80211 ideal-int
pkg syscall (netbsd-arm64-cgo), const RTM_IFANNOUNCE = 16
pkg syscall (netbsd-arm64-cgo), const RTM_IFANNOUNCE ideal-int
pkg syscall (netbsd-arm64-cgo), const RTM_IFINFO = 20
pkg syscall (netbsd-arm64-cgo), const RTM_IFINFO ideal-int
pkg syscall (netbsd-arm64-cgo), const RTM_LLINFO_UPD = 19
pkg syscall (netbsd-arm64-cgo), const RTM_LLINFO_UPD ideal-int
pkg syscall (netbsd-arm64-cgo), const RTM_LOCK = 8
pkg syscall (netbsd-arm64-cgo), const RTM_LOCK ideal-int
pkg syscall (netbsd-arm64-cgo), const RTM_LOSING = 5
pkg syscall (netbsd-arm64-cgo), const RTM_LOSING ideal-int
pkg syscall (netbsd-arm64-cgo), const RTM_MISS = 7
pkg syscall (netbsd-arm64-cgo), const RTM_MISS ideal-int
pkg syscall (netbsd-arm64-cgo), const RTM_NEWADDR = 12
pkg syscall (netbsd-arm64-cgo), const RTM_NEWADDR ideal-int
pkg syscall (netbsd-arm64-cgo), const RTM_OIFINFO = 15
pkg syscall (netbsd-arm64-cgo), const RTM_OIFINFO ideal-int
pkg syscall (netbsd-arm64-cgo), const RTM_OLDADD = 9
pkg syscall (netbsd-arm64-cgo), const RTM_OLDADD ideal-int
pkg syscall (netbsd-arm64-cgo), const RTM_OLDDEL = 10
pkg syscall (netbsd-arm64-cgo), const RTM_OLDDEL ideal-int
pkg syscall (netbsd-arm64-cgo), const RTM_OOIFINFO = 14
pkg syscall (netbsd-arm64-cgo), const RTM_OOIFINFO ideal-int
pkg syscall (netbsd-arm64-cgo), const RTM_REDIRECT = 6
pkg syscall (netbsd-arm64-cgo), const RTM_REDIRECT ideal-int
pkg syscall (netbsd-arm64-cgo), const RTM_RESOLVE = 11
pkg syscall (netbsd-arm64-cgo), const RTM_RESOLVE ideal-int
pkg syscall (netbsd-arm64-cgo), const RTM_RTTUNIT = 1000000
pkg syscall (netbsd-arm64-cgo), const RTM_RTTUNIT ideal-int
pkg syscall (netbsd-arm64-cgo), const RTM_SETGATE = 18
pkg syscall (netbsd-arm64-cgo), const RTM_SETGATE ideal-int
pkg syscall (netbsd-arm64-cgo), const RTM_VERSION = 4
pkg syscall (netbsd-arm64-cgo), const RTM_VERSION ideal-int
pkg syscall (netbsd-arm64-cgo), const RTV_EXPIRE = 4
pkg syscall (netbsd-arm64-cgo), const RTV_EXPIRE ideal-int
pkg syscall (netbsd-arm64-cgo), const RTV_HOPCOUNT = 2
pkg syscall (netbsd-arm64-cgo), const RTV_HOPCOUNT ideal-int
pkg syscall (netbsd-arm64-cgo), const RTV_MTU = 1
pkg syscall (netbsd-arm64-cgo), const RTV_MTU ideal-int
pkg syscall (netbsd-arm64-cgo), const RTV_RPIPE = 8
pkg syscall (netbsd-arm64-cgo), const RTV_RPIPE ideal-int
pkg syscall (netbsd-arm64-cgo), const RTV_RTT = 64
pkg syscall (netbsd-arm64-cgo), const RTV_RTT ideal-int
pkg syscall (netbsd-arm64-cgo), const RTV_RTTVAR = 128
pkg syscall (netbsd-arm64-cgo), const RTV_RTTVAR ideal-int
pkg syscall (netbsd-arm64-cgo), const RTV_SPIPE = 16
pkg syscall (netbsd-arm64-cgo), const RTV_SPIPE ideal-int
pkg syscall (netbsd-arm64-cgo), const RTV_SSTHRESH = 32
pkg syscall (netbsd-arm64-cgo), const RTV_SSTHRESH ideal-int
pkg syscall (netbsd-arm64-cgo), const RUSAGE_CHILDREN = -1
pkg syscall (netbsd-arm64-cgo), const RUSAGE_CHILDREN ideal-int
pkg syscall (netbsd-arm64-cgo), const RUSAGE_SELF = 0
pkg syscall (netbsd-arm64-cgo), const RUSAGE_SELF ideal-int
pkg syscall (netbsd-arm64-cgo), const S_ARCH1 = 65536
pkg syscall (netbsd-arm64-cgo), const S_ARCH1 ideal-int
pkg syscall (netbsd-arm64-cgo), const S_ARCH2 = 131072
pkg syscall (netbsd-arm64-cgo), const S_ARCH2 ideal-int
pkg syscall (netbsd-arm64-cgo), const S_BLKSIZE = 512
pkg syscall (netbsd-arm64-cgo), const S_BLKSIZE ideal-int
pkg syscall (netbsd-arm64-cgo), const SCM_CREDS = 4
pkg syscall (netbsd-arm64-cgo), const SCM_CREDS ideal-int
pkg syscall (netbsd-arm64-cgo), const SCM_RIGHTS = 1
pkg syscall (netbsd-arm64-cgo), const SCM_RIGHTS ideal-int
pkg syscall (netbsd-arm64-cgo), const SCM_TIMESTAMP = 8
pkg syscall (netbsd-arm64-cgo), const SCM_TIMESTAMP ideal-int
pkg syscall (netbsd-arm64-cgo), const S_IEXEC = 64
pkg syscall (netbsd-arm64-cgo), const S_IEXEC ideal-int
pkg syscall (netbsd-arm64-cgo), const S_IFMT = 61440
pkg syscall (netbsd-arm64-cgo), const S_IFWHT = 57344
pkg syscall (netbsd-arm64-cgo), const S_IFWHT ideal-int
pkg syscall (netbsd-arm64-cgo), const SIGBUS = 10
pkg syscall (netbsd-arm64-cgo), const SIGCHLD = 20
pkg syscall (netbsd-arm64-cgo), const SIGCHLD Signal
pkg syscall (netbsd-arm64-cgo), const SIGCONT = 19
pkg syscall (netbsd-arm64-cgo), const SIGCONT Signal
pkg syscall (netbsd-arm64-cgo), const SIGEMT = 7
pkg syscall (netbsd-arm64-cgo), const SIGEMT Signal
pkg syscall (netbsd-arm64-cgo), const SIGINFO = 29
pkg syscall (netbsd-arm64-cgo), const SIGINFO Signal
pkg syscall (netbsd-arm64-cgo), const SIGIO = 23
pkg syscall (netbsd-arm64-cgo), const SIGIO Signal
pkg syscall (netbsd-arm64-cgo), const SIGIOT = 6
pkg syscall (netbsd-arm64-cgo), const SIGIOT Signal
pkg syscall (netbsd-arm64-cgo), const SIGPROF = 27
pkg syscall (netbsd-arm64-cgo), const SIGPROF Signal
pkg syscall (netbsd-arm64-cgo), const SIGPWR = 32
pkg syscall (netbsd-arm64-cgo), const SIGPWR Signal
pkg syscall (netbsd-arm64-cgo), const SIGSTOP = 17
pkg syscall (netbsd-arm64-cgo), const SIGSTOP Signal
pkg syscall (netbsd-arm64-cgo), const SIGSYS = 12
pkg syscall (netbsd-arm64-cgo), const SIGSYS Signal
pkg syscall (netbsd-arm64-cgo), const SIGTSTP = 18
pkg syscall (netbsd-arm64-cgo), const SIGTSTP Signal
pkg syscall (netbsd-arm64-cgo), const SIGTTIN = 21
pkg syscall (netbsd-arm64-cgo), const SIGTTIN Signal
pkg syscall (netbsd-arm64-cgo), const SIGTTOU = 22
pkg syscall (netbsd-arm64-cgo), const SIGTTOU Signal
pkg syscall (netbsd-arm64-cgo), const SIGURG = 16
pkg syscall (netbsd-arm64-cgo), const SIGURG Signal
pkg syscall (netbsd-arm64-cgo), const SIGUSR1 = 30
pkg syscall (netbsd-arm64-cgo), const SIGUSR1 Signal
pkg syscall (netbsd-arm64-cgo), const SIGUSR2 = 31
pkg syscall (netbsd-arm64-cgo), const SIGUSR2 Signal
pkg syscall (netbsd-arm64-cgo), const SIGVTALRM = 26
pkg syscall (netbsd-arm64-cgo), const SIGVTALRM Signal
pkg syscall (netbsd-arm64-cgo), const SIGWINCH = 28
pkg syscall (netbsd-arm64-cgo), const SIGWINCH Signal
pkg syscall (netbsd-arm64-cgo), const SIGXCPU = 24
pkg syscall (netbsd-arm64-cgo), const SIGXCPU Signal
pkg syscall (netbsd-arm64-cgo), const SIGXFSZ = 25
pkg syscall (netbsd-arm64-cgo), const SIGXFSZ Signal
pkg syscall (netbsd-arm64-cgo), const SIOCADDMULTI = 2156947761
pkg syscall (netbsd-arm64-cgo), const SIOCADDMULTI ideal-int
pkg syscall (netbsd-arm64-cgo), const SIOCADDRT = 2151182858
pkg syscall (netbsd-arm64-cgo), const SIOCADDRT ideal-int
pkg syscall (netbsd-arm64-cgo), const SIOCAIFADDR = 2151704858
pkg syscall (netbsd-arm64-cgo), const SIOCAIFADDR ideal-int
pkg syscall (netbsd-arm64-cgo), const SIOCALIFADDR = 2165860636
pkg syscall (netbsd-arm64-cgo), const SIOCALIFADDR ideal-int
pkg syscall (netbsd-arm64-cgo), const SIOCATMARK = 1074033415
pkg syscall (netbsd-arm64-cgo), const SIOCATMARK ideal-int
pkg syscall (netbsd-arm64-cgo), const SIOCDELMULTI = 2156947762
pkg syscall (netbsd-arm64-cgo), const SIOCDELMULTI ideal-int
pkg syscall (netbsd-arm64-cgo), const SIOCDELRT = 2151182859
pkg syscall (netbsd-arm64-cgo), const SIOCDELRT ideal-int
pkg syscall (netbsd-arm64-cgo), const SIOCDIFADDR = 2156947737
pkg syscall (netbsd-arm64-cgo), const SIOCDIFADDR ideal-int
pkg syscall (netbsd-arm64-cgo), const SIOCDIFPHYADDR = 2156947785
pkg syscall (netbsd-arm64-cgo), const SIOCDIFPHYADDR ideal-int
pkg syscall (netbsd-arm64-cgo), const SIOCDLIFADDR = 2165860638
pkg syscall (netbsd-arm64-cgo), const SIOCDLIFADDR ideal-int
pkg syscall (netbsd-arm64-cgo), const SIOCGDRVSPEC = 3223873915
pkg syscall (netbsd-arm64-cgo), const SIOCGDRVSPEC ideal-int
pkg syscall (netbsd-arm64-cgo), const SIOCGETPFSYNC = 3230689784
pkg syscall (netbsd-arm64-cgo), const SIOCGETPFSYNC ideal-int
pkg syscall (netbsd-arm64-cgo), const SIOCGETSGCNT = 3223352628
pkg syscall (netbsd-arm64-cgo), const SIOCGETSGCNT ideal-int
pkg syscall (netbsd-arm64-cgo), const SIOCGETVIFCNT = 3223876915
pkg syscall (netbsd-arm64-cgo), const SIOCGETVIFCNT ideal-int
pkg syscall (netbsd-arm64-cgo), const SIOCGHIWAT = 1074033409
pkg syscall (netbsd-arm64-cgo), const SIOCGHIWAT ideal-int
pkg syscall (netbsd-arm64-cgo), const SIOCGIFADDR = 3230689569
pkg syscall (netbsd-arm64-cgo), const SIOCGIFADDR ideal-int
pkg syscall (netbsd-arm64-cgo), const SIOCGIFADDRPREF = 3231213856
pkg syscall (netbsd-arm64-cgo), const SIOCGIFADDRPREF ideal-int
pkg syscall (netbsd-arm64-cgo), const SIOCGIFALIAS = 3225446683
pkg syscall (netbsd-arm64-cgo), const SIOCGIFALIAS ideal-int
pkg syscall (netbsd-arm64-cgo), const SIOCGIFBRDADDR = 3230689571
pkg syscall (netbsd-arm64-cgo), const SIOCGIFBRDADDR ideal-int
pkg syscall (netbsd-arm64-cgo), const SIOCGIFCAP = 3223349622
pkg syscall (netbsd-arm64-cgo), const SIOCGIFCAP ideal-int
pkg syscall (netbsd-arm64-cgo), const SIOCGIFCONF = 3222300966
pkg syscall (netbsd-arm64-cgo), const SIOCGIFCONF ideal-int
pkg syscall (netbsd-arm64-cgo), const SIOCGIFDATA = 3231213957
pkg syscall (netbsd-arm64-cgo), const SIOCGIFDATA ideal-int
pkg syscall (netbsd-arm64-cgo), const SIOCGIFDLT = 3230689655
pkg syscall (netbsd-arm64-cgo), const SIOCGIFDLT ideal-int
pkg syscall (netbsd-arm64-cgo), const SIOCGIFDSTADDR = 3230689570
pkg syscall (netbsd-arm64-cgo), const SIOCGIFDSTADDR ideal-int
pkg syscall (netbsd-arm64-cgo), const SIOCGIFFLAGS = 3230689553
pkg syscall (netbsd-arm64-cgo), const SIOCGIFFLAGS ideal-int
pkg syscall (netbsd-arm64-cgo), const SIOCGIFGENERIC = 3230689594
pkg syscall (netbsd-arm64-cgo), const SIOCGIFGENERIC ideal-int
pkg syscall (netbsd-arm64-cgo), const SIOCGIFMEDIA = 3224398134
pkg syscall (netbsd-arm64-cgo), const SIOCGIFMEDIA ideal-int
pkg syscall (netbsd-arm64-cgo), const SIOCGIFMETRIC = 3230689559
pkg syscall (netbsd-arm64-cgo), const SIOCGIFMETRIC ideal-int
pkg syscall (netbsd-arm64-cgo), const SIOCGIFMTU = 3230689662
pkg syscall (netbsd-arm64-cgo), const SIOCGIFMTU ideal-int
pkg syscall (netbsd-arm64-cgo), const SIOCGIFNETMASK = 3230689573
pkg syscall (netbsd-arm64-cgo), const SIOCGIFNETMASK ideal-int
pkg syscall (netbsd-arm64-cgo), const SIOCGIFPDSTADDR = 3230689608
pkg syscall (netbsd-arm64-cgo), const SIOCGIFPDSTADDR ideal-int
pkg syscall (netbsd-arm64-cgo), const SIOCGIFPSRCADDR = 3230689607
pkg syscall (netbsd-arm64-cgo), const SIOCGIFPSRCADDR ideal-int
pkg syscall (netbsd-arm64-cgo), const SIOCGLIFADDR = 3239602461
pkg syscall (netbsd-arm64-cgo), const SIOCGLIFADDR ideal-int
pkg syscall (netbsd-arm64-cgo), const SIOCGLIFPHYADDR = 3239602507
pkg syscall (netbsd-arm64-cgo), const SIOCGLIFPHYADDR ideal-int
pkg syscall (netbsd-arm64-cgo), const SIOCGLINKSTR = 3223873927
pkg syscall (netbsd-arm64-cgo), const SIOCGLINKSTR ideal-int
pkg syscall (netbsd-arm64-cgo), const SIOCGLOWAT = 1074033411
pkg syscall (netbsd-arm64-cgo), const SIOCGLOWAT ideal-int
pkg syscall (netbsd-arm64-cgo), const SIOCGPGRP = 1074033417
pkg syscall (netbsd-arm64-cgo), const SIOCGPGRP ideal-int
pkg syscall (netbsd-arm64-cgo), const SIOCGVH = 3230689667
pkg syscall (netbsd-arm64-cgo), const SIOCGVH ideal-int
pkg syscall (netbsd-arm64-cgo), const SIOCIFCREATE = 2156947834
pkg syscall (netbsd-arm64-cgo), const SIOCIFCREATE ideal-int
pkg syscall (netbsd-arm64-cgo), const SIOCIFDESTROY = 2156947833
pkg syscall (netbsd-arm64-cgo), const SIOCIFDESTROY ideal-int
pkg syscall (netbsd-arm64-cgo), const SIOCIFGCLONERS = 3222301048
pkg syscall (netbsd-arm64-cgo), const SIOCIFGCLONERS ideal-int
pkg syscall (netbsd-arm64-cgo), const SIOCINITIFADDR = 3228592516
pkg syscall (netbsd-arm64-cgo), const SIOCINITIFADDR ideal-int
pkg syscall (netbsd-arm64-cgo), const SIOCSDRVSPEC = 2150132091
pkg syscall (netbsd-arm64-cgo), const SIOCSDRVSPEC ideal-int
pkg syscall (netbsd-arm64-cgo), const SIOCSETPFSYNC = 2156947959
pkg syscall (netbsd-arm64-cgo), const SIOCSETPFSYNC ideal-int
pkg syscall (netbsd-arm64-cgo), const SIOCSHIWAT = 2147775232
pkg syscall (netbsd-arm64-cgo), const SIOCSHIWAT ideal-int
pkg syscall (netbsd-arm64-cgo), const SIOCSIFADDR = 2156947724
pkg syscall (netbsd-arm64-cgo), const SIOCSIFADDR ideal-int
pkg syscall (netbsd-arm64-cgo), const SIOCSIFADDRPREF = 2157472031
pkg syscall (netbsd-arm64-cgo), const SIOCSIFADDRPREF ideal-int
pkg syscall (netbsd-arm64-cgo), const SIOCSIFBRDADDR = 2156947731
pkg syscall (netbsd-arm64-cgo), const SIOCSIFBRDADDR ideal-int
pkg syscall (netbsd-arm64-cgo), const SIOCSIFCAP = 2149607797
pkg syscall (netbsd-arm64-cgo), const SIOCSIFCAP ideal-int
pkg syscall (netbsd-arm64-cgo), const SIOCSIFDSTADDR = 2156947726
pkg syscall (netbsd-arm64-cgo), const SIOCSIFDSTADDR ideal-int
pkg syscall (netbsd-arm64-cgo), const SIOCSIFFLAGS = 2156947728
pkg syscall (netbsd-arm64-cgo), const SIOCSIFFLAGS ideal-int
pkg syscall (netbsd-arm64-cgo), const SIOCSIFGENERIC = 2156947769
pkg syscall (netbsd-arm64-cgo), const SIOCSIFGENERIC ideal-int
pkg syscall (netbsd-arm64-cgo), const SIOCSIFMEDIA = 3230689589
pkg syscall (netbsd-arm64-cgo), const SIOCSIFMEDIA ideal-int
pkg syscall (netbsd-arm64-cgo), const SIOCSIFMETRIC = 2156947736
pkg syscall (netbsd-arm64-cgo), const SIOCSIFMETRIC ideal-int
pkg syscall (netbsd-arm64-cgo), const SIOCSIFMTU = 2156947839
pkg syscall (netbsd-arm64-cgo), const SIOCSIFMTU ideal-int
pkg syscall (netbsd-arm64-cgo), const SIOCSIFNETMASK = 2156947734
pkg syscall (netbsd-arm64-cgo), const SIOCSIFNETMASK ideal-int
pkg syscall (netbsd-arm64-cgo), const SIOCSIFPHYADDR = 2151704902
pkg syscall (netbsd-arm64-cgo), const SIOCSIFPHYADDR ideal-int
pkg syscall (netbsd-arm64-cgo), const SIOCSLIFPHYADDR = 2165860682
pkg syscall (netbsd-arm64-cgo), const SIOCSLIFPHYADDR ideal-int
pkg syscall (netbsd-arm64-cgo), const SIOCSLINKSTR = 2150132104
pkg syscall (netbsd-arm64-cgo), const SIOCSLINKSTR ideal-int
pkg syscall (netbsd-arm64-cgo), const SIOCSLOWAT = 2147775234
pkg syscall (netbsd-arm64-cgo), const SIOCSLOWAT ideal-int
pkg syscall (netbsd-arm64-cgo), const SIOCSPGRP = 2147775240
pkg syscall (netbsd-arm64-cgo), const SIOCSPGRP ideal-int
pkg syscall (netbsd-arm64-cgo), const SIOCSVH = 3230689666
pkg syscall (netbsd-arm64-cgo), const SIOCSVH ideal-int
pkg syscall (netbsd-arm64-cgo), const SIOCZIFDATA = 3231213958
pkg syscall (netbsd-arm64-cgo), const SIOCZIFDATA ideal-int
pkg syscall (netbsd-arm64-cgo), const S_IREAD = 256
pkg syscall (netbsd-arm64-cgo), const S_IREAD ideal-int
pkg syscall (netbsd-arm64-cgo), const S_IRGRP = 32
pkg syscall (netbsd-arm64-cgo), const S_IRGRP ideal-int
pkg syscall (netbsd-arm64-cgo), const S_IROTH = 4
pkg syscall (netbsd-arm64-cgo), const S_IROTH ideal-int
pkg syscall (netbsd-arm64-cgo), const S_IRWXG = 56
pkg syscall (netbsd-arm64-cgo), const S_IRWXG ideal-int
pkg syscall (netbsd-arm64-cgo), const S_IRWXO = 7
pkg syscall (netbsd-arm64-cgo), const S_IRWXO ideal-int
pkg syscall (netbsd-arm64-cgo), const S_IRWXU = 448
pkg syscall (netbsd-arm64-cgo), const S_IRWXU ideal-int
pkg syscall (netbsd-arm64-cgo), const S_ISTXT = 512
pkg syscall (netbsd-arm64-cgo), const S_ISTXT ideal-int
pkg syscall (netbsd-arm64-cgo), const S_IWGRP = 16
pkg syscall (netbsd-arm64-cgo), const S_IWGRP ideal-int
pkg syscall (netbsd-arm64-cgo), const S_IWOTH = 2
pkg syscall (netbsd-arm64-cgo), const S_IWOTH ideal-int
pkg syscall (netbsd-arm64-cgo), const S_IWRITE = 128
pkg syscall (netbsd-arm64-cgo), const S_IWRITE ideal-int
pkg syscall (netbsd-arm64-cgo), const S_IXGRP = 8
pkg syscall (netbsd-arm64-cgo), const S_IXGRP ideal-int
pkg syscall (netbsd-arm64-cgo), const S_IXOTH = 1
pkg syscall (netbsd-arm64-cgo), const S_IXOTH ideal-int
pkg syscall (netbsd-arm64-cgo), const SizeofBpfHdr = 32
pkg syscall (netbsd-arm64-cgo), const SizeofBpfHdr ideal-int
pkg syscall (netbsd-arm64-cgo), const SizeofBpfInsn = 8
pkg syscall (netbsd-arm64-cgo), const SizeofBpfInsn ideal-int
pkg syscall (netbsd-arm64-cgo), const SizeofBpfProgram = 16
pkg syscall (netbsd-arm64-cgo), const SizeofBpfProgram ideal-int
pkg syscall (netbsd-arm64-cgo), const SizeofBpfStat = 128
pkg syscall (netbsd-arm64-cgo), const SizeofBpfStat ideal-int
pkg syscall (netbsd-arm64-cgo), const SizeofBpfVersion = 4
pkg syscall (netbsd-arm64-cgo), const SizeofBpfVersion ideal-int
pkg syscall (netbsd-arm64-cgo), const SizeofCmsghdr = 12
pkg syscall (netbsd-arm64-cgo), const SizeofCmsghdr ideal-int
pkg syscall (netbsd-arm64-cgo), const SizeofICMPv6Filter = 32
pkg syscall (netbsd-arm64-cgo), const SizeofICMPv6Filter ideal-int
pkg syscall (netbsd-arm64-cgo), const SizeofIfaMsghdr = 24
pkg syscall (netbsd-arm64-cgo), const SizeofIfaMsghdr ideal-int
pkg syscall (netbsd-arm64-cgo), const SizeofIfAnnounceMsghdr = 24
pkg syscall (netbsd-arm64-cgo), const SizeofIfAnnounceMsghdr ideal-int
pkg syscall (netbsd-arm64-cgo), const SizeofIfData = 136
pkg syscall (netbsd-arm64-cgo), const SizeofIfData ideal-int
pkg syscall (netbsd-arm64-cgo), const SizeofIfMsghdr = 152
pkg syscall (netbsd-arm64-cgo), const SizeofIfMsghdr ideal-int
pkg syscall (netbsd-arm64-cgo), const SizeofInet6Pktinfo = 20
pkg syscall (netbsd-arm64-cgo), const SizeofInet6Pktinfo ideal-int
pkg syscall (netbsd-arm64-cgo), const SizeofIPMreq = 8
pkg syscall (netbsd-arm64-cgo), const SizeofIPMreq ideal-int
pkg syscall (netbsd-arm64-cgo), const SizeofIPv6Mreq = 20
pkg syscall (netbsd-arm64-cgo), const SizeofIPv6Mreq ideal-int
pkg syscall (netbsd-arm64-cgo), const SizeofIPv6MTUInfo = 32
pkg syscall (netbsd-arm64-cgo), const SizeofIPv6MTUInfo ideal-int
pkg syscall (netbsd-arm64-cgo), const SizeofLinger = 8
pkg syscall (netbsd-arm64-cgo), const SizeofLinger ideal-int
pkg syscall (netbsd-arm64-cgo), const SizeofMsghdr = 48
pkg syscall (netbsd-arm64-cgo), const SizeofMsghdr ideal-int
pkg syscall (netbsd-arm64-cgo), const SizeofRtMetrics = 80
pkg syscall (netbsd-arm64-cgo), const SizeofRtMetrics ideal-int
pkg syscall (netbsd-arm64-cgo), const SizeofRtMsghdr = 120
pkg syscall (netbsd-arm64-cgo), const SizeofRtMsghdr ideal-int
pkg syscall (netbsd-arm64-cgo), const SizeofSockaddrAny = 108
pkg syscall (netbsd-arm64-cgo), const SizeofSockaddrAny ideal-int
pkg syscall (netbsd-arm64-cgo), const SizeofSockaddrDatalink = 20
pkg syscall (netbsd-arm64-cgo), const SizeofSockaddrDatalink ideal-int
pkg syscall (netbsd-arm64-cgo), const SizeofSockaddrInet4 = 16
pkg syscall (netbsd-arm64-cgo), const SizeofSockaddrInet4 ideal-int
pkg syscall (netbsd-arm64-cgo), const SizeofSockaddrInet6 = 28
pkg syscall (netbsd-arm64-cgo), const SizeofSockaddrInet6 ideal-int
pkg syscall (netbsd-arm64-cgo), const SizeofSockaddrUnix = 106
pkg syscall (netbsd-arm64-cgo), const SizeofSockaddrUnix ideal-int
pkg syscall (netbsd-arm64-cgo), const S_LOGIN_SET = 1
pkg syscall (netbsd-arm64-cgo), const S_LOGIN_SET ideal-int
pkg syscall (netbsd-arm64-cgo), const SO_ACCEPTCONN = 2
pkg syscall (netbsd-arm64-cgo), const SO_ACCEPTCONN ideal-int
pkg syscall (netbsd-arm64-cgo), const SO_ACCEPTFILTER = 4096
pkg syscall (netbsd-arm64-cgo), const SO_ACCEPTFILTER ideal-int
pkg syscall (netbsd-arm64-cgo), const SO_BROADCAST = 32
pkg syscall (netbsd-arm64-cgo), const SOCK_CLOEXEC = 268435456
pkg syscall (netbsd-arm64-cgo), const SOCK_CLOEXEC ideal-int
pkg syscall (netbsd-arm64-cgo), const SOCK_FLAGS_MASK = 4026531840
pkg syscall (netbsd-arm64-cgo), const SOCK_FLAGS_MASK ideal-int
pkg syscall (netbsd-arm64-cgo), const SOCK_NONBLOCK = 536870912
pkg syscall (netbsd-arm64-cgo), const SOCK_NONBLOCK ideal-int
pkg syscall (netbsd-arm64-cgo), const SOCK_NOSIGPIPE = 1073741824
pkg syscall (netbsd-arm64-cgo), const SOCK_NOSIGPIPE ideal-int
pkg syscall (netbsd-arm64-cgo), const SOCK_RDM = 4
pkg syscall (netbsd-arm64-cgo), const SOCK_RDM ideal-int
pkg syscall (netbsd-arm64-cgo), const SO_DEBUG = 1
pkg syscall (netbsd-arm64-cgo), const SO_DEBUG ideal-int
pkg syscall (netbsd-arm64-cgo), const SO_DONTROUTE = 16
pkg syscall (netbsd-arm64-cgo), const SO_ERROR = 4103
pkg syscall (netbsd-arm64-cgo), const SO_ERROR ideal-int
pkg syscall (netbsd-arm64-cgo), const SO_KEEPALIVE = 8
pkg syscall (netbsd-arm64-cgo), const SO_LINGER = 128
pkg syscall (netbsd-arm64-cgo), const SOL_SOCKET = 65535
pkg syscall (netbsd-arm64-cgo), const SOMAXCONN = 128
pkg syscall (netbsd-arm64-cgo), const SO_NOHEADER = 4106
pkg syscall (netbsd-arm64-cgo), const SO_NOHEADER ideal-int
pkg syscall (netbsd-arm64-cgo), const SO_NOSIGPIPE = 2048
pkg syscall (netbsd-arm64-cgo), const SO_NOSIGPIPE ideal-int
pkg syscall (netbsd-arm64-cgo), const SO_OOBINLINE = 256
pkg syscall (netbsd-arm64-cgo), const SO_OOBINLINE ideal-int
pkg syscall (netbsd-arm64-cgo), const SO_OVERFLOWED = 4105
pkg syscall (netbsd-arm64-cgo), const SO_OVERFLOWED ideal-int
pkg syscall (netbsd-arm64-cgo), const SO_RCVBUF = 4098
pkg syscall (netbsd-arm64-cgo), const SO_RCVLOWAT = 4100
pkg syscall (netbsd-arm64-cgo), const SO_RCVLOWAT ideal-int
pkg syscall (netbsd-arm64-cgo), const SO_RCVTIMEO = 4108
pkg syscall (netbsd-arm64-cgo), const SO_RCVTIMEO ideal-int
pkg syscall (netbsd-arm64-cgo), const SO_REUSEADDR = 4
pkg syscall (netbsd-arm64-cgo), const SO_REUSEPORT = 512
pkg syscall (netbsd-arm64-cgo), const SO_REUSEPORT ideal-int
pkg syscall (netbsd-arm64-cgo), const SO_SNDBUF = 4097
pkg syscall (netbsd-arm64-cgo), const SO_SNDLOWAT = 4099
pkg syscall (netbsd-arm64-cgo), const SO_SNDLOWAT ideal-int
pkg syscall (netbsd-arm64-cgo), const SO_SNDTIMEO = 4107
pkg syscall (netbsd-arm64-cgo), const SO_SNDTIMEO ideal-int
pkg syscall (netbsd-arm64-cgo), const SO_TIMESTAMP = 8192
pkg syscall (netbsd-arm64-cgo), const SO_TIMESTAMP ideal-int
pkg syscall (netbsd-arm64-cgo), const SO_TYPE = 4104
pkg syscall (netbsd-arm64-cgo), const SO_TYPE ideal-int
pkg syscall (netbsd-arm64-cgo), const SO_USELOOPBACK = 64
pkg syscall (netbsd-arm64-cgo), const SO_USELOOPBACK ideal-int
pkg syscall (netbsd-arm64-cgo), const SYS_ACCEPT = 30
pkg syscall (netbsd-arm64-cgo), const SYS_ACCEPT ideal-int
pkg syscall (netbsd-arm64-cgo), const SYS_ACCESS = 33
pkg syscall (netbsd-arm64-cgo), const SYS_ACCESS ideal-int
pkg syscall (netbsd-arm64-cgo), const SYS_ACCT = 51
pkg syscall (netbsd-arm64-cgo), const SYS_ACCT ideal-int
pkg syscall (netbsd-arm64-cgo), const SYS_ADJTIME = 421
pkg syscall (netbsd-arm64-cgo), const SYS_ADJTIME ideal-int
pkg syscall (netbsd-arm64-cgo), const SYS_BIND = 104
pkg syscall (netbsd-arm64-cgo), const SYS_BIND ideal-int
pkg syscall (netbsd-arm64-cgo), const SYS_BREAK = 17
pkg syscall (netbsd-arm64-cgo), const SYS_BREAK ideal-int
pkg syscall (netbsd-arm64-cgo), const SYS_CHDIR = 12
pkg syscall (netbsd-arm64-cgo), const SYS_CHDIR ideal-int
pkg syscall (netbsd-arm64-cgo), const SYS_CHFLAGS = 34
pkg syscall (netbsd-arm64-cgo), const SYS_CHFLAGS ideal-int
pkg syscall (netbsd-arm64-cgo), const SYS_CHMOD = 15
pkg syscall (netbsd-arm64-cgo), const SYS_CHMOD ideal-int
pkg syscall (netbsd-arm64-cgo), const SYS_CHOWN = 16
pkg syscall (netbsd-arm64-cgo), const SYS_CHOWN ideal-int
pkg syscall (netbsd-arm64-cgo), const SYS_CHROOT = 61
pkg syscall (netbsd-arm64-cgo), const SYS_CHROOT ideal-int
pkg syscall (netbsd-arm64-cgo), const SYS_CLOCK_GETRES = 429
pkg syscall (netbsd-arm64-cgo), const SYS_CLOCK_GETRES ideal-int
pkg syscall (netbsd-arm64-cgo), const SYS_CLOCK_GETTIME = 427
pkg syscall (netbsd-arm64-cgo), const SYS_CLOCK_GETTIME ideal-int
pkg syscall (netbsd-arm64-cgo), const SYS_CLOCK_SETTIME = 428
pkg syscall (netbsd-arm64-cgo), const SYS_CLOCK_SETTIME ideal-int
pkg syscall (netbsd-arm64-cgo), const SYS___CLONE = 287
pkg syscall (netbsd-arm64-cgo), const SYS___CLONE ideal-int
pkg syscall (netbsd-arm64-cgo), const SYS_CLOSE = 6
pkg syscall (netbsd-arm64-cgo), const SYS_CLOSE ideal-int
pkg syscall (netbsd-arm64-cgo), const SYS_CONNECT = 98
pkg syscall (netbsd-arm64-cgo), const SYS_CONNECT ideal-int
pkg syscall (netbsd-arm64-cgo), const SYSCTL_VERS_0 = 0
pkg syscall (netbsd-arm64-cgo), const SYSCTL_VERS_0 ideal-int
pkg syscall (netbsd-arm64-cgo), const SYSCTL_VERS_1 = 16777216
pkg syscall (netbsd-arm64-cgo), const SYSCTL_VERS_1 ideal-int
pkg syscall (netbsd-arm64-cgo), const SYSCTL_VERSION = 16777216
pkg syscall (netbsd-arm64-cgo), const SYSCTL_VERSION ideal-int
pkg syscall (netbsd-arm64-cgo), const SYSCTL_VERS_MASK = 4278190080
pkg syscall (netbsd-arm64-cgo), const SYSCTL_VERS_MASK ideal-int
pkg syscall (netbsd-arm64-cgo), const SYS_DUP2 = 90
pkg syscall (netbsd-arm64-cgo), const SYS_DUP2 ideal-int
pkg syscall (netbsd-arm64-cgo), const SYS_DUP3 = 454
pkg syscall (netbsd-arm64-cgo), const SYS_DUP3 ideal-int
pkg syscall (netbsd-arm64-cgo), const SYS_DUP = 41
pkg syscall (netbsd-arm64-cgo), const SYS_DUP ideal-int
pkg syscall (netbsd-arm64-cgo), const SYS_EXECVE = 59
pkg syscall (netbsd-arm64-cgo), const SYS_EXECVE ideal-int
pkg syscall (netbsd-arm64-cgo), const SYS_EXIT = 1
pkg syscall (netbsd-arm64-cgo), const SYS_EXIT ideal-int
pkg syscall (netbsd-arm64-cgo), const SYS_EXTATTRCTL = 360
pkg syscall (netbsd-arm64-cgo), const SYS_EXTATTRCTL ideal-int
pkg syscall (netbsd-arm64-cgo), const SYS_EXTATTR_DELETE_FD = 366
pkg syscall (netbsd-arm64-cgo), const SYS_EXTATTR_DELETE_FD ideal-int
pkg syscall (netbsd-arm64-cgo), const SYS_EXTATTR_DELETE_FILE = 363
pkg syscall (netbsd-arm64-cgo), const SYS_EXTATTR_DELETE_FILE ideal-int
pkg syscall (netbsd-arm64-cgo), const SYS_EXTATTR_DELETE_LINK = 369
pkg syscall (netbsd-arm64-cgo), const SYS_EXTATTR_DELETE_LINK ideal-int
pkg syscall (netbsd-arm64-cgo), const SYS_EXTATTR_GET_FD = 365
pkg syscall (netbsd-arm64-cgo), const SYS_EXTATTR_GET_FD ideal-int
pkg syscall (netbsd-arm64-cgo), const SYS_EXTATTR_GET_FILE = 362
pkg syscall (netbsd-arm64-cgo), const SYS_EXTATTR_GET_FILE ideal-int
pkg syscall (netbsd-arm64-cgo), const SYS_EXTATTR_GET_LINK = 368
pkg syscall (netbsd-arm64-cgo), const SYS_EXTATTR_GET_LINK ideal-int
pkg syscall (netbsd-arm64-cgo), const SYS_EXTATTR_LIST_FD = 370
pkg syscall (netbsd-arm64-cgo), const SYS_EXTATTR_LIST_FD ideal-int
pkg syscall (netbsd-arm64-cgo), const SYS_EXTATTR_LIST_FILE = 371
pkg syscall (netbsd-arm64-cgo), const SYS_EXTATTR_LIST_FILE ideal-int
pkg syscall (netbsd-arm64-cgo), const SYS_EXTATTR_LIST_LINK = 372
pkg syscall (netbsd-arm64-cgo), const SYS_EXTATTR_LIST_LINK ideal-int
pkg syscall (netbsd-arm64-cgo), const SYS_EXTATTR_SET_FD = 364
pkg syscall (netbsd-arm64-cgo), const SYS_EXTATTR_SET_FD ideal-int
pkg syscall (netbsd-arm64-cgo), const SYS_EXTATTR_SET_FILE = 361
pkg syscall (netbsd-arm64-cgo), const SYS_EXTATTR_SET_FILE ideal-int
pkg syscall (netbsd-arm64-cgo), const SYS_EXTATTR_SET_LINK = 367
pkg syscall (netbsd-arm64-cgo), const SYS_EXTATTR_SET_LINK ideal-int
pkg syscall (netbsd-arm64-cgo), const SYS_FACCESSAT = 462
pkg syscall (netbsd-arm64-cgo), const SYS_FACCESSAT ideal-int
pkg syscall (netbsd-arm64-cgo), const SYS_FCHDIR = 13
pkg syscall (netbsd-arm64-cgo), const SYS_FCHDIR ideal-int
pkg syscall (netbsd-arm64-cgo), const SYS_FCHFLAGS = 35
pkg syscall (netbsd-arm64-cgo), const SYS_FCHFLAGS ideal-int
pkg syscall (netbsd-arm64-cgo), const SYS_FCHMOD = 124
pkg syscall (netbsd-arm64-cgo), const SYS_FCHMODAT = 463
pkg syscall (netbsd-arm64-cgo), const SYS_FCHMODAT ideal-int
pkg syscall (netbsd-arm64-cgo), const SYS_FCHMOD ideal-int
pkg syscall (netbsd-arm64-cgo), const SYS_FCHOWN = 123
pkg syscall (netbsd-arm64-cgo), const SYS_FCHOWNAT = 464
pkg syscall (netbsd-arm64-cgo), const SYS_FCHOWNAT ideal-int
pkg syscall (netbsd-arm64-cgo), const SYS_FCHOWN ideal-int
pkg syscall (netbsd-arm64-cgo), const SYS_FCHROOT = 297
pkg syscall (netbsd-arm64-cgo), const SYS_FCHROOT ideal-int
pkg syscall (netbsd-arm64-cgo), const SYS_FCNTL = 92
pkg syscall (netbsd-arm64-cgo), const SYS_FCNTL ideal-int
pkg syscall (netbsd-arm64-cgo), const SYS_FDATASYNC = 241
pkg syscall (netbsd-arm64-cgo), const SYS_FDATASYNC ideal-int
pkg syscall (netbsd-arm64-cgo), const SYS_FEXECVE = 465
pkg syscall (netbsd-arm64-cgo), const SYS_FEXECVE ideal-int
pkg syscall (netbsd-arm64-cgo), const SYS_FGETXATTR = 380
pkg syscall (netbsd-arm64-cgo), const SYS_FGETXATTR ideal-int
pkg syscall (netbsd-arm64-cgo), const SYS_FHSTAT = 451
pkg syscall (netbsd-arm64-cgo), const SYS_FHSTAT ideal-int
pkg syscall (netbsd-arm64-cgo), const SYS_FKTRACE = 288
pkg syscall (netbsd-arm64-cgo), const SYS_FKTRACE ideal-int
pkg syscall (netbsd-arm64-cgo), const SYS_FLISTXATTR = 383
pkg syscall (netbsd-arm64-cgo), const SYS_FLISTXATTR ideal-int
pkg syscall (netbsd-arm64-cgo), const SYS_FLOCK = 131
pkg syscall (netbsd-arm64-cgo), const SYS_FLOCK ideal-int
pkg syscall (netbsd-arm64-cgo), const SYS_FORK = 2
pkg syscall (netbsd-arm64-cgo), const SYS_FORK ideal-int
pkg syscall (netbsd-arm64-cgo), const SYS_FPATHCONF = 192
pkg syscall (netbsd-arm64-cgo), const SYS_FPATHCONF ideal-int
pkg syscall (netbsd-arm64-cgo), const SYS_FREMOVEXATTR = 386
pkg syscall (netbsd-arm64-cgo), const SYS_FREMOVEXATTR ideal-int
pkg syscall (netbsd-arm64-cgo), const SYS_FSETXATTR = 377
pkg syscall (netbsd-arm64-cgo), const SYS_FSETXATTR ideal-int
pkg syscall (netbsd-arm64-cgo), const SYS_FSTAT = 440
pkg syscall (netbsd-arm64-cgo), const SYS_FSTATAT = 466
pkg syscall (netbsd-arm64-cgo), const SYS_FSTATAT ideal-int
pkg syscall (netbsd-arm64-cgo), const SYS_FSTAT ideal-int
pkg syscall (netbsd-arm64-cgo), const SYS_FSTATVFS1 = 358
pkg syscall (netbsd-arm64-cgo), const SYS_FSTATVFS1 ideal-int
pkg syscall (netbsd-arm64-cgo), const SYS_FSYNC = 95
pkg syscall (netbsd-arm64-cgo), const SYS_FSYNC ideal-int
pkg syscall (netbsd-arm64-cgo), const SYS_FSYNC_RANGE = 354
pkg syscall (netbsd-arm64-cgo), const SYS_FSYNC_RANGE ideal-int
pkg syscall (netbsd-arm64-cgo), const SYS_FTRUNCATE = 201
pkg syscall (netbsd-arm64-cgo), const SYS_FTRUNCATE ideal-int
pkg syscall (netbsd-arm64-cgo), const SYS_FUTIMENS = 472
pkg syscall (netbsd-arm64-cgo), const SYS_FUTIMENS ideal-int
pkg syscall (netbsd-arm64-cgo), const SYS_FUTIMES = 423
pkg syscall (netbsd-arm64-cgo), const SYS_FUTIMES ideal-int
pkg syscall (netbsd-arm64-cgo), const SYS_GETCONTEXT = 307
pkg syscall (netbsd-arm64-cgo), const SYS_GETCONTEXT ideal-int
pkg syscall (netbsd-arm64-cgo), const SYS___GETCWD = 296
pkg syscall (netbsd-arm64-cgo), const SYS___GETCWD ideal-int
pkg syscall (netbsd-arm64-cgo), const SYS_GETDENTS = 390
pkg syscall (netbsd-arm64-cgo), const SYS_GETDENTS ideal-int
pkg syscall (netbsd-arm64-cgo), const SYS_GETEGID = 43
pkg syscall (netbsd-arm64-cgo), const SYS_GETEGID ideal-int
pkg syscall (netbsd-arm64-cgo), const SYS_GETEUID = 25
pkg syscall (netbsd-arm64-cgo), const SYS_GETEUID ideal-int
pkg syscall (netbsd-arm64-cgo), const SYS_GETFH = 395
pkg syscall (netbsd-arm64-cgo), const SYS_GETFH ideal-int
pkg syscall (netbsd-arm64-cgo), const SYS_GETGID = 47
pkg syscall (netbsd-arm64-cgo), const SYS_GETGID ideal-int
pkg syscall (netbsd-arm64-cgo), const SYS_GETGROUPS = 79
pkg syscall (netbsd-arm64-cgo), const SYS_GETGROUPS ideal-int
pkg syscall (netbsd-arm64-cgo), const SYS_GETITIMER = 426
pkg syscall (netbsd-arm64-cgo), const SYS_GETITIMER ideal-int
pkg syscall (netbsd-arm64-cgo), const SYS___GETLOGIN = 49
pkg syscall (netbsd-arm64-cgo), const SYS___GETLOGIN ideal-int
pkg syscall (netbsd-arm64-cgo), const SYS_GETPEERNAME = 31
pkg syscall (netbsd-arm64-cgo), const SYS_GETPEERNAME ideal-int
pkg syscall (netbsd-arm64-cgo), const SYS_GETPGID = 207
pkg syscall (netbsd-arm64-cgo), const SYS_GETPGID ideal-int
pkg syscall (netbsd-arm64-cgo), const SYS_GETPGRP = 81
pkg syscall (netbsd-arm64-cgo), const SYS_GETPGRP ideal-int
pkg syscall (netbsd-arm64-cgo), const SYS_GETPID = 20
pkg syscall (netbsd-arm64-cgo), const SYS_GETPID ideal-int
pkg syscall (netbsd-arm64-cgo), const SYS_GETPPID = 39
pkg syscall (netbsd-arm64-cgo), const SYS_GETPPID ideal-int
pkg syscall (netbsd-arm64-cgo), const SYS_GETPRIORITY = 100
pkg syscall (netbsd-arm64-cgo), const SYS_GETPRIORITY ideal-int
pkg syscall (netbsd-arm64-cgo), const SYS_GETRLIMIT = 194
pkg syscall (netbsd-arm64-cgo), const SYS_GETRLIMIT ideal-int
pkg syscall (netbsd-arm64-cgo), const SYS_GETRUSAGE = 445
pkg syscall (netbsd-arm64-cgo), const SYS_GETRUSAGE ideal-int
pkg syscall (netbsd-arm64-cgo), const SYS_GETSID = 286
pkg syscall (netbsd-arm64-cgo), const SYS_GETSID ideal-int
pkg syscall (netbsd-arm64-cgo), const SYS_GETSOCKNAME = 32
pkg syscall (netbsd-arm64-cgo), const SYS_GETSOCKNAME ideal-int
pkg syscall (netbsd-arm64-cgo), const SYS_GETSOCKOPT = 118
pkg syscall (netbsd-arm64-cgo), const SYS_GETSOCKOPT ideal-int
pkg syscall (netbsd-arm64-cgo), const SYS_GETTIMEOFDAY = 418
pkg syscall (netbsd-arm64-cgo), const SYS_GETTIMEOFDAY ideal-int
pkg syscall (netbsd-arm64-cgo), const SYS_GETUID = 24
pkg syscall (netbsd-arm64-cgo), const SYS_GETUID ideal-int
pkg syscall (netbsd-arm64-cgo), const SYS_GETVFSSTAT = 356
pkg syscall (netbsd-arm64-cgo), const SYS_GETVFSSTAT ideal-int
pkg syscall (netbsd-arm64-cgo), const SYS_GETXATTR = 378
pkg syscall (netbsd-arm64-cgo), const SYS_GETXATTR ideal-int
pkg syscall (netbsd-arm64-cgo), const SYS_IOCTL = 54
pkg syscall (netbsd-arm64-cgo), const SYS_IOCTL ideal-int
pkg syscall (netbsd-arm64-cgo), const SYS_ISSETUGID = 305
pkg syscall (netbsd-arm64-cgo), const SYS_ISSETUGID ideal-int
pkg syscall (netbsd-arm64-cgo), const SYS_KEVENT = 435
pkg syscall (netbsd-arm64-cgo), const SYS_KEVENT ideal-int
pkg syscall (netbsd-arm64-cgo), const SYS_KILL = 37
pkg syscall (netbsd-arm64-cgo), const SYS_KILL ideal-int
pkg syscall (netbsd-arm64-cgo), const SYS_KQUEUE1 = 455
pkg syscall (netbsd-arm64-cgo), const SYS_KQUEUE1 ideal-int
pkg syscall (netbsd-arm64-cgo), const SYS_KQUEUE = 344
pkg syscall (netbsd-arm64-cgo), const SYS_KQUEUE ideal-int
pkg syscall (netbsd-arm64-cgo), const SYS_KTRACE = 45
pkg syscall (netbsd-arm64-cgo), const SYS_KTRACE ideal-int
pkg syscall (netbsd-arm64-cgo), const SYS_LCHFLAGS = 304
pkg syscall (netbsd-arm64-cgo), const SYS_LCHFLAGS ideal-int
pkg syscall (netbsd-arm64-cgo), const SYS_LCHMOD = 274
pkg syscall (netbsd-arm64-cgo), const SYS_LCHMOD ideal-int
pkg syscall (netbsd-arm64-cgo), const SYS_LCHOWN = 275
pkg syscall (netbsd-arm64-cgo), const SYS_LCHOWN ideal-int
pkg syscall (netbsd-arm64-cgo), const SYS_LGETXATTR = 379
pkg syscall (netbsd-arm64-cgo), const SYS_LGETXATTR ideal-int
pkg syscall (netbsd-arm64-cgo), const SYS_LINK = 9
pkg syscall (netbsd-arm64-cgo), const SYS_LINKAT = 457
pkg syscall (netbsd-arm64-cgo), const SYS_LINKAT ideal-int
pkg syscall (netbsd-arm64-cgo), const SYS_LINK ideal-int
pkg syscall (netbsd-arm64-cgo), const SYS_LISTEN = 106
pkg syscall (netbsd-arm64-cgo), const SYS_LISTEN ideal-int
pkg syscall (netbsd-arm64-cgo), const SYS_LISTXATTR = 381
pkg syscall (netbsd-arm64-cgo), const SYS_LISTXATTR ideal-int
pkg syscall (netbsd-arm64-cgo), const SYS_LLISTXATTR = 382
pkg syscall (netbsd-arm64-cgo), const SYS_LLISTXATTR ideal-int
pkg syscall (netbsd-arm64-cgo), const SYS_LREMOVEXATTR = 385
pkg syscall (netbsd-arm64-cgo), const SYS_LREMOVEXATTR ideal-int
pkg syscall (netbsd-arm64-cgo), const SYS_LSEEK = 199
pkg syscall (netbsd-arm64-cgo), const SYS_LSEEK ideal-int
pkg syscall (netbsd-arm64-cgo), const SYS_LSETXATTR = 376
pkg syscall (netbsd-arm64-cgo), const SYS_LSETXATTR ideal-int
pkg syscall (netbsd-arm64-cgo), const SYS_LSTAT = 441
pkg syscall (netbsd-arm64-cgo), const SYS_LSTAT ideal-int
pkg syscall (netbsd-arm64-cgo), const SYS_LUTIMES = 424
pkg syscall (netbsd-arm64-cgo), const SYS_LUTIMES ideal-int
pkg syscall (netbsd-arm64-cgo), const SYS__LWP_CONTINUE = 314
pkg syscall (netbsd-arm64-cgo), const SYS__LWP_CONTINUE ideal-int
pkg syscall (netbsd-arm64-cgo), const SYS__LWP_CREATE = 309
pkg syscall (netbsd-arm64-cgo), const SYS__LWP_CREATE ideal-int
pkg syscall (netbsd-arm64-cgo), const SYS__LWP_CTL = 325
pkg syscall (netbsd-arm64-cgo), const SYS__LWP_CTL ideal-int
pkg syscall (netbsd-arm64-cgo), const SYS__LWP_DETACH = 319
pkg syscall (netbsd-arm64-cgo), const SYS__LWP_DETACH ideal-int
pkg syscall (netbsd-arm64-cgo), const SYS__LWP_EXIT = 310
pkg syscall (netbsd-arm64-cgo), const SYS__LWP_EXIT ideal-int
pkg syscall (netbsd-arm64-cgo), const SYS__LWP_GETNAME = 324
pkg syscall (netbsd-arm64-cgo), const SYS__LWP_GETNAME ideal-int
pkg syscall (netbsd-arm64-cgo), const SYS__LWP_GETPRIVATE = 316
pkg syscall (netbsd-arm64-cgo), const SYS__LWP_GETPRIVATE ideal-int
pkg syscall (netbsd-arm64-cgo), const SYS__LWP_KILL = 318
pkg syscall (netbsd-arm64-cgo), const SYS__LWP_KILL ideal-int
pkg syscall (netbsd-arm64-cgo), const SYS__LWP_PARK = 434
pkg syscall (netbsd-arm64-cgo), const SYS__LWP_PARK ideal-int
pkg syscall (netbsd-arm64-cgo), const SYS__LWP_SELF = 311
pkg syscall (netbsd-arm64-cgo), const SYS__LWP_SELF ideal-int
pkg syscall (netbsd-arm64-cgo), const SYS__LWP_SETNAME = 323
pkg syscall (netbsd-arm64-cgo), const SYS__LWP_SETNAME ideal-int
pkg syscall (netbsd-arm64-cgo), const SYS__LWP_SETPRIVATE = 317
pkg syscall (netbsd-arm64-cgo), const SYS__LWP_SETPRIVATE ideal-int
pkg syscall (netbsd-arm64-cgo), const SYS__LWP_SUSPEND = 313
pkg syscall (netbsd-arm64-cgo), const SYS__LWP_SUSPEND ideal-int
pkg syscall (netbsd-arm64-cgo), const SYS__LWP_UNPARK = 321
pkg syscall (netbsd-arm64-cgo), const SYS__LWP_UNPARK_ALL = 322
pkg syscall (netbsd-arm64-cgo), const SYS__LWP_UNPARK_ALL ideal-int
pkg syscall (netbsd-arm64-cgo), const SYS__LWP_UNPARK ideal-int
pkg syscall (netbsd-arm64-cgo), const SYS__LWP_WAIT = 312
pkg syscall (netbsd-arm64-cgo), const SYS__LWP_WAIT ideal-int
pkg syscall (netbsd-arm64-cgo), const SYS__LWP_WAKEUP = 315
pkg syscall (netbsd-arm64-cgo), const SYS__LWP_WAKEUP ideal-int
pkg syscall (netbsd-arm64-cgo), const SYS_MADVISE = 75
pkg syscall (netbsd-arm64-cgo), const SYS_MADVISE ideal-int
pkg syscall (netbsd-arm64-cgo), const SYS_MINCORE = 78
pkg syscall (netbsd-arm64-cgo), const SYS_MINCORE ideal-int
pkg syscall (netbsd-arm64-cgo), const SYS_MINHERIT = 273
pkg syscall (netbsd-arm64-cgo), const SYS_MINHERIT ideal-int
pkg syscall (netbsd-arm64-cgo), const SYS_MKDIR = 136
pkg syscall (netbsd-arm64-cgo), const SYS_MKDIRAT = 461
pkg syscall (netbsd-arm64-cgo), const SYS_MKDIRAT ideal-int
pkg syscall (netbsd-arm64-cgo), const SYS_MKDIR ideal-int
pkg syscall (netbsd-arm64-cgo), const SYS_MKFIFO = 132
pkg syscall (netbsd-arm64-cgo), const SYS_MKFIFOAT = 459
pkg syscall (netbsd-arm64-cgo), const SYS_MKFIFOAT ideal-int
pkg syscall (netbsd-arm64-cgo), const SYS_MKFIFO ideal-int
pkg syscall (netbsd-arm64-cgo), const SYS_MKNOD = 450
pkg syscall (netbsd-arm64-cgo), const SYS_MKNODAT = 460
pkg syscall (netbsd-arm64-cgo), const SYS_MKNODAT ideal-int
pkg syscall (netbsd-arm64-cgo), const SYS_MKNOD ideal-int
pkg syscall (netbsd-arm64-cgo), const SYS_MLOCK = 203
pkg syscall (netbsd-arm64-cgo), const SYS_MLOCKALL = 242
pkg syscall (netbsd-arm64-cgo), const SYS_MLOCKALL ideal-int
pkg syscall (netbsd-arm64-cgo), const SYS_MLOCK ideal-int
pkg syscall (netbsd-arm64-cgo), const SYS_MMAP = 197
pkg syscall (netbsd-arm64-cgo), const SYS_MMAP ideal-int
pkg syscall (netbsd-arm64-cgo), const SYS_MODCTL = 246
pkg syscall (netbsd-arm64-cgo), const SYS_MODCTL ideal-int
pkg syscall (netbsd-arm64-cgo), const SYS_MOUNT = 410
pkg syscall (netbsd-arm64-cgo), const SYS_MOUNT ideal-int
pkg syscall (netbsd-arm64-cgo), const SYS_MPROTECT = 74
pkg syscall (netbsd-arm64-cgo), const SYS_MPROTECT ideal-int
pkg syscall (netbsd-arm64-cgo), const SYS_MREMAP = 411
pkg syscall (netbsd-arm64-cgo), const SYS_MREMAP ideal-int
pkg syscall (netbsd-arm64-cgo), const SYS_MSGCTL = 444
pkg syscall (netbsd-arm64-cgo), const SYS_MSGCTL ideal-int
pkg syscall (netbsd-arm64-cgo), const SYS_MSGGET = 225
pkg syscall (netbsd-arm64-cgo), const SYS_MSGGET ideal-int
pkg syscall (netbsd-arm64-cgo), const SYS_MSGRCV = 227
pkg syscall (netbsd-arm64-cgo), const SYS_MSGRCV ideal-int
pkg syscall (netbsd-arm64-cgo), const SYS_MSGSND = 226
pkg syscall (netbsd-arm64-cgo), const SYS_MSGSND ideal-int
pkg syscall (netbsd-arm64-cgo), const SYS_MUNLOCK = 204
pkg syscall (netbsd-arm64-cgo), const SYS_MUNLOCKALL = 243
pkg syscall (netbsd-arm64-cgo), const SYS_MUNLOCKALL ideal-int
pkg syscall (netbsd-arm64-cgo), const SYS_MUNLOCK ideal-int
pkg syscall (netbsd-arm64-cgo), const SYS_MUNMAP = 73
pkg syscall (netbsd-arm64-cgo), const SYS_MUNMAP ideal-int
pkg syscall (netbsd-arm64-cgo), const SYS_NANOSLEEP = 430
pkg syscall (netbsd-arm64-cgo), const SYS_NANOSLEEP ideal-int
pkg syscall (netbsd-arm64-cgo), const SYS_NTP_ADJTIME = 176
pkg syscall (netbsd-arm64-cgo), const SYS_NTP_ADJTIME ideal-int
pkg syscall (netbsd-arm64-cgo), const SYS_NTP_GETTIME = 448
pkg syscall (netbsd-arm64-cgo), const SYS_NTP_GETTIME ideal-int
pkg syscall (netbsd-arm64-cgo), const SYS_OPEN = 5
pkg syscall (netbsd-arm64-cgo), const SYS_OPENAT = 468
pkg syscall (netbsd-arm64-cgo), const SYS_OPENAT ideal-int
pkg syscall (netbsd-arm64-cgo), const SYS_OPEN ideal-int
pkg syscall (netbsd-arm64-cgo), const SYS_PACCEPT = 456
pkg syscall (netbsd-arm64-cgo), const SYS_PACCEPT ideal-int
pkg syscall (netbsd-arm64-cgo), const SYS_PATHCONF = 191
pkg syscall (netbsd-arm64-cgo), const SYS_PATHCONF ideal-int
pkg syscall (netbsd-arm64-cgo), const SYS_PIPE2 = 453
pkg syscall (netbsd-arm64-cgo), const SYS_PIPE2 ideal-int
pkg syscall (netbsd-arm64-cgo), const SYS_PIPE = 42
pkg syscall (netbsd-arm64-cgo), const SYS_PIPE ideal-int
pkg syscall (netbsd-arm64-cgo), const SYS_PMC_CONTROL = 342
pkg syscall (netbsd-arm64-cgo), const SYS_PMC_CONTROL ideal-int
pkg syscall (netbsd-arm64-cgo), const SYS_PMC_GET_INFO = 341
pkg syscall (netbsd-arm64-cgo), const SYS_PMC_GET_INFO ideal-int
pkg syscall (netbsd-arm64-cgo), const SYS_POLL = 209
pkg syscall (netbsd-arm64-cgo), const SYS_POLL ideal-int
pkg syscall (netbsd-arm64-cgo), const SYS_POLLTS = 437
pkg syscall (netbsd-arm64-cgo), const SYS_POLLTS ideal-int
pkg syscall (netbsd-arm64-cgo), const SYS___POSIX_CHOWN = 283
pkg syscall (netbsd-arm64-cgo), const SYS___POSIX_CHOWN ideal-int
pkg syscall (netbsd-arm64-cgo), const SYS_POSIX_FADVISE = 416
pkg syscall (netbsd-arm64-cgo), const SYS_POSIX_FADVISE ideal-int
pkg syscall (netbsd-arm64-cgo), const SYS___POSIX_FCHOWN = 284
pkg syscall (netbsd-arm64-cgo), const SYS___POSIX_FCHOWN ideal-int
pkg syscall (netbsd-arm64-cgo), const SYS___POSIX_LCHOWN = 285
pkg syscall (netbsd-arm64-cgo), const SYS___POSIX_LCHOWN ideal-int
pkg syscall (netbsd-arm64-cgo), const SYS___POSIX_RENAME = 270
pkg syscall (netbsd-arm64-cgo), const SYS___POSIX_RENAME ideal-int
pkg syscall (netbsd-arm64-cgo), const SYS_POSIX_SPAWN = 474
pkg syscall (netbsd-arm64-cgo), const SYS_POSIX_SPAWN ideal-int
pkg syscall (netbsd-arm64-cgo), const SYS_PREAD = 173
pkg syscall (netbsd-arm64-cgo), const SYS_PREAD ideal-int
pkg syscall (netbsd-arm64-cgo), const SYS_PREADV = 289
pkg syscall (netbsd-arm64-cgo), const SYS_PREADV ideal-int
pkg syscall (netbsd-arm64-cgo), const SYS_PROFIL = 44
pkg syscall (netbsd-arm64-cgo), const SYS_PROFIL ideal-int
pkg syscall (netbsd-arm64-cgo), const SYS_PSELECT = 436
pkg syscall (netbsd-arm64-cgo), const SYS_PSELECT ideal-int
pkg syscall (netbsd-arm64-cgo), const SYS_PSET_ASSIGN = 414
pkg syscall (netbsd-arm64-cgo), const SYS_PSET_ASSIGN ideal-int
pkg syscall (netbsd-arm64-cgo), const SYS__PSET_BIND = 415
pkg syscall (netbsd-arm64-cgo), const SYS__PSET_BIND ideal-int
pkg syscall (netbsd-arm64-cgo), const SYS_PSET_CREATE = 412
pkg syscall (netbsd-arm64-cgo), const SYS_PSET_CREATE ideal-int
pkg syscall (netbsd-arm64-cgo), const SYS_PSET_DESTROY = 413
pkg syscall (netbsd-arm64-cgo), const SYS_PSET_DESTROY ideal-int
pkg syscall (netbsd-arm64-cgo), const SYS_PTRACE = 26
pkg syscall (netbsd-arm64-cgo), const SYS_PTRACE ideal-int
pkg syscall (netbsd-arm64-cgo), const SYS_PWRITE = 174
pkg syscall (netbsd-arm64-cgo), const SYS_PWRITE ideal-int
pkg syscall (netbsd-arm64-cgo), const SYS_PWRITEV = 290
pkg syscall (netbsd-arm64-cgo), const SYS_PWRITEV ideal-int
pkg syscall (netbsd-arm64-cgo), const SYS___QUOTACTL = 473
pkg syscall (netbsd-arm64-cgo), const SYS___QUOTACTL ideal-int
pkg syscall (netbsd-arm64-cgo), const SYS_RASCTL = 343
pkg syscall (netbsd-arm64-cgo), const SYS_RASCTL ideal-int
pkg syscall (netbsd-arm64-cgo), const SYS_READ = 3
pkg syscall (netbsd-arm64-cgo), const SYS_READ ideal-int
pkg syscall (netbsd-arm64-cgo), const SYS_READLINK = 58
pkg syscall (netbsd-arm64-cgo), const SYS_READLINKAT = 469
pkg syscall (netbsd-arm64-cgo), const SYS_READLINKAT ideal-int
pkg syscall (netbsd-arm64-cgo), const SYS_READLINK ideal-int
pkg syscall (netbsd-arm64-cgo), const SYS_READV = 120
pkg syscall (netbsd-arm64-cgo), const SYS_READV ideal-int
pkg syscall (netbsd-arm64-cgo), const SYS_REBOOT = 208
pkg syscall (netbsd-arm64-cgo), const SYS_REBOOT ideal-int
pkg syscall (netbsd-arm64-cgo), const SYS_RECVFROM = 29
pkg syscall (netbsd-arm64-cgo), const SYS_RECVFROM ideal-int
pkg syscall (netbsd-arm64-cgo), const SYS_RECVMMSG = 475
pkg syscall (netbsd-arm64-cgo), const SYS_RECVMMSG ideal-int
pkg syscall (netbsd-arm64-cgo), const SYS_RECVMSG = 27
pkg syscall (netbsd-arm64-cgo), const SYS_RECVMSG ideal-int
pkg syscall (netbsd-arm64-cgo), const SYS_REMOVEXATTR = 384
pkg syscall (netbsd-arm64-cgo), const SYS_REMOVEXATTR ideal-int
pkg syscall (netbsd-arm64-cgo), const SYS_RENAME = 128
pkg syscall (netbsd-arm64-cgo), const SYS_RENAMEAT = 458
pkg syscall (netbsd-arm64-cgo), const SYS_RENAMEAT ideal-int
pkg syscall (netbsd-arm64-cgo), const SYS_RENAME ideal-int
pkg syscall (netbsd-arm64-cgo), const SYS_REVOKE = 56
pkg syscall (netbsd-arm64-cgo), const SYS_REVOKE ideal-int
pkg syscall (netbsd-arm64-cgo), const SYS_RMDIR = 137
pkg syscall (netbsd-arm64-cgo), const SYS_RMDIR ideal-int
pkg syscall (netbsd-arm64-cgo), const SYS_SBRK = 69
pkg syscall (netbsd-arm64-cgo), const SYS_SBRK ideal-int
pkg syscall (netbsd-arm64-cgo), const SYS__SCHED_GETAFFINITY = 349
pkg syscall (netbsd-arm64-cgo), const SYS__SCHED_GETAFFINITY ideal-int
pkg syscall (netbsd-arm64-cgo), const SYS__SCHED_GETPARAM = 347
pkg syscall (netbsd-arm64-cgo), const SYS__SCHED_GETPARAM ideal-int
pkg syscall (netbsd-arm64-cgo), const SYS__SCHED_SETAFFINITY = 348
pkg syscall (netbsd-arm64-cgo), const SYS__SCHED_SETAFFINITY ideal-int
pkg syscall (netbsd-arm64-cgo), const SYS__SCHED_SETPARAM = 346
pkg syscall (netbsd-arm64-cgo), const SYS__SCHED_SETPARAM ideal-int
pkg syscall (netbsd-arm64-cgo), const SYS_SCHED_YIELD = 350
pkg syscall (netbsd-arm64-cgo), const SYS_SCHED_YIELD ideal-int
pkg syscall (netbsd-arm64-cgo), const SYS_SELECT = 417
pkg syscall (netbsd-arm64-cgo), const SYS_SELECT ideal-int
pkg syscall (netbsd-arm64-cgo), const SYS_SEMCONFIG = 223
pkg syscall (netbsd-arm64-cgo), const SYS_SEMCONFIG ideal-int
pkg syscall (netbsd-arm64-cgo), const SYS___SEMCTL = 442
pkg syscall (netbsd-arm64-cgo), const SYS___SEMCTL ideal-int
pkg syscall (netbsd-arm64-cgo), const SYS_SEMGET = 221
pkg syscall (netbsd-arm64-cgo), const SYS_SEMGET ideal-int
pkg syscall (netbsd-arm64-cgo), const SYS_SEMOP = 222
pkg syscall (netbsd-arm64-cgo), const SYS_SEMOP ideal-int
pkg syscall (netbsd-arm64-cgo), const SYS_SENDMMSG = 476
pkg syscall (netbsd-arm64-cgo), const SYS_SENDMMSG ideal-int
pkg syscall (netbsd-arm64-cgo), const SYS_SENDMSG = 28
pkg syscall (netbsd-arm64-cgo), const SYS_SENDMSG ideal-int
pkg syscall (netbsd-arm64-cgo), const SYS_SENDTO = 133
pkg syscall (netbsd-arm64-cgo), const SYS_SENDTO ideal-int
pkg syscall (netbsd-arm64-cgo), const SYS_SETCONTEXT = 308
pkg syscall (netbsd-arm64-cgo), const SYS_SETCONTEXT ideal-int
pkg syscall (netbsd-arm64-cgo), const SYS_SETEGID = 182
pkg syscall (netbsd-arm64-cgo), const SYS_SETEGID ideal-int
pkg syscall (netbsd-arm64-cgo), const SYS_SETEUID = 183
pkg syscall (netbsd-arm64-cgo), const SYS_SETEUID ideal-int
pkg syscall (netbsd-arm64-cgo), const SYS_SETGID = 181
pkg syscall (netbsd-arm64-cgo), const SYS_SETGID ideal-int
pkg syscall (netbsd-arm64-cgo), const SYS_SETGROUPS = 80
pkg syscall (netbsd-arm64-cgo), const SYS_SETGROUPS ideal-int
pkg syscall (netbsd-arm64-cgo), const SYS_SETITIMER = 425
pkg syscall (netbsd-arm64-cgo), const SYS_SETITIMER ideal-int
pkg syscall (netbsd-arm64-cgo), const SYS___SETLOGIN = 50
pkg syscall (netbsd-arm64-cgo), const SYS___SETLOGIN ideal-int
pkg syscall (netbsd-arm64-cgo), const SYS_SETPGID = 82
pkg syscall (netbsd-arm64-cgo), const SYS_SETPGID ideal-int
pkg syscall (netbsd-arm64-cgo), const SYS_SETPRIORITY = 96
pkg syscall (netbsd-arm64-cgo), const SYS_SETPRIORITY ideal-int
pkg syscall (netbsd-arm64-cgo), const SYS_SETREGID = 127
pkg syscall (netbsd-arm64-cgo), const SYS_SETREGID ideal-int
pkg syscall (netbsd-arm64-cgo), const SYS_SETREUID = 126
pkg syscall (netbsd-arm64-cgo), const SYS_SETREUID ideal-int
pkg syscall (netbsd-arm64-cgo), const SYS_SETRLIMIT = 195
pkg syscall (netbsd-arm64-cgo), const SYS_SETRLIMIT ideal-int
pkg syscall (netbsd-arm64-cgo), const SYS_SETSID = 147
pkg syscall (netbsd-arm64-cgo), const SYS_SETSID ideal-int
pkg syscall (netbsd-arm64-cgo), const SYS_SETSOCKOPT = 105
pkg syscall (netbsd-arm64-cgo), const SYS_SETSOCKOPT ideal-int
pkg syscall (netbsd-arm64-cgo), const SYS_SETTIMEOFDAY = 419
pkg syscall (netbsd-arm64-cgo), const SYS_SETTIMEOFDAY ideal-int
pkg syscall (netbsd-arm64-cgo), const SYS_SETUID = 23
pkg syscall (netbsd-arm64-cgo), const SYS_SETUID ideal-int
pkg syscall (netbsd-arm64-cgo), const SYS_SETXATTR = 375
pkg syscall (netbsd-arm64-cgo), const SYS_SETXATTR ideal-int
pkg syscall (netbsd-arm64-cgo), const SYS_SHMAT = 228
pkg syscall (netbsd-arm64-cgo), const SYS_SHMAT ideal-int
pkg syscall (netbsd-arm64-cgo), const SYS_SHMCTL = 443
pkg syscall (netbsd-arm64-cgo), const SYS_SHMCTL ideal-int
pkg syscall (netbsd-arm64-cgo), const SYS_SHMDT = 230
pkg syscall (netbsd-arm64-cgo), const SYS_SHMDT ideal-int
pkg syscall (netbsd-arm64-cgo), const SYS_SHMGET = 231
pkg syscall (netbsd-arm64-cgo), const SYS_SHMGET ideal-int
pkg syscall (netbsd-arm64-cgo), const SYS_SHUTDOWN = 134
pkg syscall (netbsd-arm64-cgo), const SYS_SHUTDOWN ideal-int
pkg syscall (netbsd-arm64-cgo), const SYS___SIGACTION_SIGTRAMP = 340
pkg syscall (netbsd-arm64-cgo), const SYS___SIGACTION_SIGTRAMP ideal-int
pkg syscall (netbsd-arm64-cgo), const SYS_SIGQUEUEINFO = 245
pkg syscall (netbsd-arm64-cgo), const SYS_SIGQUEUEINFO ideal-int
pkg syscall (netbsd-arm64-cgo), const SYS___SIGTIMEDWAIT = 431
pkg syscall (netbsd-arm64-cgo), const SYS___SIGTIMEDWAIT ideal-int
pkg syscall (netbsd-arm64-cgo), const SYS_SOCKET = 394
pkg syscall (netbsd-arm64-cgo), const SYS_SOCKET ideal-int
pkg syscall (netbsd-arm64-cgo), const SYS_SOCKETPAIR = 135
pkg syscall (netbsd-arm64-cgo), const SYS_SOCKETPAIR ideal-int
pkg syscall (netbsd-arm64-cgo), const SYS_SSTK = 70
pkg syscall (netbsd-arm64-cgo), const SYS_SSTK ideal-int
pkg syscall (netbsd-arm64-cgo), const SYS_STAT = 439
pkg syscall (netbsd-arm64-cgo), const SYS_STAT ideal-int
pkg syscall (netbsd-arm64-cgo), const SYS_STATVFS1 = 357
pkg syscall (netbsd-arm64-cgo), const SYS_STATVFS1 ideal-int
pkg syscall (netbsd-arm64-cgo), const SYS_SWAPCTL = 271
pkg syscall (netbsd-arm64-cgo), const SYS_SWAPCTL ideal-int
pkg syscall (netbsd-arm64-cgo), const SYS_SYMLINK = 57
pkg syscall (netbsd-arm64-cgo), const SYS_SYMLINKAT = 470
pkg syscall (netbsd-arm64-cgo), const SYS_SYMLINKAT ideal-int
pkg syscall (netbsd-arm64-cgo), const SYS_SYMLINK ideal-int
pkg syscall (netbsd-arm64-cgo), const SYS_SYNC = 36
pkg syscall (netbsd-arm64-cgo), const SYS_SYNC ideal-int
pkg syscall (netbsd-arm64-cgo), const SYS_SYSARCH = 165
pkg syscall (netbsd-arm64-cgo), const SYS_SYSARCH ideal-int
pkg syscall (netbsd-arm64-cgo), const SYS___SYSCTL = 202
pkg syscall (netbsd-arm64-cgo), const SYS___SYSCTL ideal-int
pkg syscall (netbsd-arm64-cgo), const SYS_TIMER_CREATE = 235
pkg syscall (netbsd-arm64-cgo), const SYS_TIMER_CREATE ideal-int
pkg syscall (netbsd-arm64-cgo), const SYS_TIMER_DELETE = 236
pkg syscall (netbsd-arm64-cgo), const SYS_TIMER_DELETE ideal-int
pkg syscall (netbsd-arm64-cgo), const SYS_TIMER_GETOVERRUN = 239
pkg syscall (netbsd-arm64-cgo), const SYS_TIMER_GETOVERRUN ideal-int
pkg syscall (netbsd-arm64-cgo), const SYS_TIMER_GETTIME = 447
pkg syscall (netbsd-arm64-cgo), const SYS_TIMER_GETTIME ideal-int
pkg syscall (netbsd-arm64-cgo), const SYS_TIMER_SETTIME = 446
pkg syscall (netbsd-arm64-cgo), const SYS_TIMER_SETTIME ideal-int
pkg syscall (netbsd-arm64-cgo), const SYS_TRUNCATE = 200
pkg syscall (netbsd-arm64-cgo), const SYS_TRUNCATE ideal-int
pkg syscall (netbsd-arm64-cgo), const SYS_UMASK = 60
pkg syscall (netbsd-arm64-cgo), const SYS_UMASK ideal-int
pkg syscall (netbsd-arm64-cgo), const SYS_UNDELETE = 205
pkg syscall (netbsd-arm64-cgo), const SYS_UNDELETE ideal-int
pkg syscall (netbsd-arm64-cgo), const SYS_UNLINK = 10
pkg syscall (netbsd-arm64-cgo), const SYS_UNLINKAT = 471
pkg syscall (netbsd-arm64-cgo), const SYS_UNLINKAT ideal-int
pkg syscall (netbsd-arm64-cgo), const SYS_UNLINK ideal-int
pkg syscall (netbsd-arm64-cgo), const SYS_UNMOUNT = 22
pkg syscall (netbsd-arm64-cgo), const SYS_UNMOUNT ideal-int
pkg syscall (netbsd-arm64-cgo), const SYS_UTIMENSAT = 467
pkg syscall (netbsd-arm64-cgo), const SYS_UTIMENSAT ideal-int
pkg syscall (netbsd-arm64-cgo), const SYS_UTIMES = 420
pkg syscall (netbsd-arm64-cgo), const SYS_UTIMES ideal-int
pkg syscall (netbsd-arm64-cgo), const SYS_UTRACE = 306
pkg syscall (netbsd-arm64-cgo), const SYS_UTRACE ideal-int
pkg syscall (netbsd-arm64-cgo), const SYS_UUIDGEN = 355
pkg syscall (netbsd-arm64-cgo), const SYS_UUIDGEN ideal-int
pkg syscall (netbsd-arm64-cgo), const SYS_VADVISE = 72
pkg syscall (netbsd-arm64-cgo), const SYS_VADVISE ideal-int
pkg syscall (netbsd-arm64-cgo), const SYS_VFORK = 66
pkg syscall (netbsd-arm64-cgo), const SYS_VFORK ideal-int
pkg syscall (netbsd-arm64-cgo), const SYS_WAIT4 = 449
pkg syscall (netbsd-arm64-cgo), const SYS_WAIT4 ideal-int
pkg syscall (netbsd-arm64-cgo), const SYS_WRITE = 4
pkg syscall (netbsd-arm64-cgo), const SYS_WRITE ideal-int
pkg syscall (netbsd-arm64-cgo), const SYS_WRITEV = 121
pkg syscall (netbsd-arm64-cgo), const SYS_WRITEV ideal-int
pkg syscall (netbsd-arm64-cgo), const TCIFLUSH = 1
pkg syscall (netbsd-arm64-cgo), const TCIFLUSH ideal-int
pkg syscall (netbsd-arm64-cgo), const TCIOFLUSH = 3
pkg syscall (netbsd-arm64-cgo), const TCIOFLUSH ideal-int
pkg syscall (netbsd-arm64-cgo), const TCOFLUSH = 2
pkg syscall (netbsd-arm64-cgo), const TCOFLUSH ideal-int
pkg syscall (netbsd-arm64-cgo), const TCP_CONGCTL = 32
pkg syscall (netbsd-arm64-cgo), const TCP_CONGCTL ideal-int
pkg syscall (netbsd-arm64-cgo), const TCP_KEEPCNT = 6
pkg syscall (netbsd-arm64-cgo), const TCP_KEEPCNT ideal-int
pkg syscall (netbsd-arm64-cgo), const TCP_KEEPIDLE = 3
pkg syscall (netbsd-arm64-cgo), const TCP_KEEPIDLE ideal-int
pkg syscall (netbsd-arm64-cgo), const TCP_KEEPINIT = 7
pkg syscall (netbsd-arm64-cgo), const TCP_KEEPINIT ideal-int
pkg syscall (netbsd-arm64-cgo), const TCP_KEEPINTVL = 5
pkg syscall (netbsd-arm64-cgo), const TCP_KEEPINTVL ideal-int
pkg syscall (netbsd-arm64-cgo), const TCP_MAXBURST = 4
pkg syscall (netbsd-arm64-cgo), const TCP_MAXBURST ideal-int
pkg syscall (netbsd-arm64-cgo), const TCP_MAXSEG = 2
pkg syscall (netbsd-arm64-cgo), const TCP_MAXSEG ideal-int
pkg syscall (netbsd-arm64-cgo), const TCP_MAXWIN = 65535
pkg syscall (netbsd-arm64-cgo), const TCP_MAXWIN ideal-int
pkg syscall (netbsd-arm64-cgo), const TCP_MAX_WINSHIFT = 14
pkg syscall (netbsd-arm64-cgo), const TCP_MAX_WINSHIFT ideal-int
pkg syscall (netbsd-arm64-cgo), const TCP_MD5SIG = 16
pkg syscall (netbsd-arm64-cgo), const TCP_MD5SIG ideal-int
pkg syscall (netbsd-arm64-cgo), const TCP_MINMSS = 216
pkg syscall (netbsd-arm64-cgo), const TCP_MINMSS ideal-int
pkg syscall (netbsd-arm64-cgo), const TCP_MSS = 536
pkg syscall (netbsd-arm64-cgo), const TCP_MSS ideal-int
pkg syscall (netbsd-arm64-cgo), const TCSAFLUSH = 2
pkg syscall (netbsd-arm64-cgo), const TCSAFLUSH ideal-int
pkg syscall (netbsd-arm64-cgo), const TIOCCBRK = 536900730
pkg syscall (netbsd-arm64-cgo), const TIOCCBRK ideal-int
pkg syscall (netbsd-arm64-cgo), const TIOCCDTR = 536900728
pkg syscall (netbsd-arm64-cgo), const TIOCCDTR ideal-int
pkg syscall (netbsd-arm64-cgo), const TIOCCONS = 2147775586
pkg syscall (netbsd-arm64-cgo), const TIOCCONS ideal-int
pkg syscall (netbsd-arm64-cgo), const TIOCDCDTIMESTAMP = 1074820184
pkg syscall (netbsd-arm64-cgo), const TIOCDCDTIMESTAMP ideal-int
pkg syscall (netbsd-arm64-cgo), const TIOCDRAIN = 536900702
pkg syscall (netbsd-arm64-cgo), const TIOCDRAIN ideal-int
pkg syscall (netbsd-arm64-cgo), const TIOCEXCL = 536900621
pkg syscall (netbsd-arm64-cgo), const TIOCEXCL ideal-int
pkg syscall (netbsd-arm64-cgo), const TIOCEXT = 2147775584
pkg syscall (netbsd-arm64-cgo), const TIOCEXT ideal-int
pkg syscall (netbsd-arm64-cgo), const TIOCFLAG_CDTRCTS = 16
pkg syscall (netbsd-arm64-cgo), const TIOCFLAG_CDTRCTS ideal-int
pkg syscall (netbsd-arm64-cgo), const TIOCFLAG_CLOCAL = 2
pkg syscall (netbsd-arm64-cgo), const TIOCFLAG_CLOCAL ideal-int
pkg syscall (netbsd-arm64-cgo), const TIOCFLAG_CRTSCTS = 4
pkg syscall (netbsd-arm64-cgo), const TIOCFLAG_CRTSCTS ideal-int
pkg syscall (netbsd-arm64-cgo), const TIOCFLAG_MDMBUF = 8
pkg syscall (netbsd-arm64-cgo), const TIOCFLAG_MDMBUF ideal-int
pkg syscall (netbsd-arm64-cgo), const TIOCFLAG_SOFTCAR = 1
pkg syscall (netbsd-arm64-cgo), const TIOCFLAG_SOFTCAR ideal-int
pkg syscall (netbsd-arm64-cgo), const TIOCFLUSH = 2147775504
pkg syscall (netbsd-arm64-cgo), const TIOCFLUSH ideal-int
pkg syscall (netbsd-arm64-cgo), const TIOCGETA = 1076655123
pkg syscall (netbsd-arm64-cgo), const TIOCGETA ideal-int
pkg syscall (netbsd-arm64-cgo), const TIOCGETD = 1074033690
pkg syscall (netbsd-arm64-cgo), const TIOCGETD ideal-int
pkg syscall (netbsd-arm64-cgo), const TIOCGFLAGS = 1074033757
pkg syscall (netbsd-arm64-cgo), const TIOCGFLAGS ideal-int
pkg syscall (netbsd-arm64-cgo), const TIOCGLINED = 1075868738
pkg syscall (netbsd-arm64-cgo), const TIOCGLINED ideal-int
pkg syscall (netbsd-arm64-cgo), const TIOCGPGRP = 1074033783
pkg syscall (netbsd-arm64-cgo), const TIOCGPGRP ideal-int
pkg syscall (netbsd-arm64-cgo), const TIOCGQSIZE = 1074033793
pkg syscall (netbsd-arm64-cgo), const TIOCGQSIZE ideal-int
pkg syscall (netbsd-arm64-cgo), const TIOCGRANTPT = 536900679
pkg syscall (netbsd-arm64-cgo), const TIOCGRANTPT ideal-int
pkg syscall (netbsd-arm64-cgo), const TIOCGSID = 1074033763
pkg syscall (netbsd-arm64-cgo), const TIOCGSID ideal-int
pkg syscall (netbsd-arm64-cgo), const TIOCGSIZE = 1074295912
pkg syscall (netbsd-arm64-cgo), const TIOCGSIZE ideal-int
pkg syscall (netbsd-arm64-cgo), const TIOCGWINSZ = 1074295912
pkg syscall (netbsd-arm64-cgo), const TIOCGWINSZ ideal-int
pkg syscall (netbsd-arm64-cgo), const TIOCMBIC = 2147775595
pkg syscall (netbsd-arm64-cgo), const TIOCMBIC ideal-int
pkg syscall (netbsd-arm64-cgo), const TIOCMBIS = 2147775596
pkg syscall (netbsd-arm64-cgo), const TIOCMBIS ideal-int
pkg syscall (netbsd-arm64-cgo), const TIOCM_CAR = 64
pkg syscall (netbsd-arm64-cgo), const TIOCM_CAR ideal-int
pkg syscall (netbsd-arm64-cgo), const TIOCM_CD = 64
pkg syscall (netbsd-arm64-cgo), const TIOCM_CD ideal-int
pkg syscall (netbsd-arm64-cgo), const TIOCM_CTS = 32
pkg syscall (netbsd-arm64-cgo), const TIOCM_CTS ideal-int
pkg syscall (netbsd-arm64-cgo), const TIOCM_DSR = 256
pkg syscall (netbsd-arm64-cgo), const TIOCM_DSR ideal-int
pkg syscall (netbsd-arm64-cgo), const TIOCM_DTR = 2
pkg syscall (netbsd-arm64-cgo), const TIOCM_DTR ideal-int
pkg syscall (netbsd-arm64-cgo), const TIOCMGET = 1074033770
pkg syscall (netbsd-arm64-cgo), const TIOCMGET ideal-int
pkg syscall (netbsd-arm64-cgo), const TIOCM_LE = 1
pkg syscall (netbsd-arm64-cgo), const TIOCM_LE ideal-int
pkg syscall (netbsd-arm64-cgo), const TIOCM_RI = 128
pkg syscall (netbsd-arm64-cgo), const TIOCM_RI ideal-int
pkg syscall (netbsd-arm64-cgo), const TIOCM_RNG = 128
pkg syscall (netbsd-arm64-cgo), const TIOCM_RNG ideal-int
pkg syscall (netbsd-arm64-cgo), const TIOCM_RTS = 4
pkg syscall (netbsd-arm64-cgo), const TIOCM_RTS ideal-int
pkg syscall (netbsd-arm64-cgo), const TIOCMSET = 2147775597
pkg syscall (netbsd-arm64-cgo), const TIOCMSET ideal-int
pkg syscall (netbsd-arm64-cgo), const TIOCM_SR = 16
pkg syscall (netbsd-arm64-cgo), const TIOCM_SR ideal-int
pkg syscall (netbsd-arm64-cgo), const TIOCM_ST = 8
pkg syscall (netbsd-arm64-cgo), const TIOCM_ST ideal-int
pkg syscall (netbsd-arm64-cgo), const TIOCNOTTY = 536900721
pkg syscall (netbsd-arm64-cgo), const TIOCNOTTY ideal-int
pkg syscall (netbsd-arm64-cgo), const TIOCNXCL = 536900622
pkg syscall (netbsd-arm64-cgo), const TIOCNXCL ideal-int
pkg syscall (netbsd-arm64-cgo), const TIOCOUTQ = 1074033779
pkg syscall (netbsd-arm64-cgo), const TIOCOUTQ ideal-int
pkg syscall (netbsd-arm64-cgo), const TIOCPKT = 2147775600
pkg syscall (netbsd-arm64-cgo), const TIOCPKT_DATA = 0
pkg syscall (netbsd-arm64-cgo), const TIOCPKT_DATA ideal-int
pkg syscall (netbsd-arm64-cgo), const TIOCPKT_DOSTOP = 32
pkg syscall (netbsd-arm64-cgo), const TIOCPKT_DOSTOP ideal-int
pkg syscall (netbsd-arm64-cgo), const TIOCPKT_FLUSHREAD = 1
pkg syscall (netbsd-arm64-cgo), const TIOCPKT_FLUSHREAD ideal-int
pkg syscall (netbsd-arm64-cgo), const TIOCPKT_FLUSHWRITE = 2
pkg syscall (netbsd-arm64-cgo), const TIOCPKT_FLUSHWRITE ideal-int
pkg syscall (netbsd-arm64-cgo), const TIOCPKT ideal-int
pkg syscall (netbsd-arm64-cgo), const TIOCPKT_IOCTL = 64
pkg syscall (netbsd-arm64-cgo), const TIOCPKT_IOCTL ideal-int
pkg syscall (netbsd-arm64-cgo), const TIOCPKT_NOSTOP = 16
pkg syscall (netbsd-arm64-cgo), const TIOCPKT_NOSTOP ideal-int
pkg syscall (netbsd-arm64-cgo), const TIOCPKT_START = 8
pkg syscall (netbsd-arm64-cgo), const TIOCPKT_START ideal-int
pkg syscall (netbsd-arm64-cgo), const TIOCPKT_STOP = 4
pkg syscall (netbsd-arm64-cgo), const TIOCPKT_STOP ideal-int
pkg syscall (netbsd-arm64-cgo), const TIOCPTMGET = 1076393030
pkg syscall (netbsd-arm64-cgo), const TIOCPTMGET ideal-int
pkg syscall (netbsd-arm64-cgo), const TIOCPTSNAME = 1076393032
pkg syscall (netbsd-arm64-cgo), const TIOCPTSNAME ideal-int
pkg syscall (netbsd-arm64-cgo), const TIOCRCVFRAME = 2148037701
pkg syscall (netbsd-arm64-cgo), const TIOCRCVFRAME ideal-int
pkg syscall (netbsd-arm64-cgo), const TIOCREMOTE = 2147775593
pkg syscall (netbsd-arm64-cgo), const TIOCREMOTE ideal-int
pkg syscall (netbsd-arm64-cgo), const TIOCSBRK = 536900731
pkg syscall (netbsd-arm64-cgo), const TIOCSBRK ideal-int
pkg syscall (netbsd-arm64-cgo), const TIOCSCTTY = 536900705
pkg syscall (netbsd-arm64-cgo), const TIOCSCTTY ideal-int
pkg syscall (netbsd-arm64-cgo), const TIOCSDTR = 536900729
pkg syscall (netbsd-arm64-cgo), const TIOCSDTR ideal-int
pkg syscall (netbsd-arm64-cgo), const TIOCSETA = 2150396948
pkg syscall (netbsd-arm64-cgo), const TIOCSETAF = 2150396950
pkg syscall (netbsd-arm64-cgo), const TIOCSETAF ideal-int
pkg syscall (netbsd-arm64-cgo), const TIOCSETA ideal-int
pkg syscall (netbsd-arm64-cgo), const TIOCSETAW = 2150396949
pkg syscall (netbsd-arm64-cgo), const TIOCSETAW ideal-int
pkg syscall (netbsd-arm64-cgo), const TIOCSETD = 2147775515
pkg syscall (netbsd-arm64-cgo), const TIOCSETD ideal-int
pkg syscall (netbsd-arm64-cgo), const TIOCSFLAGS = 2147775580
pkg syscall (netbsd-arm64-cgo), const TIOCSFLAGS ideal-int
pkg syscall (netbsd-arm64-cgo), const TIOCSIG = 536900703
pkg syscall (netbsd-arm64-cgo), const TIOCSIG ideal-int
pkg syscall (netbsd-arm64-cgo), const TIOCSLINED = 2149610563
pkg syscall (netbsd-arm64-cgo), const TIOCSLINED ideal-int
pkg syscall (netbsd-arm64-cgo), const TIOCSPGRP = 2147775606
pkg syscall (netbsd-arm64-cgo), const TIOCSPGRP ideal-int
pkg syscall (netbsd-arm64-cgo), const TIOCSQSIZE = 2147775616
pkg syscall (netbsd-arm64-cgo), const TIOCSQSIZE ideal-int
pkg syscall (netbsd-arm64-cgo), const TIOCSSIZE = 2148037735
pkg syscall (netbsd-arm64-cgo), const TIOCSSIZE ideal-int
pkg syscall (netbsd-arm64-cgo), const TIOCSTART = 536900718
pkg syscall (netbsd-arm64-cgo), const TIOCSTART ideal-int
pkg syscall (netbsd-arm64-cgo), const TIOCSTAT = 2147775589
pkg syscall (netbsd-arm64-cgo), const TIOCSTAT ideal-int
pkg syscall (netbsd-arm64-cgo), const TIOCSTI = 2147578994
pkg syscall (netbsd-arm64-cgo), const TIOCSTI ideal-int
pkg syscall (netbsd-arm64-cgo), const TIOCSTOP = 536900719
pkg syscall (netbsd-arm64-cgo), const TIOCSTOP ideal-int
pkg syscall (netbsd-arm64-cgo), const TIOCSWINSZ = 2148037735
pkg syscall (netbsd-arm64-cgo), const TIOCSWINSZ ideal-int
pkg syscall (netbsd-arm64-cgo), const TIOCUCNTL = 2147775590
pkg syscall (netbsd-arm64-cgo), const TIOCUCNTL ideal-int
pkg syscall (netbsd-arm64-cgo), const TIOCXMTFRAME = 2148037700
pkg syscall (netbsd-arm64-cgo), const TIOCXMTFRAME ideal-int
pkg syscall (netbsd-arm64-cgo), const TOSTOP = 4194304
pkg syscall (netbsd-arm64-cgo), const TOSTOP ideal-int
pkg syscall (netbsd-arm64-cgo), const VDISCARD = 15
pkg syscall (netbsd-arm64-cgo), const VDISCARD ideal-int
pkg syscall (netbsd-arm64-cgo), const VDSUSP = 11
pkg syscall (netbsd-arm64-cgo), const VDSUSP ideal-int
pkg syscall (netbsd-arm64-cgo), const VEOF = 0
pkg syscall (netbsd-arm64-cgo), const VEOF ideal-int
pkg syscall (netbsd-arm64-cgo), const VEOL = 1
pkg syscall (netbsd-arm64-cgo), const VEOL2 = 2
pkg syscall (netbsd-arm64-cgo), const VEOL2 ideal-int
pkg syscall (netbsd-arm64-cgo), const VEOL ideal-int
pkg syscall (netbsd-arm64-cgo), const VERASE = 3
pkg syscall (netbsd-arm64-cgo), const VERASE ideal-int
pkg syscall (netbsd-arm64-cgo), const VINTR = 8
pkg syscall (netbsd-arm64-cgo), const VINTR ideal-int
pkg syscall (netbsd-arm64-cgo), const VKILL = 5
pkg syscall (netbsd-arm64-cgo), const VKILL ideal-int
pkg syscall (netbsd-arm64-cgo), const VLNEXT = 14
pkg syscall (netbsd-arm64-cgo), const VLNEXT ideal-int
pkg syscall (netbsd-arm64-cgo), const VMIN = 16
pkg syscall (netbsd-arm64-cgo), const VMIN ideal-int
pkg syscall (netbsd-arm64-cgo), const VQUIT = 9
pkg syscall (netbsd-arm64-cgo), const VQUIT ideal-int
pkg syscall (netbsd-arm64-cgo), const VREPRINT = 6
pkg syscall (netbsd-arm64-cgo), const VREPRINT ideal-int
pkg syscall (netbsd-arm64-cgo), const VSTART = 12
pkg syscall (netbsd-arm64-cgo), const VSTART ideal-int
pkg syscall (netbsd-arm64-cgo), const VSTATUS = 18
pkg syscall (netbsd-arm64-cgo), const VSTATUS ideal-int
pkg syscall (netbsd-arm64-cgo), const VSTOP = 13
pkg syscall (netbsd-arm64-cgo), const VSTOP ideal-int
pkg syscall (netbsd-arm64-cgo), const VSUSP = 10
pkg syscall (netbsd-arm64-cgo), const VSUSP ideal-int
pkg syscall (netbsd-arm64-cgo), const VTIME = 17
pkg syscall (netbsd-arm64-cgo), const VTIME ideal-int
pkg syscall (netbsd-arm64-cgo), const VWERASE = 4
pkg syscall (netbsd-arm64-cgo), const VWERASE ideal-int
pkg syscall (netbsd-arm64-cgo), const WALL = 8
pkg syscall (netbsd-arm64-cgo), const WALL ideal-int
pkg syscall (netbsd-arm64-cgo), const WALLSIG = 8
pkg syscall (netbsd-arm64-cgo), const WALLSIG ideal-int
pkg syscall (netbsd-arm64-cgo), const WALTSIG = 4
pkg syscall (netbsd-arm64-cgo), const WALTSIG ideal-int
pkg syscall (netbsd-arm64-cgo), const WCLONE = 4
pkg syscall (netbsd-arm64-cgo), const WCLONE ideal-int
pkg syscall (netbsd-arm64-cgo), const WCOREFLAG = 128
pkg syscall (netbsd-arm64-cgo), const WCOREFLAG ideal-int
pkg syscall (netbsd-arm64-cgo), const WNOHANG = 1
pkg syscall (netbsd-arm64-cgo), const WNOHANG ideal-int
pkg syscall (netbsd-arm64-cgo), const WNOWAIT = 65536
pkg syscall (netbsd-arm64-cgo), const WNOWAIT ideal-int
pkg syscall (netbsd-arm64-cgo), const WNOZOMBIE = 131072
pkg syscall (netbsd-arm64-cgo), const WNOZOMBIE ideal-int
pkg syscall (netbsd-arm64-cgo), const WOPTSCHECKED = 262144
pkg syscall (netbsd-arm64-cgo), const WOPTSCHECKED ideal-int
pkg syscall (netbsd-arm64-cgo), const WSTOPPED = 127
pkg syscall (netbsd-arm64-cgo), const WSTOPPED ideal-int
pkg syscall (netbsd-arm64-cgo), const WUNTRACED = 2
pkg syscall (netbsd-arm64-cgo), const WUNTRACED ideal-int
pkg syscall (netbsd-arm64-cgo), func Accept4(int, int) (int, Sockaddr, error)
pkg syscall (netbsd-arm64-cgo), func Accept(int) (int, Sockaddr, error)
pkg syscall (netbsd-arm64-cgo), func Access(string, uint32) error
pkg syscall (netbsd-arm64-cgo), func Adjtime(*Timeval, *Timeval) error
pkg syscall (netbsd-arm64-cgo), func Bind(int, Sockaddr) error
pkg syscall (netbsd-arm64-cgo), func BpfBuflen(int) (int, error)
pkg syscall (netbsd-arm64-cgo), func BpfDatalink(int) (int, error)
pkg syscall (netbsd-arm64-cgo), func BpfHeadercmpl(int) (int, error)
pkg syscall (netbsd-arm64-cgo), func BpfInterface(int, string) (string, error)
pkg syscall (netbsd-arm64-cgo), func BpfJump(int, int, int, int) *BpfInsn
pkg syscall (netbsd-arm64-cgo), func BpfStats(int) (*BpfStat, error)
pkg syscall (netbsd-arm64-cgo), func BpfStmt(int, int) *BpfInsn
pkg syscall (netbsd-arm64-cgo), func BpfTimeout(int) (*Timeval, error)
pkg syscall (netbsd-arm64-cgo), func CheckBpfVersion(int) error
pkg syscall (netbsd-arm64-cgo), func Chflags(string, int) error
pkg syscall (netbsd-arm64-cgo), func Chroot(string) error
pkg syscall (netbsd-arm64-cgo), func Close(int) error
pkg syscall (netbsd-arm64-cgo), func CloseOnExec(int)
pkg syscall (netbsd-arm64-cgo), func CmsgLen(int) int
pkg syscall (netbsd-arm64-cgo), func CmsgSpace(int) int
pkg syscall (netbsd-arm64-cgo), func Connect(int, Sockaddr) error
pkg syscall (netbsd-arm64-cgo), func Dup2(int, int) error
pkg syscall (netbsd-arm64-cgo), func Dup(int) (int, error)
pkg syscall (netbsd-arm64-cgo), func Fchdir(int) error
pkg syscall (netbsd-arm64-cgo), func Fchflags(int, int) error
pkg syscall (netbsd-arm64-cgo), func Fchmod(int, uint32) error
pkg syscall (netbsd-arm64-cgo), func Fchown(int, int, int) error
pkg syscall (netbsd-arm64-cgo), func FcntlFlock(uintptr, int, *Flock_t) error
pkg syscall (netbsd-arm64-cgo), func Flock(int, int) error
pkg syscall (netbsd-arm64-cgo), func FlushBpf(int) error
pkg syscall (netbsd-arm64-cgo), func ForkExec(string, []string, *ProcAttr) (int, error)
pkg syscall (netbsd-arm64-cgo), func Fpathconf(int, int) (int, error)
pkg syscall (netbsd-arm64-cgo), func Fstat(int, *Stat_t) error
pkg syscall (netbsd-arm64-cgo), func Fsync(int) error
pkg syscall (netbsd-arm64-cgo), func Ftruncate(int, int64) error
pkg syscall (netbsd-arm64-cgo), func Futimes(int, []Timeval) error
pkg syscall (netbsd-arm64-cgo), func Getdirentries(int, []uint8, *uintptr) (int, error)
pkg syscall (netbsd-arm64-cgo), func Getpeername(int) (Sockaddr, error)
pkg syscall (netbsd-arm64-cgo), func Getpgid(int) (int, error)
pkg syscall (netbsd-arm64-cgo), func Getpgrp() int
pkg syscall (netbsd-arm64-cgo), func Getpriority(int, int) (int, error)
pkg syscall (netbsd-arm64-cgo), func Getrlimit(int, *Rlimit) error
pkg syscall (netbsd-arm64-cgo), func Getrusage(int, *Rusage) error
pkg syscall (netbsd-arm64-cgo), func Getsid(int) (int, error)
pkg syscall (netbsd-arm64-cgo), func Getsockname(int) (Sockaddr, error)
pkg syscall (netbsd-arm64-cgo), func GetsockoptByte(int, int, int) (uint8, error)
pkg syscall (netbsd-arm64-cgo), func GetsockoptICMPv6Filter(int, int, int) (*ICMPv6Filter, error)
pkg syscall (netbsd-arm64-cgo), func GetsockoptInet4Addr(int, int, int) ([4]uint8, error)
pkg syscall (netbsd-arm64-cgo), func GetsockoptInt(int, int, int) (int, error)
pkg syscall (netbsd-arm64-cgo), func GetsockoptIPMreq(int, int, int) (*IPMreq, error)
pkg syscall (netbsd-arm64-cgo), func GetsockoptIPv6Mreq(int, int, int) (*IPv6Mreq, error)
pkg syscall (netbsd-arm64-cgo), func GetsockoptIPv6MTUInfo(int, int, int) (*IPv6MTUInfo, error)
pkg syscall (netbsd-arm64-cgo), func Issetugid() bool
pkg syscall (netbsd-arm64-cgo), func Kevent(int, []Kevent_t, []Kevent_t, *Timespec) (int, error)
pkg syscall (netbsd-arm64-cgo), func Kill(int, Signal) error
pkg syscall (netbsd-arm64-cgo), func Kqueue() (int, error)
pkg syscall (netbsd-arm64-cgo), func Listen(int, int) error
pkg syscall (netbsd-arm64-cgo), func Lstat(string, *Stat_t) error
pkg syscall (netbsd-arm64-cgo), func Mkfifo(string, uint32) error
pkg syscall (netbsd-arm64-cgo), func Mknod(string, uint32, int) error
pkg syscall (netbsd-arm64-cgo), func Mmap(int, int64, int, int, int) ([]uint8, error)
pkg syscall (netbsd-arm64-cgo), func Munmap([]uint8) error
pkg syscall (netbsd-arm64-cgo), func Nanosleep(*Timespec, *Timespec) error
pkg syscall (netbsd-arm64-cgo), func Open(string, int, uint32) (int, error)
pkg syscall (netbsd-arm64-cgo), func ParseDirent([]uint8, int, []string) (int, int, []string)
pkg syscall (netbsd-arm64-cgo), func ParseRoutingMessage([]uint8) ([]RoutingMessage, error)
pkg syscall (netbsd-arm64-cgo), func ParseRoutingSockaddr(RoutingMessage) ([]Sockaddr, error)
pkg syscall (netbsd-arm64-cgo), func ParseSocketControlMessage([]uint8) ([]SocketControlMessage, error)
pkg syscall (netbsd-arm64-cgo), func ParseUnixRights(*SocketControlMessage) ([]int, error)
pkg syscall (netbsd-arm64-cgo), func Pathconf(string, int) (int, error)
pkg syscall (netbsd-arm64-cgo), func Pipe2([]int, int) error
pkg syscall (netbsd-arm64-cgo), func Pipe([]int) error
pkg syscall (netbsd-arm64-cgo), func Pread(int, []uint8, int64) (int, error)
pkg syscall (netbsd-arm64-cgo), func Pwrite(int, []uint8, int64) (int, error)
pkg syscall (netbsd-arm64-cgo), func RawSyscall6(uintptr, uintptr, uintptr, uintptr, uintptr, uintptr, uintptr) (uintptr, uintptr, Errno)
pkg syscall (netbsd-arm64-cgo), func RawSyscall(uintptr, uintptr, uintptr, uintptr) (uintptr, uintptr, Errno)
pkg syscall (netbsd-arm64-cgo), func ReadDirent(int, []uint8) (int, error)
pkg syscall (netbsd-arm64-cgo), func Read(int, []uint8) (int, error)
pkg syscall (netbsd-arm64-cgo), func Recvfrom(int, []uint8, int) (int, Sockaddr, error)
pkg syscall (netbsd-arm64-cgo), func Recvmsg(int, []uint8, []uint8, int) (int, int, int, Sockaddr, error)
pkg syscall (netbsd-arm64-cgo), func Revoke(string) error
pkg syscall (netbsd-arm64-cgo), func RouteRIB(int, int) ([]uint8, error)
pkg syscall (netbsd-arm64-cgo), func Seek(int, int64, int) (int64, error)
pkg syscall (netbsd-arm64-cgo), func Select(int, *FdSet, *FdSet, *FdSet, *Timeval) error
pkg syscall (netbsd-arm64-cgo), func Sendfile(int, int, *int64, int) (int, error)
pkg syscall (netbsd-arm64-cgo), func Sendmsg(int, []uint8, []uint8, Sockaddr, int) error
pkg syscall (netbsd-arm64-cgo), func SendmsgN(int, []uint8, []uint8, Sockaddr, int) (int, error)
pkg syscall (netbsd-arm64-cgo), func Sendto(int, []uint8, int, Sockaddr) error
pkg syscall (netbsd-arm64-cgo), func SetBpfBuflen(int, int) (int, error)
pkg syscall (netbsd-arm64-cgo), func SetBpfDatalink(int, int) (int, error)
pkg syscall (netbsd-arm64-cgo), func SetBpfHeadercmpl(int, int) error
pkg syscall (netbsd-arm64-cgo), func SetBpfImmediate(int, int) error
pkg syscall (netbsd-arm64-cgo), func SetBpf(int, []BpfInsn) error
pkg syscall (netbsd-arm64-cgo), func SetBpfInterface(int, string) error
pkg syscall (netbsd-arm64-cgo), func SetBpfPromisc(int, int) error
pkg syscall (netbsd-arm64-cgo), func SetBpfTimeout(int, *Timeval) error
pkg syscall (netbsd-arm64-cgo), func Setegid(int) error
pkg syscall (netbsd-arm64-cgo), func Seteuid(int) error
pkg syscall (netbsd-arm64-cgo), func Setgid(int) error
pkg syscall (netbsd-arm64-cgo), func Setgroups([]int) error
pkg syscall (netbsd-arm64-cgo), func SetKevent(*Kevent_t, int, int, int)
pkg syscall (netbsd-arm64-cgo), func SetNonblock(int, bool) error
pkg syscall (netbsd-arm64-cgo), func Setpgid(int, int) error
pkg syscall (netbsd-arm64-cgo), func Setpriority(int, int, int) error
pkg syscall (netbsd-arm64-cgo), func Setregid(int, int) error
pkg syscall (netbsd-arm64-cgo), func Setreuid(int, int) error
pkg syscall (netbsd-arm64-cgo), func Setrlimit(int, *Rlimit) error
pkg syscall (netbsd-arm64-cgo), func Setsid() (int, error)
pkg syscall (netbsd-arm64-cgo), func SetsockoptByte(int, int, int, uint8) error
pkg syscall (netbsd-arm64-cgo), func SetsockoptICMPv6Filter(int, int, int, *ICMPv6Filter) error
pkg syscall (netbsd-arm64-cgo), func SetsockoptInet4Addr(int, int, int, [4]uint8) error
pkg syscall (netbsd-arm64-cgo), func SetsockoptInt(int, int, int, int) error
pkg syscall (netbsd-arm64-cgo), func SetsockoptIPMreq(int, int, int, *IPMreq) error
pkg syscall (netbsd-arm64-cgo), func SetsockoptIPv6Mreq(int, int, int, *IPv6Mreq) error
pkg syscall (netbsd-arm64-cgo), func SetsockoptLinger(int, int, int, *Linger) error
pkg syscall (netbsd-arm64-cgo), func SetsockoptString(int, int, int, string) error
pkg syscall (netbsd-arm64-cgo), func SetsockoptTimeval(int, int, int, *Timeval) error
pkg syscall (netbsd-arm64-cgo), func Settimeofday(*Timeval) error
pkg syscall (netbsd-arm64-cgo), func Setuid(int) error
pkg syscall (netbsd-arm64-cgo), func Shutdown(int, int) error
pkg syscall (netbsd-arm64-cgo), func SlicePtrFromStrings([]string) ([]*uint8, error)
pkg syscall (netbsd-arm64-cgo), func Socket(int, int, int) (int, error)
pkg syscall (netbsd-arm64-cgo), func Socketpair(int, int, int) ([2]int, error)
pkg syscall (netbsd-arm64-cgo), func Stat(string, *Stat_t) error
pkg syscall (netbsd-arm64-cgo), func StringSlicePtr([]string) []*uint8
pkg syscall (netbsd-arm64-cgo), func Sync() error
pkg syscall (netbsd-arm64-cgo), func Syscall6(uintptr, uintptr, uintptr, uintptr, uintptr, uintptr, uintptr) (uintptr, uintptr, Errno)
pkg syscall (netbsd-arm64-cgo), func Syscall9(uintptr, uintptr, uintptr, uintptr, uintptr, uintptr, uintptr, uintptr, uintptr, uintptr) (uintptr, uintptr, Errno)
pkg syscall (netbsd-arm64-cgo), func Syscall(uintptr, uintptr, uintptr, uintptr) (uintptr, uintptr, Errno)
pkg syscall (netbsd-arm64-cgo), func Sysctl(string) (string, error)
pkg syscall (netbsd-arm64-cgo), func SysctlUint32(string) (uint32, error)
pkg syscall (netbsd-arm64-cgo), func TimevalToNsec(Timeval) int64
pkg syscall (netbsd-arm64-cgo), func Truncate(string, int64) error
pkg syscall (netbsd-arm64-cgo), func Umask(int) int
pkg syscall (netbsd-arm64-cgo), func UnixRights(...int) []uint8
pkg syscall (netbsd-arm64-cgo), func Unmount(string, int) error
pkg syscall (netbsd-arm64-cgo), func Wait4(int, *WaitStatus, int, *Rusage) (int, error)
pkg syscall (netbsd-arm64-cgo), func Write(int, []uint8) (int, error)
pkg syscall (netbsd-arm64-cgo), method (*Cmsghdr) SetLen(int)
pkg syscall (netbsd-arm64-cgo), method (*Iovec) SetLen(int)
pkg syscall (netbsd-arm64-cgo), method (*Msghdr) SetControllen(int)
pkg syscall (netbsd-arm64-cgo), type BpfHdr struct
pkg syscall (netbsd-arm64-cgo), type BpfHdr struct, Caplen uint32
pkg syscall (netbsd-arm64-cgo), type BpfHdr struct, Datalen uint32
pkg syscall (netbsd-arm64-cgo), type BpfHdr struct, Hdrlen uint16
pkg syscall (netbsd-arm64-cgo), type BpfHdr struct, Pad_cgo_0 [6]uint8
pkg syscall (netbsd-arm64-cgo), type BpfHdr struct, Tstamp BpfTimeval
pkg syscall (netbsd-arm64-cgo), type BpfInsn struct
pkg syscall (netbsd-arm64-cgo), type BpfInsn struct, Code uint16
pkg syscall (netbsd-arm64-cgo), type BpfInsn struct, Jf uint8
pkg syscall (netbsd-arm64-cgo), type BpfInsn struct, Jt uint8
pkg syscall (netbsd-arm64-cgo), type BpfInsn struct, K uint32
pkg syscall (netbsd-arm64-cgo), type BpfProgram struct
pkg syscall (netbsd-arm64-cgo), type BpfProgram struct, Insns *BpfInsn
pkg syscall (netbsd-arm64-cgo), type BpfProgram struct, Len uint32
pkg syscall (netbsd-arm64-cgo), type BpfProgram struct, Pad_cgo_0 [4]uint8
pkg syscall (netbsd-arm64-cgo), type BpfStat struct
pkg syscall (netbsd-arm64-cgo), type BpfStat struct, Capt uint64
pkg syscall (netbsd-arm64-cgo), type BpfStat struct, Drop uint64
pkg syscall (netbsd-arm64-cgo), type BpfStat struct, Padding [13]uint64
pkg syscall (netbsd-arm64-cgo), type BpfStat struct, Recv uint64
pkg syscall (netbsd-arm64-cgo), type BpfTimeval struct
pkg syscall (netbsd-arm64-cgo), type BpfTimeval struct, Sec int64
pkg syscall (netbsd-arm64-cgo), type BpfTimeval struct, Usec int64
pkg syscall (netbsd-arm64-cgo), type BpfVersion struct
pkg syscall (netbsd-arm64-cgo), type BpfVersion struct, Major uint16
pkg syscall (netbsd-arm64-cgo), type BpfVersion struct, Minor uint16
pkg syscall (netbsd-arm64-cgo), type Cmsghdr struct
pkg syscall (netbsd-arm64-cgo), type Cmsghdr struct, Len uint32
pkg syscall (netbsd-arm64-cgo), type Cmsghdr struct, Level int32
pkg syscall (netbsd-arm64-cgo), type Cmsghdr struct, Type int32
pkg syscall (netbsd-arm64-cgo), type Credential struct
pkg syscall (netbsd-arm64-cgo), type Credential struct, Gid uint32
pkg syscall (netbsd-arm64-cgo), type Credential struct, Groups []uint32
pkg syscall (netbsd-arm64-cgo), type Credential struct, NoSetGroups bool
pkg syscall (netbsd-arm64-cgo), type Credential struct, Uid uint32
pkg syscall (netbsd-arm64-cgo), type Dirent struct
pkg syscall (netbsd-arm64-cgo), type Dirent struct, Fileno uint64
pkg syscall (netbsd-arm64-cgo), type Dirent struct, Name [512]int8
pkg syscall (netbsd-arm64-cgo), type Dirent struct, Namlen uint16
pkg syscall (netbsd-arm64-cgo), type Dirent struct, Pad_cgo_0 [3]uint8
pkg syscall (netbsd-arm64-cgo), type Dirent struct, Reclen uint16
pkg syscall (netbsd-arm64-cgo), type Dirent struct, Type uint8
pkg syscall (netbsd-arm64-cgo), type FdSet struct
pkg syscall (netbsd-arm64-cgo), type FdSet struct, Bits [8]uint32
pkg syscall (netbsd-arm64-cgo), type Flock_t struct
pkg syscall (netbsd-arm64-cgo), type Flock_t struct, Len int64
pkg syscall (netbsd-arm64-cgo), type Flock_t struct, Pid int32
pkg syscall (netbsd-arm64-cgo), type Flock_t struct, Start int64
pkg syscall (netbsd-arm64-cgo), type Flock_t struct, Type int16
pkg syscall (netbsd-arm64-cgo), type Flock_t struct, Whence int16
pkg syscall (netbsd-arm64-cgo), type Fsid struct
pkg syscall (netbsd-arm64-cgo), type Fsid struct, X__fsid_val [2]int32
pkg syscall (netbsd-arm64-cgo), type ICMPv6Filter struct
pkg syscall (netbsd-arm64-cgo), type ICMPv6Filter struct, Filt [8]uint32
pkg syscall (netbsd-arm64-cgo), type IfaMsghdr struct
pkg syscall (netbsd-arm64-cgo), type IfaMsghdr struct, Addrs int32
pkg syscall (netbsd-arm64-cgo), type IfaMsghdr struct, Flags int32
pkg syscall (netbsd-arm64-cgo), type IfaMsghdr struct, Index uint16
pkg syscall (netbsd-arm64-cgo), type IfaMsghdr struct, Metric int32
pkg syscall (netbsd-arm64-cgo), type IfaMsghdr struct, Msglen uint16
pkg syscall (netbsd-arm64-cgo), type IfaMsghdr struct, Pad_cgo_0 [6]uint8
pkg syscall (netbsd-arm64-cgo), type IfaMsghdr struct, Type uint8
pkg syscall (netbsd-arm64-cgo), type IfaMsghdr struct, Version uint8
pkg syscall (netbsd-arm64-cgo), type IfAnnounceMsghdr struct
pkg syscall (netbsd-arm64-cgo), type IfAnnounceMsghdr struct, Index uint16
pkg syscall (netbsd-arm64-cgo), type IfAnnounceMsghdr struct, Msglen uint16
pkg syscall (netbsd-arm64-cgo), type IfAnnounceMsghdr struct, Name [16]int8
pkg syscall (netbsd-arm64-cgo), type IfAnnounceMsghdr struct, Type uint8
pkg syscall (netbsd-arm64-cgo), type IfAnnounceMsghdr struct, Version uint8
pkg syscall (netbsd-arm64-cgo), type IfAnnounceMsghdr struct, What uint16
pkg syscall (netbsd-arm64-cgo), type IfData struct
pkg syscall (netbsd-arm64-cgo), type IfData struct, Addrlen uint8
pkg syscall (netbsd-arm64-cgo), type IfData struct, Baudrate uint64
pkg syscall (netbsd-arm64-cgo), type IfData struct, Collisions uint64
pkg syscall (netbsd-arm64-cgo), type IfData struct, Hdrlen uint8
pkg syscall (netbsd-arm64-cgo), type IfData struct, Ibytes uint64
pkg syscall (netbsd-arm64-cgo), type IfData struct, Ierrors uint64
pkg syscall (netbsd-arm64-cgo), type IfData struct, Imcasts uint64
pkg syscall (netbsd-arm64-cgo), type IfData struct, Ipackets uint64
pkg syscall (netbsd-arm64-cgo), type IfData struct, Iqdrops uint64
pkg syscall (netbsd-arm64-cgo), type IfData struct, Lastchange Timespec
pkg syscall (netbsd-arm64-cgo), type IfData struct, Link_state int32
pkg syscall (netbsd-arm64-cgo), type IfData struct, Metric uint64
pkg syscall (netbsd-arm64-cgo), type IfData struct, Mtu uint64
pkg syscall (netbsd-arm64-cgo), type IfData struct, Noproto uint64
pkg syscall (netbsd-arm64-cgo), type IfData struct, Obytes uint64
pkg syscall (netbsd-arm64-cgo), type IfData struct, Oerrors uint64
pkg syscall (netbsd-arm64-cgo), type IfData struct, Omcasts uint64
pkg syscall (netbsd-arm64-cgo), type IfData struct, Opackets uint64
pkg syscall (netbsd-arm64-cgo), type IfData struct, Pad_cgo_0 [1]uint8
pkg syscall (netbsd-arm64-cgo), type IfData struct, Type uint8
pkg syscall (netbsd-arm64-cgo), type IfMsghdr struct
pkg syscall (netbsd-arm64-cgo), type IfMsghdr struct, Addrs int32
pkg syscall (netbsd-arm64-cgo), type IfMsghdr struct, Data IfData
pkg syscall (netbsd-arm64-cgo), type IfMsghdr struct, Flags int32
pkg syscall (netbsd-arm64-cgo), type IfMsghdr struct, Index uint16
pkg syscall (netbsd-arm64-cgo), type IfMsghdr struct, Msglen uint16
pkg syscall (netbsd-arm64-cgo), type IfMsghdr struct, Pad_cgo_0 [2]uint8
pkg syscall (netbsd-arm64-cgo), type IfMsghdr struct, Type uint8
pkg syscall (netbsd-arm64-cgo), type IfMsghdr struct, Version uint8
pkg syscall (netbsd-arm64-cgo), type Inet6Pktinfo struct
pkg syscall (netbsd-arm64-cgo), type Inet6Pktinfo struct, Addr [16]uint8
pkg syscall (netbsd-arm64-cgo), type Inet6Pktinfo struct, Ifindex uint32
pkg syscall (netbsd-arm64-cgo), type InterfaceAddrMessage struct
pkg syscall (netbsd-arm64-cgo), type InterfaceAddrMessage struct, Data []uint8
pkg syscall (netbsd-arm64-cgo), type InterfaceAddrMessage struct, Header IfaMsghdr
pkg syscall (netbsd-arm64-cgo), type InterfaceAnnounceMessage struct
pkg syscall (netbsd-arm64-cgo), type InterfaceAnnounceMessage struct, Header IfAnnounceMsghdr
pkg syscall (netbsd-arm64-cgo), type InterfaceMessage struct
pkg syscall (netbsd-arm64-cgo), type InterfaceMessage struct, Data []uint8
pkg syscall (netbsd-arm64-cgo), type InterfaceMessage struct, Header IfMsghdr
pkg syscall (netbsd-arm64-cgo), type Iovec struct
pkg syscall (netbsd-arm64-cgo), type Iovec struct, Base *uint8
pkg syscall (netbsd-arm64-cgo), type Iovec struct, Len uint64
pkg syscall (netbsd-arm64-cgo), type IPv6MTUInfo struct
pkg syscall (netbsd-arm64-cgo), type IPv6MTUInfo struct, Addr RawSockaddrInet6
pkg syscall (netbsd-arm64-cgo), type IPv6MTUInfo struct, Mtu uint32
pkg syscall (netbsd-arm64-cgo), type Kevent_t struct
pkg syscall (netbsd-arm64-cgo), type Kevent_t struct, Data int64
pkg syscall (netbsd-arm64-cgo), type Kevent_t struct, Fflags uint32
pkg syscall (netbsd-arm64-cgo), type Kevent_t struct, Filter uint32
pkg syscall (netbsd-arm64-cgo), type Kevent_t struct, Flags uint32
pkg syscall (netbsd-arm64-cgo), type Kevent_t struct, Ident uint64
pkg syscall (netbsd-arm64-cgo), type Kevent_t struct, Pad_cgo_0 [4]uint8
pkg syscall (netbsd-arm64-cgo), type Kevent_t struct, Udata int64
pkg syscall (netbsd-arm64-cgo), type Mclpool [0]uint8
pkg syscall (netbsd-arm64-cgo), type Msghdr struct
pkg syscall (netbsd-arm64-cgo), type Msghdr struct, Controllen uint32
pkg syscall (netbsd-arm64-cgo), type Msghdr struct, Control *uint8
pkg syscall (netbsd-arm64-cgo), type Msghdr struct, Flags int32
pkg syscall (netbsd-arm64-cgo), type Msghdr struct, Iov *Iovec
pkg syscall (netbsd-arm64-cgo), type Msghdr struct, Iovlen int32
pkg syscall (netbsd-arm64-cgo), type Msghdr struct, Namelen uint32
pkg syscall (netbsd-arm64-cgo), type Msghdr struct, Name *uint8
pkg syscall (netbsd-arm64-cgo), type Msghdr struct, Pad_cgo_0 [4]uint8
pkg syscall (netbsd-arm64-cgo), type Msghdr struct, Pad_cgo_1 [4]uint8
pkg syscall (netbsd-arm64-cgo), type RawSockaddrAny struct, Pad [92]int8
pkg syscall (netbsd-arm64-cgo), type RawSockaddrDatalink struct
pkg syscall (netbsd-arm64-cgo), type RawSockaddrDatalink struct, Alen uint8
pkg syscall (netbsd-arm64-cgo), type RawSockaddrDatalink struct, Data [12]int8
pkg syscall (netbsd-arm64-cgo), type RawSockaddrDatalink struct, Family uint8
pkg syscall (netbsd-arm64-cgo), type RawSockaddrDatalink struct, Index uint16
pkg syscall (netbsd-arm64-cgo), type RawSockaddrDatalink struct, Len uint8
pkg syscall (netbsd-arm64-cgo), type RawSockaddrDatalink struct, Nlen uint8
pkg syscall (netbsd-arm64-cgo), type RawSockaddrDatalink struct, Slen uint8
pkg syscall (netbsd-arm64-cgo), type RawSockaddrDatalink struct, Type uint8
pkg syscall (netbsd-arm64-cgo), type RawSockaddrInet4 struct, Family uint8
pkg syscall (netbsd-arm64-cgo), type RawSockaddrInet4 struct, Len uint8
pkg syscall (netbsd-arm64-cgo), type RawSockaddrInet4 struct, Zero [8]int8
pkg syscall (netbsd-arm64-cgo), type RawSockaddrInet6 struct, Family uint8
pkg syscall (netbsd-arm64-cgo), type RawSockaddrInet6 struct, Len uint8
pkg syscall (netbsd-arm64-cgo), type RawSockaddr struct, Data [14]int8
pkg syscall (netbsd-arm64-cgo), type RawSockaddr struct, Family uint8
pkg syscall (netbsd-arm64-cgo), type RawSockaddr struct, Len uint8
pkg syscall (netbsd-arm64-cgo), type RawSockaddrUnix struct, Family uint8
pkg syscall (netbsd-arm64-cgo), type RawSockaddrUnix struct, Len uint8
pkg syscall (netbsd-arm64-cgo), type RawSockaddrUnix struct, Path [104]int8
pkg syscall (netbsd-arm64-cgo), type Rlimit struct
pkg syscall (netbsd-arm64-cgo), type Rlimit struct, Cur uint64
pkg syscall (netbsd-arm64-cgo), type Rlimit struct, Max uint64
pkg syscall (netbsd-arm64-cgo), type RouteMessage struct
pkg syscall (netbsd-arm64-cgo), type RouteMessage struct, Data []uint8
pkg syscall (netbsd-arm64-cgo), type RouteMessage struct, Header RtMsghdr
pkg syscall (netbsd-arm64-cgo), type RoutingMessage interface, unexported methods
pkg syscall (netbsd-arm64-cgo), type RtMetrics struct
pkg syscall (netbsd-arm64-cgo), type RtMetrics struct, Expire int64
pkg syscall (netbsd-arm64-cgo), type RtMetrics struct, Hopcount uint64
pkg syscall (netbsd-arm64-cgo), type RtMetrics struct, Locks uint64
pkg syscall (netbsd-arm64-cgo), type RtMetrics struct, Mtu uint64
pkg syscall (netbsd-arm64-cgo), type RtMetrics struct, Pksent int64
pkg syscall (netbsd-arm64-cgo), type RtMetrics struct, Recvpipe uint64
pkg syscall (netbsd-arm64-cgo), type RtMetrics struct, Rtt uint64
pkg syscall (netbsd-arm64-cgo), type RtMetrics struct, Rttvar uint64
pkg syscall (netbsd-arm64-cgo), type RtMetrics struct, Sendpipe uint64
pkg syscall (netbsd-arm64-cgo), type RtMetrics struct, Ssthresh uint64
pkg syscall (netbsd-arm64-cgo), type RtMsghdr struct
pkg syscall (netbsd-arm64-cgo), type RtMsghdr struct, Addrs int32
pkg syscall (netbsd-arm64-cgo), type RtMsghdr struct, Errno int32
pkg syscall (netbsd-arm64-cgo), type RtMsghdr struct, Flags int32
pkg syscall (netbsd-arm64-cgo), type RtMsghdr struct, Index uint16
pkg syscall (netbsd-arm64-cgo), type RtMsghdr struct, Inits int32
pkg syscall (netbsd-arm64-cgo), type RtMsghdr struct, Msglen uint16
pkg syscall (netbsd-arm64-cgo), type RtMsghdr struct, Pad_cgo_0 [2]uint8
pkg syscall (netbsd-arm64-cgo), type RtMsghdr struct, Pad_cgo_1 [4]uint8
pkg syscall (netbsd-arm64-cgo), type RtMsghdr struct, Pid int32
pkg syscall (netbsd-arm64-cgo), type RtMsghdr struct, Rmx RtMetrics
pkg syscall (netbsd-arm64-cgo), type RtMsghdr struct, Seq int32
pkg syscall (netbsd-arm64-cgo), type RtMsghdr struct, Type uint8
pkg syscall (netbsd-arm64-cgo), type RtMsghdr struct, Use int32
pkg syscall (netbsd-arm64-cgo), type RtMsghdr struct, Version uint8
pkg syscall (netbsd-arm64-cgo), type Rusage struct, Idrss int64
pkg syscall (netbsd-arm64-cgo), type Rusage struct, Inblock int64
pkg syscall (netbsd-arm64-cgo), type Rusage struct, Isrss int64
pkg syscall (netbsd-arm64-cgo), type Rusage struct, Ixrss int64
pkg syscall (netbsd-arm64-cgo), type Rusage struct, Majflt int64
pkg syscall (netbsd-arm64-cgo), type Rusage struct, Maxrss int64
pkg syscall (netbsd-arm64-cgo), type Rusage struct, Minflt int64
pkg syscall (netbsd-arm64-cgo), type Rusage struct, Msgrcv int64
pkg syscall (netbsd-arm64-cgo), type Rusage struct, Msgsnd int64
pkg syscall (netbsd-arm64-cgo), type Rusage struct, Nivcsw int64
pkg syscall (netbsd-arm64-cgo), type Rusage struct, Nsignals int64
pkg syscall (netbsd-arm64-cgo), type Rusage struct, Nswap int64
pkg syscall (netbsd-arm64-cgo), type Rusage struct, Nvcsw int64
pkg syscall (netbsd-arm64-cgo), type Rusage struct, Oublock int64
pkg syscall (netbsd-arm64-cgo), type Rusage struct, Stime Timeval
pkg syscall (netbsd-arm64-cgo), type Rusage struct, Utime Timeval
pkg syscall (netbsd-arm64-cgo), type SockaddrDatalink struct
pkg syscall (netbsd-arm64-cgo), type SockaddrDatalink struct, Alen uint8
pkg syscall (netbsd-arm64-cgo), type SockaddrDatalink struct, Data [12]int8
pkg syscall (netbsd-arm64-cgo), type SockaddrDatalink struct, Family uint8
pkg syscall (netbsd-arm64-cgo), type SockaddrDatalink struct, Index uint16
pkg syscall (netbsd-arm64-cgo), type SockaddrDatalink struct, Len uint8
pkg syscall (netbsd-arm64-cgo), type SockaddrDatalink struct, Nlen uint8
pkg syscall (netbsd-arm64-cgo), type SockaddrDatalink struct, Slen uint8
pkg syscall (netbsd-arm64-cgo), type SockaddrDatalink struct, Type uint8
pkg syscall (netbsd-arm64-cgo), type SocketControlMessage struct
pkg syscall (netbsd-arm64-cgo), type SocketControlMessage struct, Data []uint8
pkg syscall (netbsd-arm64-cgo), type SocketControlMessage struct, Header Cmsghdr
pkg syscall (netbsd-arm64-cgo), type Statfs_t [0]uint8
pkg syscall (netbsd-arm64-cgo), type Stat_t struct
pkg syscall (netbsd-arm64-cgo), type Stat_t struct, Atimespec Timespec
pkg syscall (netbsd-arm64-cgo), type Stat_t struct, Birthtimespec Timespec
pkg syscall (netbsd-arm64-cgo), type Stat_t struct, Blksize uint32
pkg syscall (netbsd-arm64-cgo), type Stat_t struct, Blocks int64
pkg syscall (netbsd-arm64-cgo), type Stat_t struct, Ctimespec Timespec
pkg syscall (netbsd-arm64-cgo), type Stat_t struct, Dev uint64
pkg syscall (netbsd-arm64-cgo), type Stat_t struct, Flags uint32
pkg syscall (netbsd-arm64-cgo), type Stat_t struct, Gen uint32
pkg syscall (netbsd-arm64-cgo), type Stat_t struct, Gid uint32
pkg syscall (netbsd-arm64-cgo), type Stat_t struct, Ino uint64
pkg syscall (netbsd-arm64-cgo), type Stat_t struct, Mode uint32
pkg syscall (netbsd-arm64-cgo), type Stat_t struct, Mtimespec Timespec
pkg syscall (netbsd-arm64-cgo), type Stat_t struct, Nlink uint32
pkg syscall (netbsd-arm64-cgo), type Stat_t struct, Pad_cgo_0 [4]uint8
pkg syscall (netbsd-arm64-cgo), type Stat_t struct, Pad_cgo_1 [4]uint8
pkg syscall (netbsd-arm64-cgo), type Stat_t struct, Pad_cgo_2 [4]uint8
pkg syscall (netbsd-arm64-cgo), type Stat_t struct, Rdev uint64
pkg syscall (netbsd-arm64-cgo), type Stat_t struct, Size int64
pkg syscall (netbsd-arm64-cgo), type Stat_t struct, Spare [2]uint32
pkg syscall (netbsd-arm64-cgo), type Stat_t struct, Uid uint32
pkg syscall (netbsd-arm64-cgo), type Sysctlnode struct
pkg syscall (netbsd-arm64-cgo), type Sysctlnode struct, Flags uint32
pkg syscall (netbsd-arm64-cgo), type Sysctlnode struct, Name [32]int8
pkg syscall (netbsd-arm64-cgo), type Sysctlnode struct, Num int32
pkg syscall (netbsd-arm64-cgo), type Sysctlnode struct, Un [16]uint8
pkg syscall (netbsd-arm64-cgo), type Sysctlnode struct, Ver uint32
pkg syscall (netbsd-arm64-cgo), type Sysctlnode struct, X__rsvd uint32
pkg syscall (netbsd-arm64-cgo), type Sysctlnode struct, X_sysctl_desc [8]uint8
pkg syscall (netbsd-arm64-cgo), type Sysctlnode struct, X_sysctl_func [8]uint8
pkg syscall (netbsd-arm64-cgo), type Sysctlnode struct, X_sysctl_parent [8]uint8
pkg syscall (netbsd-arm64-cgo), type Sysctlnode struct, X_sysctl_size [8]uint8
pkg syscall (netbsd-arm64-cgo), type SysProcAttr struct, Chroot string
pkg syscall (netbsd-arm64-cgo), type SysProcAttr struct, Credential *Credential
pkg syscall (netbsd-arm64-cgo), type SysProcAttr struct, Ctty int
pkg syscall (netbsd-arm64-cgo), type SysProcAttr struct, Foreground bool
pkg syscall (netbsd-arm64-cgo), type SysProcAttr struct, Noctty bool
pkg syscall (netbsd-arm64-cgo), type SysProcAttr struct, Pgid int
pkg syscall (netbsd-arm64-cgo), type SysProcAttr struct, Ptrace bool
pkg syscall (netbsd-arm64-cgo), type SysProcAttr struct, Setctty bool
pkg syscall (netbsd-arm64-cgo), type SysProcAttr struct, Setpgid bool
pkg syscall (netbsd-arm64-cgo), type SysProcAttr struct, Setsid bool
pkg syscall (netbsd-arm64-cgo), type Termios struct
pkg syscall (netbsd-arm64-cgo), type Termios struct, Cc [20]uint8
pkg syscall (netbsd-arm64-cgo), type Termios struct, Cflag uint32
pkg syscall (netbsd-arm64-cgo), type Termios struct, Iflag uint32
pkg syscall (netbsd-arm64-cgo), type Termios struct, Ispeed int32
pkg syscall (netbsd-arm64-cgo), type Termios struct, Lflag uint32
pkg syscall (netbsd-arm64-cgo), type Termios struct, Oflag uint32
pkg syscall (netbsd-arm64-cgo), type Termios struct, Ospeed int32
pkg syscall (netbsd-arm64-cgo), type Timespec struct, Nsec int64
pkg syscall (netbsd-arm64-cgo), type Timespec struct, Sec int64
pkg syscall (netbsd-arm64-cgo), type Timeval struct, Pad_cgo_0 [4]uint8
pkg syscall (netbsd-arm64-cgo), type Timeval struct, Sec int64
pkg syscall (netbsd-arm64-cgo), type Timeval struct, Usec int32
pkg syscall (netbsd-arm64-cgo), type WaitStatus uint32
pkg syscall (netbsd-arm64-cgo), var Stderr int
pkg syscall (netbsd-arm64-cgo), var Stdin int
pkg syscall (netbsd-arm64-cgo), var Stdout int
pkg syscall (netbsd-arm64), const AF_APPLETALK = 16
pkg syscall (netbsd-arm64), const AF_APPLETALK ideal-int
pkg syscall (netbsd-arm64), const AF_ARP = 28
pkg syscall (netbsd-arm64), const AF_ARP ideal-int
pkg syscall (netbsd-arm64), const AF_BLUETOOTH = 31
pkg syscall (netbsd-arm64), const AF_BLUETOOTH ideal-int
pkg syscall (netbsd-arm64), const AF_CCITT = 10
pkg syscall (netbsd-arm64), const AF_CCITT ideal-int
pkg syscall (netbsd-arm64), const AF_CHAOS = 5
pkg syscall (netbsd-arm64), const AF_CHAOS ideal-int
pkg syscall (netbsd-arm64), const AF_CNT = 21
pkg syscall (netbsd-arm64), const AF_CNT ideal-int
pkg syscall (netbsd-arm64), const AF_COIP = 20
pkg syscall (netbsd-arm64), const AF_COIP ideal-int
pkg syscall (netbsd-arm64), const AF_DATAKIT = 9
pkg syscall (netbsd-arm64), const AF_DATAKIT ideal-int
pkg syscall (netbsd-arm64), const AF_DECnet = 12
pkg syscall (netbsd-arm64), const AF_DECnet ideal-int
pkg syscall (netbsd-arm64), const AF_DLI = 13
pkg syscall (netbsd-arm64), const AF_DLI ideal-int
pkg syscall (netbsd-arm64), const AF_E164 = 26
pkg syscall (netbsd-arm64), const AF_E164 ideal-int
pkg syscall (netbsd-arm64), const AF_ECMA = 8
pkg syscall (netbsd-arm64), const AF_ECMA ideal-int
pkg syscall (netbsd-arm64), const AF_HYLINK = 15
pkg syscall (netbsd-arm64), const AF_HYLINK ideal-int
pkg syscall (netbsd-arm64), const AF_IEEE80211 = 32
pkg syscall (netbsd-arm64), const AF_IEEE80211 ideal-int
pkg syscall (netbsd-arm64), const AF_IMPLINK = 3
pkg syscall (netbsd-arm64), const AF_IMPLINK ideal-int
pkg syscall (netbsd-arm64), const AF_INET6 = 24
pkg syscall (netbsd-arm64), const AF_IPX = 23
pkg syscall (netbsd-arm64), const AF_IPX ideal-int
pkg syscall (netbsd-arm64), const AF_ISDN = 26
pkg syscall (netbsd-arm64), const AF_ISDN ideal-int
pkg syscall (netbsd-arm64), const AF_ISO = 7
pkg syscall (netbsd-arm64), const AF_ISO ideal-int
pkg syscall (netbsd-arm64), const AF_LAT = 14
pkg syscall (netbsd-arm64), const AF_LAT ideal-int
pkg syscall (netbsd-arm64), const AF_LINK = 18
pkg syscall (netbsd-arm64), const AF_LINK ideal-int
pkg syscall (netbsd-arm64), const AF_LOCAL = 1
pkg syscall (netbsd-arm64), const AF_LOCAL ideal-int
pkg syscall (netbsd-arm64), const AF_MAX = 35
pkg syscall (netbsd-arm64), const AF_MAX ideal-int
pkg syscall (netbsd-arm64), const AF_MPLS = 33
pkg syscall (netbsd-arm64), const AF_MPLS ideal-int
pkg syscall (netbsd-arm64), const AF_NATM = 27
pkg syscall (netbsd-arm64), const AF_NATM ideal-int
pkg syscall (netbsd-arm64), const AF_NS = 6
pkg syscall (netbsd-arm64), const AF_NS ideal-int
pkg syscall (netbsd-arm64), const AF_OROUTE = 17
pkg syscall (netbsd-arm64), const AF_OROUTE ideal-int
pkg syscall (netbsd-arm64), const AF_OSI = 7
pkg syscall (netbsd-arm64), const AF_OSI ideal-int
pkg syscall (netbsd-arm64), const AF_PUP = 4
pkg syscall (netbsd-arm64), const AF_PUP ideal-int
pkg syscall (netbsd-arm64), const AF_ROUTE = 34
pkg syscall (netbsd-arm64), const AF_ROUTE ideal-int
pkg syscall (netbsd-arm64), const AF_SNA = 11
pkg syscall (netbsd-arm64), const AF_SNA ideal-int
pkg syscall (netbsd-arm64), const ARPHRD_ARCNET = 7
pkg syscall (netbsd-arm64), const ARPHRD_ARCNET ideal-int
pkg syscall (netbsd-arm64), const ARPHRD_ETHER = 1
pkg syscall (netbsd-arm64), const ARPHRD_ETHER ideal-int
pkg syscall (netbsd-arm64), const ARPHRD_FRELAY = 15
pkg syscall (netbsd-arm64), const ARPHRD_FRELAY ideal-int
pkg syscall (netbsd-arm64), const ARPHRD_IEEE1394 = 24
pkg syscall (netbsd-arm64), const ARPHRD_IEEE1394 ideal-int
pkg syscall (netbsd-arm64), const ARPHRD_IEEE802 = 6
pkg syscall (netbsd-arm64), const ARPHRD_IEEE802 ideal-int
pkg syscall (netbsd-arm64), const ARPHRD_STRIP = 23
pkg syscall (netbsd-arm64), const ARPHRD_STRIP ideal-int
pkg syscall (netbsd-arm64), const B0 = 0
pkg syscall (netbsd-arm64), const B0 ideal-int
pkg syscall (netbsd-arm64), const B110 = 110
pkg syscall (netbsd-arm64), const B110 ideal-int
pkg syscall (netbsd-arm64), const B115200 = 115200
pkg syscall (netbsd-arm64), const B115200 ideal-int
pkg syscall (netbsd-arm64), const B1200 = 1200
pkg syscall (netbsd-arm64), const B1200 ideal-int
pkg syscall (netbsd-arm64), const B134 = 134
pkg syscall (netbsd-arm64), const B134 ideal-int
pkg syscall (netbsd-arm64), const B14400 = 14400
pkg syscall (netbsd-arm64), const B14400 ideal-int
pkg syscall (netbsd-arm64), const B150 = 150
pkg syscall (netbsd-arm64), const B150 ideal-int
pkg syscall (netbsd-arm64), const B1800 = 1800
pkg syscall (netbsd-arm64), const B1800 ideal-int
pkg syscall (netbsd-arm64), const B19200 = 19200
pkg syscall (netbsd-arm64), const B19200 ideal-int
pkg syscall (netbsd-arm64), const B200 = 200
pkg syscall (netbsd-arm64), const B200 ideal-int
pkg syscall (netbsd-arm64), const B230400 = 230400
pkg syscall (netbsd-arm64), const B230400 ideal-int
pkg syscall (netbsd-arm64), const B2400 = 2400
pkg syscall (netbsd-arm64), const B2400 ideal-int
pkg syscall (netbsd-arm64), const B28800 = 28800
pkg syscall (netbsd-arm64), const B28800 ideal-int
pkg syscall (netbsd-arm64), const B300 = 300
pkg syscall (netbsd-arm64), const B300 ideal-int
pkg syscall (netbsd-arm64), const B38400 = 38400
pkg syscall (netbsd-arm64), const B38400 ideal-int
pkg syscall (netbsd-arm64), const B460800 = 460800
pkg syscall (netbsd-arm64), const B460800 ideal-int
pkg syscall (netbsd-arm64), const B4800 = 4800
pkg syscall (netbsd-arm64), const B4800 ideal-int
pkg syscall (netbsd-arm64), const B50 = 50
pkg syscall (netbsd-arm64), const B50 ideal-int
pkg syscall (netbsd-arm64), const B57600 = 57600
pkg syscall (netbsd-arm64), const B57600 ideal-int
pkg syscall (netbsd-arm64), const B600 = 600
pkg syscall (netbsd-arm64), const B600 ideal-int
pkg syscall (netbsd-arm64), const B7200 = 7200
pkg syscall (netbsd-arm64), const B7200 ideal-int
pkg syscall (netbsd-arm64), const B75 = 75
pkg syscall (netbsd-arm64), const B75 ideal-int
pkg syscall (netbsd-arm64), const B76800 = 76800
pkg syscall (netbsd-arm64), const B76800 ideal-int
pkg syscall (netbsd-arm64), const B921600 = 921600
pkg syscall (netbsd-arm64), const B921600 ideal-int
pkg syscall (netbsd-arm64), const B9600 = 9600
pkg syscall (netbsd-arm64), const B9600 ideal-int
pkg syscall (netbsd-arm64), const BIOCFEEDBACK = 2147762813
pkg syscall (netbsd-arm64), const BIOCFEEDBACK ideal-int
pkg syscall (netbsd-arm64), const BIOCFLUSH = 536887912
pkg syscall (netbsd-arm64), const BIOCFLUSH ideal-int
pkg syscall (netbsd-arm64), const BIOCGBLEN = 1074020966
pkg syscall (netbsd-arm64), const BIOCGBLEN ideal-int
pkg syscall (netbsd-arm64), const BIOCGDLT = 1074020970
pkg syscall (netbsd-arm64), const BIOCGDLT ideal-int
pkg syscall (netbsd-arm64), const BIOCGDLTLIST = 3222291063
pkg syscall (netbsd-arm64), const BIOCGDLTLIST ideal-int
pkg syscall (netbsd-arm64), const BIOCGETIF = 1083196011
pkg syscall (netbsd-arm64), const BIOCGETIF ideal-int
pkg syscall (netbsd-arm64), const BIOCGFEEDBACK = 1074020988
pkg syscall (netbsd-arm64), const BIOCGFEEDBACK ideal-int
pkg syscall (netbsd-arm64), const BIOCGHDRCMPLT = 1074020980
pkg syscall (netbsd-arm64), const BIOCGHDRCMPLT ideal-int
pkg syscall (netbsd-arm64), const BIOCGRTIMEOUT = 1074807419
pkg syscall (netbsd-arm64), const BIOCGRTIMEOUT ideal-int
pkg syscall (netbsd-arm64), const BIOCGSEESENT = 1074020984
pkg syscall (netbsd-arm64), const BIOCGSEESENT ideal-int
pkg syscall (netbsd-arm64), const BIOCGSTATS = 1082147439
pkg syscall (netbsd-arm64), const BIOCGSTATS ideal-int
pkg syscall (netbsd-arm64), const BIOCGSTATSOLD = 1074283119
pkg syscall (netbsd-arm64), const BIOCGSTATSOLD ideal-int
pkg syscall (netbsd-arm64), const BIOCIMMEDIATE = 2147762800
pkg syscall (netbsd-arm64), const BIOCIMMEDIATE ideal-int
pkg syscall (netbsd-arm64), const BIOCPROMISC = 536887913
pkg syscall (netbsd-arm64), const BIOCPROMISC ideal-int
pkg syscall (netbsd-arm64), const BIOCSBLEN = 3221504614
pkg syscall (netbsd-arm64), const BIOCSBLEN ideal-int
pkg syscall (netbsd-arm64), const BIOCSDLT = 2147762806
pkg syscall (netbsd-arm64), const BIOCSDLT ideal-int
pkg syscall (netbsd-arm64), const BIOCSETF = 2148549223
pkg syscall (netbsd-arm64), const BIOCSETF ideal-int
pkg syscall (netbsd-arm64), const BIOCSETIF = 2156937836
pkg syscall (netbsd-arm64), const BIOCSETIF ideal-int
pkg syscall (netbsd-arm64), const BIOCSFEEDBACK = 2147762813
pkg syscall (netbsd-arm64), const BIOCSFEEDBACK ideal-int
pkg syscall (netbsd-arm64), const BIOCSHDRCMPLT = 2147762805
pkg syscall (netbsd-arm64), const BIOCSHDRCMPLT ideal-int
pkg syscall (netbsd-arm64), const BIOCSRTIMEOUT = 2148549242
pkg syscall (netbsd-arm64), const BIOCSRTIMEOUT ideal-int
pkg syscall (netbsd-arm64), const BIOCSSEESENT = 2147762809
pkg syscall (netbsd-arm64), const BIOCSSEESENT ideal-int
pkg syscall (netbsd-arm64), const BIOCSTCPF = 2148549234
pkg syscall (netbsd-arm64), const BIOCSTCPF ideal-int
pkg syscall (netbsd-arm64), const BIOCSUDPF = 2148549235
pkg syscall (netbsd-arm64), const BIOCSUDPF ideal-int
pkg syscall (netbsd-arm64), const BIOCVERSION = 1074020977
pkg syscall (netbsd-arm64), const BIOCVERSION ideal-int
pkg syscall (netbsd-arm64), const BPF_A = 16
pkg syscall (netbsd-arm64), const BPF_ABS = 32
pkg syscall (netbsd-arm64), const BPF_ABS ideal-int
pkg syscall (netbsd-arm64), const BPF_ADD = 0
pkg syscall (netbsd-arm64), const BPF_ADD ideal-int
pkg syscall (netbsd-arm64), const BPF_A ideal-int
pkg syscall (netbsd-arm64), const BPF_ALIGNMENT32 = 4
pkg syscall (netbsd-arm64), const BPF_ALIGNMENT32 ideal-int
pkg syscall (netbsd-arm64), const BPF_ALIGNMENT = 8
pkg syscall (netbsd-arm64), const BPF_ALIGNMENT ideal-int
pkg syscall (netbsd-arm64), const BPF_ALU = 4
pkg syscall (netbsd-arm64), const BPF_ALU ideal-int
pkg syscall (netbsd-arm64), const BPF_AND = 80
pkg syscall (netbsd-arm64), const BPF_AND ideal-int
pkg syscall (netbsd-arm64), const BPF_B = 16
pkg syscall (netbsd-arm64), const BPF_B ideal-int
pkg syscall (netbsd-arm64), const BPF_DFLTBUFSIZE = 1048576
pkg syscall (netbsd-arm64), const BPF_DFLTBUFSIZE ideal-int
pkg syscall (netbsd-arm64), const BPF_DIV = 48
pkg syscall (netbsd-arm64), const BPF_DIV ideal-int
pkg syscall (netbsd-arm64), const BPF_H = 8
pkg syscall (netbsd-arm64), const BPF_H ideal-int
pkg syscall (netbsd-arm64), const BPF_IMM = 0
pkg syscall (netbsd-arm64), const BPF_IMM ideal-int
pkg syscall (netbsd-arm64), const BPF_IND = 64
pkg syscall (netbsd-arm64), const BPF_IND ideal-int
pkg syscall (netbsd-arm64), const BPF_JA = 0
pkg syscall (netbsd-arm64), const BPF_JA ideal-int
pkg syscall (netbsd-arm64), const BPF_JEQ = 16
pkg syscall (netbsd-arm64), const BPF_JEQ ideal-int
pkg syscall (netbsd-arm64), const BPF_JGE = 48
pkg syscall (netbsd-arm64), const BPF_JGE ideal-int
pkg syscall (netbsd-arm64), const BPF_JGT = 32
pkg syscall (netbsd-arm64), const BPF_JGT ideal-int
pkg syscall (netbsd-arm64), const BPF_JMP = 5
pkg syscall (netbsd-arm64), const BPF_JMP ideal-int
pkg syscall (netbsd-arm64), const BPF_JSET = 64
pkg syscall (netbsd-arm64), const BPF_JSET ideal-int
pkg syscall (netbsd-arm64), const BPF_K = 0
pkg syscall (netbsd-arm64), const BPF_K ideal-int
pkg syscall (netbsd-arm64), const BPF_LD = 0
pkg syscall (netbsd-arm64), const BPF_LD ideal-int
pkg syscall (netbsd-arm64), const BPF_LDX = 1
pkg syscall (netbsd-arm64), const BPF_LDX ideal-int
pkg syscall (netbsd-arm64), const BPF_LEN = 128
pkg syscall (netbsd-arm64), const BPF_LEN ideal-int
pkg syscall (netbsd-arm64), const BPF_LSH = 96
pkg syscall (netbsd-arm64), const BPF_LSH ideal-int
pkg syscall (netbsd-arm64), const BPF_MAJOR_VERSION = 1
pkg syscall (netbsd-arm64), const BPF_MAJOR_VERSION ideal-int
pkg syscall (netbsd-arm64), const BPF_MAXBUFSIZE = 16777216
pkg syscall (netbsd-arm64), const BPF_MAXBUFSIZE ideal-int
pkg syscall (netbsd-arm64), const BPF_MAXINSNS = 512
pkg syscall (netbsd-arm64), const BPF_MAXINSNS ideal-int
pkg syscall (netbsd-arm64), const BPF_MEM = 96
pkg syscall (netbsd-arm64), const BPF_MEM ideal-int
pkg syscall (netbsd-arm64), const BPF_MEMWORDS = 16
pkg syscall (netbsd-arm64), const BPF_MEMWORDS ideal-int
pkg syscall (netbsd-arm64), const BPF_MINBUFSIZE = 32
pkg syscall (netbsd-arm64), const BPF_MINBUFSIZE ideal-int
pkg syscall (netbsd-arm64), const BPF_MINOR_VERSION = 1
pkg syscall (netbsd-arm64), const BPF_MINOR_VERSION ideal-int
pkg syscall (netbsd-arm64), const BPF_MISC = 7
pkg syscall (netbsd-arm64), const BPF_MISC ideal-int
pkg syscall (netbsd-arm64), const BPF_MSH = 160
pkg syscall (netbsd-arm64), const BPF_MSH ideal-int
pkg syscall (netbsd-arm64), const BPF_MUL = 32
pkg syscall (netbsd-arm64), const BPF_MUL ideal-int
pkg syscall (netbsd-arm64), const BPF_NEG = 128
pkg syscall (netbsd-arm64), const BPF_NEG ideal-int
pkg syscall (netbsd-arm64), const BPF_OR = 64
pkg syscall (netbsd-arm64), const BPF_OR ideal-int
pkg syscall (netbsd-arm64), const BPF_RELEASE = 199606
pkg syscall (netbsd-arm64), const BPF_RELEASE ideal-int
pkg syscall (netbsd-arm64), const BPF_RET = 6
pkg syscall (netbsd-arm64), const BPF_RET ideal-int
pkg syscall (netbsd-arm64), const BPF_RSH = 112
pkg syscall (netbsd-arm64), const BPF_RSH ideal-int
pkg syscall (netbsd-arm64), const BPF_ST = 2
pkg syscall (netbsd-arm64), const BPF_ST ideal-int
pkg syscall (netbsd-arm64), const BPF_STX = 3
pkg syscall (netbsd-arm64), const BPF_STX ideal-int
pkg syscall (netbsd-arm64), const BPF_SUB = 16
pkg syscall (netbsd-arm64), const BPF_SUB ideal-int
pkg syscall (netbsd-arm64), const BPF_TAX = 0
pkg syscall (netbsd-arm64), const BPF_TAX ideal-int
pkg syscall (netbsd-arm64), const BPF_TXA = 128
pkg syscall (netbsd-arm64), const BPF_TXA ideal-int
pkg syscall (netbsd-arm64), const BPF_W = 0
pkg syscall (netbsd-arm64), const BPF_W ideal-int
pkg syscall (netbsd-arm64), const BPF_X = 8
pkg syscall (netbsd-arm64), const BPF_X ideal-int
pkg syscall (netbsd-arm64), const BRKINT = 2
pkg syscall (netbsd-arm64), const BRKINT ideal-int
pkg syscall (netbsd-arm64), const CFLUSH = 15
pkg syscall (netbsd-arm64), const CFLUSH ideal-int
pkg syscall (netbsd-arm64), const CLOCAL = 32768
pkg syscall (netbsd-arm64), const CLOCAL ideal-int
pkg syscall (netbsd-arm64), const CLONE_CSIGNAL = 255
pkg syscall (netbsd-arm64), const CLONE_CSIGNAL ideal-int
pkg syscall (netbsd-arm64), const CLONE_FILES = 1024
pkg syscall (netbsd-arm64), const CLONE_FILES ideal-int
pkg syscall (netbsd-arm64), const CLONE_FS = 512
pkg syscall (netbsd-arm64), const CLONE_FS ideal-int
pkg syscall (netbsd-arm64), const CLONE_PID = 4096
pkg syscall (netbsd-arm64), const CLONE_PID ideal-int
pkg syscall (netbsd-arm64), const CLONE_PTRACE = 8192
pkg syscall (netbsd-arm64), const CLONE_PTRACE ideal-int
pkg syscall (netbsd-arm64), const CLONE_SIGHAND = 2048
pkg syscall (netbsd-arm64), const CLONE_SIGHAND ideal-int
pkg syscall (netbsd-arm64), const CLONE_VFORK = 16384
pkg syscall (netbsd-arm64), const CLONE_VFORK ideal-int
pkg syscall (netbsd-arm64), const CLONE_VM = 256
pkg syscall (netbsd-arm64), const CLONE_VM ideal-int
pkg syscall (netbsd-arm64), const CREAD = 2048
pkg syscall (netbsd-arm64), const CREAD ideal-int
pkg syscall (netbsd-arm64), const CS5 = 0
pkg syscall (netbsd-arm64), const CS5 ideal-int
pkg syscall (netbsd-arm64), const CS6 = 256
pkg syscall (netbsd-arm64), const CS6 ideal-int
pkg syscall (netbsd-arm64), const CS7 = 512
pkg syscall (netbsd-arm64), const CS7 ideal-int
pkg syscall (netbsd-arm64), const CS8 = 768
pkg syscall (netbsd-arm64), const CS8 ideal-int
pkg syscall (netbsd-arm64), const CSIZE = 768
pkg syscall (netbsd-arm64), const CSIZE ideal-int
pkg syscall (netbsd-arm64), const CSTART = 17
pkg syscall (netbsd-arm64), const CSTART ideal-int
pkg syscall (netbsd-arm64), const CSTATUS = 20
pkg syscall (netbsd-arm64), const CSTATUS ideal-int
pkg syscall (netbsd-arm64), const CSTOP = 19
pkg syscall (netbsd-arm64), const CSTOPB = 1024
pkg syscall (netbsd-arm64), const CSTOPB ideal-int
pkg syscall (netbsd-arm64), const CSTOP ideal-int
pkg syscall (netbsd-arm64), const CSUSP = 26
pkg syscall (netbsd-arm64), const CSUSP ideal-int
pkg syscall (netbsd-arm64), const CTL_MAXNAME = 12
pkg syscall (netbsd-arm64), const CTL_MAXNAME ideal-int
pkg syscall (netbsd-arm64), const CTL_NET = 4
pkg syscall (netbsd-arm64), const CTL_NET ideal-int
pkg syscall (netbsd-arm64), const CTL_QUERY = -2
pkg syscall (netbsd-arm64), const CTL_QUERY ideal-int
pkg syscall (netbsd-arm64), const DIOCBSFLUSH = 536896632
pkg syscall (netbsd-arm64), const DIOCBSFLUSH ideal-int
pkg syscall (netbsd-arm64), const DLT_A429 = 184
pkg syscall (netbsd-arm64), const DLT_A429 ideal-int
pkg syscall (netbsd-arm64), const DLT_A653_ICM = 185
pkg syscall (netbsd-arm64), const DLT_A653_ICM ideal-int
pkg syscall (netbsd-arm64), const DLT_AIRONET_HEADER = 120
pkg syscall (netbsd-arm64), const DLT_AIRONET_HEADER ideal-int
pkg syscall (netbsd-arm64), const DLT_AOS = 222
pkg syscall (netbsd-arm64), const DLT_AOS ideal-int
pkg syscall (netbsd-arm64), const DLT_APPLE_IP_OVER_IEEE1394 = 138
pkg syscall (netbsd-arm64), const DLT_APPLE_IP_OVER_IEEE1394 ideal-int
pkg syscall (netbsd-arm64), const DLT_ARCNET = 7
pkg syscall (netbsd-arm64), const DLT_ARCNET ideal-int
pkg syscall (netbsd-arm64), const DLT_ARCNET_LINUX = 129
pkg syscall (netbsd-arm64), const DLT_ARCNET_LINUX ideal-int
pkg syscall (netbsd-arm64), const DLT_ATM_CLIP = 19
pkg syscall (netbsd-arm64), const DLT_ATM_CLIP ideal-int
pkg syscall (netbsd-arm64), const DLT_ATM_RFC1483 = 11
pkg syscall (netbsd-arm64), const DLT_ATM_RFC1483 ideal-int
pkg syscall (netbsd-arm64), const DLT_AURORA = 126
pkg syscall (netbsd-arm64), const DLT_AURORA ideal-int
pkg syscall (netbsd-arm64), const DLT_AX25 = 3
pkg syscall (netbsd-arm64), const DLT_AX25 ideal-int
pkg syscall (netbsd-arm64), const DLT_AX25_KISS = 202
pkg syscall (netbsd-arm64), const DLT_AX25_KISS ideal-int
pkg syscall (netbsd-arm64), const DLT_BACNET_MS_TP = 165
pkg syscall (netbsd-arm64), const DLT_BACNET_MS_TP ideal-int
pkg syscall (netbsd-arm64), const DLT_BLUETOOTH_HCI_H4 = 187
pkg syscall (netbsd-arm64), const DLT_BLUETOOTH_HCI_H4 ideal-int
pkg syscall (netbsd-arm64), const DLT_BLUETOOTH_HCI_H4_WITH_PHDR = 201
pkg syscall (netbsd-arm64), const DLT_BLUETOOTH_HCI_H4_WITH_PHDR ideal-int
pkg syscall (netbsd-arm64), const DLT_CAN20B = 190
pkg syscall (netbsd-arm64), const DLT_CAN20B ideal-int
pkg syscall (netbsd-arm64), const DLT_CAN_SOCKETCAN = 227
pkg syscall (netbsd-arm64), const DLT_CAN_SOCKETCAN ideal-int
pkg syscall (netbsd-arm64), const DLT_CHAOS = 5
pkg syscall (netbsd-arm64), const DLT_CHAOS ideal-int
pkg syscall (netbsd-arm64), const DLT_C_HDLC = 104
pkg syscall (netbsd-arm64), const DLT_C_HDLC ideal-int
pkg syscall (netbsd-arm64), const DLT_C_HDLC_WITH_DIR = 205
pkg syscall (netbsd-arm64), const DLT_C_HDLC_WITH_DIR ideal-int
pkg syscall (netbsd-arm64), const DLT_CISCO_IOS = 118
pkg syscall (netbsd-arm64), const DLT_CISCO_IOS ideal-int
pkg syscall (netbsd-arm64), const DLT_DECT = 221
pkg syscall (netbsd-arm64), const DLT_DECT ideal-int
pkg syscall (netbsd-arm64), const DLT_DOCSIS = 143
pkg syscall (netbsd-arm64), const DLT_DOCSIS ideal-int
pkg syscall (netbsd-arm64), const DLT_ECONET = 115
pkg syscall (netbsd-arm64), const DLT_ECONET ideal-int
pkg syscall (netbsd-arm64), const DLT_EN10MB = 1
pkg syscall (netbsd-arm64), const DLT_EN10MB ideal-int
pkg syscall (netbsd-arm64), const DLT_EN3MB = 2
pkg syscall (netbsd-arm64), const DLT_EN3MB ideal-int
pkg syscall (netbsd-arm64), const DLT_ENC = 109
pkg syscall (netbsd-arm64), const DLT_ENC ideal-int
pkg syscall (netbsd-arm64), const DLT_ERF = 197
pkg syscall (netbsd-arm64), const DLT_ERF_ETH = 175
pkg syscall (netbsd-arm64), const DLT_ERF_ETH ideal-int
pkg syscall (netbsd-arm64), const DLT_ERF ideal-int
pkg syscall (netbsd-arm64), const DLT_ERF_POS = 176
pkg syscall (netbsd-arm64), const DLT_ERF_POS ideal-int
pkg syscall (netbsd-arm64), const DLT_FC_2 = 224
pkg syscall (netbsd-arm64), const DLT_FC_2 ideal-int
pkg syscall (netbsd-arm64), const DLT_FC_2_WITH_FRAME_DELIMS = 225
pkg syscall (netbsd-arm64), const DLT_FC_2_WITH_FRAME_DELIMS ideal-int
pkg syscall (netbsd-arm64), const DLT_FDDI = 10
pkg syscall (netbsd-arm64), const DLT_FDDI ideal-int
pkg syscall (netbsd-arm64), const DLT_FLEXRAY = 210
pkg syscall (netbsd-arm64), const DLT_FLEXRAY ideal-int
pkg syscall (netbsd-arm64), const DLT_FRELAY = 107
pkg syscall (netbsd-arm64), const DLT_FRELAY ideal-int
pkg syscall (netbsd-arm64), const DLT_FRELAY_WITH_DIR = 206
pkg syscall (netbsd-arm64), const DLT_FRELAY_WITH_DIR ideal-int
pkg syscall (netbsd-arm64), const DLT_GCOM_SERIAL = 173
pkg syscall (netbsd-arm64), const DLT_GCOM_SERIAL ideal-int
pkg syscall (netbsd-arm64), const DLT_GCOM_T1E1 = 172
pkg syscall (netbsd-arm64), const DLT_GCOM_T1E1 ideal-int
pkg syscall (netbsd-arm64), const DLT_GPF_F = 171
pkg syscall (netbsd-arm64), const DLT_GPF_F ideal-int
pkg syscall (netbsd-arm64), const DLT_GPF_T = 170
pkg syscall (netbsd-arm64), const DLT_GPF_T ideal-int
pkg syscall (netbsd-arm64), const DLT_GPRS_LLC = 169
pkg syscall (netbsd-arm64), const DLT_GPRS_LLC ideal-int
pkg syscall (netbsd-arm64), const DLT_GSMTAP_ABIS = 218
pkg syscall (netbsd-arm64), const DLT_GSMTAP_ABIS ideal-int
pkg syscall (netbsd-arm64), const DLT_GSMTAP_UM = 217
pkg syscall (netbsd-arm64), const DLT_GSMTAP_UM ideal-int
pkg syscall (netbsd-arm64), const DLT_HDLC = 16
pkg syscall (netbsd-arm64), const DLT_HDLC ideal-int
pkg syscall (netbsd-arm64), const DLT_HHDLC = 121
pkg syscall (netbsd-arm64), const DLT_HHDLC ideal-int
pkg syscall (netbsd-arm64), const DLT_HIPPI = 15
pkg syscall (netbsd-arm64), const DLT_HIPPI ideal-int
pkg syscall (netbsd-arm64), const DLT_IBM_SN = 146
pkg syscall (netbsd-arm64), const DLT_IBM_SN ideal-int
pkg syscall (netbsd-arm64), const DLT_IBM_SP = 145
pkg syscall (netbsd-arm64), const DLT_IBM_SP ideal-int
pkg syscall (netbsd-arm64), const DLT_IEEE802_11 = 105
pkg syscall (netbsd-arm64), const DLT_IEEE802_11 ideal-int
pkg syscall (netbsd-arm64), const DLT_IEEE802_11_RADIO = 127
pkg syscall (netbsd-arm64), const DLT_IEEE802_11_RADIO_AVS = 163
pkg syscall (netbsd-arm64), const DLT_IEEE802_11_RADIO_AVS ideal-int
pkg syscall (netbsd-arm64), const DLT_IEEE802_11_RADIO ideal-int
pkg syscall (netbsd-arm64), const DLT_IEEE802_15_4 = 195
pkg syscall (netbsd-arm64), const DLT_IEEE802_15_4 ideal-int
pkg syscall (netbsd-arm64), const DLT_IEEE802_15_4_LINUX = 191
pkg syscall (netbsd-arm64), const DLT_IEEE802_15_4_LINUX ideal-int
pkg syscall (netbsd-arm64), const DLT_IEEE802_15_4_NONASK_PHY = 215
pkg syscall (netbsd-arm64), const DLT_IEEE802_15_4_NONASK_PHY ideal-int
pkg syscall (netbsd-arm64), const DLT_IEEE802_16_MAC_CPS = 188
pkg syscall (netbsd-arm64), const DLT_IEEE802_16_MAC_CPS ideal-int
pkg syscall (netbsd-arm64), const DLT_IEEE802_16_MAC_CPS_RADIO = 193
pkg syscall (netbsd-arm64), const DLT_IEEE802_16_MAC_CPS_RADIO ideal-int
pkg syscall (netbsd-arm64), const DLT_IEEE802 = 6
pkg syscall (netbsd-arm64), const DLT_IEEE802 ideal-int
pkg syscall (netbsd-arm64), const DLT_IPMB = 199
pkg syscall (netbsd-arm64), const DLT_IPMB ideal-int
pkg syscall (netbsd-arm64), const DLT_IPMB_LINUX = 209
pkg syscall (netbsd-arm64), const DLT_IPMB_LINUX ideal-int
pkg syscall (netbsd-arm64), const DLT_IPNET = 226
pkg syscall (netbsd-arm64), const DLT_IPNET ideal-int
pkg syscall (netbsd-arm64), const DLT_IP_OVER_FC = 122
pkg syscall (netbsd-arm64), const DLT_IP_OVER_FC ideal-int
pkg syscall (netbsd-arm64), const DLT_IPV4 = 228
pkg syscall (netbsd-arm64), const DLT_IPV4 ideal-int
pkg syscall (netbsd-arm64), const DLT_IPV6 = 229
pkg syscall (netbsd-arm64), const DLT_IPV6 ideal-int
pkg syscall (netbsd-arm64), const DLT_JUNIPER_ATM1 = 137
pkg syscall (netbsd-arm64), const DLT_JUNIPER_ATM1 ideal-int
pkg syscall (netbsd-arm64), const DLT_JUNIPER_ATM2 = 135
pkg syscall (netbsd-arm64), const DLT_JUNIPER_ATM2 ideal-int
pkg syscall (netbsd-arm64), const DLT_JUNIPER_CHDLC = 181
pkg syscall (netbsd-arm64), const DLT_JUNIPER_CHDLC ideal-int
pkg syscall (netbsd-arm64), const DLT_JUNIPER_ES = 132
pkg syscall (netbsd-arm64), const DLT_JUNIPER_ES ideal-int
pkg syscall (netbsd-arm64), const DLT_JUNIPER_ETHER = 178
pkg syscall (netbsd-arm64), const DLT_JUNIPER_ETHER ideal-int
pkg syscall (netbsd-arm64), const DLT_JUNIPER_FRELAY = 180
pkg syscall (netbsd-arm64), const DLT_JUNIPER_FRELAY ideal-int
pkg syscall (netbsd-arm64), const DLT_JUNIPER_GGSN = 133
pkg syscall (netbsd-arm64), const DLT_JUNIPER_GGSN ideal-int
pkg syscall (netbsd-arm64), const DLT_JUNIPER_ISM = 194
pkg syscall (netbsd-arm64), const DLT_JUNIPER_ISM ideal-int
pkg syscall (netbsd-arm64), const DLT_JUNIPER_MFR = 134
pkg syscall (netbsd-arm64), const DLT_JUNIPER_MFR ideal-int
pkg syscall (netbsd-arm64), const DLT_JUNIPER_MLFR = 131
pkg syscall (netbsd-arm64), const DLT_JUNIPER_MLFR ideal-int
pkg syscall (netbsd-arm64), const DLT_JUNIPER_MLPPP = 130
pkg syscall (netbsd-arm64), const DLT_JUNIPER_MLPPP ideal-int
pkg syscall (netbsd-arm64), const DLT_JUNIPER_MONITOR = 164
pkg syscall (netbsd-arm64), const DLT_JUNIPER_MONITOR ideal-int
pkg syscall (netbsd-arm64), const DLT_JUNIPER_PIC_PEER = 174
pkg syscall (netbsd-arm64), const DLT_JUNIPER_PIC_PEER ideal-int
pkg syscall (netbsd-arm64), const DLT_JUNIPER_PPP = 179
pkg syscall (netbsd-arm64), const DLT_JUNIPER_PPP ideal-int
pkg syscall (netbsd-arm64), const DLT_JUNIPER_PPPOE = 167
pkg syscall (netbsd-arm64), const DLT_JUNIPER_PPPOE_ATM = 168
pkg syscall (netbsd-arm64), const DLT_JUNIPER_PPPOE_ATM ideal-int
pkg syscall (netbsd-arm64), const DLT_JUNIPER_PPPOE ideal-int
pkg syscall (netbsd-arm64), const DLT_JUNIPER_SERVICES = 136
pkg syscall (netbsd-arm64), const DLT_JUNIPER_SERVICES ideal-int
pkg syscall (netbsd-arm64), const DLT_JUNIPER_ST = 200
pkg syscall (netbsd-arm64), const DLT_JUNIPER_ST ideal-int
pkg syscall (netbsd-arm64), const DLT_JUNIPER_VP = 183
pkg syscall (netbsd-arm64), const DLT_JUNIPER_VP ideal-int
pkg syscall (netbsd-arm64), const DLT_LAPB_WITH_DIR = 207
pkg syscall (netbsd-arm64), const DLT_LAPB_WITH_DIR ideal-int
pkg syscall (netbsd-arm64), const DLT_LAPD = 203
pkg syscall (netbsd-arm64), const DLT_LAPD ideal-int
pkg syscall (netbsd-arm64), const DLT_LIN = 212
pkg syscall (netbsd-arm64), const DLT_LIN ideal-int
pkg syscall (netbsd-arm64), const DLT_LINUX_EVDEV = 216
pkg syscall (netbsd-arm64), const DLT_LINUX_EVDEV ideal-int
pkg syscall (netbsd-arm64), const DLT_LINUX_IRDA = 144
pkg syscall (netbsd-arm64), const DLT_LINUX_IRDA ideal-int
pkg syscall (netbsd-arm64), const DLT_LINUX_LAPD = 177
pkg syscall (netbsd-arm64), const DLT_LINUX_LAPD ideal-int
pkg syscall (netbsd-arm64), const DLT_LINUX_SLL = 113
pkg syscall (netbsd-arm64), const DLT_LINUX_SLL ideal-int
pkg syscall (netbsd-arm64), const DLT_LOOP = 108
pkg syscall (netbsd-arm64), const DLT_LOOP ideal-int
pkg syscall (netbsd-arm64), const DLT_LTALK = 114
pkg syscall (netbsd-arm64), const DLT_LTALK ideal-int
pkg syscall (netbsd-arm64), const DLT_MFR = 182
pkg syscall (netbsd-arm64), const DLT_MFR ideal-int
pkg syscall (netbsd-arm64), const DLT_MOST = 211
pkg syscall (netbsd-arm64), const DLT_MOST ideal-int
pkg syscall (netbsd-arm64), const DLT_MPLS = 219
pkg syscall (netbsd-arm64), const DLT_MPLS ideal-int
pkg syscall (netbsd-arm64), const DLT_MTP2 = 140
pkg syscall (netbsd-arm64), const DLT_MTP2 ideal-int
pkg syscall (netbsd-arm64), const DLT_MTP2_WITH_PHDR = 139
pkg syscall (netbsd-arm64), const DLT_MTP2_WITH_PHDR ideal-int
pkg syscall (netbsd-arm64), const DLT_MTP3 = 141
pkg syscall (netbsd-arm64), const DLT_MTP3 ideal-int
pkg syscall (netbsd-arm64), const DLT_NULL = 0
pkg syscall (netbsd-arm64), const DLT_NULL ideal-int
pkg syscall (netbsd-arm64), const DLT_PCI_EXP = 125
pkg syscall (netbsd-arm64), const DLT_PCI_EXP ideal-int
pkg syscall (netbsd-arm64), const DLT_PFLOG = 117
pkg syscall (netbsd-arm64), const DLT_PFLOG ideal-int
pkg syscall (netbsd-arm64), const DLT_PFSYNC = 18
pkg syscall (netbsd-arm64), const DLT_PFSYNC ideal-int
pkg syscall (netbsd-arm64), const DLT_PPI = 192
pkg syscall (netbsd-arm64), const DLT_PPI ideal-int
pkg syscall (netbsd-arm64), const DLT_PPP = 9
pkg syscall (netbsd-arm64), const DLT_PPP_BSDOS = 14
pkg syscall (netbsd-arm64), const DLT_PPP_BSDOS ideal-int
pkg syscall (netbsd-arm64), const DLT_PPP_ETHER = 51
pkg syscall (netbsd-arm64), const DLT_PPP_ETHER ideal-int
pkg syscall (netbsd-arm64), const DLT_PPP ideal-int
pkg syscall (netbsd-arm64), const DLT_PPP_PPPD = 166
pkg syscall (netbsd-arm64), const DLT_PPP_PPPD ideal-int
pkg syscall (netbsd-arm64), const DLT_PPP_SERIAL = 50
pkg syscall (netbsd-arm64), const DLT_PPP_SERIAL ideal-int
pkg syscall (netbsd-arm64), const DLT_PPP_WITH_DIR = 204
pkg syscall (netbsd-arm64), const DLT_PPP_WITH_DIR ideal-int
pkg syscall (netbsd-arm64), const DLT_PRISM_HEADER = 119
pkg syscall (netbsd-arm64), const DLT_PRISM_HEADER ideal-int
pkg syscall (netbsd-arm64), const DLT_PRONET = 4
pkg syscall (netbsd-arm64), const DLT_PRONET ideal-int
pkg syscall (netbsd-arm64), const DLT_RAIF1 = 198
pkg syscall (netbsd-arm64), const DLT_RAIF1 ideal-int
pkg syscall (netbsd-arm64), const DLT_RAW = 12
pkg syscall (netbsd-arm64), const DLT_RAWAF_MASK = 35913728
pkg syscall (netbsd-arm64), const DLT_RAWAF_MASK ideal-int
pkg syscall (netbsd-arm64), const DLT_RAW ideal-int
pkg syscall (netbsd-arm64), const DLT_RIO = 124
pkg syscall (netbsd-arm64), const DLT_RIO ideal-int
pkg syscall (netbsd-arm64), const DLT_SCCP = 142
pkg syscall (netbsd-arm64), const DLT_SCCP ideal-int
pkg syscall (netbsd-arm64), const DLT_SITA = 196
pkg syscall (netbsd-arm64), const DLT_SITA ideal-int
pkg syscall (netbsd-arm64), const DLT_SLIP = 8
pkg syscall (netbsd-arm64), const DLT_SLIP_BSDOS = 13
pkg syscall (netbsd-arm64), const DLT_SLIP_BSDOS ideal-int
pkg syscall (netbsd-arm64), const DLT_SLIP ideal-int
pkg syscall (netbsd-arm64), const DLT_SUNATM = 123
pkg syscall (netbsd-arm64), const DLT_SUNATM ideal-int
pkg syscall (netbsd-arm64), const DLT_SYMANTEC_FIREWALL = 99
pkg syscall (netbsd-arm64), const DLT_SYMANTEC_FIREWALL ideal-int
pkg syscall (netbsd-arm64), const DLT_TZSP = 128
pkg syscall (netbsd-arm64), const DLT_TZSP ideal-int
pkg syscall (netbsd-arm64), const DLT_USB = 186
pkg syscall (netbsd-arm64), const DLT_USB ideal-int
pkg syscall (netbsd-arm64), const DLT_USB_LINUX = 189
pkg syscall (netbsd-arm64), const DLT_USB_LINUX ideal-int
pkg syscall (netbsd-arm64), const DLT_USB_LINUX_MMAPPED = 220
pkg syscall (netbsd-arm64), const DLT_USB_LINUX_MMAPPED ideal-int
pkg syscall (netbsd-arm64), const DLT_WIHART = 223
pkg syscall (netbsd-arm64), const DLT_WIHART ideal-int
pkg syscall (netbsd-arm64), const DLT_X2E_SERIAL = 213
pkg syscall (netbsd-arm64), const DLT_X2E_SERIAL ideal-int
pkg syscall (netbsd-arm64), const DLT_X2E_XORAYA = 214
pkg syscall (netbsd-arm64), const DLT_X2E_XORAYA ideal-int
pkg syscall (netbsd-arm64), const DT_BLK = 6
pkg syscall (netbsd-arm64), const DT_BLK ideal-int
pkg syscall (netbsd-arm64), const DT_CHR = 2
pkg syscall (netbsd-arm64), const DT_CHR ideal-int
pkg syscall (netbsd-arm64), const DT_DIR = 4
pkg syscall (netbsd-arm64), const DT_DIR ideal-int
pkg syscall (netbsd-arm64), const DT_FIFO = 1
pkg syscall (netbsd-arm64), const DT_FIFO ideal-int
pkg syscall (netbsd-arm64), const DT_LNK = 10
pkg syscall (netbsd-arm64), const DT_LNK ideal-int
pkg syscall (netbsd-arm64), const DT_REG = 8
pkg syscall (netbsd-arm64), const DT_REG ideal-int
pkg syscall (netbsd-arm64), const DT_SOCK = 12
pkg syscall (netbsd-arm64), const DT_SOCK ideal-int
pkg syscall (netbsd-arm64), const DT_UNKNOWN = 0
pkg syscall (netbsd-arm64), const DT_UNKNOWN ideal-int
pkg syscall (netbsd-arm64), const DT_WHT = 14
pkg syscall (netbsd-arm64), const DT_WHT ideal-int
pkg syscall (netbsd-arm64), const E2BIG = 7
pkg syscall (netbsd-arm64), const EACCES = 13
pkg syscall (netbsd-arm64), const EADDRINUSE = 48
pkg syscall (netbsd-arm64), const EADDRNOTAVAIL = 49
pkg syscall (netbsd-arm64), const EAFNOSUPPORT = 47
pkg syscall (netbsd-arm64), const EAGAIN = 35
pkg syscall (netbsd-arm64), const EALREADY = 37
pkg syscall (netbsd-arm64), const EAUTH = 80
pkg syscall (netbsd-arm64), const EAUTH Errno
pkg syscall (netbsd-arm64), const EBADF = 9
pkg syscall (netbsd-arm64), const EBADMSG = 88
pkg syscall (netbsd-arm64), const EBADMSG Errno
pkg syscall (netbsd-arm64), const EBADRPC = 72
pkg syscall (netbsd-arm64), const EBADRPC Errno
pkg syscall (netbsd-arm64), const EBUSY = 16
pkg syscall (netbsd-arm64), const ECANCELED = 87
pkg syscall (netbsd-arm64), const ECHILD = 10
pkg syscall (netbsd-arm64), const ECHO = 8
pkg syscall (netbsd-arm64), const ECHOCTL = 64
pkg syscall (netbsd-arm64), const ECHOCTL ideal-int
pkg syscall (netbsd-arm64), const ECHOE = 2
pkg syscall (netbsd-arm64), const ECHOE ideal-int
pkg syscall (netbsd-arm64), const ECHO ideal-int
pkg syscall (netbsd-arm64), const ECHOK = 4
pkg syscall (netbsd-arm64), const ECHOKE = 1
pkg syscall (netbsd-arm64), const ECHOKE ideal-int
pkg syscall (netbsd-arm64), const ECHOK ideal-int
pkg syscall (netbsd-arm64), const ECHONL = 16
pkg syscall (netbsd-arm64), const ECHONL ideal-int
pkg syscall (netbsd-arm64), const ECHOPRT = 32
pkg syscall (netbsd-arm64), const ECHOPRT ideal-int
pkg syscall (netbsd-arm64), const ECONNABORTED = 53
pkg syscall (netbsd-arm64), const ECONNREFUSED = 61
pkg syscall (netbsd-arm64), const ECONNRESET = 54
pkg syscall (netbsd-arm64), const EDEADLK = 11
pkg syscall (netbsd-arm64), const EDESTADDRREQ = 39
pkg syscall (netbsd-arm64), const EDOM = 33
pkg syscall (netbsd-arm64), const EDQUOT = 69
pkg syscall (netbsd-arm64), const EEXIST = 17
pkg syscall (netbsd-arm64), const EFAULT = 14
pkg syscall (netbsd-arm64), const EFBIG = 27
pkg syscall (netbsd-arm64), const EFTYPE = 79
pkg syscall (netbsd-arm64), const EFTYPE Errno
pkg syscall (netbsd-arm64), const EHOSTDOWN = 64
pkg syscall (netbsd-arm64), const EHOSTUNREACH = 65
pkg syscall (netbsd-arm64), const EIDRM = 82
pkg syscall (netbsd-arm64), const EILSEQ = 85
pkg syscall (netbsd-arm64), const EINPROGRESS = 36
pkg syscall (netbsd-arm64), const EINTR = 4
pkg syscall (netbsd-arm64), const EINVAL = 22
pkg syscall (netbsd-arm64), const EIO = 5
pkg syscall (netbsd-arm64), const EISCONN = 56
pkg syscall (netbsd-arm64), const EISDIR = 21
pkg syscall (netbsd-arm64), const ELAST = 96
pkg syscall (netbsd-arm64), const ELAST Errno
pkg syscall (netbsd-arm64), const ELOOP = 62
pkg syscall (netbsd-arm64), const EMFILE = 24
pkg syscall (netbsd-arm64), const EMLINK = 31
pkg syscall (netbsd-arm64), const EMSGSIZE = 40
pkg syscall (netbsd-arm64), const EMUL_LINUX = 1
pkg syscall (netbsd-arm64), const EMUL_LINUX32 = 5
pkg syscall (netbsd-arm64), const EMUL_LINUX32 ideal-int
pkg syscall (netbsd-arm64), const EMUL_LINUX ideal-int
pkg syscall (netbsd-arm64), const EMUL_MAXID = 6
pkg syscall (netbsd-arm64), const EMUL_MAXID ideal-int
pkg syscall (netbsd-arm64), const EMULTIHOP = 94
pkg syscall (netbsd-arm64), const EMULTIHOP Errno
pkg syscall (netbsd-arm64), const ENAMETOOLONG = 63
pkg syscall (netbsd-arm64), const ENEEDAUTH = 81
pkg syscall (netbsd-arm64), const ENEEDAUTH Errno
pkg syscall (netbsd-arm64), const ENETDOWN = 50
pkg syscall (netbsd-arm64), const ENETRESET = 52
pkg syscall (netbsd-arm64), const ENETUNREACH = 51
pkg syscall (netbsd-arm64), const ENFILE = 23
pkg syscall (netbsd-arm64), const ENOATTR = 93
pkg syscall (netbsd-arm64), const ENOATTR Errno
pkg syscall (netbsd-arm64), const ENOBUFS = 55
pkg syscall (netbsd-arm64), const ENODATA = 89
pkg syscall (netbsd-arm64), const ENODATA Errno
pkg syscall (netbsd-arm64), const ENODEV = 19
pkg syscall (netbsd-arm64), const ENOEXEC = 8
pkg syscall (netbsd-arm64), const ENOLCK = 77
pkg syscall (netbsd-arm64), const ENOLINK = 95
pkg syscall (netbsd-arm64), const ENOLINK Errno
pkg syscall (netbsd-arm64), const ENOMEM = 12
pkg syscall (netbsd-arm64), const ENOMSG = 83
pkg syscall (netbsd-arm64), const ENOPROTOOPT = 42
pkg syscall (netbsd-arm64), const ENOSPC = 28
pkg syscall (netbsd-arm64), const ENOSR = 90
pkg syscall (netbsd-arm64), const ENOSR Errno
pkg syscall (netbsd-arm64), const ENOSTR = 91
pkg syscall (netbsd-arm64), const ENOSTR Errno
pkg syscall (netbsd-arm64), const ENOSYS = 78
pkg syscall (netbsd-arm64), const ENOTBLK = 15
pkg syscall (netbsd-arm64), const ENOTCONN = 57
pkg syscall (netbsd-arm64), const ENOTDIR = 20
pkg syscall (netbsd-arm64), const ENOTEMPTY = 66
pkg syscall (netbsd-arm64), const ENOTSOCK = 38
pkg syscall (netbsd-arm64), const ENOTSUP = 86
pkg syscall (netbsd-arm64), const ENOTTY = 25
pkg syscall (netbsd-arm64), const ENXIO = 6
pkg syscall (netbsd-arm64), const EOPNOTSUPP = 45
pkg syscall (netbsd-arm64), const EOVERFLOW = 84
pkg syscall (netbsd-arm64), const EPERM = 1
pkg syscall (netbsd-arm64), const EPFNOSUPPORT = 46
pkg syscall (netbsd-arm64), const EPIPE = 32
pkg syscall (netbsd-arm64), const EPROCLIM = 67
pkg syscall (netbsd-arm64), const EPROCLIM Errno
pkg syscall (netbsd-arm64), const EPROCUNAVAIL = 76
pkg syscall (netbsd-arm64), const EPROCUNAVAIL Errno
pkg syscall (netbsd-arm64), const EPROGMISMATCH = 75
pkg syscall (netbsd-arm64), const EPROGMISMATCH Errno
pkg syscall (netbsd-arm64), const EPROGUNAVAIL = 74
pkg syscall (netbsd-arm64), const EPROGUNAVAIL Errno
pkg syscall (netbsd-arm64), const EPROTO = 96
pkg syscall (netbsd-arm64), const EPROTO Errno
pkg syscall (netbsd-arm64), const EPROTONOSUPPORT = 43
pkg syscall (netbsd-arm64), const EPROTOTYPE = 41
pkg syscall (netbsd-arm64), const ERANGE = 34
pkg syscall (netbsd-arm64), const EREMOTE = 71
pkg syscall (netbsd-arm64), const EROFS = 30
pkg syscall (netbsd-arm64), const ERPCMISMATCH = 73
pkg syscall (netbsd-arm64), const ERPCMISMATCH Errno
pkg syscall (netbsd-arm64), const ESHUTDOWN = 58
pkg syscall (netbsd-arm64), const ESOCKTNOSUPPORT = 44
pkg syscall (netbsd-arm64), const ESPIPE = 29
pkg syscall (netbsd-arm64), const ESRCH = 3
pkg syscall (netbsd-arm64), const ESTALE = 70
pkg syscall (netbsd-arm64), const ETHER_ADDR_LEN = 6
pkg syscall (netbsd-arm64), const ETHER_ADDR_LEN ideal-int
pkg syscall (netbsd-arm64), const ETHERCAP_JUMBO_MTU = 4
pkg syscall (netbsd-arm64), const ETHERCAP_JUMBO_MTU ideal-int
pkg syscall (netbsd-arm64), const ETHERCAP_VLAN_HWTAGGING = 2
pkg syscall (netbsd-arm64), const ETHERCAP_VLAN_HWTAGGING ideal-int
pkg syscall (netbsd-arm64), const ETHERCAP_VLAN_MTU = 1
pkg syscall (netbsd-arm64), const ETHERCAP_VLAN_MTU ideal-int
pkg syscall (netbsd-arm64), const ETHER_CRC_LEN = 4
pkg syscall (netbsd-arm64), const ETHER_CRC_LEN ideal-int
pkg syscall (netbsd-arm64), const ETHER_CRC_POLY_BE = 79764918
pkg syscall (netbsd-arm64), const ETHER_CRC_POLY_BE ideal-int
pkg syscall (netbsd-arm64), const ETHER_CRC_POLY_LE = 3988292384
pkg syscall (netbsd-arm64), const ETHER_CRC_POLY_LE ideal-int
pkg syscall (netbsd-arm64), const ETHER_HDR_LEN = 14
pkg syscall (netbsd-arm64), const ETHER_HDR_LEN ideal-int
pkg syscall (netbsd-arm64), const ETHER_MAX_LEN = 1518
pkg syscall (netbsd-arm64), const ETHER_MAX_LEN ideal-int
pkg syscall (netbsd-arm64), const ETHER_MAX_LEN_JUMBO = 9018
pkg syscall (netbsd-arm64), const ETHER_MAX_LEN_JUMBO ideal-int
pkg syscall (netbsd-arm64), const ETHERMIN = 46
pkg syscall (netbsd-arm64), const ETHERMIN ideal-int
pkg syscall (netbsd-arm64), const ETHER_MIN_LEN = 64
pkg syscall (netbsd-arm64), const ETHER_MIN_LEN ideal-int
pkg syscall (netbsd-arm64), const ETHERMTU = 1500
pkg syscall (netbsd-arm64), const ETHERMTU ideal-int
pkg syscall (netbsd-arm64), const ETHERMTU_JUMBO = 9000
pkg syscall (netbsd-arm64), const ETHERMTU_JUMBO ideal-int
pkg syscall (netbsd-arm64), const ETHER_PPPOE_ENCAP_LEN = 8
pkg syscall (netbsd-arm64), const ETHER_PPPOE_ENCAP_LEN ideal-int
pkg syscall (netbsd-arm64), const ETHERTYPE_8023 = 4
pkg syscall (netbsd-arm64), const ETHERTYPE_8023 ideal-int
pkg syscall (netbsd-arm64), const ETHERTYPE_AARP = 33011
pkg syscall (netbsd-arm64), const ETHERTYPE_AARP ideal-int
pkg syscall (netbsd-arm64), const ETHERTYPE_ACCTON = 33680
pkg syscall (netbsd-arm64), const ETHERTYPE_ACCTON ideal-int
pkg syscall (netbsd-arm64), const ETHERTYPE_AEONIC = 32822
pkg syscall (netbsd-arm64), const ETHERTYPE_AEONIC ideal-int
pkg syscall (netbsd-arm64), const ETHERTYPE_ALPHA = 33098
pkg syscall (netbsd-arm64), const ETHERTYPE_ALPHA ideal-int
pkg syscall (netbsd-arm64), const ETHERTYPE_AMBER = 24584
pkg syscall (netbsd-arm64), const ETHERTYPE_AMBER ideal-int
pkg syscall (netbsd-arm64), const ETHERTYPE_AMOEBA = 33093
pkg syscall (netbsd-arm64), const ETHERTYPE_AMOEBA ideal-int
pkg syscall (netbsd-arm64), const ETHERTYPE_APOLLO = 33015
pkg syscall (netbsd-arm64), const ETHERTYPE_APOLLODOMAIN = 32793
pkg syscall (netbsd-arm64), const ETHERTYPE_APOLLODOMAIN ideal-int
pkg syscall (netbsd-arm64), const ETHERTYPE_APOLLO ideal-int
pkg syscall (netbsd-arm64), const ETHERTYPE_APPLETALK = 32923
pkg syscall (netbsd-arm64), const ETHERTYPE_APPLETALK ideal-int
pkg syscall (netbsd-arm64), const ETHERTYPE_APPLITEK = 32967
pkg syscall (netbsd-arm64), const ETHERTYPE_APPLITEK ideal-int
pkg syscall (netbsd-arm64), const ETHERTYPE_ARGONAUT = 32826
pkg syscall (netbsd-arm64), const ETHERTYPE_ARGONAUT ideal-int
pkg syscall (netbsd-arm64), const ETHERTYPE_ARP = 2054
pkg syscall (netbsd-arm64), const ETHERTYPE_ARP ideal-int
pkg syscall (netbsd-arm64), const ETHERTYPE_AT = 32923
pkg syscall (netbsd-arm64), const ETHERTYPE_ATALK = 32923
pkg syscall (netbsd-arm64), const ETHERTYPE_ATALK ideal-int
pkg syscall (netbsd-arm64), const ETHERTYPE_AT ideal-int
pkg syscall (netbsd-arm64), const ETHERTYPE_ATOMIC = 34527
pkg syscall (netbsd-arm64), const ETHERTYPE_ATOMIC ideal-int
pkg syscall (netbsd-arm64), const ETHERTYPE_ATT = 32873
pkg syscall (netbsd-arm64), const ETHERTYPE_ATT ideal-int
pkg syscall (netbsd-arm64), const ETHERTYPE_ATTSTANFORD = 32776
pkg syscall (netbsd-arm64), const ETHERTYPE_ATTSTANFORD ideal-int
pkg syscall (netbsd-arm64), const ETHERTYPE_AUTOPHON = 32874
pkg syscall (netbsd-arm64), const ETHERTYPE_AUTOPHON ideal-int
pkg syscall (netbsd-arm64), const ETHERTYPE_AXIS = 34902
pkg syscall (netbsd-arm64), const ETHERTYPE_AXIS ideal-int
pkg syscall (netbsd-arm64), const ETHERTYPE_BCLOOP = 36867
pkg syscall (netbsd-arm64), const ETHERTYPE_BCLOOP ideal-int
pkg syscall (netbsd-arm64), const ETHERTYPE_BOFL = 33026
pkg syscall (netbsd-arm64), const ETHERTYPE_BOFL ideal-int
pkg syscall (netbsd-arm64), const ETHERTYPE_CABLETRON = 28724
pkg syscall (netbsd-arm64), const ETHERTYPE_CABLETRON ideal-int
pkg syscall (netbsd-arm64), const ETHERTYPE_CHAOS = 2052
pkg syscall (netbsd-arm64), const ETHERTYPE_CHAOS ideal-int
pkg syscall (netbsd-arm64), const ETHERTYPE_COMDESIGN = 32876
pkg syscall (netbsd-arm64), const ETHERTYPE_COMDESIGN ideal-int
pkg syscall (netbsd-arm64), const ETHERTYPE_COMPUGRAPHIC = 32877
pkg syscall (netbsd-arm64), const ETHERTYPE_COMPUGRAPHIC ideal-int
pkg syscall (netbsd-arm64), const ETHERTYPE_COUNTERPOINT = 32866
pkg syscall (netbsd-arm64), const ETHERTYPE_COUNTERPOINT ideal-int
pkg syscall (netbsd-arm64), const ETHERTYPE_CRONUS = 32772
pkg syscall (netbsd-arm64), const ETHERTYPE_CRONUS ideal-int
pkg syscall (netbsd-arm64), const ETHERTYPE_CRONUSVLN = 32771
pkg syscall (netbsd-arm64), const ETHERTYPE_CRONUSVLN ideal-int
pkg syscall (netbsd-arm64), const ETHERTYPE_DCA = 4660
pkg syscall (netbsd-arm64), const ETHERTYPE_DCA ideal-int
pkg syscall (netbsd-arm64), const ETHERTYPE_DDE = 32891
pkg syscall (netbsd-arm64), const ETHERTYPE_DDE ideal-int
pkg syscall (netbsd-arm64), const ETHERTYPE_DEBNI = 43690
pkg syscall (netbsd-arm64), const ETHERTYPE_DEBNI ideal-int
pkg syscall (netbsd-arm64), const ETHERTYPE_DECAM = 32840
pkg syscall (netbsd-arm64), const ETHERTYPE_DECAM ideal-int
pkg syscall (netbsd-arm64), const ETHERTYPE_DECCUST = 24582
pkg syscall (netbsd-arm64), const ETHERTYPE_DECCUST ideal-int
pkg syscall (netbsd-arm64), const ETHERTYPE_DECDIAG = 24581
pkg syscall (netbsd-arm64), const ETHERTYPE_DECDIAG ideal-int
pkg syscall (netbsd-arm64), const ETHERTYPE_DECDNS = 32828
pkg syscall (netbsd-arm64), const ETHERTYPE_DECDNS ideal-int
pkg syscall (netbsd-arm64), const ETHERTYPE_DECDTS = 32830
pkg syscall (netbsd-arm64), const ETHERTYPE_DECDTS ideal-int
pkg syscall (netbsd-arm64), const ETHERTYPE_DECEXPER = 24576
pkg syscall (netbsd-arm64), const ETHERTYPE_DECEXPER ideal-int
pkg syscall (netbsd-arm64), const ETHERTYPE_DECLAST = 32833
pkg syscall (netbsd-arm64), const ETHERTYPE_DECLAST ideal-int
pkg syscall (netbsd-arm64), const ETHERTYPE_DECLTM = 32831
pkg syscall (netbsd-arm64), const ETHERTYPE_DECLTM ideal-int
pkg syscall (netbsd-arm64), const ETHERTYPE_DECMUMPS = 24585
pkg syscall (netbsd-arm64), const ETHERTYPE_DECMUMPS ideal-int
pkg syscall (netbsd-arm64), const ETHERTYPE_DECNETBIOS = 32832
pkg syscall (netbsd-arm64), const ETHERTYPE_DECNETBIOS ideal-int
pkg syscall (netbsd-arm64), const ETHERTYPE_DELTACON = 34526
pkg syscall (netbsd-arm64), const ETHERTYPE_DELTACON ideal-int
pkg syscall (netbsd-arm64), const ETHERTYPE_DIDDLE = 17185
pkg syscall (netbsd-arm64), const ETHERTYPE_DIDDLE ideal-int
pkg syscall (netbsd-arm64), const ETHERTYPE_DLOG1 = 1632
pkg syscall (netbsd-arm64), const ETHERTYPE_DLOG1 ideal-int
pkg syscall (netbsd-arm64), const ETHERTYPE_DLOG2 = 1633
pkg syscall (netbsd-arm64), const ETHERTYPE_DLOG2 ideal-int
pkg syscall (netbsd-arm64), const ETHERTYPE_DN = 24579
pkg syscall (netbsd-arm64), const ETHERTYPE_DN ideal-int
pkg syscall (netbsd-arm64), const ETHERTYPE_DOGFIGHT = 6537
pkg syscall (netbsd-arm64), const ETHERTYPE_DOGFIGHT ideal-int
pkg syscall (netbsd-arm64), const ETHERTYPE_DSMD = 32825
pkg syscall (netbsd-arm64), const ETHERTYPE_DSMD ideal-int
pkg syscall (netbsd-arm64), const ETHERTYPE_ECMA = 2051
pkg syscall (netbsd-arm64), const ETHERTYPE_ECMA ideal-int
pkg syscall (netbsd-arm64), const ETHERTYPE_ENCRYPT = 32829
pkg syscall (netbsd-arm64), const ETHERTYPE_ENCRYPT ideal-int
pkg syscall (netbsd-arm64), const ETHERTYPE_ES = 32861
pkg syscall (netbsd-arm64), const ETHERTYPE_ES ideal-int
pkg syscall (netbsd-arm64), const ETHERTYPE_EXCELAN = 32784
pkg syscall (netbsd-arm64), const ETHERTYPE_EXCELAN ideal-int
pkg syscall (netbsd-arm64), const ETHERTYPE_EXPERDATA = 32841
pkg syscall (netbsd-arm64), const ETHERTYPE_EXPERDATA ideal-int
pkg syscall (netbsd-arm64), const ETHERTYPE_FLIP = 33094
pkg syscall (netbsd-arm64), const ETHERTYPE_FLIP ideal-int
pkg syscall (netbsd-arm64), const ETHERTYPE_FLOWCONTROL = 34824
pkg syscall (netbsd-arm64), const ETHERTYPE_FLOWCONTROL ideal-int
pkg syscall (netbsd-arm64), const ETHERTYPE_FRARP = 2056
pkg syscall (netbsd-arm64), const ETHERTYPE_FRARP ideal-int
pkg syscall (netbsd-arm64), const ETHERTYPE_GENDYN = 32872
pkg syscall (netbsd-arm64), const ETHERTYPE_GENDYN ideal-int
pkg syscall (netbsd-arm64), const ETHERTYPE_HAYES = 33072
pkg syscall (netbsd-arm64), const ETHERTYPE_HAYES ideal-int
pkg syscall (netbsd-arm64), const ETHERTYPE_HIPPI_FP = 33152
pkg syscall (netbsd-arm64), const ETHERTYPE_HIPPI_FP ideal-int
pkg syscall (netbsd-arm64), const ETHERTYPE_HITACHI = 34848
pkg syscall (netbsd-arm64), const ETHERTYPE_HITACHI ideal-int
pkg syscall (netbsd-arm64), const ETHERTYPE_HP = 32773
pkg syscall (netbsd-arm64), const ETHERTYPE_HP ideal-int
pkg syscall (netbsd-arm64), const ETHERTYPE_IEEEPUP = 2560
pkg syscall (netbsd-arm64), const ETHERTYPE_IEEEPUPAT = 2561
pkg syscall (netbsd-arm64), const ETHERTYPE_IEEEPUPAT ideal-int
pkg syscall (netbsd-arm64), const ETHERTYPE_IEEEPUP ideal-int
pkg syscall (netbsd-arm64), const ETHERTYPE_IMLBL = 19522
pkg syscall (netbsd-arm64), const ETHERTYPE_IMLBLDIAG = 16972
pkg syscall (netbsd-arm64), const ETHERTYPE_IMLBLDIAG ideal-int
pkg syscall (netbsd-arm64), const ETHERTYPE_IMLBL ideal-int
pkg syscall (netbsd-arm64), const ETHERTYPE_IP = 2048
pkg syscall (netbsd-arm64), const ETHERTYPE_IPAS = 34668
pkg syscall (netbsd-arm64), const ETHERTYPE_IPAS ideal-int
pkg syscall (netbsd-arm64), const ETHERTYPE_IP ideal-int
pkg syscall (netbsd-arm64), const ETHERTYPE_IPV6 = 34525
pkg syscall (netbsd-arm64), const ETHERTYPE_IPV6 ideal-int
pkg syscall (netbsd-arm64), const ETHERTYPE_IPX = 33079
pkg syscall (netbsd-arm64), const ETHERTYPE_IPX ideal-int
pkg syscall (netbsd-arm64), const ETHERTYPE_IPXNEW = 32823
pkg syscall (netbsd-arm64), const ETHERTYPE_IPXNEW ideal-int
pkg syscall (netbsd-arm64), const ETHERTYPE_KALPANA = 34178
pkg syscall (netbsd-arm64), const ETHERTYPE_KALPANA ideal-int
pkg syscall (netbsd-arm64), const ETHERTYPE_LANBRIDGE = 32824
pkg syscall (netbsd-arm64), const ETHERTYPE_LANBRIDGE ideal-int
pkg syscall (netbsd-arm64), const ETHERTYPE_LANPROBE = 34952
pkg syscall (netbsd-arm64), const ETHERTYPE_LANPROBE ideal-int
pkg syscall (netbsd-arm64), const ETHERTYPE_LAT = 24580
pkg syscall (netbsd-arm64), const ETHERTYPE_LAT ideal-int
pkg syscall (netbsd-arm64), const ETHERTYPE_LBACK = 36864
pkg syscall (netbsd-arm64), const ETHERTYPE_LBACK ideal-int
pkg syscall (netbsd-arm64), const ETHER_TYPE_LEN = 2
pkg syscall (netbsd-arm64), const ETHER_TYPE_LEN ideal-int
pkg syscall (netbsd-arm64), const ETHERTYPE_LITTLE = 32864
pkg syscall (netbsd-arm64), const ETHERTYPE_LITTLE ideal-int
pkg syscall (netbsd-arm64), const ETHERTYPE_LOGICRAFT = 33096
pkg syscall (netbsd-arm64), const ETHERTYPE_LOGICRAFT ideal-int
pkg syscall (netbsd-arm64), const ETHERTYPE_LOOPBACK = 36864
pkg syscall (netbsd-arm64), const ETHERTYPE_LOOPBACK ideal-int
pkg syscall (netbsd-arm64), const ETHERTYPE_MATRA = 32890
pkg syscall (netbsd-arm64), const ETHERTYPE_MATRA ideal-int
pkg syscall (netbsd-arm64), const ETHERTYPE_MAX = 65535
pkg syscall (netbsd-arm64), const ETHERTYPE_MAX ideal-int
pkg syscall (netbsd-arm64), const ETHERTYPE_MERIT = 32892
pkg syscall (netbsd-arm64), const ETHERTYPE_MERIT ideal-int
pkg syscall (netbsd-arm64), const ETHERTYPE_MICP = 34618
pkg syscall (netbsd-arm64), const ETHERTYPE_MICP ideal-int
pkg syscall (netbsd-arm64), const ETHERTYPE_MOPDL = 24577
pkg syscall (netbsd-arm64), const ETHERTYPE_MOPDL ideal-int
pkg syscall (netbsd-arm64), const ETHERTYPE_MOPRC = 24578
pkg syscall (netbsd-arm64), const ETHERTYPE_MOPRC ideal-int
pkg syscall (netbsd-arm64), const ETHERTYPE_MOTOROLA = 33165
pkg syscall (netbsd-arm64), const ETHERTYPE_MOTOROLA ideal-int
pkg syscall (netbsd-arm64), const ETHERTYPE_MPLS = 34887
pkg syscall (netbsd-arm64), const ETHERTYPE_MPLS ideal-int
pkg syscall (netbsd-arm64), const ETHERTYPE_MPLS_MCAST = 34888
pkg syscall (netbsd-arm64), const ETHERTYPE_MPLS_MCAST ideal-int
pkg syscall (netbsd-arm64), const ETHERTYPE_MUMPS = 33087
pkg syscall (netbsd-arm64), const ETHERTYPE_MUMPS ideal-int
pkg syscall (netbsd-arm64), const ETHERTYPE_NBPCC = 15364
pkg syscall (netbsd-arm64), const ETHERTYPE_NBPCC ideal-int
pkg syscall (netbsd-arm64), const ETHERTYPE_NBPCLAIM = 15369
pkg syscall (netbsd-arm64), const ETHERTYPE_NBPCLAIM ideal-int
pkg syscall (netbsd-arm64), const ETHERTYPE_NBPCLREQ = 15365
pkg syscall (netbsd-arm64), const ETHERTYPE_NBPCLREQ ideal-int
pkg syscall (netbsd-arm64), const ETHERTYPE_NBPCLRSP = 15366
pkg syscall (netbsd-arm64), const ETHERTYPE_NBPCLRSP ideal-int
pkg syscall (netbsd-arm64), const ETHERTYPE_NBPCREQ = 15362
pkg syscall (netbsd-arm64), const ETHERTYPE_NBPCREQ ideal-int
pkg syscall (netbsd-arm64), const ETHERTYPE_NBPCRSP = 15363
pkg syscall (netbsd-arm64), const ETHERTYPE_NBPCRSP ideal-int
pkg syscall (netbsd-arm64), const ETHERTYPE_NBPDG = 15367
pkg syscall (netbsd-arm64), const ETHERTYPE_NBPDGB = 15368
pkg syscall (netbsd-arm64), const ETHERTYPE_NBPDGB ideal-int
pkg syscall (netbsd-arm64), const ETHERTYPE_NBPDG ideal-int
pkg syscall (netbsd-arm64), const ETHERTYPE_NBPDLTE = 15370
pkg syscall (netbsd-arm64), const ETHERTYPE_NBPDLTE ideal-int
pkg syscall (netbsd-arm64), const ETHERTYPE_NBPRAR = 15372
pkg syscall (netbsd-arm64), const ETHERTYPE_NBPRAR ideal-int
pkg syscall (netbsd-arm64), const ETHERTYPE_NBPRAS = 15371
pkg syscall (netbsd-arm64), const ETHERTYPE_NBPRAS ideal-int
pkg syscall (netbsd-arm64), const ETHERTYPE_NBPRST = 15373
pkg syscall (netbsd-arm64), const ETHERTYPE_NBPRST ideal-int
pkg syscall (netbsd-arm64), const ETHERTYPE_NBPSCD = 15361
pkg syscall (netbsd-arm64), const ETHERTYPE_NBPSCD ideal-int
pkg syscall (netbsd-arm64), const ETHERTYPE_NBPVCD = 15360
pkg syscall (netbsd-arm64), const ETHERTYPE_NBPVCD ideal-int
pkg syscall (netbsd-arm64), const ETHERTYPE_NBS = 2050
pkg syscall (netbsd-arm64), const ETHERTYPE_NBS ideal-int
pkg syscall (netbsd-arm64), const ETHERTYPE_NCD = 33097
pkg syscall (netbsd-arm64), const ETHERTYPE_NCD ideal-int
pkg syscall (netbsd-arm64), const ETHERTYPE_NESTAR = 32774
pkg syscall (netbsd-arm64), const ETHERTYPE_NESTAR ideal-int
pkg syscall (netbsd-arm64), const ETHERTYPE_NETBEUI = 33169
pkg syscall (netbsd-arm64), const ETHERTYPE_NETBEUI ideal-int
pkg syscall (netbsd-arm64), const ETHERTYPE_NOVELL = 33080
pkg syscall (netbsd-arm64), const ETHERTYPE_NOVELL ideal-int
pkg syscall (netbsd-arm64), const ETHERTYPE_NS = 1536
pkg syscall (netbsd-arm64), const ETHERTYPE_NSAT = 1537
pkg syscall (netbsd-arm64), const ETHERTYPE_NSAT ideal-int
pkg syscall (netbsd-arm64), const ETHERTYPE_NSCOMPAT = 2055
pkg syscall (netbsd-arm64), const ETHERTYPE_NSCOMPAT ideal-int
pkg syscall (netbsd-arm64), const ETHERTYPE_NS ideal-int
pkg syscall (netbsd-arm64), const ETHERTYPE_NTRAILER = 16
pkg syscall (netbsd-arm64), const ETHERTYPE_NTRAILER ideal-int
pkg syscall (netbsd-arm64), const ETHERTYPE_OS9 = 28679
pkg syscall (netbsd-arm64), const ETHERTYPE_OS9 ideal-int
pkg syscall (netbsd-arm64), const ETHERTYPE_OS9NET = 28681
pkg syscall (netbsd-arm64), const ETHERTYPE_OS9NET ideal-int
pkg syscall (netbsd-arm64), const ETHERTYPE_PACER = 32966
pkg syscall (netbsd-arm64), const ETHERTYPE_PACER ideal-int
pkg syscall (netbsd-arm64), const ETHERTYPE_PAE = 34958
pkg syscall (netbsd-arm64), const ETHERTYPE_PAE ideal-int
pkg syscall (netbsd-arm64), const ETHERTYPE_PCS = 16962
pkg syscall (netbsd-arm64), const ETHERTYPE_PCS ideal-int
pkg syscall (netbsd-arm64), const ETHERTYPE_PLANNING = 32836
pkg syscall (netbsd-arm64), const ETHERTYPE_PLANNING ideal-int
pkg syscall (netbsd-arm64), const ETHERTYPE_PPP = 34827
pkg syscall (netbsd-arm64), const ETHERTYPE_PPP ideal-int
pkg syscall (netbsd-arm64), const ETHERTYPE_PPPOE = 34916
pkg syscall (netbsd-arm64), const ETHERTYPE_PPPOEDISC = 34915
pkg syscall (netbsd-arm64), const ETHERTYPE_PPPOEDISC ideal-int
pkg syscall (netbsd-arm64), const ETHERTYPE_PPPOE ideal-int
pkg syscall (netbsd-arm64), const ETHERTYPE_PRIMENTS = 28721
pkg syscall (netbsd-arm64), const ETHERTYPE_PRIMENTS ideal-int
pkg syscall (netbsd-arm64), const ETHERTYPE_PUP = 512
pkg syscall (netbsd-arm64), const ETHERTYPE_PUPAT = 512
pkg syscall (netbsd-arm64), const ETHERTYPE_PUPAT ideal-int
pkg syscall (netbsd-arm64), const ETHERTYPE_PUP ideal-int
pkg syscall (netbsd-arm64), const ETHERTYPE_RACAL = 28720
pkg syscall (netbsd-arm64), const ETHERTYPE_RACAL ideal-int
pkg syscall (netbsd-arm64), const ETHERTYPE_RATIONAL = 33104
pkg syscall (netbsd-arm64), const ETHERTYPE_RATIONAL ideal-int
pkg syscall (netbsd-arm64), const ETHERTYPE_RAWFR = 25945
pkg syscall (netbsd-arm64), const ETHERTYPE_RAWFR ideal-int
pkg syscall (netbsd-arm64), const ETHERTYPE_RCL = 6549
pkg syscall (netbsd-arm64), const ETHERTYPE_RCL ideal-int
pkg syscall (netbsd-arm64), const ETHERTYPE_RDP = 34617
pkg syscall (netbsd-arm64), const ETHERTYPE_RDP ideal-int
pkg syscall (netbsd-arm64), const ETHERTYPE_RETIX = 33010
pkg syscall (netbsd-arm64), const ETHERTYPE_RETIX ideal-int
pkg syscall (netbsd-arm64), const ETHERTYPE_REVARP = 32821
pkg syscall (netbsd-arm64), const ETHERTYPE_REVARP ideal-int
pkg syscall (netbsd-arm64), const ETHERTYPE_SCA = 24583
pkg syscall (netbsd-arm64), const ETHERTYPE_SCA ideal-int
pkg syscall (netbsd-arm64), const ETHERTYPE_SECTRA = 34523
pkg syscall (netbsd-arm64), const ETHERTYPE_SECTRA ideal-int
pkg syscall (netbsd-arm64), const ETHERTYPE_SECUREDATA = 34669
pkg syscall (netbsd-arm64), const ETHERTYPE_SECUREDATA ideal-int
pkg syscall (netbsd-arm64), const ETHERTYPE_SG_BOUNCE = 32790
pkg syscall (netbsd-arm64), const ETHERTYPE_SG_BOUNCE ideal-int
pkg syscall (netbsd-arm64), const ETHERTYPE_SG_DIAG = 32787
pkg syscall (netbsd-arm64), const ETHERTYPE_SG_DIAG ideal-int
pkg syscall (netbsd-arm64), const ETHERTYPE_SGITW = 33150
pkg syscall (netbsd-arm64), const ETHERTYPE_SGITW ideal-int
pkg syscall (netbsd-arm64), const ETHERTYPE_SG_NETGAMES = 32788
pkg syscall (netbsd-arm64), const ETHERTYPE_SG_NETGAMES ideal-int
pkg syscall (netbsd-arm64), const ETHERTYPE_SG_RESV = 32789
pkg syscall (netbsd-arm64), const ETHERTYPE_SG_RESV ideal-int
pkg syscall (netbsd-arm64), const ETHERTYPE_SIMNET = 21000
pkg syscall (netbsd-arm64), const ETHERTYPE_SIMNET ideal-int
pkg syscall (netbsd-arm64), const ETHERTYPE_SLOWPROTOCOLS = 34825
pkg syscall (netbsd-arm64), const ETHERTYPE_SLOWPROTOCOLS ideal-int
pkg syscall (netbsd-arm64), const ETHERTYPE_SNA = 32981
pkg syscall (netbsd-arm64), const ETHERTYPE_SNA ideal-int
pkg syscall (netbsd-arm64), const ETHERTYPE_SNMP = 33100
pkg syscall (netbsd-arm64), const ETHERTYPE_SNMP ideal-int
pkg syscall (netbsd-arm64), const ETHERTYPE_SONIX = 64245
pkg syscall (netbsd-arm64), const ETHERTYPE_SONIX ideal-int
pkg syscall (netbsd-arm64), const ETHERTYPE_SPIDER = 32927
pkg syscall (netbsd-arm64), const ETHERTYPE_SPIDER ideal-int
pkg syscall (netbsd-arm64), const ETHERTYPE_SPRITE = 1280
pkg syscall (netbsd-arm64), const ETHERTYPE_SPRITE ideal-int
pkg syscall (netbsd-arm64), const ETHERTYPE_STP = 33153
pkg syscall (netbsd-arm64), const ETHERTYPE_STP ideal-int
pkg syscall (netbsd-arm64), const ETHERTYPE_TALARIS = 33067
pkg syscall (netbsd-arm64), const ETHERTYPE_TALARIS ideal-int
pkg syscall (netbsd-arm64), const ETHERTYPE_TALARISMC = 34091
pkg syscall (netbsd-arm64), const ETHERTYPE_TALARISMC ideal-int
pkg syscall (netbsd-arm64), const ETHERTYPE_TCPCOMP = 34667
pkg syscall (netbsd-arm64), const ETHERTYPE_TCPCOMP ideal-int
pkg syscall (netbsd-arm64), const ETHERTYPE_TCPSM = 36866
pkg syscall (netbsd-arm64), const ETHERTYPE_TCPSM ideal-int
pkg syscall (netbsd-arm64), const ETHERTYPE_TEC = 33103
pkg syscall (netbsd-arm64), const ETHERTYPE_TEC ideal-int
pkg syscall (netbsd-arm64), const ETHERTYPE_TIGAN = 32815
pkg syscall (netbsd-arm64), const ETHERTYPE_TIGAN ideal-int
pkg syscall (netbsd-arm64), const ETHERTYPE_TRAIL = 4096
pkg syscall (netbsd-arm64), const ETHERTYPE_TRAIL ideal-int
pkg syscall (netbsd-arm64), const ETHERTYPE_TRANSETHER = 25944
pkg syscall (netbsd-arm64), const ETHERTYPE_TRANSETHER ideal-int
pkg syscall (netbsd-arm64), const ETHERTYPE_TYMSHARE = 32814
pkg syscall (netbsd-arm64), const ETHERTYPE_TYMSHARE ideal-int
pkg syscall (netbsd-arm64), const ETHERTYPE_UBBST = 28677
pkg syscall (netbsd-arm64), const ETHERTYPE_UBBST ideal-int
pkg syscall (netbsd-arm64), const ETHERTYPE_UBDEBUG = 2304
pkg syscall (netbsd-arm64), const ETHERTYPE_UBDEBUG ideal-int
pkg syscall (netbsd-arm64), const ETHERTYPE_UBDIAGLOOP = 28674
pkg syscall (netbsd-arm64), const ETHERTYPE_UBDIAGLOOP ideal-int
pkg syscall (netbsd-arm64), const ETHERTYPE_UBDL = 28672
pkg syscall (netbsd-arm64), const ETHERTYPE_UBDL ideal-int
pkg syscall (netbsd-arm64), const ETHERTYPE_UBNIU = 28673
pkg syscall (netbsd-arm64), const ETHERTYPE_UBNIU ideal-int
pkg syscall (netbsd-arm64), const ETHERTYPE_UBNMC = 28675
pkg syscall (netbsd-arm64), const ETHERTYPE_UBNMC ideal-int
pkg syscall (netbsd-arm64), const ETHERTYPE_VALID = 5632
pkg syscall (netbsd-arm64), const ETHERTYPE_VALID ideal-int
pkg syscall (netbsd-arm64), const ETHERTYPE_VARIAN = 32989
pkg syscall (netbsd-arm64), const ETHERTYPE_VARIAN ideal-int
pkg syscall (netbsd-arm64), const ETHERTYPE_VAXELN = 32827
pkg syscall (netbsd-arm64), const ETHERTYPE_VAXELN ideal-int
pkg syscall (netbsd-arm64), const ETHERTYPE_VEECO = 32871
pkg syscall (netbsd-arm64), const ETHERTYPE_VEECO ideal-int
pkg syscall (netbsd-arm64), const ETHERTYPE_VEXP = 32859
pkg syscall (netbsd-arm64), const ETHERTYPE_VEXP ideal-int
pkg syscall (netbsd-arm64), const ETHERTYPE_VGLAB = 33073
pkg syscall (netbsd-arm64), const ETHERTYPE_VGLAB ideal-int
pkg syscall (netbsd-arm64), const ETHERTYPE_VINES = 2989
pkg syscall (netbsd-arm64), const ETHERTYPE_VINESECHO = 2991
pkg syscall (netbsd-arm64), const ETHERTYPE_VINESECHO ideal-int
pkg syscall (netbsd-arm64), const ETHERTYPE_VINES ideal-int
pkg syscall (netbsd-arm64), const ETHERTYPE_VINESLOOP = 2990
pkg syscall (netbsd-arm64), const ETHERTYPE_VINESLOOP ideal-int
pkg syscall (netbsd-arm64), const ETHERTYPE_VITAL = 65280
pkg syscall (netbsd-arm64), const ETHERTYPE_VITAL ideal-int
pkg syscall (netbsd-arm64), const ETHERTYPE_VLAN = 33024
pkg syscall (netbsd-arm64), const ETHERTYPE_VLAN ideal-int
pkg syscall (netbsd-arm64), const ETHERTYPE_VLTLMAN = 32896
pkg syscall (netbsd-arm64), const ETHERTYPE_VLTLMAN ideal-int
pkg syscall (netbsd-arm64), const ETHERTYPE_VPROD = 32860
pkg syscall (netbsd-arm64), const ETHERTYPE_VPROD ideal-int
pkg syscall (netbsd-arm64), const ETHERTYPE_VURESERVED = 33095
pkg syscall (netbsd-arm64), const ETHERTYPE_VURESERVED ideal-int
pkg syscall (netbsd-arm64), const ETHERTYPE_WATERLOO = 33072
pkg syscall (netbsd-arm64), const ETHERTYPE_WATERLOO ideal-int
pkg syscall (netbsd-arm64), const ETHERTYPE_WELLFLEET = 33027
pkg syscall (netbsd-arm64), const ETHERTYPE_WELLFLEET ideal-int
pkg syscall (netbsd-arm64), const ETHERTYPE_X25 = 2053
pkg syscall (netbsd-arm64), const ETHERTYPE_X25 ideal-int
pkg syscall (netbsd-arm64), const ETHERTYPE_X75 = 2049
pkg syscall (netbsd-arm64), const ETHERTYPE_X75 ideal-int
pkg syscall (netbsd-arm64), const ETHERTYPE_XNSSM = 36865
pkg syscall (netbsd-arm64), const ETHERTYPE_XNSSM ideal-int
pkg syscall (netbsd-arm64), const ETHERTYPE_XTP = 33149
pkg syscall (netbsd-arm64), const ETHERTYPE_XTP ideal-int
pkg syscall (netbsd-arm64), const ETHER_VLAN_ENCAP_LEN = 4
pkg syscall (netbsd-arm64), const ETHER_VLAN_ENCAP_LEN ideal-int
pkg syscall (netbsd-arm64), const ETIME = 92
pkg syscall (netbsd-arm64), const ETIMEDOUT = 60
pkg syscall (netbsd-arm64), const ETIME Errno
pkg syscall (netbsd-arm64), const ETOOMANYREFS = 59
pkg syscall (netbsd-arm64), const ETXTBSY = 26
pkg syscall (netbsd-arm64), const EUSERS = 68
pkg syscall (netbsd-arm64), const EV_ADD = 1
pkg syscall (netbsd-arm64), const EV_ADD ideal-int
pkg syscall (netbsd-arm64), const EV_CLEAR = 32
pkg syscall (netbsd-arm64), const EV_CLEAR ideal-int
pkg syscall (netbsd-arm64), const EV_DELETE = 2
pkg syscall (netbsd-arm64), const EV_DELETE ideal-int
pkg syscall (netbsd-arm64), const EV_DISABLE = 8
pkg syscall (netbsd-arm64), const EV_DISABLE ideal-int
pkg syscall (netbsd-arm64), const EV_ENABLE = 4
pkg syscall (netbsd-arm64), const EV_ENABLE ideal-int
pkg syscall (netbsd-arm64), const EV_EOF = 32768
pkg syscall (netbsd-arm64), const EV_EOF ideal-int
pkg syscall (netbsd-arm64), const EV_ERROR = 16384
pkg syscall (netbsd-arm64), const EV_ERROR ideal-int
pkg syscall (netbsd-arm64), const EVFILT_AIO = 2
pkg syscall (netbsd-arm64), const EVFILT_AIO ideal-int
pkg syscall (netbsd-arm64), const EVFILT_PROC = 4
pkg syscall (netbsd-arm64), const EVFILT_PROC ideal-int
pkg syscall (netbsd-arm64), const EVFILT_READ = 0
pkg syscall (netbsd-arm64), const EVFILT_READ ideal-int
pkg syscall (netbsd-arm64), const EVFILT_SIGNAL = 5
pkg syscall (netbsd-arm64), const EVFILT_SIGNAL ideal-int
pkg syscall (netbsd-arm64), const EVFILT_SYSCOUNT = 7
pkg syscall (netbsd-arm64), const EVFILT_SYSCOUNT ideal-int
pkg syscall (netbsd-arm64), const EVFILT_TIMER = 6
pkg syscall (netbsd-arm64), const EVFILT_TIMER ideal-int
pkg syscall (netbsd-arm64), const EVFILT_VNODE = 3
pkg syscall (netbsd-arm64), const EVFILT_VNODE ideal-int
pkg syscall (netbsd-arm64), const EVFILT_WRITE = 1
pkg syscall (netbsd-arm64), const EVFILT_WRITE ideal-int
pkg syscall (netbsd-arm64), const EV_FLAG1 = 8192
pkg syscall (netbsd-arm64), const EV_FLAG1 ideal-int
pkg syscall (netbsd-arm64), const EV_ONESHOT = 16
pkg syscall (netbsd-arm64), const EV_ONESHOT ideal-int
pkg syscall (netbsd-arm64), const EV_SYSFLAGS = 61440
pkg syscall (netbsd-arm64), const EV_SYSFLAGS ideal-int
pkg syscall (netbsd-arm64), const EWOULDBLOCK = 35
pkg syscall (netbsd-arm64), const EXDEV = 18
pkg syscall (netbsd-arm64), const EXTA = 19200
pkg syscall (netbsd-arm64), const EXTA ideal-int
pkg syscall (netbsd-arm64), const EXTB = 38400
pkg syscall (netbsd-arm64), const EXTB ideal-int
pkg syscall (netbsd-arm64), const EXTPROC = 2048
pkg syscall (netbsd-arm64), const EXTPROC ideal-int
pkg syscall (netbsd-arm64), const F_CLOSEM = 10
pkg syscall (netbsd-arm64), const F_CLOSEM ideal-int
pkg syscall (netbsd-arm64), const FD_CLOEXEC = 1
pkg syscall (netbsd-arm64), const FD_CLOEXEC ideal-int
pkg syscall (netbsd-arm64), const FD_SETSIZE = 256
pkg syscall (netbsd-arm64), const FD_SETSIZE ideal-int
pkg syscall (netbsd-arm64), const F_DUPFD = 0
pkg syscall (netbsd-arm64), const F_DUPFD_CLOEXEC = 12
pkg syscall (netbsd-arm64), const F_DUPFD_CLOEXEC ideal-int
pkg syscall (netbsd-arm64), const F_DUPFD ideal-int
pkg syscall (netbsd-arm64), const F_FSCTL = -2147483648
pkg syscall (netbsd-arm64), const F_FSCTL ideal-int
pkg syscall (netbsd-arm64), const F_FSDIRMASK = 1879048192
pkg syscall (netbsd-arm64), const F_FSDIRMASK ideal-int
pkg syscall (netbsd-arm64), const F_FSIN = 268435456
pkg syscall (netbsd-arm64), const F_FSIN ideal-int
pkg syscall (netbsd-arm64), const F_FSINOUT = 805306368
pkg syscall (netbsd-arm64), const F_FSINOUT ideal-int
pkg syscall (netbsd-arm64), const F_FSOUT = 536870912
pkg syscall (netbsd-arm64), const F_FSOUT ideal-int
pkg syscall (netbsd-arm64), const F_FSPRIV = 32768
pkg syscall (netbsd-arm64), const F_FSPRIV ideal-int
pkg syscall (netbsd-arm64), const F_FSVOID = 1073741824
pkg syscall (netbsd-arm64), const F_FSVOID ideal-int
pkg syscall (netbsd-arm64), const F_GETFD = 1
pkg syscall (netbsd-arm64), const F_GETFD ideal-int
pkg syscall (netbsd-arm64), const F_GETFL = 3
pkg syscall (netbsd-arm64), const F_GETFL ideal-int
pkg syscall (netbsd-arm64), const F_GETLK = 7
pkg syscall (netbsd-arm64), const F_GETLK ideal-int
pkg syscall (netbsd-arm64), const F_GETNOSIGPIPE = 13
pkg syscall (netbsd-arm64), const F_GETNOSIGPIPE ideal-int
pkg syscall (netbsd-arm64), const F_GETOWN = 5
pkg syscall (netbsd-arm64), const F_GETOWN ideal-int
pkg syscall (netbsd-arm64), const FLUSHO = 8388608
pkg syscall (netbsd-arm64), const FLUSHO ideal-int
pkg syscall (netbsd-arm64), const F_MAXFD = 11
pkg syscall (netbsd-arm64), const F_MAXFD ideal-int
pkg syscall (netbsd-arm64), const F_OK = 0
pkg syscall (netbsd-arm64), const F_OK ideal-int
pkg syscall (netbsd-arm64), const F_PARAM_MASK = 4095
pkg syscall (netbsd-arm64), const F_PARAM_MASK ideal-int
pkg syscall (netbsd-arm64), const F_PARAM_MAX = 4095
pkg syscall (netbsd-arm64), const F_PARAM_MAX ideal-int
pkg syscall (netbsd-arm64), const F_RDLCK = 1
pkg syscall (netbsd-arm64), const F_RDLCK ideal-int
pkg syscall (netbsd-arm64), const F_SETFD = 2
pkg syscall (netbsd-arm64), const F_SETFD ideal-int
pkg syscall (netbsd-arm64), const F_SETFL = 4
pkg syscall (netbsd-arm64), const F_SETFL ideal-int
pkg syscall (netbsd-arm64), const F_SETLK = 8
pkg syscall (netbsd-arm64), const F_SETLK ideal-int
pkg syscall (netbsd-arm64), const F_SETLKW = 9
pkg syscall (netbsd-arm64), const F_SETLKW ideal-int
pkg syscall (netbsd-arm64), const F_SETNOSIGPIPE = 14
pkg syscall (netbsd-arm64), const F_SETNOSIGPIPE ideal-int
pkg syscall (netbsd-arm64), const F_SETOWN = 6
pkg syscall (netbsd-arm64), const F_SETOWN ideal-int
pkg syscall (netbsd-arm64), const F_UNLCK = 2
pkg syscall (netbsd-arm64), const F_UNLCK ideal-int
pkg syscall (netbsd-arm64), const F_WRLCK = 3
pkg syscall (netbsd-arm64), const F_WRLCK ideal-int
pkg syscall (netbsd-arm64), const HUPCL = 16384
pkg syscall (netbsd-arm64), const HUPCL ideal-int
pkg syscall (netbsd-arm64), const ICANON = 256
pkg syscall (netbsd-arm64), const ICANON ideal-int
pkg syscall (netbsd-arm64), const ICMP6_FILTER = 18
pkg syscall (netbsd-arm64), const ICMP6_FILTER ideal-int
pkg syscall (netbsd-arm64), const ICRNL = 256
pkg syscall (netbsd-arm64), const ICRNL ideal-int
pkg syscall (netbsd-arm64), const IEXTEN = 1024
pkg syscall (netbsd-arm64), const IEXTEN ideal-int
pkg syscall (netbsd-arm64), const IFAN_ARRIVAL = 0
pkg syscall (netbsd-arm64), const IFAN_ARRIVAL ideal-int
pkg syscall (netbsd-arm64), const IFAN_DEPARTURE = 1
pkg syscall (netbsd-arm64), const IFAN_DEPARTURE ideal-int
pkg syscall (netbsd-arm64), const IFA_ROUTE = 1
pkg syscall (netbsd-arm64), const IFA_ROUTE ideal-int
pkg syscall (netbsd-arm64), const IFF_ALLMULTI = 512
pkg syscall (netbsd-arm64), const IFF_ALLMULTI ideal-int
pkg syscall (netbsd-arm64), const IFF_CANTCHANGE = 36690
pkg syscall (netbsd-arm64), const IFF_CANTCHANGE ideal-int
pkg syscall (netbsd-arm64), const IFF_DEBUG = 4
pkg syscall (netbsd-arm64), const IFF_DEBUG ideal-int
pkg syscall (netbsd-arm64), const IFF_LINK0 = 4096
pkg syscall (netbsd-arm64), const IFF_LINK0 ideal-int
pkg syscall (netbsd-arm64), const IFF_LINK1 = 8192
pkg syscall (netbsd-arm64), const IFF_LINK1 ideal-int
pkg syscall (netbsd-arm64), const IFF_LINK2 = 16384
pkg syscall (netbsd-arm64), const IFF_LINK2 ideal-int
pkg syscall (netbsd-arm64), const IFF_LOOPBACK = 8
pkg syscall (netbsd-arm64), const IFF_MULTICAST = 32768
pkg syscall (netbsd-arm64), const IFF_NOARP = 128
pkg syscall (netbsd-arm64), const IFF_NOARP ideal-int
pkg syscall (netbsd-arm64), const IFF_NOTRAILERS = 32
pkg syscall (netbsd-arm64), const IFF_NOTRAILERS ideal-int
pkg syscall (netbsd-arm64), const IFF_OACTIVE = 1024
pkg syscall (netbsd-arm64), const IFF_OACTIVE ideal-int
pkg syscall (netbsd-arm64), const IFF_POINTOPOINT = 16
pkg syscall (netbsd-arm64), const IFF_POINTOPOINT ideal-int
pkg syscall (netbsd-arm64), const IFF_PROMISC = 256
pkg syscall (netbsd-arm64), const IFF_PROMISC ideal-int
pkg syscall (netbsd-arm64), const IFF_RUNNING = 64
pkg syscall (netbsd-arm64), const IFF_RUNNING ideal-int
pkg syscall (netbsd-arm64), const IFF_SIMPLEX = 2048
pkg syscall (netbsd-arm64), const IFF_SIMPLEX ideal-int
pkg syscall (netbsd-arm64), const IFNAMSIZ = 16
pkg syscall (netbsd-arm64), const IFNAMSIZ ideal-int
pkg syscall (netbsd-arm64), const IFT_1822 = 2
pkg syscall (netbsd-arm64), const IFT_1822 ideal-int
pkg syscall (netbsd-arm64), const IFT_A12MPPSWITCH = 130
pkg syscall (netbsd-arm64), const IFT_A12MPPSWITCH ideal-int
pkg syscall (netbsd-arm64), const IFT_AAL2 = 187
pkg syscall (netbsd-arm64), const IFT_AAL2 ideal-int
pkg syscall (netbsd-arm64), const IFT_AAL5 = 49
pkg syscall (netbsd-arm64), const IFT_AAL5 ideal-int
pkg syscall (netbsd-arm64), const IFT_ADSL = 94
pkg syscall (netbsd-arm64), const IFT_ADSL ideal-int
pkg syscall (netbsd-arm64), const IFT_AFLANE8023 = 59
pkg syscall (netbsd-arm64), const IFT_AFLANE8023 ideal-int
pkg syscall (netbsd-arm64), const IFT_AFLANE8025 = 60
pkg syscall (netbsd-arm64), const IFT_AFLANE8025 ideal-int
pkg syscall (netbsd-arm64), const IFT_ARAP = 88
pkg syscall (netbsd-arm64), const IFT_ARAP ideal-int
pkg syscall (netbsd-arm64), const IFT_ARCNET = 35
pkg syscall (netbsd-arm64), const IFT_ARCNET ideal-int
pkg syscall (netbsd-arm64), const IFT_ARCNETPLUS = 36
pkg syscall (netbsd-arm64), const IFT_ARCNETPLUS ideal-int
pkg syscall (netbsd-arm64), const IFT_ASYNC = 84
pkg syscall (netbsd-arm64), const IFT_ASYNC ideal-int
pkg syscall (netbsd-arm64), const IFT_ATM = 37
pkg syscall (netbsd-arm64), const IFT_ATMDXI = 105
pkg syscall (netbsd-arm64), const IFT_ATMDXI ideal-int
pkg syscall (netbsd-arm64), const IFT_ATMFUNI = 106
pkg syscall (netbsd-arm64), const IFT_ATMFUNI ideal-int
pkg syscall (netbsd-arm64), const IFT_ATM ideal-int
pkg syscall (netbsd-arm64), const IFT_ATMIMA = 107
pkg syscall (netbsd-arm64), const IFT_ATMIMA ideal-int
pkg syscall (netbsd-arm64), const IFT_ATMLOGICAL = 80
pkg syscall (netbsd-arm64), const IFT_ATMLOGICAL ideal-int
pkg syscall (netbsd-arm64), const IFT_ATMRADIO = 189
pkg syscall (netbsd-arm64), const IFT_ATMRADIO ideal-int
pkg syscall (netbsd-arm64), const IFT_ATMSUBINTERFACE = 134
pkg syscall (netbsd-arm64), const IFT_ATMSUBINTERFACE ideal-int
pkg syscall (netbsd-arm64), const IFT_ATMVCIENDPT = 194
pkg syscall (netbsd-arm64), const IFT_ATMVCIENDPT ideal-int
pkg syscall (netbsd-arm64), const IFT_ATMVIRTUAL = 149
pkg syscall (netbsd-arm64), const IFT_ATMVIRTUAL ideal-int
pkg syscall (netbsd-arm64), const IFT_BGPPOLICYACCOUNTING = 162
pkg syscall (netbsd-arm64), const IFT_BGPPOLICYACCOUNTING ideal-int
pkg syscall (netbsd-arm64), const IFT_BRIDGE = 209
pkg syscall (netbsd-arm64), const IFT_BRIDGE ideal-int
pkg syscall (netbsd-arm64), const IFT_BSC = 83
pkg syscall (netbsd-arm64), const IFT_BSC ideal-int
pkg syscall (netbsd-arm64), const IFT_CARP = 248
pkg syscall (netbsd-arm64), const IFT_CARP ideal-int
pkg syscall (netbsd-arm64), const IFT_CCTEMUL = 61
pkg syscall (netbsd-arm64), const IFT_CCTEMUL ideal-int
pkg syscall (netbsd-arm64), const IFT_CEPT = 19
pkg syscall (netbsd-arm64), const IFT_CEPT ideal-int
pkg syscall (netbsd-arm64), const IFT_CES = 133
pkg syscall (netbsd-arm64), const IFT_CES ideal-int
pkg syscall (netbsd-arm64), const IFT_CHANNEL = 70
pkg syscall (netbsd-arm64), const IFT_CHANNEL ideal-int
pkg syscall (netbsd-arm64), const IFT_CNR = 85
pkg syscall (netbsd-arm64), const IFT_CNR ideal-int
pkg syscall (netbsd-arm64), const IFT_COFFEE = 132
pkg syscall (netbsd-arm64), const IFT_COFFEE ideal-int
pkg syscall (netbsd-arm64), const IFT_COMPOSITELINK = 155
pkg syscall (netbsd-arm64), const IFT_COMPOSITELINK ideal-int
pkg syscall (netbsd-arm64), const IFT_DCN = 141
pkg syscall (netbsd-arm64), const IFT_DCN ideal-int
pkg syscall (netbsd-arm64), const IFT_DIGITALPOWERLINE = 138
pkg syscall (netbsd-arm64), const IFT_DIGITALPOWERLINE ideal-int
pkg syscall (netbsd-arm64), const IFT_DIGITALWRAPPEROVERHEADCHANNEL = 186
pkg syscall (netbsd-arm64), const IFT_DIGITALWRAPPEROVERHEADCHANNEL ideal-int
pkg syscall (netbsd-arm64), const IFT_DLSW = 74
pkg syscall (netbsd-arm64), const IFT_DLSW ideal-int
pkg syscall (netbsd-arm64), const IFT_DOCSCABLEDOWNSTREAM = 128
pkg syscall (netbsd-arm64), const IFT_DOCSCABLEDOWNSTREAM ideal-int
pkg syscall (netbsd-arm64), const IFT_DOCSCABLEMACLAYER = 127
pkg syscall (netbsd-arm64), const IFT_DOCSCABLEMACLAYER ideal-int
pkg syscall (netbsd-arm64), const IFT_DOCSCABLEUPSTREAM = 129
pkg syscall (netbsd-arm64), const IFT_DOCSCABLEUPSTREAMCHANNEL = 205
pkg syscall (netbsd-arm64), const IFT_DOCSCABLEUPSTREAMCHANNEL ideal-int
pkg syscall (netbsd-arm64), const IFT_DOCSCABLEUPSTREAM ideal-int
pkg syscall (netbsd-arm64), const IFT_DS0 = 81
pkg syscall (netbsd-arm64), const IFT_DS0BUNDLE = 82
pkg syscall (netbsd-arm64), const IFT_DS0BUNDLE ideal-int
pkg syscall (netbsd-arm64), const IFT_DS0 ideal-int
pkg syscall (netbsd-arm64), const IFT_DS1FDL = 170
pkg syscall (netbsd-arm64), const IFT_DS1FDL ideal-int
pkg syscall (netbsd-arm64), const IFT_DS3 = 30
pkg syscall (netbsd-arm64), const IFT_DS3 ideal-int
pkg syscall (netbsd-arm64), const IFT_DTM = 140
pkg syscall (netbsd-arm64), const IFT_DTM ideal-int
pkg syscall (netbsd-arm64), const IFT_DVBASILN = 172
pkg syscall (netbsd-arm64), const IFT_DVBASILN ideal-int
pkg syscall (netbsd-arm64), const IFT_DVBASIOUT = 173
pkg syscall (netbsd-arm64), const IFT_DVBASIOUT ideal-int
pkg syscall (netbsd-arm64), const IFT_DVBRCCDOWNSTREAM = 147
pkg syscall (netbsd-arm64), const IFT_DVBRCCDOWNSTREAM ideal-int
pkg syscall (netbsd-arm64), const IFT_DVBRCCMACLAYER = 146
pkg syscall (netbsd-arm64), const IFT_DVBRCCMACLAYER ideal-int
pkg syscall (netbsd-arm64), const IFT_DVBRCCUPSTREAM = 148
pkg syscall (netbsd-arm64), const IFT_DVBRCCUPSTREAM ideal-int
pkg syscall (netbsd-arm64), const IFT_ECONET = 206
pkg syscall (netbsd-arm64), const IFT_ECONET ideal-int
pkg syscall (netbsd-arm64), const IFT_EON = 25
pkg syscall (netbsd-arm64), const IFT_EON ideal-int
pkg syscall (netbsd-arm64), const IFT_EPLRS = 87
pkg syscall (netbsd-arm64), const IFT_EPLRS ideal-int
pkg syscall (netbsd-arm64), const IFT_ESCON = 73
pkg syscall (netbsd-arm64), const IFT_ESCON ideal-int
pkg syscall (netbsd-arm64), const IFT_ETHER = 6
pkg syscall (netbsd-arm64), const IFT_ETHER ideal-int
pkg syscall (netbsd-arm64), const IFT_FAITH = 242
pkg syscall (netbsd-arm64), const IFT_FAITH ideal-int
pkg syscall (netbsd-arm64), const IFT_FAST = 125
pkg syscall (netbsd-arm64), const IFT_FASTETHER = 62
pkg syscall (netbsd-arm64), const IFT_FASTETHERFX = 69
pkg syscall (netbsd-arm64), const IFT_FASTETHERFX ideal-int
pkg syscall (netbsd-arm64), const IFT_FASTETHER ideal-int
pkg syscall (netbsd-arm64), const IFT_FAST ideal-int
pkg syscall (netbsd-arm64), const IFT_FDDI = 15
pkg syscall (netbsd-arm64), const IFT_FDDI ideal-int
pkg syscall (netbsd-arm64), const IFT_FIBRECHANNEL = 56
pkg syscall (netbsd-arm64), const IFT_FIBRECHANNEL ideal-int
pkg syscall (netbsd-arm64), const IFT_FRAMERELAYINTERCONNECT = 58
pkg syscall (netbsd-arm64), const IFT_FRAMERELAYINTERCONNECT ideal-int
pkg syscall (netbsd-arm64), const IFT_FRAMERELAYMPI = 92
pkg syscall (netbsd-arm64), const IFT_FRAMERELAYMPI ideal-int
pkg syscall (netbsd-arm64), const IFT_FRDLCIENDPT = 193
pkg syscall (netbsd-arm64), const IFT_FRDLCIENDPT ideal-int
pkg syscall (netbsd-arm64), const IFT_FRELAY = 32
pkg syscall (netbsd-arm64), const IFT_FRELAYDCE = 44
pkg syscall (netbsd-arm64), const IFT_FRELAYDCE ideal-int
pkg syscall (netbsd-arm64), const IFT_FRELAY ideal-int
pkg syscall (netbsd-arm64), const IFT_FRF16MFRBUNDLE = 163
pkg syscall (netbsd-arm64), const IFT_FRF16MFRBUNDLE ideal-int
pkg syscall (netbsd-arm64), const IFT_FRFORWARD = 158
pkg syscall (netbsd-arm64), const IFT_FRFORWARD ideal-int
pkg syscall (netbsd-arm64), const IFT_G703AT2MB = 67
pkg syscall (netbsd-arm64), const IFT_G703AT2MB ideal-int
pkg syscall (netbsd-arm64), const IFT_G703AT64K = 66
pkg syscall (netbsd-arm64), const IFT_G703AT64K ideal-int
pkg syscall (netbsd-arm64), const IFT_GIF = 240
pkg syscall (netbsd-arm64), const IFT_GIF ideal-int
pkg syscall (netbsd-arm64), const IFT_GIGABITETHERNET = 117
pkg syscall (netbsd-arm64), const IFT_GIGABITETHERNET ideal-int
pkg syscall (netbsd-arm64), const IFT_GR303IDT = 178
pkg syscall (netbsd-arm64), const IFT_GR303IDT ideal-int
pkg syscall (netbsd-arm64), const IFT_GR303RDT = 177
pkg syscall (netbsd-arm64), const IFT_GR303RDT ideal-int
pkg syscall (netbsd-arm64), const IFT_H323GATEKEEPER = 164
pkg syscall (netbsd-arm64), const IFT_H323GATEKEEPER ideal-int
pkg syscall (netbsd-arm64), const IFT_H323PROXY = 165
pkg syscall (netbsd-arm64), const IFT_H323PROXY ideal-int
pkg syscall (netbsd-arm64), const IFT_HDH1822 = 3
pkg syscall (netbsd-arm64), const IFT_HDH1822 ideal-int
pkg syscall (netbsd-arm64), const IFT_HDLC = 118
pkg syscall (netbsd-arm64), const IFT_HDLC ideal-int
pkg syscall (netbsd-arm64), const IFT_HDSL2 = 168
pkg syscall (netbsd-arm64), const IFT_HDSL2 ideal-int
pkg syscall (netbsd-arm64), const IFT_HIPERLAN2 = 183
pkg syscall (netbsd-arm64), const IFT_HIPERLAN2 ideal-int
pkg syscall (netbsd-arm64), const IFT_HIPPI = 47
pkg syscall (netbsd-arm64), const IFT_HIPPI ideal-int
pkg syscall (netbsd-arm64), const IFT_HIPPIINTERFACE = 57
pkg syscall (netbsd-arm64), const IFT_HIPPIINTERFACE ideal-int
pkg syscall (netbsd-arm64), const IFT_HOSTPAD = 90
pkg syscall (netbsd-arm64), const IFT_HOSTPAD ideal-int
pkg syscall (netbsd-arm64), const IFT_HSSI = 46
pkg syscall (netbsd-arm64), const IFT_HSSI ideal-int
pkg syscall (netbsd-arm64), const IFT_HY = 14
pkg syscall (netbsd-arm64), const IFT_HY ideal-int
pkg syscall (netbsd-arm64), const IFT_IBM370PARCHAN = 72
pkg syscall (netbsd-arm64), const IFT_IBM370PARCHAN ideal-int
pkg syscall (netbsd-arm64), const IFT_IDSL = 154
pkg syscall (netbsd-arm64), const IFT_IDSL ideal-int
pkg syscall (netbsd-arm64), const IFT_IEEE1394 = 144
pkg syscall (netbsd-arm64), const IFT_IEEE1394 ideal-int
pkg syscall (netbsd-arm64), const IFT_IEEE80211 = 71
pkg syscall (netbsd-arm64), const IFT_IEEE80211 ideal-int
pkg syscall (netbsd-arm64), const IFT_IEEE80212 = 55
pkg syscall (netbsd-arm64), const IFT_IEEE80212 ideal-int
pkg syscall (netbsd-arm64), const IFT_IEEE8023ADLAG = 161
pkg syscall (netbsd-arm64), const IFT_IEEE8023ADLAG ideal-int
pkg syscall (netbsd-arm64), const IFT_IFGSN = 145
pkg syscall (netbsd-arm64), const IFT_IFGSN ideal-int
pkg syscall (netbsd-arm64), const IFT_IMT = 190
pkg syscall (netbsd-arm64), const IFT_IMT ideal-int
pkg syscall (netbsd-arm64), const IFT_INFINIBAND = 199
pkg syscall (netbsd-arm64), const IFT_INFINIBAND ideal-int
pkg syscall (netbsd-arm64), const IFT_INTERLEAVE = 124
pkg syscall (netbsd-arm64), const IFT_INTERLEAVE ideal-int
pkg syscall (netbsd-arm64), const IFT_IP = 126
pkg syscall (netbsd-arm64), const IFT_IPFORWARD = 142
pkg syscall (netbsd-arm64), const IFT_IPFORWARD ideal-int
pkg syscall (netbsd-arm64), const IFT_IP ideal-int
pkg syscall (netbsd-arm64), const IFT_IPOVERATM = 114
pkg syscall (netbsd-arm64), const IFT_IPOVERATM ideal-int
pkg syscall (netbsd-arm64), const IFT_IPOVERCDLC = 109
pkg syscall (netbsd-arm64), const IFT_IPOVERCDLC ideal-int
pkg syscall (netbsd-arm64), const IFT_IPOVERCLAW = 110
pkg syscall (netbsd-arm64), const IFT_IPOVERCLAW ideal-int
pkg syscall (netbsd-arm64), const IFT_IPSWITCH = 78
pkg syscall (netbsd-arm64), const IFT_IPSWITCH ideal-int
pkg syscall (netbsd-arm64), const IFT_ISDN = 63
pkg syscall (netbsd-arm64), const IFT_ISDNBASIC = 20
pkg syscall (netbsd-arm64), const IFT_ISDNBASIC ideal-int
pkg syscall (netbsd-arm64), const IFT_ISDN ideal-int
pkg syscall (netbsd-arm64), const IFT_ISDNPRIMARY = 21
pkg syscall (netbsd-arm64), const IFT_ISDNPRIMARY ideal-int
pkg syscall (netbsd-arm64), const IFT_ISDNS = 75
pkg syscall (netbsd-arm64), const IFT_ISDNS ideal-int
pkg syscall (netbsd-arm64), const IFT_ISDNU = 76
pkg syscall (netbsd-arm64), const IFT_ISDNU ideal-int
pkg syscall (netbsd-arm64), const IFT_ISO88022LLC = 41
pkg syscall (netbsd-arm64), const IFT_ISO88022LLC ideal-int
pkg syscall (netbsd-arm64), const IFT_ISO88023 = 7
pkg syscall (netbsd-arm64), const IFT_ISO88023 ideal-int
pkg syscall (netbsd-arm64), const IFT_ISO88024 = 8
pkg syscall (netbsd-arm64), const IFT_ISO88024 ideal-int
pkg syscall (netbsd-arm64), const IFT_ISO88025 = 9
pkg syscall (netbsd-arm64), const IFT_ISO88025CRFPINT = 98
pkg syscall (netbsd-arm64), const IFT_ISO88025CRFPINT ideal-int
pkg syscall (netbsd-arm64), const IFT_ISO88025DTR = 86
pkg syscall (netbsd-arm64), const IFT_ISO88025DTR ideal-int
pkg syscall (netbsd-arm64), const IFT_ISO88025FIBER = 115
pkg syscall (netbsd-arm64), const IFT_ISO88025FIBER ideal-int
pkg syscall (netbsd-arm64), const IFT_ISO88025 ideal-int
pkg syscall (netbsd-arm64), const IFT_ISO88026 = 10
pkg syscall (netbsd-arm64), const IFT_ISO88026 ideal-int
pkg syscall (netbsd-arm64), const IFT_ISUP = 179
pkg syscall (netbsd-arm64), const IFT_ISUP ideal-int
pkg syscall (netbsd-arm64), const IFT_L2VLAN = 135
pkg syscall (netbsd-arm64), const IFT_L2VLAN ideal-int
pkg syscall (netbsd-arm64), const IFT_L3IPVLAN = 136
pkg syscall (netbsd-arm64), const IFT_L3IPVLAN ideal-int
pkg syscall (netbsd-arm64), const IFT_L3IPXVLAN = 137
pkg syscall (netbsd-arm64), const IFT_L3IPXVLAN ideal-int
pkg syscall (netbsd-arm64), const IFT_LAPB = 16
pkg syscall (netbsd-arm64), const IFT_LAPB ideal-int
pkg syscall (netbsd-arm64), const IFT_LAPD = 77
pkg syscall (netbsd-arm64), const IFT_LAPD ideal-int
pkg syscall (netbsd-arm64), const IFT_LAPF = 119
pkg syscall (netbsd-arm64), const IFT_LAPF ideal-int
pkg syscall (netbsd-arm64), const IFT_LINEGROUP = 210
pkg syscall (netbsd-arm64), const IFT_LINEGROUP ideal-int
pkg syscall (netbsd-arm64), const IFT_LOCALTALK = 42
pkg syscall (netbsd-arm64), const IFT_LOCALTALK ideal-int
pkg syscall (netbsd-arm64), const IFT_LOOP = 24
pkg syscall (netbsd-arm64), const IFT_LOOP ideal-int
pkg syscall (netbsd-arm64), const IFT_MEDIAMAILOVERIP = 139
pkg syscall (netbsd-arm64), const IFT_MEDIAMAILOVERIP ideal-int
pkg syscall (netbsd-arm64), const IFT_MFSIGLINK = 167
pkg syscall (netbsd-arm64), const IFT_MFSIGLINK ideal-int
pkg syscall (netbsd-arm64), const IFT_MIOX25 = 38
pkg syscall (netbsd-arm64), const IFT_MIOX25 ideal-int
pkg syscall (netbsd-arm64), const IFT_MODEM = 48
pkg syscall (netbsd-arm64), const IFT_MODEM ideal-int
pkg syscall (netbsd-arm64), const IFT_MPC = 113
pkg syscall (netbsd-arm64), const IFT_MPC ideal-int
pkg syscall (netbsd-arm64), const IFT_MPLS = 166
pkg syscall (netbsd-arm64), const IFT_MPLS ideal-int
pkg syscall (netbsd-arm64), const IFT_MPLSTUNNEL = 150
pkg syscall (netbsd-arm64), const IFT_MPLSTUNNEL ideal-int
pkg syscall (netbsd-arm64), const IFT_MSDSL = 143
pkg syscall (netbsd-arm64), const IFT_MSDSL ideal-int
pkg syscall (netbsd-arm64), const IFT_MVL = 191
pkg syscall (netbsd-arm64), const IFT_MVL ideal-int
pkg syscall (netbsd-arm64), const IFT_MYRINET = 99
pkg syscall (netbsd-arm64), const IFT_MYRINET ideal-int
pkg syscall (netbsd-arm64), const IFT_NFAS = 175
pkg syscall (netbsd-arm64), const IFT_NFAS ideal-int
pkg syscall (netbsd-arm64), const IFT_NSIP = 27
pkg syscall (netbsd-arm64), const IFT_NSIP ideal-int
pkg syscall (netbsd-arm64), const IFT_OPTICALCHANNEL = 195
pkg syscall (netbsd-arm64), const IFT_OPTICALCHANNEL ideal-int
pkg syscall (netbsd-arm64), const IFT_OPTICALTRANSPORT = 196
pkg syscall (netbsd-arm64), const IFT_OPTICALTRANSPORT ideal-int
pkg syscall (netbsd-arm64), const IFT_OTHER = 1
pkg syscall (netbsd-arm64), const IFT_OTHER ideal-int
pkg syscall (netbsd-arm64), const IFT_P10 = 12
pkg syscall (netbsd-arm64), const IFT_P10 ideal-int
pkg syscall (netbsd-arm64), const IFT_P80 = 13
pkg syscall (netbsd-arm64), const IFT_P80 ideal-int
pkg syscall (netbsd-arm64), const IFT_PARA = 34
pkg syscall (netbsd-arm64), const IFT_PARA ideal-int
pkg syscall (netbsd-arm64), const IFT_PFLOG = 245
pkg syscall (netbsd-arm64), const IFT_PFLOG ideal-int
pkg syscall (netbsd-arm64), const IFT_PFSYNC = 246
pkg syscall (netbsd-arm64), const IFT_PFSYNC ideal-int
pkg syscall (netbsd-arm64), const IFT_PLC = 174
pkg syscall (netbsd-arm64), const IFT_PLC ideal-int
pkg syscall (netbsd-arm64), const IFT_PON155 = 207
pkg syscall (netbsd-arm64), const IFT_PON155 ideal-int
pkg syscall (netbsd-arm64), const IFT_PON622 = 208
pkg syscall (netbsd-arm64), const IFT_PON622 ideal-int
pkg syscall (netbsd-arm64), const IFT_POS = 171
pkg syscall (netbsd-arm64), const IFT_POS ideal-int
pkg syscall (netbsd-arm64), const IFT_PPP = 23
pkg syscall (netbsd-arm64), const IFT_PPP ideal-int
pkg syscall (netbsd-arm64), const IFT_PPPMULTILINKBUNDLE = 108
pkg syscall (netbsd-arm64), const IFT_PPPMULTILINKBUNDLE ideal-int
pkg syscall (netbsd-arm64), const IFT_PROPATM = 197
pkg syscall (netbsd-arm64), const IFT_PROPATM ideal-int
pkg syscall (netbsd-arm64), const IFT_PROPBWAP2MP = 184
pkg syscall (netbsd-arm64), const IFT_PROPBWAP2MP ideal-int
pkg syscall (netbsd-arm64), const IFT_PROPCNLS = 89
pkg syscall (netbsd-arm64), const IFT_PROPCNLS ideal-int
pkg syscall (netbsd-arm64), const IFT_PROPDOCSWIRELESSDOWNSTREAM = 181
pkg syscall (netbsd-arm64), const IFT_PROPDOCSWIRELESSDOWNSTREAM ideal-int
pkg syscall (netbsd-arm64), const IFT_PROPDOCSWIRELESSMACLAYER = 180
pkg syscall (netbsd-arm64), const IFT_PROPDOCSWIRELESSMACLAYER ideal-int
pkg syscall (netbsd-arm64), const IFT_PROPDOCSWIRELESSUPSTREAM = 182
pkg syscall (netbsd-arm64), const IFT_PROPDOCSWIRELESSUPSTREAM ideal-int
pkg syscall (netbsd-arm64), const IFT_PROPMUX = 54
pkg syscall (netbsd-arm64), const IFT_PROPMUX ideal-int
pkg syscall (netbsd-arm64), const IFT_PROPVIRTUAL = 53
pkg syscall (netbsd-arm64), const IFT_PROPVIRTUAL ideal-int
pkg syscall (netbsd-arm64), const IFT_PROPWIRELESSP2P = 157
pkg syscall (netbsd-arm64), const IFT_PROPWIRELESSP2P ideal-int
pkg syscall (netbsd-arm64), const IFT_PTPSERIAL = 22
pkg syscall (netbsd-arm64), const IFT_PTPSERIAL ideal-int
pkg syscall (netbsd-arm64), const IFT_PVC = 241
pkg syscall (netbsd-arm64), const IFT_PVC ideal-int
pkg syscall (netbsd-arm64), const IFT_Q2931 = 201
pkg syscall (netbsd-arm64), const IFT_Q2931 ideal-int
pkg syscall (netbsd-arm64), const IFT_QLLC = 68
pkg syscall (netbsd-arm64), const IFT_QLLC ideal-int
pkg syscall (netbsd-arm64), const IFT_RADIOMAC = 188
pkg syscall (netbsd-arm64), const IFT_RADIOMAC ideal-int
pkg syscall (netbsd-arm64), const IFT_RADSL = 95
pkg syscall (netbsd-arm64), const IFT_RADSL ideal-int
pkg syscall (netbsd-arm64), const IFT_REACHDSL = 192
pkg syscall (netbsd-arm64), const IFT_REACHDSL ideal-int
pkg syscall (netbsd-arm64), const IFT_RFC1483 = 159
pkg syscall (netbsd-arm64), const IFT_RFC1483 ideal-int
pkg syscall (netbsd-arm64), const IFT_RS232 = 33
pkg syscall (netbsd-arm64), const IFT_RS232 ideal-int
pkg syscall (netbsd-arm64), const IFT_RSRB = 79
pkg syscall (netbsd-arm64), const IFT_RSRB ideal-int
pkg syscall (netbsd-arm64), const IFT_SDLC = 17
pkg syscall (netbsd-arm64), const IFT_SDLC ideal-int
pkg syscall (netbsd-arm64), const IFT_SDSL = 96
pkg syscall (netbsd-arm64), const IFT_SDSL ideal-int
pkg syscall (netbsd-arm64), const IFT_SHDSL = 169
pkg syscall (netbsd-arm64), const IFT_SHDSL ideal-int
pkg syscall (netbsd-arm64), const IFT_SIP = 31
pkg syscall (netbsd-arm64), const IFT_SIP ideal-int
pkg syscall (netbsd-arm64), const IFT_SIPSIG = 204
pkg syscall (netbsd-arm64), const IFT_SIPSIG ideal-int
pkg syscall (netbsd-arm64), const IFT_SIPTG = 203
pkg syscall (netbsd-arm64), const IFT_SIPTG ideal-int
pkg syscall (netbsd-arm64), const IFT_SLIP = 28
pkg syscall (netbsd-arm64), const IFT_SLIP ideal-int
pkg syscall (netbsd-arm64), const IFT_SMDSDXI = 43
pkg syscall (netbsd-arm64), const IFT_SMDSDXI ideal-int
pkg syscall (netbsd-arm64), const IFT_SMDSICIP = 52
pkg syscall (netbsd-arm64), const IFT_SMDSICIP ideal-int
pkg syscall (netbsd-arm64), const IFT_SONET = 39
pkg syscall (netbsd-arm64), const IFT_SONET ideal-int
pkg syscall (netbsd-arm64), const IFT_SONETOVERHEADCHANNEL = 185
pkg syscall (netbsd-arm64), const IFT_SONETOVERHEADCHANNEL ideal-int
pkg syscall (netbsd-arm64), const IFT_SONETPATH = 50
pkg syscall (netbsd-arm64), const IFT_SONETPATH ideal-int
pkg syscall (netbsd-arm64), const IFT_SONETVT = 51
pkg syscall (netbsd-arm64), const IFT_SONETVT ideal-int
pkg syscall (netbsd-arm64), const IFT_SRP = 151
pkg syscall (netbsd-arm64), const IFT_SRP ideal-int
pkg syscall (netbsd-arm64), const IFT_SS7SIGLINK = 156
pkg syscall (netbsd-arm64), const IFT_SS7SIGLINK ideal-int
pkg syscall (netbsd-arm64), const IFT_STACKTOSTACK = 111
pkg syscall (netbsd-arm64), const IFT_STACKTOSTACK ideal-int
pkg syscall (netbsd-arm64), const IFT_STARLAN = 11
pkg syscall (netbsd-arm64), const IFT_STARLAN ideal-int
pkg syscall (netbsd-arm64), const IFT_STF = 215
pkg syscall (netbsd-arm64), const IFT_STF ideal-int
pkg syscall (netbsd-arm64), const IFT_T1 = 18
pkg syscall (netbsd-arm64), const IFT_T1 ideal-int
pkg syscall (netbsd-arm64), const IFT_TDLC = 116
pkg syscall (netbsd-arm64), const IFT_TDLC ideal-int
pkg syscall (netbsd-arm64), const IFT_TELINK = 200
pkg syscall (netbsd-arm64), const IFT_TELINK ideal-int
pkg syscall (netbsd-arm64), const IFT_TERMPAD = 91
pkg syscall (netbsd-arm64), const IFT_TERMPAD ideal-int
pkg syscall (netbsd-arm64), const IFT_TR008 = 176
pkg syscall (netbsd-arm64), const IFT_TR008 ideal-int
pkg syscall (netbsd-arm64), const IFT_TRANSPHDLC = 123
pkg syscall (netbsd-arm64), const IFT_TRANSPHDLC ideal-int
pkg syscall (netbsd-arm64), const IFT_TUNNEL = 131
pkg syscall (netbsd-arm64), const IFT_TUNNEL ideal-int
pkg syscall (netbsd-arm64), const IFT_ULTRA = 29
pkg syscall (netbsd-arm64), const IFT_ULTRA ideal-int
pkg syscall (netbsd-arm64), const IFT_USB = 160
pkg syscall (netbsd-arm64), const IFT_USB ideal-int
pkg syscall (netbsd-arm64), const IFT_V11 = 64
pkg syscall (netbsd-arm64), const IFT_V11 ideal-int
pkg syscall (netbsd-arm64), const IFT_V35 = 45
pkg syscall (netbsd-arm64), const IFT_V35 ideal-int
pkg syscall (netbsd-arm64), const IFT_V36 = 65
pkg syscall (netbsd-arm64), const IFT_V36 ideal-int
pkg syscall (netbsd-arm64), const IFT_V37 = 120
pkg syscall (netbsd-arm64), const IFT_V37 ideal-int
pkg syscall (netbsd-arm64), const IFT_VDSL = 97
pkg syscall (netbsd-arm64), const IFT_VDSL ideal-int
pkg syscall (netbsd-arm64), const IFT_VIRTUALIPADDRESS = 112
pkg syscall (netbsd-arm64), const IFT_VIRTUALIPADDRESS ideal-int
pkg syscall (netbsd-arm64), const IFT_VIRTUALTG = 202
pkg syscall (netbsd-arm64), const IFT_VIRTUALTG ideal-int
pkg syscall (netbsd-arm64), const IFT_VOICEDID = 213
pkg syscall (netbsd-arm64), const IFT_VOICEDID ideal-int
pkg syscall (netbsd-arm64), const IFT_VOICEEM = 100
pkg syscall (netbsd-arm64), const IFT_VOICEEMFGD = 211
pkg syscall (netbsd-arm64), const IFT_VOICEEMFGD ideal-int
pkg syscall (netbsd-arm64), const IFT_VOICEEM ideal-int
pkg syscall (netbsd-arm64), const IFT_VOICEENCAP = 103
pkg syscall (netbsd-arm64), const IFT_VOICEENCAP ideal-int
pkg syscall (netbsd-arm64), const IFT_VOICEFGDEANA = 212
pkg syscall (netbsd-arm64), const IFT_VOICEFGDEANA ideal-int
pkg syscall (netbsd-arm64), const IFT_VOICEFXO = 101
pkg syscall (netbsd-arm64), const IFT_VOICEFXO ideal-int
pkg syscall (netbsd-arm64), const IFT_VOICEFXS = 102
pkg syscall (netbsd-arm64), const IFT_VOICEFXS ideal-int
pkg syscall (netbsd-arm64), const IFT_VOICEOVERATM = 152
pkg syscall (netbsd-arm64), const IFT_VOICEOVERATM ideal-int
pkg syscall (netbsd-arm64), const IFT_VOICEOVERCABLE = 198
pkg syscall (netbsd-arm64), const IFT_VOICEOVERCABLE ideal-int
pkg syscall (netbsd-arm64), const IFT_VOICEOVERFRAMERELAY = 153
pkg syscall (netbsd-arm64), const IFT_VOICEOVERFRAMERELAY ideal-int
pkg syscall (netbsd-arm64), const IFT_VOICEOVERIP = 104
pkg syscall (netbsd-arm64), const IFT_VOICEOVERIP ideal-int
pkg syscall (netbsd-arm64), const IFT_X213 = 93
pkg syscall (netbsd-arm64), const IFT_X213 ideal-int
pkg syscall (netbsd-arm64), const IFT_X25 = 5
pkg syscall (netbsd-arm64), const IFT_X25DDN = 4
pkg syscall (netbsd-arm64), const IFT_X25DDN ideal-int
pkg syscall (netbsd-arm64), const IFT_X25HUNTGROUP = 122
pkg syscall (netbsd-arm64), const IFT_X25HUNTGROUP ideal-int
pkg syscall (netbsd-arm64), const IFT_X25 ideal-int
pkg syscall (netbsd-arm64), const IFT_X25MLP = 121
pkg syscall (netbsd-arm64), const IFT_X25MLP ideal-int
pkg syscall (netbsd-arm64), const IFT_X25PLE = 40
pkg syscall (netbsd-arm64), const IFT_X25PLE ideal-int
pkg syscall (netbsd-arm64), const IFT_XETHER = 26
pkg syscall (netbsd-arm64), const IFT_XETHER ideal-int
pkg syscall (netbsd-arm64), const IGNBRK = 1
pkg syscall (netbsd-arm64), const IGNBRK ideal-int
pkg syscall (netbsd-arm64), const IGNCR = 128
pkg syscall (netbsd-arm64), const IGNCR ideal-int
pkg syscall (netbsd-arm64), const IGNPAR = 4
pkg syscall (netbsd-arm64), const IGNPAR ideal-int
pkg syscall (netbsd-arm64), const IMAXBEL = 8192
pkg syscall (netbsd-arm64), const IMAXBEL ideal-int
pkg syscall (netbsd-arm64), const IN_CLASSA_HOST = 16777215
pkg syscall (netbsd-arm64), const IN_CLASSA_HOST ideal-int
pkg syscall (netbsd-arm64), const IN_CLASSA_MAX = 128
pkg syscall (netbsd-arm64), const IN_CLASSA_MAX ideal-int
pkg syscall (netbsd-arm64), const IN_CLASSA_NET = 4278190080
pkg syscall (netbsd-arm64), const IN_CLASSA_NET ideal-int
pkg syscall (netbsd-arm64), const IN_CLASSA_NSHIFT = 24
pkg syscall (netbsd-arm64), const IN_CLASSA_NSHIFT ideal-int
pkg syscall (netbsd-arm64), const IN_CLASSB_HOST = 65535
pkg syscall (netbsd-arm64), const IN_CLASSB_HOST ideal-int
pkg syscall (netbsd-arm64), const IN_CLASSB_MAX = 65536
pkg syscall (netbsd-arm64), const IN_CLASSB_MAX ideal-int
pkg syscall (netbsd-arm64), const IN_CLASSB_NET = 4294901760
pkg syscall (netbsd-arm64), const IN_CLASSB_NET ideal-int
pkg syscall (netbsd-arm64), const IN_CLASSB_NSHIFT = 16
pkg syscall (netbsd-arm64), const IN_CLASSB_NSHIFT ideal-int
pkg syscall (netbsd-arm64), const IN_CLASSC_HOST = 255
pkg syscall (netbsd-arm64), const IN_CLASSC_HOST ideal-int
pkg syscall (netbsd-arm64), const IN_CLASSC_NET = 4294967040
pkg syscall (netbsd-arm64), const IN_CLASSC_NET ideal-int
pkg syscall (netbsd-arm64), const IN_CLASSC_NSHIFT = 8
pkg syscall (netbsd-arm64), const IN_CLASSC_NSHIFT ideal-int
pkg syscall (netbsd-arm64), const IN_CLASSD_HOST = 268435455
pkg syscall (netbsd-arm64), const IN_CLASSD_HOST ideal-int
pkg syscall (netbsd-arm64), const IN_CLASSD_NET = 4026531840
pkg syscall (netbsd-arm64), const IN_CLASSD_NET ideal-int
pkg syscall (netbsd-arm64), const IN_CLASSD_NSHIFT = 28
pkg syscall (netbsd-arm64), const IN_CLASSD_NSHIFT ideal-int
pkg syscall (netbsd-arm64), const INLCR = 64
pkg syscall (netbsd-arm64), const INLCR ideal-int
pkg syscall (netbsd-arm64), const IN_LOOPBACKNET = 127
pkg syscall (netbsd-arm64), const IN_LOOPBACKNET ideal-int
pkg syscall (netbsd-arm64), const INPCK = 16
pkg syscall (netbsd-arm64), const INPCK ideal-int
pkg syscall (netbsd-arm64), const IP_ADD_MEMBERSHIP = 12
pkg syscall (netbsd-arm64), const IP_DEFAULT_MULTICAST_LOOP = 1
pkg syscall (netbsd-arm64), const IP_DEFAULT_MULTICAST_LOOP ideal-int
pkg syscall (netbsd-arm64), const IP_DEFAULT_MULTICAST_TTL = 1
pkg syscall (netbsd-arm64), const IP_DEFAULT_MULTICAST_TTL ideal-int
pkg syscall (netbsd-arm64), const IP_DF = 16384
pkg syscall (netbsd-arm64), const IP_DF ideal-int
pkg syscall (netbsd-arm64), const IP_DROP_MEMBERSHIP = 13
pkg syscall (netbsd-arm64), const IP_EF = 32768
pkg syscall (netbsd-arm64), const IP_EF ideal-int
pkg syscall (netbsd-arm64), const IP_ERRORMTU = 21
pkg syscall (netbsd-arm64), const IP_ERRORMTU ideal-int
pkg syscall (netbsd-arm64), const IP_HDRINCL = 2
pkg syscall (netbsd-arm64), const IP_HDRINCL ideal-int
pkg syscall (netbsd-arm64), const IP_IPSEC_POLICY = 22
pkg syscall (netbsd-arm64), const IP_IPSEC_POLICY ideal-int
pkg syscall (netbsd-arm64), const IP_MAX_MEMBERSHIPS = 20
pkg syscall (netbsd-arm64), const IP_MAX_MEMBERSHIPS ideal-int
pkg syscall (netbsd-arm64), const IP_MAXPACKET = 65535
pkg syscall (netbsd-arm64), const IP_MAXPACKET ideal-int
pkg syscall (netbsd-arm64), const IP_MF = 8192
pkg syscall (netbsd-arm64), const IP_MF ideal-int
pkg syscall (netbsd-arm64), const IP_MINFRAGSIZE = 69
pkg syscall (netbsd-arm64), const IP_MINFRAGSIZE ideal-int
pkg syscall (netbsd-arm64), const IP_MINTTL = 24
pkg syscall (netbsd-arm64), const IP_MINTTL ideal-int
pkg syscall (netbsd-arm64), const IP_MSS = 576
pkg syscall (netbsd-arm64), const IP_MSS ideal-int
pkg syscall (netbsd-arm64), const IP_MULTICAST_IF = 9
pkg syscall (netbsd-arm64), const IP_MULTICAST_LOOP = 11
pkg syscall (netbsd-arm64), const IP_MULTICAST_TTL = 10
pkg syscall (netbsd-arm64), const IP_OFFMASK = 8191
pkg syscall (netbsd-arm64), const IP_OFFMASK ideal-int
pkg syscall (netbsd-arm64), const IP_OPTIONS = 1
pkg syscall (netbsd-arm64), const IP_OPTIONS ideal-int
pkg syscall (netbsd-arm64), const IP_PORTRANGE = 19
pkg syscall (netbsd-arm64), const IP_PORTRANGE_DEFAULT = 0
pkg syscall (netbsd-arm64), const IP_PORTRANGE_DEFAULT ideal-int
pkg syscall (netbsd-arm64), const IP_PORTRANGE_HIGH = 1
pkg syscall (netbsd-arm64), const IP_PORTRANGE_HIGH ideal-int
pkg syscall (netbsd-arm64), const IP_PORTRANGE ideal-int
pkg syscall (netbsd-arm64), const IP_PORTRANGE_LOW = 2
pkg syscall (netbsd-arm64), const IP_PORTRANGE_LOW ideal-int
pkg syscall (netbsd-arm64), const IPPROTO_AH = 51
pkg syscall (netbsd-arm64), const IPPROTO_AH ideal-int
pkg syscall (netbsd-arm64), const IPPROTO_CARP = 112
pkg syscall (netbsd-arm64), const IPPROTO_CARP ideal-int
pkg syscall (netbsd-arm64), const IPPROTO_DONE = 257
pkg syscall (netbsd-arm64), const IPPROTO_DONE ideal-int
pkg syscall (netbsd-arm64), const IPPROTO_DSTOPTS = 60
pkg syscall (netbsd-arm64), const IPPROTO_DSTOPTS ideal-int
pkg syscall (netbsd-arm64), const IPPROTO_EGP = 8
pkg syscall (netbsd-arm64), const IPPROTO_EGP ideal-int
pkg syscall (netbsd-arm64), const IPPROTO_ENCAP = 98
pkg syscall (netbsd-arm64), const IPPROTO_ENCAP ideal-int
pkg syscall (netbsd-arm64), const IPPROTO_EON = 80
pkg syscall (netbsd-arm64), const IPPROTO_EON ideal-int
pkg syscall (netbsd-arm64), const IPPROTO_ESP = 50
pkg syscall (netbsd-arm64), const IPPROTO_ESP ideal-int
pkg syscall (netbsd-arm64), const IPPROTO_ETHERIP = 97
pkg syscall (netbsd-arm64), const IPPROTO_ETHERIP ideal-int
pkg syscall (netbsd-arm64), const IPPROTO_FRAGMENT = 44
pkg syscall (netbsd-arm64), const IPPROTO_FRAGMENT ideal-int
pkg syscall (netbsd-arm64), const IPPROTO_GGP = 3
pkg syscall (netbsd-arm64), const IPPROTO_GGP ideal-int
pkg syscall (netbsd-arm64), const IPPROTO_GRE = 47
pkg syscall (netbsd-arm64), const IPPROTO_GRE ideal-int
pkg syscall (netbsd-arm64), const IPPROTO_HOPOPTS = 0
pkg syscall (netbsd-arm64), const IPPROTO_HOPOPTS ideal-int
pkg syscall (netbsd-arm64), const IPPROTO_ICMP = 1
pkg syscall (netbsd-arm64), const IPPROTO_ICMP ideal-int
pkg syscall (netbsd-arm64), const IPPROTO_ICMPV6 = 58
pkg syscall (netbsd-arm64), const IPPROTO_ICMPV6 ideal-int
pkg syscall (netbsd-arm64), const IPPROTO_IDP = 22
pkg syscall (netbsd-arm64), const IPPROTO_IDP ideal-int
pkg syscall (netbsd-arm64), const IPPROTO_IGMP = 2
pkg syscall (netbsd-arm64), const IPPROTO_IGMP ideal-int
pkg syscall (netbsd-arm64), const IPPROTO_IPCOMP = 108
pkg syscall (netbsd-arm64), const IPPROTO_IPCOMP ideal-int
pkg syscall (netbsd-arm64), const IPPROTO_IPIP = 4
pkg syscall (netbsd-arm64), const IPPROTO_IPIP ideal-int
pkg syscall (netbsd-arm64), const IPPROTO_IPV4 = 4
pkg syscall (netbsd-arm64), const IPPROTO_IPV4 ideal-int
pkg syscall (netbsd-arm64), const IPPROTO_IPV6_ICMP = 58
pkg syscall (netbsd-arm64), const IPPROTO_IPV6_ICMP ideal-int
pkg syscall (netbsd-arm64), const IPPROTO_MAX = 256
pkg syscall (netbsd-arm64), const IPPROTO_MAXID = 52
pkg syscall (netbsd-arm64), const IPPROTO_MAX ideal-int
pkg syscall (netbsd-arm64), const IPPROTO_MAXID ideal-int
pkg syscall (netbsd-arm64), const IPPROTO_MOBILE = 55
pkg syscall (netbsd-arm64), const IPPROTO_MOBILE ideal-int
pkg syscall (netbsd-arm64), const IPPROTO_NONE = 59
pkg syscall (netbsd-arm64), const IPPROTO_NONE ideal-int
pkg syscall (netbsd-arm64), const IPPROTO_PFSYNC = 240
pkg syscall (netbsd-arm64), const IPPROTO_PFSYNC ideal-int
pkg syscall (netbsd-arm64), const IPPROTO_PIM = 103
pkg syscall (netbsd-arm64), const IPPROTO_PIM ideal-int
pkg syscall (netbsd-arm64), const IPPROTO_PUP = 12
pkg syscall (netbsd-arm64), const IPPROTO_PUP ideal-int
pkg syscall (netbsd-arm64), const IPPROTO_RAW = 255
pkg syscall (netbsd-arm64), const IPPROTO_RAW ideal-int
pkg syscall (netbsd-arm64), const IPPROTO_ROUTING = 43
pkg syscall (netbsd-arm64), const IPPROTO_ROUTING ideal-int
pkg syscall (netbsd-arm64), const IPPROTO_RSVP = 46
pkg syscall (netbsd-arm64), const IPPROTO_RSVP ideal-int
pkg syscall (netbsd-arm64), const IPPROTO_TP = 29
pkg syscall (netbsd-arm64), const IPPROTO_TP ideal-int
pkg syscall (netbsd-arm64), const IPPROTO_VRRP = 112
pkg syscall (netbsd-arm64), const IPPROTO_VRRP ideal-int
pkg syscall (netbsd-arm64), const IP_RECVDSTADDR = 7
pkg syscall (netbsd-arm64), const IP_RECVDSTADDR ideal-int
pkg syscall (netbsd-arm64), const IP_RECVIF = 20
pkg syscall (netbsd-arm64), const IP_RECVIF ideal-int
pkg syscall (netbsd-arm64), const IP_RECVOPTS = 5
pkg syscall (netbsd-arm64), const IP_RECVOPTS ideal-int
pkg syscall (netbsd-arm64), const IP_RECVRETOPTS = 6
pkg syscall (netbsd-arm64), const IP_RECVRETOPTS ideal-int
pkg syscall (netbsd-arm64), const IP_RECVTTL = 23
pkg syscall (netbsd-arm64), const IP_RECVTTL ideal-int
pkg syscall (netbsd-arm64), const IP_RETOPTS = 8
pkg syscall (netbsd-arm64), const IP_RETOPTS ideal-int
pkg syscall (netbsd-arm64), const IP_RF = 32768
pkg syscall (netbsd-arm64), const IP_RF ideal-int
pkg syscall (netbsd-arm64), const IP_TOS = 3
pkg syscall (netbsd-arm64), const IP_TTL = 4
pkg syscall (netbsd-arm64), const IPV6_CHECKSUM = 26
pkg syscall (netbsd-arm64), const IPV6_CHECKSUM ideal-int
pkg syscall (netbsd-arm64), const IPV6_DEFAULT_MULTICAST_HOPS = 1
pkg syscall (netbsd-arm64), const IPV6_DEFAULT_MULTICAST_HOPS ideal-int
pkg syscall (netbsd-arm64), const IPV6_DEFAULT_MULTICAST_LOOP = 1
pkg syscall (netbsd-arm64), const IPV6_DEFAULT_MULTICAST_LOOP ideal-int
pkg syscall (netbsd-arm64), const IPV6_DEFHLIM = 64
pkg syscall (netbsd-arm64), const IPV6_DEFHLIM ideal-int
pkg syscall (netbsd-arm64), const IPV6_DONTFRAG = 62
pkg syscall (netbsd-arm64), const IPV6_DONTFRAG ideal-int
pkg syscall (netbsd-arm64), const IPV6_DSTOPTS = 50
pkg syscall (netbsd-arm64), const IPV6_DSTOPTS ideal-int
pkg syscall (netbsd-arm64), const IPV6_FAITH = 29
pkg syscall (netbsd-arm64), const IPV6_FAITH ideal-int
pkg syscall (netbsd-arm64), const IPV6_FLOWINFO_MASK = 4294967055
pkg syscall (netbsd-arm64), const IPV6_FLOWINFO_MASK ideal-int
pkg syscall (netbsd-arm64), const IPV6_FLOWLABEL_MASK = 4294905600
pkg syscall (netbsd-arm64), const IPV6_FLOWLABEL_MASK ideal-int
pkg syscall (netbsd-arm64), const IPV6_FRAGTTL = 120
pkg syscall (netbsd-arm64), const IPV6_FRAGTTL ideal-int
pkg syscall (netbsd-arm64), const IPV6_HLIMDEC = 1
pkg syscall (netbsd-arm64), const IPV6_HLIMDEC ideal-int
pkg syscall (netbsd-arm64), const IPV6_HOPLIMIT = 47
pkg syscall (netbsd-arm64), const IPV6_HOPLIMIT ideal-int
pkg syscall (netbsd-arm64), const IPV6_HOPOPTS = 49
pkg syscall (netbsd-arm64), const IPV6_HOPOPTS ideal-int
pkg syscall (netbsd-arm64), const IPV6_IPSEC_POLICY = 28
pkg syscall (netbsd-arm64), const IPV6_IPSEC_POLICY ideal-int
pkg syscall (netbsd-arm64), const IPV6_JOIN_GROUP = 12
pkg syscall (netbsd-arm64), const IPV6_LEAVE_GROUP = 13
pkg syscall (netbsd-arm64), const IPV6_MAXHLIM = 255
pkg syscall (netbsd-arm64), const IPV6_MAXHLIM ideal-int
pkg syscall (netbsd-arm64), const IPV6_MAXPACKET = 65535
pkg syscall (netbsd-arm64), const IPV6_MAXPACKET ideal-int
pkg syscall (netbsd-arm64), const IPV6_MMTU = 1280
pkg syscall (netbsd-arm64), const IPV6_MMTU ideal-int
pkg syscall (netbsd-arm64), const IPV6_MULTICAST_HOPS = 10
pkg syscall (netbsd-arm64), const IPV6_MULTICAST_IF = 9
pkg syscall (netbsd-arm64), const IPV6_MULTICAST_LOOP = 11
pkg syscall (netbsd-arm64), const IPV6_NEXTHOP = 48
pkg syscall (netbsd-arm64), const IPV6_NEXTHOP ideal-int
pkg syscall (netbsd-arm64), const IPV6_PATHMTU = 44
pkg syscall (netbsd-arm64), const IPV6_PATHMTU ideal-int
pkg syscall (netbsd-arm64), const IPV6_PKTINFO = 46
pkg syscall (netbsd-arm64), const IPV6_PKTINFO ideal-int
pkg syscall (netbsd-arm64), const IPV6_PORTRANGE = 14
pkg syscall (netbsd-arm64), const IPV6_PORTRANGE_DEFAULT = 0
pkg syscall (netbsd-arm64), const IPV6_PORTRANGE_DEFAULT ideal-int
pkg syscall (netbsd-arm64), const IPV6_PORTRANGE_HIGH = 1
pkg syscall (netbsd-arm64), const IPV6_PORTRANGE_HIGH ideal-int
pkg syscall (netbsd-arm64), const IPV6_PORTRANGE ideal-int
pkg syscall (netbsd-arm64), const IPV6_PORTRANGE_LOW = 2
pkg syscall (netbsd-arm64), const IPV6_PORTRANGE_LOW ideal-int
pkg syscall (netbsd-arm64), const IPV6_RECVDSTOPTS = 40
pkg syscall (netbsd-arm64), const IPV6_RECVDSTOPTS ideal-int
pkg syscall (netbsd-arm64), const IPV6_RECVHOPLIMIT = 37
pkg syscall (netbsd-arm64), const IPV6_RECVHOPLIMIT ideal-int
pkg syscall (netbsd-arm64), const IPV6_RECVHOPOPTS = 39
pkg syscall (netbsd-arm64), const IPV6_RECVHOPOPTS ideal-int
pkg syscall (netbsd-arm64), const IPV6_RECVPATHMTU = 43
pkg syscall (netbsd-arm64), const IPV6_RECVPATHMTU ideal-int
pkg syscall (netbsd-arm64), const IPV6_RECVPKTINFO = 36
pkg syscall (netbsd-arm64), const IPV6_RECVPKTINFO ideal-int
pkg syscall (netbsd-arm64), const IPV6_RECVRTHDR = 38
pkg syscall (netbsd-arm64), const IPV6_RECVRTHDR ideal-int
pkg syscall (netbsd-arm64), const IPV6_RECVTCLASS = 57
pkg syscall (netbsd-arm64), const IPV6_RECVTCLASS ideal-int
pkg syscall (netbsd-arm64), const IPV6_RTHDR = 51
pkg syscall (netbsd-arm64), const IPV6_RTHDRDSTOPTS = 35
pkg syscall (netbsd-arm64), const IPV6_RTHDRDSTOPTS ideal-int
pkg syscall (netbsd-arm64), const IPV6_RTHDR ideal-int
pkg syscall (netbsd-arm64), const IPV6_RTHDR_LOOSE = 0
pkg syscall (netbsd-arm64), const IPV6_RTHDR_LOOSE ideal-int
pkg syscall (netbsd-arm64), const IPV6_RTHDR_STRICT = 1
pkg syscall (netbsd-arm64), const IPV6_RTHDR_STRICT ideal-int
pkg syscall (netbsd-arm64), const IPV6_RTHDR_TYPE_0 = 0
pkg syscall (netbsd-arm64), const IPV6_RTHDR_TYPE_0 ideal-int
pkg syscall (netbsd-arm64), const IPV6_SOCKOPT_RESERVED1 = 3
pkg syscall (netbsd-arm64), const IPV6_SOCKOPT_RESERVED1 ideal-int
pkg syscall (netbsd-arm64), const IPV6_TCLASS = 61
pkg syscall (netbsd-arm64), const IPV6_TCLASS ideal-int
pkg syscall (netbsd-arm64), const IPV6_UNICAST_HOPS = 4
pkg syscall (netbsd-arm64), const IPV6_USE_MIN_MTU = 42
pkg syscall (netbsd-arm64), const IPV6_USE_MIN_MTU ideal-int
pkg syscall (netbsd-arm64), const IPV6_V6ONLY = 27
pkg syscall (netbsd-arm64), const IPV6_VERSION = 96
pkg syscall (netbsd-arm64), const IPV6_VERSION ideal-int
pkg syscall (netbsd-arm64), const IPV6_VERSION_MASK = 240
pkg syscall (netbsd-arm64), const IPV6_VERSION_MASK ideal-int
pkg syscall (netbsd-arm64), const ISIG = 128
pkg syscall (netbsd-arm64), const ISIG ideal-int
pkg syscall (netbsd-arm64), const ISTRIP = 32
pkg syscall (netbsd-arm64), const ISTRIP ideal-int
pkg syscall (netbsd-arm64), const IXANY = 2048
pkg syscall (netbsd-arm64), const IXANY ideal-int
pkg syscall (netbsd-arm64), const IXOFF = 1024
pkg syscall (netbsd-arm64), const IXOFF ideal-int
pkg syscall (netbsd-arm64), const IXON = 512
pkg syscall (netbsd-arm64), const IXON ideal-int
pkg syscall (netbsd-arm64), const LOCK_EX = 2
pkg syscall (netbsd-arm64), const LOCK_EX ideal-int
pkg syscall (netbsd-arm64), const LOCK_NB = 4
pkg syscall (netbsd-arm64), const LOCK_NB ideal-int
pkg syscall (netbsd-arm64), const LOCK_SH = 1
pkg syscall (netbsd-arm64), const LOCK_SH ideal-int
pkg syscall (netbsd-arm64), const LOCK_UN = 8
pkg syscall (netbsd-arm64), const LOCK_UN ideal-int
pkg syscall (netbsd-arm64), const MADV_DONTNEED = 4
pkg syscall (netbsd-arm64), const MADV_DONTNEED ideal-int
pkg syscall (netbsd-arm64), const MADV_FREE = 6
pkg syscall (netbsd-arm64), const MADV_FREE ideal-int
pkg syscall (netbsd-arm64), const MADV_NORMAL = 0
pkg syscall (netbsd-arm64), const MADV_NORMAL ideal-int
pkg syscall (netbsd-arm64), const MADV_RANDOM = 1
pkg syscall (netbsd-arm64), const MADV_RANDOM ideal-int
pkg syscall (netbsd-arm64), const MADV_SEQUENTIAL = 2
pkg syscall (netbsd-arm64), const MADV_SEQUENTIAL ideal-int
pkg syscall (netbsd-arm64), const MADV_SPACEAVAIL = 5
pkg syscall (netbsd-arm64), const MADV_SPACEAVAIL ideal-int
pkg syscall (netbsd-arm64), const MADV_WILLNEED = 3
pkg syscall (netbsd-arm64), const MADV_WILLNEED ideal-int
pkg syscall (netbsd-arm64), const MAP_ALIGNMENT_16MB = 402653184
pkg syscall (netbsd-arm64), const MAP_ALIGNMENT_16MB ideal-int
pkg syscall (netbsd-arm64), const MAP_ALIGNMENT_1TB = 671088640
pkg syscall (netbsd-arm64), const MAP_ALIGNMENT_1TB ideal-int
pkg syscall (netbsd-arm64), const MAP_ALIGNMENT_256TB = 805306368
pkg syscall (netbsd-arm64), const MAP_ALIGNMENT_256TB ideal-int
pkg syscall (netbsd-arm64), const MAP_ALIGNMENT_4GB = 536870912
pkg syscall (netbsd-arm64), const MAP_ALIGNMENT_4GB ideal-int
pkg syscall (netbsd-arm64), const MAP_ALIGNMENT_64KB = 268435456
pkg syscall (netbsd-arm64), const MAP_ALIGNMENT_64KB ideal-int
pkg syscall (netbsd-arm64), const MAP_ALIGNMENT_64PB = 939524096
pkg syscall (netbsd-arm64), const MAP_ALIGNMENT_64PB ideal-int
pkg syscall (netbsd-arm64), const MAP_ALIGNMENT_MASK = -16777216
pkg syscall (netbsd-arm64), const MAP_ALIGNMENT_MASK ideal-int
pkg syscall (netbsd-arm64), const MAP_ALIGNMENT_SHIFT = 24
pkg syscall (netbsd-arm64), const MAP_ALIGNMENT_SHIFT ideal-int
pkg syscall (netbsd-arm64), const MAP_ANON = 4096
pkg syscall (netbsd-arm64), const MAP_ANON ideal-int
pkg syscall (netbsd-arm64), const MAP_FILE = 0
pkg syscall (netbsd-arm64), const MAP_FILE ideal-int
pkg syscall (netbsd-arm64), const MAP_FIXED = 16
pkg syscall (netbsd-arm64), const MAP_FIXED ideal-int
pkg syscall (netbsd-arm64), const MAP_HASSEMAPHORE = 512
pkg syscall (netbsd-arm64), const MAP_HASSEMAPHORE ideal-int
pkg syscall (netbsd-arm64), const MAP_INHERIT = 128
pkg syscall (netbsd-arm64), const MAP_INHERIT_COPY = 1
pkg syscall (netbsd-arm64), const MAP_INHERIT_COPY ideal-int
pkg syscall (netbsd-arm64), const MAP_INHERIT_DEFAULT = 1
pkg syscall (netbsd-arm64), const MAP_INHERIT_DEFAULT ideal-int
pkg syscall (netbsd-arm64), const MAP_INHERIT_DONATE_COPY = 3
pkg syscall (netbsd-arm64), const MAP_INHERIT_DONATE_COPY ideal-int
pkg syscall (netbsd-arm64), const MAP_INHERIT ideal-int
pkg syscall (netbsd-arm64), const MAP_INHERIT_NONE = 2
pkg syscall (netbsd-arm64), const MAP_INHERIT_NONE ideal-int
pkg syscall (netbsd-arm64), const MAP_INHERIT_SHARE = 0
pkg syscall (netbsd-arm64), const MAP_INHERIT_SHARE ideal-int
pkg syscall (netbsd-arm64), const MAP_NORESERVE = 64
pkg syscall (netbsd-arm64), const MAP_NORESERVE ideal-int
pkg syscall (netbsd-arm64), const MAP_PRIVATE = 2
pkg syscall (netbsd-arm64), const MAP_PRIVATE ideal-int
pkg syscall (netbsd-arm64), const MAP_RENAME = 32
pkg syscall (netbsd-arm64), const MAP_RENAME ideal-int
pkg syscall (netbsd-arm64), const MAP_SHARED = 1
pkg syscall (netbsd-arm64), const MAP_SHARED ideal-int
pkg syscall (netbsd-arm64), const MAP_STACK = 8192
pkg syscall (netbsd-arm64), const MAP_STACK ideal-int
pkg syscall (netbsd-arm64), const MAP_TRYFIXED = 1024
pkg syscall (netbsd-arm64), const MAP_TRYFIXED ideal-int
pkg syscall (netbsd-arm64), const MAP_WIRED = 2048
pkg syscall (netbsd-arm64), const MAP_WIRED ideal-int
pkg syscall (netbsd-arm64), const MCL_CURRENT = 1
pkg syscall (netbsd-arm64), const MCL_CURRENT ideal-int
pkg syscall (netbsd-arm64), const MCL_FUTURE = 2
pkg syscall (netbsd-arm64), const MCL_FUTURE ideal-int
pkg syscall (netbsd-arm64), const MS_ASYNC = 1
pkg syscall (netbsd-arm64), const MS_ASYNC ideal-int
pkg syscall (netbsd-arm64), const MSG_BCAST = 256
pkg syscall (netbsd-arm64), const MSG_BCAST ideal-int
pkg syscall (netbsd-arm64), const MSG_CMSG_CLOEXEC = 2048
pkg syscall (netbsd-arm64), const MSG_CMSG_CLOEXEC ideal-int
pkg syscall (netbsd-arm64), const MSG_CONTROLMBUF = 33554432
pkg syscall (netbsd-arm64), const MSG_CONTROLMBUF ideal-int
pkg syscall (netbsd-arm64), const MSG_CTRUNC = 32
pkg syscall (netbsd-arm64), const MSG_CTRUNC ideal-int
pkg syscall (netbsd-arm64), const MSG_DONTROUTE = 4
pkg syscall (netbsd-arm64), const MSG_DONTROUTE ideal-int
pkg syscall (netbsd-arm64), const MSG_DONTWAIT = 128
pkg syscall (netbsd-arm64), const MSG_DONTWAIT ideal-int
pkg syscall (netbsd-arm64), const MSG_EOR = 8
pkg syscall (netbsd-arm64), const MSG_EOR ideal-int
pkg syscall (netbsd-arm64), const MSG_IOVUSRSPACE = 67108864
pkg syscall (netbsd-arm64), const MSG_IOVUSRSPACE ideal-int
pkg syscall (netbsd-arm64), const MSG_LENUSRSPACE = 134217728
pkg syscall (netbsd-arm64), const MSG_LENUSRSPACE ideal-int
pkg syscall (netbsd-arm64), const MSG_MCAST = 512
pkg syscall (netbsd-arm64), const MSG_MCAST ideal-int
pkg syscall (netbsd-arm64), const MSG_NAMEMBUF = 16777216
pkg syscall (netbsd-arm64), const MSG_NAMEMBUF ideal-int
pkg syscall (netbsd-arm64), const MSG_NBIO = 4096
pkg syscall (netbsd-arm64), const MSG_NBIO ideal-int
pkg syscall (netbsd-arm64), const MSG_NOSIGNAL = 1024
pkg syscall (netbsd-arm64), const MSG_NOSIGNAL ideal-int
pkg syscall (netbsd-arm64), const MSG_OOB = 1
pkg syscall (netbsd-arm64), const MSG_OOB ideal-int
pkg syscall (netbsd-arm64), const MSG_PEEK = 2
pkg syscall (netbsd-arm64), const MSG_PEEK ideal-int
pkg syscall (netbsd-arm64), const MSG_TRUNC = 16
pkg syscall (netbsd-arm64), const MSG_TRUNC ideal-int
pkg syscall (netbsd-arm64), const MSG_USERFLAGS = 16777215
pkg syscall (netbsd-arm64), const MSG_USERFLAGS ideal-int
pkg syscall (netbsd-arm64), const MSG_WAITALL = 64
pkg syscall (netbsd-arm64), const MSG_WAITALL ideal-int
pkg syscall (netbsd-arm64), const MS_INVALIDATE = 2
pkg syscall (netbsd-arm64), const MS_INVALIDATE ideal-int
pkg syscall (netbsd-arm64), const MS_SYNC = 4
pkg syscall (netbsd-arm64), const MS_SYNC ideal-int
pkg syscall (netbsd-arm64), const NAME_MAX = 511
pkg syscall (netbsd-arm64), const NAME_MAX ideal-int
pkg syscall (netbsd-arm64), const NET_RT_DUMP = 1
pkg syscall (netbsd-arm64), const NET_RT_DUMP ideal-int
pkg syscall (netbsd-arm64), const NET_RT_FLAGS = 2
pkg syscall (netbsd-arm64), const NET_RT_FLAGS ideal-int
pkg syscall (netbsd-arm64), const NET_RT_IFLIST = 5
pkg syscall (netbsd-arm64), const NET_RT_IFLIST ideal-int
pkg syscall (netbsd-arm64), const NET_RT_MAXID = 6
pkg syscall (netbsd-arm64), const NET_RT_MAXID ideal-int
pkg syscall (netbsd-arm64), const NET_RT_OIFLIST = 4
pkg syscall (netbsd-arm64), const NET_RT_OIFLIST ideal-int
pkg syscall (netbsd-arm64), const NET_RT_OOIFLIST = 3
pkg syscall (netbsd-arm64), const NET_RT_OOIFLIST ideal-int
pkg syscall (netbsd-arm64), const NOFLSH = 2147483648
pkg syscall (netbsd-arm64), const NOFLSH ideal-int
pkg syscall (netbsd-arm64), const NOTE_ATTRIB = 8
pkg syscall (netbsd-arm64), const NOTE_ATTRIB ideal-int
pkg syscall (netbsd-arm64), const NOTE_CHILD = 4
pkg syscall (netbsd-arm64), const NOTE_CHILD ideal-int
pkg syscall (netbsd-arm64), const NOTE_DELETE = 1
pkg syscall (netbsd-arm64), const NOTE_DELETE ideal-int
pkg syscall (netbsd-arm64), const NOTE_EXEC = 536870912
pkg syscall (netbsd-arm64), const NOTE_EXEC ideal-int
pkg syscall (netbsd-arm64), const NOTE_EXIT = 2147483648
pkg syscall (netbsd-arm64), const NOTE_EXIT ideal-int
pkg syscall (netbsd-arm64), const NOTE_EXTEND = 4
pkg syscall (netbsd-arm64), const NOTE_EXTEND ideal-int
pkg syscall (netbsd-arm64), const NOTE_FORK = 1073741824
pkg syscall (netbsd-arm64), const NOTE_FORK ideal-int
pkg syscall (netbsd-arm64), const NOTE_LINK = 16
pkg syscall (netbsd-arm64), const NOTE_LINK ideal-int
pkg syscall (netbsd-arm64), const NOTE_LOWAT = 1
pkg syscall (netbsd-arm64), const NOTE_LOWAT ideal-int
pkg syscall (netbsd-arm64), const NOTE_PCTRLMASK = 4026531840
pkg syscall (netbsd-arm64), const NOTE_PCTRLMASK ideal-int
pkg syscall (netbsd-arm64), const NOTE_PDATAMASK = 1048575
pkg syscall (netbsd-arm64), const NOTE_PDATAMASK ideal-int
pkg syscall (netbsd-arm64), const NOTE_RENAME = 32
pkg syscall (netbsd-arm64), const NOTE_RENAME ideal-int
pkg syscall (netbsd-arm64), const NOTE_REVOKE = 64
pkg syscall (netbsd-arm64), const NOTE_REVOKE ideal-int
pkg syscall (netbsd-arm64), const NOTE_TRACK = 1
pkg syscall (netbsd-arm64), const NOTE_TRACKERR = 2
pkg syscall (netbsd-arm64), const NOTE_TRACKERR ideal-int
pkg syscall (netbsd-arm64), const NOTE_TRACK ideal-int
pkg syscall (netbsd-arm64), const NOTE_WRITE = 2
pkg syscall (netbsd-arm64), const NOTE_WRITE ideal-int
pkg syscall (netbsd-arm64), const O_ACCMODE = 3
pkg syscall (netbsd-arm64), const O_ACCMODE ideal-int
pkg syscall (netbsd-arm64), const O_ALT_IO = 262144
pkg syscall (netbsd-arm64), const O_ALT_IO ideal-int
pkg syscall (netbsd-arm64), const O_APPEND = 8
pkg syscall (netbsd-arm64), const O_ASYNC = 64
pkg syscall (netbsd-arm64), const O_CLOEXEC = 4194304
pkg syscall (netbsd-arm64), const O_CREAT = 512
pkg syscall (netbsd-arm64), const OCRNL = 16
pkg syscall (netbsd-arm64), const OCRNL ideal-int
pkg syscall (netbsd-arm64), const O_DIRECT = 524288
pkg syscall (netbsd-arm64), const O_DIRECT ideal-int
pkg syscall (netbsd-arm64), const O_DIRECTORY = 2097152
pkg syscall (netbsd-arm64), const O_DIRECTORY ideal-int
pkg syscall (netbsd-arm64), const O_DSYNC = 65536
pkg syscall (netbsd-arm64), const O_DSYNC ideal-int
pkg syscall (netbsd-arm64), const O_EXCL = 2048
pkg syscall (netbsd-arm64), const O_EXLOCK = 32
pkg syscall (netbsd-arm64), const O_EXLOCK ideal-int
pkg syscall (netbsd-arm64), const OFIOGETBMAP = 3221513850
pkg syscall (netbsd-arm64), const OFIOGETBMAP ideal-int
pkg syscall (netbsd-arm64), const O_FSYNC = 128
pkg syscall (netbsd-arm64), const O_FSYNC ideal-int
pkg syscall (netbsd-arm64), const O_NDELAY = 4
pkg syscall (netbsd-arm64), const O_NDELAY ideal-int
pkg syscall (netbsd-arm64), const ONLCR = 2
pkg syscall (netbsd-arm64), const ONLCR ideal-int
pkg syscall (netbsd-arm64), const ONLRET = 64
pkg syscall (netbsd-arm64), const ONLRET ideal-int
pkg syscall (netbsd-arm64), const ONOCR = 32
pkg syscall (netbsd-arm64), const ONOCR ideal-int
pkg syscall (netbsd-arm64), const O_NOCTTY = 32768
pkg syscall (netbsd-arm64), const ONOEOT = 8
pkg syscall (netbsd-arm64), const ONOEOT ideal-int
pkg syscall (netbsd-arm64), const O_NOFOLLOW = 256
pkg syscall (netbsd-arm64), const O_NOFOLLOW ideal-int
pkg syscall (netbsd-arm64), const O_NONBLOCK = 4
pkg syscall (netbsd-arm64), const O_NOSIGPIPE = 16777216
pkg syscall (netbsd-arm64), const O_NOSIGPIPE ideal-int
pkg syscall (netbsd-arm64), const OPOST = 1
pkg syscall (netbsd-arm64), const OPOST ideal-int
pkg syscall (netbsd-arm64), const O_RSYNC = 131072
pkg syscall (netbsd-arm64), const O_RSYNC ideal-int
pkg syscall (netbsd-arm64), const O_SHLOCK = 16
pkg syscall (netbsd-arm64), const O_SHLOCK ideal-int
pkg syscall (netbsd-arm64), const O_SYNC = 128
pkg syscall (netbsd-arm64), const O_TRUNC = 1024
pkg syscall (netbsd-arm64), const PARENB = 4096
pkg syscall (netbsd-arm64), const PARENB ideal-int
pkg syscall (netbsd-arm64), const PARMRK = 8
pkg syscall (netbsd-arm64), const PARMRK ideal-int
pkg syscall (netbsd-arm64), const PARODD = 8192
pkg syscall (netbsd-arm64), const PARODD ideal-int
pkg syscall (netbsd-arm64), const PENDIN = 536870912
pkg syscall (netbsd-arm64), const PENDIN ideal-int
pkg syscall (netbsd-arm64), const PRI_IOFLUSH = 124
pkg syscall (netbsd-arm64), const PRI_IOFLUSH ideal-int
pkg syscall (netbsd-arm64), const PRIO_PGRP = 1
pkg syscall (netbsd-arm64), const PRIO_PGRP ideal-int
pkg syscall (netbsd-arm64), const PRIO_PROCESS = 0
pkg syscall (netbsd-arm64), const PRIO_PROCESS ideal-int
pkg syscall (netbsd-arm64), const PRIO_USER = 2
pkg syscall (netbsd-arm64), const PRIO_USER ideal-int
pkg syscall (netbsd-arm64), const PROT_EXEC = 4
pkg syscall (netbsd-arm64), const PROT_EXEC ideal-int
pkg syscall (netbsd-arm64), const PROT_NONE = 0
pkg syscall (netbsd-arm64), const PROT_NONE ideal-int
pkg syscall (netbsd-arm64), const PROT_READ = 1
pkg syscall (netbsd-arm64), const PROT_READ ideal-int
pkg syscall (netbsd-arm64), const PROT_WRITE = 2
pkg syscall (netbsd-arm64), const PROT_WRITE ideal-int
pkg syscall (netbsd-arm64), const PTRACE_CONT = 7
pkg syscall (netbsd-arm64), const PTRACE_CONT ideal-int
pkg syscall (netbsd-arm64), const PTRACE_KILL = 8
pkg syscall (netbsd-arm64), const PTRACE_KILL ideal-int
pkg syscall (netbsd-arm64), const PTRACE_TRACEME = 0
pkg syscall (netbsd-arm64), const PTRACE_TRACEME ideal-int
pkg syscall (netbsd-arm64), const RLIM_INFINITY = 9223372036854775807
pkg syscall (netbsd-arm64), const RLIM_INFINITY ideal-int
pkg syscall (netbsd-arm64), const RLIMIT_AS = 10
pkg syscall (netbsd-arm64), const RLIMIT_AS ideal-int
pkg syscall (netbsd-arm64), const RLIMIT_CORE = 4
pkg syscall (netbsd-arm64), const RLIMIT_CORE ideal-int
pkg syscall (netbsd-arm64), const RLIMIT_CPU = 0
pkg syscall (netbsd-arm64), const RLIMIT_CPU ideal-int
pkg syscall (netbsd-arm64), const RLIMIT_DATA = 2
pkg syscall (netbsd-arm64), const RLIMIT_DATA ideal-int
pkg syscall (netbsd-arm64), const RLIMIT_FSIZE = 1
pkg syscall (netbsd-arm64), const RLIMIT_FSIZE ideal-int
pkg syscall (netbsd-arm64), const RLIMIT_NOFILE = 8
pkg syscall (netbsd-arm64), const RLIMIT_NOFILE ideal-int
pkg syscall (netbsd-arm64), const RLIMIT_STACK = 3
pkg syscall (netbsd-arm64), const RLIMIT_STACK ideal-int
pkg syscall (netbsd-arm64), const RTA_AUTHOR = 64
pkg syscall (netbsd-arm64), const RTA_AUTHOR ideal-int
pkg syscall (netbsd-arm64), const RTA_BRD = 128
pkg syscall (netbsd-arm64), const RTA_BRD ideal-int
pkg syscall (netbsd-arm64), const RTA_DST = 1
pkg syscall (netbsd-arm64), const RTA_DST ideal-int
pkg syscall (netbsd-arm64), const RTA_GATEWAY = 2
pkg syscall (netbsd-arm64), const RTA_GATEWAY ideal-int
pkg syscall (netbsd-arm64), const RTA_GENMASK = 8
pkg syscall (netbsd-arm64), const RTA_GENMASK ideal-int
pkg syscall (netbsd-arm64), const RTA_IFA = 32
pkg syscall (netbsd-arm64), const RTA_IFA ideal-int
pkg syscall (netbsd-arm64), const RTA_IFP = 16
pkg syscall (netbsd-arm64), const RTA_IFP ideal-int
pkg syscall (netbsd-arm64), const RTA_NETMASK = 4
pkg syscall (netbsd-arm64), const RTA_NETMASK ideal-int
pkg syscall (netbsd-arm64), const RTA_TAG = 256
pkg syscall (netbsd-arm64), const RTA_TAG ideal-int
pkg syscall (netbsd-arm64), const RTAX_AUTHOR = 6
pkg syscall (netbsd-arm64), const RTAX_AUTHOR ideal-int
pkg syscall (netbsd-arm64), const RTAX_BRD = 7
pkg syscall (netbsd-arm64), const RTAX_BRD ideal-int
pkg syscall (netbsd-arm64), const RTAX_DST = 0
pkg syscall (netbsd-arm64), const RTAX_DST ideal-int
pkg syscall (netbsd-arm64), const RTAX_GATEWAY = 1
pkg syscall (netbsd-arm64), const RTAX_GATEWAY ideal-int
pkg syscall (netbsd-arm64), const RTAX_GENMASK = 3
pkg syscall (netbsd-arm64), const RTAX_GENMASK ideal-int
pkg syscall (netbsd-arm64), const RTAX_IFA = 5
pkg syscall (netbsd-arm64), const RTAX_IFA ideal-int
pkg syscall (netbsd-arm64), const RTAX_IFP = 4
pkg syscall (netbsd-arm64), const RTAX_IFP ideal-int
pkg syscall (netbsd-arm64), const RTAX_MAX = 9
pkg syscall (netbsd-arm64), const RTAX_MAX ideal-int
pkg syscall (netbsd-arm64), const RTAX_NETMASK = 2
pkg syscall (netbsd-arm64), const RTAX_NETMASK ideal-int
pkg syscall (netbsd-arm64), const RTAX_TAG = 8
pkg syscall (netbsd-arm64), const RTAX_TAG ideal-int
pkg syscall (netbsd-arm64), const RTF_ANNOUNCE = 131072
pkg syscall (netbsd-arm64), const RTF_ANNOUNCE ideal-int
pkg syscall (netbsd-arm64), const RTF_BLACKHOLE = 4096
pkg syscall (netbsd-arm64), const RTF_BLACKHOLE ideal-int
pkg syscall (netbsd-arm64), const RTF_CLONED = 8192
pkg syscall (netbsd-arm64), const RTF_CLONED ideal-int
pkg syscall (netbsd-arm64), const RTF_CLONING = 256
pkg syscall (netbsd-arm64), const RTF_CLONING ideal-int
pkg syscall (netbsd-arm64), const RTF_DONE = 64
pkg syscall (netbsd-arm64), const RTF_DONE ideal-int
pkg syscall (netbsd-arm64), const RTF_DYNAMIC = 16
pkg syscall (netbsd-arm64), const RTF_DYNAMIC ideal-int
pkg syscall (netbsd-arm64), const RTF_GATEWAY = 2
pkg syscall (netbsd-arm64), const RTF_GATEWAY ideal-int
pkg syscall (netbsd-arm64), const RTF_HOST = 4
pkg syscall (netbsd-arm64), const RTF_HOST ideal-int
pkg syscall (netbsd-arm64), const RTF_LLINFO = 1024
pkg syscall (netbsd-arm64), const RTF_LLINFO ideal-int
pkg syscall (netbsd-arm64), const RTF_MASK = 128
pkg syscall (netbsd-arm64), const RTF_MASK ideal-int
pkg syscall (netbsd-arm64), const RTF_MODIFIED = 32
pkg syscall (netbsd-arm64), const RTF_MODIFIED ideal-int
pkg syscall (netbsd-arm64), const RTF_PROTO1 = 32768
pkg syscall (netbsd-arm64), const RTF_PROTO1 ideal-int
pkg syscall (netbsd-arm64), const RTF_PROTO2 = 16384
pkg syscall (netbsd-arm64), const RTF_PROTO2 ideal-int
pkg syscall (netbsd-arm64), const RTF_REJECT = 8
pkg syscall (netbsd-arm64), const RTF_REJECT ideal-int
pkg syscall (netbsd-arm64), const RTF_SRC = 65536
pkg syscall (netbsd-arm64), const RTF_SRC ideal-int
pkg syscall (netbsd-arm64), const RTF_STATIC = 2048
pkg syscall (netbsd-arm64), const RTF_STATIC ideal-int
pkg syscall (netbsd-arm64), const RTF_UP = 1
pkg syscall (netbsd-arm64), const RTF_UP ideal-int
pkg syscall (netbsd-arm64), const RTF_XRESOLVE = 512
pkg syscall (netbsd-arm64), const RTF_XRESOLVE ideal-int
pkg syscall (netbsd-arm64), const RTM_ADD = 1
pkg syscall (netbsd-arm64), const RTM_ADD ideal-int
pkg syscall (netbsd-arm64), const RTM_CHANGE = 3
pkg syscall (netbsd-arm64), const RTM_CHANGE ideal-int
pkg syscall (netbsd-arm64), const RTM_CHGADDR = 21
pkg syscall (netbsd-arm64), const RTM_CHGADDR ideal-int
pkg syscall (netbsd-arm64), const RTM_DELADDR = 13
pkg syscall (netbsd-arm64), const RTM_DELADDR ideal-int
pkg syscall (netbsd-arm64), const RTM_DELETE = 2
pkg syscall (netbsd-arm64), const RTM_DELETE ideal-int
pkg syscall (netbsd-arm64), const RTM_GET = 4
pkg syscall (netbsd-arm64), const RTM_GET ideal-int
pkg syscall (netbsd-arm64), const RTM_IEEE80211 = 17
pkg syscall (netbsd-arm64), const RTM_IEEE80211 ideal-int
pkg syscall (netbsd-arm64), const RTM_IFANNOUNCE = 16
pkg syscall (netbsd-arm64), const RTM_IFANNOUNCE ideal-int
pkg syscall (netbsd-arm64), const RTM_IFINFO = 20
pkg syscall (netbsd-arm64), const RTM_IFINFO ideal-int
pkg syscall (netbsd-arm64), const RTM_LLINFO_UPD = 19
pkg syscall (netbsd-arm64), const RTM_LLINFO_UPD ideal-int
pkg syscall (netbsd-arm64), const RTM_LOCK = 8
pkg syscall (netbsd-arm64), const RTM_LOCK ideal-int
pkg syscall (netbsd-arm64), const RTM_LOSING = 5
pkg syscall (netbsd-arm64), const RTM_LOSING ideal-int
pkg syscall (netbsd-arm64), const RTM_MISS = 7
pkg syscall (netbsd-arm64), const RTM_MISS ideal-int
pkg syscall (netbsd-arm64), const RTM_NEWADDR = 12
pkg syscall (netbsd-arm64), const RTM_NEWADDR ideal-int
pkg syscall (netbsd-arm64), const RTM_OIFINFO = 15
pkg syscall (netbsd-arm64), const RTM_OIFINFO ideal-int
pkg syscall (netbsd-arm64), const RTM_OLDADD = 9
pkg syscall (netbsd-arm64), const RTM_OLDADD ideal-int
pkg syscall (netbsd-arm64), const RTM_OLDDEL = 10
pkg syscall (netbsd-arm64), const RTM_OLDDEL ideal-int
pkg syscall (netbsd-arm64), const RTM_OOIFINFO = 14
pkg syscall (netbsd-arm64), const RTM_OOIFINFO ideal-int
pkg syscall (netbsd-arm64), const RTM_REDIRECT = 6
pkg syscall (netbsd-arm64), const RTM_REDIRECT ideal-int
pkg syscall (netbsd-arm64), const RTM_RESOLVE = 11
pkg syscall (netbsd-arm64), const RTM_RESOLVE ideal-int
pkg syscall (netbsd-arm64), const RTM_RTTUNIT = 1000000
pkg syscall (netbsd-arm64), const RTM_RTTUNIT ideal-int
pkg syscall (netbsd-arm64), const RTM_SETGATE = 18
pkg syscall (netbsd-arm64), const RTM_SETGATE ideal-int
pkg syscall (netbsd-arm64), const RTM_VERSION = 4
pkg syscall (netbsd-arm64), const RTM_VERSION ideal-int
pkg syscall (netbsd-arm64), const RTV_EXPIRE = 4
pkg syscall (netbsd-arm64), const RTV_EXPIRE ideal-int
pkg syscall (netbsd-arm64), const RTV_HOPCOUNT = 2
pkg syscall (netbsd-arm64), const RTV_HOPCOUNT ideal-int
pkg syscall (netbsd-arm64), const RTV_MTU = 1
pkg syscall (netbsd-arm64), const RTV_MTU ideal-int
pkg syscall (netbsd-arm64), const RTV_RPIPE = 8
pkg syscall (netbsd-arm64), const RTV_RPIPE ideal-int
pkg syscall (netbsd-arm64), const RTV_RTT = 64
pkg syscall (netbsd-arm64), const RTV_RTT ideal-int
pkg syscall (netbsd-arm64), const RTV_RTTVAR = 128
pkg syscall (netbsd-arm64), const RTV_RTTVAR ideal-int
pkg syscall (netbsd-arm64), const RTV_SPIPE = 16
pkg syscall (netbsd-arm64), const RTV_SPIPE ideal-int
pkg syscall (netbsd-arm64), const RTV_SSTHRESH = 32
pkg syscall (netbsd-arm64), const RTV_SSTHRESH ideal-int
pkg syscall (netbsd-arm64), const RUSAGE_CHILDREN = -1
pkg syscall (netbsd-arm64), const RUSAGE_CHILDREN ideal-int
pkg syscall (netbsd-arm64), const RUSAGE_SELF = 0
pkg syscall (netbsd-arm64), const RUSAGE_SELF ideal-int
pkg syscall (netbsd-arm64), const S_ARCH1 = 65536
pkg syscall (netbsd-arm64), const S_ARCH1 ideal-int
pkg syscall (netbsd-arm64), const S_ARCH2 = 131072
pkg syscall (netbsd-arm64), const S_ARCH2 ideal-int
pkg syscall (netbsd-arm64), const S_BLKSIZE = 512
pkg syscall (netbsd-arm64), const S_BLKSIZE ideal-int
pkg syscall (netbsd-arm64), const SCM_CREDS = 4
pkg syscall (netbsd-arm64), const SCM_CREDS ideal-int
pkg syscall (netbsd-arm64), const SCM_RIGHTS = 1
pkg syscall (netbsd-arm64), const SCM_RIGHTS ideal-int
pkg syscall (netbsd-arm64), const SCM_TIMESTAMP = 8
pkg syscall (netbsd-arm64), const SCM_TIMESTAMP ideal-int
pkg syscall (netbsd-arm64), const S_IEXEC = 64
pkg syscall (netbsd-arm64), const S_IEXEC ideal-int
pkg syscall (netbsd-arm64), const S_IFMT = 61440
pkg syscall (netbsd-arm64), const S_IFWHT = 57344
pkg syscall (netbsd-arm64), const S_IFWHT ideal-int
pkg syscall (netbsd-arm64), const SIGBUS = 10
pkg syscall (netbsd-arm64), const SIGCHLD = 20
pkg syscall (netbsd-arm64), const SIGCHLD Signal
pkg syscall (netbsd-arm64), const SIGCONT = 19
pkg syscall (netbsd-arm64), const SIGCONT Signal
pkg syscall (netbsd-arm64), const SIGEMT = 7
pkg syscall (netbsd-arm64), const SIGEMT Signal
pkg syscall (netbsd-arm64), const SIGINFO = 29
pkg syscall (netbsd-arm64), const SIGINFO Signal
pkg syscall (netbsd-arm64), const SIGIO = 23
pkg syscall (netbsd-arm64), const SIGIO Signal
pkg syscall (netbsd-arm64), const SIGIOT = 6
pkg syscall (netbsd-arm64), const SIGIOT Signal
pkg syscall (netbsd-arm64), const SIGPROF = 27
pkg syscall (netbsd-arm64), const SIGPROF Signal
pkg syscall (netbsd-arm64), const SIGPWR = 32
pkg syscall (netbsd-arm64), const SIGPWR Signal
pkg syscall (netbsd-arm64), const SIGSTOP = 17
pkg syscall (netbsd-arm64), const SIGSTOP Signal
pkg syscall (netbsd-arm64), const SIGSYS = 12
pkg syscall (netbsd-arm64), const SIGSYS Signal
pkg syscall (netbsd-arm64), const SIGTSTP = 18
pkg syscall (netbsd-arm64), const SIGTSTP Signal
pkg syscall (netbsd-arm64), const SIGTTIN = 21
pkg syscall (netbsd-arm64), const SIGTTIN Signal
pkg syscall (netbsd-arm64), const SIGTTOU = 22
pkg syscall (netbsd-arm64), const SIGTTOU Signal
pkg syscall (netbsd-arm64), const SIGURG = 16
pkg syscall (netbsd-arm64), const SIGURG Signal
pkg syscall (netbsd-arm64), const SIGUSR1 = 30
pkg syscall (netbsd-arm64), const SIGUSR1 Signal
pkg syscall (netbsd-arm64), const SIGUSR2 = 31
pkg syscall (netbsd-arm64), const SIGUSR2 Signal
pkg syscall (netbsd-arm64), const SIGVTALRM = 26
pkg syscall (netbsd-arm64), const SIGVTALRM Signal
pkg syscall (netbsd-arm64), const SIGWINCH = 28
pkg syscall (netbsd-arm64), const SIGWINCH Signal
pkg syscall (netbsd-arm64), const SIGXCPU = 24
pkg syscall (netbsd-arm64), const SIGXCPU Signal
pkg syscall (netbsd-arm64), const SIGXFSZ = 25
pkg syscall (netbsd-arm64), const SIGXFSZ Signal
pkg syscall (netbsd-arm64), const SIOCADDMULTI = 2156947761
pkg syscall (netbsd-arm64), const SIOCADDMULTI ideal-int
pkg syscall (netbsd-arm64), const SIOCADDRT = 2151182858
pkg syscall (netbsd-arm64), const SIOCADDRT ideal-int
pkg syscall (netbsd-arm64), const SIOCAIFADDR = 2151704858
pkg syscall (netbsd-arm64), const SIOCAIFADDR ideal-int
pkg syscall (netbsd-arm64), const SIOCALIFADDR = 2165860636
pkg syscall (netbsd-arm64), const SIOCALIFADDR ideal-int
pkg syscall (netbsd-arm64), const SIOCATMARK = 1074033415
pkg syscall (netbsd-arm64), const SIOCATMARK ideal-int
pkg syscall (netbsd-arm64), const SIOCDELMULTI = 2156947762
pkg syscall (netbsd-arm64), const SIOCDELMULTI ideal-int
pkg syscall (netbsd-arm64), const SIOCDELRT = 2151182859
pkg syscall (netbsd-arm64), const SIOCDELRT ideal-int
pkg syscall (netbsd-arm64), const SIOCDIFADDR = 2156947737
pkg syscall (netbsd-arm64), const SIOCDIFADDR ideal-int
pkg syscall (netbsd-arm64), const SIOCDIFPHYADDR = 2156947785
pkg syscall (netbsd-arm64), const SIOCDIFPHYADDR ideal-int
pkg syscall (netbsd-arm64), const SIOCDLIFADDR = 2165860638
pkg syscall (netbsd-arm64), const SIOCDLIFADDR ideal-int
pkg syscall (netbsd-arm64), const SIOCGDRVSPEC = 3223873915
pkg syscall (netbsd-arm64), const SIOCGDRVSPEC ideal-int
pkg syscall (netbsd-arm64), const SIOCGETPFSYNC = 3230689784
pkg syscall (netbsd-arm64), const SIOCGETPFSYNC ideal-int
pkg syscall (netbsd-arm64), const SIOCGETSGCNT = 3223352628
pkg syscall (netbsd-arm64), const SIOCGETSGCNT ideal-int
pkg syscall (netbsd-arm64), const SIOCGETVIFCNT = 3223876915
pkg syscall (netbsd-arm64), const SIOCGETVIFCNT ideal-int
pkg syscall (netbsd-arm64), const SIOCGHIWAT = 1074033409
pkg syscall (netbsd-arm64), const SIOCGHIWAT ideal-int
pkg syscall (netbsd-arm64), const SIOCGIFADDR = 3230689569
pkg syscall (netbsd-arm64), const SIOCGIFADDR ideal-int
pkg syscall (netbsd-arm64), const SIOCGIFADDRPREF = 3231213856
pkg syscall (netbsd-arm64), const SIOCGIFADDRPREF ideal-int
pkg syscall (netbsd-arm64), const SIOCGIFALIAS = 3225446683
pkg syscall (netbsd-arm64), const SIOCGIFALIAS ideal-int
pkg syscall (netbsd-arm64), const SIOCGIFBRDADDR = 3230689571
pkg syscall (netbsd-arm64), const SIOCGIFBRDADDR ideal-int
pkg syscall (netbsd-arm64), const SIOCGIFCAP = 3223349622
pkg syscall (netbsd-arm64), const SIOCGIFCAP ideal-int
pkg syscall (netbsd-arm64), const SIOCGIFCONF = 3222300966
pkg syscall (netbsd-arm64), const SIOCGIFCONF ideal-int
pkg syscall (netbsd-arm64), const SIOCGIFDATA = 3231213957
pkg syscall (netbsd-arm64), const SIOCGIFDATA ideal-int
pkg syscall (netbsd-arm64), const SIOCGIFDLT = 3230689655
pkg syscall (netbsd-arm64), const SIOCGIFDLT ideal-int
pkg syscall (netbsd-arm64), const SIOCGIFDSTADDR = 3230689570
pkg syscall (netbsd-arm64), const SIOCGIFDSTADDR ideal-int
pkg syscall (netbsd-arm64), const SIOCGIFFLAGS = 3230689553
pkg syscall (netbsd-arm64), const SIOCGIFFLAGS ideal-int
pkg syscall (netbsd-arm64), const SIOCGIFGENERIC = 3230689594
pkg syscall (netbsd-arm64), const SIOCGIFGENERIC ideal-int
pkg syscall (netbsd-arm64), const SIOCGIFMEDIA = 3224398134
pkg syscall (netbsd-arm64), const SIOCGIFMEDIA ideal-int
pkg syscall (netbsd-arm64), const SIOCGIFMETRIC = 3230689559
pkg syscall (netbsd-arm64), const SIOCGIFMETRIC ideal-int
pkg syscall (netbsd-arm64), const SIOCGIFMTU = 3230689662
pkg syscall (netbsd-arm64), const SIOCGIFMTU ideal-int
pkg syscall (netbsd-arm64), const SIOCGIFNETMASK = 3230689573
pkg syscall (netbsd-arm64), const SIOCGIFNETMASK ideal-int
pkg syscall (netbsd-arm64), const SIOCGIFPDSTADDR = 3230689608
pkg syscall (netbsd-arm64), const SIOCGIFPDSTADDR ideal-int
pkg syscall (netbsd-arm64), const SIOCGIFPSRCADDR = 3230689607
pkg syscall (netbsd-arm64), const SIOCGIFPSRCADDR ideal-int
pkg syscall (netbsd-arm64), const SIOCGLIFADDR = 3239602461
pkg syscall (netbsd-arm64), const SIOCGLIFADDR ideal-int
pkg syscall (netbsd-arm64), const SIOCGLIFPHYADDR = 3239602507
pkg syscall (netbsd-arm64), const SIOCGLIFPHYADDR ideal-int
pkg syscall (netbsd-arm64), const SIOCGLINKSTR = 3223873927
pkg syscall (netbsd-arm64), const SIOCGLINKSTR ideal-int
pkg syscall (netbsd-arm64), const SIOCGLOWAT = 1074033411
pkg syscall (netbsd-arm64), const SIOCGLOWAT ideal-int
pkg syscall (netbsd-arm64), const SIOCGPGRP = 1074033417
pkg syscall (netbsd-arm64), const SIOCGPGRP ideal-int
pkg syscall (netbsd-arm64), const SIOCGVH = 3230689667
pkg syscall (netbsd-arm64), const SIOCGVH ideal-int
pkg syscall (netbsd-arm64), const SIOCIFCREATE = 2156947834
pkg syscall (netbsd-arm64), const SIOCIFCREATE ideal-int
pkg syscall (netbsd-arm64), const SIOCIFDESTROY = 2156947833
pkg syscall (netbsd-arm64), const SIOCIFDESTROY ideal-int
pkg syscall (netbsd-arm64), const SIOCIFGCLONERS = 3222301048
pkg syscall (netbsd-arm64), const SIOCIFGCLONERS ideal-int
pkg syscall (netbsd-arm64), const SIOCINITIFADDR = 3228592516
pkg syscall (netbsd-arm64), const SIOCINITIFADDR ideal-int
pkg syscall (netbsd-arm64), const SIOCSDRVSPEC = 2150132091
pkg syscall (netbsd-arm64), const SIOCSDRVSPEC ideal-int
pkg syscall (netbsd-arm64), const SIOCSETPFSYNC = 2156947959
pkg syscall (netbsd-arm64), const SIOCSETPFSYNC ideal-int
pkg syscall (netbsd-arm64), const SIOCSHIWAT = 2147775232
pkg syscall (netbsd-arm64), const SIOCSHIWAT ideal-int
pkg syscall (netbsd-arm64), const SIOCSIFADDR = 2156947724
pkg syscall (netbsd-arm64), const SIOCSIFADDR ideal-int
pkg syscall (netbsd-arm64), const SIOCSIFADDRPREF = 2157472031
pkg syscall (netbsd-arm64), const SIOCSIFADDRPREF ideal-int
pkg syscall (netbsd-arm64), const SIOCSIFBRDADDR = 2156947731
pkg syscall (netbsd-arm64), const SIOCSIFBRDADDR ideal-int
pkg syscall (netbsd-arm64), const SIOCSIFCAP = 2149607797
pkg syscall (netbsd-arm64), const SIOCSIFCAP ideal-int
pkg syscall (netbsd-arm64), const SIOCSIFDSTADDR = 2156947726
pkg syscall (netbsd-arm64), const SIOCSIFDSTADDR ideal-int
pkg syscall (netbsd-arm64), const SIOCSIFFLAGS = 2156947728
pkg syscall (netbsd-arm64), const SIOCSIFFLAGS ideal-int
pkg syscall (netbsd-arm64), const SIOCSIFGENERIC = 2156947769
pkg syscall (netbsd-arm64), const SIOCSIFGENERIC ideal-int
pkg syscall (netbsd-arm64), const SIOCSIFMEDIA = 3230689589
pkg syscall (netbsd-arm64), const SIOCSIFMEDIA ideal-int
pkg syscall (netbsd-arm64), const SIOCSIFMETRIC = 2156947736
pkg syscall (netbsd-arm64), const SIOCSIFMETRIC ideal-int
pkg syscall (netbsd-arm64), const SIOCSIFMTU = 2156947839
pkg syscall (netbsd-arm64), const SIOCSIFMTU ideal-int
pkg syscall (netbsd-arm64), const SIOCSIFNETMASK = 2156947734
pkg syscall (netbsd-arm64), const SIOCSIFNETMASK ideal-int
pkg syscall (netbsd-arm64), const SIOCSIFPHYADDR = 2151704902
pkg syscall (netbsd-arm64), const SIOCSIFPHYADDR ideal-int
pkg syscall (netbsd-arm64), const SIOCSLIFPHYADDR = 2165860682
pkg syscall (netbsd-arm64), const SIOCSLIFPHYADDR ideal-int
pkg syscall (netbsd-arm64), const SIOCSLINKSTR = 2150132104
pkg syscall (netbsd-arm64), const SIOCSLINKSTR ideal-int
pkg syscall (netbsd-arm64), const SIOCSLOWAT = 2147775234
pkg syscall (netbsd-arm64), const SIOCSLOWAT ideal-int
pkg syscall (netbsd-arm64), const SIOCSPGRP = 2147775240
pkg syscall (netbsd-arm64), const SIOCSPGRP ideal-int
pkg syscall (netbsd-arm64), const SIOCSVH = 3230689666
pkg syscall (netbsd-arm64), const SIOCSVH ideal-int
pkg syscall (netbsd-arm64), const SIOCZIFDATA = 3231213958
pkg syscall (netbsd-arm64), const SIOCZIFDATA ideal-int
pkg syscall (netbsd-arm64), const S_IREAD = 256
pkg syscall (netbsd-arm64), const S_IREAD ideal-int
pkg syscall (netbsd-arm64), const S_IRGRP = 32
pkg syscall (netbsd-arm64), const S_IRGRP ideal-int
pkg syscall (netbsd-arm64), const S_IROTH = 4
pkg syscall (netbsd-arm64), const S_IROTH ideal-int
pkg syscall (netbsd-arm64), const S_IRWXG = 56
pkg syscall (netbsd-arm64), const S_IRWXG ideal-int
pkg syscall (netbsd-arm64), const S_IRWXO = 7
pkg syscall (netbsd-arm64), const S_IRWXO ideal-int
pkg syscall (netbsd-arm64), const S_IRWXU = 448
pkg syscall (netbsd-arm64), const S_IRWXU ideal-int
pkg syscall (netbsd-arm64), const S_ISTXT = 512
pkg syscall (netbsd-arm64), const S_ISTXT ideal-int
pkg syscall (netbsd-arm64), const S_IWGRP = 16
pkg syscall (netbsd-arm64), const S_IWGRP ideal-int
pkg syscall (netbsd-arm64), const S_IWOTH = 2
pkg syscall (netbsd-arm64), const S_IWOTH ideal-int
pkg syscall (netbsd-arm64), const S_IWRITE = 128
pkg syscall (netbsd-arm64), const S_IWRITE ideal-int
pkg syscall (netbsd-arm64), const S_IXGRP = 8
pkg syscall (netbsd-arm64), const S_IXGRP ideal-int
pkg syscall (netbsd-arm64), const S_IXOTH = 1
pkg syscall (netbsd-arm64), const S_IXOTH ideal-int
pkg syscall (netbsd-arm64), const SizeofBpfHdr = 32
pkg syscall (netbsd-arm64), const SizeofBpfHdr ideal-int
pkg syscall (netbsd-arm64), const SizeofBpfInsn = 8
pkg syscall (netbsd-arm64), const SizeofBpfInsn ideal-int
pkg syscall (netbsd-arm64), const SizeofBpfProgram = 16
pkg syscall (netbsd-arm64), const SizeofBpfProgram ideal-int
pkg syscall (netbsd-arm64), const SizeofBpfStat = 128
pkg syscall (netbsd-arm64), const SizeofBpfStat ideal-int
pkg syscall (netbsd-arm64), const SizeofBpfVersion = 4
pkg syscall (netbsd-arm64), const SizeofBpfVersion ideal-int
pkg syscall (netbsd-arm64), const SizeofCmsghdr = 12
pkg syscall (netbsd-arm64), const SizeofCmsghdr ideal-int
pkg syscall (netbsd-arm64), const SizeofICMPv6Filter = 32
pkg syscall (netbsd-arm64), const SizeofICMPv6Filter ideal-int
pkg syscall (netbsd-arm64), const SizeofIfaMsghdr = 24
pkg syscall (netbsd-arm64), const SizeofIfaMsghdr ideal-int
pkg syscall (netbsd-arm64), const SizeofIfAnnounceMsghdr = 24
pkg syscall (netbsd-arm64), const SizeofIfAnnounceMsghdr ideal-int
pkg syscall (netbsd-arm64), const SizeofIfData = 136
pkg syscall (netbsd-arm64), const SizeofIfData ideal-int
pkg syscall (netbsd-arm64), const SizeofIfMsghdr = 152
pkg syscall (netbsd-arm64), const SizeofIfMsghdr ideal-int
pkg syscall (netbsd-arm64), const SizeofInet6Pktinfo = 20
pkg syscall (netbsd-arm64), const SizeofInet6Pktinfo ideal-int
pkg syscall (netbsd-arm64), const SizeofIPMreq = 8
pkg syscall (netbsd-arm64), const SizeofIPMreq ideal-int
pkg syscall (netbsd-arm64), const SizeofIPv6Mreq = 20
pkg syscall (netbsd-arm64), const SizeofIPv6Mreq ideal-int
pkg syscall (netbsd-arm64), const SizeofIPv6MTUInfo = 32
pkg syscall (netbsd-arm64), const SizeofIPv6MTUInfo ideal-int
pkg syscall (netbsd-arm64), const SizeofLinger = 8
pkg syscall (netbsd-arm64), const SizeofLinger ideal-int
pkg syscall (netbsd-arm64), const SizeofMsghdr = 48
pkg syscall (netbsd-arm64), const SizeofMsghdr ideal-int
pkg syscall (netbsd-arm64), const SizeofRtMetrics = 80
pkg syscall (netbsd-arm64), const SizeofRtMetrics ideal-int
pkg syscall (netbsd-arm64), const SizeofRtMsghdr = 120
pkg syscall (netbsd-arm64), const SizeofRtMsghdr ideal-int
pkg syscall (netbsd-arm64), const SizeofSockaddrAny = 108
pkg syscall (netbsd-arm64), const SizeofSockaddrAny ideal-int
pkg syscall (netbsd-arm64), const SizeofSockaddrDatalink = 20
pkg syscall (netbsd-arm64), const SizeofSockaddrDatalink ideal-int
pkg syscall (netbsd-arm64), const SizeofSockaddrInet4 = 16
pkg syscall (netbsd-arm64), const SizeofSockaddrInet4 ideal-int
pkg syscall (netbsd-arm64), const SizeofSockaddrInet6 = 28
pkg syscall (netbsd-arm64), const SizeofSockaddrInet6 ideal-int
pkg syscall (netbsd-arm64), const SizeofSockaddrUnix = 106
pkg syscall (netbsd-arm64), const SizeofSockaddrUnix ideal-int
pkg syscall (netbsd-arm64), const S_LOGIN_SET = 1
pkg syscall (netbsd-arm64), const S_LOGIN_SET ideal-int
pkg syscall (netbsd-arm64), const SO_ACCEPTCONN = 2
pkg syscall (netbsd-arm64), const SO_ACCEPTCONN ideal-int
pkg syscall (netbsd-arm64), const SO_ACCEPTFILTER = 4096
pkg syscall (netbsd-arm64), const SO_ACCEPTFILTER ideal-int
pkg syscall (netbsd-arm64), const SO_BROADCAST = 32
pkg syscall (netbsd-arm64), const SOCK_CLOEXEC = 268435456
pkg syscall (netbsd-arm64), const SOCK_CLOEXEC ideal-int
pkg syscall (netbsd-arm64), const SOCK_FLAGS_MASK = 4026531840
pkg syscall (netbsd-arm64), const SOCK_FLAGS_MASK ideal-int
pkg syscall (netbsd-arm64), const SOCK_NONBLOCK = 536870912
pkg syscall (netbsd-arm64), const SOCK_NONBLOCK ideal-int
pkg syscall (netbsd-arm64), const SOCK_NOSIGPIPE = 1073741824
pkg syscall (netbsd-arm64), const SOCK_NOSIGPIPE ideal-int
pkg syscall (netbsd-arm64), const SOCK_RDM = 4
pkg syscall (netbsd-arm64), const SOCK_RDM ideal-int
pkg syscall (netbsd-arm64), const SO_DEBUG = 1
pkg syscall (netbsd-arm64), const SO_DEBUG ideal-int
pkg syscall (netbsd-arm64), const SO_DONTROUTE = 16
pkg syscall (netbsd-arm64), const SO_ERROR = 4103
pkg syscall (netbsd-arm64), const SO_ERROR ideal-int
pkg syscall (netbsd-arm64), const SO_KEEPALIVE = 8
pkg syscall (netbsd-arm64), const SO_LINGER = 128
pkg syscall (netbsd-arm64), const SOL_SOCKET = 65535
pkg syscall (netbsd-arm64), const SOMAXCONN = 128
pkg syscall (netbsd-arm64), const SO_NOHEADER = 4106
pkg syscall (netbsd-arm64), const SO_NOHEADER ideal-int
pkg syscall (netbsd-arm64), const SO_NOSIGPIPE = 2048
pkg syscall (netbsd-arm64), const SO_NOSIGPIPE ideal-int
pkg syscall (netbsd-arm64), const SO_OOBINLINE = 256
pkg syscall (netbsd-arm64), const SO_OOBINLINE ideal-int
pkg syscall (netbsd-arm64), const SO_OVERFLOWED = 4105
pkg syscall (netbsd-arm64), const SO_OVERFLOWED ideal-int
pkg syscall (netbsd-arm64), const SO_RCVBUF = 4098
pkg syscall (netbsd-arm64), const SO_RCVLOWAT = 4100
pkg syscall (netbsd-arm64), const SO_RCVLOWAT ideal-int
pkg syscall (netbsd-arm64), const SO_RCVTIMEO = 4108
pkg syscall (netbsd-arm64), const SO_RCVTIMEO ideal-int
pkg syscall (netbsd-arm64), const SO_REUSEADDR = 4
pkg syscall (netbsd-arm64), const SO_REUSEPORT = 512
pkg syscall (netbsd-arm64), const SO_REUSEPORT ideal-int
pkg syscall (netbsd-arm64), const SO_SNDBUF = 4097
pkg syscall (netbsd-arm64), const SO_SNDLOWAT = 4099
pkg syscall (netbsd-arm64), const SO_SNDLOWAT ideal-int
pkg syscall (netbsd-arm64), const SO_SNDTIMEO = 4107
pkg syscall (netbsd-arm64), const SO_SNDTIMEO ideal-int
pkg syscall (netbsd-arm64), const SO_TIMESTAMP = 8192
pkg syscall (netbsd-arm64), const SO_TIMESTAMP ideal-int
pkg syscall (netbsd-arm64), const SO_TYPE = 4104
pkg syscall (netbsd-arm64), const SO_TYPE ideal-int
pkg syscall (netbsd-arm64), const SO_USELOOPBACK = 64
pkg syscall (netbsd-arm64), const SO_USELOOPBACK ideal-int
pkg syscall (netbsd-arm64), const SYS_ACCEPT = 30
pkg syscall (netbsd-arm64), const SYS_ACCEPT ideal-int
pkg syscall (netbsd-arm64), const SYS_ACCESS = 33
pkg syscall (netbsd-arm64), const SYS_ACCESS ideal-int
pkg syscall (netbsd-arm64), const SYS_ACCT = 51
pkg syscall (netbsd-arm64), const SYS_ACCT ideal-int
pkg syscall (netbsd-arm64), const SYS_ADJTIME = 421
pkg syscall (netbsd-arm64), const SYS_ADJTIME ideal-int
pkg syscall (netbsd-arm64), const SYS_BIND = 104
pkg syscall (netbsd-arm64), const SYS_BIND ideal-int
pkg syscall (netbsd-arm64), const SYS_BREAK = 17
pkg syscall (netbsd-arm64), const SYS_BREAK ideal-int
pkg syscall (netbsd-arm64), const SYS_CHDIR = 12
pkg syscall (netbsd-arm64), const SYS_CHDIR ideal-int
pkg syscall (netbsd-arm64), const SYS_CHFLAGS = 34
pkg syscall (netbsd-arm64), const SYS_CHFLAGS ideal-int
pkg syscall (netbsd-arm64), const SYS_CHMOD = 15
pkg syscall (netbsd-arm64), const SYS_CHMOD ideal-int
pkg syscall (netbsd-arm64), const SYS_CHOWN = 16
pkg syscall (netbsd-arm64), const SYS_CHOWN ideal-int
pkg syscall (netbsd-arm64), const SYS_CHROOT = 61
pkg syscall (netbsd-arm64), const SYS_CHROOT ideal-int
pkg syscall (netbsd-arm64), const SYS_CLOCK_GETRES = 429
pkg syscall (netbsd-arm64), const SYS_CLOCK_GETRES ideal-int
pkg syscall (netbsd-arm64), const SYS_CLOCK_GETTIME = 427
pkg syscall (netbsd-arm64), const SYS_CLOCK_GETTIME ideal-int
pkg syscall (netbsd-arm64), const SYS_CLOCK_SETTIME = 428
pkg syscall (netbsd-arm64), const SYS_CLOCK_SETTIME ideal-int
pkg syscall (netbsd-arm64), const SYS___CLONE = 287
pkg syscall (netbsd-arm64), const SYS___CLONE ideal-int
pkg syscall (netbsd-arm64), const SYS_CLOSE = 6
pkg syscall (netbsd-arm64), const SYS_CLOSE ideal-int
pkg syscall (netbsd-arm64), const SYS_CONNECT = 98
pkg syscall (netbsd-arm64), const SYS_CONNECT ideal-int
pkg syscall (netbsd-arm64), const SYSCTL_VERS_0 = 0
pkg syscall (netbsd-arm64), const SYSCTL_VERS_0 ideal-int
pkg syscall (netbsd-arm64), const SYSCTL_VERS_1 = 16777216
pkg syscall (netbsd-arm64), const SYSCTL_VERS_1 ideal-int
pkg syscall (netbsd-arm64), const SYSCTL_VERSION = 16777216
pkg syscall (netbsd-arm64), const SYSCTL_VERSION ideal-int
pkg syscall (netbsd-arm64), const SYSCTL_VERS_MASK = 4278190080
pkg syscall (netbsd-arm64), const SYSCTL_VERS_MASK ideal-int
pkg syscall (netbsd-arm64), const SYS_DUP2 = 90
pkg syscall (netbsd-arm64), const SYS_DUP2 ideal-int
pkg syscall (netbsd-arm64), const SYS_DUP3 = 454
pkg syscall (netbsd-arm64), const SYS_DUP3 ideal-int
pkg syscall (netbsd-arm64), const SYS_DUP = 41
pkg syscall (netbsd-arm64), const SYS_DUP ideal-int
pkg syscall (netbsd-arm64), const SYS_EXECVE = 59
pkg syscall (netbsd-arm64), const SYS_EXECVE ideal-int
pkg syscall (netbsd-arm64), const SYS_EXIT = 1
pkg syscall (netbsd-arm64), const SYS_EXIT ideal-int
pkg syscall (netbsd-arm64), const SYS_EXTATTRCTL = 360
pkg syscall (netbsd-arm64), const SYS_EXTATTRCTL ideal-int
pkg syscall (netbsd-arm64), const SYS_EXTATTR_DELETE_FD = 366
pkg syscall (netbsd-arm64), const SYS_EXTATTR_DELETE_FD ideal-int
pkg syscall (netbsd-arm64), const SYS_EXTATTR_DELETE_FILE = 363
pkg syscall (netbsd-arm64), const SYS_EXTATTR_DELETE_FILE ideal-int
pkg syscall (netbsd-arm64), const SYS_EXTATTR_DELETE_LINK = 369
pkg syscall (netbsd-arm64), const SYS_EXTATTR_DELETE_LINK ideal-int
pkg syscall (netbsd-arm64), const SYS_EXTATTR_GET_FD = 365
pkg syscall (netbsd-arm64), const SYS_EXTATTR_GET_FD ideal-int
pkg syscall (netbsd-arm64), const SYS_EXTATTR_GET_FILE = 362
pkg syscall (netbsd-arm64), const SYS_EXTATTR_GET_FILE ideal-int
pkg syscall (netbsd-arm64), const SYS_EXTATTR_GET_LINK = 368
pkg syscall (netbsd-arm64), const SYS_EXTATTR_GET_LINK ideal-int
pkg syscall (netbsd-arm64), const SYS_EXTATTR_LIST_FD = 370
pkg syscall (netbsd-arm64), const SYS_EXTATTR_LIST_FD ideal-int
pkg syscall (netbsd-arm64), const SYS_EXTATTR_LIST_FILE = 371
pkg syscall (netbsd-arm64), const SYS_EXTATTR_LIST_FILE ideal-int
pkg syscall (netbsd-arm64), const SYS_EXTATTR_LIST_LINK = 372
pkg syscall (netbsd-arm64), const SYS_EXTATTR_LIST_LINK ideal-int
pkg syscall (netbsd-arm64), const SYS_EXTATTR_SET_FD = 364
pkg syscall (netbsd-arm64), const SYS_EXTATTR_SET_FD ideal-int
pkg syscall (netbsd-arm64), const SYS_EXTATTR_SET_FILE = 361
pkg syscall (netbsd-arm64), const SYS_EXTATTR_SET_FILE ideal-int
pkg syscall (netbsd-arm64), const SYS_EXTATTR_SET_LINK = 367
pkg syscall (netbsd-arm64), const SYS_EXTATTR_SET_LINK ideal-int
pkg syscall (netbsd-arm64), const SYS_FACCESSAT = 462
pkg syscall (netbsd-arm64), const SYS_FACCESSAT ideal-int
pkg syscall (netbsd-arm64), const SYS_FCHDIR = 13
pkg syscall (netbsd-arm64), const SYS_FCHDIR ideal-int
pkg syscall (netbsd-arm64), const SYS_FCHFLAGS = 35
pkg syscall (netbsd-arm64), const SYS_FCHFLAGS ideal-int
pkg syscall (netbsd-arm64), const SYS_FCHMOD = 124
pkg syscall (netbsd-arm64), const SYS_FCHMODAT = 463
pkg syscall (netbsd-arm64), const SYS_FCHMODAT ideal-int
pkg syscall (netbsd-arm64), const SYS_FCHMOD ideal-int
pkg syscall (netbsd-arm64), const SYS_FCHOWN = 123
pkg syscall (netbsd-arm64), const SYS_FCHOWNAT = 464
pkg syscall (netbsd-arm64), const SYS_FCHOWNAT ideal-int
pkg syscall (netbsd-arm64), const SYS_FCHOWN ideal-int
pkg syscall (netbsd-arm64), const SYS_FCHROOT = 297
pkg syscall (netbsd-arm64), const SYS_FCHROOT ideal-int
pkg syscall (netbsd-arm64), const SYS_FCNTL = 92
pkg syscall (netbsd-arm64), const SYS_FCNTL ideal-int
pkg syscall (netbsd-arm64), const SYS_FDATASYNC = 241
pkg syscall (netbsd-arm64), const SYS_FDATASYNC ideal-int
pkg syscall (netbsd-arm64), const SYS_FEXECVE = 465
pkg syscall (netbsd-arm64), const SYS_FEXECVE ideal-int
pkg syscall (netbsd-arm64), const SYS_FGETXATTR = 380
pkg syscall (netbsd-arm64), const SYS_FGETXATTR ideal-int
pkg syscall (netbsd-arm64), const SYS_FHSTAT = 451
pkg syscall (netbsd-arm64), const SYS_FHSTAT ideal-int
pkg syscall (netbsd-arm64), const SYS_FKTRACE = 288
pkg syscall (netbsd-arm64), const SYS_FKTRACE ideal-int
pkg syscall (netbsd-arm64), const SYS_FLISTXATTR = 383
pkg syscall (netbsd-arm64), const SYS_FLISTXATTR ideal-int
pkg syscall (netbsd-arm64), const SYS_FLOCK = 131
pkg syscall (netbsd-arm64), const SYS_FLOCK ideal-int
pkg syscall (netbsd-arm64), const SYS_FORK = 2
pkg syscall (netbsd-arm64), const SYS_FORK ideal-int
pkg syscall (netbsd-arm64), const SYS_FPATHCONF = 192
pkg syscall (netbsd-arm64), const SYS_FPATHCONF ideal-int
pkg syscall (netbsd-arm64), const SYS_FREMOVEXATTR = 386
pkg syscall (netbsd-arm64), const SYS_FREMOVEXATTR ideal-int
pkg syscall (netbsd-arm64), const SYS_FSETXATTR = 377
pkg syscall (netbsd-arm64), const SYS_FSETXATTR ideal-int
pkg syscall (netbsd-arm64), const SYS_FSTAT = 440
pkg syscall (netbsd-arm64), const SYS_FSTATAT = 466
pkg syscall (netbsd-arm64), const SYS_FSTATAT ideal-int
pkg syscall (netbsd-arm64), const SYS_FSTAT ideal-int
pkg syscall (netbsd-arm64), const SYS_FSTATVFS1 = 358
pkg syscall (netbsd-arm64), const SYS_FSTATVFS1 ideal-int
pkg syscall (netbsd-arm64), const SYS_FSYNC = 95
pkg syscall (netbsd-arm64), const SYS_FSYNC ideal-int
pkg syscall (netbsd-arm64), const SYS_FSYNC_RANGE = 354
pkg syscall (netbsd-arm64), const SYS_FSYNC_RANGE ideal-int
pkg syscall (netbsd-arm64), const SYS_FTRUNCATE = 201
pkg syscall (netbsd-arm64), const SYS_FTRUNCATE ideal-int
pkg syscall (netbsd-arm64), const SYS_FUTIMENS = 472
pkg syscall (netbsd-arm64), const SYS_FUTIMENS ideal-int
pkg syscall (netbsd-arm64), const SYS_FUTIMES = 423
pkg syscall (netbsd-arm64), const SYS_FUTIMES ideal-int
pkg syscall (netbsd-arm64), const SYS_GETCONTEXT = 307
pkg syscall (netbsd-arm64), const SYS_GETCONTEXT ideal-int
pkg syscall (netbsd-arm64), const SYS___GETCWD = 296
pkg syscall (netbsd-arm64), const SYS___GETCWD ideal-int
pkg syscall (netbsd-arm64), const SYS_GETDENTS = 390
pkg syscall (netbsd-arm64), const SYS_GETDENTS ideal-int
pkg syscall (netbsd-arm64), const SYS_GETEGID = 43
pkg syscall (netbsd-arm64), const SYS_GETEGID ideal-int
pkg syscall (netbsd-arm64), const SYS_GETEUID = 25
pkg syscall (netbsd-arm64), const SYS_GETEUID ideal-int
pkg syscall (netbsd-arm64), const SYS_GETFH = 395
pkg syscall (netbsd-arm64), const SYS_GETFH ideal-int
pkg syscall (netbsd-arm64), const SYS_GETGID = 47
pkg syscall (netbsd-arm64), const SYS_GETGID ideal-int
pkg syscall (netbsd-arm64), const SYS_GETGROUPS = 79
pkg syscall (netbsd-arm64), const SYS_GETGROUPS ideal-int
pkg syscall (netbsd-arm64), const SYS_GETITIMER = 426
pkg syscall (netbsd-arm64), const SYS_GETITIMER ideal-int
pkg syscall (netbsd-arm64), const SYS___GETLOGIN = 49
pkg syscall (netbsd-arm64), const SYS___GETLOGIN ideal-int
pkg syscall (netbsd-arm64), const SYS_GETPEERNAME = 31
pkg syscall (netbsd-arm64), const SYS_GETPEERNAME ideal-int
pkg syscall (netbsd-arm64), const SYS_GETPGID = 207
pkg syscall (netbsd-arm64), const SYS_GETPGID ideal-int
pkg syscall (netbsd-arm64), const SYS_GETPGRP = 81
pkg syscall (netbsd-arm64), const SYS_GETPGRP ideal-int
pkg syscall (netbsd-arm64), const SYS_GETPID = 20
pkg syscall (netbsd-arm64), const SYS_GETPID ideal-int
pkg syscall (netbsd-arm64), const SYS_GETPPID = 39
pkg syscall (netbsd-arm64), const SYS_GETPPID ideal-int
pkg syscall (netbsd-arm64), const SYS_GETPRIORITY = 100
pkg syscall (netbsd-arm64), const SYS_GETPRIORITY ideal-int
pkg syscall (netbsd-arm64), const SYS_GETRLIMIT = 194
pkg syscall (netbsd-arm64), const SYS_GETRLIMIT ideal-int
pkg syscall (netbsd-arm64), const SYS_GETRUSAGE = 445
pkg syscall (netbsd-arm64), const SYS_GETRUSAGE ideal-int
pkg syscall (netbsd-arm64), const SYS_GETSID = 286
pkg syscall (netbsd-arm64), const SYS_GETSID ideal-int
pkg syscall (netbsd-arm64), const SYS_GETSOCKNAME = 32
pkg syscall (netbsd-arm64), const SYS_GETSOCKNAME ideal-int
pkg syscall (netbsd-arm64), const SYS_GETSOCKOPT = 118
pkg syscall (netbsd-arm64), const SYS_GETSOCKOPT ideal-int
pkg syscall (netbsd-arm64), const SYS_GETTIMEOFDAY = 418
pkg syscall (netbsd-arm64), const SYS_GETTIMEOFDAY ideal-int
pkg syscall (netbsd-arm64), const SYS_GETUID = 24
pkg syscall (netbsd-arm64), const SYS_GETUID ideal-int
pkg syscall (netbsd-arm64), const SYS_GETVFSSTAT = 356
pkg syscall (netbsd-arm64), const SYS_GETVFSSTAT ideal-int
pkg syscall (netbsd-arm64), const SYS_GETXATTR = 378
pkg syscall (netbsd-arm64), const SYS_GETXATTR ideal-int
pkg syscall (netbsd-arm64), const SYS_IOCTL = 54
pkg syscall (netbsd-arm64), const SYS_IOCTL ideal-int
pkg syscall (netbsd-arm64), const SYS_ISSETUGID = 305
pkg syscall (netbsd-arm64), const SYS_ISSETUGID ideal-int
pkg syscall (netbsd-arm64), const SYS_KEVENT = 435
pkg syscall (netbsd-arm64), const SYS_KEVENT ideal-int
pkg syscall (netbsd-arm64), const SYS_KILL = 37
pkg syscall (netbsd-arm64), const SYS_KILL ideal-int
pkg syscall (netbsd-arm64), const SYS_KQUEUE1 = 455
pkg syscall (netbsd-arm64), const SYS_KQUEUE1 ideal-int
pkg syscall (netbsd-arm64), const SYS_KQUEUE = 344
pkg syscall (netbsd-arm64), const SYS_KQUEUE ideal-int
pkg syscall (netbsd-arm64), const SYS_KTRACE = 45
pkg syscall (netbsd-arm64), const SYS_KTRACE ideal-int
pkg syscall (netbsd-arm64), const SYS_LCHFLAGS = 304
pkg syscall (netbsd-arm64), const SYS_LCHFLAGS ideal-int
pkg syscall (netbsd-arm64), const SYS_LCHMOD = 274
pkg syscall (netbsd-arm64), const SYS_LCHMOD ideal-int
pkg syscall (netbsd-arm64), const SYS_LCHOWN = 275
pkg syscall (netbsd-arm64), const SYS_LCHOWN ideal-int
pkg syscall (netbsd-arm64), const SYS_LGETXATTR = 379
pkg syscall (netbsd-arm64), const SYS_LGETXATTR ideal-int
pkg syscall (netbsd-arm64), const SYS_LINK = 9
pkg syscall (netbsd-arm64), const SYS_LINKAT = 457
pkg syscall (netbsd-arm64), const SYS_LINKAT ideal-int
pkg syscall (netbsd-arm64), const SYS_LINK ideal-int
pkg syscall (netbsd-arm64), const SYS_LISTEN = 106
pkg syscall (netbsd-arm64), const SYS_LISTEN ideal-int
pkg syscall (netbsd-arm64), const SYS_LISTXATTR = 381
pkg syscall (netbsd-arm64), const SYS_LISTXATTR ideal-int
pkg syscall (netbsd-arm64), const SYS_LLISTXATTR = 382
pkg syscall (netbsd-arm64), const SYS_LLISTXATTR ideal-int
pkg syscall (netbsd-arm64), const SYS_LREMOVEXATTR = 385
pkg syscall (netbsd-arm64), const SYS_LREMOVEXATTR ideal-int
pkg syscall (netbsd-arm64), const SYS_LSEEK = 199
pkg syscall (netbsd-arm64), const SYS_LSEEK ideal-int
pkg syscall (netbsd-arm64), const SYS_LSETXATTR = 376
pkg syscall (netbsd-arm64), const SYS_LSETXATTR ideal-int
pkg syscall (netbsd-arm64), const SYS_LSTAT = 441
pkg syscall (netbsd-arm64), const SYS_LSTAT ideal-int
pkg syscall (netbsd-arm64), const SYS_LUTIMES = 424
pkg syscall (netbsd-arm64), const SYS_LUTIMES ideal-int
pkg syscall (netbsd-arm64), const SYS__LWP_CONTINUE = 314
pkg syscall (netbsd-arm64), const SYS__LWP_CONTINUE ideal-int
pkg syscall (netbsd-arm64), const SYS__LWP_CREATE = 309
pkg syscall (netbsd-arm64), const SYS__LWP_CREATE ideal-int
pkg syscall (netbsd-arm64), const SYS__LWP_CTL = 325
pkg syscall (netbsd-arm64), const SYS__LWP_CTL ideal-int
pkg syscall (netbsd-arm64), const SYS__LWP_DETACH = 319
pkg syscall (netbsd-arm64), const SYS__LWP_DETACH ideal-int
pkg syscall (netbsd-arm64), const SYS__LWP_EXIT = 310
pkg syscall (netbsd-arm64), const SYS__LWP_EXIT ideal-int
pkg syscall (netbsd-arm64), const SYS__LWP_GETNAME = 324
pkg syscall (netbsd-arm64), const SYS__LWP_GETNAME ideal-int
pkg syscall (netbsd-arm64), const SYS__LWP_GETPRIVATE = 316
pkg syscall (netbsd-arm64), const SYS__LWP_GETPRIVATE ideal-int
pkg syscall (netbsd-arm64), const SYS__LWP_KILL = 318
pkg syscall (netbsd-arm64), const SYS__LWP_KILL ideal-int
pkg syscall (netbsd-arm64), const SYS__LWP_PARK = 434
pkg syscall (netbsd-arm64), const SYS__LWP_PARK ideal-int
pkg syscall (netbsd-arm64), const SYS__LWP_SELF = 311
pkg syscall (netbsd-arm64), const SYS__LWP_SELF ideal-int
pkg syscall (netbsd-arm64), const SYS__LWP_SETNAME = 323
pkg syscall (netbsd-arm64), const SYS__LWP_SETNAME ideal-int
pkg syscall (netbsd-arm64), const SYS__LWP_SETPRIVATE = 317
pkg syscall (netbsd-arm64), const SYS__LWP_SETPRIVATE ideal-int
pkg syscall (netbsd-arm64), const SYS__LWP_SUSPEND = 313
pkg syscall (netbsd-arm64), const SYS__LWP_SUSPEND ideal-int
pkg syscall (netbsd-arm64), const SYS__LWP_UNPARK = 321
pkg syscall (netbsd-arm64), const SYS__LWP_UNPARK_ALL = 322
pkg syscall (netbsd-arm64), const SYS__LWP_UNPARK_ALL ideal-int
pkg syscall (netbsd-arm64), const SYS__LWP_UNPARK ideal-int
pkg syscall (netbsd-arm64), const SYS__LWP_WAIT = 312
pkg syscall (netbsd-arm64), const SYS__LWP_WAIT ideal-int
pkg syscall (netbsd-arm64), const SYS__LWP_WAKEUP = 315
pkg syscall (netbsd-arm64), const SYS__LWP_WAKEUP ideal-int
pkg syscall (netbsd-arm64), const SYS_MADVISE = 75
pkg syscall (netbsd-arm64), const SYS_MADVISE ideal-int
pkg syscall (netbsd-arm64), const SYS_MINCORE = 78
pkg syscall (netbsd-arm64), const SYS_MINCORE ideal-int
pkg syscall (netbsd-arm64), const SYS_MINHERIT = 273
pkg syscall (netbsd-arm64), const SYS_MINHERIT ideal-int
pkg syscall (netbsd-arm64), const SYS_MKDIR = 136
pkg syscall (netbsd-arm64), const SYS_MKDIRAT = 461
pkg syscall (netbsd-arm64), const SYS_MKDIRAT ideal-int
pkg syscall (netbsd-arm64), const SYS_MKDIR ideal-int
pkg syscall (netbsd-arm64), const SYS_MKFIFO = 132
pkg syscall (netbsd-arm64), const SYS_MKFIFOAT = 459
pkg syscall (netbsd-arm64), const SYS_MKFIFOAT ideal-int
pkg syscall (netbsd-arm64), const SYS_MKFIFO ideal-int
pkg syscall (netbsd-arm64), const SYS_MKNOD = 450
pkg syscall (netbsd-arm64), const SYS_MKNODAT = 460
pkg syscall (netbsd-arm64), const SYS_MKNODAT ideal-int
pkg syscall (netbsd-arm64), const SYS_MKNOD ideal-int
pkg syscall (netbsd-arm64), const SYS_MLOCK = 203
pkg syscall (netbsd-arm64), const SYS_MLOCKALL = 242
pkg syscall (netbsd-arm64), const SYS_MLOCKALL ideal-int
pkg syscall (netbsd-arm64), const SYS_MLOCK ideal-int
pkg syscall (netbsd-arm64), const SYS_MMAP = 197
pkg syscall (netbsd-arm64), const SYS_MMAP ideal-int
pkg syscall (netbsd-arm64), const SYS_MODCTL = 246
pkg syscall (netbsd-arm64), const SYS_MODCTL ideal-int
pkg syscall (netbsd-arm64), const SYS_MOUNT = 410
pkg syscall (netbsd-arm64), const SYS_MOUNT ideal-int
pkg syscall (netbsd-arm64), const SYS_MPROTECT = 74
pkg syscall (netbsd-arm64), const SYS_MPROTECT ideal-int
pkg syscall (netbsd-arm64), const SYS_MREMAP = 411
pkg syscall (netbsd-arm64), const SYS_MREMAP ideal-int
pkg syscall (netbsd-arm64), const SYS_MSGCTL = 444
pkg syscall (netbsd-arm64), const SYS_MSGCTL ideal-int
pkg syscall (netbsd-arm64), const SYS_MSGGET = 225
pkg syscall (netbsd-arm64), const SYS_MSGGET ideal-int
pkg syscall (netbsd-arm64), const SYS_MSGRCV = 227
pkg syscall (netbsd-arm64), const SYS_MSGRCV ideal-int
pkg syscall (netbsd-arm64), const SYS_MSGSND = 226
pkg syscall (netbsd-arm64), const SYS_MSGSND ideal-int
pkg syscall (netbsd-arm64), const SYS_MUNLOCK = 204
pkg syscall (netbsd-arm64), const SYS_MUNLOCKALL = 243
pkg syscall (netbsd-arm64), const SYS_MUNLOCKALL ideal-int
pkg syscall (netbsd-arm64), const SYS_MUNLOCK ideal-int
pkg syscall (netbsd-arm64), const SYS_MUNMAP = 73
pkg syscall (netbsd-arm64), const SYS_MUNMAP ideal-int
pkg syscall (netbsd-arm64), const SYS_NANOSLEEP = 430
pkg syscall (netbsd-arm64), const SYS_NANOSLEEP ideal-int
pkg syscall (netbsd-arm64), const SYS_NTP_ADJTIME = 176
pkg syscall (netbsd-arm64), const SYS_NTP_ADJTIME ideal-int
pkg syscall (netbsd-arm64), const SYS_NTP_GETTIME = 448
pkg syscall (netbsd-arm64), const SYS_NTP_GETTIME ideal-int
pkg syscall (netbsd-arm64), const SYS_OPEN = 5
pkg syscall (netbsd-arm64), const SYS_OPENAT = 468
pkg syscall (netbsd-arm64), const SYS_OPENAT ideal-int
pkg syscall (netbsd-arm64), const SYS_OPEN ideal-int
pkg syscall (netbsd-arm64), const SYS_PACCEPT = 456
pkg syscall (netbsd-arm64), const SYS_PACCEPT ideal-int
pkg syscall (netbsd-arm64), const SYS_PATHCONF = 191
pkg syscall (netbsd-arm64), const SYS_PATHCONF ideal-int
pkg syscall (netbsd-arm64), const SYS_PIPE2 = 453
pkg syscall (netbsd-arm64), const SYS_PIPE2 ideal-int
pkg syscall (netbsd-arm64), const SYS_PIPE = 42
pkg syscall (netbsd-arm64), const SYS_PIPE ideal-int
pkg syscall (netbsd-arm64), const SYS_PMC_CONTROL = 342
pkg syscall (netbsd-arm64), const SYS_PMC_CONTROL ideal-int
pkg syscall (netbsd-arm64), const SYS_PMC_GET_INFO = 341
pkg syscall (netbsd-arm64), const SYS_PMC_GET_INFO ideal-int
pkg syscall (netbsd-arm64), const SYS_POLL = 209
pkg syscall (netbsd-arm64), const SYS_POLL ideal-int
pkg syscall (netbsd-arm64), const SYS_POLLTS = 437
pkg syscall (netbsd-arm64), const SYS_POLLTS ideal-int
pkg syscall (netbsd-arm64), const SYS___POSIX_CHOWN = 283
pkg syscall (netbsd-arm64), const SYS___POSIX_CHOWN ideal-int
pkg syscall (netbsd-arm64), const SYS_POSIX_FADVISE = 416
pkg syscall (netbsd-arm64), const SYS_POSIX_FADVISE ideal-int
pkg syscall (netbsd-arm64), const SYS___POSIX_FCHOWN = 284
pkg syscall (netbsd-arm64), const SYS___POSIX_FCHOWN ideal-int
pkg syscall (netbsd-arm64), const SYS___POSIX_LCHOWN = 285
pkg syscall (netbsd-arm64), const SYS___POSIX_LCHOWN ideal-int
pkg syscall (netbsd-arm64), const SYS___POSIX_RENAME = 270
pkg syscall (netbsd-arm64), const SYS___POSIX_RENAME ideal-int
pkg syscall (netbsd-arm64), const SYS_POSIX_SPAWN = 474
pkg syscall (netbsd-arm64), const SYS_POSIX_SPAWN ideal-int
pkg syscall (netbsd-arm64), const SYS_PREAD = 173
pkg syscall (netbsd-arm64), const SYS_PREAD ideal-int
pkg syscall (netbsd-arm64), const SYS_PREADV = 289
pkg syscall (netbsd-arm64), const SYS_PREADV ideal-int
pkg syscall (netbsd-arm64), const SYS_PROFIL = 44
pkg syscall (netbsd-arm64), const SYS_PROFIL ideal-int
pkg syscall (netbsd-arm64), const SYS_PSELECT = 436
pkg syscall (netbsd-arm64), const SYS_PSELECT ideal-int
pkg syscall (netbsd-arm64), const SYS_PSET_ASSIGN = 414
pkg syscall (netbsd-arm64), const SYS_PSET_ASSIGN ideal-int
pkg syscall (netbsd-arm64), const SYS__PSET_BIND = 415
pkg syscall (netbsd-arm64), const SYS__PSET_BIND ideal-int
pkg syscall (netbsd-arm64), const SYS_PSET_CREATE = 412
pkg syscall (netbsd-arm64), const SYS_PSET_CREATE ideal-int
pkg syscall (netbsd-arm64), const SYS_PSET_DESTROY = 413
pkg syscall (netbsd-arm64), const SYS_PSET_DESTROY ideal-int
pkg syscall (netbsd-arm64), const SYS_PTRACE = 26
pkg syscall (netbsd-arm64), const SYS_PTRACE ideal-int
pkg syscall (netbsd-arm64), const SYS_PWRITE = 174
pkg syscall (netbsd-arm64), const SYS_PWRITE ideal-int
pkg syscall (netbsd-arm64), const SYS_PWRITEV = 290
pkg syscall (netbsd-arm64), const SYS_PWRITEV ideal-int
pkg syscall (netbsd-arm64), const SYS___QUOTACTL = 473
pkg syscall (netbsd-arm64), const SYS___QUOTACTL ideal-int
pkg syscall (netbsd-arm64), const SYS_RASCTL = 343
pkg syscall (netbsd-arm64), const SYS_RASCTL ideal-int
pkg syscall (netbsd-arm64), const SYS_READ = 3
pkg syscall (netbsd-arm64), const SYS_READ ideal-int
pkg syscall (netbsd-arm64), const SYS_READLINK = 58
pkg syscall (netbsd-arm64), const SYS_READLINKAT = 469
pkg syscall (netbsd-arm64), const SYS_READLINKAT ideal-int
pkg syscall (netbsd-arm64), const SYS_READLINK ideal-int
pkg syscall (netbsd-arm64), const SYS_READV = 120
pkg syscall (netbsd-arm64), const SYS_READV ideal-int
pkg syscall (netbsd-arm64), const SYS_REBOOT = 208
pkg syscall (netbsd-arm64), const SYS_REBOOT ideal-int
pkg syscall (netbsd-arm64), const SYS_RECVFROM = 29
pkg syscall (netbsd-arm64), const SYS_RECVFROM ideal-int
pkg syscall (netbsd-arm64), const SYS_RECVMMSG = 475
pkg syscall (netbsd-arm64), const SYS_RECVMMSG ideal-int
pkg syscall (netbsd-arm64), const SYS_RECVMSG = 27
pkg syscall (netbsd-arm64), const SYS_RECVMSG ideal-int
pkg syscall (netbsd-arm64), const SYS_REMOVEXATTR = 384
pkg syscall (netbsd-arm64), const SYS_REMOVEXATTR ideal-int
pkg syscall (netbsd-arm64), const SYS_RENAME = 128
pkg syscall (netbsd-arm64), const SYS_RENAMEAT = 458
pkg syscall (netbsd-arm64), const SYS_RENAMEAT ideal-int
pkg syscall (netbsd-arm64), const SYS_RENAME ideal-int
pkg syscall (netbsd-arm64), const SYS_REVOKE = 56
pkg syscall (netbsd-arm64), const SYS_REVOKE ideal-int
pkg syscall (netbsd-arm64), const SYS_RMDIR = 137
pkg syscall (netbsd-arm64), const SYS_RMDIR ideal-int
pkg syscall (netbsd-arm64), const SYS_SBRK = 69
pkg syscall (netbsd-arm64), const SYS_SBRK ideal-int
pkg syscall (netbsd-arm64), const SYS__SCHED_GETAFFINITY = 349
pkg syscall (netbsd-arm64), const SYS__SCHED_GETAFFINITY ideal-int
pkg syscall (netbsd-arm64), const SYS__SCHED_GETPARAM = 347
pkg syscall (netbsd-arm64), const SYS__SCHED_GETPARAM ideal-int
pkg syscall (netbsd-arm64), const SYS__SCHED_SETAFFINITY = 348
pkg syscall (netbsd-arm64), const SYS__SCHED_SETAFFINITY ideal-int
pkg syscall (netbsd-arm64), const SYS__SCHED_SETPARAM = 346
pkg syscall (netbsd-arm64), const SYS__SCHED_SETPARAM ideal-int
pkg syscall (netbsd-arm64), const SYS_SCHED_YIELD = 350
pkg syscall (netbsd-arm64), const SYS_SCHED_YIELD ideal-int
pkg syscall (netbsd-arm64), const SYS_SELECT = 417
pkg syscall (netbsd-arm64), const SYS_SELECT ideal-int
pkg syscall (netbsd-arm64), const SYS_SEMCONFIG = 223
pkg syscall (netbsd-arm64), const SYS_SEMCONFIG ideal-int
pkg syscall (netbsd-arm64), const SYS___SEMCTL = 442
pkg syscall (netbsd-arm64), const SYS___SEMCTL ideal-int
pkg syscall (netbsd-arm64), const SYS_SEMGET = 221
pkg syscall (netbsd-arm64), const SYS_SEMGET ideal-int
pkg syscall (netbsd-arm64), const SYS_SEMOP = 222
pkg syscall (netbsd-arm64), const SYS_SEMOP ideal-int
pkg syscall (netbsd-arm64), const SYS_SENDMMSG = 476
pkg syscall (netbsd-arm64), const SYS_SENDMMSG ideal-int
pkg syscall (netbsd-arm64), const SYS_SENDMSG = 28
pkg syscall (netbsd-arm64), const SYS_SENDMSG ideal-int
pkg syscall (netbsd-arm64), const SYS_SENDTO = 133
pkg syscall (netbsd-arm64), const SYS_SENDTO ideal-int
pkg syscall (netbsd-arm64), const SYS_SETCONTEXT = 308
pkg syscall (netbsd-arm64), const SYS_SETCONTEXT ideal-int
pkg syscall (netbsd-arm64), const SYS_SETEGID = 182
pkg syscall (netbsd-arm64), const SYS_SETEGID ideal-int
pkg syscall (netbsd-arm64), const SYS_SETEUID = 183
pkg syscall (netbsd-arm64), const SYS_SETEUID ideal-int
pkg syscall (netbsd-arm64), const SYS_SETGID = 181
pkg syscall (netbsd-arm64), const SYS_SETGID ideal-int
pkg syscall (netbsd-arm64), const SYS_SETGROUPS = 80
pkg syscall (netbsd-arm64), const SYS_SETGROUPS ideal-int
pkg syscall (netbsd-arm64), const SYS_SETITIMER = 425
pkg syscall (netbsd-arm64), const SYS_SETITIMER ideal-int
pkg syscall (netbsd-arm64), const SYS___SETLOGIN = 50
pkg syscall (netbsd-arm64), const SYS___SETLOGIN ideal-int
pkg syscall (netbsd-arm64), const SYS_SETPGID = 82
pkg syscall (netbsd-arm64), const SYS_SETPGID ideal-int
pkg syscall (netbsd-arm64), const SYS_SETPRIORITY = 96
pkg syscall (netbsd-arm64), const SYS_SETPRIORITY ideal-int
pkg syscall (netbsd-arm64), const SYS_SETREGID = 127
pkg syscall (netbsd-arm64), const SYS_SETREGID ideal-int
pkg syscall (netbsd-arm64), const SYS_SETREUID = 126
pkg syscall (netbsd-arm64), const SYS_SETREUID ideal-int
pkg syscall (netbsd-arm64), const SYS_SETRLIMIT = 195
pkg syscall (netbsd-arm64), const SYS_SETRLIMIT ideal-int
pkg syscall (netbsd-arm64), const SYS_SETSID = 147
pkg syscall (netbsd-arm64), const SYS_SETSID ideal-int
pkg syscall (netbsd-arm64), const SYS_SETSOCKOPT = 105
pkg syscall (netbsd-arm64), const SYS_SETSOCKOPT ideal-int
pkg syscall (netbsd-arm64), const SYS_SETTIMEOFDAY = 419
pkg syscall (netbsd-arm64), const SYS_SETTIMEOFDAY ideal-int
pkg syscall (netbsd-arm64), const SYS_SETUID = 23
pkg syscall (netbsd-arm64), const SYS_SETUID ideal-int
pkg syscall (netbsd-arm64), const SYS_SETXATTR = 375
pkg syscall (netbsd-arm64), const SYS_SETXATTR ideal-int
pkg syscall (netbsd-arm64), const SYS_SHMAT = 228
pkg syscall (netbsd-arm64), const SYS_SHMAT ideal-int
pkg syscall (netbsd-arm64), const SYS_SHMCTL = 443
pkg syscall (netbsd-arm64), const SYS_SHMCTL ideal-int
pkg syscall (netbsd-arm64), const SYS_SHMDT = 230
pkg syscall (netbsd-arm64), const SYS_SHMDT ideal-int
pkg syscall (netbsd-arm64), const SYS_SHMGET = 231
pkg syscall (netbsd-arm64), const SYS_SHMGET ideal-int
pkg syscall (netbsd-arm64), const SYS_SHUTDOWN = 134
pkg syscall (netbsd-arm64), const SYS_SHUTDOWN ideal-int
pkg syscall (netbsd-arm64), const SYS___SIGACTION_SIGTRAMP = 340
pkg syscall (netbsd-arm64), const SYS___SIGACTION_SIGTRAMP ideal-int
pkg syscall (netbsd-arm64), const SYS_SIGQUEUEINFO = 245
pkg syscall (netbsd-arm64), const SYS_SIGQUEUEINFO ideal-int
pkg syscall (netbsd-arm64), const SYS___SIGTIMEDWAIT = 431
pkg syscall (netbsd-arm64), const SYS___SIGTIMEDWAIT ideal-int
pkg syscall (netbsd-arm64), const SYS_SOCKET = 394
pkg syscall (netbsd-arm64), const SYS_SOCKET ideal-int
pkg syscall (netbsd-arm64), const SYS_SOCKETPAIR = 135
pkg syscall (netbsd-arm64), const SYS_SOCKETPAIR ideal-int
pkg syscall (netbsd-arm64), const SYS_SSTK = 70
pkg syscall (netbsd-arm64), const SYS_SSTK ideal-int
pkg syscall (netbsd-arm64), const SYS_STAT = 439
pkg syscall (netbsd-arm64), const SYS_STAT ideal-int
pkg syscall (netbsd-arm64), const SYS_STATVFS1 = 357
pkg syscall (netbsd-arm64), const SYS_STATVFS1 ideal-int
pkg syscall (netbsd-arm64), const SYS_SWAPCTL = 271
pkg syscall (netbsd-arm64), const SYS_SWAPCTL ideal-int
pkg syscall (netbsd-arm64), const SYS_SYMLINK = 57
pkg syscall (netbsd-arm64), const SYS_SYMLINKAT = 470
pkg syscall (netbsd-arm64), const SYS_SYMLINKAT ideal-int
pkg syscall (netbsd-arm64), const SYS_SYMLINK ideal-int
pkg syscall (netbsd-arm64), const SYS_SYNC = 36
pkg syscall (netbsd-arm64), const SYS_SYNC ideal-int
pkg syscall (netbsd-arm64), const SYS_SYSARCH = 165
pkg syscall (netbsd-arm64), const SYS_SYSARCH ideal-int
pkg syscall (netbsd-arm64), const SYS___SYSCTL = 202
pkg syscall (netbsd-arm64), const SYS___SYSCTL ideal-int
pkg syscall (netbsd-arm64), const SYS_TIMER_CREATE = 235
pkg syscall (netbsd-arm64), const SYS_TIMER_CREATE ideal-int
pkg syscall (netbsd-arm64), const SYS_TIMER_DELETE = 236
pkg syscall (netbsd-arm64), const SYS_TIMER_DELETE ideal-int
pkg syscall (netbsd-arm64), const SYS_TIMER_GETOVERRUN = 239
pkg syscall (netbsd-arm64), const SYS_TIMER_GETOVERRUN ideal-int
pkg syscall (netbsd-arm64), const SYS_TIMER_GETTIME = 447
pkg syscall (netbsd-arm64), const SYS_TIMER_GETTIME ideal-int
pkg syscall (netbsd-arm64), const SYS_TIMER_SETTIME = 446
pkg syscall (netbsd-arm64), const SYS_TIMER_SETTIME ideal-int
pkg syscall (netbsd-arm64), const SYS_TRUNCATE = 200
pkg syscall (netbsd-arm64), const SYS_TRUNCATE ideal-int
pkg syscall (netbsd-arm64), const SYS_UMASK = 60
pkg syscall (netbsd-arm64), const SYS_UMASK ideal-int
pkg syscall (netbsd-arm64), const SYS_UNDELETE = 205
pkg syscall (netbsd-arm64), const SYS_UNDELETE ideal-int
pkg syscall (netbsd-arm64), const SYS_UNLINK = 10
pkg syscall (netbsd-arm64), const SYS_UNLINKAT = 471
pkg syscall (netbsd-arm64), const SYS_UNLINKAT ideal-int
pkg syscall (netbsd-arm64), const SYS_UNLINK ideal-int
pkg syscall (netbsd-arm64), const SYS_UNMOUNT = 22
pkg syscall (netbsd-arm64), const SYS_UNMOUNT ideal-int
pkg syscall (netbsd-arm64), const SYS_UTIMENSAT = 467
pkg syscall (netbsd-arm64), const SYS_UTIMENSAT ideal-int
pkg syscall (netbsd-arm64), const SYS_UTIMES = 420
pkg syscall (netbsd-arm64), const SYS_UTIMES ideal-int
pkg syscall (netbsd-arm64), const SYS_UTRACE = 306
pkg syscall (netbsd-arm64), const SYS_UTRACE ideal-int
pkg syscall (netbsd-arm64), const SYS_UUIDGEN = 355
pkg syscall (netbsd-arm64), const SYS_UUIDGEN ideal-int
pkg syscall (netbsd-arm64), const SYS_VADVISE = 72
pkg syscall (netbsd-arm64), const SYS_VADVISE ideal-int
pkg syscall (netbsd-arm64), const SYS_VFORK = 66
pkg syscall (netbsd-arm64), const SYS_VFORK ideal-int
pkg syscall (netbsd-arm64), const SYS_WAIT4 = 449
pkg syscall (netbsd-arm64), const SYS_WAIT4 ideal-int
pkg syscall (netbsd-arm64), const SYS_WRITE = 4
pkg syscall (netbsd-arm64), const SYS_WRITE ideal-int
pkg syscall (netbsd-arm64), const SYS_WRITEV = 121
pkg syscall (netbsd-arm64), const SYS_WRITEV ideal-int
pkg syscall (netbsd-arm64), const TCIFLUSH = 1
pkg syscall (netbsd-arm64), const TCIFLUSH ideal-int
pkg syscall (netbsd-arm64), const TCIOFLUSH = 3
pkg syscall (netbsd-arm64), const TCIOFLUSH ideal-int
pkg syscall (netbsd-arm64), const TCOFLUSH = 2
pkg syscall (netbsd-arm64), const TCOFLUSH ideal-int
pkg syscall (netbsd-arm64), const TCP_CONGCTL = 32
pkg syscall (netbsd-arm64), const TCP_CONGCTL ideal-int
pkg syscall (netbsd-arm64), const TCP_KEEPCNT = 6
pkg syscall (netbsd-arm64), const TCP_KEEPCNT ideal-int
pkg syscall (netbsd-arm64), const TCP_KEEPIDLE = 3
pkg syscall (netbsd-arm64), const TCP_KEEPIDLE ideal-int
pkg syscall (netbsd-arm64), const TCP_KEEPINIT = 7
pkg syscall (netbsd-arm64), const TCP_KEEPINIT ideal-int
pkg syscall (netbsd-arm64), const TCP_KEEPINTVL = 5
pkg syscall (netbsd-arm64), const TCP_KEEPINTVL ideal-int
pkg syscall (netbsd-arm64), const TCP_MAXBURST = 4
pkg syscall (netbsd-arm64), const TCP_MAXBURST ideal-int
pkg syscall (netbsd-arm64), const TCP_MAXSEG = 2
pkg syscall (netbsd-arm64), const TCP_MAXSEG ideal-int
pkg syscall (netbsd-arm64), const TCP_MAXWIN = 65535
pkg syscall (netbsd-arm64), const TCP_MAXWIN ideal-int
pkg syscall (netbsd-arm64), const TCP_MAX_WINSHIFT = 14
pkg syscall (netbsd-arm64), const TCP_MAX_WINSHIFT ideal-int
pkg syscall (netbsd-arm64), const TCP_MD5SIG = 16
pkg syscall (netbsd-arm64), const TCP_MD5SIG ideal-int
pkg syscall (netbsd-arm64), const TCP_MINMSS = 216
pkg syscall (netbsd-arm64), const TCP_MINMSS ideal-int
pkg syscall (netbsd-arm64), const TCP_MSS = 536
pkg syscall (netbsd-arm64), const TCP_MSS ideal-int
pkg syscall (netbsd-arm64), const TCSAFLUSH = 2
pkg syscall (netbsd-arm64), const TCSAFLUSH ideal-int
pkg syscall (netbsd-arm64), const TIOCCBRK = 536900730
pkg syscall (netbsd-arm64), const TIOCCBRK ideal-int
pkg syscall (netbsd-arm64), const TIOCCDTR = 536900728
pkg syscall (netbsd-arm64), const TIOCCDTR ideal-int
pkg syscall (netbsd-arm64), const TIOCCONS = 2147775586
pkg syscall (netbsd-arm64), const TIOCCONS ideal-int
pkg syscall (netbsd-arm64), const TIOCDCDTIMESTAMP = 1074820184
pkg syscall (netbsd-arm64), const TIOCDCDTIMESTAMP ideal-int
pkg syscall (netbsd-arm64), const TIOCDRAIN = 536900702
pkg syscall (netbsd-arm64), const TIOCDRAIN ideal-int
pkg syscall (netbsd-arm64), const TIOCEXCL = 536900621
pkg syscall (netbsd-arm64), const TIOCEXCL ideal-int
pkg syscall (netbsd-arm64), const TIOCEXT = 2147775584
pkg syscall (netbsd-arm64), const TIOCEXT ideal-int
pkg syscall (netbsd-arm64), const TIOCFLAG_CDTRCTS = 16
pkg syscall (netbsd-arm64), const TIOCFLAG_CDTRCTS ideal-int
pkg syscall (netbsd-arm64), const TIOCFLAG_CLOCAL = 2
pkg syscall (netbsd-arm64), const TIOCFLAG_CLOCAL ideal-int
pkg syscall (netbsd-arm64), const TIOCFLAG_CRTSCTS = 4
pkg syscall (netbsd-arm64), const TIOCFLAG_CRTSCTS ideal-int
pkg syscall (netbsd-arm64), const TIOCFLAG_MDMBUF = 8
pkg syscall (netbsd-arm64), const TIOCFLAG_MDMBUF ideal-int
pkg syscall (netbsd-arm64), const TIOCFLAG_SOFTCAR = 1
pkg syscall (netbsd-arm64), const TIOCFLAG_SOFTCAR ideal-int
pkg syscall (netbsd-arm64), const TIOCFLUSH = 2147775504
pkg syscall (netbsd-arm64), const TIOCFLUSH ideal-int
pkg syscall (netbsd-arm64), const TIOCGETA = 1076655123
pkg syscall (netbsd-arm64), const TIOCGETA ideal-int
pkg syscall (netbsd-arm64), const TIOCGETD = 1074033690
pkg syscall (netbsd-arm64), const TIOCGETD ideal-int
pkg syscall (netbsd-arm64), const TIOCGFLAGS = 1074033757
pkg syscall (netbsd-arm64), const TIOCGFLAGS ideal-int
pkg syscall (netbsd-arm64), const TIOCGLINED = 1075868738
pkg syscall (netbsd-arm64), const TIOCGLINED ideal-int
pkg syscall (netbsd-arm64), const TIOCGPGRP = 1074033783
pkg syscall (netbsd-arm64), const TIOCGPGRP ideal-int
pkg syscall (netbsd-arm64), const TIOCGQSIZE = 1074033793
pkg syscall (netbsd-arm64), const TIOCGQSIZE ideal-int
pkg syscall (netbsd-arm64), const TIOCGRANTPT = 536900679
pkg syscall (netbsd-arm64), const TIOCGRANTPT ideal-int
pkg syscall (netbsd-arm64), const TIOCGSID = 1074033763
pkg syscall (netbsd-arm64), const TIOCGSID ideal-int
pkg syscall (netbsd-arm64), const TIOCGSIZE = 1074295912
pkg syscall (netbsd-arm64), const TIOCGSIZE ideal-int
pkg syscall (netbsd-arm64), const TIOCGWINSZ = 1074295912
pkg syscall (netbsd-arm64), const TIOCGWINSZ ideal-int
pkg syscall (netbsd-arm64), const TIOCMBIC = 2147775595
pkg syscall (netbsd-arm64), const TIOCMBIC ideal-int
pkg syscall (netbsd-arm64), const TIOCMBIS = 2147775596
pkg syscall (netbsd-arm64), const TIOCMBIS ideal-int
pkg syscall (netbsd-arm64), const TIOCM_CAR = 64
pkg syscall (netbsd-arm64), const TIOCM_CAR ideal-int
pkg syscall (netbsd-arm64), const TIOCM_CD = 64
pkg syscall (netbsd-arm64), const TIOCM_CD ideal-int
pkg syscall (netbsd-arm64), const TIOCM_CTS = 32
pkg syscall (netbsd-arm64), const TIOCM_CTS ideal-int
pkg syscall (netbsd-arm64), const TIOCM_DSR = 256
pkg syscall (netbsd-arm64), const TIOCM_DSR ideal-int
pkg syscall (netbsd-arm64), const TIOCM_DTR = 2
pkg syscall (netbsd-arm64), const TIOCM_DTR ideal-int
pkg syscall (netbsd-arm64), const TIOCMGET = 1074033770
pkg syscall (netbsd-arm64), const TIOCMGET ideal-int
pkg syscall (netbsd-arm64), const TIOCM_LE = 1
pkg syscall (netbsd-arm64), const TIOCM_LE ideal-int
pkg syscall (netbsd-arm64), const TIOCM_RI = 128
pkg syscall (netbsd-arm64), const TIOCM_RI ideal-int
pkg syscall (netbsd-arm64), const TIOCM_RNG = 128
pkg syscall (netbsd-arm64), const TIOCM_RNG ideal-int
pkg syscall (netbsd-arm64), const TIOCM_RTS = 4
pkg syscall (netbsd-arm64), const TIOCM_RTS ideal-int
pkg syscall (netbsd-arm64), const TIOCMSET = 2147775597
pkg syscall (netbsd-arm64), const TIOCMSET ideal-int
pkg syscall (netbsd-arm64), const TIOCM_SR = 16
pkg syscall (netbsd-arm64), const TIOCM_SR ideal-int
pkg syscall (netbsd-arm64), const TIOCM_ST = 8
pkg syscall (netbsd-arm64), const TIOCM_ST ideal-int
pkg syscall (netbsd-arm64), const TIOCNOTTY = 536900721
pkg syscall (netbsd-arm64), const TIOCNOTTY ideal-int
pkg syscall (netbsd-arm64), const TIOCNXCL = 536900622
pkg syscall (netbsd-arm64), const TIOCNXCL ideal-int
pkg syscall (netbsd-arm64), const TIOCOUTQ = 1074033779
pkg syscall (netbsd-arm64), const TIOCOUTQ ideal-int
pkg syscall (netbsd-arm64), const TIOCPKT = 2147775600
pkg syscall (netbsd-arm64), const TIOCPKT_DATA = 0
pkg syscall (netbsd-arm64), const TIOCPKT_DATA ideal-int
pkg syscall (netbsd-arm64), const TIOCPKT_DOSTOP = 32
pkg syscall (netbsd-arm64), const TIOCPKT_DOSTOP ideal-int
pkg syscall (netbsd-arm64), const TIOCPKT_FLUSHREAD = 1
pkg syscall (netbsd-arm64), const TIOCPKT_FLUSHREAD ideal-int
pkg syscall (netbsd-arm64), const TIOCPKT_FLUSHWRITE = 2
pkg syscall (netbsd-arm64), const TIOCPKT_FLUSHWRITE ideal-int
pkg syscall (netbsd-arm64), const TIOCPKT ideal-int
pkg syscall (netbsd-arm64), const TIOCPKT_IOCTL = 64
pkg syscall (netbsd-arm64), const TIOCPKT_IOCTL ideal-int
pkg syscall (netbsd-arm64), const TIOCPKT_NOSTOP = 16
pkg syscall (netbsd-arm64), const TIOCPKT_NOSTOP ideal-int
pkg syscall (netbsd-arm64), const TIOCPKT_START = 8
pkg syscall (netbsd-arm64), const TIOCPKT_START ideal-int
pkg syscall (netbsd-arm64), const TIOCPKT_STOP = 4
pkg syscall (netbsd-arm64), const TIOCPKT_STOP ideal-int
pkg syscall (netbsd-arm64), const TIOCPTMGET = 1076393030
pkg syscall (netbsd-arm64), const TIOCPTMGET ideal-int
pkg syscall (netbsd-arm64), const TIOCPTSNAME = 1076393032
pkg syscall (netbsd-arm64), const TIOCPTSNAME ideal-int
pkg syscall (netbsd-arm64), const TIOCRCVFRAME = 2148037701
pkg syscall (netbsd-arm64), const TIOCRCVFRAME ideal-int
pkg syscall (netbsd-arm64), const TIOCREMOTE = 2147775593
pkg syscall (netbsd-arm64), const TIOCREMOTE ideal-int
pkg syscall (netbsd-arm64), const TIOCSBRK = 536900731
pkg syscall (netbsd-arm64), const TIOCSBRK ideal-int
pkg syscall (netbsd-arm64), const TIOCSCTTY = 536900705
pkg syscall (netbsd-arm64), const TIOCSCTTY ideal-int
pkg syscall (netbsd-arm64), const TIOCSDTR = 536900729
pkg syscall (netbsd-arm64), const TIOCSDTR ideal-int
pkg syscall (netbsd-arm64), const TIOCSETA = 2150396948
pkg syscall (netbsd-arm64), const TIOCSETAF = 2150396950
pkg syscall (netbsd-arm64), const TIOCSETAF ideal-int
pkg syscall (netbsd-arm64), const TIOCSETA ideal-int
pkg syscall (netbsd-arm64), const TIOCSETAW = 2150396949
pkg syscall (netbsd-arm64), const TIOCSETAW ideal-int
pkg syscall (netbsd-arm64), const TIOCSETD = 2147775515
pkg syscall (netbsd-arm64), const TIOCSETD ideal-int
pkg syscall (netbsd-arm64), const TIOCSFLAGS = 2147775580
pkg syscall (netbsd-arm64), const TIOCSFLAGS ideal-int
pkg syscall (netbsd-arm64), const TIOCSIG = 536900703
pkg syscall (netbsd-arm64), const TIOCSIG ideal-int
pkg syscall (netbsd-arm64), const TIOCSLINED = 2149610563
pkg syscall (netbsd-arm64), const TIOCSLINED ideal-int
pkg syscall (netbsd-arm64), const TIOCSPGRP = 2147775606
pkg syscall (netbsd-arm64), const TIOCSPGRP ideal-int
pkg syscall (netbsd-arm64), const TIOCSQSIZE = 2147775616
pkg syscall (netbsd-arm64), const TIOCSQSIZE ideal-int
pkg syscall (netbsd-arm64), const TIOCSSIZE = 2148037735
pkg syscall (netbsd-arm64), const TIOCSSIZE ideal-int
pkg syscall (netbsd-arm64), const TIOCSTART = 536900718
pkg syscall (netbsd-arm64), const TIOCSTART ideal-int
pkg syscall (netbsd-arm64), const TIOCSTAT = 2147775589
pkg syscall (netbsd-arm64), const TIOCSTAT ideal-int
pkg syscall (netbsd-arm64), const TIOCSTI = 2147578994
pkg syscall (netbsd-arm64), const TIOCSTI ideal-int
pkg syscall (netbsd-arm64), const TIOCSTOP = 536900719
pkg syscall (netbsd-arm64), const TIOCSTOP ideal-int
pkg syscall (netbsd-arm64), const TIOCSWINSZ = 2148037735
pkg syscall (netbsd-arm64), const TIOCSWINSZ ideal-int
pkg syscall (netbsd-arm64), const TIOCUCNTL = 2147775590
pkg syscall (netbsd-arm64), const TIOCUCNTL ideal-int
pkg syscall (netbsd-arm64), const TIOCXMTFRAME = 2148037700
pkg syscall (netbsd-arm64), const TIOCXMTFRAME ideal-int
pkg syscall (netbsd-arm64), const TOSTOP = 4194304
pkg syscall (netbsd-arm64), const TOSTOP ideal-int
pkg syscall (netbsd-arm64), const VDISCARD = 15
pkg syscall (netbsd-arm64), const VDISCARD ideal-int
pkg syscall (netbsd-arm64), const VDSUSP = 11
pkg syscall (netbsd-arm64), const VDSUSP ideal-int
pkg syscall (netbsd-arm64), const VEOF = 0
pkg syscall (netbsd-arm64), const VEOF ideal-int
pkg syscall (netbsd-arm64), const VEOL = 1
pkg syscall (netbsd-arm64), const VEOL2 = 2
pkg syscall (netbsd-arm64), const VEOL2 ideal-int
pkg syscall (netbsd-arm64), const VEOL ideal-int
pkg syscall (netbsd-arm64), const VERASE = 3
pkg syscall (netbsd-arm64), const VERASE ideal-int
pkg syscall (netbsd-arm64), const VINTR = 8
pkg syscall (netbsd-arm64), const VINTR ideal-int
pkg syscall (netbsd-arm64), const VKILL = 5
pkg syscall (netbsd-arm64), const VKILL ideal-int
pkg syscall (netbsd-arm64), const VLNEXT = 14
pkg syscall (netbsd-arm64), const VLNEXT ideal-int
pkg syscall (netbsd-arm64), const VMIN = 16
pkg syscall (netbsd-arm64), const VMIN ideal-int
pkg syscall (netbsd-arm64), const VQUIT = 9
pkg syscall (netbsd-arm64), const VQUIT ideal-int
pkg syscall (netbsd-arm64), const VREPRINT = 6
pkg syscall (netbsd-arm64), const VREPRINT ideal-int
pkg syscall (netbsd-arm64), const VSTART = 12
pkg syscall (netbsd-arm64), const VSTART ideal-int
pkg syscall (netbsd-arm64), const VSTATUS = 18
pkg syscall (netbsd-arm64), const VSTATUS ideal-int
pkg syscall (netbsd-arm64), const VSTOP = 13
pkg syscall (netbsd-arm64), const VSTOP ideal-int
pkg syscall (netbsd-arm64), const VSUSP = 10
pkg syscall (netbsd-arm64), const VSUSP ideal-int
pkg syscall (netbsd-arm64), const VTIME = 17
pkg syscall (netbsd-arm64), const VTIME ideal-int
pkg syscall (netbsd-arm64), const VWERASE = 4
pkg syscall (netbsd-arm64), const VWERASE ideal-int
pkg syscall (netbsd-arm64), const WALL = 8
pkg syscall (netbsd-arm64), const WALL ideal-int
pkg syscall (netbsd-arm64), const WALLSIG = 8
pkg syscall (netbsd-arm64), const WALLSIG ideal-int
pkg syscall (netbsd-arm64), const WALTSIG = 4
pkg syscall (netbsd-arm64), const WALTSIG ideal-int
pkg syscall (netbsd-arm64), const WCLONE = 4
pkg syscall (netbsd-arm64), const WCLONE ideal-int
pkg syscall (netbsd-arm64), const WCOREFLAG = 128
pkg syscall (netbsd-arm64), const WCOREFLAG ideal-int
pkg syscall (netbsd-arm64), const WNOHANG = 1
pkg syscall (netbsd-arm64), const WNOHANG ideal-int
pkg syscall (netbsd-arm64), const WNOWAIT = 65536
pkg syscall (netbsd-arm64), const WNOWAIT ideal-int
pkg syscall (netbsd-arm64), const WNOZOMBIE = 131072
pkg syscall (netbsd-arm64), const WNOZOMBIE ideal-int
pkg syscall (netbsd-arm64), const WOPTSCHECKED = 262144
pkg syscall (netbsd-arm64), const WOPTSCHECKED ideal-int
pkg syscall (netbsd-arm64), const WSTOPPED = 127
pkg syscall (netbsd-arm64), const WSTOPPED ideal-int
pkg syscall (netbsd-arm64), const WUNTRACED = 2
pkg syscall (netbsd-arm64), const WUNTRACED ideal-int
pkg syscall (netbsd-arm64), func Accept4(int, int) (int, Sockaddr, error)
pkg syscall (netbsd-arm64), func Accept(int) (int, Sockaddr, error)
pkg syscall (netbsd-arm64), func Access(string, uint32) error
pkg syscall (netbsd-arm64), func Adjtime(*Timeval, *Timeval) error
pkg syscall (netbsd-arm64), func Bind(int, Sockaddr) error
pkg syscall (netbsd-arm64), func BpfBuflen(int) (int, error)
pkg syscall (netbsd-arm64), func BpfDatalink(int) (int, error)
pkg syscall (netbsd-arm64), func BpfHeadercmpl(int) (int, error)
pkg syscall (netbsd-arm64), func BpfInterface(int, string) (string, error)
pkg syscall (netbsd-arm64), func BpfJump(int, int, int, int) *BpfInsn
pkg syscall (netbsd-arm64), func BpfStats(int) (*BpfStat, error)
pkg syscall (netbsd-arm64), func BpfStmt(int, int) *BpfInsn
pkg syscall (netbsd-arm64), func BpfTimeout(int) (*Timeval, error)
pkg syscall (netbsd-arm64), func CheckBpfVersion(int) error
pkg syscall (netbsd-arm64), func Chflags(string, int) error
pkg syscall (netbsd-arm64), func Chroot(string) error
pkg syscall (netbsd-arm64), func Close(int) error
pkg syscall (netbsd-arm64), func CloseOnExec(int)
pkg syscall (netbsd-arm64), func CmsgLen(int) int
pkg syscall (netbsd-arm64), func CmsgSpace(int) int
pkg syscall (netbsd-arm64), func Connect(int, Sockaddr) error
pkg syscall (netbsd-arm64), func Dup2(int, int) error
pkg syscall (netbsd-arm64), func Dup(int) (int, error)
pkg syscall (netbsd-arm64), func Fchdir(int) error
pkg syscall (netbsd-arm64), func Fchflags(int, int) error
pkg syscall (netbsd-arm64), func Fchmod(int, uint32) error
pkg syscall (netbsd-arm64), func Fchown(int, int, int) error
pkg syscall (netbsd-arm64), func FcntlFlock(uintptr, int, *Flock_t) error
pkg syscall (netbsd-arm64), func Flock(int, int) error
pkg syscall (netbsd-arm64), func FlushBpf(int) error
pkg syscall (netbsd-arm64), func ForkExec(string, []string, *ProcAttr) (int, error)
pkg syscall (netbsd-arm64), func Fpathconf(int, int) (int, error)
pkg syscall (netbsd-arm64), func Fstat(int, *Stat_t) error
pkg syscall (netbsd-arm64), func Fsync(int) error
pkg syscall (netbsd-arm64), func Ftruncate(int, int64) error
pkg syscall (netbsd-arm64), func Futimes(int, []Timeval) error
pkg syscall (netbsd-arm64), func Getdirentries(int, []uint8, *uintptr) (int, error)
pkg syscall (netbsd-arm64), func Getpeername(int) (Sockaddr, error)
pkg syscall (netbsd-arm64), func Getpgid(int) (int, error)
pkg syscall (netbsd-arm64), func Getpgrp() int
pkg syscall (netbsd-arm64), func Getpriority(int, int) (int, error)
pkg syscall (netbsd-arm64), func Getrlimit(int, *Rlimit) error
pkg syscall (netbsd-arm64), func Getrusage(int, *Rusage) error
pkg syscall (netbsd-arm64), func Getsid(int) (int, error)
pkg syscall (netbsd-arm64), func Getsockname(int) (Sockaddr, error)
pkg syscall (netbsd-arm64), func GetsockoptByte(int, int, int) (uint8, error)
pkg syscall (netbsd-arm64), func GetsockoptICMPv6Filter(int, int, int) (*ICMPv6Filter, error)
pkg syscall (netbsd-arm64), func GetsockoptInet4Addr(int, int, int) ([4]uint8, error)
pkg syscall (netbsd-arm64), func GetsockoptInt(int, int, int) (int, error)
pkg syscall (netbsd-arm64), func GetsockoptIPMreq(int, int, int) (*IPMreq, error)
pkg syscall (netbsd-arm64), func GetsockoptIPv6Mreq(int, int, int) (*IPv6Mreq, error)
pkg syscall (netbsd-arm64), func GetsockoptIPv6MTUInfo(int, int, int) (*IPv6MTUInfo, error)
pkg syscall (netbsd-arm64), func Issetugid() bool
pkg syscall (netbsd-arm64), func Kevent(int, []Kevent_t, []Kevent_t, *Timespec) (int, error)
pkg syscall (netbsd-arm64), func Kill(int, Signal) error
pkg syscall (netbsd-arm64), func Kqueue() (int, error)
pkg syscall (netbsd-arm64), func Listen(int, int) error
pkg syscall (netbsd-arm64), func Lstat(string, *Stat_t) error
pkg syscall (netbsd-arm64), func Mkfifo(string, uint32) error
pkg syscall (netbsd-arm64), func Mknod(string, uint32, int) error
pkg syscall (netbsd-arm64), func Mmap(int, int64, int, int, int) ([]uint8, error)
pkg syscall (netbsd-arm64), func Munmap([]uint8) error
pkg syscall (netbsd-arm64), func Nanosleep(*Timespec, *Timespec) error
pkg syscall (netbsd-arm64), func Open(string, int, uint32) (int, error)
pkg syscall (netbsd-arm64), func ParseDirent([]uint8, int, []string) (int, int, []string)
pkg syscall (netbsd-arm64), func ParseRoutingMessage([]uint8) ([]RoutingMessage, error)
pkg syscall (netbsd-arm64), func ParseRoutingSockaddr(RoutingMessage) ([]Sockaddr, error)
pkg syscall (netbsd-arm64), func ParseSocketControlMessage([]uint8) ([]SocketControlMessage, error)
pkg syscall (netbsd-arm64), func ParseUnixRights(*SocketControlMessage) ([]int, error)
pkg syscall (netbsd-arm64), func Pathconf(string, int) (int, error)
pkg syscall (netbsd-arm64), func Pipe2([]int, int) error
pkg syscall (netbsd-arm64), func Pipe([]int) error
pkg syscall (netbsd-arm64), func Pread(int, []uint8, int64) (int, error)
pkg syscall (netbsd-arm64), func Pwrite(int, []uint8, int64) (int, error)
pkg syscall (netbsd-arm64), func RawSyscall6(uintptr, uintptr, uintptr, uintptr, uintptr, uintptr, uintptr) (uintptr, uintptr, Errno)
pkg syscall (netbsd-arm64), func RawSyscall(uintptr, uintptr, uintptr, uintptr) (uintptr, uintptr, Errno)
pkg syscall (netbsd-arm64), func ReadDirent(int, []uint8) (int, error)
pkg syscall (netbsd-arm64), func Read(int, []uint8) (int, error)
pkg syscall (netbsd-arm64), func Recvfrom(int, []uint8, int) (int, Sockaddr, error)
pkg syscall (netbsd-arm64), func Recvmsg(int, []uint8, []uint8, int) (int, int, int, Sockaddr, error)
pkg syscall (netbsd-arm64), func Revoke(string) error
pkg syscall (netbsd-arm64), func RouteRIB(int, int) ([]uint8, error)
pkg syscall (netbsd-arm64), func Seek(int, int64, int) (int64, error)
pkg syscall (netbsd-arm64), func Select(int, *FdSet, *FdSet, *FdSet, *Timeval) error
pkg syscall (netbsd-arm64), func Sendfile(int, int, *int64, int) (int, error)
pkg syscall (netbsd-arm64), func Sendmsg(int, []uint8, []uint8, Sockaddr, int) error
pkg syscall (netbsd-arm64), func SendmsgN(int, []uint8, []uint8, Sockaddr, int) (int, error)
pkg syscall (netbsd-arm64), func Sendto(int, []uint8, int, Sockaddr) error
pkg syscall (netbsd-arm64), func SetBpfBuflen(int, int) (int, error)
pkg syscall (netbsd-arm64), func SetBpfDatalink(int, int) (int, error)
pkg syscall (netbsd-arm64), func SetBpfHeadercmpl(int, int) error
pkg syscall (netbsd-arm64), func SetBpfImmediate(int, int) error
pkg syscall (netbsd-arm64), func SetBpf(int, []BpfInsn) error
pkg syscall (netbsd-arm64), func SetBpfInterface(int, string) error
pkg syscall (netbsd-arm64), func SetBpfPromisc(int, int) error
pkg syscall (netbsd-arm64), func SetBpfTimeout(int, *Timeval) error
pkg syscall (netbsd-arm64), func Setegid(int) error
pkg syscall (netbsd-arm64), func Seteuid(int) error
pkg syscall (netbsd-arm64), func Setgid(int) error
pkg syscall (netbsd-arm64), func Setgroups([]int) error
pkg syscall (netbsd-arm64), func SetKevent(*Kevent_t, int, int, int)
pkg syscall (netbsd-arm64), func SetNonblock(int, bool) error
pkg syscall (netbsd-arm64), func Setpgid(int, int) error
pkg syscall (netbsd-arm64), func Setpriority(int, int, int) error
pkg syscall (netbsd-arm64), func Setregid(int, int) error
pkg syscall (netbsd-arm64), func Setreuid(int, int) error
pkg syscall (netbsd-arm64), func Setrlimit(int, *Rlimit) error
pkg syscall (netbsd-arm64), func Setsid() (int, error)
pkg syscall (netbsd-arm64), func SetsockoptByte(int, int, int, uint8) error
pkg syscall (netbsd-arm64), func SetsockoptICMPv6Filter(int, int, int, *ICMPv6Filter) error
pkg syscall (netbsd-arm64), func SetsockoptInet4Addr(int, int, int, [4]uint8) error
pkg syscall (netbsd-arm64), func SetsockoptInt(int, int, int, int) error
pkg syscall (netbsd-arm64), func SetsockoptIPMreq(int, int, int, *IPMreq) error
pkg syscall (netbsd-arm64), func SetsockoptIPv6Mreq(int, int, int, *IPv6Mreq) error
pkg syscall (netbsd-arm64), func SetsockoptLinger(int, int, int, *Linger) error
pkg syscall (netbsd-arm64), func SetsockoptString(int, int, int, string) error
pkg syscall (netbsd-arm64), func SetsockoptTimeval(int, int, int, *Timeval) error
pkg syscall (netbsd-arm64), func Settimeofday(*Timeval) error
pkg syscall (netbsd-arm64), func Setuid(int) error
pkg syscall (netbsd-arm64), func Shutdown(int, int) error
pkg syscall (netbsd-arm64), func SlicePtrFromStrings([]string) ([]*uint8, error)
pkg syscall (netbsd-arm64), func Socket(int, int, int) (int, error)
pkg syscall (netbsd-arm64), func Socketpair(int, int, int) ([2]int, error)
pkg syscall (netbsd-arm64), func Stat(string, *Stat_t) error
pkg syscall (netbsd-arm64), func StringSlicePtr([]string) []*uint8
pkg syscall (netbsd-arm64), func Sync() error
pkg syscall (netbsd-arm64), func Syscall6(uintptr, uintptr, uintptr, uintptr, uintptr, uintptr, uintptr) (uintptr, uintptr, Errno)
pkg syscall (netbsd-arm64), func Syscall9(uintptr, uintptr, uintptr, uintptr, uintptr, uintptr, uintptr, uintptr, uintptr, uintptr) (uintptr, uintptr, Errno)
pkg syscall (netbsd-arm64), func Syscall(uintptr, uintptr, uintptr, uintptr) (uintptr, uintptr, Errno)
pkg syscall (netbsd-arm64), func Sysctl(string) (string, error)
pkg syscall (netbsd-arm64), func SysctlUint32(string) (uint32, error)
pkg syscall (netbsd-arm64), func TimevalToNsec(Timeval) int64
pkg syscall (netbsd-arm64), func Truncate(string, int64) error
pkg syscall (netbsd-arm64), func Umask(int) int
pkg syscall (netbsd-arm64), func UnixRights(...int) []uint8
pkg syscall (netbsd-arm64), func Unmount(string, int) error
pkg syscall (netbsd-arm64), func Wait4(int, *WaitStatus, int, *Rusage) (int, error)
pkg syscall (netbsd-arm64), func Write(int, []uint8) (int, error)
pkg syscall (netbsd-arm64), method (*Cmsghdr) SetLen(int)
pkg syscall (netbsd-arm64), method (*Iovec) SetLen(int)
pkg syscall (netbsd-arm64), method (*Msghdr) SetControllen(int)
pkg syscall (netbsd-arm64), type BpfHdr struct
pkg syscall (netbsd-arm64), type BpfHdr struct, Caplen uint32
pkg syscall (netbsd-arm64), type BpfHdr struct, Datalen uint32
pkg syscall (netbsd-arm64), type BpfHdr struct, Hdrlen uint16
pkg syscall (netbsd-arm64), type BpfHdr struct, Pad_cgo_0 [6]uint8
pkg syscall (netbsd-arm64), type BpfHdr struct, Tstamp BpfTimeval
pkg syscall (netbsd-arm64), type BpfInsn struct
pkg syscall (netbsd-arm64), type BpfInsn struct, Code uint16
pkg syscall (netbsd-arm64), type BpfInsn struct, Jf uint8
pkg syscall (netbsd-arm64), type BpfInsn struct, Jt uint8
pkg syscall (netbsd-arm64), type BpfInsn struct, K uint32
pkg syscall (netbsd-arm64), type BpfProgram struct
pkg syscall (netbsd-arm64), type BpfProgram struct, Insns *BpfInsn
pkg syscall (netbsd-arm64), type BpfProgram struct, Len uint32
pkg syscall (netbsd-arm64), type BpfProgram struct, Pad_cgo_0 [4]uint8
pkg syscall (netbsd-arm64), type BpfStat struct
pkg syscall (netbsd-arm64), type BpfStat struct, Capt uint64
pkg syscall (netbsd-arm64), type BpfStat struct, Drop uint64
pkg syscall (netbsd-arm64), type BpfStat struct, Padding [13]uint64
pkg syscall (netbsd-arm64), type BpfStat struct, Recv uint64
pkg syscall (netbsd-arm64), type BpfTimeval struct
pkg syscall (netbsd-arm64), type BpfTimeval struct, Sec int64
pkg syscall (netbsd-arm64), type BpfTimeval struct, Usec int64
pkg syscall (netbsd-arm64), type BpfVersion struct
pkg syscall (netbsd-arm64), type BpfVersion struct, Major uint16
pkg syscall (netbsd-arm64), type BpfVersion struct, Minor uint16
pkg syscall (netbsd-arm64), type Cmsghdr struct
pkg syscall (netbsd-arm64), type Cmsghdr struct, Len uint32
pkg syscall (netbsd-arm64), type Cmsghdr struct, Level int32
pkg syscall (netbsd-arm64), type Cmsghdr struct, Type int32
pkg syscall (netbsd-arm64), type Credential struct
pkg syscall (netbsd-arm64), type Credential struct, Gid uint32
pkg syscall (netbsd-arm64), type Credential struct, Groups []uint32
pkg syscall (netbsd-arm64), type Credential struct, NoSetGroups bool
pkg syscall (netbsd-arm64), type Credential struct, Uid uint32
pkg syscall (netbsd-arm64), type Dirent struct
pkg syscall (netbsd-arm64), type Dirent struct, Fileno uint64
pkg syscall (netbsd-arm64), type Dirent struct, Name [512]int8
pkg syscall (netbsd-arm64), type Dirent struct, Namlen uint16
pkg syscall (netbsd-arm64), type Dirent struct, Pad_cgo_0 [3]uint8
pkg syscall (netbsd-arm64), type Dirent struct, Reclen uint16
pkg syscall (netbsd-arm64), type Dirent struct, Type uint8
pkg syscall (netbsd-arm64), type FdSet struct
pkg syscall (netbsd-arm64), type FdSet struct, Bits [8]uint32
pkg syscall (netbsd-arm64), type Flock_t struct
pkg syscall (netbsd-arm64), type Flock_t struct, Len int64
pkg syscall (netbsd-arm64), type Flock_t struct, Pid int32
pkg syscall (netbsd-arm64), type Flock_t struct, Start int64
pkg syscall (netbsd-arm64), type Flock_t struct, Type int16
pkg syscall (netbsd-arm64), type Flock_t struct, Whence int16
pkg syscall (netbsd-arm64), type Fsid struct
pkg syscall (netbsd-arm64), type Fsid struct, X__fsid_val [2]int32
pkg syscall (netbsd-arm64), type ICMPv6Filter struct
pkg syscall (netbsd-arm64), type ICMPv6Filter struct, Filt [8]uint32
pkg syscall (netbsd-arm64), type IfaMsghdr struct
pkg syscall (netbsd-arm64), type IfaMsghdr struct, Addrs int32
pkg syscall (netbsd-arm64), type IfaMsghdr struct, Flags int32
pkg syscall (netbsd-arm64), type IfaMsghdr struct, Index uint16
pkg syscall (netbsd-arm64), type IfaMsghdr struct, Metric int32
pkg syscall (netbsd-arm64), type IfaMsghdr struct, Msglen uint16
pkg syscall (netbsd-arm64), type IfaMsghdr struct, Pad_cgo_0 [6]uint8
pkg syscall (netbsd-arm64), type IfaMsghdr struct, Type uint8
pkg syscall (netbsd-arm64), type IfaMsghdr struct, Version uint8
pkg syscall (netbsd-arm64), type IfAnnounceMsghdr struct
pkg syscall (netbsd-arm64), type IfAnnounceMsghdr struct, Index uint16
pkg syscall (netbsd-arm64), type IfAnnounceMsghdr struct, Msglen uint16
pkg syscall (netbsd-arm64), type IfAnnounceMsghdr struct, Name [16]int8
pkg syscall (netbsd-arm64), type IfAnnounceMsghdr struct, Type uint8
pkg syscall (netbsd-arm64), type IfAnnounceMsghdr struct, Version uint8
pkg syscall (netbsd-arm64), type IfAnnounceMsghdr struct, What uint16
pkg syscall (netbsd-arm64), type IfData struct
pkg syscall (netbsd-arm64), type IfData struct, Addrlen uint8
pkg syscall (netbsd-arm64), type IfData struct, Baudrate uint64
pkg syscall (netbsd-arm64), type IfData struct, Collisions uint64
pkg syscall (netbsd-arm64), type IfData struct, Hdrlen uint8
pkg syscall (netbsd-arm64), type IfData struct, Ibytes uint64
pkg syscall (netbsd-arm64), type IfData struct, Ierrors uint64
pkg syscall (netbsd-arm64), type IfData struct, Imcasts uint64
pkg syscall (netbsd-arm64), type IfData struct, Ipackets uint64
pkg syscall (netbsd-arm64), type IfData struct, Iqdrops uint64
pkg syscall (netbsd-arm64), type IfData struct, Lastchange Timespec
pkg syscall (netbsd-arm64), type IfData struct, Link_state int32
pkg syscall (netbsd-arm64), type IfData struct, Metric uint64
pkg syscall (netbsd-arm64), type IfData struct, Mtu uint64
pkg syscall (netbsd-arm64), type IfData struct, Noproto uint64
pkg syscall (netbsd-arm64), type IfData struct, Obytes uint64
pkg syscall (netbsd-arm64), type IfData struct, Oerrors uint64
pkg syscall (netbsd-arm64), type IfData struct, Omcasts uint64
pkg syscall (netbsd-arm64), type IfData struct, Opackets uint64
pkg syscall (netbsd-arm64), type IfData struct, Pad_cgo_0 [1]uint8
pkg syscall (netbsd-arm64), type IfData struct, Type uint8
pkg syscall (netbsd-arm64), type IfMsghdr struct
pkg syscall (netbsd-arm64), type IfMsghdr struct, Addrs int32
pkg syscall (netbsd-arm64), type IfMsghdr struct, Data IfData
pkg syscall (netbsd-arm64), type IfMsghdr struct, Flags int32
pkg syscall (netbsd-arm64), type IfMsghdr struct, Index uint16
pkg syscall (netbsd-arm64), type IfMsghdr struct, Msglen uint16
pkg syscall (netbsd-arm64), type IfMsghdr struct, Pad_cgo_0 [2]uint8
pkg syscall (netbsd-arm64), type IfMsghdr struct, Type uint8
pkg syscall (netbsd-arm64), type IfMsghdr struct, Version uint8
pkg syscall (netbsd-arm64), type Inet6Pktinfo struct
pkg syscall (netbsd-arm64), type Inet6Pktinfo struct, Addr [16]uint8
pkg syscall (netbsd-arm64), type Inet6Pktinfo struct, Ifindex uint32
pkg syscall (netbsd-arm64), type InterfaceAddrMessage struct
pkg syscall (netbsd-arm64), type InterfaceAddrMessage struct, Data []uint8
pkg syscall (netbsd-arm64), type InterfaceAddrMessage struct, Header IfaMsghdr
pkg syscall (netbsd-arm64), type InterfaceAnnounceMessage struct
pkg syscall (netbsd-arm64), type InterfaceAnnounceMessage struct, Header IfAnnounceMsghdr
pkg syscall (netbsd-arm64), type InterfaceMessage struct
pkg syscall (netbsd-arm64), type InterfaceMessage struct, Data []uint8
pkg syscall (netbsd-arm64), type InterfaceMessage struct, Header IfMsghdr
pkg syscall (netbsd-arm64), type Iovec struct
pkg syscall (netbsd-arm64), type Iovec struct, Base *uint8
pkg syscall (netbsd-arm64), type Iovec struct, Len uint64
pkg syscall (netbsd-arm64), type IPv6MTUInfo struct
pkg syscall (netbsd-arm64), type IPv6MTUInfo struct, Addr RawSockaddrInet6
pkg syscall (netbsd-arm64), type IPv6MTUInfo struct, Mtu uint32
pkg syscall (netbsd-arm64), type Kevent_t struct
pkg syscall (netbsd-arm64), type Kevent_t struct, Data int64
pkg syscall (netbsd-arm64), type Kevent_t struct, Fflags uint32
pkg syscall (netbsd-arm64), type Kevent_t struct, Filter uint32
pkg syscall (netbsd-arm64), type Kevent_t struct, Flags uint32
pkg syscall (netbsd-arm64), type Kevent_t struct, Ident uint64
pkg syscall (netbsd-arm64), type Kevent_t struct, Pad_cgo_0 [4]uint8
pkg syscall (netbsd-arm64), type Kevent_t struct, Udata int64
pkg syscall (netbsd-arm64), type Mclpool [0]uint8
pkg syscall (netbsd-arm64), type Msghdr struct
pkg syscall (netbsd-arm64), type Msghdr struct, Controllen uint32
pkg syscall (netbsd-arm64), type Msghdr struct, Control *uint8
pkg syscall (netbsd-arm64), type Msghdr struct, Flags int32
pkg syscall (netbsd-arm64), type Msghdr struct, Iov *Iovec
pkg syscall (netbsd-arm64), type Msghdr struct, Iovlen int32
pkg syscall (netbsd-arm64), type Msghdr struct, Namelen uint32
pkg syscall (netbsd-arm64), type Msghdr struct, Name *uint8
pkg syscall (netbsd-arm64), type Msghdr struct, Pad_cgo_0 [4]uint8
pkg syscall (netbsd-arm64), type Msghdr struct, Pad_cgo_1 [4]uint8
pkg syscall (netbsd-arm64), type RawSockaddrAny struct, Pad [92]int8
pkg syscall (netbsd-arm64), type RawSockaddrDatalink struct
pkg syscall (netbsd-arm64), type RawSockaddrDatalink struct, Alen uint8
pkg syscall (netbsd-arm64), type RawSockaddrDatalink struct, Data [12]int8
pkg syscall (netbsd-arm64), type RawSockaddrDatalink struct, Family uint8
pkg syscall (netbsd-arm64), type RawSockaddrDatalink struct, Index uint16
pkg syscall (netbsd-arm64), type RawSockaddrDatalink struct, Len uint8
pkg syscall (netbsd-arm64), type RawSockaddrDatalink struct, Nlen uint8
pkg syscall (netbsd-arm64), type RawSockaddrDatalink struct, Slen uint8
pkg syscall (netbsd-arm64), type RawSockaddrDatalink struct, Type uint8
pkg syscall (netbsd-arm64), type RawSockaddrInet4 struct, Family uint8
pkg syscall (netbsd-arm64), type RawSockaddrInet4 struct, Len uint8
pkg syscall (netbsd-arm64), type RawSockaddrInet4 struct, Zero [8]int8
pkg syscall (netbsd-arm64), type RawSockaddrInet6 struct, Family uint8
pkg syscall (netbsd-arm64), type RawSockaddrInet6 struct, Len uint8
pkg syscall (netbsd-arm64), type RawSockaddr struct, Data [14]int8
pkg syscall (netbsd-arm64), type RawSockaddr struct, Family uint8
pkg syscall (netbsd-arm64), type RawSockaddr struct, Len uint8
pkg syscall (netbsd-arm64), type RawSockaddrUnix struct, Family uint8
pkg syscall (netbsd-arm64), type RawSockaddrUnix struct, Len uint8
pkg syscall (netbsd-arm64), type RawSockaddrUnix struct, Path [104]int8
pkg syscall (netbsd-arm64), type Rlimit struct
pkg syscall (netbsd-arm64), type Rlimit struct, Cur uint64
pkg syscall (netbsd-arm64), type Rlimit struct, Max uint64
pkg syscall (netbsd-arm64), type RouteMessage struct
pkg syscall (netbsd-arm64), type RouteMessage struct, Data []uint8
pkg syscall (netbsd-arm64), type RouteMessage struct, Header RtMsghdr
pkg syscall (netbsd-arm64), type RoutingMessage interface, unexported methods
pkg syscall (netbsd-arm64), type RtMetrics struct
pkg syscall (netbsd-arm64), type RtMetrics struct, Expire int64
pkg syscall (netbsd-arm64), type RtMetrics struct, Hopcount uint64
pkg syscall (netbsd-arm64), type RtMetrics struct, Locks uint64
pkg syscall (netbsd-arm64), type RtMetrics struct, Mtu uint64
pkg syscall (netbsd-arm64), type RtMetrics struct, Pksent int64
pkg syscall (netbsd-arm64), type RtMetrics struct, Recvpipe uint64
pkg syscall (netbsd-arm64), type RtMetrics struct, Rtt uint64
pkg syscall (netbsd-arm64), type RtMetrics struct, Rttvar uint64
pkg syscall (netbsd-arm64), type RtMetrics struct, Sendpipe uint64
pkg syscall (netbsd-arm64), type RtMetrics struct, Ssthresh uint64
pkg syscall (netbsd-arm64), type RtMsghdr struct
pkg syscall (netbsd-arm64), type RtMsghdr struct, Addrs int32
pkg syscall (netbsd-arm64), type RtMsghdr struct, Errno int32
pkg syscall (netbsd-arm64), type RtMsghdr struct, Flags int32
pkg syscall (netbsd-arm64), type RtMsghdr struct, Index uint16
pkg syscall (netbsd-arm64), type RtMsghdr struct, Inits int32
pkg syscall (netbsd-arm64), type RtMsghdr struct, Msglen uint16
pkg syscall (netbsd-arm64), type RtMsghdr struct, Pad_cgo_0 [2]uint8
pkg syscall (netbsd-arm64), type RtMsghdr struct, Pad_cgo_1 [4]uint8
pkg syscall (netbsd-arm64), type RtMsghdr struct, Pid int32
pkg syscall (netbsd-arm64), type RtMsghdr struct, Rmx RtMetrics
pkg syscall (netbsd-arm64), type RtMsghdr struct, Seq int32
pkg syscall (netbsd-arm64), type RtMsghdr struct, Type uint8
pkg syscall (netbsd-arm64), type RtMsghdr struct, Use int32
pkg syscall (netbsd-arm64), type RtMsghdr struct, Version uint8
pkg syscall (netbsd-arm64), type Rusage struct, Idrss int64
pkg syscall (netbsd-arm64), type Rusage struct, Inblock int64
pkg syscall (netbsd-arm64), type Rusage struct, Isrss int64
pkg syscall (netbsd-arm64), type Rusage struct, Ixrss int64
pkg syscall (netbsd-arm64), type Rusage struct, Majflt int64
pkg syscall (netbsd-arm64), type Rusage struct, Maxrss int64
pkg syscall (netbsd-arm64), type Rusage struct, Minflt int64
pkg syscall (netbsd-arm64), type Rusage struct, Msgrcv int64
pkg syscall (netbsd-arm64), type Rusage struct, Msgsnd int64
pkg syscall (netbsd-arm64), type Rusage struct, Nivcsw int64
pkg syscall (netbsd-arm64), type Rusage struct, Nsignals int64
pkg syscall (netbsd-arm64), type Rusage struct, Nswap int64
pkg syscall (netbsd-arm64), type Rusage struct, Nvcsw int64
pkg syscall (netbsd-arm64), type Rusage struct, Oublock int64
pkg syscall (netbsd-arm64), type Rusage struct, Stime Timeval
pkg syscall (netbsd-arm64), type Rusage struct, Utime Timeval
pkg syscall (netbsd-arm64), type SockaddrDatalink struct
pkg syscall (netbsd-arm64), type SockaddrDatalink struct, Alen uint8
pkg syscall (netbsd-arm64), type SockaddrDatalink struct, Data [12]int8
pkg syscall (netbsd-arm64), type SockaddrDatalink struct, Family uint8
pkg syscall (netbsd-arm64), type SockaddrDatalink struct, Index uint16
pkg syscall (netbsd-arm64), type SockaddrDatalink struct, Len uint8
pkg syscall (netbsd-arm64), type SockaddrDatalink struct, Nlen uint8
pkg syscall (netbsd-arm64), type SockaddrDatalink struct, Slen uint8
pkg syscall (netbsd-arm64), type SockaddrDatalink struct, Type uint8
pkg syscall (netbsd-arm64), type SocketControlMessage struct
pkg syscall (netbsd-arm64), type SocketControlMessage struct, Data []uint8
pkg syscall (netbsd-arm64), type SocketControlMessage struct, Header Cmsghdr
pkg syscall (netbsd-arm64), type Statfs_t [0]uint8
pkg syscall (netbsd-arm64), type Stat_t struct
pkg syscall (netbsd-arm64), type Stat_t struct, Atimespec Timespec
pkg syscall (netbsd-arm64), type Stat_t struct, Birthtimespec Timespec
pkg syscall (netbsd-arm64), type Stat_t struct, Blksize uint32
pkg syscall (netbsd-arm64), type Stat_t struct, Blocks int64
pkg syscall (netbsd-arm64), type Stat_t struct, Ctimespec Timespec
pkg syscall (netbsd-arm64), type Stat_t struct, Dev uint64
pkg syscall (netbsd-arm64), type Stat_t struct, Flags uint32
pkg syscall (netbsd-arm64), type Stat_t struct, Gen uint32
pkg syscall (netbsd-arm64), type Stat_t struct, Gid uint32
pkg syscall (netbsd-arm64), type Stat_t struct, Ino uint64
pkg syscall (netbsd-arm64), type Stat_t struct, Mode uint32
pkg syscall (netbsd-arm64), type Stat_t struct, Mtimespec Timespec
pkg syscall (netbsd-arm64), type Stat_t struct, Nlink uint32
pkg syscall (netbsd-arm64), type Stat_t struct, Pad_cgo_0 [4]uint8
pkg syscall (netbsd-arm64), type Stat_t struct, Pad_cgo_1 [4]uint8
pkg syscall (netbsd-arm64), type Stat_t struct, Pad_cgo_2 [4]uint8
pkg syscall (netbsd-arm64), type Stat_t struct, Rdev uint64
pkg syscall (netbsd-arm64), type Stat_t struct, Size int64
pkg syscall (netbsd-arm64), type Stat_t struct, Spare [2]uint32
pkg syscall (netbsd-arm64), type Stat_t struct, Uid uint32
pkg syscall (netbsd-arm64), type Sysctlnode struct
pkg syscall (netbsd-arm64), type Sysctlnode struct, Flags uint32
pkg syscall (netbsd-arm64), type Sysctlnode struct, Name [32]int8
pkg syscall (netbsd-arm64), type Sysctlnode struct, Num int32
pkg syscall (netbsd-arm64), type Sysctlnode struct, Un [16]uint8
pkg syscall (netbsd-arm64), type Sysctlnode struct, Ver uint32
pkg syscall (netbsd-arm64), type Sysctlnode struct, X__rsvd uint32
pkg syscall (netbsd-arm64), type Sysctlnode struct, X_sysctl_desc [8]uint8
pkg syscall (netbsd-arm64), type Sysctlnode struct, X_sysctl_func [8]uint8
pkg syscall (netbsd-arm64), type Sysctlnode struct, X_sysctl_parent [8]uint8
pkg syscall (netbsd-arm64), type Sysctlnode struct, X_sysctl_size [8]uint8
pkg syscall (netbsd-arm64), type SysProcAttr struct, Chroot string
pkg syscall (netbsd-arm64), type SysProcAttr struct, Credential *Credential
pkg syscall (netbsd-arm64), type SysProcAttr struct, Ctty int
pkg syscall (netbsd-arm64), type SysProcAttr struct, Foreground bool
pkg syscall (netbsd-arm64), type SysProcAttr struct, Noctty bool
pkg syscall (netbsd-arm64), type SysProcAttr struct, Pgid int
pkg syscall (netbsd-arm64), type SysProcAttr struct, Ptrace bool
pkg syscall (netbsd-arm64), type SysProcAttr struct, Setctty bool
pkg syscall (netbsd-arm64), type SysProcAttr struct, Setpgid bool
pkg syscall (netbsd-arm64), type SysProcAttr struct, Setsid bool
pkg syscall (netbsd-arm64), type Termios struct
pkg syscall (netbsd-arm64), type Termios struct, Cc [20]uint8
pkg syscall (netbsd-arm64), type Termios struct, Cflag uint32
pkg syscall (netbsd-arm64), type Termios struct, Iflag uint32
pkg syscall (netbsd-arm64), type Termios struct, Ispeed int32
pkg syscall (netbsd-arm64), type Termios struct, Lflag uint32
pkg syscall (netbsd-arm64), type Termios struct, Oflag uint32
pkg syscall (netbsd-arm64), type Termios struct, Ospeed int32
pkg syscall (netbsd-arm64), type Timespec struct, Nsec int64
pkg syscall (netbsd-arm64), type Timespec struct, Sec int64
pkg syscall (netbsd-arm64), type Timeval struct, Pad_cgo_0 [4]uint8
pkg syscall (netbsd-arm64), type Timeval struct, Sec int64
pkg syscall (netbsd-arm64), type Timeval struct, Usec int32
pkg syscall (netbsd-arm64), type WaitStatus uint32
pkg syscall (netbsd-arm64), var Stderr int
pkg syscall (netbsd-arm64), var Stdin int
pkg syscall (netbsd-arm64), var Stdout int
pkg syscall (windows-386), type SysProcAttr struct, ProcessAttributes *SecurityAttributes
pkg syscall (windows-386), type SysProcAttr struct, ThreadAttributes *SecurityAttributes
pkg syscall (windows-amd64), type SysProcAttr struct, ProcessAttributes *SecurityAttributes
pkg syscall (windows-amd64), type SysProcAttr struct, ThreadAttributes *SecurityAttributes
pkg testing, func Init()
pkg testing, method (*B) ReportMetric(float64, string)
pkg testing, type BenchmarkResult struct, Extra map[string]float64
pkg text/template, method (ExecError) Unwrap() error
pkg time, method (Duration) Microseconds() int64
pkg time, method (Duration) Milliseconds() int64
pkg unicode, const Version = "11.0.0"
pkg unicode, var Dogra *RangeTable
pkg unicode, var Gunjala_Gondi *RangeTable
pkg unicode, var Hanifi_Rohingya *RangeTable
pkg unicode, var Makasar *RangeTable
pkg unicode, var Medefaidrin *RangeTable
pkg unicode, var Old_Sogdian *RangeTable
pkg unicode, var Sogdian *RangeTable
