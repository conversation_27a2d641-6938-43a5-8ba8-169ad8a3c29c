<script lang="ts" setup>
import { ref, onMounted, onUnmounted } from 'vue'
import scannerUtils from '@/utils/scannerUtils.js'

// 用户信息
const userInfo = ref<any>({})

// 历史记录
const reportHistory = ref([
  {
    id: 1,
    machineCode: 'M001',
    machineName: '织机A-01',
    reportTime: '2024-01-15 14:30',
    status: 'pending', // pending: 待维修, repairing: 维修中, completed: 已完成
    description: '机器异响，疑似轴承问题'
  },
  {
    id: 2,
    machineCode: 'M002',
    machineName: '织机B-03',
    reportTime: '2024-01-15 10:15',
    status: 'completed',
    description: '线头断裂频繁'
  },
  {
    id: 3,
    machineCode: 'M003',
    machineName: '织机C-05',
    reportTime: '2024-01-14 16:45',
    status: 'repairing',
    description: '温度过高，需要检查冷却系统'
  }
])

// 获取状态文本和颜色
const getStatusInfo = (status: string) => {
  const statusMap = {
    pending: { text: '待维修', color: '#FF9800' },
    repairing: { text: '维修中', color: '#2196F3' },
    completed: { text: '已完成', color: '#4CAF50' }
  }
  return statusMap[status as keyof typeof statusMap] || { text: '未知', color: '#999999' }
}

// 扫码上报
const handleScanReport = () => {
  // 使用新大陆扫码头或降级到uni.scanCode
  scannerUtils.initScanner((result) => {
    if (result.success) {
      console.log('扫码结果:', result.result)
      // 跳转到异常上报页面，传递机器码
      uni.navigateTo({
        url: `/pages/report/report?machineCode=${result.result}`
      })
    } else {
      console.error('扫码失败:', result.error)
      uni.showToast({
        title: result.error || '扫码失败',
        icon: 'none'
      })
    }
  }, true)
}

// 查看报告详情
const viewReportDetail = (report: any) => {
  uni.showModal({
    title: '上报详情',
    content: `机器：${report.machineName}\n时间：${report.reportTime}\n状态：${getStatusInfo(report.status).text}\n描述：${report.description}`,
    showCancel: false
  })
}

// 退出登录
const handleLogout = () => {
  uni.showModal({
    title: '确认退出',
    content: '确定要退出登录吗？',
    success: (res) => {
      if (res.confirm) {
        uni.removeStorageSync('userInfo')
        uni.reLaunch({
          url: '/pages/login/login'
        })
      }
    }
  })
}

// 页面加载时获取用户信息
onMounted(() => {
  const user = uni.getStorageSync('userInfo')
  if (user) {
    userInfo.value = user
  } else {
    // 如果没有用户信息，跳转到登录页
    uni.reLaunch({
      url: '/pages/login/login'
    })
  }

  // 设置页面为活跃状态
  scannerUtils.setPageActive(true)
})

// 页面卸载时清理扫码资源
onUnmounted(() => {
  scannerUtils.destroy()
})
</script>

<script>
export default {
  onShow() {
    console.log('织工页面显示，激活扫码功能')
    // 这里不能直接使用scannerUtils，需要通过全局方式访问
    if (typeof scannerUtils !== 'undefined') {
      scannerUtils.setPageActive(true)
    }
  },
  onHide() {
    console.log('织工页面隐藏，停用扫码功能')
    if (typeof scannerUtils !== 'undefined') {
      scannerUtils.setPageActive(false)
    }
  },
  onUnload() {
    console.log('织工页面卸载，清理扫码资源')
    if (typeof scannerUtils !== 'undefined') {
      scannerUtils.destroy()
    }
  }
}
</script>

<template>
  <view class="worker-home">
    <!-- 用户信息头部 -->
    <view class="user-header">
      <view class="user-info">
        <view class="avatar">
          <text class="avatar-text">{{ userInfo.username?.charAt(0)?.toUpperCase() }}</text>
        </view>
        <view class="user-details">
          <text class="username">{{ userInfo.username }}</text>
          <text class="role">织工</text>
        </view>
      </view>
      <view class="logout-btn" @click="handleLogout">
        <text class="logout-text">退出</text>
      </view>
    </view>
    
    <!-- 快速操作区域 -->
    <view class="quick-actions">
      <view class="scan-card" @click="handleScanReport">
        <view class="scan-icon">
          <text class="icon">📱</text>
        </view>
        <view class="scan-content">
          <text class="scan-title">扫码上报异常</text>
          <text class="scan-desc">扫描机器二维码快速上报故障</text>
        </view>
        <view class="scan-arrow">
          <text class="arrow">→</text>
        </view>
      </view>
    </view>
    
    <!-- 统计信息 -->
    <view class="stats-section">
      <text class="section-title">今日统计</text>
      <view class="stats-grid">
        <view class="stat-item">
          <text class="stat-number">3</text>
          <text class="stat-label">今日上报</text>
        </view>
        <view class="stat-item">
          <text class="stat-number">1</text>
          <text class="stat-label">待维修</text>
        </view>
        <view class="stat-item">
          <text class="stat-number">2</text>
          <text class="stat-label">已完成</text>
        </view>
      </view>
    </view>
    
    <!-- 历史记录 -->
    <view class="history-section">
      <view class="section-header">
        <text class="section-title">最近上报</text>
        <text class="view-all">查看全部</text>
      </view>
      
      <view class="history-list">
        <view 
          class="history-item" 
          v-for="item in reportHistory" 
          :key="item.id"
          @click="viewReportDetail(item)"
        >
          <view class="machine-info">
            <text class="machine-name">{{ item.machineName }}</text>
            <text class="machine-code">{{ item.machineCode }}</text>
          </view>
          <view class="report-info">
            <text class="report-time">{{ item.reportTime }}</text>
            <view 
              class="status-tag" 
              :style="{ backgroundColor: getStatusInfo(item.status).color }"
            >
              <text class="status-text">{{ getStatusInfo(item.status).text }}</text>
            </view>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<style lang="scss">
.worker-home {
  min-height: 100vh;
  background: #f5f5f5;
  padding-bottom: 40rpx;
}

.user-header {
  background: linear-gradient(135deg, #2196F3 0%, #1976D2 100%);
  padding: 40rpx 30rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.user-info {
  display: flex;
  align-items: center;
}

.avatar {
  width: 80rpx;
  height: 80rpx;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 24rpx;
}

.avatar-text {
  font-size: 32rpx;
  font-weight: bold;
  color: #ffffff;
}

.user-details {
  display: flex;
  flex-direction: column;
}

.username {
  font-size: 32rpx;
  font-weight: bold;
  color: #ffffff;
  margin-bottom: 8rpx;
}

.role {
  font-size: 24rpx;
  color: rgba(255, 255, 255, 0.8);
}

.logout-btn {
  padding: 16rpx 24rpx;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 20rpx;
}

.logout-text {
  font-size: 24rpx;
  color: #ffffff;
}

.quick-actions {
  padding: 30rpx;
}

.scan-card {
  background: #ffffff;
  border-radius: 16rpx;
  padding: 40rpx 30rpx;
  display: flex;
  align-items: center;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
  
  &:active {
    transform: scale(0.98);
  }
}

.scan-icon {
  width: 80rpx;
  height: 80rpx;
  background: linear-gradient(135deg, #FF9800 0%, #F57C00 100%);
  border-radius: 16rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 24rpx;
}

.icon {
  font-size: 36rpx;
}

.scan-content {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.scan-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333333;
  margin-bottom: 8rpx;
}

.scan-desc {
  font-size: 24rpx;
  color: #666666;
}

.scan-arrow {
  width: 40rpx;
  height: 40rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.arrow {
  font-size: 24rpx;
  color: #999999;
}

.stats-section {
  padding: 0 30rpx 30rpx;
}

.section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333333;
  margin-bottom: 24rpx;
}

.stats-grid {
  display: flex;
  gap: 20rpx;
}

.stat-item {
  flex: 1;
  background: #ffffff;
  border-radius: 12rpx;
  padding: 30rpx 20rpx;
  text-align: center;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.stat-number {
  display: block;
  font-size: 40rpx;
  font-weight: bold;
  color: #2196F3;
  margin-bottom: 8rpx;
}

.stat-label {
  font-size: 24rpx;
  color: #666666;
}

.history-section {
  padding: 0 30rpx;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24rpx;
}

.view-all {
  font-size: 24rpx;
  color: #2196F3;
}

.history-list {
  background: #ffffff;
  border-radius: 12rpx;
  overflow: hidden;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.history-item {
  padding: 30rpx;
  border-bottom: 1rpx solid #f0f0f0;
  display: flex;
  justify-content: space-between;
  align-items: center;
  
  &:last-child {
    border-bottom: none;
  }
  
  &:active {
    background: #f8f8f8;
  }
}

.machine-info {
  display: flex;
  flex-direction: column;
}

.machine-name {
  font-size: 28rpx;
  font-weight: 500;
  color: #333333;
  margin-bottom: 8rpx;
}

.machine-code {
  font-size: 24rpx;
  color: #666666;
}

.report-info {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
}

.report-time {
  font-size: 24rpx;
  color: #666666;
  margin-bottom: 8rpx;
}

.status-tag {
  padding: 8rpx 16rpx;
  border-radius: 12rpx;
}

.status-text {
  font-size: 20rpx;
  color: #ffffff;
  font-weight: 500;
}
</style>
