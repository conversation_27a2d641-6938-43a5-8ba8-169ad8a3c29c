/**
 * API 接口配置和调用工具
 */

// API 基础配置
const API_CONFIG = {
  BASE_URL: 'http://localhost:8080/api/v1', // 后端服务器地址
  TIMEOUT: 10000
}

// 根据环境判断使用不同的base url
function getBaseUrl() {
  // #ifdef H5
  return 'http://localhost:8080/api/v1'
  // #endif
  // #ifdef APP-PLUS
  return 'http://localhost:8080/api/v1' // 请替换为实际的服务器IP
  // #endif
  // #ifdef MP-WEIXIN
  return 'https://your-domain.com/api/v1' // 微信小程序需要使用https
  // #endif
  return API_CONFIG.BASE_URL
}

// 存储token
let authToken = uni.getStorageSync('token') || ''

/**
 * 设置认证token
 */
export function setAuthToken(token) {
  authToken = token
  uni.setStorageSync('token', token)
}

/**
 * 获取认证token
 */
export function getAuthToken() {
  return authToken || uni.getStorageSync('token')
}

/**
 * 清除认证信息
 */
export function clearAuth() {
  authToken = ''
  uni.removeStorageSync('token')
  uni.removeStorageSync('userInfo')
}

/**
 * 统一的HTTP请求方法
 */
function request(options) {
  return new Promise((resolve, reject) => {
    const token = getAuthToken()
    const baseUrl = getBaseUrl()
    
    // 添加详细的请求日志
    console.log('发起API请求:', {
      url: `${baseUrl}${options.url}`,
      method: options.method || 'GET',
      data: options.data,
      hasToken: !!token
    })
    
    uni.request({
      url: `${baseUrl}${options.url}`,
      method: options.method || 'GET',
      data: options.data || {},
      header: {
        'Content-Type': 'application/json',
        ...(token ? { 'Authorization': `Bearer ${token}` } : {}),
        ...(options.header || {})
      },
      timeout: API_CONFIG.TIMEOUT,
      success: (res) => {
        console.log('API请求成功:', res)
        if (res.statusCode === 200) {
          resolve(res.data)
        } else if (res.statusCode === 401) {
          // token过期，清除认证信息
          clearAuth()
          uni.showToast({
            title: '登录已过期，请重新登录',
            icon: 'none'
          })
          // 跳转到登录页
          uni.reLaunch({
            url: '/pages/login/login'
          })
          reject(new Error('登录已过期'))
        } else {
          const errorMsg = res.data?.message || res.data?.error || `请求失败 (${res.statusCode})`
          reject(new Error(errorMsg))
        }
      },
      fail: (err) => {
        console.error('API请求失败:', err)
        let errorMsg = '网络请求失败'
        
        if (err.errMsg) {
          if (err.errMsg.includes('timeout')) {
            errorMsg = '请求超时，请检查网络连接'
          } else if (err.errMsg.includes('fail')) {
            if (err.errMsg.includes('localhost') || err.errMsg.includes('127.0.0.1')) {
              errorMsg = '无法连接到本地服务器，请确保后端服务已启动'
            } else {
              errorMsg = '无法连接到服务器，请检查网络和服务器状态'
            }
          } else if (err.errMsg.includes('abort')) {
            errorMsg = '请求被中断，请检查网络连接'
          }
        }
        
        reject(new Error(errorMsg))
      }
    })
  })
}

// API接口定义
export const API = {
  // 用户认证
  auth: {
    login: (data) => request({
      url: '/login',
      method: 'POST',
      data
    }),
    getUserInfo: () => request({
      url: '/user/info'
    })
  },
  
  // 二维码扫描
  qr: {
    scan: (qrCode) => request({
      url: `/qr/scan/${qrCode}`
    }),
    scanForReport: (qrCode) => request({
      url: `/qr/scan/report/${qrCode}`
    }),
    scanForRepair: (qrCode) => request({
      url: `/qr/scan/repair/${qrCode}`
    })
  },
  
  // 机器管理
  machines: {
    getList: (params) => request({
      url: '/machines',
      data: params
    }),
    getByCode: (code) => request({
      url: `/machines/code/${code}`
    }),
    getByQRCode: (qrCode) => request({
      url: `/machines/qr/${qrCode}`
    }),
    updateStatus: (id, status) => request({
      url: `/machines/${id}/status`,
      method: 'PUT',
      data: { status }
    })
  },
  
  // 异常管理
  anomalies: {
    create: (data) => request({
      url: '/anomalies',
      method: 'POST',
      data
    }),
    getList: (params) => request({
      url: '/anomalies',
      data: params
    }),
    getDetail: (id) => request({
      url: `/anomalies/${id}`
    }),
    updateStatus: (id, status) => request({
      url: `/anomalies/${id}/status`,
      method: 'PUT',
      data: { status }
    })
  },
  
  // 维修管理
  repairs: {
    start: (data) => request({
      url: '/repairs/start',
      method: 'POST',
      data
    }),
    complete: (id, data) => request({
      url: `/repairs/${id}/complete`,
      method: 'PUT',
      data
    }),
    getList: (params) => request({
      url: '/repairs',
      data: params
    }),
    getDetail: (id) => request({
      url: `/repairs/${id}`
    })
  }
}

export default API