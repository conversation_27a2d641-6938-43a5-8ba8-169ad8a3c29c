

> @jimp/plugin-rotate@1.1.2 test /Users/<USER>/Documents/jimp/plugins/plugin-rotate
> vitest "--watch=false"


[7m[1m[36m RUN [39m[22m[27m [36mv2.0.5[39m [90m/Users/<USER>/Documents/jimp/plugins/plugin-rotate[39m

[?25l     [90m·[39m 30 degrees
     [90m·[39m 45 degrees
     [90m·[39m 90 degrees
     [90m·[39m -90 degrees
     [90m·[39m 135 degrees
     [90m·[39m 180 degrees
     [90m·[39m 225 degrees
     [90m·[39m 315 degrees
     [90m·[39m -180 degrees
     [90m·[39m -270 degrees
   [90m·[39m Rotate a non-square image without resizing[2m (4)[22m
     [90m·[39m 90 degrees
     [90m·[39m 180 degrees
     [90m·[39m 270 degrees
     [90m·[39m 45 degrees
[?25l[?25l[?25l[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G     [32m✓[39m 30 degrees
     [32m✓[39m 45 degrees
     [32m✓[39m 90 degrees
     [32m✓[39m -90 degrees
     [32m✓[39m 135 degrees
     [32m✓[39m 180 degrees
     [32m✓[39m 225 degrees
     [32m✓[39m 315 degrees
     [32m✓[39m -180 degrees
     [32m✓[39m -270 degrees
   [32m✓[39m Rotate a non-square image without resizing[2m (4)[22m
     [32m✓[39m 90 degrees
     [32m✓[39m 180 degrees
     [32m✓[39m 270 degrees
     [32m✓[39m 45 degrees
[?25l[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (34)[22m
   [32m✓[39m Rotate a image with even size[2m (14)[22m
     [32m✓[39m 1 degrees
     [32m✓[39m 91 degrees
     [32m✓[39m 30 degrees
     [32m✓[39m 45 degrees
     [32m✓[39m 60 degrees
     [32m✓[39m 90 degrees
     [32m✓[39m -90 degrees
     [32m✓[39m 120 degrees
     [32m✓[39m 135 degrees
     [32m✓[39m 180 degrees
     [32m✓[39m 225 degrees
     [32m✓[39m 270 degrees
     [32m✓[39m 315 degrees
     [32m✓[39m 360 degrees
   [32m✓[39m Rotate a image with odd size[2m (4)[22m
     [32m✓[39m 45 degrees
     [32m✓[39m 135 degrees
     [32m✓[39m 225 degrees
     [32m✓[39m 315 degrees
   [32m✓[39m Rotate a non-square image[2m (12)[22m
     [32m✓[39m 1 degrees
     [32m✓[39m 10 degrees
     [32m✓[39m 30 degrees
     [32m✓[39m 45 degrees
     [32m✓[39m 90 degrees
     [32m✓[39m -90 degrees
     [32m✓[39m 135 degrees
     [32m✓[39m 180 degrees
     [32m✓[39m 225 degrees
     [32m✓[39m 315 degrees
     [32m✓[39m -180 degrees
     [32m✓[39m -270 degrees
   [32m✓[39m Rotate a non-square image without resizing[2m (4)[22m
     [32m✓[39m 90 degrees
     [32m✓[39m 180 degrees
     [32m✓[39m 270 degrees
     [32m✓[39m 45 degrees

[2m Test Files [22m [1m[32m1 passed[39m[22m[90m (1)[39m
[2m      Tests [22m [1m[32m34 passed[39m[22m[90m (34)[39m
[2m   Start at [22m 01:34:10
[2m   Duration [22m 3.38s[2m (transform 1.13s, setup 0ms, collect 1.43s, tests 32ms, environment 0ms, prepare 591ms)[22m

[?25h[?25h
