# Expect no panic
go list -f '{{if .DepsErrors}}{{.DepsErrors}}{{end}}' -export -e -deps
cmpenv stdout wanterr

-- wanterr --
[# test/main/level1a
level1a${/}pkg.go:5:2: level2x redeclared in this block
	level1a${/}pkg.go:4:2: other declaration of level2x
level1a${/}pkg.go:5:2: "test/main/level1a/level2y" imported as level2x and not used
level1a${/}pkg.go:8:39: undefined: level2y
 # test/main/level1b
level1b${/}pkg.go:5:2: level2x redeclared in this block
	level1b${/}pkg.go:4:2: other declaration of level2x
level1b${/}pkg.go:5:2: "test/main/level1b/level2y" imported as level2x and not used
level1b${/}pkg.go:8:39: undefined: level2y
]
-- go.mod --
module test/main

go 1.20
-- main.go --
package main

import (
	"test/main/level1a"
	"test/main/level1b"
)

func main() {
	level1a.Print()
	level1b.Print()
}
-- level1a/pkg.go --
package level1a

import (
	"test/main/level1a/level2x"
	"test/main/level1a/level2y"
)

func Print() { println(level2x.Value, level2y.Value) }
-- level1a/level2x/pkg.go --
package level2x

var Value = "1a/2x"
-- level1a/level2y/pkg.go --
package level2x

var Value = "1a/2y"
-- level1b/pkg.go --
package level1b

import (
	"test/main/level1b/level2x"
	"test/main/level1b/level2y"
)

func Print() { println(level2x.Value, level2y.Value) }
-- level1b/level2x/pkg.go --
package level2x

var Value = "1b/2x"
-- level1b/level2y/pkg.go --
package level2x

var Value = "1b/2y"