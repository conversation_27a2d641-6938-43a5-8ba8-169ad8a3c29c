// run
// Code generated by gen/constFoldGen.go. DO NOT EDIT.

package test

import "testing"

func TestConstFolduint64add(t *testing.T) {
	var x, y, r uint64
	x = 0
	y = 0
	r = x + y
	if r != 0 {
		t.<PERSON><PERSON><PERSON>("0 %s 0 = %d, want 0", "+", r)
	}
	y = 1
	r = x + y
	if r != 1 {
		t.<PERSON><PERSON><PERSON>("0 %s 1 = %d, want 1", "+", r)
	}
	y = 4294967296
	r = x + y
	if r != 4294967296 {
		t.<PERSON><PERSON><PERSON>("0 %s 4294967296 = %d, want 4294967296", "+", r)
	}
	y = 18446744073709551615
	r = x + y
	if r != 18446744073709551615 {
		t.<PERSON><PERSON><PERSON>("0 %s 18446744073709551615 = %d, want 18446744073709551615", "+", r)
	}
	x = 1
	y = 0
	r = x + y
	if r != 1 {
		t.<PERSON><PERSON><PERSON>("1 %s 0 = %d, want 1", "+", r)
	}
	y = 1
	r = x + y
	if r != 2 {
		t.<PERSON><PERSON><PERSON>("1 %s 1 = %d, want 2", "+", r)
	}
	y = 4294967296
	r = x + y
	if r != 4294967297 {
		t.Errorf("1 %s 4294967296 = %d, want 4294967297", "+", r)
	}
	y = 18446744073709551615
	r = x + y
	if r != 0 {
		t.Errorf("1 %s 18446744073709551615 = %d, want 0", "+", r)
	}
	x = 4294967296
	y = 0
	r = x + y
	if r != 4294967296 {
		t.Errorf("4294967296 %s 0 = %d, want 4294967296", "+", r)
	}
	y = 1
	r = x + y
	if r != 4294967297 {
		t.Errorf("4294967296 %s 1 = %d, want 4294967297", "+", r)
	}
	y = 4294967296
	r = x + y
	if r != 8589934592 {
		t.Errorf("4294967296 %s 4294967296 = %d, want 8589934592", "+", r)
	}
	y = 18446744073709551615
	r = x + y
	if r != 4294967295 {
		t.Errorf("4294967296 %s 18446744073709551615 = %d, want 4294967295", "+", r)
	}
	x = 18446744073709551615
	y = 0
	r = x + y
	if r != 18446744073709551615 {
		t.Errorf("18446744073709551615 %s 0 = %d, want 18446744073709551615", "+", r)
	}
	y = 1
	r = x + y
	if r != 0 {
		t.Errorf("18446744073709551615 %s 1 = %d, want 0", "+", r)
	}
	y = 4294967296
	r = x + y
	if r != 4294967295 {
		t.Errorf("18446744073709551615 %s 4294967296 = %d, want 4294967295", "+", r)
	}
	y = 18446744073709551615
	r = x + y
	if r != 18446744073709551614 {
		t.Errorf("18446744073709551615 %s 18446744073709551615 = %d, want 18446744073709551614", "+", r)
	}
}
func TestConstFolduint64sub(t *testing.T) {
	var x, y, r uint64
	x = 0
	y = 0
	r = x - y
	if r != 0 {
		t.Errorf("0 %s 0 = %d, want 0", "-", r)
	}
	y = 1
	r = x - y
	if r != 18446744073709551615 {
		t.Errorf("0 %s 1 = %d, want 18446744073709551615", "-", r)
	}
	y = 4294967296
	r = x - y
	if r != 18446744069414584320 {
		t.Errorf("0 %s 4294967296 = %d, want 18446744069414584320", "-", r)
	}
	y = 18446744073709551615
	r = x - y
	if r != 1 {
		t.Errorf("0 %s 18446744073709551615 = %d, want 1", "-", r)
	}
	x = 1
	y = 0
	r = x - y
	if r != 1 {
		t.Errorf("1 %s 0 = %d, want 1", "-", r)
	}
	y = 1
	r = x - y
	if r != 0 {
		t.Errorf("1 %s 1 = %d, want 0", "-", r)
	}
	y = 4294967296
	r = x - y
	if r != 18446744069414584321 {
		t.Errorf("1 %s 4294967296 = %d, want 18446744069414584321", "-", r)
	}
	y = 18446744073709551615
	r = x - y
	if r != 2 {
		t.Errorf("1 %s 18446744073709551615 = %d, want 2", "-", r)
	}
	x = 4294967296
	y = 0
	r = x - y
	if r != 4294967296 {
		t.Errorf("4294967296 %s 0 = %d, want 4294967296", "-", r)
	}
	y = 1
	r = x - y
	if r != 4294967295 {
		t.Errorf("4294967296 %s 1 = %d, want 4294967295", "-", r)
	}
	y = 4294967296
	r = x - y
	if r != 0 {
		t.Errorf("4294967296 %s 4294967296 = %d, want 0", "-", r)
	}
	y = 18446744073709551615
	r = x - y
	if r != 4294967297 {
		t.Errorf("4294967296 %s 18446744073709551615 = %d, want 4294967297", "-", r)
	}
	x = 18446744073709551615
	y = 0
	r = x - y
	if r != 18446744073709551615 {
		t.Errorf("18446744073709551615 %s 0 = %d, want 18446744073709551615", "-", r)
	}
	y = 1
	r = x - y
	if r != 18446744073709551614 {
		t.Errorf("18446744073709551615 %s 1 = %d, want 18446744073709551614", "-", r)
	}
	y = 4294967296
	r = x - y
	if r != 18446744069414584319 {
		t.Errorf("18446744073709551615 %s 4294967296 = %d, want 18446744069414584319", "-", r)
	}
	y = 18446744073709551615
	r = x - y
	if r != 0 {
		t.Errorf("18446744073709551615 %s 18446744073709551615 = %d, want 0", "-", r)
	}
}
func TestConstFolduint64div(t *testing.T) {
	var x, y, r uint64
	x = 0
	y = 1
	r = x / y
	if r != 0 {
		t.Errorf("0 %s 1 = %d, want 0", "/", r)
	}
	y = 4294967296
	r = x / y
	if r != 0 {
		t.Errorf("0 %s 4294967296 = %d, want 0", "/", r)
	}
	y = 18446744073709551615
	r = x / y
	if r != 0 {
		t.Errorf("0 %s 18446744073709551615 = %d, want 0", "/", r)
	}
	x = 1
	y = 1
	r = x / y
	if r != 1 {
		t.Errorf("1 %s 1 = %d, want 1", "/", r)
	}
	y = 4294967296
	r = x / y
	if r != 0 {
		t.Errorf("1 %s 4294967296 = %d, want 0", "/", r)
	}
	y = 18446744073709551615
	r = x / y
	if r != 0 {
		t.Errorf("1 %s 18446744073709551615 = %d, want 0", "/", r)
	}
	x = 4294967296
	y = 1
	r = x / y
	if r != 4294967296 {
		t.Errorf("4294967296 %s 1 = %d, want 4294967296", "/", r)
	}
	y = 4294967296
	r = x / y
	if r != 1 {
		t.Errorf("4294967296 %s 4294967296 = %d, want 1", "/", r)
	}
	y = 18446744073709551615
	r = x / y
	if r != 0 {
		t.Errorf("4294967296 %s 18446744073709551615 = %d, want 0", "/", r)
	}
	x = 18446744073709551615
	y = 1
	r = x / y
	if r != 18446744073709551615 {
		t.Errorf("18446744073709551615 %s 1 = %d, want 18446744073709551615", "/", r)
	}
	y = 4294967296
	r = x / y
	if r != 4294967295 {
		t.Errorf("18446744073709551615 %s 4294967296 = %d, want 4294967295", "/", r)
	}
	y = 18446744073709551615
	r = x / y
	if r != 1 {
		t.Errorf("18446744073709551615 %s 18446744073709551615 = %d, want 1", "/", r)
	}
}
func TestConstFolduint64mul(t *testing.T) {
	var x, y, r uint64
	x = 0
	y = 0
	r = x * y
	if r != 0 {
		t.Errorf("0 %s 0 = %d, want 0", "*", r)
	}
	y = 1
	r = x * y
	if r != 0 {
		t.Errorf("0 %s 1 = %d, want 0", "*", r)
	}
	y = 4294967296
	r = x * y
	if r != 0 {
		t.Errorf("0 %s 4294967296 = %d, want 0", "*", r)
	}
	y = 18446744073709551615
	r = x * y
	if r != 0 {
		t.Errorf("0 %s 18446744073709551615 = %d, want 0", "*", r)
	}
	x = 1
	y = 0
	r = x * y
	if r != 0 {
		t.Errorf("1 %s 0 = %d, want 0", "*", r)
	}
	y = 1
	r = x * y
	if r != 1 {
		t.Errorf("1 %s 1 = %d, want 1", "*", r)
	}
	y = 4294967296
	r = x * y
	if r != 4294967296 {
		t.Errorf("1 %s 4294967296 = %d, want 4294967296", "*", r)
	}
	y = 18446744073709551615
	r = x * y
	if r != 18446744073709551615 {
		t.Errorf("1 %s 18446744073709551615 = %d, want 18446744073709551615", "*", r)
	}
	x = 4294967296
	y = 0
	r = x * y
	if r != 0 {
		t.Errorf("4294967296 %s 0 = %d, want 0", "*", r)
	}
	y = 1
	r = x * y
	if r != 4294967296 {
		t.Errorf("4294967296 %s 1 = %d, want 4294967296", "*", r)
	}
	y = 4294967296
	r = x * y
	if r != 0 {
		t.Errorf("4294967296 %s 4294967296 = %d, want 0", "*", r)
	}
	y = 18446744073709551615
	r = x * y
	if r != 18446744069414584320 {
		t.Errorf("4294967296 %s 18446744073709551615 = %d, want 18446744069414584320", "*", r)
	}
	x = 18446744073709551615
	y = 0
	r = x * y
	if r != 0 {
		t.Errorf("18446744073709551615 %s 0 = %d, want 0", "*", r)
	}
	y = 1
	r = x * y
	if r != 18446744073709551615 {
		t.Errorf("18446744073709551615 %s 1 = %d, want 18446744073709551615", "*", r)
	}
	y = 4294967296
	r = x * y
	if r != 18446744069414584320 {
		t.Errorf("18446744073709551615 %s 4294967296 = %d, want 18446744069414584320", "*", r)
	}
	y = 18446744073709551615
	r = x * y
	if r != 1 {
		t.Errorf("18446744073709551615 %s 18446744073709551615 = %d, want 1", "*", r)
	}
}
func TestConstFolduint64mod(t *testing.T) {
	var x, y, r uint64
	x = 0
	y = 1
	r = x % y
	if r != 0 {
		t.Errorf("0 %s 1 = %d, want 0", "%", r)
	}
	y = 4294967296
	r = x % y
	if r != 0 {
		t.Errorf("0 %s 4294967296 = %d, want 0", "%", r)
	}
	y = 18446744073709551615
	r = x % y
	if r != 0 {
		t.Errorf("0 %s 18446744073709551615 = %d, want 0", "%", r)
	}
	x = 1
	y = 1
	r = x % y
	if r != 0 {
		t.Errorf("1 %s 1 = %d, want 0", "%", r)
	}
	y = 4294967296
	r = x % y
	if r != 1 {
		t.Errorf("1 %s 4294967296 = %d, want 1", "%", r)
	}
	y = 18446744073709551615
	r = x % y
	if r != 1 {
		t.Errorf("1 %s 18446744073709551615 = %d, want 1", "%", r)
	}
	x = 4294967296
	y = 1
	r = x % y
	if r != 0 {
		t.Errorf("4294967296 %s 1 = %d, want 0", "%", r)
	}
	y = 4294967296
	r = x % y
	if r != 0 {
		t.Errorf("4294967296 %s 4294967296 = %d, want 0", "%", r)
	}
	y = 18446744073709551615
	r = x % y
	if r != 4294967296 {
		t.Errorf("4294967296 %s 18446744073709551615 = %d, want 4294967296", "%", r)
	}
	x = 18446744073709551615
	y = 1
	r = x % y
	if r != 0 {
		t.Errorf("18446744073709551615 %s 1 = %d, want 0", "%", r)
	}
	y = 4294967296
	r = x % y
	if r != 4294967295 {
		t.Errorf("18446744073709551615 %s 4294967296 = %d, want 4294967295", "%", r)
	}
	y = 18446744073709551615
	r = x % y
	if r != 0 {
		t.Errorf("18446744073709551615 %s 18446744073709551615 = %d, want 0", "%", r)
	}
}
func TestConstFoldint64add(t *testing.T) {
	var x, y, r int64
	x = -9223372036854775808
	y = -9223372036854775808
	r = x + y
	if r != 0 {
		t.Errorf("-9223372036854775808 %s -9223372036854775808 = %d, want 0", "+", r)
	}
	y = -9223372036854775807
	r = x + y
	if r != 1 {
		t.Errorf("-9223372036854775808 %s -9223372036854775807 = %d, want 1", "+", r)
	}
	y = -4294967296
	r = x + y
	if r != 9223372032559808512 {
		t.Errorf("-9223372036854775808 %s -4294967296 = %d, want 9223372032559808512", "+", r)
	}
	y = -1
	r = x + y
	if r != 9223372036854775807 {
		t.Errorf("-9223372036854775808 %s -1 = %d, want 9223372036854775807", "+", r)
	}
	y = 0
	r = x + y
	if r != -9223372036854775808 {
		t.Errorf("-9223372036854775808 %s 0 = %d, want -9223372036854775808", "+", r)
	}
	y = 1
	r = x + y
	if r != -9223372036854775807 {
		t.Errorf("-9223372036854775808 %s 1 = %d, want -9223372036854775807", "+", r)
	}
	y = 4294967296
	r = x + y
	if r != -9223372032559808512 {
		t.Errorf("-9223372036854775808 %s 4294967296 = %d, want -9223372032559808512", "+", r)
	}
	y = 9223372036854775806
	r = x + y
	if r != -2 {
		t.Errorf("-9223372036854775808 %s 9223372036854775806 = %d, want -2", "+", r)
	}
	y = 9223372036854775807
	r = x + y
	if r != -1 {
		t.Errorf("-9223372036854775808 %s 9223372036854775807 = %d, want -1", "+", r)
	}
	x = -9223372036854775807
	y = -9223372036854775808
	r = x + y
	if r != 1 {
		t.Errorf("-9223372036854775807 %s -9223372036854775808 = %d, want 1", "+", r)
	}
	y = -9223372036854775807
	r = x + y
	if r != 2 {
		t.Errorf("-9223372036854775807 %s -9223372036854775807 = %d, want 2", "+", r)
	}
	y = -4294967296
	r = x + y
	if r != 9223372032559808513 {
		t.Errorf("-9223372036854775807 %s -4294967296 = %d, want 9223372032559808513", "+", r)
	}
	y = -1
	r = x + y
	if r != -9223372036854775808 {
		t.Errorf("-9223372036854775807 %s -1 = %d, want -9223372036854775808", "+", r)
	}
	y = 0
	r = x + y
	if r != -9223372036854775807 {
		t.Errorf("-9223372036854775807 %s 0 = %d, want -9223372036854775807", "+", r)
	}
	y = 1
	r = x + y
	if r != -9223372036854775806 {
		t.Errorf("-9223372036854775807 %s 1 = %d, want -9223372036854775806", "+", r)
	}
	y = 4294967296
	r = x + y
	if r != -9223372032559808511 {
		t.Errorf("-9223372036854775807 %s 4294967296 = %d, want -9223372032559808511", "+", r)
	}
	y = 9223372036854775806
	r = x + y
	if r != -1 {
		t.Errorf("-9223372036854775807 %s 9223372036854775806 = %d, want -1", "+", r)
	}
	y = 9223372036854775807
	r = x + y
	if r != 0 {
		t.Errorf("-9223372036854775807 %s 9223372036854775807 = %d, want 0", "+", r)
	}
	x = -4294967296
	y = -9223372036854775808
	r = x + y
	if r != 9223372032559808512 {
		t.Errorf("-4294967296 %s -9223372036854775808 = %d, want 9223372032559808512", "+", r)
	}
	y = -9223372036854775807
	r = x + y
	if r != 9223372032559808513 {
		t.Errorf("-4294967296 %s -9223372036854775807 = %d, want 9223372032559808513", "+", r)
	}
	y = -4294967296
	r = x + y
	if r != -8589934592 {
		t.Errorf("-4294967296 %s -4294967296 = %d, want -8589934592", "+", r)
	}
	y = -1
	r = x + y
	if r != -4294967297 {
		t.Errorf("-4294967296 %s -1 = %d, want -4294967297", "+", r)
	}
	y = 0
	r = x + y
	if r != -4294967296 {
		t.Errorf("-4294967296 %s 0 = %d, want -4294967296", "+", r)
	}
	y = 1
	r = x + y
	if r != -4294967295 {
		t.Errorf("-4294967296 %s 1 = %d, want -4294967295", "+", r)
	}
	y = 4294967296
	r = x + y
	if r != 0 {
		t.Errorf("-4294967296 %s 4294967296 = %d, want 0", "+", r)
	}
	y = 9223372036854775806
	r = x + y
	if r != 9223372032559808510 {
		t.Errorf("-4294967296 %s 9223372036854775806 = %d, want 9223372032559808510", "+", r)
	}
	y = 9223372036854775807
	r = x + y
	if r != 9223372032559808511 {
		t.Errorf("-4294967296 %s 9223372036854775807 = %d, want 9223372032559808511", "+", r)
	}
	x = -1
	y = -9223372036854775808
	r = x + y
	if r != 9223372036854775807 {
		t.Errorf("-1 %s -9223372036854775808 = %d, want 9223372036854775807", "+", r)
	}
	y = -9223372036854775807
	r = x + y
	if r != -9223372036854775808 {
		t.Errorf("-1 %s -9223372036854775807 = %d, want -9223372036854775808", "+", r)
	}
	y = -4294967296
	r = x + y
	if r != -4294967297 {
		t.Errorf("-1 %s -4294967296 = %d, want -4294967297", "+", r)
	}
	y = -1
	r = x + y
	if r != -2 {
		t.Errorf("-1 %s -1 = %d, want -2", "+", r)
	}
	y = 0
	r = x + y
	if r != -1 {
		t.Errorf("-1 %s 0 = %d, want -1", "+", r)
	}
	y = 1
	r = x + y
	if r != 0 {
		t.Errorf("-1 %s 1 = %d, want 0", "+", r)
	}
	y = 4294967296
	r = x + y
	if r != 4294967295 {
		t.Errorf("-1 %s 4294967296 = %d, want 4294967295", "+", r)
	}
	y = 9223372036854775806
	r = x + y
	if r != 9223372036854775805 {
		t.Errorf("-1 %s 9223372036854775806 = %d, want 9223372036854775805", "+", r)
	}
	y = 9223372036854775807
	r = x + y
	if r != 9223372036854775806 {
		t.Errorf("-1 %s 9223372036854775807 = %d, want 9223372036854775806", "+", r)
	}
	x = 0
	y = -9223372036854775808
	r = x + y
	if r != -9223372036854775808 {
		t.Errorf("0 %s -9223372036854775808 = %d, want -9223372036854775808", "+", r)
	}
	y = -9223372036854775807
	r = x + y
	if r != -9223372036854775807 {
		t.Errorf("0 %s -9223372036854775807 = %d, want -9223372036854775807", "+", r)
	}
	y = -4294967296
	r = x + y
	if r != -4294967296 {
		t.Errorf("0 %s -4294967296 = %d, want -4294967296", "+", r)
	}
	y = -1
	r = x + y
	if r != -1 {
		t.Errorf("0 %s -1 = %d, want -1", "+", r)
	}
	y = 0
	r = x + y
	if r != 0 {
		t.Errorf("0 %s 0 = %d, want 0", "+", r)
	}
	y = 1
	r = x + y
	if r != 1 {
		t.Errorf("0 %s 1 = %d, want 1", "+", r)
	}
	y = 4294967296
	r = x + y
	if r != 4294967296 {
		t.Errorf("0 %s 4294967296 = %d, want 4294967296", "+", r)
	}
	y = 9223372036854775806
	r = x + y
	if r != 9223372036854775806 {
		t.Errorf("0 %s 9223372036854775806 = %d, want 9223372036854775806", "+", r)
	}
	y = 9223372036854775807
	r = x + y
	if r != 9223372036854775807 {
		t.Errorf("0 %s 9223372036854775807 = %d, want 9223372036854775807", "+", r)
	}
	x = 1
	y = -9223372036854775808
	r = x + y
	if r != -9223372036854775807 {
		t.Errorf("1 %s -9223372036854775808 = %d, want -9223372036854775807", "+", r)
	}
	y = -9223372036854775807
	r = x + y
	if r != -9223372036854775806 {
		t.Errorf("1 %s -9223372036854775807 = %d, want -9223372036854775806", "+", r)
	}
	y = -4294967296
	r = x + y
	if r != -4294967295 {
		t.Errorf("1 %s -4294967296 = %d, want -4294967295", "+", r)
	}
	y = -1
	r = x + y
	if r != 0 {
		t.Errorf("1 %s -1 = %d, want 0", "+", r)
	}
	y = 0
	r = x + y
	if r != 1 {
		t.Errorf("1 %s 0 = %d, want 1", "+", r)
	}
	y = 1
	r = x + y
	if r != 2 {
		t.Errorf("1 %s 1 = %d, want 2", "+", r)
	}
	y = 4294967296
	r = x + y
	if r != 4294967297 {
		t.Errorf("1 %s 4294967296 = %d, want 4294967297", "+", r)
	}
	y = 9223372036854775806
	r = x + y
	if r != 9223372036854775807 {
		t.Errorf("1 %s 9223372036854775806 = %d, want 9223372036854775807", "+", r)
	}
	y = 9223372036854775807
	r = x + y
	if r != -9223372036854775808 {
		t.Errorf("1 %s 9223372036854775807 = %d, want -9223372036854775808", "+", r)
	}
	x = 4294967296
	y = -9223372036854775808
	r = x + y
	if r != -9223372032559808512 {
		t.Errorf("4294967296 %s -9223372036854775808 = %d, want -9223372032559808512", "+", r)
	}
	y = -9223372036854775807
	r = x + y
	if r != -9223372032559808511 {
		t.Errorf("4294967296 %s -9223372036854775807 = %d, want -9223372032559808511", "+", r)
	}
	y = -4294967296
	r = x + y
	if r != 0 {
		t.Errorf("4294967296 %s -4294967296 = %d, want 0", "+", r)
	}
	y = -1
	r = x + y
	if r != 4294967295 {
		t.Errorf("4294967296 %s -1 = %d, want 4294967295", "+", r)
	}
	y = 0
	r = x + y
	if r != 4294967296 {
		t.Errorf("4294967296 %s 0 = %d, want 4294967296", "+", r)
	}
	y = 1
	r = x + y
	if r != 4294967297 {
		t.Errorf("4294967296 %s 1 = %d, want 4294967297", "+", r)
	}
	y = 4294967296
	r = x + y
	if r != 8589934592 {
		t.Errorf("4294967296 %s 4294967296 = %d, want 8589934592", "+", r)
	}
	y = 9223372036854775806
	r = x + y
	if r != -9223372032559808514 {
		t.Errorf("4294967296 %s 9223372036854775806 = %d, want -9223372032559808514", "+", r)
	}
	y = 9223372036854775807
	r = x + y
	if r != -9223372032559808513 {
		t.Errorf("4294967296 %s 9223372036854775807 = %d, want -9223372032559808513", "+", r)
	}
	x = 9223372036854775806
	y = -9223372036854775808
	r = x + y
	if r != -2 {
		t.Errorf("9223372036854775806 %s -9223372036854775808 = %d, want -2", "+", r)
	}
	y = -9223372036854775807
	r = x + y
	if r != -1 {
		t.Errorf("9223372036854775806 %s -9223372036854775807 = %d, want -1", "+", r)
	}
	y = -4294967296
	r = x + y
	if r != 9223372032559808510 {
		t.Errorf("9223372036854775806 %s -4294967296 = %d, want 9223372032559808510", "+", r)
	}
	y = -1
	r = x + y
	if r != 9223372036854775805 {
		t.Errorf("9223372036854775806 %s -1 = %d, want 9223372036854775805", "+", r)
	}
	y = 0
	r = x + y
	if r != 9223372036854775806 {
		t.Errorf("9223372036854775806 %s 0 = %d, want 9223372036854775806", "+", r)
	}
	y = 1
	r = x + y
	if r != 9223372036854775807 {
		t.Errorf("9223372036854775806 %s 1 = %d, want 9223372036854775807", "+", r)
	}
	y = 4294967296
	r = x + y
	if r != -9223372032559808514 {
		t.Errorf("9223372036854775806 %s 4294967296 = %d, want -9223372032559808514", "+", r)
	}
	y = 9223372036854775806
	r = x + y
	if r != -4 {
		t.Errorf("9223372036854775806 %s 9223372036854775806 = %d, want -4", "+", r)
	}
	y = 9223372036854775807
	r = x + y
	if r != -3 {
		t.Errorf("9223372036854775806 %s 9223372036854775807 = %d, want -3", "+", r)
	}
	x = 9223372036854775807
	y = -9223372036854775808
	r = x + y
	if r != -1 {
		t.Errorf("9223372036854775807 %s -9223372036854775808 = %d, want -1", "+", r)
	}
	y = -9223372036854775807
	r = x + y
	if r != 0 {
		t.Errorf("9223372036854775807 %s -9223372036854775807 = %d, want 0", "+", r)
	}
	y = -4294967296
	r = x + y
	if r != 9223372032559808511 {
		t.Errorf("9223372036854775807 %s -4294967296 = %d, want 9223372032559808511", "+", r)
	}
	y = -1
	r = x + y
	if r != 9223372036854775806 {
		t.Errorf("9223372036854775807 %s -1 = %d, want 9223372036854775806", "+", r)
	}
	y = 0
	r = x + y
	if r != 9223372036854775807 {
		t.Errorf("9223372036854775807 %s 0 = %d, want 9223372036854775807", "+", r)
	}
	y = 1
	r = x + y
	if r != -9223372036854775808 {
		t.Errorf("9223372036854775807 %s 1 = %d, want -9223372036854775808", "+", r)
	}
	y = 4294967296
	r = x + y
	if r != -9223372032559808513 {
		t.Errorf("9223372036854775807 %s 4294967296 = %d, want -9223372032559808513", "+", r)
	}
	y = 9223372036854775806
	r = x + y
	if r != -3 {
		t.Errorf("9223372036854775807 %s 9223372036854775806 = %d, want -3", "+", r)
	}
	y = 9223372036854775807
	r = x + y
	if r != -2 {
		t.Errorf("9223372036854775807 %s 9223372036854775807 = %d, want -2", "+", r)
	}
}
func TestConstFoldint64sub(t *testing.T) {
	var x, y, r int64
	x = -9223372036854775808
	y = -9223372036854775808
	r = x - y
	if r != 0 {
		t.Errorf("-9223372036854775808 %s -9223372036854775808 = %d, want 0", "-", r)
	}
	y = -9223372036854775807
	r = x - y
	if r != -1 {
		t.Errorf("-9223372036854775808 %s -9223372036854775807 = %d, want -1", "-", r)
	}
	y = -4294967296
	r = x - y
	if r != -9223372032559808512 {
		t.Errorf("-9223372036854775808 %s -4294967296 = %d, want -9223372032559808512", "-", r)
	}
	y = -1
	r = x - y
	if r != -9223372036854775807 {
		t.Errorf("-9223372036854775808 %s -1 = %d, want -9223372036854775807", "-", r)
	}
	y = 0
	r = x - y
	if r != -9223372036854775808 {
		t.Errorf("-9223372036854775808 %s 0 = %d, want -9223372036854775808", "-", r)
	}
	y = 1
	r = x - y
	if r != 9223372036854775807 {
		t.Errorf("-9223372036854775808 %s 1 = %d, want 9223372036854775807", "-", r)
	}
	y = 4294967296
	r = x - y
	if r != 9223372032559808512 {
		t.Errorf("-9223372036854775808 %s 4294967296 = %d, want 9223372032559808512", "-", r)
	}
	y = 9223372036854775806
	r = x - y
	if r != 2 {
		t.Errorf("-9223372036854775808 %s 9223372036854775806 = %d, want 2", "-", r)
	}
	y = 9223372036854775807
	r = x - y
	if r != 1 {
		t.Errorf("-9223372036854775808 %s 9223372036854775807 = %d, want 1", "-", r)
	}
	x = -9223372036854775807
	y = -9223372036854775808
	r = x - y
	if r != 1 {
		t.Errorf("-9223372036854775807 %s -9223372036854775808 = %d, want 1", "-", r)
	}
	y = -9223372036854775807
	r = x - y
	if r != 0 {
		t.Errorf("-9223372036854775807 %s -9223372036854775807 = %d, want 0", "-", r)
	}
	y = -4294967296
	r = x - y
	if r != -9223372032559808511 {
		t.Errorf("-9223372036854775807 %s -4294967296 = %d, want -9223372032559808511", "-", r)
	}
	y = -1
	r = x - y
	if r != -9223372036854775806 {
		t.Errorf("-9223372036854775807 %s -1 = %d, want -9223372036854775806", "-", r)
	}
	y = 0
	r = x - y
	if r != -9223372036854775807 {
		t.Errorf("-9223372036854775807 %s 0 = %d, want -9223372036854775807", "-", r)
	}
	y = 1
	r = x - y
	if r != -9223372036854775808 {
		t.Errorf("-9223372036854775807 %s 1 = %d, want -9223372036854775808", "-", r)
	}
	y = 4294967296
	r = x - y
	if r != 9223372032559808513 {
		t.Errorf("-9223372036854775807 %s 4294967296 = %d, want 9223372032559808513", "-", r)
	}
	y = 9223372036854775806
	r = x - y
	if r != 3 {
		t.Errorf("-9223372036854775807 %s 9223372036854775806 = %d, want 3", "-", r)
	}
	y = 9223372036854775807
	r = x - y
	if r != 2 {
		t.Errorf("-9223372036854775807 %s 9223372036854775807 = %d, want 2", "-", r)
	}
	x = -4294967296
	y = -9223372036854775808
	r = x - y
	if r != 9223372032559808512 {
		t.Errorf("-4294967296 %s -9223372036854775808 = %d, want 9223372032559808512", "-", r)
	}
	y = -9223372036854775807
	r = x - y
	if r != 9223372032559808511 {
		t.Errorf("-4294967296 %s -9223372036854775807 = %d, want 9223372032559808511", "-", r)
	}
	y = -4294967296
	r = x - y
	if r != 0 {
		t.Errorf("-4294967296 %s -4294967296 = %d, want 0", "-", r)
	}
	y = -1
	r = x - y
	if r != -4294967295 {
		t.Errorf("-4294967296 %s -1 = %d, want -4294967295", "-", r)
	}
	y = 0
	r = x - y
	if r != -4294967296 {
		t.Errorf("-4294967296 %s 0 = %d, want -4294967296", "-", r)
	}
	y = 1
	r = x - y
	if r != -4294967297 {
		t.Errorf("-4294967296 %s 1 = %d, want -4294967297", "-", r)
	}
	y = 4294967296
	r = x - y
	if r != -8589934592 {
		t.Errorf("-4294967296 %s 4294967296 = %d, want -8589934592", "-", r)
	}
	y = 9223372036854775806
	r = x - y
	if r != 9223372032559808514 {
		t.Errorf("-4294967296 %s 9223372036854775806 = %d, want 9223372032559808514", "-", r)
	}
	y = 9223372036854775807
	r = x - y
	if r != 9223372032559808513 {
		t.Errorf("-4294967296 %s 9223372036854775807 = %d, want 9223372032559808513", "-", r)
	}
	x = -1
	y = -9223372036854775808
	r = x - y
	if r != 9223372036854775807 {
		t.Errorf("-1 %s -9223372036854775808 = %d, want 9223372036854775807", "-", r)
	}
	y = -9223372036854775807
	r = x - y
	if r != 9223372036854775806 {
		t.Errorf("-1 %s -9223372036854775807 = %d, want 9223372036854775806", "-", r)
	}
	y = -4294967296
	r = x - y
	if r != 4294967295 {
		t.Errorf("-1 %s -4294967296 = %d, want 4294967295", "-", r)
	}
	y = -1
	r = x - y
	if r != 0 {
		t.Errorf("-1 %s -1 = %d, want 0", "-", r)
	}
	y = 0
	r = x - y
	if r != -1 {
		t.Errorf("-1 %s 0 = %d, want -1", "-", r)
	}
	y = 1
	r = x - y
	if r != -2 {
		t.Errorf("-1 %s 1 = %d, want -2", "-", r)
	}
	y = 4294967296
	r = x - y
	if r != -4294967297 {
		t.Errorf("-1 %s 4294967296 = %d, want -4294967297", "-", r)
	}
	y = 9223372036854775806
	r = x - y
	if r != -9223372036854775807 {
		t.Errorf("-1 %s 9223372036854775806 = %d, want -9223372036854775807", "-", r)
	}
	y = 9223372036854775807
	r = x - y
	if r != -9223372036854775808 {
		t.Errorf("-1 %s 9223372036854775807 = %d, want -9223372036854775808", "-", r)
	}
	x = 0
	y = -9223372036854775808
	r = x - y
	if r != -9223372036854775808 {
		t.Errorf("0 %s -9223372036854775808 = %d, want -9223372036854775808", "-", r)
	}
	y = -9223372036854775807
	r = x - y
	if r != 9223372036854775807 {
		t.Errorf("0 %s -9223372036854775807 = %d, want 9223372036854775807", "-", r)
	}
	y = -4294967296
	r = x - y
	if r != 4294967296 {
		t.Errorf("0 %s -4294967296 = %d, want 4294967296", "-", r)
	}
	y = -1
	r = x - y
	if r != 1 {
		t.Errorf("0 %s -1 = %d, want 1", "-", r)
	}
	y = 0
	r = x - y
	if r != 0 {
		t.Errorf("0 %s 0 = %d, want 0", "-", r)
	}
	y = 1
	r = x - y
	if r != -1 {
		t.Errorf("0 %s 1 = %d, want -1", "-", r)
	}
	y = 4294967296
	r = x - y
	if r != -4294967296 {
		t.Errorf("0 %s 4294967296 = %d, want -4294967296", "-", r)
	}
	y = 9223372036854775806
	r = x - y
	if r != -9223372036854775806 {
		t.Errorf("0 %s 9223372036854775806 = %d, want -9223372036854775806", "-", r)
	}
	y = 9223372036854775807
	r = x - y
	if r != -9223372036854775807 {
		t.Errorf("0 %s 9223372036854775807 = %d, want -9223372036854775807", "-", r)
	}
	x = 1
	y = -9223372036854775808
	r = x - y
	if r != -9223372036854775807 {
		t.Errorf("1 %s -9223372036854775808 = %d, want -9223372036854775807", "-", r)
	}
	y = -9223372036854775807
	r = x - y
	if r != -9223372036854775808 {
		t.Errorf("1 %s -9223372036854775807 = %d, want -9223372036854775808", "-", r)
	}
	y = -4294967296
	r = x - y
	if r != 4294967297 {
		t.Errorf("1 %s -4294967296 = %d, want 4294967297", "-", r)
	}
	y = -1
	r = x - y
	if r != 2 {
		t.Errorf("1 %s -1 = %d, want 2", "-", r)
	}
	y = 0
	r = x - y
	if r != 1 {
		t.Errorf("1 %s 0 = %d, want 1", "-", r)
	}
	y = 1
	r = x - y
	if r != 0 {
		t.Errorf("1 %s 1 = %d, want 0", "-", r)
	}
	y = 4294967296
	r = x - y
	if r != -4294967295 {
		t.Errorf("1 %s 4294967296 = %d, want -4294967295", "-", r)
	}
	y = 9223372036854775806
	r = x - y
	if r != -9223372036854775805 {
		t.Errorf("1 %s 9223372036854775806 = %d, want -9223372036854775805", "-", r)
	}
	y = 9223372036854775807
	r = x - y
	if r != -9223372036854775806 {
		t.Errorf("1 %s 9223372036854775807 = %d, want -9223372036854775806", "-", r)
	}
	x = 4294967296
	y = -9223372036854775808
	r = x - y
	if r != -9223372032559808512 {
		t.Errorf("4294967296 %s -9223372036854775808 = %d, want -9223372032559808512", "-", r)
	}
	y = -9223372036854775807
	r = x - y
	if r != -9223372032559808513 {
		t.Errorf("4294967296 %s -9223372036854775807 = %d, want -9223372032559808513", "-", r)
	}
	y = -4294967296
	r = x - y
	if r != 8589934592 {
		t.Errorf("4294967296 %s -4294967296 = %d, want 8589934592", "-", r)
	}
	y = -1
	r = x - y
	if r != 4294967297 {
		t.Errorf("4294967296 %s -1 = %d, want 4294967297", "-", r)
	}
	y = 0
	r = x - y
	if r != 4294967296 {
		t.Errorf("4294967296 %s 0 = %d, want 4294967296", "-", r)
	}
	y = 1
	r = x - y
	if r != 4294967295 {
		t.Errorf("4294967296 %s 1 = %d, want 4294967295", "-", r)
	}
	y = 4294967296
	r = x - y
	if r != 0 {
		t.Errorf("4294967296 %s 4294967296 = %d, want 0", "-", r)
	}
	y = 9223372036854775806
	r = x - y
	if r != -9223372032559808510 {
		t.Errorf("4294967296 %s 9223372036854775806 = %d, want -9223372032559808510", "-", r)
	}
	y = 9223372036854775807
	r = x - y
	if r != -9223372032559808511 {
		t.Errorf("4294967296 %s 9223372036854775807 = %d, want -9223372032559808511", "-", r)
	}
	x = 9223372036854775806
	y = -9223372036854775808
	r = x - y
	if r != -2 {
		t.Errorf("9223372036854775806 %s -9223372036854775808 = %d, want -2", "-", r)
	}
	y = -9223372036854775807
	r = x - y
	if r != -3 {
		t.Errorf("9223372036854775806 %s -9223372036854775807 = %d, want -3", "-", r)
	}
	y = -4294967296
	r = x - y
	if r != -9223372032559808514 {
		t.Errorf("9223372036854775806 %s -4294967296 = %d, want -9223372032559808514", "-", r)
	}
	y = -1
	r = x - y
	if r != 9223372036854775807 {
		t.Errorf("9223372036854775806 %s -1 = %d, want 9223372036854775807", "-", r)
	}
	y = 0
	r = x - y
	if r != 9223372036854775806 {
		t.Errorf("9223372036854775806 %s 0 = %d, want 9223372036854775806", "-", r)
	}
	y = 1
	r = x - y
	if r != 9223372036854775805 {
		t.Errorf("9223372036854775806 %s 1 = %d, want 9223372036854775805", "-", r)
	}
	y = 4294967296
	r = x - y
	if r != 9223372032559808510 {
		t.Errorf("9223372036854775806 %s 4294967296 = %d, want 9223372032559808510", "-", r)
	}
	y = 9223372036854775806
	r = x - y
	if r != 0 {
		t.Errorf("9223372036854775806 %s 9223372036854775806 = %d, want 0", "-", r)
	}
	y = 9223372036854775807
	r = x - y
	if r != -1 {
		t.Errorf("9223372036854775806 %s 9223372036854775807 = %d, want -1", "-", r)
	}
	x = 9223372036854775807
	y = -9223372036854775808
	r = x - y
	if r != -1 {
		t.Errorf("9223372036854775807 %s -9223372036854775808 = %d, want -1", "-", r)
	}
	y = -9223372036854775807
	r = x - y
	if r != -2 {
		t.Errorf("9223372036854775807 %s -9223372036854775807 = %d, want -2", "-", r)
	}
	y = -4294967296
	r = x - y
	if r != -9223372032559808513 {
		t.Errorf("9223372036854775807 %s -4294967296 = %d, want -9223372032559808513", "-", r)
	}
	y = -1
	r = x - y
	if r != -9223372036854775808 {
		t.Errorf("9223372036854775807 %s -1 = %d, want -9223372036854775808", "-", r)
	}
	y = 0
	r = x - y
	if r != 9223372036854775807 {
		t.Errorf("9223372036854775807 %s 0 = %d, want 9223372036854775807", "-", r)
	}
	y = 1
	r = x - y
	if r != 9223372036854775806 {
		t.Errorf("9223372036854775807 %s 1 = %d, want 9223372036854775806", "-", r)
	}
	y = 4294967296
	r = x - y
	if r != 9223372032559808511 {
		t.Errorf("9223372036854775807 %s 4294967296 = %d, want 9223372032559808511", "-", r)
	}
	y = 9223372036854775806
	r = x - y
	if r != 1 {
		t.Errorf("9223372036854775807 %s 9223372036854775806 = %d, want 1", "-", r)
	}
	y = 9223372036854775807
	r = x - y
	if r != 0 {
		t.Errorf("9223372036854775807 %s 9223372036854775807 = %d, want 0", "-", r)
	}
}
func TestConstFoldint64div(t *testing.T) {
	var x, y, r int64
	x = -9223372036854775808
	y = -9223372036854775808
	r = x / y
	if r != 1 {
		t.Errorf("-9223372036854775808 %s -9223372036854775808 = %d, want 1", "/", r)
	}
	y = -9223372036854775807
	r = x / y
	if r != 1 {
		t.Errorf("-9223372036854775808 %s -9223372036854775807 = %d, want 1", "/", r)
	}
	y = -4294967296
	r = x / y
	if r != 2147483648 {
		t.Errorf("-9223372036854775808 %s -4294967296 = %d, want 2147483648", "/", r)
	}
	y = -1
	r = x / y
	if r != -9223372036854775808 {
		t.Errorf("-9223372036854775808 %s -1 = %d, want -9223372036854775808", "/", r)
	}
	y = 1
	r = x / y
	if r != -9223372036854775808 {
		t.Errorf("-9223372036854775808 %s 1 = %d, want -9223372036854775808", "/", r)
	}
	y = 4294967296
	r = x / y
	if r != -2147483648 {
		t.Errorf("-9223372036854775808 %s 4294967296 = %d, want -2147483648", "/", r)
	}
	y = 9223372036854775806
	r = x / y
	if r != -1 {
		t.Errorf("-9223372036854775808 %s 9223372036854775806 = %d, want -1", "/", r)
	}
	y = 9223372036854775807
	r = x / y
	if r != -1 {
		t.Errorf("-9223372036854775808 %s 9223372036854775807 = %d, want -1", "/", r)
	}
	x = -9223372036854775807
	y = -9223372036854775808
	r = x / y
	if r != 0 {
		t.Errorf("-9223372036854775807 %s -9223372036854775808 = %d, want 0", "/", r)
	}
	y = -9223372036854775807
	r = x / y
	if r != 1 {
		t.Errorf("-9223372036854775807 %s -9223372036854775807 = %d, want 1", "/", r)
	}
	y = -4294967296
	r = x / y
	if r != 2147483647 {
		t.Errorf("-9223372036854775807 %s -4294967296 = %d, want 2147483647", "/", r)
	}
	y = -1
	r = x / y
	if r != 9223372036854775807 {
		t.Errorf("-9223372036854775807 %s -1 = %d, want 9223372036854775807", "/", r)
	}
	y = 1
	r = x / y
	if r != -9223372036854775807 {
		t.Errorf("-9223372036854775807 %s 1 = %d, want -9223372036854775807", "/", r)
	}
	y = 4294967296
	r = x / y
	if r != -2147483647 {
		t.Errorf("-9223372036854775807 %s 4294967296 = %d, want -2147483647", "/", r)
	}
	y = 9223372036854775806
	r = x / y
	if r != -1 {
		t.Errorf("-9223372036854775807 %s 9223372036854775806 = %d, want -1", "/", r)
	}
	y = 9223372036854775807
	r = x / y
	if r != -1 {
		t.Errorf("-9223372036854775807 %s 9223372036854775807 = %d, want -1", "/", r)
	}
	x = -4294967296
	y = -9223372036854775808
	r = x / y
	if r != 0 {
		t.Errorf("-4294967296 %s -9223372036854775808 = %d, want 0", "/", r)
	}
	y = -9223372036854775807
	r = x / y
	if r != 0 {
		t.Errorf("-4294967296 %s -9223372036854775807 = %d, want 0", "/", r)
	}
	y = -4294967296
	r = x / y
	if r != 1 {
		t.Errorf("-4294967296 %s -4294967296 = %d, want 1", "/", r)
	}
	y = -1
	r = x / y
	if r != 4294967296 {
		t.Errorf("-4294967296 %s -1 = %d, want 4294967296", "/", r)
	}
	y = 1
	r = x / y
	if r != -4294967296 {
		t.Errorf("-4294967296 %s 1 = %d, want -4294967296", "/", r)
	}
	y = 4294967296
	r = x / y
	if r != -1 {
		t.Errorf("-4294967296 %s 4294967296 = %d, want -1", "/", r)
	}
	y = 9223372036854775806
	r = x / y
	if r != 0 {
		t.Errorf("-4294967296 %s 9223372036854775806 = %d, want 0", "/", r)
	}
	y = 9223372036854775807
	r = x / y
	if r != 0 {
		t.Errorf("-4294967296 %s 9223372036854775807 = %d, want 0", "/", r)
	}
	x = -1
	y = -9223372036854775808
	r = x / y
	if r != 0 {
		t.Errorf("-1 %s -9223372036854775808 = %d, want 0", "/", r)
	}
	y = -9223372036854775807
	r = x / y
	if r != 0 {
		t.Errorf("-1 %s -9223372036854775807 = %d, want 0", "/", r)
	}
	y = -4294967296
	r = x / y
	if r != 0 {
		t.Errorf("-1 %s -4294967296 = %d, want 0", "/", r)
	}
	y = -1
	r = x / y
	if r != 1 {
		t.Errorf("-1 %s -1 = %d, want 1", "/", r)
	}
	y = 1
	r = x / y
	if r != -1 {
		t.Errorf("-1 %s 1 = %d, want -1", "/", r)
	}
	y = 4294967296
	r = x / y
	if r != 0 {
		t.Errorf("-1 %s 4294967296 = %d, want 0", "/", r)
	}
	y = 9223372036854775806
	r = x / y
	if r != 0 {
		t.Errorf("-1 %s 9223372036854775806 = %d, want 0", "/", r)
	}
	y = 9223372036854775807
	r = x / y
	if r != 0 {
		t.Errorf("-1 %s 9223372036854775807 = %d, want 0", "/", r)
	}
	x = 0
	y = -9223372036854775808
	r = x / y
	if r != 0 {
		t.Errorf("0 %s -9223372036854775808 = %d, want 0", "/", r)
	}
	y = -9223372036854775807
	r = x / y
	if r != 0 {
		t.Errorf("0 %s -9223372036854775807 = %d, want 0", "/", r)
	}
	y = -4294967296
	r = x / y
	if r != 0 {
		t.Errorf("0 %s -4294967296 = %d, want 0", "/", r)
	}
	y = -1
	r = x / y
	if r != 0 {
		t.Errorf("0 %s -1 = %d, want 0", "/", r)
	}
	y = 1
	r = x / y
	if r != 0 {
		t.Errorf("0 %s 1 = %d, want 0", "/", r)
	}
	y = 4294967296
	r = x / y
	if r != 0 {
		t.Errorf("0 %s 4294967296 = %d, want 0", "/", r)
	}
	y = 9223372036854775806
	r = x / y
	if r != 0 {
		t.Errorf("0 %s 9223372036854775806 = %d, want 0", "/", r)
	}
	y = 9223372036854775807
	r = x / y
	if r != 0 {
		t.Errorf("0 %s 9223372036854775807 = %d, want 0", "/", r)
	}
	x = 1
	y = -9223372036854775808
	r = x / y
	if r != 0 {
		t.Errorf("1 %s -9223372036854775808 = %d, want 0", "/", r)
	}
	y = -9223372036854775807
	r = x / y
	if r != 0 {
		t.Errorf("1 %s -9223372036854775807 = %d, want 0", "/", r)
	}
	y = -4294967296
	r = x / y
	if r != 0 {
		t.Errorf("1 %s -4294967296 = %d, want 0", "/", r)
	}
	y = -1
	r = x / y
	if r != -1 {
		t.Errorf("1 %s -1 = %d, want -1", "/", r)
	}
	y = 1
	r = x / y
	if r != 1 {
		t.Errorf("1 %s 1 = %d, want 1", "/", r)
	}
	y = 4294967296
	r = x / y
	if r != 0 {
		t.Errorf("1 %s 4294967296 = %d, want 0", "/", r)
	}
	y = 9223372036854775806
	r = x / y
	if r != 0 {
		t.Errorf("1 %s 9223372036854775806 = %d, want 0", "/", r)
	}
	y = 9223372036854775807
	r = x / y
	if r != 0 {
		t.Errorf("1 %s 9223372036854775807 = %d, want 0", "/", r)
	}
	x = 4294967296
	y = -9223372036854775808
	r = x / y
	if r != 0 {
		t.Errorf("4294967296 %s -9223372036854775808 = %d, want 0", "/", r)
	}
	y = -9223372036854775807
	r = x / y
	if r != 0 {
		t.Errorf("4294967296 %s -9223372036854775807 = %d, want 0", "/", r)
	}
	y = -4294967296
	r = x / y
	if r != -1 {
		t.Errorf("4294967296 %s -4294967296 = %d, want -1", "/", r)
	}
	y = -1
	r = x / y
	if r != -4294967296 {
		t.Errorf("4294967296 %s -1 = %d, want -4294967296", "/", r)
	}
	y = 1
	r = x / y
	if r != 4294967296 {
		t.Errorf("4294967296 %s 1 = %d, want 4294967296", "/", r)
	}
	y = 4294967296
	r = x / y
	if r != 1 {
		t.Errorf("4294967296 %s 4294967296 = %d, want 1", "/", r)
	}
	y = 9223372036854775806
	r = x / y
	if r != 0 {
		t.Errorf("4294967296 %s 9223372036854775806 = %d, want 0", "/", r)
	}
	y = 9223372036854775807
	r = x / y
	if r != 0 {
		t.Errorf("4294967296 %s 9223372036854775807 = %d, want 0", "/", r)
	}
	x = 9223372036854775806
	y = -9223372036854775808
	r = x / y
	if r != 0 {
		t.Errorf("9223372036854775806 %s -9223372036854775808 = %d, want 0", "/", r)
	}
	y = -9223372036854775807
	r = x / y
	if r != 0 {
		t.Errorf("9223372036854775806 %s -9223372036854775807 = %d, want 0", "/", r)
	}
	y = -4294967296
	r = x / y
	if r != -2147483647 {
		t.Errorf("9223372036854775806 %s -4294967296 = %d, want -2147483647", "/", r)
	}
	y = -1
	r = x / y
	if r != -9223372036854775806 {
		t.Errorf("9223372036854775806 %s -1 = %d, want -9223372036854775806", "/", r)
	}
	y = 1
	r = x / y
	if r != 9223372036854775806 {
		t.Errorf("9223372036854775806 %s 1 = %d, want 9223372036854775806", "/", r)
	}
	y = 4294967296
	r = x / y
	if r != 2147483647 {
		t.Errorf("9223372036854775806 %s 4294967296 = %d, want 2147483647", "/", r)
	}
	y = 9223372036854775806
	r = x / y
	if r != 1 {
		t.Errorf("9223372036854775806 %s 9223372036854775806 = %d, want 1", "/", r)
	}
	y = 9223372036854775807
	r = x / y
	if r != 0 {
		t.Errorf("9223372036854775806 %s 9223372036854775807 = %d, want 0", "/", r)
	}
	x = 9223372036854775807
	y = -9223372036854775808
	r = x / y
	if r != 0 {
		t.Errorf("9223372036854775807 %s -9223372036854775808 = %d, want 0", "/", r)
	}
	y = -9223372036854775807
	r = x / y
	if r != -1 {
		t.Errorf("9223372036854775807 %s -9223372036854775807 = %d, want -1", "/", r)
	}
	y = -4294967296
	r = x / y
	if r != -2147483647 {
		t.Errorf("9223372036854775807 %s -4294967296 = %d, want -2147483647", "/", r)
	}
	y = -1
	r = x / y
	if r != -9223372036854775807 {
		t.Errorf("9223372036854775807 %s -1 = %d, want -9223372036854775807", "/", r)
	}
	y = 1
	r = x / y
	if r != 9223372036854775807 {
		t.Errorf("9223372036854775807 %s 1 = %d, want 9223372036854775807", "/", r)
	}
	y = 4294967296
	r = x / y
	if r != 2147483647 {
		t.Errorf("9223372036854775807 %s 4294967296 = %d, want 2147483647", "/", r)
	}
	y = 9223372036854775806
	r = x / y
	if r != 1 {
		t.Errorf("9223372036854775807 %s 9223372036854775806 = %d, want 1", "/", r)
	}
	y = 9223372036854775807
	r = x / y
	if r != 1 {
		t.Errorf("9223372036854775807 %s 9223372036854775807 = %d, want 1", "/", r)
	}
}
func TestConstFoldint64mul(t *testing.T) {
	var x, y, r int64
	x = -9223372036854775808
	y = -9223372036854775808
	r = x * y
	if r != 0 {
		t.Errorf("-9223372036854775808 %s -9223372036854775808 = %d, want 0", "*", r)
	}
	y = -9223372036854775807
	r = x * y
	if r != -9223372036854775808 {
		t.Errorf("-9223372036854775808 %s -9223372036854775807 = %d, want -9223372036854775808", "*", r)
	}
	y = -4294967296
	r = x * y
	if r != 0 {
		t.Errorf("-9223372036854775808 %s -4294967296 = %d, want 0", "*", r)
	}
	y = -1
	r = x * y
	if r != -9223372036854775808 {
		t.Errorf("-9223372036854775808 %s -1 = %d, want -9223372036854775808", "*", r)
	}
	y = 0
	r = x * y
	if r != 0 {
		t.Errorf("-9223372036854775808 %s 0 = %d, want 0", "*", r)
	}
	y = 1
	r = x * y
	if r != -9223372036854775808 {
		t.Errorf("-9223372036854775808 %s 1 = %d, want -9223372036854775808", "*", r)
	}
	y = 4294967296
	r = x * y
	if r != 0 {
		t.Errorf("-9223372036854775808 %s 4294967296 = %d, want 0", "*", r)
	}
	y = 9223372036854775806
	r = x * y
	if r != 0 {
		t.Errorf("-9223372036854775808 %s 9223372036854775806 = %d, want 0", "*", r)
	}
	y = 9223372036854775807
	r = x * y
	if r != -9223372036854775808 {
		t.Errorf("-9223372036854775808 %s 9223372036854775807 = %d, want -9223372036854775808", "*", r)
	}
	x = -9223372036854775807
	y = -9223372036854775808
	r = x * y
	if r != -9223372036854775808 {
		t.Errorf("-9223372036854775807 %s -9223372036854775808 = %d, want -9223372036854775808", "*", r)
	}
	y = -9223372036854775807
	r = x * y
	if r != 1 {
		t.Errorf("-9223372036854775807 %s -9223372036854775807 = %d, want 1", "*", r)
	}
	y = -4294967296
	r = x * y
	if r != -4294967296 {
		t.Errorf("-9223372036854775807 %s -4294967296 = %d, want -4294967296", "*", r)
	}
	y = -1
	r = x * y
	if r != 9223372036854775807 {
		t.Errorf("-9223372036854775807 %s -1 = %d, want 9223372036854775807", "*", r)
	}
	y = 0
	r = x * y
	if r != 0 {
		t.Errorf("-9223372036854775807 %s 0 = %d, want 0", "*", r)
	}
	y = 1
	r = x * y
	if r != -9223372036854775807 {
		t.Errorf("-9223372036854775807 %s 1 = %d, want -9223372036854775807", "*", r)
	}
	y = 4294967296
	r = x * y
	if r != 4294967296 {
		t.Errorf("-9223372036854775807 %s 4294967296 = %d, want 4294967296", "*", r)
	}
	y = 9223372036854775806
	r = x * y
	if r != 9223372036854775806 {
		t.Errorf("-9223372036854775807 %s 9223372036854775806 = %d, want 9223372036854775806", "*", r)
	}
	y = 9223372036854775807
	r = x * y
	if r != -1 {
		t.Errorf("-9223372036854775807 %s 9223372036854775807 = %d, want -1", "*", r)
	}
	x = -4294967296
	y = -9223372036854775808
	r = x * y
	if r != 0 {
		t.Errorf("-4294967296 %s -9223372036854775808 = %d, want 0", "*", r)
	}
	y = -9223372036854775807
	r = x * y
	if r != -4294967296 {
		t.Errorf("-4294967296 %s -9223372036854775807 = %d, want -4294967296", "*", r)
	}
	y = -4294967296
	r = x * y
	if r != 0 {
		t.Errorf("-4294967296 %s -4294967296 = %d, want 0", "*", r)
	}
	y = -1
	r = x * y
	if r != 4294967296 {
		t.Errorf("-4294967296 %s -1 = %d, want 4294967296", "*", r)
	}
	y = 0
	r = x * y
	if r != 0 {
		t.Errorf("-4294967296 %s 0 = %d, want 0", "*", r)
	}
	y = 1
	r = x * y
	if r != -4294967296 {
		t.Errorf("-4294967296 %s 1 = %d, want -4294967296", "*", r)
	}
	y = 4294967296
	r = x * y
	if r != 0 {
		t.Errorf("-4294967296 %s 4294967296 = %d, want 0", "*", r)
	}
	y = 9223372036854775806
	r = x * y
	if r != 8589934592 {
		t.Errorf("-4294967296 %s 9223372036854775806 = %d, want 8589934592", "*", r)
	}
	y = 9223372036854775807
	r = x * y
	if r != 4294967296 {
		t.Errorf("-4294967296 %s 9223372036854775807 = %d, want 4294967296", "*", r)
	}
	x = -1
	y = -9223372036854775808
	r = x * y
	if r != -9223372036854775808 {
		t.Errorf("-1 %s -9223372036854775808 = %d, want -9223372036854775808", "*", r)
	}
	y = -9223372036854775807
	r = x * y
	if r != 9223372036854775807 {
		t.Errorf("-1 %s -9223372036854775807 = %d, want 9223372036854775807", "*", r)
	}
	y = -4294967296
	r = x * y
	if r != 4294967296 {
		t.Errorf("-1 %s -4294967296 = %d, want 4294967296", "*", r)
	}
	y = -1
	r = x * y
	if r != 1 {
		t.Errorf("-1 %s -1 = %d, want 1", "*", r)
	}
	y = 0
	r = x * y
	if r != 0 {
		t.Errorf("-1 %s 0 = %d, want 0", "*", r)
	}
	y = 1
	r = x * y
	if r != -1 {
		t.Errorf("-1 %s 1 = %d, want -1", "*", r)
	}
	y = 4294967296
	r = x * y
	if r != -4294967296 {
		t.Errorf("-1 %s 4294967296 = %d, want -4294967296", "*", r)
	}
	y = 9223372036854775806
	r = x * y
	if r != -9223372036854775806 {
		t.Errorf("-1 %s 9223372036854775806 = %d, want -9223372036854775806", "*", r)
	}
	y = 9223372036854775807
	r = x * y
	if r != -9223372036854775807 {
		t.Errorf("-1 %s 9223372036854775807 = %d, want -9223372036854775807", "*", r)
	}
	x = 0
	y = -9223372036854775808
	r = x * y
	if r != 0 {
		t.Errorf("0 %s -9223372036854775808 = %d, want 0", "*", r)
	}
	y = -9223372036854775807
	r = x * y
	if r != 0 {
		t.Errorf("0 %s -9223372036854775807 = %d, want 0", "*", r)
	}
	y = -4294967296
	r = x * y
	if r != 0 {
		t.Errorf("0 %s -4294967296 = %d, want 0", "*", r)
	}
	y = -1
	r = x * y
	if r != 0 {
		t.Errorf("0 %s -1 = %d, want 0", "*", r)
	}
	y = 0
	r = x * y
	if r != 0 {
		t.Errorf("0 %s 0 = %d, want 0", "*", r)
	}
	y = 1
	r = x * y
	if r != 0 {
		t.Errorf("0 %s 1 = %d, want 0", "*", r)
	}
	y = 4294967296
	r = x * y
	if r != 0 {
		t.Errorf("0 %s 4294967296 = %d, want 0", "*", r)
	}
	y = 9223372036854775806
	r = x * y
	if r != 0 {
		t.Errorf("0 %s 9223372036854775806 = %d, want 0", "*", r)
	}
	y = 9223372036854775807
	r = x * y
	if r != 0 {
		t.Errorf("0 %s 9223372036854775807 = %d, want 0", "*", r)
	}
	x = 1
	y = -9223372036854775808
	r = x * y
	if r != -9223372036854775808 {
		t.Errorf("1 %s -9223372036854775808 = %d, want -9223372036854775808", "*", r)
	}
	y = -9223372036854775807
	r = x * y
	if r != -9223372036854775807 {
		t.Errorf("1 %s -9223372036854775807 = %d, want -9223372036854775807", "*", r)
	}
	y = -4294967296
	r = x * y
	if r != -4294967296 {
		t.Errorf("1 %s -4294967296 = %d, want -4294967296", "*", r)
	}
	y = -1
	r = x * y
	if r != -1 {
		t.Errorf("1 %s -1 = %d, want -1", "*", r)
	}
	y = 0
	r = x * y
	if r != 0 {
		t.Errorf("1 %s 0 = %d, want 0", "*", r)
	}
	y = 1
	r = x * y
	if r != 1 {
		t.Errorf("1 %s 1 = %d, want 1", "*", r)
	}
	y = 4294967296
	r = x * y
	if r != 4294967296 {
		t.Errorf("1 %s 4294967296 = %d, want 4294967296", "*", r)
	}
	y = 9223372036854775806
	r = x * y
	if r != 9223372036854775806 {
		t.Errorf("1 %s 9223372036854775806 = %d, want 9223372036854775806", "*", r)
	}
	y = 9223372036854775807
	r = x * y
	if r != 9223372036854775807 {
		t.Errorf("1 %s 9223372036854775807 = %d, want 9223372036854775807", "*", r)
	}
	x = 4294967296
	y = -9223372036854775808
	r = x * y
	if r != 0 {
		t.Errorf("4294967296 %s -9223372036854775808 = %d, want 0", "*", r)
	}
	y = -9223372036854775807
	r = x * y
	if r != 4294967296 {
		t.Errorf("4294967296 %s -9223372036854775807 = %d, want 4294967296", "*", r)
	}
	y = -4294967296
	r = x * y
	if r != 0 {
		t.Errorf("4294967296 %s -4294967296 = %d, want 0", "*", r)
	}
	y = -1
	r = x * y
	if r != -4294967296 {
		t.Errorf("4294967296 %s -1 = %d, want -4294967296", "*", r)
	}
	y = 0
	r = x * y
	if r != 0 {
		t.Errorf("4294967296 %s 0 = %d, want 0", "*", r)
	}
	y = 1
	r = x * y
	if r != 4294967296 {
		t.Errorf("4294967296 %s 1 = %d, want 4294967296", "*", r)
	}
	y = 4294967296
	r = x * y
	if r != 0 {
		t.Errorf("4294967296 %s 4294967296 = %d, want 0", "*", r)
	}
	y = 9223372036854775806
	r = x * y
	if r != -8589934592 {
		t.Errorf("4294967296 %s 9223372036854775806 = %d, want -8589934592", "*", r)
	}
	y = 9223372036854775807
	r = x * y
	if r != -4294967296 {
		t.Errorf("4294967296 %s 9223372036854775807 = %d, want -4294967296", "*", r)
	}
	x = 9223372036854775806
	y = -9223372036854775808
	r = x * y
	if r != 0 {
		t.Errorf("9223372036854775806 %s -9223372036854775808 = %d, want 0", "*", r)
	}
	y = -9223372036854775807
	r = x * y
	if r != 9223372036854775806 {
		t.Errorf("9223372036854775806 %s -9223372036854775807 = %d, want 9223372036854775806", "*", r)
	}
	y = -4294967296
	r = x * y
	if r != 8589934592 {
		t.Errorf("9223372036854775806 %s -4294967296 = %d, want 8589934592", "*", r)
	}
	y = -1
	r = x * y
	if r != -9223372036854775806 {
		t.Errorf("9223372036854775806 %s -1 = %d, want -9223372036854775806", "*", r)
	}
	y = 0
	r = x * y
	if r != 0 {
		t.Errorf("9223372036854775806 %s 0 = %d, want 0", "*", r)
	}
	y = 1
	r = x * y
	if r != 9223372036854775806 {
		t.Errorf("9223372036854775806 %s 1 = %d, want 9223372036854775806", "*", r)
	}
	y = 4294967296
	r = x * y
	if r != -8589934592 {
		t.Errorf("9223372036854775806 %s 4294967296 = %d, want -8589934592", "*", r)
	}
	y = 9223372036854775806
	r = x * y
	if r != 4 {
		t.Errorf("9223372036854775806 %s 9223372036854775806 = %d, want 4", "*", r)
	}
	y = 9223372036854775807
	r = x * y
	if r != -9223372036854775806 {
		t.Errorf("9223372036854775806 %s 9223372036854775807 = %d, want -9223372036854775806", "*", r)
	}
	x = 9223372036854775807
	y = -9223372036854775808
	r = x * y
	if r != -9223372036854775808 {
		t.Errorf("9223372036854775807 %s -9223372036854775808 = %d, want -9223372036854775808", "*", r)
	}
	y = -9223372036854775807
	r = x * y
	if r != -1 {
		t.Errorf("9223372036854775807 %s -9223372036854775807 = %d, want -1", "*", r)
	}
	y = -4294967296
	r = x * y
	if r != 4294967296 {
		t.Errorf("9223372036854775807 %s -4294967296 = %d, want 4294967296", "*", r)
	}
	y = -1
	r = x * y
	if r != -9223372036854775807 {
		t.Errorf("9223372036854775807 %s -1 = %d, want -9223372036854775807", "*", r)
	}
	y = 0
	r = x * y
	if r != 0 {
		t.Errorf("9223372036854775807 %s 0 = %d, want 0", "*", r)
	}
	y = 1
	r = x * y
	if r != 9223372036854775807 {
		t.Errorf("9223372036854775807 %s 1 = %d, want 9223372036854775807", "*", r)
	}
	y = 4294967296
	r = x * y
	if r != -4294967296 {
		t.Errorf("9223372036854775807 %s 4294967296 = %d, want -4294967296", "*", r)
	}
	y = 9223372036854775806
	r = x * y
	if r != -9223372036854775806 {
		t.Errorf("9223372036854775807 %s 9223372036854775806 = %d, want -9223372036854775806", "*", r)
	}
	y = 9223372036854775807
	r = x * y
	if r != 1 {
		t.Errorf("9223372036854775807 %s 9223372036854775807 = %d, want 1", "*", r)
	}
}
func TestConstFoldint64mod(t *testing.T) {
	var x, y, r int64
	x = -9223372036854775808
	y = -9223372036854775808
	r = x % y
	if r != 0 {
		t.Errorf("-9223372036854775808 %s -9223372036854775808 = %d, want 0", "%", r)
	}
	y = -9223372036854775807
	r = x % y
	if r != -1 {
		t.Errorf("-9223372036854775808 %s -9223372036854775807 = %d, want -1", "%", r)
	}
	y = -4294967296
	r = x % y
	if r != 0 {
		t.Errorf("-9223372036854775808 %s -4294967296 = %d, want 0", "%", r)
	}
	y = -1
	r = x % y
	if r != 0 {
		t.Errorf("-9223372036854775808 %s -1 = %d, want 0", "%", r)
	}
	y = 1
	r = x % y
	if r != 0 {
		t.Errorf("-9223372036854775808 %s 1 = %d, want 0", "%", r)
	}
	y = 4294967296
	r = x % y
	if r != 0 {
		t.Errorf("-9223372036854775808 %s 4294967296 = %d, want 0", "%", r)
	}
	y = 9223372036854775806
	r = x % y
	if r != -2 {
		t.Errorf("-9223372036854775808 %s 9223372036854775806 = %d, want -2", "%", r)
	}
	y = 9223372036854775807
	r = x % y
	if r != -1 {
		t.Errorf("-9223372036854775808 %s 9223372036854775807 = %d, want -1", "%", r)
	}
	x = -9223372036854775807
	y = -9223372036854775808
	r = x % y
	if r != -9223372036854775807 {
		t.Errorf("-9223372036854775807 %s -9223372036854775808 = %d, want -9223372036854775807", "%", r)
	}
	y = -9223372036854775807
	r = x % y
	if r != 0 {
		t.Errorf("-9223372036854775807 %s -9223372036854775807 = %d, want 0", "%", r)
	}
	y = -4294967296
	r = x % y
	if r != -4294967295 {
		t.Errorf("-9223372036854775807 %s -4294967296 = %d, want -4294967295", "%", r)
	}
	y = -1
	r = x % y
	if r != 0 {
		t.Errorf("-9223372036854775807 %s -1 = %d, want 0", "%", r)
	}
	y = 1
	r = x % y
	if r != 0 {
		t.Errorf("-9223372036854775807 %s 1 = %d, want 0", "%", r)
	}
	y = 4294967296
	r = x % y
	if r != -4294967295 {
		t.Errorf("-9223372036854775807 %s 4294967296 = %d, want -4294967295", "%", r)
	}
	y = 9223372036854775806
	r = x % y
	if r != -1 {
		t.Errorf("-9223372036854775807 %s 9223372036854775806 = %d, want -1", "%", r)
	}
	y = 9223372036854775807
	r = x % y
	if r != 0 {
		t.Errorf("-9223372036854775807 %s 9223372036854775807 = %d, want 0", "%", r)
	}
	x = -4294967296
	y = -9223372036854775808
	r = x % y
	if r != -4294967296 {
		t.Errorf("-4294967296 %s -9223372036854775808 = %d, want -4294967296", "%", r)
	}
	y = -9223372036854775807
	r = x % y
	if r != -4294967296 {
		t.Errorf("-4294967296 %s -9223372036854775807 = %d, want -4294967296", "%", r)
	}
	y = -4294967296
	r = x % y
	if r != 0 {
		t.Errorf("-4294967296 %s -4294967296 = %d, want 0", "%", r)
	}
	y = -1
	r = x % y
	if r != 0 {
		t.Errorf("-4294967296 %s -1 = %d, want 0", "%", r)
	}
	y = 1
	r = x % y
	if r != 0 {
		t.Errorf("-4294967296 %s 1 = %d, want 0", "%", r)
	}
	y = 4294967296
	r = x % y
	if r != 0 {
		t.Errorf("-4294967296 %s 4294967296 = %d, want 0", "%", r)
	}
	y = 9223372036854775806
	r = x % y
	if r != -4294967296 {
		t.Errorf("-4294967296 %s 9223372036854775806 = %d, want -4294967296", "%", r)
	}
	y = 9223372036854775807
	r = x % y
	if r != -4294967296 {
		t.Errorf("-4294967296 %s 9223372036854775807 = %d, want -4294967296", "%", r)
	}
	x = -1
	y = -9223372036854775808
	r = x % y
	if r != -1 {
		t.Errorf("-1 %s -9223372036854775808 = %d, want -1", "%", r)
	}
	y = -9223372036854775807
	r = x % y
	if r != -1 {
		t.Errorf("-1 %s -9223372036854775807 = %d, want -1", "%", r)
	}
	y = -4294967296
	r = x % y
	if r != -1 {
		t.Errorf("-1 %s -4294967296 = %d, want -1", "%", r)
	}
	y = -1
	r = x % y
	if r != 0 {
		t.Errorf("-1 %s -1 = %d, want 0", "%", r)
	}
	y = 1
	r = x % y
	if r != 0 {
		t.Errorf("-1 %s 1 = %d, want 0", "%", r)
	}
	y = 4294967296
	r = x % y
	if r != -1 {
		t.Errorf("-1 %s 4294967296 = %d, want -1", "%", r)
	}
	y = 9223372036854775806
	r = x % y
	if r != -1 {
		t.Errorf("-1 %s 9223372036854775806 = %d, want -1", "%", r)
	}
	y = 9223372036854775807
	r = x % y
	if r != -1 {
		t.Errorf("-1 %s 9223372036854775807 = %d, want -1", "%", r)
	}
	x = 0
	y = -9223372036854775808
	r = x % y
	if r != 0 {
		t.Errorf("0 %s -9223372036854775808 = %d, want 0", "%", r)
	}
	y = -9223372036854775807
	r = x % y
	if r != 0 {
		t.Errorf("0 %s -9223372036854775807 = %d, want 0", "%", r)
	}
	y = -4294967296
	r = x % y
	if r != 0 {
		t.Errorf("0 %s -4294967296 = %d, want 0", "%", r)
	}
	y = -1
	r = x % y
	if r != 0 {
		t.Errorf("0 %s -1 = %d, want 0", "%", r)
	}
	y = 1
	r = x % y
	if r != 0 {
		t.Errorf("0 %s 1 = %d, want 0", "%", r)
	}
	y = 4294967296
	r = x % y
	if r != 0 {
		t.Errorf("0 %s 4294967296 = %d, want 0", "%", r)
	}
	y = 9223372036854775806
	r = x % y
	if r != 0 {
		t.Errorf("0 %s 9223372036854775806 = %d, want 0", "%", r)
	}
	y = 9223372036854775807
	r = x % y
	if r != 0 {
		t.Errorf("0 %s 9223372036854775807 = %d, want 0", "%", r)
	}
	x = 1
	y = -9223372036854775808
	r = x % y
	if r != 1 {
		t.Errorf("1 %s -9223372036854775808 = %d, want 1", "%", r)
	}
	y = -9223372036854775807
	r = x % y
	if r != 1 {
		t.Errorf("1 %s -9223372036854775807 = %d, want 1", "%", r)
	}
	y = -4294967296
	r = x % y
	if r != 1 {
		t.Errorf("1 %s -4294967296 = %d, want 1", "%", r)
	}
	y = -1
	r = x % y
	if r != 0 {
		t.Errorf("1 %s -1 = %d, want 0", "%", r)
	}
	y = 1
	r = x % y
	if r != 0 {
		t.Errorf("1 %s 1 = %d, want 0", "%", r)
	}
	y = 4294967296
	r = x % y
	if r != 1 {
		t.Errorf("1 %s 4294967296 = %d, want 1", "%", r)
	}
	y = 9223372036854775806
	r = x % y
	if r != 1 {
		t.Errorf("1 %s 9223372036854775806 = %d, want 1", "%", r)
	}
	y = 9223372036854775807
	r = x % y
	if r != 1 {
		t.Errorf("1 %s 9223372036854775807 = %d, want 1", "%", r)
	}
	x = 4294967296
	y = -9223372036854775808
	r = x % y
	if r != 4294967296 {
		t.Errorf("4294967296 %s -9223372036854775808 = %d, want 4294967296", "%", r)
	}
	y = -9223372036854775807
	r = x % y
	if r != 4294967296 {
		t.Errorf("4294967296 %s -9223372036854775807 = %d, want 4294967296", "%", r)
	}
	y = -4294967296
	r = x % y
	if r != 0 {
		t.Errorf("4294967296 %s -4294967296 = %d, want 0", "%", r)
	}
	y = -1
	r = x % y
	if r != 0 {
		t.Errorf("4294967296 %s -1 = %d, want 0", "%", r)
	}
	y = 1
	r = x % y
	if r != 0 {
		t.Errorf("4294967296 %s 1 = %d, want 0", "%", r)
	}
	y = 4294967296
	r = x % y
	if r != 0 {
		t.Errorf("4294967296 %s 4294967296 = %d, want 0", "%", r)
	}
	y = 9223372036854775806
	r = x % y
	if r != 4294967296 {
		t.Errorf("4294967296 %s 9223372036854775806 = %d, want 4294967296", "%", r)
	}
	y = 9223372036854775807
	r = x % y
	if r != 4294967296 {
		t.Errorf("4294967296 %s 9223372036854775807 = %d, want 4294967296", "%", r)
	}
	x = 9223372036854775806
	y = -9223372036854775808
	r = x % y
	if r != 9223372036854775806 {
		t.Errorf("9223372036854775806 %s -9223372036854775808 = %d, want 9223372036854775806", "%", r)
	}
	y = -9223372036854775807
	r = x % y
	if r != 9223372036854775806 {
		t.Errorf("9223372036854775806 %s -9223372036854775807 = %d, want 9223372036854775806", "%", r)
	}
	y = -4294967296
	r = x % y
	if r != 4294967294 {
		t.Errorf("9223372036854775806 %s -4294967296 = %d, want 4294967294", "%", r)
	}
	y = -1
	r = x % y
	if r != 0 {
		t.Errorf("9223372036854775806 %s -1 = %d, want 0", "%", r)
	}
	y = 1
	r = x % y
	if r != 0 {
		t.Errorf("9223372036854775806 %s 1 = %d, want 0", "%", r)
	}
	y = 4294967296
	r = x % y
	if r != 4294967294 {
		t.Errorf("9223372036854775806 %s 4294967296 = %d, want 4294967294", "%", r)
	}
	y = 9223372036854775806
	r = x % y
	if r != 0 {
		t.Errorf("9223372036854775806 %s 9223372036854775806 = %d, want 0", "%", r)
	}
	y = 9223372036854775807
	r = x % y
	if r != 9223372036854775806 {
		t.Errorf("9223372036854775806 %s 9223372036854775807 = %d, want 9223372036854775806", "%", r)
	}
	x = 9223372036854775807
	y = -9223372036854775808
	r = x % y
	if r != 9223372036854775807 {
		t.Errorf("9223372036854775807 %s -9223372036854775808 = %d, want 9223372036854775807", "%", r)
	}
	y = -9223372036854775807
	r = x % y
	if r != 0 {
		t.Errorf("9223372036854775807 %s -9223372036854775807 = %d, want 0", "%", r)
	}
	y = -4294967296
	r = x % y
	if r != 4294967295 {
		t.Errorf("9223372036854775807 %s -4294967296 = %d, want 4294967295", "%", r)
	}
	y = -1
	r = x % y
	if r != 0 {
		t.Errorf("9223372036854775807 %s -1 = %d, want 0", "%", r)
	}
	y = 1
	r = x % y
	if r != 0 {
		t.Errorf("9223372036854775807 %s 1 = %d, want 0", "%", r)
	}
	y = 4294967296
	r = x % y
	if r != 4294967295 {
		t.Errorf("9223372036854775807 %s 4294967296 = %d, want 4294967295", "%", r)
	}
	y = 9223372036854775806
	r = x % y
	if r != 1 {
		t.Errorf("9223372036854775807 %s 9223372036854775806 = %d, want 1", "%", r)
	}
	y = 9223372036854775807
	r = x % y
	if r != 0 {
		t.Errorf("9223372036854775807 %s 9223372036854775807 = %d, want 0", "%", r)
	}
}
func TestConstFolduint32add(t *testing.T) {
	var x, y, r uint32
	x = 0
	y = 0
	r = x + y
	if r != 0 {
		t.Errorf("0 %s 0 = %d, want 0", "+", r)
	}
	y = 1
	r = x + y
	if r != 1 {
		t.Errorf("0 %s 1 = %d, want 1", "+", r)
	}
	y = 4294967295
	r = x + y
	if r != 4294967295 {
		t.Errorf("0 %s 4294967295 = %d, want 4294967295", "+", r)
	}
	x = 1
	y = 0
	r = x + y
	if r != 1 {
		t.Errorf("1 %s 0 = %d, want 1", "+", r)
	}
	y = 1
	r = x + y
	if r != 2 {
		t.Errorf("1 %s 1 = %d, want 2", "+", r)
	}
	y = 4294967295
	r = x + y
	if r != 0 {
		t.Errorf("1 %s 4294967295 = %d, want 0", "+", r)
	}
	x = 4294967295
	y = 0
	r = x + y
	if r != 4294967295 {
		t.Errorf("4294967295 %s 0 = %d, want 4294967295", "+", r)
	}
	y = 1
	r = x + y
	if r != 0 {
		t.Errorf("4294967295 %s 1 = %d, want 0", "+", r)
	}
	y = 4294967295
	r = x + y
	if r != 4294967294 {
		t.Errorf("4294967295 %s 4294967295 = %d, want 4294967294", "+", r)
	}
}
func TestConstFolduint32sub(t *testing.T) {
	var x, y, r uint32
	x = 0
	y = 0
	r = x - y
	if r != 0 {
		t.Errorf("0 %s 0 = %d, want 0", "-", r)
	}
	y = 1
	r = x - y
	if r != 4294967295 {
		t.Errorf("0 %s 1 = %d, want 4294967295", "-", r)
	}
	y = 4294967295
	r = x - y
	if r != 1 {
		t.Errorf("0 %s 4294967295 = %d, want 1", "-", r)
	}
	x = 1
	y = 0
	r = x - y
	if r != 1 {
		t.Errorf("1 %s 0 = %d, want 1", "-", r)
	}
	y = 1
	r = x - y
	if r != 0 {
		t.Errorf("1 %s 1 = %d, want 0", "-", r)
	}
	y = 4294967295
	r = x - y
	if r != 2 {
		t.Errorf("1 %s 4294967295 = %d, want 2", "-", r)
	}
	x = 4294967295
	y = 0
	r = x - y
	if r != 4294967295 {
		t.Errorf("4294967295 %s 0 = %d, want 4294967295", "-", r)
	}
	y = 1
	r = x - y
	if r != 4294967294 {
		t.Errorf("4294967295 %s 1 = %d, want 4294967294", "-", r)
	}
	y = 4294967295
	r = x - y
	if r != 0 {
		t.Errorf("4294967295 %s 4294967295 = %d, want 0", "-", r)
	}
}
func TestConstFolduint32div(t *testing.T) {
	var x, y, r uint32
	x = 0
	y = 1
	r = x / y
	if r != 0 {
		t.Errorf("0 %s 1 = %d, want 0", "/", r)
	}
	y = 4294967295
	r = x / y
	if r != 0 {
		t.Errorf("0 %s 4294967295 = %d, want 0", "/", r)
	}
	x = 1
	y = 1
	r = x / y
	if r != 1 {
		t.Errorf("1 %s 1 = %d, want 1", "/", r)
	}
	y = 4294967295
	r = x / y
	if r != 0 {
		t.Errorf("1 %s 4294967295 = %d, want 0", "/", r)
	}
	x = 4294967295
	y = 1
	r = x / y
	if r != 4294967295 {
		t.Errorf("4294967295 %s 1 = %d, want 4294967295", "/", r)
	}
	y = 4294967295
	r = x / y
	if r != 1 {
		t.Errorf("4294967295 %s 4294967295 = %d, want 1", "/", r)
	}
}
func TestConstFolduint32mul(t *testing.T) {
	var x, y, r uint32
	x = 0
	y = 0
	r = x * y
	if r != 0 {
		t.Errorf("0 %s 0 = %d, want 0", "*", r)
	}
	y = 1
	r = x * y
	if r != 0 {
		t.Errorf("0 %s 1 = %d, want 0", "*", r)
	}
	y = 4294967295
	r = x * y
	if r != 0 {
		t.Errorf("0 %s 4294967295 = %d, want 0", "*", r)
	}
	x = 1
	y = 0
	r = x * y
	if r != 0 {
		t.Errorf("1 %s 0 = %d, want 0", "*", r)
	}
	y = 1
	r = x * y
	if r != 1 {
		t.Errorf("1 %s 1 = %d, want 1", "*", r)
	}
	y = 4294967295
	r = x * y
	if r != 4294967295 {
		t.Errorf("1 %s 4294967295 = %d, want 4294967295", "*", r)
	}
	x = 4294967295
	y = 0
	r = x * y
	if r != 0 {
		t.Errorf("4294967295 %s 0 = %d, want 0", "*", r)
	}
	y = 1
	r = x * y
	if r != 4294967295 {
		t.Errorf("4294967295 %s 1 = %d, want 4294967295", "*", r)
	}
	y = 4294967295
	r = x * y
	if r != 1 {
		t.Errorf("4294967295 %s 4294967295 = %d, want 1", "*", r)
	}
}
func TestConstFolduint32mod(t *testing.T) {
	var x, y, r uint32
	x = 0
	y = 1
	r = x % y
	if r != 0 {
		t.Errorf("0 %s 1 = %d, want 0", "%", r)
	}
	y = 4294967295
	r = x % y
	if r != 0 {
		t.Errorf("0 %s 4294967295 = %d, want 0", "%", r)
	}
	x = 1
	y = 1
	r = x % y
	if r != 0 {
		t.Errorf("1 %s 1 = %d, want 0", "%", r)
	}
	y = 4294967295
	r = x % y
	if r != 1 {
		t.Errorf("1 %s 4294967295 = %d, want 1", "%", r)
	}
	x = 4294967295
	y = 1
	r = x % y
	if r != 0 {
		t.Errorf("4294967295 %s 1 = %d, want 0", "%", r)
	}
	y = 4294967295
	r = x % y
	if r != 0 {
		t.Errorf("4294967295 %s 4294967295 = %d, want 0", "%", r)
	}
}
func TestConstFoldint32add(t *testing.T) {
	var x, y, r int32
	x = -2147483648
	y = -2147483648
	r = x + y
	if r != 0 {
		t.Errorf("-2147483648 %s -2147483648 = %d, want 0", "+", r)
	}
	y = -2147483647
	r = x + y
	if r != 1 {
		t.Errorf("-2147483648 %s -2147483647 = %d, want 1", "+", r)
	}
	y = -1
	r = x + y
	if r != 2147483647 {
		t.Errorf("-2147483648 %s -1 = %d, want 2147483647", "+", r)
	}
	y = 0
	r = x + y
	if r != -2147483648 {
		t.Errorf("-2147483648 %s 0 = %d, want -2147483648", "+", r)
	}
	y = 1
	r = x + y
	if r != -2147483647 {
		t.Errorf("-2147483648 %s 1 = %d, want -2147483647", "+", r)
	}
	y = 2147483647
	r = x + y
	if r != -1 {
		t.Errorf("-2147483648 %s 2147483647 = %d, want -1", "+", r)
	}
	x = -2147483647
	y = -2147483648
	r = x + y
	if r != 1 {
		t.Errorf("-2147483647 %s -2147483648 = %d, want 1", "+", r)
	}
	y = -2147483647
	r = x + y
	if r != 2 {
		t.Errorf("-2147483647 %s -2147483647 = %d, want 2", "+", r)
	}
	y = -1
	r = x + y
	if r != -2147483648 {
		t.Errorf("-2147483647 %s -1 = %d, want -2147483648", "+", r)
	}
	y = 0
	r = x + y
	if r != -2147483647 {
		t.Errorf("-2147483647 %s 0 = %d, want -2147483647", "+", r)
	}
	y = 1
	r = x + y
	if r != -2147483646 {
		t.Errorf("-2147483647 %s 1 = %d, want -2147483646", "+", r)
	}
	y = 2147483647
	r = x + y
	if r != 0 {
		t.Errorf("-2147483647 %s 2147483647 = %d, want 0", "+", r)
	}
	x = -1
	y = -2147483648
	r = x + y
	if r != 2147483647 {
		t.Errorf("-1 %s -2147483648 = %d, want 2147483647", "+", r)
	}
	y = -2147483647
	r = x + y
	if r != -2147483648 {
		t.Errorf("-1 %s -2147483647 = %d, want -2147483648", "+", r)
	}
	y = -1
	r = x + y
	if r != -2 {
		t.Errorf("-1 %s -1 = %d, want -2", "+", r)
	}
	y = 0
	r = x + y
	if r != -1 {
		t.Errorf("-1 %s 0 = %d, want -1", "+", r)
	}
	y = 1
	r = x + y
	if r != 0 {
		t.Errorf("-1 %s 1 = %d, want 0", "+", r)
	}
	y = 2147483647
	r = x + y
	if r != 2147483646 {
		t.Errorf("-1 %s 2147483647 = %d, want 2147483646", "+", r)
	}
	x = 0
	y = -2147483648
	r = x + y
	if r != -2147483648 {
		t.Errorf("0 %s -2147483648 = %d, want -2147483648", "+", r)
	}
	y = -2147483647
	r = x + y
	if r != -2147483647 {
		t.Errorf("0 %s -2147483647 = %d, want -2147483647", "+", r)
	}
	y = -1
	r = x + y
	if r != -1 {
		t.Errorf("0 %s -1 = %d, want -1", "+", r)
	}
	y = 0
	r = x + y
	if r != 0 {
		t.Errorf("0 %s 0 = %d, want 0", "+", r)
	}
	y = 1
	r = x + y
	if r != 1 {
		t.Errorf("0 %s 1 = %d, want 1", "+", r)
	}
	y = 2147483647
	r = x + y
	if r != 2147483647 {
		t.Errorf("0 %s 2147483647 = %d, want 2147483647", "+", r)
	}
	x = 1
	y = -2147483648
	r = x + y
	if r != -2147483647 {
		t.Errorf("1 %s -2147483648 = %d, want -2147483647", "+", r)
	}
	y = -2147483647
	r = x + y
	if r != -2147483646 {
		t.Errorf("1 %s -2147483647 = %d, want -2147483646", "+", r)
	}
	y = -1
	r = x + y
	if r != 0 {
		t.Errorf("1 %s -1 = %d, want 0", "+", r)
	}
	y = 0
	r = x + y
	if r != 1 {
		t.Errorf("1 %s 0 = %d, want 1", "+", r)
	}
	y = 1
	r = x + y
	if r != 2 {
		t.Errorf("1 %s 1 = %d, want 2", "+", r)
	}
	y = 2147483647
	r = x + y
	if r != -2147483648 {
		t.Errorf("1 %s 2147483647 = %d, want -2147483648", "+", r)
	}
	x = 2147483647
	y = -2147483648
	r = x + y
	if r != -1 {
		t.Errorf("2147483647 %s -2147483648 = %d, want -1", "+", r)
	}
	y = -2147483647
	r = x + y
	if r != 0 {
		t.Errorf("2147483647 %s -2147483647 = %d, want 0", "+", r)
	}
	y = -1
	r = x + y
	if r != 2147483646 {
		t.Errorf("2147483647 %s -1 = %d, want 2147483646", "+", r)
	}
	y = 0
	r = x + y
	if r != 2147483647 {
		t.Errorf("2147483647 %s 0 = %d, want 2147483647", "+", r)
	}
	y = 1
	r = x + y
	if r != -2147483648 {
		t.Errorf("2147483647 %s 1 = %d, want -2147483648", "+", r)
	}
	y = 2147483647
	r = x + y
	if r != -2 {
		t.Errorf("2147483647 %s 2147483647 = %d, want -2", "+", r)
	}
}
func TestConstFoldint32sub(t *testing.T) {
	var x, y, r int32
	x = -2147483648
	y = -2147483648
	r = x - y
	if r != 0 {
		t.Errorf("-2147483648 %s -2147483648 = %d, want 0", "-", r)
	}
	y = -2147483647
	r = x - y
	if r != -1 {
		t.Errorf("-2147483648 %s -2147483647 = %d, want -1", "-", r)
	}
	y = -1
	r = x - y
	if r != -2147483647 {
		t.Errorf("-2147483648 %s -1 = %d, want -2147483647", "-", r)
	}
	y = 0
	r = x - y
	if r != -2147483648 {
		t.Errorf("-2147483648 %s 0 = %d, want -2147483648", "-", r)
	}
	y = 1
	r = x - y
	if r != 2147483647 {
		t.Errorf("-2147483648 %s 1 = %d, want 2147483647", "-", r)
	}
	y = 2147483647
	r = x - y
	if r != 1 {
		t.Errorf("-2147483648 %s 2147483647 = %d, want 1", "-", r)
	}
	x = -2147483647
	y = -2147483648
	r = x - y
	if r != 1 {
		t.Errorf("-2147483647 %s -2147483648 = %d, want 1", "-", r)
	}
	y = -2147483647
	r = x - y
	if r != 0 {
		t.Errorf("-2147483647 %s -2147483647 = %d, want 0", "-", r)
	}
	y = -1
	r = x - y
	if r != -2147483646 {
		t.Errorf("-2147483647 %s -1 = %d, want -2147483646", "-", r)
	}
	y = 0
	r = x - y
	if r != -2147483647 {
		t.Errorf("-2147483647 %s 0 = %d, want -2147483647", "-", r)
	}
	y = 1
	r = x - y
	if r != -2147483648 {
		t.Errorf("-2147483647 %s 1 = %d, want -2147483648", "-", r)
	}
	y = 2147483647
	r = x - y
	if r != 2 {
		t.Errorf("-2147483647 %s 2147483647 = %d, want 2", "-", r)
	}
	x = -1
	y = -2147483648
	r = x - y
	if r != 2147483647 {
		t.Errorf("-1 %s -2147483648 = %d, want 2147483647", "-", r)
	}
	y = -2147483647
	r = x - y
	if r != 2147483646 {
		t.Errorf("-1 %s -2147483647 = %d, want 2147483646", "-", r)
	}
	y = -1
	r = x - y
	if r != 0 {
		t.Errorf("-1 %s -1 = %d, want 0", "-", r)
	}
	y = 0
	r = x - y
	if r != -1 {
		t.Errorf("-1 %s 0 = %d, want -1", "-", r)
	}
	y = 1
	r = x - y
	if r != -2 {
		t.Errorf("-1 %s 1 = %d, want -2", "-", r)
	}
	y = 2147483647
	r = x - y
	if r != -2147483648 {
		t.Errorf("-1 %s 2147483647 = %d, want -2147483648", "-", r)
	}
	x = 0
	y = -2147483648
	r = x - y
	if r != -2147483648 {
		t.Errorf("0 %s -2147483648 = %d, want -2147483648", "-", r)
	}
	y = -2147483647
	r = x - y
	if r != 2147483647 {
		t.Errorf("0 %s -2147483647 = %d, want 2147483647", "-", r)
	}
	y = -1
	r = x - y
	if r != 1 {
		t.Errorf("0 %s -1 = %d, want 1", "-", r)
	}
	y = 0
	r = x - y
	if r != 0 {
		t.Errorf("0 %s 0 = %d, want 0", "-", r)
	}
	y = 1
	r = x - y
	if r != -1 {
		t.Errorf("0 %s 1 = %d, want -1", "-", r)
	}
	y = 2147483647
	r = x - y
	if r != -2147483647 {
		t.Errorf("0 %s 2147483647 = %d, want -2147483647", "-", r)
	}
	x = 1
	y = -2147483648
	r = x - y
	if r != -2147483647 {
		t.Errorf("1 %s -2147483648 = %d, want -2147483647", "-", r)
	}
	y = -2147483647
	r = x - y
	if r != -2147483648 {
		t.Errorf("1 %s -2147483647 = %d, want -2147483648", "-", r)
	}
	y = -1
	r = x - y
	if r != 2 {
		t.Errorf("1 %s -1 = %d, want 2", "-", r)
	}
	y = 0
	r = x - y
	if r != 1 {
		t.Errorf("1 %s 0 = %d, want 1", "-", r)
	}
	y = 1
	r = x - y
	if r != 0 {
		t.Errorf("1 %s 1 = %d, want 0", "-", r)
	}
	y = 2147483647
	r = x - y
	if r != -2147483646 {
		t.Errorf("1 %s 2147483647 = %d, want -2147483646", "-", r)
	}
	x = 2147483647
	y = -2147483648
	r = x - y
	if r != -1 {
		t.Errorf("2147483647 %s -2147483648 = %d, want -1", "-", r)
	}
	y = -2147483647
	r = x - y
	if r != -2 {
		t.Errorf("2147483647 %s -2147483647 = %d, want -2", "-", r)
	}
	y = -1
	r = x - y
	if r != -2147483648 {
		t.Errorf("2147483647 %s -1 = %d, want -2147483648", "-", r)
	}
	y = 0
	r = x - y
	if r != 2147483647 {
		t.Errorf("2147483647 %s 0 = %d, want 2147483647", "-", r)
	}
	y = 1
	r = x - y
	if r != 2147483646 {
		t.Errorf("2147483647 %s 1 = %d, want 2147483646", "-", r)
	}
	y = 2147483647
	r = x - y
	if r != 0 {
		t.Errorf("2147483647 %s 2147483647 = %d, want 0", "-", r)
	}
}
func TestConstFoldint32div(t *testing.T) {
	var x, y, r int32
	x = -2147483648
	y = -2147483648
	r = x / y
	if r != 1 {
		t.Errorf("-2147483648 %s -2147483648 = %d, want 1", "/", r)
	}
	y = -2147483647
	r = x / y
	if r != 1 {
		t.Errorf("-2147483648 %s -2147483647 = %d, want 1", "/", r)
	}
	y = -1
	r = x / y
	if r != -2147483648 {
		t.Errorf("-2147483648 %s -1 = %d, want -2147483648", "/", r)
	}
	y = 1
	r = x / y
	if r != -2147483648 {
		t.Errorf("-2147483648 %s 1 = %d, want -2147483648", "/", r)
	}
	y = 2147483647
	r = x / y
	if r != -1 {
		t.Errorf("-2147483648 %s 2147483647 = %d, want -1", "/", r)
	}
	x = -2147483647
	y = -2147483648
	r = x / y
	if r != 0 {
		t.Errorf("-2147483647 %s -2147483648 = %d, want 0", "/", r)
	}
	y = -2147483647
	r = x / y
	if r != 1 {
		t.Errorf("-2147483647 %s -2147483647 = %d, want 1", "/", r)
	}
	y = -1
	r = x / y
	if r != 2147483647 {
		t.Errorf("-2147483647 %s -1 = %d, want 2147483647", "/", r)
	}
	y = 1
	r = x / y
	if r != -2147483647 {
		t.Errorf("-2147483647 %s 1 = %d, want -2147483647", "/", r)
	}
	y = 2147483647
	r = x / y
	if r != -1 {
		t.Errorf("-2147483647 %s 2147483647 = %d, want -1", "/", r)
	}
	x = -1
	y = -2147483648
	r = x / y
	if r != 0 {
		t.Errorf("-1 %s -2147483648 = %d, want 0", "/", r)
	}
	y = -2147483647
	r = x / y
	if r != 0 {
		t.Errorf("-1 %s -2147483647 = %d, want 0", "/", r)
	}
	y = -1
	r = x / y
	if r != 1 {
		t.Errorf("-1 %s -1 = %d, want 1", "/", r)
	}
	y = 1
	r = x / y
	if r != -1 {
		t.Errorf("-1 %s 1 = %d, want -1", "/", r)
	}
	y = 2147483647
	r = x / y
	if r != 0 {
		t.Errorf("-1 %s 2147483647 = %d, want 0", "/", r)
	}
	x = 0
	y = -2147483648
	r = x / y
	if r != 0 {
		t.Errorf("0 %s -2147483648 = %d, want 0", "/", r)
	}
	y = -2147483647
	r = x / y
	if r != 0 {
		t.Errorf("0 %s -2147483647 = %d, want 0", "/", r)
	}
	y = -1
	r = x / y
	if r != 0 {
		t.Errorf("0 %s -1 = %d, want 0", "/", r)
	}
	y = 1
	r = x / y
	if r != 0 {
		t.Errorf("0 %s 1 = %d, want 0", "/", r)
	}
	y = 2147483647
	r = x / y
	if r != 0 {
		t.Errorf("0 %s 2147483647 = %d, want 0", "/", r)
	}
	x = 1
	y = -2147483648
	r = x / y
	if r != 0 {
		t.Errorf("1 %s -2147483648 = %d, want 0", "/", r)
	}
	y = -2147483647
	r = x / y
	if r != 0 {
		t.Errorf("1 %s -2147483647 = %d, want 0", "/", r)
	}
	y = -1
	r = x / y
	if r != -1 {
		t.Errorf("1 %s -1 = %d, want -1", "/", r)
	}
	y = 1
	r = x / y
	if r != 1 {
		t.Errorf("1 %s 1 = %d, want 1", "/", r)
	}
	y = 2147483647
	r = x / y
	if r != 0 {
		t.Errorf("1 %s 2147483647 = %d, want 0", "/", r)
	}
	x = 2147483647
	y = -2147483648
	r = x / y
	if r != 0 {
		t.Errorf("2147483647 %s -2147483648 = %d, want 0", "/", r)
	}
	y = -2147483647
	r = x / y
	if r != -1 {
		t.Errorf("2147483647 %s -2147483647 = %d, want -1", "/", r)
	}
	y = -1
	r = x / y
	if r != -2147483647 {
		t.Errorf("2147483647 %s -1 = %d, want -2147483647", "/", r)
	}
	y = 1
	r = x / y
	if r != 2147483647 {
		t.Errorf("2147483647 %s 1 = %d, want 2147483647", "/", r)
	}
	y = 2147483647
	r = x / y
	if r != 1 {
		t.Errorf("2147483647 %s 2147483647 = %d, want 1", "/", r)
	}
}
func TestConstFoldint32mul(t *testing.T) {
	var x, y, r int32
	x = -2147483648
	y = -2147483648
	r = x * y
	if r != 0 {
		t.Errorf("-2147483648 %s -2147483648 = %d, want 0", "*", r)
	}
	y = -2147483647
	r = x * y
	if r != -2147483648 {
		t.Errorf("-2147483648 %s -2147483647 = %d, want -2147483648", "*", r)
	}
	y = -1
	r = x * y
	if r != -2147483648 {
		t.Errorf("-2147483648 %s -1 = %d, want -2147483648", "*", r)
	}
	y = 0
	r = x * y
	if r != 0 {
		t.Errorf("-2147483648 %s 0 = %d, want 0", "*", r)
	}
	y = 1
	r = x * y
	if r != -2147483648 {
		t.Errorf("-2147483648 %s 1 = %d, want -2147483648", "*", r)
	}
	y = 2147483647
	r = x * y
	if r != -2147483648 {
		t.Errorf("-2147483648 %s 2147483647 = %d, want -2147483648", "*", r)
	}
	x = -2147483647
	y = -2147483648
	r = x * y
	if r != -2147483648 {
		t.Errorf("-2147483647 %s -2147483648 = %d, want -2147483648", "*", r)
	}
	y = -2147483647
	r = x * y
	if r != 1 {
		t.Errorf("-2147483647 %s -2147483647 = %d, want 1", "*", r)
	}
	y = -1
	r = x * y
	if r != 2147483647 {
		t.Errorf("-2147483647 %s -1 = %d, want 2147483647", "*", r)
	}
	y = 0
	r = x * y
	if r != 0 {
		t.Errorf("-2147483647 %s 0 = %d, want 0", "*", r)
	}
	y = 1
	r = x * y
	if r != -2147483647 {
		t.Errorf("-2147483647 %s 1 = %d, want -2147483647", "*", r)
	}
	y = 2147483647
	r = x * y
	if r != -1 {
		t.Errorf("-2147483647 %s 2147483647 = %d, want -1", "*", r)
	}
	x = -1
	y = -2147483648
	r = x * y
	if r != -2147483648 {
		t.Errorf("-1 %s -2147483648 = %d, want -2147483648", "*", r)
	}
	y = -2147483647
	r = x * y
	if r != 2147483647 {
		t.Errorf("-1 %s -2147483647 = %d, want 2147483647", "*", r)
	}
	y = -1
	r = x * y
	if r != 1 {
		t.Errorf("-1 %s -1 = %d, want 1", "*", r)
	}
	y = 0
	r = x * y
	if r != 0 {
		t.Errorf("-1 %s 0 = %d, want 0", "*", r)
	}
	y = 1
	r = x * y
	if r != -1 {
		t.Errorf("-1 %s 1 = %d, want -1", "*", r)
	}
	y = 2147483647
	r = x * y
	if r != -2147483647 {
		t.Errorf("-1 %s 2147483647 = %d, want -2147483647", "*", r)
	}
	x = 0
	y = -2147483648
	r = x * y
	if r != 0 {
		t.Errorf("0 %s -2147483648 = %d, want 0", "*", r)
	}
	y = -2147483647
	r = x * y
	if r != 0 {
		t.Errorf("0 %s -2147483647 = %d, want 0", "*", r)
	}
	y = -1
	r = x * y
	if r != 0 {
		t.Errorf("0 %s -1 = %d, want 0", "*", r)
	}
	y = 0
	r = x * y
	if r != 0 {
		t.Errorf("0 %s 0 = %d, want 0", "*", r)
	}
	y = 1
	r = x * y
	if r != 0 {
		t.Errorf("0 %s 1 = %d, want 0", "*", r)
	}
	y = 2147483647
	r = x * y
	if r != 0 {
		t.Errorf("0 %s 2147483647 = %d, want 0", "*", r)
	}
	x = 1
	y = -2147483648
	r = x * y
	if r != -2147483648 {
		t.Errorf("1 %s -2147483648 = %d, want -2147483648", "*", r)
	}
	y = -2147483647
	r = x * y
	if r != -2147483647 {
		t.Errorf("1 %s -2147483647 = %d, want -2147483647", "*", r)
	}
	y = -1
	r = x * y
	if r != -1 {
		t.Errorf("1 %s -1 = %d, want -1", "*", r)
	}
	y = 0
	r = x * y
	if r != 0 {
		t.Errorf("1 %s 0 = %d, want 0", "*", r)
	}
	y = 1
	r = x * y
	if r != 1 {
		t.Errorf("1 %s 1 = %d, want 1", "*", r)
	}
	y = 2147483647
	r = x * y
	if r != 2147483647 {
		t.Errorf("1 %s 2147483647 = %d, want 2147483647", "*", r)
	}
	x = 2147483647
	y = -2147483648
	r = x * y
	if r != -2147483648 {
		t.Errorf("2147483647 %s -2147483648 = %d, want -2147483648", "*", r)
	}
	y = -2147483647
	r = x * y
	if r != -1 {
		t.Errorf("2147483647 %s -2147483647 = %d, want -1", "*", r)
	}
	y = -1
	r = x * y
	if r != -2147483647 {
		t.Errorf("2147483647 %s -1 = %d, want -2147483647", "*", r)
	}
	y = 0
	r = x * y
	if r != 0 {
		t.Errorf("2147483647 %s 0 = %d, want 0", "*", r)
	}
	y = 1
	r = x * y
	if r != 2147483647 {
		t.Errorf("2147483647 %s 1 = %d, want 2147483647", "*", r)
	}
	y = 2147483647
	r = x * y
	if r != 1 {
		t.Errorf("2147483647 %s 2147483647 = %d, want 1", "*", r)
	}
}
func TestConstFoldint32mod(t *testing.T) {
	var x, y, r int32
	x = -2147483648
	y = -2147483648
	r = x % y
	if r != 0 {
		t.Errorf("-2147483648 %s -2147483648 = %d, want 0", "%", r)
	}
	y = -2147483647
	r = x % y
	if r != -1 {
		t.Errorf("-2147483648 %s -2147483647 = %d, want -1", "%", r)
	}
	y = -1
	r = x % y
	if r != 0 {
		t.Errorf("-2147483648 %s -1 = %d, want 0", "%", r)
	}
	y = 1
	r = x % y
	if r != 0 {
		t.Errorf("-2147483648 %s 1 = %d, want 0", "%", r)
	}
	y = 2147483647
	r = x % y
	if r != -1 {
		t.Errorf("-2147483648 %s 2147483647 = %d, want -1", "%", r)
	}
	x = -2147483647
	y = -2147483648
	r = x % y
	if r != -2147483647 {
		t.Errorf("-2147483647 %s -2147483648 = %d, want -2147483647", "%", r)
	}
	y = -2147483647
	r = x % y
	if r != 0 {
		t.Errorf("-2147483647 %s -2147483647 = %d, want 0", "%", r)
	}
	y = -1
	r = x % y
	if r != 0 {
		t.Errorf("-2147483647 %s -1 = %d, want 0", "%", r)
	}
	y = 1
	r = x % y
	if r != 0 {
		t.Errorf("-2147483647 %s 1 = %d, want 0", "%", r)
	}
	y = 2147483647
	r = x % y
	if r != 0 {
		t.Errorf("-2147483647 %s 2147483647 = %d, want 0", "%", r)
	}
	x = -1
	y = -2147483648
	r = x % y
	if r != -1 {
		t.Errorf("-1 %s -2147483648 = %d, want -1", "%", r)
	}
	y = -2147483647
	r = x % y
	if r != -1 {
		t.Errorf("-1 %s -2147483647 = %d, want -1", "%", r)
	}
	y = -1
	r = x % y
	if r != 0 {
		t.Errorf("-1 %s -1 = %d, want 0", "%", r)
	}
	y = 1
	r = x % y
	if r != 0 {
		t.Errorf("-1 %s 1 = %d, want 0", "%", r)
	}
	y = 2147483647
	r = x % y
	if r != -1 {
		t.Errorf("-1 %s 2147483647 = %d, want -1", "%", r)
	}
	x = 0
	y = -2147483648
	r = x % y
	if r != 0 {
		t.Errorf("0 %s -2147483648 = %d, want 0", "%", r)
	}
	y = -2147483647
	r = x % y
	if r != 0 {
		t.Errorf("0 %s -2147483647 = %d, want 0", "%", r)
	}
	y = -1
	r = x % y
	if r != 0 {
		t.Errorf("0 %s -1 = %d, want 0", "%", r)
	}
	y = 1
	r = x % y
	if r != 0 {
		t.Errorf("0 %s 1 = %d, want 0", "%", r)
	}
	y = 2147483647
	r = x % y
	if r != 0 {
		t.Errorf("0 %s 2147483647 = %d, want 0", "%", r)
	}
	x = 1
	y = -2147483648
	r = x % y
	if r != 1 {
		t.Errorf("1 %s -2147483648 = %d, want 1", "%", r)
	}
	y = -2147483647
	r = x % y
	if r != 1 {
		t.Errorf("1 %s -2147483647 = %d, want 1", "%", r)
	}
	y = -1
	r = x % y
	if r != 0 {
		t.Errorf("1 %s -1 = %d, want 0", "%", r)
	}
	y = 1
	r = x % y
	if r != 0 {
		t.Errorf("1 %s 1 = %d, want 0", "%", r)
	}
	y = 2147483647
	r = x % y
	if r != 1 {
		t.Errorf("1 %s 2147483647 = %d, want 1", "%", r)
	}
	x = 2147483647
	y = -2147483648
	r = x % y
	if r != 2147483647 {
		t.Errorf("2147483647 %s -2147483648 = %d, want 2147483647", "%", r)
	}
	y = -2147483647
	r = x % y
	if r != 0 {
		t.Errorf("2147483647 %s -2147483647 = %d, want 0", "%", r)
	}
	y = -1
	r = x % y
	if r != 0 {
		t.Errorf("2147483647 %s -1 = %d, want 0", "%", r)
	}
	y = 1
	r = x % y
	if r != 0 {
		t.Errorf("2147483647 %s 1 = %d, want 0", "%", r)
	}
	y = 2147483647
	r = x % y
	if r != 0 {
		t.Errorf("2147483647 %s 2147483647 = %d, want 0", "%", r)
	}
}
func TestConstFolduint16add(t *testing.T) {
	var x, y, r uint16
	x = 0
	y = 0
	r = x + y
	if r != 0 {
		t.Errorf("0 %s 0 = %d, want 0", "+", r)
	}
	y = 1
	r = x + y
	if r != 1 {
		t.Errorf("0 %s 1 = %d, want 1", "+", r)
	}
	y = 65535
	r = x + y
	if r != 65535 {
		t.Errorf("0 %s 65535 = %d, want 65535", "+", r)
	}
	x = 1
	y = 0
	r = x + y
	if r != 1 {
		t.Errorf("1 %s 0 = %d, want 1", "+", r)
	}
	y = 1
	r = x + y
	if r != 2 {
		t.Errorf("1 %s 1 = %d, want 2", "+", r)
	}
	y = 65535
	r = x + y
	if r != 0 {
		t.Errorf("1 %s 65535 = %d, want 0", "+", r)
	}
	x = 65535
	y = 0
	r = x + y
	if r != 65535 {
		t.Errorf("65535 %s 0 = %d, want 65535", "+", r)
	}
	y = 1
	r = x + y
	if r != 0 {
		t.Errorf("65535 %s 1 = %d, want 0", "+", r)
	}
	y = 65535
	r = x + y
	if r != 65534 {
		t.Errorf("65535 %s 65535 = %d, want 65534", "+", r)
	}
}
func TestConstFolduint16sub(t *testing.T) {
	var x, y, r uint16
	x = 0
	y = 0
	r = x - y
	if r != 0 {
		t.Errorf("0 %s 0 = %d, want 0", "-", r)
	}
	y = 1
	r = x - y
	if r != 65535 {
		t.Errorf("0 %s 1 = %d, want 65535", "-", r)
	}
	y = 65535
	r = x - y
	if r != 1 {
		t.Errorf("0 %s 65535 = %d, want 1", "-", r)
	}
	x = 1
	y = 0
	r = x - y
	if r != 1 {
		t.Errorf("1 %s 0 = %d, want 1", "-", r)
	}
	y = 1
	r = x - y
	if r != 0 {
		t.Errorf("1 %s 1 = %d, want 0", "-", r)
	}
	y = 65535
	r = x - y
	if r != 2 {
		t.Errorf("1 %s 65535 = %d, want 2", "-", r)
	}
	x = 65535
	y = 0
	r = x - y
	if r != 65535 {
		t.Errorf("65535 %s 0 = %d, want 65535", "-", r)
	}
	y = 1
	r = x - y
	if r != 65534 {
		t.Errorf("65535 %s 1 = %d, want 65534", "-", r)
	}
	y = 65535
	r = x - y
	if r != 0 {
		t.Errorf("65535 %s 65535 = %d, want 0", "-", r)
	}
}
func TestConstFolduint16div(t *testing.T) {
	var x, y, r uint16
	x = 0
	y = 1
	r = x / y
	if r != 0 {
		t.Errorf("0 %s 1 = %d, want 0", "/", r)
	}
	y = 65535
	r = x / y
	if r != 0 {
		t.Errorf("0 %s 65535 = %d, want 0", "/", r)
	}
	x = 1
	y = 1
	r = x / y
	if r != 1 {
		t.Errorf("1 %s 1 = %d, want 1", "/", r)
	}
	y = 65535
	r = x / y
	if r != 0 {
		t.Errorf("1 %s 65535 = %d, want 0", "/", r)
	}
	x = 65535
	y = 1
	r = x / y
	if r != 65535 {
		t.Errorf("65535 %s 1 = %d, want 65535", "/", r)
	}
	y = 65535
	r = x / y
	if r != 1 {
		t.Errorf("65535 %s 65535 = %d, want 1", "/", r)
	}
}
func TestConstFolduint16mul(t *testing.T) {
	var x, y, r uint16
	x = 0
	y = 0
	r = x * y
	if r != 0 {
		t.Errorf("0 %s 0 = %d, want 0", "*", r)
	}
	y = 1
	r = x * y
	if r != 0 {
		t.Errorf("0 %s 1 = %d, want 0", "*", r)
	}
	y = 65535
	r = x * y
	if r != 0 {
		t.Errorf("0 %s 65535 = %d, want 0", "*", r)
	}
	x = 1
	y = 0
	r = x * y
	if r != 0 {
		t.Errorf("1 %s 0 = %d, want 0", "*", r)
	}
	y = 1
	r = x * y
	if r != 1 {
		t.Errorf("1 %s 1 = %d, want 1", "*", r)
	}
	y = 65535
	r = x * y
	if r != 65535 {
		t.Errorf("1 %s 65535 = %d, want 65535", "*", r)
	}
	x = 65535
	y = 0
	r = x * y
	if r != 0 {
		t.Errorf("65535 %s 0 = %d, want 0", "*", r)
	}
	y = 1
	r = x * y
	if r != 65535 {
		t.Errorf("65535 %s 1 = %d, want 65535", "*", r)
	}
	y = 65535
	r = x * y
	if r != 1 {
		t.Errorf("65535 %s 65535 = %d, want 1", "*", r)
	}
}
func TestConstFolduint16mod(t *testing.T) {
	var x, y, r uint16
	x = 0
	y = 1
	r = x % y
	if r != 0 {
		t.Errorf("0 %s 1 = %d, want 0", "%", r)
	}
	y = 65535
	r = x % y
	if r != 0 {
		t.Errorf("0 %s 65535 = %d, want 0", "%", r)
	}
	x = 1
	y = 1
	r = x % y
	if r != 0 {
		t.Errorf("1 %s 1 = %d, want 0", "%", r)
	}
	y = 65535
	r = x % y
	if r != 1 {
		t.Errorf("1 %s 65535 = %d, want 1", "%", r)
	}
	x = 65535
	y = 1
	r = x % y
	if r != 0 {
		t.Errorf("65535 %s 1 = %d, want 0", "%", r)
	}
	y = 65535
	r = x % y
	if r != 0 {
		t.Errorf("65535 %s 65535 = %d, want 0", "%", r)
	}
}
func TestConstFoldint16add(t *testing.T) {
	var x, y, r int16
	x = -32768
	y = -32768
	r = x + y
	if r != 0 {
		t.Errorf("-32768 %s -32768 = %d, want 0", "+", r)
	}
	y = -32767
	r = x + y
	if r != 1 {
		t.Errorf("-32768 %s -32767 = %d, want 1", "+", r)
	}
	y = -1
	r = x + y
	if r != 32767 {
		t.Errorf("-32768 %s -1 = %d, want 32767", "+", r)
	}
	y = 0
	r = x + y
	if r != -32768 {
		t.Errorf("-32768 %s 0 = %d, want -32768", "+", r)
	}
	y = 1
	r = x + y
	if r != -32767 {
		t.Errorf("-32768 %s 1 = %d, want -32767", "+", r)
	}
	y = 32766
	r = x + y
	if r != -2 {
		t.Errorf("-32768 %s 32766 = %d, want -2", "+", r)
	}
	y = 32767
	r = x + y
	if r != -1 {
		t.Errorf("-32768 %s 32767 = %d, want -1", "+", r)
	}
	x = -32767
	y = -32768
	r = x + y
	if r != 1 {
		t.Errorf("-32767 %s -32768 = %d, want 1", "+", r)
	}
	y = -32767
	r = x + y
	if r != 2 {
		t.Errorf("-32767 %s -32767 = %d, want 2", "+", r)
	}
	y = -1
	r = x + y
	if r != -32768 {
		t.Errorf("-32767 %s -1 = %d, want -32768", "+", r)
	}
	y = 0
	r = x + y
	if r != -32767 {
		t.Errorf("-32767 %s 0 = %d, want -32767", "+", r)
	}
	y = 1
	r = x + y
	if r != -32766 {
		t.Errorf("-32767 %s 1 = %d, want -32766", "+", r)
	}
	y = 32766
	r = x + y
	if r != -1 {
		t.Errorf("-32767 %s 32766 = %d, want -1", "+", r)
	}
	y = 32767
	r = x + y
	if r != 0 {
		t.Errorf("-32767 %s 32767 = %d, want 0", "+", r)
	}
	x = -1
	y = -32768
	r = x + y
	if r != 32767 {
		t.Errorf("-1 %s -32768 = %d, want 32767", "+", r)
	}
	y = -32767
	r = x + y
	if r != -32768 {
		t.Errorf("-1 %s -32767 = %d, want -32768", "+", r)
	}
	y = -1
	r = x + y
	if r != -2 {
		t.Errorf("-1 %s -1 = %d, want -2", "+", r)
	}
	y = 0
	r = x + y
	if r != -1 {
		t.Errorf("-1 %s 0 = %d, want -1", "+", r)
	}
	y = 1
	r = x + y
	if r != 0 {
		t.Errorf("-1 %s 1 = %d, want 0", "+", r)
	}
	y = 32766
	r = x + y
	if r != 32765 {
		t.Errorf("-1 %s 32766 = %d, want 32765", "+", r)
	}
	y = 32767
	r = x + y
	if r != 32766 {
		t.Errorf("-1 %s 32767 = %d, want 32766", "+", r)
	}
	x = 0
	y = -32768
	r = x + y
	if r != -32768 {
		t.Errorf("0 %s -32768 = %d, want -32768", "+", r)
	}
	y = -32767
	r = x + y
	if r != -32767 {
		t.Errorf("0 %s -32767 = %d, want -32767", "+", r)
	}
	y = -1
	r = x + y
	if r != -1 {
		t.Errorf("0 %s -1 = %d, want -1", "+", r)
	}
	y = 0
	r = x + y
	if r != 0 {
		t.Errorf("0 %s 0 = %d, want 0", "+", r)
	}
	y = 1
	r = x + y
	if r != 1 {
		t.Errorf("0 %s 1 = %d, want 1", "+", r)
	}
	y = 32766
	r = x + y
	if r != 32766 {
		t.Errorf("0 %s 32766 = %d, want 32766", "+", r)
	}
	y = 32767
	r = x + y
	if r != 32767 {
		t.Errorf("0 %s 32767 = %d, want 32767", "+", r)
	}
	x = 1
	y = -32768
	r = x + y
	if r != -32767 {
		t.Errorf("1 %s -32768 = %d, want -32767", "+", r)
	}
	y = -32767
	r = x + y
	if r != -32766 {
		t.Errorf("1 %s -32767 = %d, want -32766", "+", r)
	}
	y = -1
	r = x + y
	if r != 0 {
		t.Errorf("1 %s -1 = %d, want 0", "+", r)
	}
	y = 0
	r = x + y
	if r != 1 {
		t.Errorf("1 %s 0 = %d, want 1", "+", r)
	}
	y = 1
	r = x + y
	if r != 2 {
		t.Errorf("1 %s 1 = %d, want 2", "+", r)
	}
	y = 32766
	r = x + y
	if r != 32767 {
		t.Errorf("1 %s 32766 = %d, want 32767", "+", r)
	}
	y = 32767
	r = x + y
	if r != -32768 {
		t.Errorf("1 %s 32767 = %d, want -32768", "+", r)
	}
	x = 32766
	y = -32768
	r = x + y
	if r != -2 {
		t.Errorf("32766 %s -32768 = %d, want -2", "+", r)
	}
	y = -32767
	r = x + y
	if r != -1 {
		t.Errorf("32766 %s -32767 = %d, want -1", "+", r)
	}
	y = -1
	r = x + y
	if r != 32765 {
		t.Errorf("32766 %s -1 = %d, want 32765", "+", r)
	}
	y = 0
	r = x + y
	if r != 32766 {
		t.Errorf("32766 %s 0 = %d, want 32766", "+", r)
	}
	y = 1
	r = x + y
	if r != 32767 {
		t.Errorf("32766 %s 1 = %d, want 32767", "+", r)
	}
	y = 32766
	r = x + y
	if r != -4 {
		t.Errorf("32766 %s 32766 = %d, want -4", "+", r)
	}
	y = 32767
	r = x + y
	if r != -3 {
		t.Errorf("32766 %s 32767 = %d, want -3", "+", r)
	}
	x = 32767
	y = -32768
	r = x + y
	if r != -1 {
		t.Errorf("32767 %s -32768 = %d, want -1", "+", r)
	}
	y = -32767
	r = x + y
	if r != 0 {
		t.Errorf("32767 %s -32767 = %d, want 0", "+", r)
	}
	y = -1
	r = x + y
	if r != 32766 {
		t.Errorf("32767 %s -1 = %d, want 32766", "+", r)
	}
	y = 0
	r = x + y
	if r != 32767 {
		t.Errorf("32767 %s 0 = %d, want 32767", "+", r)
	}
	y = 1
	r = x + y
	if r != -32768 {
		t.Errorf("32767 %s 1 = %d, want -32768", "+", r)
	}
	y = 32766
	r = x + y
	if r != -3 {
		t.Errorf("32767 %s 32766 = %d, want -3", "+", r)
	}
	y = 32767
	r = x + y
	if r != -2 {
		t.Errorf("32767 %s 32767 = %d, want -2", "+", r)
	}
}
func TestConstFoldint16sub(t *testing.T) {
	var x, y, r int16
	x = -32768
	y = -32768
	r = x - y
	if r != 0 {
		t.Errorf("-32768 %s -32768 = %d, want 0", "-", r)
	}
	y = -32767
	r = x - y
	if r != -1 {
		t.Errorf("-32768 %s -32767 = %d, want -1", "-", r)
	}
	y = -1
	r = x - y
	if r != -32767 {
		t.Errorf("-32768 %s -1 = %d, want -32767", "-", r)
	}
	y = 0
	r = x - y
	if r != -32768 {
		t.Errorf("-32768 %s 0 = %d, want -32768", "-", r)
	}
	y = 1
	r = x - y
	if r != 32767 {
		t.Errorf("-32768 %s 1 = %d, want 32767", "-", r)
	}
	y = 32766
	r = x - y
	if r != 2 {
		t.Errorf("-32768 %s 32766 = %d, want 2", "-", r)
	}
	y = 32767
	r = x - y
	if r != 1 {
		t.Errorf("-32768 %s 32767 = %d, want 1", "-", r)
	}
	x = -32767
	y = -32768
	r = x - y
	if r != 1 {
		t.Errorf("-32767 %s -32768 = %d, want 1", "-", r)
	}
	y = -32767
	r = x - y
	if r != 0 {
		t.Errorf("-32767 %s -32767 = %d, want 0", "-", r)
	}
	y = -1
	r = x - y
	if r != -32766 {
		t.Errorf("-32767 %s -1 = %d, want -32766", "-", r)
	}
	y = 0
	r = x - y
	if r != -32767 {
		t.Errorf("-32767 %s 0 = %d, want -32767", "-", r)
	}
	y = 1
	r = x - y
	if r != -32768 {
		t.Errorf("-32767 %s 1 = %d, want -32768", "-", r)
	}
	y = 32766
	r = x - y
	if r != 3 {
		t.Errorf("-32767 %s 32766 = %d, want 3", "-", r)
	}
	y = 32767
	r = x - y
	if r != 2 {
		t.Errorf("-32767 %s 32767 = %d, want 2", "-", r)
	}
	x = -1
	y = -32768
	r = x - y
	if r != 32767 {
		t.Errorf("-1 %s -32768 = %d, want 32767", "-", r)
	}
	y = -32767
	r = x - y
	if r != 32766 {
		t.Errorf("-1 %s -32767 = %d, want 32766", "-", r)
	}
	y = -1
	r = x - y
	if r != 0 {
		t.Errorf("-1 %s -1 = %d, want 0", "-", r)
	}
	y = 0
	r = x - y
	if r != -1 {
		t.Errorf("-1 %s 0 = %d, want -1", "-", r)
	}
	y = 1
	r = x - y
	if r != -2 {
		t.Errorf("-1 %s 1 = %d, want -2", "-", r)
	}
	y = 32766
	r = x - y
	if r != -32767 {
		t.Errorf("-1 %s 32766 = %d, want -32767", "-", r)
	}
	y = 32767
	r = x - y
	if r != -32768 {
		t.Errorf("-1 %s 32767 = %d, want -32768", "-", r)
	}
	x = 0
	y = -32768
	r = x - y
	if r != -32768 {
		t.Errorf("0 %s -32768 = %d, want -32768", "-", r)
	}
	y = -32767
	r = x - y
	if r != 32767 {
		t.Errorf("0 %s -32767 = %d, want 32767", "-", r)
	}
	y = -1
	r = x - y
	if r != 1 {
		t.Errorf("0 %s -1 = %d, want 1", "-", r)
	}
	y = 0
	r = x - y
	if r != 0 {
		t.Errorf("0 %s 0 = %d, want 0", "-", r)
	}
	y = 1
	r = x - y
	if r != -1 {
		t.Errorf("0 %s 1 = %d, want -1", "-", r)
	}
	y = 32766
	r = x - y
	if r != -32766 {
		t.Errorf("0 %s 32766 = %d, want -32766", "-", r)
	}
	y = 32767
	r = x - y
	if r != -32767 {
		t.Errorf("0 %s 32767 = %d, want -32767", "-", r)
	}
	x = 1
	y = -32768
	r = x - y
	if r != -32767 {
		t.Errorf("1 %s -32768 = %d, want -32767", "-", r)
	}
	y = -32767
	r = x - y
	if r != -32768 {
		t.Errorf("1 %s -32767 = %d, want -32768", "-", r)
	}
	y = -1
	r = x - y
	if r != 2 {
		t.Errorf("1 %s -1 = %d, want 2", "-", r)
	}
	y = 0
	r = x - y
	if r != 1 {
		t.Errorf("1 %s 0 = %d, want 1", "-", r)
	}
	y = 1
	r = x - y
	if r != 0 {
		t.Errorf("1 %s 1 = %d, want 0", "-", r)
	}
	y = 32766
	r = x - y
	if r != -32765 {
		t.Errorf("1 %s 32766 = %d, want -32765", "-", r)
	}
	y = 32767
	r = x - y
	if r != -32766 {
		t.Errorf("1 %s 32767 = %d, want -32766", "-", r)
	}
	x = 32766
	y = -32768
	r = x - y
	if r != -2 {
		t.Errorf("32766 %s -32768 = %d, want -2", "-", r)
	}
	y = -32767
	r = x - y
	if r != -3 {
		t.Errorf("32766 %s -32767 = %d, want -3", "-", r)
	}
	y = -1
	r = x - y
	if r != 32767 {
		t.Errorf("32766 %s -1 = %d, want 32767", "-", r)
	}
	y = 0
	r = x - y
	if r != 32766 {
		t.Errorf("32766 %s 0 = %d, want 32766", "-", r)
	}
	y = 1
	r = x - y
	if r != 32765 {
		t.Errorf("32766 %s 1 = %d, want 32765", "-", r)
	}
	y = 32766
	r = x - y
	if r != 0 {
		t.Errorf("32766 %s 32766 = %d, want 0", "-", r)
	}
	y = 32767
	r = x - y
	if r != -1 {
		t.Errorf("32766 %s 32767 = %d, want -1", "-", r)
	}
	x = 32767
	y = -32768
	r = x - y
	if r != -1 {
		t.Errorf("32767 %s -32768 = %d, want -1", "-", r)
	}
	y = -32767
	r = x - y
	if r != -2 {
		t.Errorf("32767 %s -32767 = %d, want -2", "-", r)
	}
	y = -1
	r = x - y
	if r != -32768 {
		t.Errorf("32767 %s -1 = %d, want -32768", "-", r)
	}
	y = 0
	r = x - y
	if r != 32767 {
		t.Errorf("32767 %s 0 = %d, want 32767", "-", r)
	}
	y = 1
	r = x - y
	if r != 32766 {
		t.Errorf("32767 %s 1 = %d, want 32766", "-", r)
	}
	y = 32766
	r = x - y
	if r != 1 {
		t.Errorf("32767 %s 32766 = %d, want 1", "-", r)
	}
	y = 32767
	r = x - y
	if r != 0 {
		t.Errorf("32767 %s 32767 = %d, want 0", "-", r)
	}
}
func TestConstFoldint16div(t *testing.T) {
	var x, y, r int16
	x = -32768
	y = -32768
	r = x / y
	if r != 1 {
		t.Errorf("-32768 %s -32768 = %d, want 1", "/", r)
	}
	y = -32767
	r = x / y
	if r != 1 {
		t.Errorf("-32768 %s -32767 = %d, want 1", "/", r)
	}
	y = -1
	r = x / y
	if r != -32768 {
		t.Errorf("-32768 %s -1 = %d, want -32768", "/", r)
	}
	y = 1
	r = x / y
	if r != -32768 {
		t.Errorf("-32768 %s 1 = %d, want -32768", "/", r)
	}
	y = 32766
	r = x / y
	if r != -1 {
		t.Errorf("-32768 %s 32766 = %d, want -1", "/", r)
	}
	y = 32767
	r = x / y
	if r != -1 {
		t.Errorf("-32768 %s 32767 = %d, want -1", "/", r)
	}
	x = -32767
	y = -32768
	r = x / y
	if r != 0 {
		t.Errorf("-32767 %s -32768 = %d, want 0", "/", r)
	}
	y = -32767
	r = x / y
	if r != 1 {
		t.Errorf("-32767 %s -32767 = %d, want 1", "/", r)
	}
	y = -1
	r = x / y
	if r != 32767 {
		t.Errorf("-32767 %s -1 = %d, want 32767", "/", r)
	}
	y = 1
	r = x / y
	if r != -32767 {
		t.Errorf("-32767 %s 1 = %d, want -32767", "/", r)
	}
	y = 32766
	r = x / y
	if r != -1 {
		t.Errorf("-32767 %s 32766 = %d, want -1", "/", r)
	}
	y = 32767
	r = x / y
	if r != -1 {
		t.Errorf("-32767 %s 32767 = %d, want -1", "/", r)
	}
	x = -1
	y = -32768
	r = x / y
	if r != 0 {
		t.Errorf("-1 %s -32768 = %d, want 0", "/", r)
	}
	y = -32767
	r = x / y
	if r != 0 {
		t.Errorf("-1 %s -32767 = %d, want 0", "/", r)
	}
	y = -1
	r = x / y
	if r != 1 {
		t.Errorf("-1 %s -1 = %d, want 1", "/", r)
	}
	y = 1
	r = x / y
	if r != -1 {
		t.Errorf("-1 %s 1 = %d, want -1", "/", r)
	}
	y = 32766
	r = x / y
	if r != 0 {
		t.Errorf("-1 %s 32766 = %d, want 0", "/", r)
	}
	y = 32767
	r = x / y
	if r != 0 {
		t.Errorf("-1 %s 32767 = %d, want 0", "/", r)
	}
	x = 0
	y = -32768
	r = x / y
	if r != 0 {
		t.Errorf("0 %s -32768 = %d, want 0", "/", r)
	}
	y = -32767
	r = x / y
	if r != 0 {
		t.Errorf("0 %s -32767 = %d, want 0", "/", r)
	}
	y = -1
	r = x / y
	if r != 0 {
		t.Errorf("0 %s -1 = %d, want 0", "/", r)
	}
	y = 1
	r = x / y
	if r != 0 {
		t.Errorf("0 %s 1 = %d, want 0", "/", r)
	}
	y = 32766
	r = x / y
	if r != 0 {
		t.Errorf("0 %s 32766 = %d, want 0", "/", r)
	}
	y = 32767
	r = x / y
	if r != 0 {
		t.Errorf("0 %s 32767 = %d, want 0", "/", r)
	}
	x = 1
	y = -32768
	r = x / y
	if r != 0 {
		t.Errorf("1 %s -32768 = %d, want 0", "/", r)
	}
	y = -32767
	r = x / y
	if r != 0 {
		t.Errorf("1 %s -32767 = %d, want 0", "/", r)
	}
	y = -1
	r = x / y
	if r != -1 {
		t.Errorf("1 %s -1 = %d, want -1", "/", r)
	}
	y = 1
	r = x / y
	if r != 1 {
		t.Errorf("1 %s 1 = %d, want 1", "/", r)
	}
	y = 32766
	r = x / y
	if r != 0 {
		t.Errorf("1 %s 32766 = %d, want 0", "/", r)
	}
	y = 32767
	r = x / y
	if r != 0 {
		t.Errorf("1 %s 32767 = %d, want 0", "/", r)
	}
	x = 32766
	y = -32768
	r = x / y
	if r != 0 {
		t.Errorf("32766 %s -32768 = %d, want 0", "/", r)
	}
	y = -32767
	r = x / y
	if r != 0 {
		t.Errorf("32766 %s -32767 = %d, want 0", "/", r)
	}
	y = -1
	r = x / y
	if r != -32766 {
		t.Errorf("32766 %s -1 = %d, want -32766", "/", r)
	}
	y = 1
	r = x / y
	if r != 32766 {
		t.Errorf("32766 %s 1 = %d, want 32766", "/", r)
	}
	y = 32766
	r = x / y
	if r != 1 {
		t.Errorf("32766 %s 32766 = %d, want 1", "/", r)
	}
	y = 32767
	r = x / y
	if r != 0 {
		t.Errorf("32766 %s 32767 = %d, want 0", "/", r)
	}
	x = 32767
	y = -32768
	r = x / y
	if r != 0 {
		t.Errorf("32767 %s -32768 = %d, want 0", "/", r)
	}
	y = -32767
	r = x / y
	if r != -1 {
		t.Errorf("32767 %s -32767 = %d, want -1", "/", r)
	}
	y = -1
	r = x / y
	if r != -32767 {
		t.Errorf("32767 %s -1 = %d, want -32767", "/", r)
	}
	y = 1
	r = x / y
	if r != 32767 {
		t.Errorf("32767 %s 1 = %d, want 32767", "/", r)
	}
	y = 32766
	r = x / y
	if r != 1 {
		t.Errorf("32767 %s 32766 = %d, want 1", "/", r)
	}
	y = 32767
	r = x / y
	if r != 1 {
		t.Errorf("32767 %s 32767 = %d, want 1", "/", r)
	}
}
func TestConstFoldint16mul(t *testing.T) {
	var x, y, r int16
	x = -32768
	y = -32768
	r = x * y
	if r != 0 {
		t.Errorf("-32768 %s -32768 = %d, want 0", "*", r)
	}
	y = -32767
	r = x * y
	if r != -32768 {
		t.Errorf("-32768 %s -32767 = %d, want -32768", "*", r)
	}
	y = -1
	r = x * y
	if r != -32768 {
		t.Errorf("-32768 %s -1 = %d, want -32768", "*", r)
	}
	y = 0
	r = x * y
	if r != 0 {
		t.Errorf("-32768 %s 0 = %d, want 0", "*", r)
	}
	y = 1
	r = x * y
	if r != -32768 {
		t.Errorf("-32768 %s 1 = %d, want -32768", "*", r)
	}
	y = 32766
	r = x * y
	if r != 0 {
		t.Errorf("-32768 %s 32766 = %d, want 0", "*", r)
	}
	y = 32767
	r = x * y
	if r != -32768 {
		t.Errorf("-32768 %s 32767 = %d, want -32768", "*", r)
	}
	x = -32767
	y = -32768
	r = x * y
	if r != -32768 {
		t.Errorf("-32767 %s -32768 = %d, want -32768", "*", r)
	}
	y = -32767
	r = x * y
	if r != 1 {
		t.Errorf("-32767 %s -32767 = %d, want 1", "*", r)
	}
	y = -1
	r = x * y
	if r != 32767 {
		t.Errorf("-32767 %s -1 = %d, want 32767", "*", r)
	}
	y = 0
	r = x * y
	if r != 0 {
		t.Errorf("-32767 %s 0 = %d, want 0", "*", r)
	}
	y = 1
	r = x * y
	if r != -32767 {
		t.Errorf("-32767 %s 1 = %d, want -32767", "*", r)
	}
	y = 32766
	r = x * y
	if r != 32766 {
		t.Errorf("-32767 %s 32766 = %d, want 32766", "*", r)
	}
	y = 32767
	r = x * y
	if r != -1 {
		t.Errorf("-32767 %s 32767 = %d, want -1", "*", r)
	}
	x = -1
	y = -32768
	r = x * y
	if r != -32768 {
		t.Errorf("-1 %s -32768 = %d, want -32768", "*", r)
	}
	y = -32767
	r = x * y
	if r != 32767 {
		t.Errorf("-1 %s -32767 = %d, want 32767", "*", r)
	}
	y = -1
	r = x * y
	if r != 1 {
		t.Errorf("-1 %s -1 = %d, want 1", "*", r)
	}
	y = 0
	r = x * y
	if r != 0 {
		t.Errorf("-1 %s 0 = %d, want 0", "*", r)
	}
	y = 1
	r = x * y
	if r != -1 {
		t.Errorf("-1 %s 1 = %d, want -1", "*", r)
	}
	y = 32766
	r = x * y
	if r != -32766 {
		t.Errorf("-1 %s 32766 = %d, want -32766", "*", r)
	}
	y = 32767
	r = x * y
	if r != -32767 {
		t.Errorf("-1 %s 32767 = %d, want -32767", "*", r)
	}
	x = 0
	y = -32768
	r = x * y
	if r != 0 {
		t.Errorf("0 %s -32768 = %d, want 0", "*", r)
	}
	y = -32767
	r = x * y
	if r != 0 {
		t.Errorf("0 %s -32767 = %d, want 0", "*", r)
	}
	y = -1
	r = x * y
	if r != 0 {
		t.Errorf("0 %s -1 = %d, want 0", "*", r)
	}
	y = 0
	r = x * y
	if r != 0 {
		t.Errorf("0 %s 0 = %d, want 0", "*", r)
	}
	y = 1
	r = x * y
	if r != 0 {
		t.Errorf("0 %s 1 = %d, want 0", "*", r)
	}
	y = 32766
	r = x * y
	if r != 0 {
		t.Errorf("0 %s 32766 = %d, want 0", "*", r)
	}
	y = 32767
	r = x * y
	if r != 0 {
		t.Errorf("0 %s 32767 = %d, want 0", "*", r)
	}
	x = 1
	y = -32768
	r = x * y
	if r != -32768 {
		t.Errorf("1 %s -32768 = %d, want -32768", "*", r)
	}
	y = -32767
	r = x * y
	if r != -32767 {
		t.Errorf("1 %s -32767 = %d, want -32767", "*", r)
	}
	y = -1
	r = x * y
	if r != -1 {
		t.Errorf("1 %s -1 = %d, want -1", "*", r)
	}
	y = 0
	r = x * y
	if r != 0 {
		t.Errorf("1 %s 0 = %d, want 0", "*", r)
	}
	y = 1
	r = x * y
	if r != 1 {
		t.Errorf("1 %s 1 = %d, want 1", "*", r)
	}
	y = 32766
	r = x * y
	if r != 32766 {
		t.Errorf("1 %s 32766 = %d, want 32766", "*", r)
	}
	y = 32767
	r = x * y
	if r != 32767 {
		t.Errorf("1 %s 32767 = %d, want 32767", "*", r)
	}
	x = 32766
	y = -32768
	r = x * y
	if r != 0 {
		t.Errorf("32766 %s -32768 = %d, want 0", "*", r)
	}
	y = -32767
	r = x * y
	if r != 32766 {
		t.Errorf("32766 %s -32767 = %d, want 32766", "*", r)
	}
	y = -1
	r = x * y
	if r != -32766 {
		t.Errorf("32766 %s -1 = %d, want -32766", "*", r)
	}
	y = 0
	r = x * y
	if r != 0 {
		t.Errorf("32766 %s 0 = %d, want 0", "*", r)
	}
	y = 1
	r = x * y
	if r != 32766 {
		t.Errorf("32766 %s 1 = %d, want 32766", "*", r)
	}
	y = 32766
	r = x * y
	if r != 4 {
		t.Errorf("32766 %s 32766 = %d, want 4", "*", r)
	}
	y = 32767
	r = x * y
	if r != -32766 {
		t.Errorf("32766 %s 32767 = %d, want -32766", "*", r)
	}
	x = 32767
	y = -32768
	r = x * y
	if r != -32768 {
		t.Errorf("32767 %s -32768 = %d, want -32768", "*", r)
	}
	y = -32767
	r = x * y
	if r != -1 {
		t.Errorf("32767 %s -32767 = %d, want -1", "*", r)
	}
	y = -1
	r = x * y
	if r != -32767 {
		t.Errorf("32767 %s -1 = %d, want -32767", "*", r)
	}
	y = 0
	r = x * y
	if r != 0 {
		t.Errorf("32767 %s 0 = %d, want 0", "*", r)
	}
	y = 1
	r = x * y
	if r != 32767 {
		t.Errorf("32767 %s 1 = %d, want 32767", "*", r)
	}
	y = 32766
	r = x * y
	if r != -32766 {
		t.Errorf("32767 %s 32766 = %d, want -32766", "*", r)
	}
	y = 32767
	r = x * y
	if r != 1 {
		t.Errorf("32767 %s 32767 = %d, want 1", "*", r)
	}
}
func TestConstFoldint16mod(t *testing.T) {
	var x, y, r int16
	x = -32768
	y = -32768
	r = x % y
	if r != 0 {
		t.Errorf("-32768 %s -32768 = %d, want 0", "%", r)
	}
	y = -32767
	r = x % y
	if r != -1 {
		t.Errorf("-32768 %s -32767 = %d, want -1", "%", r)
	}
	y = -1
	r = x % y
	if r != 0 {
		t.Errorf("-32768 %s -1 = %d, want 0", "%", r)
	}
	y = 1
	r = x % y
	if r != 0 {
		t.Errorf("-32768 %s 1 = %d, want 0", "%", r)
	}
	y = 32766
	r = x % y
	if r != -2 {
		t.Errorf("-32768 %s 32766 = %d, want -2", "%", r)
	}
	y = 32767
	r = x % y
	if r != -1 {
		t.Errorf("-32768 %s 32767 = %d, want -1", "%", r)
	}
	x = -32767
	y = -32768
	r = x % y
	if r != -32767 {
		t.Errorf("-32767 %s -32768 = %d, want -32767", "%", r)
	}
	y = -32767
	r = x % y
	if r != 0 {
		t.Errorf("-32767 %s -32767 = %d, want 0", "%", r)
	}
	y = -1
	r = x % y
	if r != 0 {
		t.Errorf("-32767 %s -1 = %d, want 0", "%", r)
	}
	y = 1
	r = x % y
	if r != 0 {
		t.Errorf("-32767 %s 1 = %d, want 0", "%", r)
	}
	y = 32766
	r = x % y
	if r != -1 {
		t.Errorf("-32767 %s 32766 = %d, want -1", "%", r)
	}
	y = 32767
	r = x % y
	if r != 0 {
		t.Errorf("-32767 %s 32767 = %d, want 0", "%", r)
	}
	x = -1
	y = -32768
	r = x % y
	if r != -1 {
		t.Errorf("-1 %s -32768 = %d, want -1", "%", r)
	}
	y = -32767
	r = x % y
	if r != -1 {
		t.Errorf("-1 %s -32767 = %d, want -1", "%", r)
	}
	y = -1
	r = x % y
	if r != 0 {
		t.Errorf("-1 %s -1 = %d, want 0", "%", r)
	}
	y = 1
	r = x % y
	if r != 0 {
		t.Errorf("-1 %s 1 = %d, want 0", "%", r)
	}
	y = 32766
	r = x % y
	if r != -1 {
		t.Errorf("-1 %s 32766 = %d, want -1", "%", r)
	}
	y = 32767
	r = x % y
	if r != -1 {
		t.Errorf("-1 %s 32767 = %d, want -1", "%", r)
	}
	x = 0
	y = -32768
	r = x % y
	if r != 0 {
		t.Errorf("0 %s -32768 = %d, want 0", "%", r)
	}
	y = -32767
	r = x % y
	if r != 0 {
		t.Errorf("0 %s -32767 = %d, want 0", "%", r)
	}
	y = -1
	r = x % y
	if r != 0 {
		t.Errorf("0 %s -1 = %d, want 0", "%", r)
	}
	y = 1
	r = x % y
	if r != 0 {
		t.Errorf("0 %s 1 = %d, want 0", "%", r)
	}
	y = 32766
	r = x % y
	if r != 0 {
		t.Errorf("0 %s 32766 = %d, want 0", "%", r)
	}
	y = 32767
	r = x % y
	if r != 0 {
		t.Errorf("0 %s 32767 = %d, want 0", "%", r)
	}
	x = 1
	y = -32768
	r = x % y
	if r != 1 {
		t.Errorf("1 %s -32768 = %d, want 1", "%", r)
	}
	y = -32767
	r = x % y
	if r != 1 {
		t.Errorf("1 %s -32767 = %d, want 1", "%", r)
	}
	y = -1
	r = x % y
	if r != 0 {
		t.Errorf("1 %s -1 = %d, want 0", "%", r)
	}
	y = 1
	r = x % y
	if r != 0 {
		t.Errorf("1 %s 1 = %d, want 0", "%", r)
	}
	y = 32766
	r = x % y
	if r != 1 {
		t.Errorf("1 %s 32766 = %d, want 1", "%", r)
	}
	y = 32767
	r = x % y
	if r != 1 {
		t.Errorf("1 %s 32767 = %d, want 1", "%", r)
	}
	x = 32766
	y = -32768
	r = x % y
	if r != 32766 {
		t.Errorf("32766 %s -32768 = %d, want 32766", "%", r)
	}
	y = -32767
	r = x % y
	if r != 32766 {
		t.Errorf("32766 %s -32767 = %d, want 32766", "%", r)
	}
	y = -1
	r = x % y
	if r != 0 {
		t.Errorf("32766 %s -1 = %d, want 0", "%", r)
	}
	y = 1
	r = x % y
	if r != 0 {
		t.Errorf("32766 %s 1 = %d, want 0", "%", r)
	}
	y = 32766
	r = x % y
	if r != 0 {
		t.Errorf("32766 %s 32766 = %d, want 0", "%", r)
	}
	y = 32767
	r = x % y
	if r != 32766 {
		t.Errorf("32766 %s 32767 = %d, want 32766", "%", r)
	}
	x = 32767
	y = -32768
	r = x % y
	if r != 32767 {
		t.Errorf("32767 %s -32768 = %d, want 32767", "%", r)
	}
	y = -32767
	r = x % y
	if r != 0 {
		t.Errorf("32767 %s -32767 = %d, want 0", "%", r)
	}
	y = -1
	r = x % y
	if r != 0 {
		t.Errorf("32767 %s -1 = %d, want 0", "%", r)
	}
	y = 1
	r = x % y
	if r != 0 {
		t.Errorf("32767 %s 1 = %d, want 0", "%", r)
	}
	y = 32766
	r = x % y
	if r != 1 {
		t.Errorf("32767 %s 32766 = %d, want 1", "%", r)
	}
	y = 32767
	r = x % y
	if r != 0 {
		t.Errorf("32767 %s 32767 = %d, want 0", "%", r)
	}
}
func TestConstFolduint8add(t *testing.T) {
	var x, y, r uint8
	x = 0
	y = 0
	r = x + y
	if r != 0 {
		t.Errorf("0 %s 0 = %d, want 0", "+", r)
	}
	y = 1
	r = x + y
	if r != 1 {
		t.Errorf("0 %s 1 = %d, want 1", "+", r)
	}
	y = 255
	r = x + y
	if r != 255 {
		t.Errorf("0 %s 255 = %d, want 255", "+", r)
	}
	x = 1
	y = 0
	r = x + y
	if r != 1 {
		t.Errorf("1 %s 0 = %d, want 1", "+", r)
	}
	y = 1
	r = x + y
	if r != 2 {
		t.Errorf("1 %s 1 = %d, want 2", "+", r)
	}
	y = 255
	r = x + y
	if r != 0 {
		t.Errorf("1 %s 255 = %d, want 0", "+", r)
	}
	x = 255
	y = 0
	r = x + y
	if r != 255 {
		t.Errorf("255 %s 0 = %d, want 255", "+", r)
	}
	y = 1
	r = x + y
	if r != 0 {
		t.Errorf("255 %s 1 = %d, want 0", "+", r)
	}
	y = 255
	r = x + y
	if r != 254 {
		t.Errorf("255 %s 255 = %d, want 254", "+", r)
	}
}
func TestConstFolduint8sub(t *testing.T) {
	var x, y, r uint8
	x = 0
	y = 0
	r = x - y
	if r != 0 {
		t.Errorf("0 %s 0 = %d, want 0", "-", r)
	}
	y = 1
	r = x - y
	if r != 255 {
		t.Errorf("0 %s 1 = %d, want 255", "-", r)
	}
	y = 255
	r = x - y
	if r != 1 {
		t.Errorf("0 %s 255 = %d, want 1", "-", r)
	}
	x = 1
	y = 0
	r = x - y
	if r != 1 {
		t.Errorf("1 %s 0 = %d, want 1", "-", r)
	}
	y = 1
	r = x - y
	if r != 0 {
		t.Errorf("1 %s 1 = %d, want 0", "-", r)
	}
	y = 255
	r = x - y
	if r != 2 {
		t.Errorf("1 %s 255 = %d, want 2", "-", r)
	}
	x = 255
	y = 0
	r = x - y
	if r != 255 {
		t.Errorf("255 %s 0 = %d, want 255", "-", r)
	}
	y = 1
	r = x - y
	if r != 254 {
		t.Errorf("255 %s 1 = %d, want 254", "-", r)
	}
	y = 255
	r = x - y
	if r != 0 {
		t.Errorf("255 %s 255 = %d, want 0", "-", r)
	}
}
func TestConstFolduint8div(t *testing.T) {
	var x, y, r uint8
	x = 0
	y = 1
	r = x / y
	if r != 0 {
		t.Errorf("0 %s 1 = %d, want 0", "/", r)
	}
	y = 255
	r = x / y
	if r != 0 {
		t.Errorf("0 %s 255 = %d, want 0", "/", r)
	}
	x = 1
	y = 1
	r = x / y
	if r != 1 {
		t.Errorf("1 %s 1 = %d, want 1", "/", r)
	}
	y = 255
	r = x / y
	if r != 0 {
		t.Errorf("1 %s 255 = %d, want 0", "/", r)
	}
	x = 255
	y = 1
	r = x / y
	if r != 255 {
		t.Errorf("255 %s 1 = %d, want 255", "/", r)
	}
	y = 255
	r = x / y
	if r != 1 {
		t.Errorf("255 %s 255 = %d, want 1", "/", r)
	}
}
func TestConstFolduint8mul(t *testing.T) {
	var x, y, r uint8
	x = 0
	y = 0
	r = x * y
	if r != 0 {
		t.Errorf("0 %s 0 = %d, want 0", "*", r)
	}
	y = 1
	r = x * y
	if r != 0 {
		t.Errorf("0 %s 1 = %d, want 0", "*", r)
	}
	y = 255
	r = x * y
	if r != 0 {
		t.Errorf("0 %s 255 = %d, want 0", "*", r)
	}
	x = 1
	y = 0
	r = x * y
	if r != 0 {
		t.Errorf("1 %s 0 = %d, want 0", "*", r)
	}
	y = 1
	r = x * y
	if r != 1 {
		t.Errorf("1 %s 1 = %d, want 1", "*", r)
	}
	y = 255
	r = x * y
	if r != 255 {
		t.Errorf("1 %s 255 = %d, want 255", "*", r)
	}
	x = 255
	y = 0
	r = x * y
	if r != 0 {
		t.Errorf("255 %s 0 = %d, want 0", "*", r)
	}
	y = 1
	r = x * y
	if r != 255 {
		t.Errorf("255 %s 1 = %d, want 255", "*", r)
	}
	y = 255
	r = x * y
	if r != 1 {
		t.Errorf("255 %s 255 = %d, want 1", "*", r)
	}
}
func TestConstFolduint8mod(t *testing.T) {
	var x, y, r uint8
	x = 0
	y = 1
	r = x % y
	if r != 0 {
		t.Errorf("0 %s 1 = %d, want 0", "%", r)
	}
	y = 255
	r = x % y
	if r != 0 {
		t.Errorf("0 %s 255 = %d, want 0", "%", r)
	}
	x = 1
	y = 1
	r = x % y
	if r != 0 {
		t.Errorf("1 %s 1 = %d, want 0", "%", r)
	}
	y = 255
	r = x % y
	if r != 1 {
		t.Errorf("1 %s 255 = %d, want 1", "%", r)
	}
	x = 255
	y = 1
	r = x % y
	if r != 0 {
		t.Errorf("255 %s 1 = %d, want 0", "%", r)
	}
	y = 255
	r = x % y
	if r != 0 {
		t.Errorf("255 %s 255 = %d, want 0", "%", r)
	}
}
func TestConstFoldint8add(t *testing.T) {
	var x, y, r int8
	x = -128
	y = -128
	r = x + y
	if r != 0 {
		t.Errorf("-128 %s -128 = %d, want 0", "+", r)
	}
	y = -127
	r = x + y
	if r != 1 {
		t.Errorf("-128 %s -127 = %d, want 1", "+", r)
	}
	y = -1
	r = x + y
	if r != 127 {
		t.Errorf("-128 %s -1 = %d, want 127", "+", r)
	}
	y = 0
	r = x + y
	if r != -128 {
		t.Errorf("-128 %s 0 = %d, want -128", "+", r)
	}
	y = 1
	r = x + y
	if r != -127 {
		t.Errorf("-128 %s 1 = %d, want -127", "+", r)
	}
	y = 126
	r = x + y
	if r != -2 {
		t.Errorf("-128 %s 126 = %d, want -2", "+", r)
	}
	y = 127
	r = x + y
	if r != -1 {
		t.Errorf("-128 %s 127 = %d, want -1", "+", r)
	}
	x = -127
	y = -128
	r = x + y
	if r != 1 {
		t.Errorf("-127 %s -128 = %d, want 1", "+", r)
	}
	y = -127
	r = x + y
	if r != 2 {
		t.Errorf("-127 %s -127 = %d, want 2", "+", r)
	}
	y = -1
	r = x + y
	if r != -128 {
		t.Errorf("-127 %s -1 = %d, want -128", "+", r)
	}
	y = 0
	r = x + y
	if r != -127 {
		t.Errorf("-127 %s 0 = %d, want -127", "+", r)
	}
	y = 1
	r = x + y
	if r != -126 {
		t.Errorf("-127 %s 1 = %d, want -126", "+", r)
	}
	y = 126
	r = x + y
	if r != -1 {
		t.Errorf("-127 %s 126 = %d, want -1", "+", r)
	}
	y = 127
	r = x + y
	if r != 0 {
		t.Errorf("-127 %s 127 = %d, want 0", "+", r)
	}
	x = -1
	y = -128
	r = x + y
	if r != 127 {
		t.Errorf("-1 %s -128 = %d, want 127", "+", r)
	}
	y = -127
	r = x + y
	if r != -128 {
		t.Errorf("-1 %s -127 = %d, want -128", "+", r)
	}
	y = -1
	r = x + y
	if r != -2 {
		t.Errorf("-1 %s -1 = %d, want -2", "+", r)
	}
	y = 0
	r = x + y
	if r != -1 {
		t.Errorf("-1 %s 0 = %d, want -1", "+", r)
	}
	y = 1
	r = x + y
	if r != 0 {
		t.Errorf("-1 %s 1 = %d, want 0", "+", r)
	}
	y = 126
	r = x + y
	if r != 125 {
		t.Errorf("-1 %s 126 = %d, want 125", "+", r)
	}
	y = 127
	r = x + y
	if r != 126 {
		t.Errorf("-1 %s 127 = %d, want 126", "+", r)
	}
	x = 0
	y = -128
	r = x + y
	if r != -128 {
		t.Errorf("0 %s -128 = %d, want -128", "+", r)
	}
	y = -127
	r = x + y
	if r != -127 {
		t.Errorf("0 %s -127 = %d, want -127", "+", r)
	}
	y = -1
	r = x + y
	if r != -1 {
		t.Errorf("0 %s -1 = %d, want -1", "+", r)
	}
	y = 0
	r = x + y
	if r != 0 {
		t.Errorf("0 %s 0 = %d, want 0", "+", r)
	}
	y = 1
	r = x + y
	if r != 1 {
		t.Errorf("0 %s 1 = %d, want 1", "+", r)
	}
	y = 126
	r = x + y
	if r != 126 {
		t.Errorf("0 %s 126 = %d, want 126", "+", r)
	}
	y = 127
	r = x + y
	if r != 127 {
		t.Errorf("0 %s 127 = %d, want 127", "+", r)
	}
	x = 1
	y = -128
	r = x + y
	if r != -127 {
		t.Errorf("1 %s -128 = %d, want -127", "+", r)
	}
	y = -127
	r = x + y
	if r != -126 {
		t.Errorf("1 %s -127 = %d, want -126", "+", r)
	}
	y = -1
	r = x + y
	if r != 0 {
		t.Errorf("1 %s -1 = %d, want 0", "+", r)
	}
	y = 0
	r = x + y
	if r != 1 {
		t.Errorf("1 %s 0 = %d, want 1", "+", r)
	}
	y = 1
	r = x + y
	if r != 2 {
		t.Errorf("1 %s 1 = %d, want 2", "+", r)
	}
	y = 126
	r = x + y
	if r != 127 {
		t.Errorf("1 %s 126 = %d, want 127", "+", r)
	}
	y = 127
	r = x + y
	if r != -128 {
		t.Errorf("1 %s 127 = %d, want -128", "+", r)
	}
	x = 126
	y = -128
	r = x + y
	if r != -2 {
		t.Errorf("126 %s -128 = %d, want -2", "+", r)
	}
	y = -127
	r = x + y
	if r != -1 {
		t.Errorf("126 %s -127 = %d, want -1", "+", r)
	}
	y = -1
	r = x + y
	if r != 125 {
		t.Errorf("126 %s -1 = %d, want 125", "+", r)
	}
	y = 0
	r = x + y
	if r != 126 {
		t.Errorf("126 %s 0 = %d, want 126", "+", r)
	}
	y = 1
	r = x + y
	if r != 127 {
		t.Errorf("126 %s 1 = %d, want 127", "+", r)
	}
	y = 126
	r = x + y
	if r != -4 {
		t.Errorf("126 %s 126 = %d, want -4", "+", r)
	}
	y = 127
	r = x + y
	if r != -3 {
		t.Errorf("126 %s 127 = %d, want -3", "+", r)
	}
	x = 127
	y = -128
	r = x + y
	if r != -1 {
		t.Errorf("127 %s -128 = %d, want -1", "+", r)
	}
	y = -127
	r = x + y
	if r != 0 {
		t.Errorf("127 %s -127 = %d, want 0", "+", r)
	}
	y = -1
	r = x + y
	if r != 126 {
		t.Errorf("127 %s -1 = %d, want 126", "+", r)
	}
	y = 0
	r = x + y
	if r != 127 {
		t.Errorf("127 %s 0 = %d, want 127", "+", r)
	}
	y = 1
	r = x + y
	if r != -128 {
		t.Errorf("127 %s 1 = %d, want -128", "+", r)
	}
	y = 126
	r = x + y
	if r != -3 {
		t.Errorf("127 %s 126 = %d, want -3", "+", r)
	}
	y = 127
	r = x + y
	if r != -2 {
		t.Errorf("127 %s 127 = %d, want -2", "+", r)
	}
}
func TestConstFoldint8sub(t *testing.T) {
	var x, y, r int8
	x = -128
	y = -128
	r = x - y
	if r != 0 {
		t.Errorf("-128 %s -128 = %d, want 0", "-", r)
	}
	y = -127
	r = x - y
	if r != -1 {
		t.Errorf("-128 %s -127 = %d, want -1", "-", r)
	}
	y = -1
	r = x - y
	if r != -127 {
		t.Errorf("-128 %s -1 = %d, want -127", "-", r)
	}
	y = 0
	r = x - y
	if r != -128 {
		t.Errorf("-128 %s 0 = %d, want -128", "-", r)
	}
	y = 1
	r = x - y
	if r != 127 {
		t.Errorf("-128 %s 1 = %d, want 127", "-", r)
	}
	y = 126
	r = x - y
	if r != 2 {
		t.Errorf("-128 %s 126 = %d, want 2", "-", r)
	}
	y = 127
	r = x - y
	if r != 1 {
		t.Errorf("-128 %s 127 = %d, want 1", "-", r)
	}
	x = -127
	y = -128
	r = x - y
	if r != 1 {
		t.Errorf("-127 %s -128 = %d, want 1", "-", r)
	}
	y = -127
	r = x - y
	if r != 0 {
		t.Errorf("-127 %s -127 = %d, want 0", "-", r)
	}
	y = -1
	r = x - y
	if r != -126 {
		t.Errorf("-127 %s -1 = %d, want -126", "-", r)
	}
	y = 0
	r = x - y
	if r != -127 {
		t.Errorf("-127 %s 0 = %d, want -127", "-", r)
	}
	y = 1
	r = x - y
	if r != -128 {
		t.Errorf("-127 %s 1 = %d, want -128", "-", r)
	}
	y = 126
	r = x - y
	if r != 3 {
		t.Errorf("-127 %s 126 = %d, want 3", "-", r)
	}
	y = 127
	r = x - y
	if r != 2 {
		t.Errorf("-127 %s 127 = %d, want 2", "-", r)
	}
	x = -1
	y = -128
	r = x - y
	if r != 127 {
		t.Errorf("-1 %s -128 = %d, want 127", "-", r)
	}
	y = -127
	r = x - y
	if r != 126 {
		t.Errorf("-1 %s -127 = %d, want 126", "-", r)
	}
	y = -1
	r = x - y
	if r != 0 {
		t.Errorf("-1 %s -1 = %d, want 0", "-", r)
	}
	y = 0
	r = x - y
	if r != -1 {
		t.Errorf("-1 %s 0 = %d, want -1", "-", r)
	}
	y = 1
	r = x - y
	if r != -2 {
		t.Errorf("-1 %s 1 = %d, want -2", "-", r)
	}
	y = 126
	r = x - y
	if r != -127 {
		t.Errorf("-1 %s 126 = %d, want -127", "-", r)
	}
	y = 127
	r = x - y
	if r != -128 {
		t.Errorf("-1 %s 127 = %d, want -128", "-", r)
	}
	x = 0
	y = -128
	r = x - y
	if r != -128 {
		t.Errorf("0 %s -128 = %d, want -128", "-", r)
	}
	y = -127
	r = x - y
	if r != 127 {
		t.Errorf("0 %s -127 = %d, want 127", "-", r)
	}
	y = -1
	r = x - y
	if r != 1 {
		t.Errorf("0 %s -1 = %d, want 1", "-", r)
	}
	y = 0
	r = x - y
	if r != 0 {
		t.Errorf("0 %s 0 = %d, want 0", "-", r)
	}
	y = 1
	r = x - y
	if r != -1 {
		t.Errorf("0 %s 1 = %d, want -1", "-", r)
	}
	y = 126
	r = x - y
	if r != -126 {
		t.Errorf("0 %s 126 = %d, want -126", "-", r)
	}
	y = 127
	r = x - y
	if r != -127 {
		t.Errorf("0 %s 127 = %d, want -127", "-", r)
	}
	x = 1
	y = -128
	r = x - y
	if r != -127 {
		t.Errorf("1 %s -128 = %d, want -127", "-", r)
	}
	y = -127
	r = x - y
	if r != -128 {
		t.Errorf("1 %s -127 = %d, want -128", "-", r)
	}
	y = -1
	r = x - y
	if r != 2 {
		t.Errorf("1 %s -1 = %d, want 2", "-", r)
	}
	y = 0
	r = x - y
	if r != 1 {
		t.Errorf("1 %s 0 = %d, want 1", "-", r)
	}
	y = 1
	r = x - y
	if r != 0 {
		t.Errorf("1 %s 1 = %d, want 0", "-", r)
	}
	y = 126
	r = x - y
	if r != -125 {
		t.Errorf("1 %s 126 = %d, want -125", "-", r)
	}
	y = 127
	r = x - y
	if r != -126 {
		t.Errorf("1 %s 127 = %d, want -126", "-", r)
	}
	x = 126
	y = -128
	r = x - y
	if r != -2 {
		t.Errorf("126 %s -128 = %d, want -2", "-", r)
	}
	y = -127
	r = x - y
	if r != -3 {
		t.Errorf("126 %s -127 = %d, want -3", "-", r)
	}
	y = -1
	r = x - y
	if r != 127 {
		t.Errorf("126 %s -1 = %d, want 127", "-", r)
	}
	y = 0
	r = x - y
	if r != 126 {
		t.Errorf("126 %s 0 = %d, want 126", "-", r)
	}
	y = 1
	r = x - y
	if r != 125 {
		t.Errorf("126 %s 1 = %d, want 125", "-", r)
	}
	y = 126
	r = x - y
	if r != 0 {
		t.Errorf("126 %s 126 = %d, want 0", "-", r)
	}
	y = 127
	r = x - y
	if r != -1 {
		t.Errorf("126 %s 127 = %d, want -1", "-", r)
	}
	x = 127
	y = -128
	r = x - y
	if r != -1 {
		t.Errorf("127 %s -128 = %d, want -1", "-", r)
	}
	y = -127
	r = x - y
	if r != -2 {
		t.Errorf("127 %s -127 = %d, want -2", "-", r)
	}
	y = -1
	r = x - y
	if r != -128 {
		t.Errorf("127 %s -1 = %d, want -128", "-", r)
	}
	y = 0
	r = x - y
	if r != 127 {
		t.Errorf("127 %s 0 = %d, want 127", "-", r)
	}
	y = 1
	r = x - y
	if r != 126 {
		t.Errorf("127 %s 1 = %d, want 126", "-", r)
	}
	y = 126
	r = x - y
	if r != 1 {
		t.Errorf("127 %s 126 = %d, want 1", "-", r)
	}
	y = 127
	r = x - y
	if r != 0 {
		t.Errorf("127 %s 127 = %d, want 0", "-", r)
	}
}
func TestConstFoldint8div(t *testing.T) {
	var x, y, r int8
	x = -128
	y = -128
	r = x / y
	if r != 1 {
		t.Errorf("-128 %s -128 = %d, want 1", "/", r)
	}
	y = -127
	r = x / y
	if r != 1 {
		t.Errorf("-128 %s -127 = %d, want 1", "/", r)
	}
	y = -1
	r = x / y
	if r != -128 {
		t.Errorf("-128 %s -1 = %d, want -128", "/", r)
	}
	y = 1
	r = x / y
	if r != -128 {
		t.Errorf("-128 %s 1 = %d, want -128", "/", r)
	}
	y = 126
	r = x / y
	if r != -1 {
		t.Errorf("-128 %s 126 = %d, want -1", "/", r)
	}
	y = 127
	r = x / y
	if r != -1 {
		t.Errorf("-128 %s 127 = %d, want -1", "/", r)
	}
	x = -127
	y = -128
	r = x / y
	if r != 0 {
		t.Errorf("-127 %s -128 = %d, want 0", "/", r)
	}
	y = -127
	r = x / y
	if r != 1 {
		t.Errorf("-127 %s -127 = %d, want 1", "/", r)
	}
	y = -1
	r = x / y
	if r != 127 {
		t.Errorf("-127 %s -1 = %d, want 127", "/", r)
	}
	y = 1
	r = x / y
	if r != -127 {
		t.Errorf("-127 %s 1 = %d, want -127", "/", r)
	}
	y = 126
	r = x / y
	if r != -1 {
		t.Errorf("-127 %s 126 = %d, want -1", "/", r)
	}
	y = 127
	r = x / y
	if r != -1 {
		t.Errorf("-127 %s 127 = %d, want -1", "/", r)
	}
	x = -1
	y = -128
	r = x / y
	if r != 0 {
		t.Errorf("-1 %s -128 = %d, want 0", "/", r)
	}
	y = -127
	r = x / y
	if r != 0 {
		t.Errorf("-1 %s -127 = %d, want 0", "/", r)
	}
	y = -1
	r = x / y
	if r != 1 {
		t.Errorf("-1 %s -1 = %d, want 1", "/", r)
	}
	y = 1
	r = x / y
	if r != -1 {
		t.Errorf("-1 %s 1 = %d, want -1", "/", r)
	}
	y = 126
	r = x / y
	if r != 0 {
		t.Errorf("-1 %s 126 = %d, want 0", "/", r)
	}
	y = 127
	r = x / y
	if r != 0 {
		t.Errorf("-1 %s 127 = %d, want 0", "/", r)
	}
	x = 0
	y = -128
	r = x / y
	if r != 0 {
		t.Errorf("0 %s -128 = %d, want 0", "/", r)
	}
	y = -127
	r = x / y
	if r != 0 {
		t.Errorf("0 %s -127 = %d, want 0", "/", r)
	}
	y = -1
	r = x / y
	if r != 0 {
		t.Errorf("0 %s -1 = %d, want 0", "/", r)
	}
	y = 1
	r = x / y
	if r != 0 {
		t.Errorf("0 %s 1 = %d, want 0", "/", r)
	}
	y = 126
	r = x / y
	if r != 0 {
		t.Errorf("0 %s 126 = %d, want 0", "/", r)
	}
	y = 127
	r = x / y
	if r != 0 {
		t.Errorf("0 %s 127 = %d, want 0", "/", r)
	}
	x = 1
	y = -128
	r = x / y
	if r != 0 {
		t.Errorf("1 %s -128 = %d, want 0", "/", r)
	}
	y = -127
	r = x / y
	if r != 0 {
		t.Errorf("1 %s -127 = %d, want 0", "/", r)
	}
	y = -1
	r = x / y
	if r != -1 {
		t.Errorf("1 %s -1 = %d, want -1", "/", r)
	}
	y = 1
	r = x / y
	if r != 1 {
		t.Errorf("1 %s 1 = %d, want 1", "/", r)
	}
	y = 126
	r = x / y
	if r != 0 {
		t.Errorf("1 %s 126 = %d, want 0", "/", r)
	}
	y = 127
	r = x / y
	if r != 0 {
		t.Errorf("1 %s 127 = %d, want 0", "/", r)
	}
	x = 126
	y = -128
	r = x / y
	if r != 0 {
		t.Errorf("126 %s -128 = %d, want 0", "/", r)
	}
	y = -127
	r = x / y
	if r != 0 {
		t.Errorf("126 %s -127 = %d, want 0", "/", r)
	}
	y = -1
	r = x / y
	if r != -126 {
		t.Errorf("126 %s -1 = %d, want -126", "/", r)
	}
	y = 1
	r = x / y
	if r != 126 {
		t.Errorf("126 %s 1 = %d, want 126", "/", r)
	}
	y = 126
	r = x / y
	if r != 1 {
		t.Errorf("126 %s 126 = %d, want 1", "/", r)
	}
	y = 127
	r = x / y
	if r != 0 {
		t.Errorf("126 %s 127 = %d, want 0", "/", r)
	}
	x = 127
	y = -128
	r = x / y
	if r != 0 {
		t.Errorf("127 %s -128 = %d, want 0", "/", r)
	}
	y = -127
	r = x / y
	if r != -1 {
		t.Errorf("127 %s -127 = %d, want -1", "/", r)
	}
	y = -1
	r = x / y
	if r != -127 {
		t.Errorf("127 %s -1 = %d, want -127", "/", r)
	}
	y = 1
	r = x / y
	if r != 127 {
		t.Errorf("127 %s 1 = %d, want 127", "/", r)
	}
	y = 126
	r = x / y
	if r != 1 {
		t.Errorf("127 %s 126 = %d, want 1", "/", r)
	}
	y = 127
	r = x / y
	if r != 1 {
		t.Errorf("127 %s 127 = %d, want 1", "/", r)
	}
}
func TestConstFoldint8mul(t *testing.T) {
	var x, y, r int8
	x = -128
	y = -128
	r = x * y
	if r != 0 {
		t.Errorf("-128 %s -128 = %d, want 0", "*", r)
	}
	y = -127
	r = x * y
	if r != -128 {
		t.Errorf("-128 %s -127 = %d, want -128", "*", r)
	}
	y = -1
	r = x * y
	if r != -128 {
		t.Errorf("-128 %s -1 = %d, want -128", "*", r)
	}
	y = 0
	r = x * y
	if r != 0 {
		t.Errorf("-128 %s 0 = %d, want 0", "*", r)
	}
	y = 1
	r = x * y
	if r != -128 {
		t.Errorf("-128 %s 1 = %d, want -128", "*", r)
	}
	y = 126
	r = x * y
	if r != 0 {
		t.Errorf("-128 %s 126 = %d, want 0", "*", r)
	}
	y = 127
	r = x * y
	if r != -128 {
		t.Errorf("-128 %s 127 = %d, want -128", "*", r)
	}
	x = -127
	y = -128
	r = x * y
	if r != -128 {
		t.Errorf("-127 %s -128 = %d, want -128", "*", r)
	}
	y = -127
	r = x * y
	if r != 1 {
		t.Errorf("-127 %s -127 = %d, want 1", "*", r)
	}
	y = -1
	r = x * y
	if r != 127 {
		t.Errorf("-127 %s -1 = %d, want 127", "*", r)
	}
	y = 0
	r = x * y
	if r != 0 {
		t.Errorf("-127 %s 0 = %d, want 0", "*", r)
	}
	y = 1
	r = x * y
	if r != -127 {
		t.Errorf("-127 %s 1 = %d, want -127", "*", r)
	}
	y = 126
	r = x * y
	if r != 126 {
		t.Errorf("-127 %s 126 = %d, want 126", "*", r)
	}
	y = 127
	r = x * y
	if r != -1 {
		t.Errorf("-127 %s 127 = %d, want -1", "*", r)
	}
	x = -1
	y = -128
	r = x * y
	if r != -128 {
		t.Errorf("-1 %s -128 = %d, want -128", "*", r)
	}
	y = -127
	r = x * y
	if r != 127 {
		t.Errorf("-1 %s -127 = %d, want 127", "*", r)
	}
	y = -1
	r = x * y
	if r != 1 {
		t.Errorf("-1 %s -1 = %d, want 1", "*", r)
	}
	y = 0
	r = x * y
	if r != 0 {
		t.Errorf("-1 %s 0 = %d, want 0", "*", r)
	}
	y = 1
	r = x * y
	if r != -1 {
		t.Errorf("-1 %s 1 = %d, want -1", "*", r)
	}
	y = 126
	r = x * y
	if r != -126 {
		t.Errorf("-1 %s 126 = %d, want -126", "*", r)
	}
	y = 127
	r = x * y
	if r != -127 {
		t.Errorf("-1 %s 127 = %d, want -127", "*", r)
	}
	x = 0
	y = -128
	r = x * y
	if r != 0 {
		t.Errorf("0 %s -128 = %d, want 0", "*", r)
	}
	y = -127
	r = x * y
	if r != 0 {
		t.Errorf("0 %s -127 = %d, want 0", "*", r)
	}
	y = -1
	r = x * y
	if r != 0 {
		t.Errorf("0 %s -1 = %d, want 0", "*", r)
	}
	y = 0
	r = x * y
	if r != 0 {
		t.Errorf("0 %s 0 = %d, want 0", "*", r)
	}
	y = 1
	r = x * y
	if r != 0 {
		t.Errorf("0 %s 1 = %d, want 0", "*", r)
	}
	y = 126
	r = x * y
	if r != 0 {
		t.Errorf("0 %s 126 = %d, want 0", "*", r)
	}
	y = 127
	r = x * y
	if r != 0 {
		t.Errorf("0 %s 127 = %d, want 0", "*", r)
	}
	x = 1
	y = -128
	r = x * y
	if r != -128 {
		t.Errorf("1 %s -128 = %d, want -128", "*", r)
	}
	y = -127
	r = x * y
	if r != -127 {
		t.Errorf("1 %s -127 = %d, want -127", "*", r)
	}
	y = -1
	r = x * y
	if r != -1 {
		t.Errorf("1 %s -1 = %d, want -1", "*", r)
	}
	y = 0
	r = x * y
	if r != 0 {
		t.Errorf("1 %s 0 = %d, want 0", "*", r)
	}
	y = 1
	r = x * y
	if r != 1 {
		t.Errorf("1 %s 1 = %d, want 1", "*", r)
	}
	y = 126
	r = x * y
	if r != 126 {
		t.Errorf("1 %s 126 = %d, want 126", "*", r)
	}
	y = 127
	r = x * y
	if r != 127 {
		t.Errorf("1 %s 127 = %d, want 127", "*", r)
	}
	x = 126
	y = -128
	r = x * y
	if r != 0 {
		t.Errorf("126 %s -128 = %d, want 0", "*", r)
	}
	y = -127
	r = x * y
	if r != 126 {
		t.Errorf("126 %s -127 = %d, want 126", "*", r)
	}
	y = -1
	r = x * y
	if r != -126 {
		t.Errorf("126 %s -1 = %d, want -126", "*", r)
	}
	y = 0
	r = x * y
	if r != 0 {
		t.Errorf("126 %s 0 = %d, want 0", "*", r)
	}
	y = 1
	r = x * y
	if r != 126 {
		t.Errorf("126 %s 1 = %d, want 126", "*", r)
	}
	y = 126
	r = x * y
	if r != 4 {
		t.Errorf("126 %s 126 = %d, want 4", "*", r)
	}
	y = 127
	r = x * y
	if r != -126 {
		t.Errorf("126 %s 127 = %d, want -126", "*", r)
	}
	x = 127
	y = -128
	r = x * y
	if r != -128 {
		t.Errorf("127 %s -128 = %d, want -128", "*", r)
	}
	y = -127
	r = x * y
	if r != -1 {
		t.Errorf("127 %s -127 = %d, want -1", "*", r)
	}
	y = -1
	r = x * y
	if r != -127 {
		t.Errorf("127 %s -1 = %d, want -127", "*", r)
	}
	y = 0
	r = x * y
	if r != 0 {
		t.Errorf("127 %s 0 = %d, want 0", "*", r)
	}
	y = 1
	r = x * y
	if r != 127 {
		t.Errorf("127 %s 1 = %d, want 127", "*", r)
	}
	y = 126
	r = x * y
	if r != -126 {
		t.Errorf("127 %s 126 = %d, want -126", "*", r)
	}
	y = 127
	r = x * y
	if r != 1 {
		t.Errorf("127 %s 127 = %d, want 1", "*", r)
	}
}
func TestConstFoldint8mod(t *testing.T) {
	var x, y, r int8
	x = -128
	y = -128
	r = x % y
	if r != 0 {
		t.Errorf("-128 %s -128 = %d, want 0", "%", r)
	}
	y = -127
	r = x % y
	if r != -1 {
		t.Errorf("-128 %s -127 = %d, want -1", "%", r)
	}
	y = -1
	r = x % y
	if r != 0 {
		t.Errorf("-128 %s -1 = %d, want 0", "%", r)
	}
	y = 1
	r = x % y
	if r != 0 {
		t.Errorf("-128 %s 1 = %d, want 0", "%", r)
	}
	y = 126
	r = x % y
	if r != -2 {
		t.Errorf("-128 %s 126 = %d, want -2", "%", r)
	}
	y = 127
	r = x % y
	if r != -1 {
		t.Errorf("-128 %s 127 = %d, want -1", "%", r)
	}
	x = -127
	y = -128
	r = x % y
	if r != -127 {
		t.Errorf("-127 %s -128 = %d, want -127", "%", r)
	}
	y = -127
	r = x % y
	if r != 0 {
		t.Errorf("-127 %s -127 = %d, want 0", "%", r)
	}
	y = -1
	r = x % y
	if r != 0 {
		t.Errorf("-127 %s -1 = %d, want 0", "%", r)
	}
	y = 1
	r = x % y
	if r != 0 {
		t.Errorf("-127 %s 1 = %d, want 0", "%", r)
	}
	y = 126
	r = x % y
	if r != -1 {
		t.Errorf("-127 %s 126 = %d, want -1", "%", r)
	}
	y = 127
	r = x % y
	if r != 0 {
		t.Errorf("-127 %s 127 = %d, want 0", "%", r)
	}
	x = -1
	y = -128
	r = x % y
	if r != -1 {
		t.Errorf("-1 %s -128 = %d, want -1", "%", r)
	}
	y = -127
	r = x % y
	if r != -1 {
		t.Errorf("-1 %s -127 = %d, want -1", "%", r)
	}
	y = -1
	r = x % y
	if r != 0 {
		t.Errorf("-1 %s -1 = %d, want 0", "%", r)
	}
	y = 1
	r = x % y
	if r != 0 {
		t.Errorf("-1 %s 1 = %d, want 0", "%", r)
	}
	y = 126
	r = x % y
	if r != -1 {
		t.Errorf("-1 %s 126 = %d, want -1", "%", r)
	}
	y = 127
	r = x % y
	if r != -1 {
		t.Errorf("-1 %s 127 = %d, want -1", "%", r)
	}
	x = 0
	y = -128
	r = x % y
	if r != 0 {
		t.Errorf("0 %s -128 = %d, want 0", "%", r)
	}
	y = -127
	r = x % y
	if r != 0 {
		t.Errorf("0 %s -127 = %d, want 0", "%", r)
	}
	y = -1
	r = x % y
	if r != 0 {
		t.Errorf("0 %s -1 = %d, want 0", "%", r)
	}
	y = 1
	r = x % y
	if r != 0 {
		t.Errorf("0 %s 1 = %d, want 0", "%", r)
	}
	y = 126
	r = x % y
	if r != 0 {
		t.Errorf("0 %s 126 = %d, want 0", "%", r)
	}
	y = 127
	r = x % y
	if r != 0 {
		t.Errorf("0 %s 127 = %d, want 0", "%", r)
	}
	x = 1
	y = -128
	r = x % y
	if r != 1 {
		t.Errorf("1 %s -128 = %d, want 1", "%", r)
	}
	y = -127
	r = x % y
	if r != 1 {
		t.Errorf("1 %s -127 = %d, want 1", "%", r)
	}
	y = -1
	r = x % y
	if r != 0 {
		t.Errorf("1 %s -1 = %d, want 0", "%", r)
	}
	y = 1
	r = x % y
	if r != 0 {
		t.Errorf("1 %s 1 = %d, want 0", "%", r)
	}
	y = 126
	r = x % y
	if r != 1 {
		t.Errorf("1 %s 126 = %d, want 1", "%", r)
	}
	y = 127
	r = x % y
	if r != 1 {
		t.Errorf("1 %s 127 = %d, want 1", "%", r)
	}
	x = 126
	y = -128
	r = x % y
	if r != 126 {
		t.Errorf("126 %s -128 = %d, want 126", "%", r)
	}
	y = -127
	r = x % y
	if r != 126 {
		t.Errorf("126 %s -127 = %d, want 126", "%", r)
	}
	y = -1
	r = x % y
	if r != 0 {
		t.Errorf("126 %s -1 = %d, want 0", "%", r)
	}
	y = 1
	r = x % y
	if r != 0 {
		t.Errorf("126 %s 1 = %d, want 0", "%", r)
	}
	y = 126
	r = x % y
	if r != 0 {
		t.Errorf("126 %s 126 = %d, want 0", "%", r)
	}
	y = 127
	r = x % y
	if r != 126 {
		t.Errorf("126 %s 127 = %d, want 126", "%", r)
	}
	x = 127
	y = -128
	r = x % y
	if r != 127 {
		t.Errorf("127 %s -128 = %d, want 127", "%", r)
	}
	y = -127
	r = x % y
	if r != 0 {
		t.Errorf("127 %s -127 = %d, want 0", "%", r)
	}
	y = -1
	r = x % y
	if r != 0 {
		t.Errorf("127 %s -1 = %d, want 0", "%", r)
	}
	y = 1
	r = x % y
	if r != 0 {
		t.Errorf("127 %s 1 = %d, want 0", "%", r)
	}
	y = 126
	r = x % y
	if r != 1 {
		t.Errorf("127 %s 126 = %d, want 1", "%", r)
	}
	y = 127
	r = x % y
	if r != 0 {
		t.Errorf("127 %s 127 = %d, want 0", "%", r)
	}
}
func TestConstFolduint64uint64lsh(t *testing.T) {
	var x, r uint64
	var y uint64
	x = 0
	y = 0
	r = x << y
	if r != 0 {
		t.Errorf("0 %s 0 = %d, want 0", "<<", r)
	}
	y = 1
	r = x << y
	if r != 0 {
		t.Errorf("0 %s 1 = %d, want 0", "<<", r)
	}
	y = 4294967296
	r = x << y
	if r != 0 {
		t.Errorf("0 %s 4294967296 = %d, want 0", "<<", r)
	}
	y = 18446744073709551615
	r = x << y
	if r != 0 {
		t.Errorf("0 %s 18446744073709551615 = %d, want 0", "<<", r)
	}
	x = 1
	y = 0
	r = x << y
	if r != 1 {
		t.Errorf("1 %s 0 = %d, want 1", "<<", r)
	}
	y = 1
	r = x << y
	if r != 2 {
		t.Errorf("1 %s 1 = %d, want 2", "<<", r)
	}
	y = 4294967296
	r = x << y
	if r != 0 {
		t.Errorf("1 %s 4294967296 = %d, want 0", "<<", r)
	}
	y = 18446744073709551615
	r = x << y
	if r != 0 {
		t.Errorf("1 %s 18446744073709551615 = %d, want 0", "<<", r)
	}
	x = 4294967296
	y = 0
	r = x << y
	if r != 4294967296 {
		t.Errorf("4294967296 %s 0 = %d, want 4294967296", "<<", r)
	}
	y = 1
	r = x << y
	if r != 8589934592 {
		t.Errorf("4294967296 %s 1 = %d, want 8589934592", "<<", r)
	}
	y = 4294967296
	r = x << y
	if r != 0 {
		t.Errorf("4294967296 %s 4294967296 = %d, want 0", "<<", r)
	}
	y = 18446744073709551615
	r = x << y
	if r != 0 {
		t.Errorf("4294967296 %s 18446744073709551615 = %d, want 0", "<<", r)
	}
	x = 18446744073709551615
	y = 0
	r = x << y
	if r != 18446744073709551615 {
		t.Errorf("18446744073709551615 %s 0 = %d, want 18446744073709551615", "<<", r)
	}
	y = 1
	r = x << y
	if r != 18446744073709551614 {
		t.Errorf("18446744073709551615 %s 1 = %d, want 18446744073709551614", "<<", r)
	}
	y = 4294967296
	r = x << y
	if r != 0 {
		t.Errorf("18446744073709551615 %s 4294967296 = %d, want 0", "<<", r)
	}
	y = 18446744073709551615
	r = x << y
	if r != 0 {
		t.Errorf("18446744073709551615 %s 18446744073709551615 = %d, want 0", "<<", r)
	}
}
func TestConstFolduint64uint64rsh(t *testing.T) {
	var x, r uint64
	var y uint64
	x = 0
	y = 0
	r = x >> y
	if r != 0 {
		t.Errorf("0 %s 0 = %d, want 0", ">>", r)
	}
	y = 1
	r = x >> y
	if r != 0 {
		t.Errorf("0 %s 1 = %d, want 0", ">>", r)
	}
	y = 4294967296
	r = x >> y
	if r != 0 {
		t.Errorf("0 %s 4294967296 = %d, want 0", ">>", r)
	}
	y = 18446744073709551615
	r = x >> y
	if r != 0 {
		t.Errorf("0 %s 18446744073709551615 = %d, want 0", ">>", r)
	}
	x = 1
	y = 0
	r = x >> y
	if r != 1 {
		t.Errorf("1 %s 0 = %d, want 1", ">>", r)
	}
	y = 1
	r = x >> y
	if r != 0 {
		t.Errorf("1 %s 1 = %d, want 0", ">>", r)
	}
	y = 4294967296
	r = x >> y
	if r != 0 {
		t.Errorf("1 %s 4294967296 = %d, want 0", ">>", r)
	}
	y = 18446744073709551615
	r = x >> y
	if r != 0 {
		t.Errorf("1 %s 18446744073709551615 = %d, want 0", ">>", r)
	}
	x = 4294967296
	y = 0
	r = x >> y
	if r != 4294967296 {
		t.Errorf("4294967296 %s 0 = %d, want 4294967296", ">>", r)
	}
	y = 1
	r = x >> y
	if r != 2147483648 {
		t.Errorf("4294967296 %s 1 = %d, want 2147483648", ">>", r)
	}
	y = 4294967296
	r = x >> y
	if r != 0 {
		t.Errorf("4294967296 %s 4294967296 = %d, want 0", ">>", r)
	}
	y = 18446744073709551615
	r = x >> y
	if r != 0 {
		t.Errorf("4294967296 %s 18446744073709551615 = %d, want 0", ">>", r)
	}
	x = 18446744073709551615
	y = 0
	r = x >> y
	if r != 18446744073709551615 {
		t.Errorf("18446744073709551615 %s 0 = %d, want 18446744073709551615", ">>", r)
	}
	y = 1
	r = x >> y
	if r != 9223372036854775807 {
		t.Errorf("18446744073709551615 %s 1 = %d, want 9223372036854775807", ">>", r)
	}
	y = 4294967296
	r = x >> y
	if r != 0 {
		t.Errorf("18446744073709551615 %s 4294967296 = %d, want 0", ">>", r)
	}
	y = 18446744073709551615
	r = x >> y
	if r != 0 {
		t.Errorf("18446744073709551615 %s 18446744073709551615 = %d, want 0", ">>", r)
	}
}
func TestConstFolduint64uint32lsh(t *testing.T) {
	var x, r uint64
	var y uint32
	x = 0
	y = 0
	r = x << y
	if r != 0 {
		t.Errorf("0 %s 0 = %d, want 0", "<<", r)
	}
	y = 1
	r = x << y
	if r != 0 {
		t.Errorf("0 %s 1 = %d, want 0", "<<", r)
	}
	y = 4294967295
	r = x << y
	if r != 0 {
		t.Errorf("0 %s 4294967295 = %d, want 0", "<<", r)
	}
	x = 1
	y = 0
	r = x << y
	if r != 1 {
		t.Errorf("1 %s 0 = %d, want 1", "<<", r)
	}
	y = 1
	r = x << y
	if r != 2 {
		t.Errorf("1 %s 1 = %d, want 2", "<<", r)
	}
	y = 4294967295
	r = x << y
	if r != 0 {
		t.Errorf("1 %s 4294967295 = %d, want 0", "<<", r)
	}
	x = 4294967296
	y = 0
	r = x << y
	if r != 4294967296 {
		t.Errorf("4294967296 %s 0 = %d, want 4294967296", "<<", r)
	}
	y = 1
	r = x << y
	if r != 8589934592 {
		t.Errorf("4294967296 %s 1 = %d, want 8589934592", "<<", r)
	}
	y = 4294967295
	r = x << y
	if r != 0 {
		t.Errorf("4294967296 %s 4294967295 = %d, want 0", "<<", r)
	}
	x = 18446744073709551615
	y = 0
	r = x << y
	if r != 18446744073709551615 {
		t.Errorf("18446744073709551615 %s 0 = %d, want 18446744073709551615", "<<", r)
	}
	y = 1
	r = x << y
	if r != 18446744073709551614 {
		t.Errorf("18446744073709551615 %s 1 = %d, want 18446744073709551614", "<<", r)
	}
	y = 4294967295
	r = x << y
	if r != 0 {
		t.Errorf("18446744073709551615 %s 4294967295 = %d, want 0", "<<", r)
	}
}
func TestConstFolduint64uint32rsh(t *testing.T) {
	var x, r uint64
	var y uint32
	x = 0
	y = 0
	r = x >> y
	if r != 0 {
		t.Errorf("0 %s 0 = %d, want 0", ">>", r)
	}
	y = 1
	r = x >> y
	if r != 0 {
		t.Errorf("0 %s 1 = %d, want 0", ">>", r)
	}
	y = 4294967295
	r = x >> y
	if r != 0 {
		t.Errorf("0 %s 4294967295 = %d, want 0", ">>", r)
	}
	x = 1
	y = 0
	r = x >> y
	if r != 1 {
		t.Errorf("1 %s 0 = %d, want 1", ">>", r)
	}
	y = 1
	r = x >> y
	if r != 0 {
		t.Errorf("1 %s 1 = %d, want 0", ">>", r)
	}
	y = 4294967295
	r = x >> y
	if r != 0 {
		t.Errorf("1 %s 4294967295 = %d, want 0", ">>", r)
	}
	x = 4294967296
	y = 0
	r = x >> y
	if r != 4294967296 {
		t.Errorf("4294967296 %s 0 = %d, want 4294967296", ">>", r)
	}
	y = 1
	r = x >> y
	if r != 2147483648 {
		t.Errorf("4294967296 %s 1 = %d, want 2147483648", ">>", r)
	}
	y = 4294967295
	r = x >> y
	if r != 0 {
		t.Errorf("4294967296 %s 4294967295 = %d, want 0", ">>", r)
	}
	x = 18446744073709551615
	y = 0
	r = x >> y
	if r != 18446744073709551615 {
		t.Errorf("18446744073709551615 %s 0 = %d, want 18446744073709551615", ">>", r)
	}
	y = 1
	r = x >> y
	if r != 9223372036854775807 {
		t.Errorf("18446744073709551615 %s 1 = %d, want 9223372036854775807", ">>", r)
	}
	y = 4294967295
	r = x >> y
	if r != 0 {
		t.Errorf("18446744073709551615 %s 4294967295 = %d, want 0", ">>", r)
	}
}
func TestConstFolduint64uint16lsh(t *testing.T) {
	var x, r uint64
	var y uint16
	x = 0
	y = 0
	r = x << y
	if r != 0 {
		t.Errorf("0 %s 0 = %d, want 0", "<<", r)
	}
	y = 1
	r = x << y
	if r != 0 {
		t.Errorf("0 %s 1 = %d, want 0", "<<", r)
	}
	y = 65535
	r = x << y
	if r != 0 {
		t.Errorf("0 %s 65535 = %d, want 0", "<<", r)
	}
	x = 1
	y = 0
	r = x << y
	if r != 1 {
		t.Errorf("1 %s 0 = %d, want 1", "<<", r)
	}
	y = 1
	r = x << y
	if r != 2 {
		t.Errorf("1 %s 1 = %d, want 2", "<<", r)
	}
	y = 65535
	r = x << y
	if r != 0 {
		t.Errorf("1 %s 65535 = %d, want 0", "<<", r)
	}
	x = 4294967296
	y = 0
	r = x << y
	if r != 4294967296 {
		t.Errorf("4294967296 %s 0 = %d, want 4294967296", "<<", r)
	}
	y = 1
	r = x << y
	if r != 8589934592 {
		t.Errorf("4294967296 %s 1 = %d, want 8589934592", "<<", r)
	}
	y = 65535
	r = x << y
	if r != 0 {
		t.Errorf("4294967296 %s 65535 = %d, want 0", "<<", r)
	}
	x = 18446744073709551615
	y = 0
	r = x << y
	if r != 18446744073709551615 {
		t.Errorf("18446744073709551615 %s 0 = %d, want 18446744073709551615", "<<", r)
	}
	y = 1
	r = x << y
	if r != 18446744073709551614 {
		t.Errorf("18446744073709551615 %s 1 = %d, want 18446744073709551614", "<<", r)
	}
	y = 65535
	r = x << y
	if r != 0 {
		t.Errorf("18446744073709551615 %s 65535 = %d, want 0", "<<", r)
	}
}
func TestConstFolduint64uint16rsh(t *testing.T) {
	var x, r uint64
	var y uint16
	x = 0
	y = 0
	r = x >> y
	if r != 0 {
		t.Errorf("0 %s 0 = %d, want 0", ">>", r)
	}
	y = 1
	r = x >> y
	if r != 0 {
		t.Errorf("0 %s 1 = %d, want 0", ">>", r)
	}
	y = 65535
	r = x >> y
	if r != 0 {
		t.Errorf("0 %s 65535 = %d, want 0", ">>", r)
	}
	x = 1
	y = 0
	r = x >> y
	if r != 1 {
		t.Errorf("1 %s 0 = %d, want 1", ">>", r)
	}
	y = 1
	r = x >> y
	if r != 0 {
		t.Errorf("1 %s 1 = %d, want 0", ">>", r)
	}
	y = 65535
	r = x >> y
	if r != 0 {
		t.Errorf("1 %s 65535 = %d, want 0", ">>", r)
	}
	x = 4294967296
	y = 0
	r = x >> y
	if r != 4294967296 {
		t.Errorf("4294967296 %s 0 = %d, want 4294967296", ">>", r)
	}
	y = 1
	r = x >> y
	if r != 2147483648 {
		t.Errorf("4294967296 %s 1 = %d, want 2147483648", ">>", r)
	}
	y = 65535
	r = x >> y
	if r != 0 {
		t.Errorf("4294967296 %s 65535 = %d, want 0", ">>", r)
	}
	x = 18446744073709551615
	y = 0
	r = x >> y
	if r != 18446744073709551615 {
		t.Errorf("18446744073709551615 %s 0 = %d, want 18446744073709551615", ">>", r)
	}
	y = 1
	r = x >> y
	if r != 9223372036854775807 {
		t.Errorf("18446744073709551615 %s 1 = %d, want 9223372036854775807", ">>", r)
	}
	y = 65535
	r = x >> y
	if r != 0 {
		t.Errorf("18446744073709551615 %s 65535 = %d, want 0", ">>", r)
	}
}
func TestConstFolduint64uint8lsh(t *testing.T) {
	var x, r uint64
	var y uint8
	x = 0
	y = 0
	r = x << y
	if r != 0 {
		t.Errorf("0 %s 0 = %d, want 0", "<<", r)
	}
	y = 1
	r = x << y
	if r != 0 {
		t.Errorf("0 %s 1 = %d, want 0", "<<", r)
	}
	y = 255
	r = x << y
	if r != 0 {
		t.Errorf("0 %s 255 = %d, want 0", "<<", r)
	}
	x = 1
	y = 0
	r = x << y
	if r != 1 {
		t.Errorf("1 %s 0 = %d, want 1", "<<", r)
	}
	y = 1
	r = x << y
	if r != 2 {
		t.Errorf("1 %s 1 = %d, want 2", "<<", r)
	}
	y = 255
	r = x << y
	if r != 0 {
		t.Errorf("1 %s 255 = %d, want 0", "<<", r)
	}
	x = 4294967296
	y = 0
	r = x << y
	if r != 4294967296 {
		t.Errorf("4294967296 %s 0 = %d, want 4294967296", "<<", r)
	}
	y = 1
	r = x << y
	if r != 8589934592 {
		t.Errorf("4294967296 %s 1 = %d, want 8589934592", "<<", r)
	}
	y = 255
	r = x << y
	if r != 0 {
		t.Errorf("4294967296 %s 255 = %d, want 0", "<<", r)
	}
	x = 18446744073709551615
	y = 0
	r = x << y
	if r != 18446744073709551615 {
		t.Errorf("18446744073709551615 %s 0 = %d, want 18446744073709551615", "<<", r)
	}
	y = 1
	r = x << y
	if r != 18446744073709551614 {
		t.Errorf("18446744073709551615 %s 1 = %d, want 18446744073709551614", "<<", r)
	}
	y = 255
	r = x << y
	if r != 0 {
		t.Errorf("18446744073709551615 %s 255 = %d, want 0", "<<", r)
	}
}
func TestConstFolduint64uint8rsh(t *testing.T) {
	var x, r uint64
	var y uint8
	x = 0
	y = 0
	r = x >> y
	if r != 0 {
		t.Errorf("0 %s 0 = %d, want 0", ">>", r)
	}
	y = 1
	r = x >> y
	if r != 0 {
		t.Errorf("0 %s 1 = %d, want 0", ">>", r)
	}
	y = 255
	r = x >> y
	if r != 0 {
		t.Errorf("0 %s 255 = %d, want 0", ">>", r)
	}
	x = 1
	y = 0
	r = x >> y
	if r != 1 {
		t.Errorf("1 %s 0 = %d, want 1", ">>", r)
	}
	y = 1
	r = x >> y
	if r != 0 {
		t.Errorf("1 %s 1 = %d, want 0", ">>", r)
	}
	y = 255
	r = x >> y
	if r != 0 {
		t.Errorf("1 %s 255 = %d, want 0", ">>", r)
	}
	x = 4294967296
	y = 0
	r = x >> y
	if r != 4294967296 {
		t.Errorf("4294967296 %s 0 = %d, want 4294967296", ">>", r)
	}
	y = 1
	r = x >> y
	if r != 2147483648 {
		t.Errorf("4294967296 %s 1 = %d, want 2147483648", ">>", r)
	}
	y = 255
	r = x >> y
	if r != 0 {
		t.Errorf("4294967296 %s 255 = %d, want 0", ">>", r)
	}
	x = 18446744073709551615
	y = 0
	r = x >> y
	if r != 18446744073709551615 {
		t.Errorf("18446744073709551615 %s 0 = %d, want 18446744073709551615", ">>", r)
	}
	y = 1
	r = x >> y
	if r != 9223372036854775807 {
		t.Errorf("18446744073709551615 %s 1 = %d, want 9223372036854775807", ">>", r)
	}
	y = 255
	r = x >> y
	if r != 0 {
		t.Errorf("18446744073709551615 %s 255 = %d, want 0", ">>", r)
	}
}
func TestConstFoldint64uint64lsh(t *testing.T) {
	var x, r int64
	var y uint64
	x = -9223372036854775808
	y = 0
	r = x << y
	if r != -9223372036854775808 {
		t.Errorf("-9223372036854775808 %s 0 = %d, want -9223372036854775808", "<<", r)
	}
	y = 1
	r = x << y
	if r != 0 {
		t.Errorf("-9223372036854775808 %s 1 = %d, want 0", "<<", r)
	}
	y = 4294967296
	r = x << y
	if r != 0 {
		t.Errorf("-9223372036854775808 %s 4294967296 = %d, want 0", "<<", r)
	}
	y = 18446744073709551615
	r = x << y
	if r != 0 {
		t.Errorf("-9223372036854775808 %s 18446744073709551615 = %d, want 0", "<<", r)
	}
	x = -9223372036854775807
	y = 0
	r = x << y
	if r != -9223372036854775807 {
		t.Errorf("-9223372036854775807 %s 0 = %d, want -9223372036854775807", "<<", r)
	}
	y = 1
	r = x << y
	if r != 2 {
		t.Errorf("-9223372036854775807 %s 1 = %d, want 2", "<<", r)
	}
	y = 4294967296
	r = x << y
	if r != 0 {
		t.Errorf("-9223372036854775807 %s 4294967296 = %d, want 0", "<<", r)
	}
	y = 18446744073709551615
	r = x << y
	if r != 0 {
		t.Errorf("-9223372036854775807 %s 18446744073709551615 = %d, want 0", "<<", r)
	}
	x = -4294967296
	y = 0
	r = x << y
	if r != -4294967296 {
		t.Errorf("-4294967296 %s 0 = %d, want -4294967296", "<<", r)
	}
	y = 1
	r = x << y
	if r != -8589934592 {
		t.Errorf("-4294967296 %s 1 = %d, want -8589934592", "<<", r)
	}
	y = 4294967296
	r = x << y
	if r != 0 {
		t.Errorf("-4294967296 %s 4294967296 = %d, want 0", "<<", r)
	}
	y = 18446744073709551615
	r = x << y
	if r != 0 {
		t.Errorf("-4294967296 %s 18446744073709551615 = %d, want 0", "<<", r)
	}
	x = -1
	y = 0
	r = x << y
	if r != -1 {
		t.Errorf("-1 %s 0 = %d, want -1", "<<", r)
	}
	y = 1
	r = x << y
	if r != -2 {
		t.Errorf("-1 %s 1 = %d, want -2", "<<", r)
	}
	y = 4294967296
	r = x << y
	if r != 0 {
		t.Errorf("-1 %s 4294967296 = %d, want 0", "<<", r)
	}
	y = 18446744073709551615
	r = x << y
	if r != 0 {
		t.Errorf("-1 %s 18446744073709551615 = %d, want 0", "<<", r)
	}
	x = 0
	y = 0
	r = x << y
	if r != 0 {
		t.Errorf("0 %s 0 = %d, want 0", "<<", r)
	}
	y = 1
	r = x << y
	if r != 0 {
		t.Errorf("0 %s 1 = %d, want 0", "<<", r)
	}
	y = 4294967296
	r = x << y
	if r != 0 {
		t.Errorf("0 %s 4294967296 = %d, want 0", "<<", r)
	}
	y = 18446744073709551615
	r = x << y
	if r != 0 {
		t.Errorf("0 %s 18446744073709551615 = %d, want 0", "<<", r)
	}
	x = 1
	y = 0
	r = x << y
	if r != 1 {
		t.Errorf("1 %s 0 = %d, want 1", "<<", r)
	}
	y = 1
	r = x << y
	if r != 2 {
		t.Errorf("1 %s 1 = %d, want 2", "<<", r)
	}
	y = 4294967296
	r = x << y
	if r != 0 {
		t.Errorf("1 %s 4294967296 = %d, want 0", "<<", r)
	}
	y = 18446744073709551615
	r = x << y
	if r != 0 {
		t.Errorf("1 %s 18446744073709551615 = %d, want 0", "<<", r)
	}
	x = 4294967296
	y = 0
	r = x << y
	if r != 4294967296 {
		t.Errorf("4294967296 %s 0 = %d, want 4294967296", "<<", r)
	}
	y = 1
	r = x << y
	if r != 8589934592 {
		t.Errorf("4294967296 %s 1 = %d, want 8589934592", "<<", r)
	}
	y = 4294967296
	r = x << y
	if r != 0 {
		t.Errorf("4294967296 %s 4294967296 = %d, want 0", "<<", r)
	}
	y = 18446744073709551615
	r = x << y
	if r != 0 {
		t.Errorf("4294967296 %s 18446744073709551615 = %d, want 0", "<<", r)
	}
	x = 9223372036854775806
	y = 0
	r = x << y
	if r != 9223372036854775806 {
		t.Errorf("9223372036854775806 %s 0 = %d, want 9223372036854775806", "<<", r)
	}
	y = 1
	r = x << y
	if r != -4 {
		t.Errorf("9223372036854775806 %s 1 = %d, want -4", "<<", r)
	}
	y = 4294967296
	r = x << y
	if r != 0 {
		t.Errorf("9223372036854775806 %s 4294967296 = %d, want 0", "<<", r)
	}
	y = 18446744073709551615
	r = x << y
	if r != 0 {
		t.Errorf("9223372036854775806 %s 18446744073709551615 = %d, want 0", "<<", r)
	}
	x = 9223372036854775807
	y = 0
	r = x << y
	if r != 9223372036854775807 {
		t.Errorf("9223372036854775807 %s 0 = %d, want 9223372036854775807", "<<", r)
	}
	y = 1
	r = x << y
	if r != -2 {
		t.Errorf("9223372036854775807 %s 1 = %d, want -2", "<<", r)
	}
	y = 4294967296
	r = x << y
	if r != 0 {
		t.Errorf("9223372036854775807 %s 4294967296 = %d, want 0", "<<", r)
	}
	y = 18446744073709551615
	r = x << y
	if r != 0 {
		t.Errorf("9223372036854775807 %s 18446744073709551615 = %d, want 0", "<<", r)
	}
}
func TestConstFoldint64uint64rsh(t *testing.T) {
	var x, r int64
	var y uint64
	x = -9223372036854775808
	y = 0
	r = x >> y
	if r != -9223372036854775808 {
		t.Errorf("-9223372036854775808 %s 0 = %d, want -9223372036854775808", ">>", r)
	}
	y = 1
	r = x >> y
	if r != -4611686018427387904 {
		t.Errorf("-9223372036854775808 %s 1 = %d, want -4611686018427387904", ">>", r)
	}
	y = 4294967296
	r = x >> y
	if r != -1 {
		t.Errorf("-9223372036854775808 %s 4294967296 = %d, want -1", ">>", r)
	}
	y = 18446744073709551615
	r = x >> y
	if r != -1 {
		t.Errorf("-9223372036854775808 %s 18446744073709551615 = %d, want -1", ">>", r)
	}
	x = -9223372036854775807
	y = 0
	r = x >> y
	if r != -9223372036854775807 {
		t.Errorf("-9223372036854775807 %s 0 = %d, want -9223372036854775807", ">>", r)
	}
	y = 1
	r = x >> y
	if r != -4611686018427387904 {
		t.Errorf("-9223372036854775807 %s 1 = %d, want -4611686018427387904", ">>", r)
	}
	y = 4294967296
	r = x >> y
	if r != -1 {
		t.Errorf("-9223372036854775807 %s 4294967296 = %d, want -1", ">>", r)
	}
	y = 18446744073709551615
	r = x >> y
	if r != -1 {
		t.Errorf("-9223372036854775807 %s 18446744073709551615 = %d, want -1", ">>", r)
	}
	x = -4294967296
	y = 0
	r = x >> y
	if r != -4294967296 {
		t.Errorf("-4294967296 %s 0 = %d, want -4294967296", ">>", r)
	}
	y = 1
	r = x >> y
	if r != -2147483648 {
		t.Errorf("-4294967296 %s 1 = %d, want -2147483648", ">>", r)
	}
	y = 4294967296
	r = x >> y
	if r != -1 {
		t.Errorf("-4294967296 %s 4294967296 = %d, want -1", ">>", r)
	}
	y = 18446744073709551615
	r = x >> y
	if r != -1 {
		t.Errorf("-4294967296 %s 18446744073709551615 = %d, want -1", ">>", r)
	}
	x = -1
	y = 0
	r = x >> y
	if r != -1 {
		t.Errorf("-1 %s 0 = %d, want -1", ">>", r)
	}
	y = 1
	r = x >> y
	if r != -1 {
		t.Errorf("-1 %s 1 = %d, want -1", ">>", r)
	}
	y = 4294967296
	r = x >> y
	if r != -1 {
		t.Errorf("-1 %s 4294967296 = %d, want -1", ">>", r)
	}
	y = 18446744073709551615
	r = x >> y
	if r != -1 {
		t.Errorf("-1 %s 18446744073709551615 = %d, want -1", ">>", r)
	}
	x = 0
	y = 0
	r = x >> y
	if r != 0 {
		t.Errorf("0 %s 0 = %d, want 0", ">>", r)
	}
	y = 1
	r = x >> y
	if r != 0 {
		t.Errorf("0 %s 1 = %d, want 0", ">>", r)
	}
	y = 4294967296
	r = x >> y
	if r != 0 {
		t.Errorf("0 %s 4294967296 = %d, want 0", ">>", r)
	}
	y = 18446744073709551615
	r = x >> y
	if r != 0 {
		t.Errorf("0 %s 18446744073709551615 = %d, want 0", ">>", r)
	}
	x = 1
	y = 0
	r = x >> y
	if r != 1 {
		t.Errorf("1 %s 0 = %d, want 1", ">>", r)
	}
	y = 1
	r = x >> y
	if r != 0 {
		t.Errorf("1 %s 1 = %d, want 0", ">>", r)
	}
	y = 4294967296
	r = x >> y
	if r != 0 {
		t.Errorf("1 %s 4294967296 = %d, want 0", ">>", r)
	}
	y = 18446744073709551615
	r = x >> y
	if r != 0 {
		t.Errorf("1 %s 18446744073709551615 = %d, want 0", ">>", r)
	}
	x = 4294967296
	y = 0
	r = x >> y
	if r != 4294967296 {
		t.Errorf("4294967296 %s 0 = %d, want 4294967296", ">>", r)
	}
	y = 1
	r = x >> y
	if r != 2147483648 {
		t.Errorf("4294967296 %s 1 = %d, want 2147483648", ">>", r)
	}
	y = 4294967296
	r = x >> y
	if r != 0 {
		t.Errorf("4294967296 %s 4294967296 = %d, want 0", ">>", r)
	}
	y = 18446744073709551615
	r = x >> y
	if r != 0 {
		t.Errorf("4294967296 %s 18446744073709551615 = %d, want 0", ">>", r)
	}
	x = 9223372036854775806
	y = 0
	r = x >> y
	if r != 9223372036854775806 {
		t.Errorf("9223372036854775806 %s 0 = %d, want 9223372036854775806", ">>", r)
	}
	y = 1
	r = x >> y
	if r != 4611686018427387903 {
		t.Errorf("9223372036854775806 %s 1 = %d, want 4611686018427387903", ">>", r)
	}
	y = 4294967296
	r = x >> y
	if r != 0 {
		t.Errorf("9223372036854775806 %s 4294967296 = %d, want 0", ">>", r)
	}
	y = 18446744073709551615
	r = x >> y
	if r != 0 {
		t.Errorf("9223372036854775806 %s 18446744073709551615 = %d, want 0", ">>", r)
	}
	x = 9223372036854775807
	y = 0
	r = x >> y
	if r != 9223372036854775807 {
		t.Errorf("9223372036854775807 %s 0 = %d, want 9223372036854775807", ">>", r)
	}
	y = 1
	r = x >> y
	if r != 4611686018427387903 {
		t.Errorf("9223372036854775807 %s 1 = %d, want 4611686018427387903", ">>", r)
	}
	y = 4294967296
	r = x >> y
	if r != 0 {
		t.Errorf("9223372036854775807 %s 4294967296 = %d, want 0", ">>", r)
	}
	y = 18446744073709551615
	r = x >> y
	if r != 0 {
		t.Errorf("9223372036854775807 %s 18446744073709551615 = %d, want 0", ">>", r)
	}
}
func TestConstFoldint64uint32lsh(t *testing.T) {
	var x, r int64
	var y uint32
	x = -9223372036854775808
	y = 0
	r = x << y
	if r != -9223372036854775808 {
		t.Errorf("-9223372036854775808 %s 0 = %d, want -9223372036854775808", "<<", r)
	}
	y = 1
	r = x << y
	if r != 0 {
		t.Errorf("-9223372036854775808 %s 1 = %d, want 0", "<<", r)
	}
	y = 4294967295
	r = x << y
	if r != 0 {
		t.Errorf("-9223372036854775808 %s 4294967295 = %d, want 0", "<<", r)
	}
	x = -9223372036854775807
	y = 0
	r = x << y
	if r != -9223372036854775807 {
		t.Errorf("-9223372036854775807 %s 0 = %d, want -9223372036854775807", "<<", r)
	}
	y = 1
	r = x << y
	if r != 2 {
		t.Errorf("-9223372036854775807 %s 1 = %d, want 2", "<<", r)
	}
	y = 4294967295
	r = x << y
	if r != 0 {
		t.Errorf("-9223372036854775807 %s 4294967295 = %d, want 0", "<<", r)
	}
	x = -4294967296
	y = 0
	r = x << y
	if r != -4294967296 {
		t.Errorf("-4294967296 %s 0 = %d, want -4294967296", "<<", r)
	}
	y = 1
	r = x << y
	if r != -8589934592 {
		t.Errorf("-4294967296 %s 1 = %d, want -8589934592", "<<", r)
	}
	y = 4294967295
	r = x << y
	if r != 0 {
		t.Errorf("-4294967296 %s 4294967295 = %d, want 0", "<<", r)
	}
	x = -1
	y = 0
	r = x << y
	if r != -1 {
		t.Errorf("-1 %s 0 = %d, want -1", "<<", r)
	}
	y = 1
	r = x << y
	if r != -2 {
		t.Errorf("-1 %s 1 = %d, want -2", "<<", r)
	}
	y = 4294967295
	r = x << y
	if r != 0 {
		t.Errorf("-1 %s 4294967295 = %d, want 0", "<<", r)
	}
	x = 0
	y = 0
	r = x << y
	if r != 0 {
		t.Errorf("0 %s 0 = %d, want 0", "<<", r)
	}
	y = 1
	r = x << y
	if r != 0 {
		t.Errorf("0 %s 1 = %d, want 0", "<<", r)
	}
	y = 4294967295
	r = x << y
	if r != 0 {
		t.Errorf("0 %s 4294967295 = %d, want 0", "<<", r)
	}
	x = 1
	y = 0
	r = x << y
	if r != 1 {
		t.Errorf("1 %s 0 = %d, want 1", "<<", r)
	}
	y = 1
	r = x << y
	if r != 2 {
		t.Errorf("1 %s 1 = %d, want 2", "<<", r)
	}
	y = 4294967295
	r = x << y
	if r != 0 {
		t.Errorf("1 %s 4294967295 = %d, want 0", "<<", r)
	}
	x = 4294967296
	y = 0
	r = x << y
	if r != 4294967296 {
		t.Errorf("4294967296 %s 0 = %d, want 4294967296", "<<", r)
	}
	y = 1
	r = x << y
	if r != 8589934592 {
		t.Errorf("4294967296 %s 1 = %d, want 8589934592", "<<", r)
	}
	y = 4294967295
	r = x << y
	if r != 0 {
		t.Errorf("4294967296 %s 4294967295 = %d, want 0", "<<", r)
	}
	x = 9223372036854775806
	y = 0
	r = x << y
	if r != 9223372036854775806 {
		t.Errorf("9223372036854775806 %s 0 = %d, want 9223372036854775806", "<<", r)
	}
	y = 1
	r = x << y
	if r != -4 {
		t.Errorf("9223372036854775806 %s 1 = %d, want -4", "<<", r)
	}
	y = 4294967295
	r = x << y
	if r != 0 {
		t.Errorf("9223372036854775806 %s 4294967295 = %d, want 0", "<<", r)
	}
	x = 9223372036854775807
	y = 0
	r = x << y
	if r != 9223372036854775807 {
		t.Errorf("9223372036854775807 %s 0 = %d, want 9223372036854775807", "<<", r)
	}
	y = 1
	r = x << y
	if r != -2 {
		t.Errorf("9223372036854775807 %s 1 = %d, want -2", "<<", r)
	}
	y = 4294967295
	r = x << y
	if r != 0 {
		t.Errorf("9223372036854775807 %s 4294967295 = %d, want 0", "<<", r)
	}
}
func TestConstFoldint64uint32rsh(t *testing.T) {
	var x, r int64
	var y uint32
	x = -9223372036854775808
	y = 0
	r = x >> y
	if r != -9223372036854775808 {
		t.Errorf("-9223372036854775808 %s 0 = %d, want -9223372036854775808", ">>", r)
	}
	y = 1
	r = x >> y
	if r != -4611686018427387904 {
		t.Errorf("-9223372036854775808 %s 1 = %d, want -4611686018427387904", ">>", r)
	}
	y = 4294967295
	r = x >> y
	if r != -1 {
		t.Errorf("-9223372036854775808 %s 4294967295 = %d, want -1", ">>", r)
	}
	x = -9223372036854775807
	y = 0
	r = x >> y
	if r != -9223372036854775807 {
		t.Errorf("-9223372036854775807 %s 0 = %d, want -9223372036854775807", ">>", r)
	}
	y = 1
	r = x >> y
	if r != -4611686018427387904 {
		t.Errorf("-9223372036854775807 %s 1 = %d, want -4611686018427387904", ">>", r)
	}
	y = 4294967295
	r = x >> y
	if r != -1 {
		t.Errorf("-9223372036854775807 %s 4294967295 = %d, want -1", ">>", r)
	}
	x = -4294967296
	y = 0
	r = x >> y
	if r != -4294967296 {
		t.Errorf("-4294967296 %s 0 = %d, want -4294967296", ">>", r)
	}
	y = 1
	r = x >> y
	if r != -2147483648 {
		t.Errorf("-4294967296 %s 1 = %d, want -2147483648", ">>", r)
	}
	y = 4294967295
	r = x >> y
	if r != -1 {
		t.Errorf("-4294967296 %s 4294967295 = %d, want -1", ">>", r)
	}
	x = -1
	y = 0
	r = x >> y
	if r != -1 {
		t.Errorf("-1 %s 0 = %d, want -1", ">>", r)
	}
	y = 1
	r = x >> y
	if r != -1 {
		t.Errorf("-1 %s 1 = %d, want -1", ">>", r)
	}
	y = 4294967295
	r = x >> y
	if r != -1 {
		t.Errorf("-1 %s 4294967295 = %d, want -1", ">>", r)
	}
	x = 0
	y = 0
	r = x >> y
	if r != 0 {
		t.Errorf("0 %s 0 = %d, want 0", ">>", r)
	}
	y = 1
	r = x >> y
	if r != 0 {
		t.Errorf("0 %s 1 = %d, want 0", ">>", r)
	}
	y = 4294967295
	r = x >> y
	if r != 0 {
		t.Errorf("0 %s 4294967295 = %d, want 0", ">>", r)
	}
	x = 1
	y = 0
	r = x >> y
	if r != 1 {
		t.Errorf("1 %s 0 = %d, want 1", ">>", r)
	}
	y = 1
	r = x >> y
	if r != 0 {
		t.Errorf("1 %s 1 = %d, want 0", ">>", r)
	}
	y = 4294967295
	r = x >> y
	if r != 0 {
		t.Errorf("1 %s 4294967295 = %d, want 0", ">>", r)
	}
	x = 4294967296
	y = 0
	r = x >> y
	if r != 4294967296 {
		t.Errorf("4294967296 %s 0 = %d, want 4294967296", ">>", r)
	}
	y = 1
	r = x >> y
	if r != 2147483648 {
		t.Errorf("4294967296 %s 1 = %d, want 2147483648", ">>", r)
	}
	y = 4294967295
	r = x >> y
	if r != 0 {
		t.Errorf("4294967296 %s 4294967295 = %d, want 0", ">>", r)
	}
	x = 9223372036854775806
	y = 0
	r = x >> y
	if r != 9223372036854775806 {
		t.Errorf("9223372036854775806 %s 0 = %d, want 9223372036854775806", ">>", r)
	}
	y = 1
	r = x >> y
	if r != 4611686018427387903 {
		t.Errorf("9223372036854775806 %s 1 = %d, want 4611686018427387903", ">>", r)
	}
	y = 4294967295
	r = x >> y
	if r != 0 {
		t.Errorf("9223372036854775806 %s 4294967295 = %d, want 0", ">>", r)
	}
	x = 9223372036854775807
	y = 0
	r = x >> y
	if r != 9223372036854775807 {
		t.Errorf("9223372036854775807 %s 0 = %d, want 9223372036854775807", ">>", r)
	}
	y = 1
	r = x >> y
	if r != 4611686018427387903 {
		t.Errorf("9223372036854775807 %s 1 = %d, want 4611686018427387903", ">>", r)
	}
	y = 4294967295
	r = x >> y
	if r != 0 {
		t.Errorf("9223372036854775807 %s 4294967295 = %d, want 0", ">>", r)
	}
}
func TestConstFoldint64uint16lsh(t *testing.T) {
	var x, r int64
	var y uint16
	x = -9223372036854775808
	y = 0
	r = x << y
	if r != -9223372036854775808 {
		t.Errorf("-9223372036854775808 %s 0 = %d, want -9223372036854775808", "<<", r)
	}
	y = 1
	r = x << y
	if r != 0 {
		t.Errorf("-9223372036854775808 %s 1 = %d, want 0", "<<", r)
	}
	y = 65535
	r = x << y
	if r != 0 {
		t.Errorf("-9223372036854775808 %s 65535 = %d, want 0", "<<", r)
	}
	x = -9223372036854775807
	y = 0
	r = x << y
	if r != -9223372036854775807 {
		t.Errorf("-9223372036854775807 %s 0 = %d, want -9223372036854775807", "<<", r)
	}
	y = 1
	r = x << y
	if r != 2 {
		t.Errorf("-9223372036854775807 %s 1 = %d, want 2", "<<", r)
	}
	y = 65535
	r = x << y
	if r != 0 {
		t.Errorf("-9223372036854775807 %s 65535 = %d, want 0", "<<", r)
	}
	x = -4294967296
	y = 0
	r = x << y
	if r != -4294967296 {
		t.Errorf("-4294967296 %s 0 = %d, want -4294967296", "<<", r)
	}
	y = 1
	r = x << y
	if r != -8589934592 {
		t.Errorf("-4294967296 %s 1 = %d, want -8589934592", "<<", r)
	}
	y = 65535
	r = x << y
	if r != 0 {
		t.Errorf("-4294967296 %s 65535 = %d, want 0", "<<", r)
	}
	x = -1
	y = 0
	r = x << y
	if r != -1 {
		t.Errorf("-1 %s 0 = %d, want -1", "<<", r)
	}
	y = 1
	r = x << y
	if r != -2 {
		t.Errorf("-1 %s 1 = %d, want -2", "<<", r)
	}
	y = 65535
	r = x << y
	if r != 0 {
		t.Errorf("-1 %s 65535 = %d, want 0", "<<", r)
	}
	x = 0
	y = 0
	r = x << y
	if r != 0 {
		t.Errorf("0 %s 0 = %d, want 0", "<<", r)
	}
	y = 1
	r = x << y
	if r != 0 {
		t.Errorf("0 %s 1 = %d, want 0", "<<", r)
	}
	y = 65535
	r = x << y
	if r != 0 {
		t.Errorf("0 %s 65535 = %d, want 0", "<<", r)
	}
	x = 1
	y = 0
	r = x << y
	if r != 1 {
		t.Errorf("1 %s 0 = %d, want 1", "<<", r)
	}
	y = 1
	r = x << y
	if r != 2 {
		t.Errorf("1 %s 1 = %d, want 2", "<<", r)
	}
	y = 65535
	r = x << y
	if r != 0 {
		t.Errorf("1 %s 65535 = %d, want 0", "<<", r)
	}
	x = 4294967296
	y = 0
	r = x << y
	if r != 4294967296 {
		t.Errorf("4294967296 %s 0 = %d, want 4294967296", "<<", r)
	}
	y = 1
	r = x << y
	if r != 8589934592 {
		t.Errorf("4294967296 %s 1 = %d, want 8589934592", "<<", r)
	}
	y = 65535
	r = x << y
	if r != 0 {
		t.Errorf("4294967296 %s 65535 = %d, want 0", "<<", r)
	}
	x = 9223372036854775806
	y = 0
	r = x << y
	if r != 9223372036854775806 {
		t.Errorf("9223372036854775806 %s 0 = %d, want 9223372036854775806", "<<", r)
	}
	y = 1
	r = x << y
	if r != -4 {
		t.Errorf("9223372036854775806 %s 1 = %d, want -4", "<<", r)
	}
	y = 65535
	r = x << y
	if r != 0 {
		t.Errorf("9223372036854775806 %s 65535 = %d, want 0", "<<", r)
	}
	x = 9223372036854775807
	y = 0
	r = x << y
	if r != 9223372036854775807 {
		t.Errorf("9223372036854775807 %s 0 = %d, want 9223372036854775807", "<<", r)
	}
	y = 1
	r = x << y
	if r != -2 {
		t.Errorf("9223372036854775807 %s 1 = %d, want -2", "<<", r)
	}
	y = 65535
	r = x << y
	if r != 0 {
		t.Errorf("9223372036854775807 %s 65535 = %d, want 0", "<<", r)
	}
}
func TestConstFoldint64uint16rsh(t *testing.T) {
	var x, r int64
	var y uint16
	x = -9223372036854775808
	y = 0
	r = x >> y
	if r != -9223372036854775808 {
		t.Errorf("-9223372036854775808 %s 0 = %d, want -9223372036854775808", ">>", r)
	}
	y = 1
	r = x >> y
	if r != -4611686018427387904 {
		t.Errorf("-9223372036854775808 %s 1 = %d, want -4611686018427387904", ">>", r)
	}
	y = 65535
	r = x >> y
	if r != -1 {
		t.Errorf("-9223372036854775808 %s 65535 = %d, want -1", ">>", r)
	}
	x = -9223372036854775807
	y = 0
	r = x >> y
	if r != -9223372036854775807 {
		t.Errorf("-9223372036854775807 %s 0 = %d, want -9223372036854775807", ">>", r)
	}
	y = 1
	r = x >> y
	if r != -4611686018427387904 {
		t.Errorf("-9223372036854775807 %s 1 = %d, want -4611686018427387904", ">>", r)
	}
	y = 65535
	r = x >> y
	if r != -1 {
		t.Errorf("-9223372036854775807 %s 65535 = %d, want -1", ">>", r)
	}
	x = -4294967296
	y = 0
	r = x >> y
	if r != -4294967296 {
		t.Errorf("-4294967296 %s 0 = %d, want -4294967296", ">>", r)
	}
	y = 1
	r = x >> y
	if r != -2147483648 {
		t.Errorf("-4294967296 %s 1 = %d, want -2147483648", ">>", r)
	}
	y = 65535
	r = x >> y
	if r != -1 {
		t.Errorf("-4294967296 %s 65535 = %d, want -1", ">>", r)
	}
	x = -1
	y = 0
	r = x >> y
	if r != -1 {
		t.Errorf("-1 %s 0 = %d, want -1", ">>", r)
	}
	y = 1
	r = x >> y
	if r != -1 {
		t.Errorf("-1 %s 1 = %d, want -1", ">>", r)
	}
	y = 65535
	r = x >> y
	if r != -1 {
		t.Errorf("-1 %s 65535 = %d, want -1", ">>", r)
	}
	x = 0
	y = 0
	r = x >> y
	if r != 0 {
		t.Errorf("0 %s 0 = %d, want 0", ">>", r)
	}
	y = 1
	r = x >> y
	if r != 0 {
		t.Errorf("0 %s 1 = %d, want 0", ">>", r)
	}
	y = 65535
	r = x >> y
	if r != 0 {
		t.Errorf("0 %s 65535 = %d, want 0", ">>", r)
	}
	x = 1
	y = 0
	r = x >> y
	if r != 1 {
		t.Errorf("1 %s 0 = %d, want 1", ">>", r)
	}
	y = 1
	r = x >> y
	if r != 0 {
		t.Errorf("1 %s 1 = %d, want 0", ">>", r)
	}
	y = 65535
	r = x >> y
	if r != 0 {
		t.Errorf("1 %s 65535 = %d, want 0", ">>", r)
	}
	x = 4294967296
	y = 0
	r = x >> y
	if r != 4294967296 {
		t.Errorf("4294967296 %s 0 = %d, want 4294967296", ">>", r)
	}
	y = 1
	r = x >> y
	if r != 2147483648 {
		t.Errorf("4294967296 %s 1 = %d, want 2147483648", ">>", r)
	}
	y = 65535
	r = x >> y
	if r != 0 {
		t.Errorf("4294967296 %s 65535 = %d, want 0", ">>", r)
	}
	x = 9223372036854775806
	y = 0
	r = x >> y
	if r != 9223372036854775806 {
		t.Errorf("9223372036854775806 %s 0 = %d, want 9223372036854775806", ">>", r)
	}
	y = 1
	r = x >> y
	if r != 4611686018427387903 {
		t.Errorf("9223372036854775806 %s 1 = %d, want 4611686018427387903", ">>", r)
	}
	y = 65535
	r = x >> y
	if r != 0 {
		t.Errorf("9223372036854775806 %s 65535 = %d, want 0", ">>", r)
	}
	x = 9223372036854775807
	y = 0
	r = x >> y
	if r != 9223372036854775807 {
		t.Errorf("9223372036854775807 %s 0 = %d, want 9223372036854775807", ">>", r)
	}
	y = 1
	r = x >> y
	if r != 4611686018427387903 {
		t.Errorf("9223372036854775807 %s 1 = %d, want 4611686018427387903", ">>", r)
	}
	y = 65535
	r = x >> y
	if r != 0 {
		t.Errorf("9223372036854775807 %s 65535 = %d, want 0", ">>", r)
	}
}
func TestConstFoldint64uint8lsh(t *testing.T) {
	var x, r int64
	var y uint8
	x = -9223372036854775808
	y = 0
	r = x << y
	if r != -9223372036854775808 {
		t.Errorf("-9223372036854775808 %s 0 = %d, want -9223372036854775808", "<<", r)
	}
	y = 1
	r = x << y
	if r != 0 {
		t.Errorf("-9223372036854775808 %s 1 = %d, want 0", "<<", r)
	}
	y = 255
	r = x << y
	if r != 0 {
		t.Errorf("-9223372036854775808 %s 255 = %d, want 0", "<<", r)
	}
	x = -9223372036854775807
	y = 0
	r = x << y
	if r != -9223372036854775807 {
		t.Errorf("-9223372036854775807 %s 0 = %d, want -9223372036854775807", "<<", r)
	}
	y = 1
	r = x << y
	if r != 2 {
		t.Errorf("-9223372036854775807 %s 1 = %d, want 2", "<<", r)
	}
	y = 255
	r = x << y
	if r != 0 {
		t.Errorf("-9223372036854775807 %s 255 = %d, want 0", "<<", r)
	}
	x = -4294967296
	y = 0
	r = x << y
	if r != -4294967296 {
		t.Errorf("-4294967296 %s 0 = %d, want -4294967296", "<<", r)
	}
	y = 1
	r = x << y
	if r != -8589934592 {
		t.Errorf("-4294967296 %s 1 = %d, want -8589934592", "<<", r)
	}
	y = 255
	r = x << y
	if r != 0 {
		t.Errorf("-4294967296 %s 255 = %d, want 0", "<<", r)
	}
	x = -1
	y = 0
	r = x << y
	if r != -1 {
		t.Errorf("-1 %s 0 = %d, want -1", "<<", r)
	}
	y = 1
	r = x << y
	if r != -2 {
		t.Errorf("-1 %s 1 = %d, want -2", "<<", r)
	}
	y = 255
	r = x << y
	if r != 0 {
		t.Errorf("-1 %s 255 = %d, want 0", "<<", r)
	}
	x = 0
	y = 0
	r = x << y
	if r != 0 {
		t.Errorf("0 %s 0 = %d, want 0", "<<", r)
	}
	y = 1
	r = x << y
	if r != 0 {
		t.Errorf("0 %s 1 = %d, want 0", "<<", r)
	}
	y = 255
	r = x << y
	if r != 0 {
		t.Errorf("0 %s 255 = %d, want 0", "<<", r)
	}
	x = 1
	y = 0
	r = x << y
	if r != 1 {
		t.Errorf("1 %s 0 = %d, want 1", "<<", r)
	}
	y = 1
	r = x << y
	if r != 2 {
		t.Errorf("1 %s 1 = %d, want 2", "<<", r)
	}
	y = 255
	r = x << y
	if r != 0 {
		t.Errorf("1 %s 255 = %d, want 0", "<<", r)
	}
	x = 4294967296
	y = 0
	r = x << y
	if r != 4294967296 {
		t.Errorf("4294967296 %s 0 = %d, want 4294967296", "<<", r)
	}
	y = 1
	r = x << y
	if r != 8589934592 {
		t.Errorf("4294967296 %s 1 = %d, want 8589934592", "<<", r)
	}
	y = 255
	r = x << y
	if r != 0 {
		t.Errorf("4294967296 %s 255 = %d, want 0", "<<", r)
	}
	x = 9223372036854775806
	y = 0
	r = x << y
	if r != 9223372036854775806 {
		t.Errorf("9223372036854775806 %s 0 = %d, want 9223372036854775806", "<<", r)
	}
	y = 1
	r = x << y
	if r != -4 {
		t.Errorf("9223372036854775806 %s 1 = %d, want -4", "<<", r)
	}
	y = 255
	r = x << y
	if r != 0 {
		t.Errorf("9223372036854775806 %s 255 = %d, want 0", "<<", r)
	}
	x = 9223372036854775807
	y = 0
	r = x << y
	if r != 9223372036854775807 {
		t.Errorf("9223372036854775807 %s 0 = %d, want 9223372036854775807", "<<", r)
	}
	y = 1
	r = x << y
	if r != -2 {
		t.Errorf("9223372036854775807 %s 1 = %d, want -2", "<<", r)
	}
	y = 255
	r = x << y
	if r != 0 {
		t.Errorf("9223372036854775807 %s 255 = %d, want 0", "<<", r)
	}
}
func TestConstFoldint64uint8rsh(t *testing.T) {
	var x, r int64
	var y uint8
	x = -9223372036854775808
	y = 0
	r = x >> y
	if r != -9223372036854775808 {
		t.Errorf("-9223372036854775808 %s 0 = %d, want -9223372036854775808", ">>", r)
	}
	y = 1
	r = x >> y
	if r != -4611686018427387904 {
		t.Errorf("-9223372036854775808 %s 1 = %d, want -4611686018427387904", ">>", r)
	}
	y = 255
	r = x >> y
	if r != -1 {
		t.Errorf("-9223372036854775808 %s 255 = %d, want -1", ">>", r)
	}
	x = -9223372036854775807
	y = 0
	r = x >> y
	if r != -9223372036854775807 {
		t.Errorf("-9223372036854775807 %s 0 = %d, want -9223372036854775807", ">>", r)
	}
	y = 1
	r = x >> y
	if r != -4611686018427387904 {
		t.Errorf("-9223372036854775807 %s 1 = %d, want -4611686018427387904", ">>", r)
	}
	y = 255
	r = x >> y
	if r != -1 {
		t.Errorf("-9223372036854775807 %s 255 = %d, want -1", ">>", r)
	}
	x = -4294967296
	y = 0
	r = x >> y
	if r != -4294967296 {
		t.Errorf("-4294967296 %s 0 = %d, want -4294967296", ">>", r)
	}
	y = 1
	r = x >> y
	if r != -2147483648 {
		t.Errorf("-4294967296 %s 1 = %d, want -2147483648", ">>", r)
	}
	y = 255
	r = x >> y
	if r != -1 {
		t.Errorf("-4294967296 %s 255 = %d, want -1", ">>", r)
	}
	x = -1
	y = 0
	r = x >> y
	if r != -1 {
		t.Errorf("-1 %s 0 = %d, want -1", ">>", r)
	}
	y = 1
	r = x >> y
	if r != -1 {
		t.Errorf("-1 %s 1 = %d, want -1", ">>", r)
	}
	y = 255
	r = x >> y
	if r != -1 {
		t.Errorf("-1 %s 255 = %d, want -1", ">>", r)
	}
	x = 0
	y = 0
	r = x >> y
	if r != 0 {
		t.Errorf("0 %s 0 = %d, want 0", ">>", r)
	}
	y = 1
	r = x >> y
	if r != 0 {
		t.Errorf("0 %s 1 = %d, want 0", ">>", r)
	}
	y = 255
	r = x >> y
	if r != 0 {
		t.Errorf("0 %s 255 = %d, want 0", ">>", r)
	}
	x = 1
	y = 0
	r = x >> y
	if r != 1 {
		t.Errorf("1 %s 0 = %d, want 1", ">>", r)
	}
	y = 1
	r = x >> y
	if r != 0 {
		t.Errorf("1 %s 1 = %d, want 0", ">>", r)
	}
	y = 255
	r = x >> y
	if r != 0 {
		t.Errorf("1 %s 255 = %d, want 0", ">>", r)
	}
	x = 4294967296
	y = 0
	r = x >> y
	if r != 4294967296 {
		t.Errorf("4294967296 %s 0 = %d, want 4294967296", ">>", r)
	}
	y = 1
	r = x >> y
	if r != 2147483648 {
		t.Errorf("4294967296 %s 1 = %d, want 2147483648", ">>", r)
	}
	y = 255
	r = x >> y
	if r != 0 {
		t.Errorf("4294967296 %s 255 = %d, want 0", ">>", r)
	}
	x = 9223372036854775806
	y = 0
	r = x >> y
	if r != 9223372036854775806 {
		t.Errorf("9223372036854775806 %s 0 = %d, want 9223372036854775806", ">>", r)
	}
	y = 1
	r = x >> y
	if r != 4611686018427387903 {
		t.Errorf("9223372036854775806 %s 1 = %d, want 4611686018427387903", ">>", r)
	}
	y = 255
	r = x >> y
	if r != 0 {
		t.Errorf("9223372036854775806 %s 255 = %d, want 0", ">>", r)
	}
	x = 9223372036854775807
	y = 0
	r = x >> y
	if r != 9223372036854775807 {
		t.Errorf("9223372036854775807 %s 0 = %d, want 9223372036854775807", ">>", r)
	}
	y = 1
	r = x >> y
	if r != 4611686018427387903 {
		t.Errorf("9223372036854775807 %s 1 = %d, want 4611686018427387903", ">>", r)
	}
	y = 255
	r = x >> y
	if r != 0 {
		t.Errorf("9223372036854775807 %s 255 = %d, want 0", ">>", r)
	}
}
func TestConstFolduint32uint64lsh(t *testing.T) {
	var x, r uint32
	var y uint64
	x = 0
	y = 0
	r = x << y
	if r != 0 {
		t.Errorf("0 %s 0 = %d, want 0", "<<", r)
	}
	y = 1
	r = x << y
	if r != 0 {
		t.Errorf("0 %s 1 = %d, want 0", "<<", r)
	}
	y = 4294967296
	r = x << y
	if r != 0 {
		t.Errorf("0 %s 4294967296 = %d, want 0", "<<", r)
	}
	y = 18446744073709551615
	r = x << y
	if r != 0 {
		t.Errorf("0 %s 18446744073709551615 = %d, want 0", "<<", r)
	}
	x = 1
	y = 0
	r = x << y
	if r != 1 {
		t.Errorf("1 %s 0 = %d, want 1", "<<", r)
	}
	y = 1
	r = x << y
	if r != 2 {
		t.Errorf("1 %s 1 = %d, want 2", "<<", r)
	}
	y = 4294967296
	r = x << y
	if r != 0 {
		t.Errorf("1 %s 4294967296 = %d, want 0", "<<", r)
	}
	y = 18446744073709551615
	r = x << y
	if r != 0 {
		t.Errorf("1 %s 18446744073709551615 = %d, want 0", "<<", r)
	}
	x = 4294967295
	y = 0
	r = x << y
	if r != 4294967295 {
		t.Errorf("4294967295 %s 0 = %d, want 4294967295", "<<", r)
	}
	y = 1
	r = x << y
	if r != 4294967294 {
		t.Errorf("4294967295 %s 1 = %d, want 4294967294", "<<", r)
	}
	y = 4294967296
	r = x << y
	if r != 0 {
		t.Errorf("4294967295 %s 4294967296 = %d, want 0", "<<", r)
	}
	y = 18446744073709551615
	r = x << y
	if r != 0 {
		t.Errorf("4294967295 %s 18446744073709551615 = %d, want 0", "<<", r)
	}
}
func TestConstFolduint32uint64rsh(t *testing.T) {
	var x, r uint32
	var y uint64
	x = 0
	y = 0
	r = x >> y
	if r != 0 {
		t.Errorf("0 %s 0 = %d, want 0", ">>", r)
	}
	y = 1
	r = x >> y
	if r != 0 {
		t.Errorf("0 %s 1 = %d, want 0", ">>", r)
	}
	y = 4294967296
	r = x >> y
	if r != 0 {
		t.Errorf("0 %s 4294967296 = %d, want 0", ">>", r)
	}
	y = 18446744073709551615
	r = x >> y
	if r != 0 {
		t.Errorf("0 %s 18446744073709551615 = %d, want 0", ">>", r)
	}
	x = 1
	y = 0
	r = x >> y
	if r != 1 {
		t.Errorf("1 %s 0 = %d, want 1", ">>", r)
	}
	y = 1
	r = x >> y
	if r != 0 {
		t.Errorf("1 %s 1 = %d, want 0", ">>", r)
	}
	y = 4294967296
	r = x >> y
	if r != 0 {
		t.Errorf("1 %s 4294967296 = %d, want 0", ">>", r)
	}
	y = 18446744073709551615
	r = x >> y
	if r != 0 {
		t.Errorf("1 %s 18446744073709551615 = %d, want 0", ">>", r)
	}
	x = 4294967295
	y = 0
	r = x >> y
	if r != 4294967295 {
		t.Errorf("4294967295 %s 0 = %d, want 4294967295", ">>", r)
	}
	y = 1
	r = x >> y
	if r != 2147483647 {
		t.Errorf("4294967295 %s 1 = %d, want 2147483647", ">>", r)
	}
	y = 4294967296
	r = x >> y
	if r != 0 {
		t.Errorf("4294967295 %s 4294967296 = %d, want 0", ">>", r)
	}
	y = 18446744073709551615
	r = x >> y
	if r != 0 {
		t.Errorf("4294967295 %s 18446744073709551615 = %d, want 0", ">>", r)
	}
}
func TestConstFolduint32uint32lsh(t *testing.T) {
	var x, r uint32
	var y uint32
	x = 0
	y = 0
	r = x << y
	if r != 0 {
		t.Errorf("0 %s 0 = %d, want 0", "<<", r)
	}
	y = 1
	r = x << y
	if r != 0 {
		t.Errorf("0 %s 1 = %d, want 0", "<<", r)
	}
	y = 4294967295
	r = x << y
	if r != 0 {
		t.Errorf("0 %s 4294967295 = %d, want 0", "<<", r)
	}
	x = 1
	y = 0
	r = x << y
	if r != 1 {
		t.Errorf("1 %s 0 = %d, want 1", "<<", r)
	}
	y = 1
	r = x << y
	if r != 2 {
		t.Errorf("1 %s 1 = %d, want 2", "<<", r)
	}
	y = 4294967295
	r = x << y
	if r != 0 {
		t.Errorf("1 %s 4294967295 = %d, want 0", "<<", r)
	}
	x = 4294967295
	y = 0
	r = x << y
	if r != 4294967295 {
		t.Errorf("4294967295 %s 0 = %d, want 4294967295", "<<", r)
	}
	y = 1
	r = x << y
	if r != 4294967294 {
		t.Errorf("4294967295 %s 1 = %d, want 4294967294", "<<", r)
	}
	y = 4294967295
	r = x << y
	if r != 0 {
		t.Errorf("4294967295 %s 4294967295 = %d, want 0", "<<", r)
	}
}
func TestConstFolduint32uint32rsh(t *testing.T) {
	var x, r uint32
	var y uint32
	x = 0
	y = 0
	r = x >> y
	if r != 0 {
		t.Errorf("0 %s 0 = %d, want 0", ">>", r)
	}
	y = 1
	r = x >> y
	if r != 0 {
		t.Errorf("0 %s 1 = %d, want 0", ">>", r)
	}
	y = 4294967295
	r = x >> y
	if r != 0 {
		t.Errorf("0 %s 4294967295 = %d, want 0", ">>", r)
	}
	x = 1
	y = 0
	r = x >> y
	if r != 1 {
		t.Errorf("1 %s 0 = %d, want 1", ">>", r)
	}
	y = 1
	r = x >> y
	if r != 0 {
		t.Errorf("1 %s 1 = %d, want 0", ">>", r)
	}
	y = 4294967295
	r = x >> y
	if r != 0 {
		t.Errorf("1 %s 4294967295 = %d, want 0", ">>", r)
	}
	x = 4294967295
	y = 0
	r = x >> y
	if r != 4294967295 {
		t.Errorf("4294967295 %s 0 = %d, want 4294967295", ">>", r)
	}
	y = 1
	r = x >> y
	if r != 2147483647 {
		t.Errorf("4294967295 %s 1 = %d, want 2147483647", ">>", r)
	}
	y = 4294967295
	r = x >> y
	if r != 0 {
		t.Errorf("4294967295 %s 4294967295 = %d, want 0", ">>", r)
	}
}
func TestConstFolduint32uint16lsh(t *testing.T) {
	var x, r uint32
	var y uint16
	x = 0
	y = 0
	r = x << y
	if r != 0 {
		t.Errorf("0 %s 0 = %d, want 0", "<<", r)
	}
	y = 1
	r = x << y
	if r != 0 {
		t.Errorf("0 %s 1 = %d, want 0", "<<", r)
	}
	y = 65535
	r = x << y
	if r != 0 {
		t.Errorf("0 %s 65535 = %d, want 0", "<<", r)
	}
	x = 1
	y = 0
	r = x << y
	if r != 1 {
		t.Errorf("1 %s 0 = %d, want 1", "<<", r)
	}
	y = 1
	r = x << y
	if r != 2 {
		t.Errorf("1 %s 1 = %d, want 2", "<<", r)
	}
	y = 65535
	r = x << y
	if r != 0 {
		t.Errorf("1 %s 65535 = %d, want 0", "<<", r)
	}
	x = 4294967295
	y = 0
	r = x << y
	if r != 4294967295 {
		t.Errorf("4294967295 %s 0 = %d, want 4294967295", "<<", r)
	}
	y = 1
	r = x << y
	if r != 4294967294 {
		t.Errorf("4294967295 %s 1 = %d, want 4294967294", "<<", r)
	}
	y = 65535
	r = x << y
	if r != 0 {
		t.Errorf("4294967295 %s 65535 = %d, want 0", "<<", r)
	}
}
func TestConstFolduint32uint16rsh(t *testing.T) {
	var x, r uint32
	var y uint16
	x = 0
	y = 0
	r = x >> y
	if r != 0 {
		t.Errorf("0 %s 0 = %d, want 0", ">>", r)
	}
	y = 1
	r = x >> y
	if r != 0 {
		t.Errorf("0 %s 1 = %d, want 0", ">>", r)
	}
	y = 65535
	r = x >> y
	if r != 0 {
		t.Errorf("0 %s 65535 = %d, want 0", ">>", r)
	}
	x = 1
	y = 0
	r = x >> y
	if r != 1 {
		t.Errorf("1 %s 0 = %d, want 1", ">>", r)
	}
	y = 1
	r = x >> y
	if r != 0 {
		t.Errorf("1 %s 1 = %d, want 0", ">>", r)
	}
	y = 65535
	r = x >> y
	if r != 0 {
		t.Errorf("1 %s 65535 = %d, want 0", ">>", r)
	}
	x = 4294967295
	y = 0
	r = x >> y
	if r != 4294967295 {
		t.Errorf("4294967295 %s 0 = %d, want 4294967295", ">>", r)
	}
	y = 1
	r = x >> y
	if r != 2147483647 {
		t.Errorf("4294967295 %s 1 = %d, want 2147483647", ">>", r)
	}
	y = 65535
	r = x >> y
	if r != 0 {
		t.Errorf("4294967295 %s 65535 = %d, want 0", ">>", r)
	}
}
func TestConstFolduint32uint8lsh(t *testing.T) {
	var x, r uint32
	var y uint8
	x = 0
	y = 0
	r = x << y
	if r != 0 {
		t.Errorf("0 %s 0 = %d, want 0", "<<", r)
	}
	y = 1
	r = x << y
	if r != 0 {
		t.Errorf("0 %s 1 = %d, want 0", "<<", r)
	}
	y = 255
	r = x << y
	if r != 0 {
		t.Errorf("0 %s 255 = %d, want 0", "<<", r)
	}
	x = 1
	y = 0
	r = x << y
	if r != 1 {
		t.Errorf("1 %s 0 = %d, want 1", "<<", r)
	}
	y = 1
	r = x << y
	if r != 2 {
		t.Errorf("1 %s 1 = %d, want 2", "<<", r)
	}
	y = 255
	r = x << y
	if r != 0 {
		t.Errorf("1 %s 255 = %d, want 0", "<<", r)
	}
	x = 4294967295
	y = 0
	r = x << y
	if r != 4294967295 {
		t.Errorf("4294967295 %s 0 = %d, want 4294967295", "<<", r)
	}
	y = 1
	r = x << y
	if r != 4294967294 {
		t.Errorf("4294967295 %s 1 = %d, want 4294967294", "<<", r)
	}
	y = 255
	r = x << y
	if r != 0 {
		t.Errorf("4294967295 %s 255 = %d, want 0", "<<", r)
	}
}
func TestConstFolduint32uint8rsh(t *testing.T) {
	var x, r uint32
	var y uint8
	x = 0
	y = 0
	r = x >> y
	if r != 0 {
		t.Errorf("0 %s 0 = %d, want 0", ">>", r)
	}
	y = 1
	r = x >> y
	if r != 0 {
		t.Errorf("0 %s 1 = %d, want 0", ">>", r)
	}
	y = 255
	r = x >> y
	if r != 0 {
		t.Errorf("0 %s 255 = %d, want 0", ">>", r)
	}
	x = 1
	y = 0
	r = x >> y
	if r != 1 {
		t.Errorf("1 %s 0 = %d, want 1", ">>", r)
	}
	y = 1
	r = x >> y
	if r != 0 {
		t.Errorf("1 %s 1 = %d, want 0", ">>", r)
	}
	y = 255
	r = x >> y
	if r != 0 {
		t.Errorf("1 %s 255 = %d, want 0", ">>", r)
	}
	x = 4294967295
	y = 0
	r = x >> y
	if r != 4294967295 {
		t.Errorf("4294967295 %s 0 = %d, want 4294967295", ">>", r)
	}
	y = 1
	r = x >> y
	if r != 2147483647 {
		t.Errorf("4294967295 %s 1 = %d, want 2147483647", ">>", r)
	}
	y = 255
	r = x >> y
	if r != 0 {
		t.Errorf("4294967295 %s 255 = %d, want 0", ">>", r)
	}
}
func TestConstFoldint32uint64lsh(t *testing.T) {
	var x, r int32
	var y uint64
	x = -2147483648
	y = 0
	r = x << y
	if r != -2147483648 {
		t.Errorf("-2147483648 %s 0 = %d, want -2147483648", "<<", r)
	}
	y = 1
	r = x << y
	if r != 0 {
		t.Errorf("-2147483648 %s 1 = %d, want 0", "<<", r)
	}
	y = 4294967296
	r = x << y
	if r != 0 {
		t.Errorf("-2147483648 %s 4294967296 = %d, want 0", "<<", r)
	}
	y = 18446744073709551615
	r = x << y
	if r != 0 {
		t.Errorf("-2147483648 %s 18446744073709551615 = %d, want 0", "<<", r)
	}
	x = -2147483647
	y = 0
	r = x << y
	if r != -2147483647 {
		t.Errorf("-2147483647 %s 0 = %d, want -2147483647", "<<", r)
	}
	y = 1
	r = x << y
	if r != 2 {
		t.Errorf("-2147483647 %s 1 = %d, want 2", "<<", r)
	}
	y = 4294967296
	r = x << y
	if r != 0 {
		t.Errorf("-2147483647 %s 4294967296 = %d, want 0", "<<", r)
	}
	y = 18446744073709551615
	r = x << y
	if r != 0 {
		t.Errorf("-2147483647 %s 18446744073709551615 = %d, want 0", "<<", r)
	}
	x = -1
	y = 0
	r = x << y
	if r != -1 {
		t.Errorf("-1 %s 0 = %d, want -1", "<<", r)
	}
	y = 1
	r = x << y
	if r != -2 {
		t.Errorf("-1 %s 1 = %d, want -2", "<<", r)
	}
	y = 4294967296
	r = x << y
	if r != 0 {
		t.Errorf("-1 %s 4294967296 = %d, want 0", "<<", r)
	}
	y = 18446744073709551615
	r = x << y
	if r != 0 {
		t.Errorf("-1 %s 18446744073709551615 = %d, want 0", "<<", r)
	}
	x = 0
	y = 0
	r = x << y
	if r != 0 {
		t.Errorf("0 %s 0 = %d, want 0", "<<", r)
	}
	y = 1
	r = x << y
	if r != 0 {
		t.Errorf("0 %s 1 = %d, want 0", "<<", r)
	}
	y = 4294967296
	r = x << y
	if r != 0 {
		t.Errorf("0 %s 4294967296 = %d, want 0", "<<", r)
	}
	y = 18446744073709551615
	r = x << y
	if r != 0 {
		t.Errorf("0 %s 18446744073709551615 = %d, want 0", "<<", r)
	}
	x = 1
	y = 0
	r = x << y
	if r != 1 {
		t.Errorf("1 %s 0 = %d, want 1", "<<", r)
	}
	y = 1
	r = x << y
	if r != 2 {
		t.Errorf("1 %s 1 = %d, want 2", "<<", r)
	}
	y = 4294967296
	r = x << y
	if r != 0 {
		t.Errorf("1 %s 4294967296 = %d, want 0", "<<", r)
	}
	y = 18446744073709551615
	r = x << y
	if r != 0 {
		t.Errorf("1 %s 18446744073709551615 = %d, want 0", "<<", r)
	}
	x = 2147483647
	y = 0
	r = x << y
	if r != 2147483647 {
		t.Errorf("2147483647 %s 0 = %d, want 2147483647", "<<", r)
	}
	y = 1
	r = x << y
	if r != -2 {
		t.Errorf("2147483647 %s 1 = %d, want -2", "<<", r)
	}
	y = 4294967296
	r = x << y
	if r != 0 {
		t.Errorf("2147483647 %s 4294967296 = %d, want 0", "<<", r)
	}
	y = 18446744073709551615
	r = x << y
	if r != 0 {
		t.Errorf("2147483647 %s 18446744073709551615 = %d, want 0", "<<", r)
	}
}
func TestConstFoldint32uint64rsh(t *testing.T) {
	var x, r int32
	var y uint64
	x = -2147483648
	y = 0
	r = x >> y
	if r != -2147483648 {
		t.Errorf("-2147483648 %s 0 = %d, want -2147483648", ">>", r)
	}
	y = 1
	r = x >> y
	if r != -1073741824 {
		t.Errorf("-2147483648 %s 1 = %d, want -1073741824", ">>", r)
	}
	y = 4294967296
	r = x >> y
	if r != -1 {
		t.Errorf("-2147483648 %s 4294967296 = %d, want -1", ">>", r)
	}
	y = 18446744073709551615
	r = x >> y
	if r != -1 {
		t.Errorf("-2147483648 %s 18446744073709551615 = %d, want -1", ">>", r)
	}
	x = -2147483647
	y = 0
	r = x >> y
	if r != -2147483647 {
		t.Errorf("-2147483647 %s 0 = %d, want -2147483647", ">>", r)
	}
	y = 1
	r = x >> y
	if r != -1073741824 {
		t.Errorf("-2147483647 %s 1 = %d, want -1073741824", ">>", r)
	}
	y = 4294967296
	r = x >> y
	if r != -1 {
		t.Errorf("-2147483647 %s 4294967296 = %d, want -1", ">>", r)
	}
	y = 18446744073709551615
	r = x >> y
	if r != -1 {
		t.Errorf("-2147483647 %s 18446744073709551615 = %d, want -1", ">>", r)
	}
	x = -1
	y = 0
	r = x >> y
	if r != -1 {
		t.Errorf("-1 %s 0 = %d, want -1", ">>", r)
	}
	y = 1
	r = x >> y
	if r != -1 {
		t.Errorf("-1 %s 1 = %d, want -1", ">>", r)
	}
	y = 4294967296
	r = x >> y
	if r != -1 {
		t.Errorf("-1 %s 4294967296 = %d, want -1", ">>", r)
	}
	y = 18446744073709551615
	r = x >> y
	if r != -1 {
		t.Errorf("-1 %s 18446744073709551615 = %d, want -1", ">>", r)
	}
	x = 0
	y = 0
	r = x >> y
	if r != 0 {
		t.Errorf("0 %s 0 = %d, want 0", ">>", r)
	}
	y = 1
	r = x >> y
	if r != 0 {
		t.Errorf("0 %s 1 = %d, want 0", ">>", r)
	}
	y = 4294967296
	r = x >> y
	if r != 0 {
		t.Errorf("0 %s 4294967296 = %d, want 0", ">>", r)
	}
	y = 18446744073709551615
	r = x >> y
	if r != 0 {
		t.Errorf("0 %s 18446744073709551615 = %d, want 0", ">>", r)
	}
	x = 1
	y = 0
	r = x >> y
	if r != 1 {
		t.Errorf("1 %s 0 = %d, want 1", ">>", r)
	}
	y = 1
	r = x >> y
	if r != 0 {
		t.Errorf("1 %s 1 = %d, want 0", ">>", r)
	}
	y = 4294967296
	r = x >> y
	if r != 0 {
		t.Errorf("1 %s 4294967296 = %d, want 0", ">>", r)
	}
	y = 18446744073709551615
	r = x >> y
	if r != 0 {
		t.Errorf("1 %s 18446744073709551615 = %d, want 0", ">>", r)
	}
	x = 2147483647
	y = 0
	r = x >> y
	if r != 2147483647 {
		t.Errorf("2147483647 %s 0 = %d, want 2147483647", ">>", r)
	}
	y = 1
	r = x >> y
	if r != 1073741823 {
		t.Errorf("2147483647 %s 1 = %d, want 1073741823", ">>", r)
	}
	y = 4294967296
	r = x >> y
	if r != 0 {
		t.Errorf("2147483647 %s 4294967296 = %d, want 0", ">>", r)
	}
	y = 18446744073709551615
	r = x >> y
	if r != 0 {
		t.Errorf("2147483647 %s 18446744073709551615 = %d, want 0", ">>", r)
	}
}
func TestConstFoldint32uint32lsh(t *testing.T) {
	var x, r int32
	var y uint32
	x = -2147483648
	y = 0
	r = x << y
	if r != -2147483648 {
		t.Errorf("-2147483648 %s 0 = %d, want -2147483648", "<<", r)
	}
	y = 1
	r = x << y
	if r != 0 {
		t.Errorf("-2147483648 %s 1 = %d, want 0", "<<", r)
	}
	y = 4294967295
	r = x << y
	if r != 0 {
		t.Errorf("-2147483648 %s 4294967295 = %d, want 0", "<<", r)
	}
	x = -2147483647
	y = 0
	r = x << y
	if r != -2147483647 {
		t.Errorf("-2147483647 %s 0 = %d, want -2147483647", "<<", r)
	}
	y = 1
	r = x << y
	if r != 2 {
		t.Errorf("-2147483647 %s 1 = %d, want 2", "<<", r)
	}
	y = 4294967295
	r = x << y
	if r != 0 {
		t.Errorf("-2147483647 %s 4294967295 = %d, want 0", "<<", r)
	}
	x = -1
	y = 0
	r = x << y
	if r != -1 {
		t.Errorf("-1 %s 0 = %d, want -1", "<<", r)
	}
	y = 1
	r = x << y
	if r != -2 {
		t.Errorf("-1 %s 1 = %d, want -2", "<<", r)
	}
	y = 4294967295
	r = x << y
	if r != 0 {
		t.Errorf("-1 %s 4294967295 = %d, want 0", "<<", r)
	}
	x = 0
	y = 0
	r = x << y
	if r != 0 {
		t.Errorf("0 %s 0 = %d, want 0", "<<", r)
	}
	y = 1
	r = x << y
	if r != 0 {
		t.Errorf("0 %s 1 = %d, want 0", "<<", r)
	}
	y = 4294967295
	r = x << y
	if r != 0 {
		t.Errorf("0 %s 4294967295 = %d, want 0", "<<", r)
	}
	x = 1
	y = 0
	r = x << y
	if r != 1 {
		t.Errorf("1 %s 0 = %d, want 1", "<<", r)
	}
	y = 1
	r = x << y
	if r != 2 {
		t.Errorf("1 %s 1 = %d, want 2", "<<", r)
	}
	y = 4294967295
	r = x << y
	if r != 0 {
		t.Errorf("1 %s 4294967295 = %d, want 0", "<<", r)
	}
	x = 2147483647
	y = 0
	r = x << y
	if r != 2147483647 {
		t.Errorf("2147483647 %s 0 = %d, want 2147483647", "<<", r)
	}
	y = 1
	r = x << y
	if r != -2 {
		t.Errorf("2147483647 %s 1 = %d, want -2", "<<", r)
	}
	y = 4294967295
	r = x << y
	if r != 0 {
		t.Errorf("2147483647 %s 4294967295 = %d, want 0", "<<", r)
	}
}
func TestConstFoldint32uint32rsh(t *testing.T) {
	var x, r int32
	var y uint32
	x = -2147483648
	y = 0
	r = x >> y
	if r != -2147483648 {
		t.Errorf("-2147483648 %s 0 = %d, want -2147483648", ">>", r)
	}
	y = 1
	r = x >> y
	if r != -1073741824 {
		t.Errorf("-2147483648 %s 1 = %d, want -1073741824", ">>", r)
	}
	y = 4294967295
	r = x >> y
	if r != -1 {
		t.Errorf("-2147483648 %s 4294967295 = %d, want -1", ">>", r)
	}
	x = -2147483647
	y = 0
	r = x >> y
	if r != -2147483647 {
		t.Errorf("-2147483647 %s 0 = %d, want -2147483647", ">>", r)
	}
	y = 1
	r = x >> y
	if r != -1073741824 {
		t.Errorf("-2147483647 %s 1 = %d, want -1073741824", ">>", r)
	}
	y = 4294967295
	r = x >> y
	if r != -1 {
		t.Errorf("-2147483647 %s 4294967295 = %d, want -1", ">>", r)
	}
	x = -1
	y = 0
	r = x >> y
	if r != -1 {
		t.Errorf("-1 %s 0 = %d, want -1", ">>", r)
	}
	y = 1
	r = x >> y
	if r != -1 {
		t.Errorf("-1 %s 1 = %d, want -1", ">>", r)
	}
	y = 4294967295
	r = x >> y
	if r != -1 {
		t.Errorf("-1 %s 4294967295 = %d, want -1", ">>", r)
	}
	x = 0
	y = 0
	r = x >> y
	if r != 0 {
		t.Errorf("0 %s 0 = %d, want 0", ">>", r)
	}
	y = 1
	r = x >> y
	if r != 0 {
		t.Errorf("0 %s 1 = %d, want 0", ">>", r)
	}
	y = 4294967295
	r = x >> y
	if r != 0 {
		t.Errorf("0 %s 4294967295 = %d, want 0", ">>", r)
	}
	x = 1
	y = 0
	r = x >> y
	if r != 1 {
		t.Errorf("1 %s 0 = %d, want 1", ">>", r)
	}
	y = 1
	r = x >> y
	if r != 0 {
		t.Errorf("1 %s 1 = %d, want 0", ">>", r)
	}
	y = 4294967295
	r = x >> y
	if r != 0 {
		t.Errorf("1 %s 4294967295 = %d, want 0", ">>", r)
	}
	x = 2147483647
	y = 0
	r = x >> y
	if r != 2147483647 {
		t.Errorf("2147483647 %s 0 = %d, want 2147483647", ">>", r)
	}
	y = 1
	r = x >> y
	if r != 1073741823 {
		t.Errorf("2147483647 %s 1 = %d, want 1073741823", ">>", r)
	}
	y = 4294967295
	r = x >> y
	if r != 0 {
		t.Errorf("2147483647 %s 4294967295 = %d, want 0", ">>", r)
	}
}
func TestConstFoldint32uint16lsh(t *testing.T) {
	var x, r int32
	var y uint16
	x = -2147483648
	y = 0
	r = x << y
	if r != -2147483648 {
		t.Errorf("-2147483648 %s 0 = %d, want -2147483648", "<<", r)
	}
	y = 1
	r = x << y
	if r != 0 {
		t.Errorf("-2147483648 %s 1 = %d, want 0", "<<", r)
	}
	y = 65535
	r = x << y
	if r != 0 {
		t.Errorf("-2147483648 %s 65535 = %d, want 0", "<<", r)
	}
	x = -2147483647
	y = 0
	r = x << y
	if r != -2147483647 {
		t.Errorf("-2147483647 %s 0 = %d, want -2147483647", "<<", r)
	}
	y = 1
	r = x << y
	if r != 2 {
		t.Errorf("-2147483647 %s 1 = %d, want 2", "<<", r)
	}
	y = 65535
	r = x << y
	if r != 0 {
		t.Errorf("-2147483647 %s 65535 = %d, want 0", "<<", r)
	}
	x = -1
	y = 0
	r = x << y
	if r != -1 {
		t.Errorf("-1 %s 0 = %d, want -1", "<<", r)
	}
	y = 1
	r = x << y
	if r != -2 {
		t.Errorf("-1 %s 1 = %d, want -2", "<<", r)
	}
	y = 65535
	r = x << y
	if r != 0 {
		t.Errorf("-1 %s 65535 = %d, want 0", "<<", r)
	}
	x = 0
	y = 0
	r = x << y
	if r != 0 {
		t.Errorf("0 %s 0 = %d, want 0", "<<", r)
	}
	y = 1
	r = x << y
	if r != 0 {
		t.Errorf("0 %s 1 = %d, want 0", "<<", r)
	}
	y = 65535
	r = x << y
	if r != 0 {
		t.Errorf("0 %s 65535 = %d, want 0", "<<", r)
	}
	x = 1
	y = 0
	r = x << y
	if r != 1 {
		t.Errorf("1 %s 0 = %d, want 1", "<<", r)
	}
	y = 1
	r = x << y
	if r != 2 {
		t.Errorf("1 %s 1 = %d, want 2", "<<", r)
	}
	y = 65535
	r = x << y
	if r != 0 {
		t.Errorf("1 %s 65535 = %d, want 0", "<<", r)
	}
	x = 2147483647
	y = 0
	r = x << y
	if r != 2147483647 {
		t.Errorf("2147483647 %s 0 = %d, want 2147483647", "<<", r)
	}
	y = 1
	r = x << y
	if r != -2 {
		t.Errorf("2147483647 %s 1 = %d, want -2", "<<", r)
	}
	y = 65535
	r = x << y
	if r != 0 {
		t.Errorf("2147483647 %s 65535 = %d, want 0", "<<", r)
	}
}
func TestConstFoldint32uint16rsh(t *testing.T) {
	var x, r int32
	var y uint16
	x = -2147483648
	y = 0
	r = x >> y
	if r != -2147483648 {
		t.Errorf("-2147483648 %s 0 = %d, want -2147483648", ">>", r)
	}
	y = 1
	r = x >> y
	if r != -1073741824 {
		t.Errorf("-2147483648 %s 1 = %d, want -1073741824", ">>", r)
	}
	y = 65535
	r = x >> y
	if r != -1 {
		t.Errorf("-2147483648 %s 65535 = %d, want -1", ">>", r)
	}
	x = -2147483647
	y = 0
	r = x >> y
	if r != -2147483647 {
		t.Errorf("-2147483647 %s 0 = %d, want -2147483647", ">>", r)
	}
	y = 1
	r = x >> y
	if r != -1073741824 {
		t.Errorf("-2147483647 %s 1 = %d, want -1073741824", ">>", r)
	}
	y = 65535
	r = x >> y
	if r != -1 {
		t.Errorf("-2147483647 %s 65535 = %d, want -1", ">>", r)
	}
	x = -1
	y = 0
	r = x >> y
	if r != -1 {
		t.Errorf("-1 %s 0 = %d, want -1", ">>", r)
	}
	y = 1
	r = x >> y
	if r != -1 {
		t.Errorf("-1 %s 1 = %d, want -1", ">>", r)
	}
	y = 65535
	r = x >> y
	if r != -1 {
		t.Errorf("-1 %s 65535 = %d, want -1", ">>", r)
	}
	x = 0
	y = 0
	r = x >> y
	if r != 0 {
		t.Errorf("0 %s 0 = %d, want 0", ">>", r)
	}
	y = 1
	r = x >> y
	if r != 0 {
		t.Errorf("0 %s 1 = %d, want 0", ">>", r)
	}
	y = 65535
	r = x >> y
	if r != 0 {
		t.Errorf("0 %s 65535 = %d, want 0", ">>", r)
	}
	x = 1
	y = 0
	r = x >> y
	if r != 1 {
		t.Errorf("1 %s 0 = %d, want 1", ">>", r)
	}
	y = 1
	r = x >> y
	if r != 0 {
		t.Errorf("1 %s 1 = %d, want 0", ">>", r)
	}
	y = 65535
	r = x >> y
	if r != 0 {
		t.Errorf("1 %s 65535 = %d, want 0", ">>", r)
	}
	x = 2147483647
	y = 0
	r = x >> y
	if r != 2147483647 {
		t.Errorf("2147483647 %s 0 = %d, want 2147483647", ">>", r)
	}
	y = 1
	r = x >> y
	if r != 1073741823 {
		t.Errorf("2147483647 %s 1 = %d, want 1073741823", ">>", r)
	}
	y = 65535
	r = x >> y
	if r != 0 {
		t.Errorf("2147483647 %s 65535 = %d, want 0", ">>", r)
	}
}
func TestConstFoldint32uint8lsh(t *testing.T) {
	var x, r int32
	var y uint8
	x = -2147483648
	y = 0
	r = x << y
	if r != -2147483648 {
		t.Errorf("-2147483648 %s 0 = %d, want -2147483648", "<<", r)
	}
	y = 1
	r = x << y
	if r != 0 {
		t.Errorf("-2147483648 %s 1 = %d, want 0", "<<", r)
	}
	y = 255
	r = x << y
	if r != 0 {
		t.Errorf("-2147483648 %s 255 = %d, want 0", "<<", r)
	}
	x = -2147483647
	y = 0
	r = x << y
	if r != -2147483647 {
		t.Errorf("-2147483647 %s 0 = %d, want -2147483647", "<<", r)
	}
	y = 1
	r = x << y
	if r != 2 {
		t.Errorf("-2147483647 %s 1 = %d, want 2", "<<", r)
	}
	y = 255
	r = x << y
	if r != 0 {
		t.Errorf("-2147483647 %s 255 = %d, want 0", "<<", r)
	}
	x = -1
	y = 0
	r = x << y
	if r != -1 {
		t.Errorf("-1 %s 0 = %d, want -1", "<<", r)
	}
	y = 1
	r = x << y
	if r != -2 {
		t.Errorf("-1 %s 1 = %d, want -2", "<<", r)
	}
	y = 255
	r = x << y
	if r != 0 {
		t.Errorf("-1 %s 255 = %d, want 0", "<<", r)
	}
	x = 0
	y = 0
	r = x << y
	if r != 0 {
		t.Errorf("0 %s 0 = %d, want 0", "<<", r)
	}
	y = 1
	r = x << y
	if r != 0 {
		t.Errorf("0 %s 1 = %d, want 0", "<<", r)
	}
	y = 255
	r = x << y
	if r != 0 {
		t.Errorf("0 %s 255 = %d, want 0", "<<", r)
	}
	x = 1
	y = 0
	r = x << y
	if r != 1 {
		t.Errorf("1 %s 0 = %d, want 1", "<<", r)
	}
	y = 1
	r = x << y
	if r != 2 {
		t.Errorf("1 %s 1 = %d, want 2", "<<", r)
	}
	y = 255
	r = x << y
	if r != 0 {
		t.Errorf("1 %s 255 = %d, want 0", "<<", r)
	}
	x = 2147483647
	y = 0
	r = x << y
	if r != 2147483647 {
		t.Errorf("2147483647 %s 0 = %d, want 2147483647", "<<", r)
	}
	y = 1
	r = x << y
	if r != -2 {
		t.Errorf("2147483647 %s 1 = %d, want -2", "<<", r)
	}
	y = 255
	r = x << y
	if r != 0 {
		t.Errorf("2147483647 %s 255 = %d, want 0", "<<", r)
	}
}
func TestConstFoldint32uint8rsh(t *testing.T) {
	var x, r int32
	var y uint8
	x = -2147483648
	y = 0
	r = x >> y
	if r != -2147483648 {
		t.Errorf("-2147483648 %s 0 = %d, want -2147483648", ">>", r)
	}
	y = 1
	r = x >> y
	if r != -1073741824 {
		t.Errorf("-2147483648 %s 1 = %d, want -1073741824", ">>", r)
	}
	y = 255
	r = x >> y
	if r != -1 {
		t.Errorf("-2147483648 %s 255 = %d, want -1", ">>", r)
	}
	x = -2147483647
	y = 0
	r = x >> y
	if r != -2147483647 {
		t.Errorf("-2147483647 %s 0 = %d, want -2147483647", ">>", r)
	}
	y = 1
	r = x >> y
	if r != -1073741824 {
		t.Errorf("-2147483647 %s 1 = %d, want -1073741824", ">>", r)
	}
	y = 255
	r = x >> y
	if r != -1 {
		t.Errorf("-2147483647 %s 255 = %d, want -1", ">>", r)
	}
	x = -1
	y = 0
	r = x >> y
	if r != -1 {
		t.Errorf("-1 %s 0 = %d, want -1", ">>", r)
	}
	y = 1
	r = x >> y
	if r != -1 {
		t.Errorf("-1 %s 1 = %d, want -1", ">>", r)
	}
	y = 255
	r = x >> y
	if r != -1 {
		t.Errorf("-1 %s 255 = %d, want -1", ">>", r)
	}
	x = 0
	y = 0
	r = x >> y
	if r != 0 {
		t.Errorf("0 %s 0 = %d, want 0", ">>", r)
	}
	y = 1
	r = x >> y
	if r != 0 {
		t.Errorf("0 %s 1 = %d, want 0", ">>", r)
	}
	y = 255
	r = x >> y
	if r != 0 {
		t.Errorf("0 %s 255 = %d, want 0", ">>", r)
	}
	x = 1
	y = 0
	r = x >> y
	if r != 1 {
		t.Errorf("1 %s 0 = %d, want 1", ">>", r)
	}
	y = 1
	r = x >> y
	if r != 0 {
		t.Errorf("1 %s 1 = %d, want 0", ">>", r)
	}
	y = 255
	r = x >> y
	if r != 0 {
		t.Errorf("1 %s 255 = %d, want 0", ">>", r)
	}
	x = 2147483647
	y = 0
	r = x >> y
	if r != 2147483647 {
		t.Errorf("2147483647 %s 0 = %d, want 2147483647", ">>", r)
	}
	y = 1
	r = x >> y
	if r != 1073741823 {
		t.Errorf("2147483647 %s 1 = %d, want 1073741823", ">>", r)
	}
	y = 255
	r = x >> y
	if r != 0 {
		t.Errorf("2147483647 %s 255 = %d, want 0", ">>", r)
	}
}
func TestConstFolduint16uint64lsh(t *testing.T) {
	var x, r uint16
	var y uint64
	x = 0
	y = 0
	r = x << y
	if r != 0 {
		t.Errorf("0 %s 0 = %d, want 0", "<<", r)
	}
	y = 1
	r = x << y
	if r != 0 {
		t.Errorf("0 %s 1 = %d, want 0", "<<", r)
	}
	y = 4294967296
	r = x << y
	if r != 0 {
		t.Errorf("0 %s 4294967296 = %d, want 0", "<<", r)
	}
	y = 18446744073709551615
	r = x << y
	if r != 0 {
		t.Errorf("0 %s 18446744073709551615 = %d, want 0", "<<", r)
	}
	x = 1
	y = 0
	r = x << y
	if r != 1 {
		t.Errorf("1 %s 0 = %d, want 1", "<<", r)
	}
	y = 1
	r = x << y
	if r != 2 {
		t.Errorf("1 %s 1 = %d, want 2", "<<", r)
	}
	y = 4294967296
	r = x << y
	if r != 0 {
		t.Errorf("1 %s 4294967296 = %d, want 0", "<<", r)
	}
	y = 18446744073709551615
	r = x << y
	if r != 0 {
		t.Errorf("1 %s 18446744073709551615 = %d, want 0", "<<", r)
	}
	x = 65535
	y = 0
	r = x << y
	if r != 65535 {
		t.Errorf("65535 %s 0 = %d, want 65535", "<<", r)
	}
	y = 1
	r = x << y
	if r != 65534 {
		t.Errorf("65535 %s 1 = %d, want 65534", "<<", r)
	}
	y = 4294967296
	r = x << y
	if r != 0 {
		t.Errorf("65535 %s 4294967296 = %d, want 0", "<<", r)
	}
	y = 18446744073709551615
	r = x << y
	if r != 0 {
		t.Errorf("65535 %s 18446744073709551615 = %d, want 0", "<<", r)
	}
}
func TestConstFolduint16uint64rsh(t *testing.T) {
	var x, r uint16
	var y uint64
	x = 0
	y = 0
	r = x >> y
	if r != 0 {
		t.Errorf("0 %s 0 = %d, want 0", ">>", r)
	}
	y = 1
	r = x >> y
	if r != 0 {
		t.Errorf("0 %s 1 = %d, want 0", ">>", r)
	}
	y = 4294967296
	r = x >> y
	if r != 0 {
		t.Errorf("0 %s 4294967296 = %d, want 0", ">>", r)
	}
	y = 18446744073709551615
	r = x >> y
	if r != 0 {
		t.Errorf("0 %s 18446744073709551615 = %d, want 0", ">>", r)
	}
	x = 1
	y = 0
	r = x >> y
	if r != 1 {
		t.Errorf("1 %s 0 = %d, want 1", ">>", r)
	}
	y = 1
	r = x >> y
	if r != 0 {
		t.Errorf("1 %s 1 = %d, want 0", ">>", r)
	}
	y = 4294967296
	r = x >> y
	if r != 0 {
		t.Errorf("1 %s 4294967296 = %d, want 0", ">>", r)
	}
	y = 18446744073709551615
	r = x >> y
	if r != 0 {
		t.Errorf("1 %s 18446744073709551615 = %d, want 0", ">>", r)
	}
	x = 65535
	y = 0
	r = x >> y
	if r != 65535 {
		t.Errorf("65535 %s 0 = %d, want 65535", ">>", r)
	}
	y = 1
	r = x >> y
	if r != 32767 {
		t.Errorf("65535 %s 1 = %d, want 32767", ">>", r)
	}
	y = 4294967296
	r = x >> y
	if r != 0 {
		t.Errorf("65535 %s 4294967296 = %d, want 0", ">>", r)
	}
	y = 18446744073709551615
	r = x >> y
	if r != 0 {
		t.Errorf("65535 %s 18446744073709551615 = %d, want 0", ">>", r)
	}
}
func TestConstFolduint16uint32lsh(t *testing.T) {
	var x, r uint16
	var y uint32
	x = 0
	y = 0
	r = x << y
	if r != 0 {
		t.Errorf("0 %s 0 = %d, want 0", "<<", r)
	}
	y = 1
	r = x << y
	if r != 0 {
		t.Errorf("0 %s 1 = %d, want 0", "<<", r)
	}
	y = 4294967295
	r = x << y
	if r != 0 {
		t.Errorf("0 %s 4294967295 = %d, want 0", "<<", r)
	}
	x = 1
	y = 0
	r = x << y
	if r != 1 {
		t.Errorf("1 %s 0 = %d, want 1", "<<", r)
	}
	y = 1
	r = x << y
	if r != 2 {
		t.Errorf("1 %s 1 = %d, want 2", "<<", r)
	}
	y = 4294967295
	r = x << y
	if r != 0 {
		t.Errorf("1 %s 4294967295 = %d, want 0", "<<", r)
	}
	x = 65535
	y = 0
	r = x << y
	if r != 65535 {
		t.Errorf("65535 %s 0 = %d, want 65535", "<<", r)
	}
	y = 1
	r = x << y
	if r != 65534 {
		t.Errorf("65535 %s 1 = %d, want 65534", "<<", r)
	}
	y = 4294967295
	r = x << y
	if r != 0 {
		t.Errorf("65535 %s 4294967295 = %d, want 0", "<<", r)
	}
}
func TestConstFolduint16uint32rsh(t *testing.T) {
	var x, r uint16
	var y uint32
	x = 0
	y = 0
	r = x >> y
	if r != 0 {
		t.Errorf("0 %s 0 = %d, want 0", ">>", r)
	}
	y = 1
	r = x >> y
	if r != 0 {
		t.Errorf("0 %s 1 = %d, want 0", ">>", r)
	}
	y = 4294967295
	r = x >> y
	if r != 0 {
		t.Errorf("0 %s 4294967295 = %d, want 0", ">>", r)
	}
	x = 1
	y = 0
	r = x >> y
	if r != 1 {
		t.Errorf("1 %s 0 = %d, want 1", ">>", r)
	}
	y = 1
	r = x >> y
	if r != 0 {
		t.Errorf("1 %s 1 = %d, want 0", ">>", r)
	}
	y = 4294967295
	r = x >> y
	if r != 0 {
		t.Errorf("1 %s 4294967295 = %d, want 0", ">>", r)
	}
	x = 65535
	y = 0
	r = x >> y
	if r != 65535 {
		t.Errorf("65535 %s 0 = %d, want 65535", ">>", r)
	}
	y = 1
	r = x >> y
	if r != 32767 {
		t.Errorf("65535 %s 1 = %d, want 32767", ">>", r)
	}
	y = 4294967295
	r = x >> y
	if r != 0 {
		t.Errorf("65535 %s 4294967295 = %d, want 0", ">>", r)
	}
}
func TestConstFolduint16uint16lsh(t *testing.T) {
	var x, r uint16
	var y uint16
	x = 0
	y = 0
	r = x << y
	if r != 0 {
		t.Errorf("0 %s 0 = %d, want 0", "<<", r)
	}
	y = 1
	r = x << y
	if r != 0 {
		t.Errorf("0 %s 1 = %d, want 0", "<<", r)
	}
	y = 65535
	r = x << y
	if r != 0 {
		t.Errorf("0 %s 65535 = %d, want 0", "<<", r)
	}
	x = 1
	y = 0
	r = x << y
	if r != 1 {
		t.Errorf("1 %s 0 = %d, want 1", "<<", r)
	}
	y = 1
	r = x << y
	if r != 2 {
		t.Errorf("1 %s 1 = %d, want 2", "<<", r)
	}
	y = 65535
	r = x << y
	if r != 0 {
		t.Errorf("1 %s 65535 = %d, want 0", "<<", r)
	}
	x = 65535
	y = 0
	r = x << y
	if r != 65535 {
		t.Errorf("65535 %s 0 = %d, want 65535", "<<", r)
	}
	y = 1
	r = x << y
	if r != 65534 {
		t.Errorf("65535 %s 1 = %d, want 65534", "<<", r)
	}
	y = 65535
	r = x << y
	if r != 0 {
		t.Errorf("65535 %s 65535 = %d, want 0", "<<", r)
	}
}
func TestConstFolduint16uint16rsh(t *testing.T) {
	var x, r uint16
	var y uint16
	x = 0
	y = 0
	r = x >> y
	if r != 0 {
		t.Errorf("0 %s 0 = %d, want 0", ">>", r)
	}
	y = 1
	r = x >> y
	if r != 0 {
		t.Errorf("0 %s 1 = %d, want 0", ">>", r)
	}
	y = 65535
	r = x >> y
	if r != 0 {
		t.Errorf("0 %s 65535 = %d, want 0", ">>", r)
	}
	x = 1
	y = 0
	r = x >> y
	if r != 1 {
		t.Errorf("1 %s 0 = %d, want 1", ">>", r)
	}
	y = 1
	r = x >> y
	if r != 0 {
		t.Errorf("1 %s 1 = %d, want 0", ">>", r)
	}
	y = 65535
	r = x >> y
	if r != 0 {
		t.Errorf("1 %s 65535 = %d, want 0", ">>", r)
	}
	x = 65535
	y = 0
	r = x >> y
	if r != 65535 {
		t.Errorf("65535 %s 0 = %d, want 65535", ">>", r)
	}
	y = 1
	r = x >> y
	if r != 32767 {
		t.Errorf("65535 %s 1 = %d, want 32767", ">>", r)
	}
	y = 65535
	r = x >> y
	if r != 0 {
		t.Errorf("65535 %s 65535 = %d, want 0", ">>", r)
	}
}
func TestConstFolduint16uint8lsh(t *testing.T) {
	var x, r uint16
	var y uint8
	x = 0
	y = 0
	r = x << y
	if r != 0 {
		t.Errorf("0 %s 0 = %d, want 0", "<<", r)
	}
	y = 1
	r = x << y
	if r != 0 {
		t.Errorf("0 %s 1 = %d, want 0", "<<", r)
	}
	y = 255
	r = x << y
	if r != 0 {
		t.Errorf("0 %s 255 = %d, want 0", "<<", r)
	}
	x = 1
	y = 0
	r = x << y
	if r != 1 {
		t.Errorf("1 %s 0 = %d, want 1", "<<", r)
	}
	y = 1
	r = x << y
	if r != 2 {
		t.Errorf("1 %s 1 = %d, want 2", "<<", r)
	}
	y = 255
	r = x << y
	if r != 0 {
		t.Errorf("1 %s 255 = %d, want 0", "<<", r)
	}
	x = 65535
	y = 0
	r = x << y
	if r != 65535 {
		t.Errorf("65535 %s 0 = %d, want 65535", "<<", r)
	}
	y = 1
	r = x << y
	if r != 65534 {
		t.Errorf("65535 %s 1 = %d, want 65534", "<<", r)
	}
	y = 255
	r = x << y
	if r != 0 {
		t.Errorf("65535 %s 255 = %d, want 0", "<<", r)
	}
}
func TestConstFolduint16uint8rsh(t *testing.T) {
	var x, r uint16
	var y uint8
	x = 0
	y = 0
	r = x >> y
	if r != 0 {
		t.Errorf("0 %s 0 = %d, want 0", ">>", r)
	}
	y = 1
	r = x >> y
	if r != 0 {
		t.Errorf("0 %s 1 = %d, want 0", ">>", r)
	}
	y = 255
	r = x >> y
	if r != 0 {
		t.Errorf("0 %s 255 = %d, want 0", ">>", r)
	}
	x = 1
	y = 0
	r = x >> y
	if r != 1 {
		t.Errorf("1 %s 0 = %d, want 1", ">>", r)
	}
	y = 1
	r = x >> y
	if r != 0 {
		t.Errorf("1 %s 1 = %d, want 0", ">>", r)
	}
	y = 255
	r = x >> y
	if r != 0 {
		t.Errorf("1 %s 255 = %d, want 0", ">>", r)
	}
	x = 65535
	y = 0
	r = x >> y
	if r != 65535 {
		t.Errorf("65535 %s 0 = %d, want 65535", ">>", r)
	}
	y = 1
	r = x >> y
	if r != 32767 {
		t.Errorf("65535 %s 1 = %d, want 32767", ">>", r)
	}
	y = 255
	r = x >> y
	if r != 0 {
		t.Errorf("65535 %s 255 = %d, want 0", ">>", r)
	}
}
func TestConstFoldint16uint64lsh(t *testing.T) {
	var x, r int16
	var y uint64
	x = -32768
	y = 0
	r = x << y
	if r != -32768 {
		t.Errorf("-32768 %s 0 = %d, want -32768", "<<", r)
	}
	y = 1
	r = x << y
	if r != 0 {
		t.Errorf("-32768 %s 1 = %d, want 0", "<<", r)
	}
	y = 4294967296
	r = x << y
	if r != 0 {
		t.Errorf("-32768 %s 4294967296 = %d, want 0", "<<", r)
	}
	y = 18446744073709551615
	r = x << y
	if r != 0 {
		t.Errorf("-32768 %s 18446744073709551615 = %d, want 0", "<<", r)
	}
	x = -32767
	y = 0
	r = x << y
	if r != -32767 {
		t.Errorf("-32767 %s 0 = %d, want -32767", "<<", r)
	}
	y = 1
	r = x << y
	if r != 2 {
		t.Errorf("-32767 %s 1 = %d, want 2", "<<", r)
	}
	y = 4294967296
	r = x << y
	if r != 0 {
		t.Errorf("-32767 %s 4294967296 = %d, want 0", "<<", r)
	}
	y = 18446744073709551615
	r = x << y
	if r != 0 {
		t.Errorf("-32767 %s 18446744073709551615 = %d, want 0", "<<", r)
	}
	x = -1
	y = 0
	r = x << y
	if r != -1 {
		t.Errorf("-1 %s 0 = %d, want -1", "<<", r)
	}
	y = 1
	r = x << y
	if r != -2 {
		t.Errorf("-1 %s 1 = %d, want -2", "<<", r)
	}
	y = 4294967296
	r = x << y
	if r != 0 {
		t.Errorf("-1 %s 4294967296 = %d, want 0", "<<", r)
	}
	y = 18446744073709551615
	r = x << y
	if r != 0 {
		t.Errorf("-1 %s 18446744073709551615 = %d, want 0", "<<", r)
	}
	x = 0
	y = 0
	r = x << y
	if r != 0 {
		t.Errorf("0 %s 0 = %d, want 0", "<<", r)
	}
	y = 1
	r = x << y
	if r != 0 {
		t.Errorf("0 %s 1 = %d, want 0", "<<", r)
	}
	y = 4294967296
	r = x << y
	if r != 0 {
		t.Errorf("0 %s 4294967296 = %d, want 0", "<<", r)
	}
	y = 18446744073709551615
	r = x << y
	if r != 0 {
		t.Errorf("0 %s 18446744073709551615 = %d, want 0", "<<", r)
	}
	x = 1
	y = 0
	r = x << y
	if r != 1 {
		t.Errorf("1 %s 0 = %d, want 1", "<<", r)
	}
	y = 1
	r = x << y
	if r != 2 {
		t.Errorf("1 %s 1 = %d, want 2", "<<", r)
	}
	y = 4294967296
	r = x << y
	if r != 0 {
		t.Errorf("1 %s 4294967296 = %d, want 0", "<<", r)
	}
	y = 18446744073709551615
	r = x << y
	if r != 0 {
		t.Errorf("1 %s 18446744073709551615 = %d, want 0", "<<", r)
	}
	x = 32766
	y = 0
	r = x << y
	if r != 32766 {
		t.Errorf("32766 %s 0 = %d, want 32766", "<<", r)
	}
	y = 1
	r = x << y
	if r != -4 {
		t.Errorf("32766 %s 1 = %d, want -4", "<<", r)
	}
	y = 4294967296
	r = x << y
	if r != 0 {
		t.Errorf("32766 %s 4294967296 = %d, want 0", "<<", r)
	}
	y = 18446744073709551615
	r = x << y
	if r != 0 {
		t.Errorf("32766 %s 18446744073709551615 = %d, want 0", "<<", r)
	}
	x = 32767
	y = 0
	r = x << y
	if r != 32767 {
		t.Errorf("32767 %s 0 = %d, want 32767", "<<", r)
	}
	y = 1
	r = x << y
	if r != -2 {
		t.Errorf("32767 %s 1 = %d, want -2", "<<", r)
	}
	y = 4294967296
	r = x << y
	if r != 0 {
		t.Errorf("32767 %s 4294967296 = %d, want 0", "<<", r)
	}
	y = 18446744073709551615
	r = x << y
	if r != 0 {
		t.Errorf("32767 %s 18446744073709551615 = %d, want 0", "<<", r)
	}
}
func TestConstFoldint16uint64rsh(t *testing.T) {
	var x, r int16
	var y uint64
	x = -32768
	y = 0
	r = x >> y
	if r != -32768 {
		t.Errorf("-32768 %s 0 = %d, want -32768", ">>", r)
	}
	y = 1
	r = x >> y
	if r != -16384 {
		t.Errorf("-32768 %s 1 = %d, want -16384", ">>", r)
	}
	y = 4294967296
	r = x >> y
	if r != -1 {
		t.Errorf("-32768 %s 4294967296 = %d, want -1", ">>", r)
	}
	y = 18446744073709551615
	r = x >> y
	if r != -1 {
		t.Errorf("-32768 %s 18446744073709551615 = %d, want -1", ">>", r)
	}
	x = -32767
	y = 0
	r = x >> y
	if r != -32767 {
		t.Errorf("-32767 %s 0 = %d, want -32767", ">>", r)
	}
	y = 1
	r = x >> y
	if r != -16384 {
		t.Errorf("-32767 %s 1 = %d, want -16384", ">>", r)
	}
	y = 4294967296
	r = x >> y
	if r != -1 {
		t.Errorf("-32767 %s 4294967296 = %d, want -1", ">>", r)
	}
	y = 18446744073709551615
	r = x >> y
	if r != -1 {
		t.Errorf("-32767 %s 18446744073709551615 = %d, want -1", ">>", r)
	}
	x = -1
	y = 0
	r = x >> y
	if r != -1 {
		t.Errorf("-1 %s 0 = %d, want -1", ">>", r)
	}
	y = 1
	r = x >> y
	if r != -1 {
		t.Errorf("-1 %s 1 = %d, want -1", ">>", r)
	}
	y = 4294967296
	r = x >> y
	if r != -1 {
		t.Errorf("-1 %s 4294967296 = %d, want -1", ">>", r)
	}
	y = 18446744073709551615
	r = x >> y
	if r != -1 {
		t.Errorf("-1 %s 18446744073709551615 = %d, want -1", ">>", r)
	}
	x = 0
	y = 0
	r = x >> y
	if r != 0 {
		t.Errorf("0 %s 0 = %d, want 0", ">>", r)
	}
	y = 1
	r = x >> y
	if r != 0 {
		t.Errorf("0 %s 1 = %d, want 0", ">>", r)
	}
	y = 4294967296
	r = x >> y
	if r != 0 {
		t.Errorf("0 %s 4294967296 = %d, want 0", ">>", r)
	}
	y = 18446744073709551615
	r = x >> y
	if r != 0 {
		t.Errorf("0 %s 18446744073709551615 = %d, want 0", ">>", r)
	}
	x = 1
	y = 0
	r = x >> y
	if r != 1 {
		t.Errorf("1 %s 0 = %d, want 1", ">>", r)
	}
	y = 1
	r = x >> y
	if r != 0 {
		t.Errorf("1 %s 1 = %d, want 0", ">>", r)
	}
	y = 4294967296
	r = x >> y
	if r != 0 {
		t.Errorf("1 %s 4294967296 = %d, want 0", ">>", r)
	}
	y = 18446744073709551615
	r = x >> y
	if r != 0 {
		t.Errorf("1 %s 18446744073709551615 = %d, want 0", ">>", r)
	}
	x = 32766
	y = 0
	r = x >> y
	if r != 32766 {
		t.Errorf("32766 %s 0 = %d, want 32766", ">>", r)
	}
	y = 1
	r = x >> y
	if r != 16383 {
		t.Errorf("32766 %s 1 = %d, want 16383", ">>", r)
	}
	y = 4294967296
	r = x >> y
	if r != 0 {
		t.Errorf("32766 %s 4294967296 = %d, want 0", ">>", r)
	}
	y = 18446744073709551615
	r = x >> y
	if r != 0 {
		t.Errorf("32766 %s 18446744073709551615 = %d, want 0", ">>", r)
	}
	x = 32767
	y = 0
	r = x >> y
	if r != 32767 {
		t.Errorf("32767 %s 0 = %d, want 32767", ">>", r)
	}
	y = 1
	r = x >> y
	if r != 16383 {
		t.Errorf("32767 %s 1 = %d, want 16383", ">>", r)
	}
	y = 4294967296
	r = x >> y
	if r != 0 {
		t.Errorf("32767 %s 4294967296 = %d, want 0", ">>", r)
	}
	y = 18446744073709551615
	r = x >> y
	if r != 0 {
		t.Errorf("32767 %s 18446744073709551615 = %d, want 0", ">>", r)
	}
}
func TestConstFoldint16uint32lsh(t *testing.T) {
	var x, r int16
	var y uint32
	x = -32768
	y = 0
	r = x << y
	if r != -32768 {
		t.Errorf("-32768 %s 0 = %d, want -32768", "<<", r)
	}
	y = 1
	r = x << y
	if r != 0 {
		t.Errorf("-32768 %s 1 = %d, want 0", "<<", r)
	}
	y = 4294967295
	r = x << y
	if r != 0 {
		t.Errorf("-32768 %s 4294967295 = %d, want 0", "<<", r)
	}
	x = -32767
	y = 0
	r = x << y
	if r != -32767 {
		t.Errorf("-32767 %s 0 = %d, want -32767", "<<", r)
	}
	y = 1
	r = x << y
	if r != 2 {
		t.Errorf("-32767 %s 1 = %d, want 2", "<<", r)
	}
	y = 4294967295
	r = x << y
	if r != 0 {
		t.Errorf("-32767 %s 4294967295 = %d, want 0", "<<", r)
	}
	x = -1
	y = 0
	r = x << y
	if r != -1 {
		t.Errorf("-1 %s 0 = %d, want -1", "<<", r)
	}
	y = 1
	r = x << y
	if r != -2 {
		t.Errorf("-1 %s 1 = %d, want -2", "<<", r)
	}
	y = 4294967295
	r = x << y
	if r != 0 {
		t.Errorf("-1 %s 4294967295 = %d, want 0", "<<", r)
	}
	x = 0
	y = 0
	r = x << y
	if r != 0 {
		t.Errorf("0 %s 0 = %d, want 0", "<<", r)
	}
	y = 1
	r = x << y
	if r != 0 {
		t.Errorf("0 %s 1 = %d, want 0", "<<", r)
	}
	y = 4294967295
	r = x << y
	if r != 0 {
		t.Errorf("0 %s 4294967295 = %d, want 0", "<<", r)
	}
	x = 1
	y = 0
	r = x << y
	if r != 1 {
		t.Errorf("1 %s 0 = %d, want 1", "<<", r)
	}
	y = 1
	r = x << y
	if r != 2 {
		t.Errorf("1 %s 1 = %d, want 2", "<<", r)
	}
	y = 4294967295
	r = x << y
	if r != 0 {
		t.Errorf("1 %s 4294967295 = %d, want 0", "<<", r)
	}
	x = 32766
	y = 0
	r = x << y
	if r != 32766 {
		t.Errorf("32766 %s 0 = %d, want 32766", "<<", r)
	}
	y = 1
	r = x << y
	if r != -4 {
		t.Errorf("32766 %s 1 = %d, want -4", "<<", r)
	}
	y = 4294967295
	r = x << y
	if r != 0 {
		t.Errorf("32766 %s 4294967295 = %d, want 0", "<<", r)
	}
	x = 32767
	y = 0
	r = x << y
	if r != 32767 {
		t.Errorf("32767 %s 0 = %d, want 32767", "<<", r)
	}
	y = 1
	r = x << y
	if r != -2 {
		t.Errorf("32767 %s 1 = %d, want -2", "<<", r)
	}
	y = 4294967295
	r = x << y
	if r != 0 {
		t.Errorf("32767 %s 4294967295 = %d, want 0", "<<", r)
	}
}
func TestConstFoldint16uint32rsh(t *testing.T) {
	var x, r int16
	var y uint32
	x = -32768
	y = 0
	r = x >> y
	if r != -32768 {
		t.Errorf("-32768 %s 0 = %d, want -32768", ">>", r)
	}
	y = 1
	r = x >> y
	if r != -16384 {
		t.Errorf("-32768 %s 1 = %d, want -16384", ">>", r)
	}
	y = 4294967295
	r = x >> y
	if r != -1 {
		t.Errorf("-32768 %s 4294967295 = %d, want -1", ">>", r)
	}
	x = -32767
	y = 0
	r = x >> y
	if r != -32767 {
		t.Errorf("-32767 %s 0 = %d, want -32767", ">>", r)
	}
	y = 1
	r = x >> y
	if r != -16384 {
		t.Errorf("-32767 %s 1 = %d, want -16384", ">>", r)
	}
	y = 4294967295
	r = x >> y
	if r != -1 {
		t.Errorf("-32767 %s 4294967295 = %d, want -1", ">>", r)
	}
	x = -1
	y = 0
	r = x >> y
	if r != -1 {
		t.Errorf("-1 %s 0 = %d, want -1", ">>", r)
	}
	y = 1
	r = x >> y
	if r != -1 {
		t.Errorf("-1 %s 1 = %d, want -1", ">>", r)
	}
	y = 4294967295
	r = x >> y
	if r != -1 {
		t.Errorf("-1 %s 4294967295 = %d, want -1", ">>", r)
	}
	x = 0
	y = 0
	r = x >> y
	if r != 0 {
		t.Errorf("0 %s 0 = %d, want 0", ">>", r)
	}
	y = 1
	r = x >> y
	if r != 0 {
		t.Errorf("0 %s 1 = %d, want 0", ">>", r)
	}
	y = 4294967295
	r = x >> y
	if r != 0 {
		t.Errorf("0 %s 4294967295 = %d, want 0", ">>", r)
	}
	x = 1
	y = 0
	r = x >> y
	if r != 1 {
		t.Errorf("1 %s 0 = %d, want 1", ">>", r)
	}
	y = 1
	r = x >> y
	if r != 0 {
		t.Errorf("1 %s 1 = %d, want 0", ">>", r)
	}
	y = 4294967295
	r = x >> y
	if r != 0 {
		t.Errorf("1 %s 4294967295 = %d, want 0", ">>", r)
	}
	x = 32766
	y = 0
	r = x >> y
	if r != 32766 {
		t.Errorf("32766 %s 0 = %d, want 32766", ">>", r)
	}
	y = 1
	r = x >> y
	if r != 16383 {
		t.Errorf("32766 %s 1 = %d, want 16383", ">>", r)
	}
	y = 4294967295
	r = x >> y
	if r != 0 {
		t.Errorf("32766 %s 4294967295 = %d, want 0", ">>", r)
	}
	x = 32767
	y = 0
	r = x >> y
	if r != 32767 {
		t.Errorf("32767 %s 0 = %d, want 32767", ">>", r)
	}
	y = 1
	r = x >> y
	if r != 16383 {
		t.Errorf("32767 %s 1 = %d, want 16383", ">>", r)
	}
	y = 4294967295
	r = x >> y
	if r != 0 {
		t.Errorf("32767 %s 4294967295 = %d, want 0", ">>", r)
	}
}
func TestConstFoldint16uint16lsh(t *testing.T) {
	var x, r int16
	var y uint16
	x = -32768
	y = 0
	r = x << y
	if r != -32768 {
		t.Errorf("-32768 %s 0 = %d, want -32768", "<<", r)
	}
	y = 1
	r = x << y
	if r != 0 {
		t.Errorf("-32768 %s 1 = %d, want 0", "<<", r)
	}
	y = 65535
	r = x << y
	if r != 0 {
		t.Errorf("-32768 %s 65535 = %d, want 0", "<<", r)
	}
	x = -32767
	y = 0
	r = x << y
	if r != -32767 {
		t.Errorf("-32767 %s 0 = %d, want -32767", "<<", r)
	}
	y = 1
	r = x << y
	if r != 2 {
		t.Errorf("-32767 %s 1 = %d, want 2", "<<", r)
	}
	y = 65535
	r = x << y
	if r != 0 {
		t.Errorf("-32767 %s 65535 = %d, want 0", "<<", r)
	}
	x = -1
	y = 0
	r = x << y
	if r != -1 {
		t.Errorf("-1 %s 0 = %d, want -1", "<<", r)
	}
	y = 1
	r = x << y
	if r != -2 {
		t.Errorf("-1 %s 1 = %d, want -2", "<<", r)
	}
	y = 65535
	r = x << y
	if r != 0 {
		t.Errorf("-1 %s 65535 = %d, want 0", "<<", r)
	}
	x = 0
	y = 0
	r = x << y
	if r != 0 {
		t.Errorf("0 %s 0 = %d, want 0", "<<", r)
	}
	y = 1
	r = x << y
	if r != 0 {
		t.Errorf("0 %s 1 = %d, want 0", "<<", r)
	}
	y = 65535
	r = x << y
	if r != 0 {
		t.Errorf("0 %s 65535 = %d, want 0", "<<", r)
	}
	x = 1
	y = 0
	r = x << y
	if r != 1 {
		t.Errorf("1 %s 0 = %d, want 1", "<<", r)
	}
	y = 1
	r = x << y
	if r != 2 {
		t.Errorf("1 %s 1 = %d, want 2", "<<", r)
	}
	y = 65535
	r = x << y
	if r != 0 {
		t.Errorf("1 %s 65535 = %d, want 0", "<<", r)
	}
	x = 32766
	y = 0
	r = x << y
	if r != 32766 {
		t.Errorf("32766 %s 0 = %d, want 32766", "<<", r)
	}
	y = 1
	r = x << y
	if r != -4 {
		t.Errorf("32766 %s 1 = %d, want -4", "<<", r)
	}
	y = 65535
	r = x << y
	if r != 0 {
		t.Errorf("32766 %s 65535 = %d, want 0", "<<", r)
	}
	x = 32767
	y = 0
	r = x << y
	if r != 32767 {
		t.Errorf("32767 %s 0 = %d, want 32767", "<<", r)
	}
	y = 1
	r = x << y
	if r != -2 {
		t.Errorf("32767 %s 1 = %d, want -2", "<<", r)
	}
	y = 65535
	r = x << y
	if r != 0 {
		t.Errorf("32767 %s 65535 = %d, want 0", "<<", r)
	}
}
func TestConstFoldint16uint16rsh(t *testing.T) {
	var x, r int16
	var y uint16
	x = -32768
	y = 0
	r = x >> y
	if r != -32768 {
		t.Errorf("-32768 %s 0 = %d, want -32768", ">>", r)
	}
	y = 1
	r = x >> y
	if r != -16384 {
		t.Errorf("-32768 %s 1 = %d, want -16384", ">>", r)
	}
	y = 65535
	r = x >> y
	if r != -1 {
		t.Errorf("-32768 %s 65535 = %d, want -1", ">>", r)
	}
	x = -32767
	y = 0
	r = x >> y
	if r != -32767 {
		t.Errorf("-32767 %s 0 = %d, want -32767", ">>", r)
	}
	y = 1
	r = x >> y
	if r != -16384 {
		t.Errorf("-32767 %s 1 = %d, want -16384", ">>", r)
	}
	y = 65535
	r = x >> y
	if r != -1 {
		t.Errorf("-32767 %s 65535 = %d, want -1", ">>", r)
	}
	x = -1
	y = 0
	r = x >> y
	if r != -1 {
		t.Errorf("-1 %s 0 = %d, want -1", ">>", r)
	}
	y = 1
	r = x >> y
	if r != -1 {
		t.Errorf("-1 %s 1 = %d, want -1", ">>", r)
	}
	y = 65535
	r = x >> y
	if r != -1 {
		t.Errorf("-1 %s 65535 = %d, want -1", ">>", r)
	}
	x = 0
	y = 0
	r = x >> y
	if r != 0 {
		t.Errorf("0 %s 0 = %d, want 0", ">>", r)
	}
	y = 1
	r = x >> y
	if r != 0 {
		t.Errorf("0 %s 1 = %d, want 0", ">>", r)
	}
	y = 65535
	r = x >> y
	if r != 0 {
		t.Errorf("0 %s 65535 = %d, want 0", ">>", r)
	}
	x = 1
	y = 0
	r = x >> y
	if r != 1 {
		t.Errorf("1 %s 0 = %d, want 1", ">>", r)
	}
	y = 1
	r = x >> y
	if r != 0 {
		t.Errorf("1 %s 1 = %d, want 0", ">>", r)
	}
	y = 65535
	r = x >> y
	if r != 0 {
		t.Errorf("1 %s 65535 = %d, want 0", ">>", r)
	}
	x = 32766
	y = 0
	r = x >> y
	if r != 32766 {
		t.Errorf("32766 %s 0 = %d, want 32766", ">>", r)
	}
	y = 1
	r = x >> y
	if r != 16383 {
		t.Errorf("32766 %s 1 = %d, want 16383", ">>", r)
	}
	y = 65535
	r = x >> y
	if r != 0 {
		t.Errorf("32766 %s 65535 = %d, want 0", ">>", r)
	}
	x = 32767
	y = 0
	r = x >> y
	if r != 32767 {
		t.Errorf("32767 %s 0 = %d, want 32767", ">>", r)
	}
	y = 1
	r = x >> y
	if r != 16383 {
		t.Errorf("32767 %s 1 = %d, want 16383", ">>", r)
	}
	y = 65535
	r = x >> y
	if r != 0 {
		t.Errorf("32767 %s 65535 = %d, want 0", ">>", r)
	}
}
func TestConstFoldint16uint8lsh(t *testing.T) {
	var x, r int16
	var y uint8
	x = -32768
	y = 0
	r = x << y
	if r != -32768 {
		t.Errorf("-32768 %s 0 = %d, want -32768", "<<", r)
	}
	y = 1
	r = x << y
	if r != 0 {
		t.Errorf("-32768 %s 1 = %d, want 0", "<<", r)
	}
	y = 255
	r = x << y
	if r != 0 {
		t.Errorf("-32768 %s 255 = %d, want 0", "<<", r)
	}
	x = -32767
	y = 0
	r = x << y
	if r != -32767 {
		t.Errorf("-32767 %s 0 = %d, want -32767", "<<", r)
	}
	y = 1
	r = x << y
	if r != 2 {
		t.Errorf("-32767 %s 1 = %d, want 2", "<<", r)
	}
	y = 255
	r = x << y
	if r != 0 {
		t.Errorf("-32767 %s 255 = %d, want 0", "<<", r)
	}
	x = -1
	y = 0
	r = x << y
	if r != -1 {
		t.Errorf("-1 %s 0 = %d, want -1", "<<", r)
	}
	y = 1
	r = x << y
	if r != -2 {
		t.Errorf("-1 %s 1 = %d, want -2", "<<", r)
	}
	y = 255
	r = x << y
	if r != 0 {
		t.Errorf("-1 %s 255 = %d, want 0", "<<", r)
	}
	x = 0
	y = 0
	r = x << y
	if r != 0 {
		t.Errorf("0 %s 0 = %d, want 0", "<<", r)
	}
	y = 1
	r = x << y
	if r != 0 {
		t.Errorf("0 %s 1 = %d, want 0", "<<", r)
	}
	y = 255
	r = x << y
	if r != 0 {
		t.Errorf("0 %s 255 = %d, want 0", "<<", r)
	}
	x = 1
	y = 0
	r = x << y
	if r != 1 {
		t.Errorf("1 %s 0 = %d, want 1", "<<", r)
	}
	y = 1
	r = x << y
	if r != 2 {
		t.Errorf("1 %s 1 = %d, want 2", "<<", r)
	}
	y = 255
	r = x << y
	if r != 0 {
		t.Errorf("1 %s 255 = %d, want 0", "<<", r)
	}
	x = 32766
	y = 0
	r = x << y
	if r != 32766 {
		t.Errorf("32766 %s 0 = %d, want 32766", "<<", r)
	}
	y = 1
	r = x << y
	if r != -4 {
		t.Errorf("32766 %s 1 = %d, want -4", "<<", r)
	}
	y = 255
	r = x << y
	if r != 0 {
		t.Errorf("32766 %s 255 = %d, want 0", "<<", r)
	}
	x = 32767
	y = 0
	r = x << y
	if r != 32767 {
		t.Errorf("32767 %s 0 = %d, want 32767", "<<", r)
	}
	y = 1
	r = x << y
	if r != -2 {
		t.Errorf("32767 %s 1 = %d, want -2", "<<", r)
	}
	y = 255
	r = x << y
	if r != 0 {
		t.Errorf("32767 %s 255 = %d, want 0", "<<", r)
	}
}
func TestConstFoldint16uint8rsh(t *testing.T) {
	var x, r int16
	var y uint8
	x = -32768
	y = 0
	r = x >> y
	if r != -32768 {
		t.Errorf("-32768 %s 0 = %d, want -32768", ">>", r)
	}
	y = 1
	r = x >> y
	if r != -16384 {
		t.Errorf("-32768 %s 1 = %d, want -16384", ">>", r)
	}
	y = 255
	r = x >> y
	if r != -1 {
		t.Errorf("-32768 %s 255 = %d, want -1", ">>", r)
	}
	x = -32767
	y = 0
	r = x >> y
	if r != -32767 {
		t.Errorf("-32767 %s 0 = %d, want -32767", ">>", r)
	}
	y = 1
	r = x >> y
	if r != -16384 {
		t.Errorf("-32767 %s 1 = %d, want -16384", ">>", r)
	}
	y = 255
	r = x >> y
	if r != -1 {
		t.Errorf("-32767 %s 255 = %d, want -1", ">>", r)
	}
	x = -1
	y = 0
	r = x >> y
	if r != -1 {
		t.Errorf("-1 %s 0 = %d, want -1", ">>", r)
	}
	y = 1
	r = x >> y
	if r != -1 {
		t.Errorf("-1 %s 1 = %d, want -1", ">>", r)
	}
	y = 255
	r = x >> y
	if r != -1 {
		t.Errorf("-1 %s 255 = %d, want -1", ">>", r)
	}
	x = 0
	y = 0
	r = x >> y
	if r != 0 {
		t.Errorf("0 %s 0 = %d, want 0", ">>", r)
	}
	y = 1
	r = x >> y
	if r != 0 {
		t.Errorf("0 %s 1 = %d, want 0", ">>", r)
	}
	y = 255
	r = x >> y
	if r != 0 {
		t.Errorf("0 %s 255 = %d, want 0", ">>", r)
	}
	x = 1
	y = 0
	r = x >> y
	if r != 1 {
		t.Errorf("1 %s 0 = %d, want 1", ">>", r)
	}
	y = 1
	r = x >> y
	if r != 0 {
		t.Errorf("1 %s 1 = %d, want 0", ">>", r)
	}
	y = 255
	r = x >> y
	if r != 0 {
		t.Errorf("1 %s 255 = %d, want 0", ">>", r)
	}
	x = 32766
	y = 0
	r = x >> y
	if r != 32766 {
		t.Errorf("32766 %s 0 = %d, want 32766", ">>", r)
	}
	y = 1
	r = x >> y
	if r != 16383 {
		t.Errorf("32766 %s 1 = %d, want 16383", ">>", r)
	}
	y = 255
	r = x >> y
	if r != 0 {
		t.Errorf("32766 %s 255 = %d, want 0", ">>", r)
	}
	x = 32767
	y = 0
	r = x >> y
	if r != 32767 {
		t.Errorf("32767 %s 0 = %d, want 32767", ">>", r)
	}
	y = 1
	r = x >> y
	if r != 16383 {
		t.Errorf("32767 %s 1 = %d, want 16383", ">>", r)
	}
	y = 255
	r = x >> y
	if r != 0 {
		t.Errorf("32767 %s 255 = %d, want 0", ">>", r)
	}
}
func TestConstFolduint8uint64lsh(t *testing.T) {
	var x, r uint8
	var y uint64
	x = 0
	y = 0
	r = x << y
	if r != 0 {
		t.Errorf("0 %s 0 = %d, want 0", "<<", r)
	}
	y = 1
	r = x << y
	if r != 0 {
		t.Errorf("0 %s 1 = %d, want 0", "<<", r)
	}
	y = 4294967296
	r = x << y
	if r != 0 {
		t.Errorf("0 %s 4294967296 = %d, want 0", "<<", r)
	}
	y = 18446744073709551615
	r = x << y
	if r != 0 {
		t.Errorf("0 %s 18446744073709551615 = %d, want 0", "<<", r)
	}
	x = 1
	y = 0
	r = x << y
	if r != 1 {
		t.Errorf("1 %s 0 = %d, want 1", "<<", r)
	}
	y = 1
	r = x << y
	if r != 2 {
		t.Errorf("1 %s 1 = %d, want 2", "<<", r)
	}
	y = 4294967296
	r = x << y
	if r != 0 {
		t.Errorf("1 %s 4294967296 = %d, want 0", "<<", r)
	}
	y = 18446744073709551615
	r = x << y
	if r != 0 {
		t.Errorf("1 %s 18446744073709551615 = %d, want 0", "<<", r)
	}
	x = 255
	y = 0
	r = x << y
	if r != 255 {
		t.Errorf("255 %s 0 = %d, want 255", "<<", r)
	}
	y = 1
	r = x << y
	if r != 254 {
		t.Errorf("255 %s 1 = %d, want 254", "<<", r)
	}
	y = 4294967296
	r = x << y
	if r != 0 {
		t.Errorf("255 %s 4294967296 = %d, want 0", "<<", r)
	}
	y = 18446744073709551615
	r = x << y
	if r != 0 {
		t.Errorf("255 %s 18446744073709551615 = %d, want 0", "<<", r)
	}
}
func TestConstFolduint8uint64rsh(t *testing.T) {
	var x, r uint8
	var y uint64
	x = 0
	y = 0
	r = x >> y
	if r != 0 {
		t.Errorf("0 %s 0 = %d, want 0", ">>", r)
	}
	y = 1
	r = x >> y
	if r != 0 {
		t.Errorf("0 %s 1 = %d, want 0", ">>", r)
	}
	y = 4294967296
	r = x >> y
	if r != 0 {
		t.Errorf("0 %s 4294967296 = %d, want 0", ">>", r)
	}
	y = 18446744073709551615
	r = x >> y
	if r != 0 {
		t.Errorf("0 %s 18446744073709551615 = %d, want 0", ">>", r)
	}
	x = 1
	y = 0
	r = x >> y
	if r != 1 {
		t.Errorf("1 %s 0 = %d, want 1", ">>", r)
	}
	y = 1
	r = x >> y
	if r != 0 {
		t.Errorf("1 %s 1 = %d, want 0", ">>", r)
	}
	y = 4294967296
	r = x >> y
	if r != 0 {
		t.Errorf("1 %s 4294967296 = %d, want 0", ">>", r)
	}
	y = 18446744073709551615
	r = x >> y
	if r != 0 {
		t.Errorf("1 %s 18446744073709551615 = %d, want 0", ">>", r)
	}
	x = 255
	y = 0
	r = x >> y
	if r != 255 {
		t.Errorf("255 %s 0 = %d, want 255", ">>", r)
	}
	y = 1
	r = x >> y
	if r != 127 {
		t.Errorf("255 %s 1 = %d, want 127", ">>", r)
	}
	y = 4294967296
	r = x >> y
	if r != 0 {
		t.Errorf("255 %s 4294967296 = %d, want 0", ">>", r)
	}
	y = 18446744073709551615
	r = x >> y
	if r != 0 {
		t.Errorf("255 %s 18446744073709551615 = %d, want 0", ">>", r)
	}
}
func TestConstFolduint8uint32lsh(t *testing.T) {
	var x, r uint8
	var y uint32
	x = 0
	y = 0
	r = x << y
	if r != 0 {
		t.Errorf("0 %s 0 = %d, want 0", "<<", r)
	}
	y = 1
	r = x << y
	if r != 0 {
		t.Errorf("0 %s 1 = %d, want 0", "<<", r)
	}
	y = 4294967295
	r = x << y
	if r != 0 {
		t.Errorf("0 %s 4294967295 = %d, want 0", "<<", r)
	}
	x = 1
	y = 0
	r = x << y
	if r != 1 {
		t.Errorf("1 %s 0 = %d, want 1", "<<", r)
	}
	y = 1
	r = x << y
	if r != 2 {
		t.Errorf("1 %s 1 = %d, want 2", "<<", r)
	}
	y = 4294967295
	r = x << y
	if r != 0 {
		t.Errorf("1 %s 4294967295 = %d, want 0", "<<", r)
	}
	x = 255
	y = 0
	r = x << y
	if r != 255 {
		t.Errorf("255 %s 0 = %d, want 255", "<<", r)
	}
	y = 1
	r = x << y
	if r != 254 {
		t.Errorf("255 %s 1 = %d, want 254", "<<", r)
	}
	y = 4294967295
	r = x << y
	if r != 0 {
		t.Errorf("255 %s 4294967295 = %d, want 0", "<<", r)
	}
}
func TestConstFolduint8uint32rsh(t *testing.T) {
	var x, r uint8
	var y uint32
	x = 0
	y = 0
	r = x >> y
	if r != 0 {
		t.Errorf("0 %s 0 = %d, want 0", ">>", r)
	}
	y = 1
	r = x >> y
	if r != 0 {
		t.Errorf("0 %s 1 = %d, want 0", ">>", r)
	}
	y = 4294967295
	r = x >> y
	if r != 0 {
		t.Errorf("0 %s 4294967295 = %d, want 0", ">>", r)
	}
	x = 1
	y = 0
	r = x >> y
	if r != 1 {
		t.Errorf("1 %s 0 = %d, want 1", ">>", r)
	}
	y = 1
	r = x >> y
	if r != 0 {
		t.Errorf("1 %s 1 = %d, want 0", ">>", r)
	}
	y = 4294967295
	r = x >> y
	if r != 0 {
		t.Errorf("1 %s 4294967295 = %d, want 0", ">>", r)
	}
	x = 255
	y = 0
	r = x >> y
	if r != 255 {
		t.Errorf("255 %s 0 = %d, want 255", ">>", r)
	}
	y = 1
	r = x >> y
	if r != 127 {
		t.Errorf("255 %s 1 = %d, want 127", ">>", r)
	}
	y = 4294967295
	r = x >> y
	if r != 0 {
		t.Errorf("255 %s 4294967295 = %d, want 0", ">>", r)
	}
}
func TestConstFolduint8uint16lsh(t *testing.T) {
	var x, r uint8
	var y uint16
	x = 0
	y = 0
	r = x << y
	if r != 0 {
		t.Errorf("0 %s 0 = %d, want 0", "<<", r)
	}
	y = 1
	r = x << y
	if r != 0 {
		t.Errorf("0 %s 1 = %d, want 0", "<<", r)
	}
	y = 65535
	r = x << y
	if r != 0 {
		t.Errorf("0 %s 65535 = %d, want 0", "<<", r)
	}
	x = 1
	y = 0
	r = x << y
	if r != 1 {
		t.Errorf("1 %s 0 = %d, want 1", "<<", r)
	}
	y = 1
	r = x << y
	if r != 2 {
		t.Errorf("1 %s 1 = %d, want 2", "<<", r)
	}
	y = 65535
	r = x << y
	if r != 0 {
		t.Errorf("1 %s 65535 = %d, want 0", "<<", r)
	}
	x = 255
	y = 0
	r = x << y
	if r != 255 {
		t.Errorf("255 %s 0 = %d, want 255", "<<", r)
	}
	y = 1
	r = x << y
	if r != 254 {
		t.Errorf("255 %s 1 = %d, want 254", "<<", r)
	}
	y = 65535
	r = x << y
	if r != 0 {
		t.Errorf("255 %s 65535 = %d, want 0", "<<", r)
	}
}
func TestConstFolduint8uint16rsh(t *testing.T) {
	var x, r uint8
	var y uint16
	x = 0
	y = 0
	r = x >> y
	if r != 0 {
		t.Errorf("0 %s 0 = %d, want 0", ">>", r)
	}
	y = 1
	r = x >> y
	if r != 0 {
		t.Errorf("0 %s 1 = %d, want 0", ">>", r)
	}
	y = 65535
	r = x >> y
	if r != 0 {
		t.Errorf("0 %s 65535 = %d, want 0", ">>", r)
	}
	x = 1
	y = 0
	r = x >> y
	if r != 1 {
		t.Errorf("1 %s 0 = %d, want 1", ">>", r)
	}
	y = 1
	r = x >> y
	if r != 0 {
		t.Errorf("1 %s 1 = %d, want 0", ">>", r)
	}
	y = 65535
	r = x >> y
	if r != 0 {
		t.Errorf("1 %s 65535 = %d, want 0", ">>", r)
	}
	x = 255
	y = 0
	r = x >> y
	if r != 255 {
		t.Errorf("255 %s 0 = %d, want 255", ">>", r)
	}
	y = 1
	r = x >> y
	if r != 127 {
		t.Errorf("255 %s 1 = %d, want 127", ">>", r)
	}
	y = 65535
	r = x >> y
	if r != 0 {
		t.Errorf("255 %s 65535 = %d, want 0", ">>", r)
	}
}
func TestConstFolduint8uint8lsh(t *testing.T) {
	var x, r uint8
	var y uint8
	x = 0
	y = 0
	r = x << y
	if r != 0 {
		t.Errorf("0 %s 0 = %d, want 0", "<<", r)
	}
	y = 1
	r = x << y
	if r != 0 {
		t.Errorf("0 %s 1 = %d, want 0", "<<", r)
	}
	y = 255
	r = x << y
	if r != 0 {
		t.Errorf("0 %s 255 = %d, want 0", "<<", r)
	}
	x = 1
	y = 0
	r = x << y
	if r != 1 {
		t.Errorf("1 %s 0 = %d, want 1", "<<", r)
	}
	y = 1
	r = x << y
	if r != 2 {
		t.Errorf("1 %s 1 = %d, want 2", "<<", r)
	}
	y = 255
	r = x << y
	if r != 0 {
		t.Errorf("1 %s 255 = %d, want 0", "<<", r)
	}
	x = 255
	y = 0
	r = x << y
	if r != 255 {
		t.Errorf("255 %s 0 = %d, want 255", "<<", r)
	}
	y = 1
	r = x << y
	if r != 254 {
		t.Errorf("255 %s 1 = %d, want 254", "<<", r)
	}
	y = 255
	r = x << y
	if r != 0 {
		t.Errorf("255 %s 255 = %d, want 0", "<<", r)
	}
}
func TestConstFolduint8uint8rsh(t *testing.T) {
	var x, r uint8
	var y uint8
	x = 0
	y = 0
	r = x >> y
	if r != 0 {
		t.Errorf("0 %s 0 = %d, want 0", ">>", r)
	}
	y = 1
	r = x >> y
	if r != 0 {
		t.Errorf("0 %s 1 = %d, want 0", ">>", r)
	}
	y = 255
	r = x >> y
	if r != 0 {
		t.Errorf("0 %s 255 = %d, want 0", ">>", r)
	}
	x = 1
	y = 0
	r = x >> y
	if r != 1 {
		t.Errorf("1 %s 0 = %d, want 1", ">>", r)
	}
	y = 1
	r = x >> y
	if r != 0 {
		t.Errorf("1 %s 1 = %d, want 0", ">>", r)
	}
	y = 255
	r = x >> y
	if r != 0 {
		t.Errorf("1 %s 255 = %d, want 0", ">>", r)
	}
	x = 255
	y = 0
	r = x >> y
	if r != 255 {
		t.Errorf("255 %s 0 = %d, want 255", ">>", r)
	}
	y = 1
	r = x >> y
	if r != 127 {
		t.Errorf("255 %s 1 = %d, want 127", ">>", r)
	}
	y = 255
	r = x >> y
	if r != 0 {
		t.Errorf("255 %s 255 = %d, want 0", ">>", r)
	}
}
func TestConstFoldint8uint64lsh(t *testing.T) {
	var x, r int8
	var y uint64
	x = -128
	y = 0
	r = x << y
	if r != -128 {
		t.Errorf("-128 %s 0 = %d, want -128", "<<", r)
	}
	y = 1
	r = x << y
	if r != 0 {
		t.Errorf("-128 %s 1 = %d, want 0", "<<", r)
	}
	y = 4294967296
	r = x << y
	if r != 0 {
		t.Errorf("-128 %s 4294967296 = %d, want 0", "<<", r)
	}
	y = 18446744073709551615
	r = x << y
	if r != 0 {
		t.Errorf("-128 %s 18446744073709551615 = %d, want 0", "<<", r)
	}
	x = -127
	y = 0
	r = x << y
	if r != -127 {
		t.Errorf("-127 %s 0 = %d, want -127", "<<", r)
	}
	y = 1
	r = x << y
	if r != 2 {
		t.Errorf("-127 %s 1 = %d, want 2", "<<", r)
	}
	y = 4294967296
	r = x << y
	if r != 0 {
		t.Errorf("-127 %s 4294967296 = %d, want 0", "<<", r)
	}
	y = 18446744073709551615
	r = x << y
	if r != 0 {
		t.Errorf("-127 %s 18446744073709551615 = %d, want 0", "<<", r)
	}
	x = -1
	y = 0
	r = x << y
	if r != -1 {
		t.Errorf("-1 %s 0 = %d, want -1", "<<", r)
	}
	y = 1
	r = x << y
	if r != -2 {
		t.Errorf("-1 %s 1 = %d, want -2", "<<", r)
	}
	y = 4294967296
	r = x << y
	if r != 0 {
		t.Errorf("-1 %s 4294967296 = %d, want 0", "<<", r)
	}
	y = 18446744073709551615
	r = x << y
	if r != 0 {
		t.Errorf("-1 %s 18446744073709551615 = %d, want 0", "<<", r)
	}
	x = 0
	y = 0
	r = x << y
	if r != 0 {
		t.Errorf("0 %s 0 = %d, want 0", "<<", r)
	}
	y = 1
	r = x << y
	if r != 0 {
		t.Errorf("0 %s 1 = %d, want 0", "<<", r)
	}
	y = 4294967296
	r = x << y
	if r != 0 {
		t.Errorf("0 %s 4294967296 = %d, want 0", "<<", r)
	}
	y = 18446744073709551615
	r = x << y
	if r != 0 {
		t.Errorf("0 %s 18446744073709551615 = %d, want 0", "<<", r)
	}
	x = 1
	y = 0
	r = x << y
	if r != 1 {
		t.Errorf("1 %s 0 = %d, want 1", "<<", r)
	}
	y = 1
	r = x << y
	if r != 2 {
		t.Errorf("1 %s 1 = %d, want 2", "<<", r)
	}
	y = 4294967296
	r = x << y
	if r != 0 {
		t.Errorf("1 %s 4294967296 = %d, want 0", "<<", r)
	}
	y = 18446744073709551615
	r = x << y
	if r != 0 {
		t.Errorf("1 %s 18446744073709551615 = %d, want 0", "<<", r)
	}
	x = 126
	y = 0
	r = x << y
	if r != 126 {
		t.Errorf("126 %s 0 = %d, want 126", "<<", r)
	}
	y = 1
	r = x << y
	if r != -4 {
		t.Errorf("126 %s 1 = %d, want -4", "<<", r)
	}
	y = 4294967296
	r = x << y
	if r != 0 {
		t.Errorf("126 %s 4294967296 = %d, want 0", "<<", r)
	}
	y = 18446744073709551615
	r = x << y
	if r != 0 {
		t.Errorf("126 %s 18446744073709551615 = %d, want 0", "<<", r)
	}
	x = 127
	y = 0
	r = x << y
	if r != 127 {
		t.Errorf("127 %s 0 = %d, want 127", "<<", r)
	}
	y = 1
	r = x << y
	if r != -2 {
		t.Errorf("127 %s 1 = %d, want -2", "<<", r)
	}
	y = 4294967296
	r = x << y
	if r != 0 {
		t.Errorf("127 %s 4294967296 = %d, want 0", "<<", r)
	}
	y = 18446744073709551615
	r = x << y
	if r != 0 {
		t.Errorf("127 %s 18446744073709551615 = %d, want 0", "<<", r)
	}
}
func TestConstFoldint8uint64rsh(t *testing.T) {
	var x, r int8
	var y uint64
	x = -128
	y = 0
	r = x >> y
	if r != -128 {
		t.Errorf("-128 %s 0 = %d, want -128", ">>", r)
	}
	y = 1
	r = x >> y
	if r != -64 {
		t.Errorf("-128 %s 1 = %d, want -64", ">>", r)
	}
	y = 4294967296
	r = x >> y
	if r != -1 {
		t.Errorf("-128 %s 4294967296 = %d, want -1", ">>", r)
	}
	y = 18446744073709551615
	r = x >> y
	if r != -1 {
		t.Errorf("-128 %s 18446744073709551615 = %d, want -1", ">>", r)
	}
	x = -127
	y = 0
	r = x >> y
	if r != -127 {
		t.Errorf("-127 %s 0 = %d, want -127", ">>", r)
	}
	y = 1
	r = x >> y
	if r != -64 {
		t.Errorf("-127 %s 1 = %d, want -64", ">>", r)
	}
	y = 4294967296
	r = x >> y
	if r != -1 {
		t.Errorf("-127 %s 4294967296 = %d, want -1", ">>", r)
	}
	y = 18446744073709551615
	r = x >> y
	if r != -1 {
		t.Errorf("-127 %s 18446744073709551615 = %d, want -1", ">>", r)
	}
	x = -1
	y = 0
	r = x >> y
	if r != -1 {
		t.Errorf("-1 %s 0 = %d, want -1", ">>", r)
	}
	y = 1
	r = x >> y
	if r != -1 {
		t.Errorf("-1 %s 1 = %d, want -1", ">>", r)
	}
	y = 4294967296
	r = x >> y
	if r != -1 {
		t.Errorf("-1 %s 4294967296 = %d, want -1", ">>", r)
	}
	y = 18446744073709551615
	r = x >> y
	if r != -1 {
		t.Errorf("-1 %s 18446744073709551615 = %d, want -1", ">>", r)
	}
	x = 0
	y = 0
	r = x >> y
	if r != 0 {
		t.Errorf("0 %s 0 = %d, want 0", ">>", r)
	}
	y = 1
	r = x >> y
	if r != 0 {
		t.Errorf("0 %s 1 = %d, want 0", ">>", r)
	}
	y = 4294967296
	r = x >> y
	if r != 0 {
		t.Errorf("0 %s 4294967296 = %d, want 0", ">>", r)
	}
	y = 18446744073709551615
	r = x >> y
	if r != 0 {
		t.Errorf("0 %s 18446744073709551615 = %d, want 0", ">>", r)
	}
	x = 1
	y = 0
	r = x >> y
	if r != 1 {
		t.Errorf("1 %s 0 = %d, want 1", ">>", r)
	}
	y = 1
	r = x >> y
	if r != 0 {
		t.Errorf("1 %s 1 = %d, want 0", ">>", r)
	}
	y = 4294967296
	r = x >> y
	if r != 0 {
		t.Errorf("1 %s 4294967296 = %d, want 0", ">>", r)
	}
	y = 18446744073709551615
	r = x >> y
	if r != 0 {
		t.Errorf("1 %s 18446744073709551615 = %d, want 0", ">>", r)
	}
	x = 126
	y = 0
	r = x >> y
	if r != 126 {
		t.Errorf("126 %s 0 = %d, want 126", ">>", r)
	}
	y = 1
	r = x >> y
	if r != 63 {
		t.Errorf("126 %s 1 = %d, want 63", ">>", r)
	}
	y = 4294967296
	r = x >> y
	if r != 0 {
		t.Errorf("126 %s 4294967296 = %d, want 0", ">>", r)
	}
	y = 18446744073709551615
	r = x >> y
	if r != 0 {
		t.Errorf("126 %s 18446744073709551615 = %d, want 0", ">>", r)
	}
	x = 127
	y = 0
	r = x >> y
	if r != 127 {
		t.Errorf("127 %s 0 = %d, want 127", ">>", r)
	}
	y = 1
	r = x >> y
	if r != 63 {
		t.Errorf("127 %s 1 = %d, want 63", ">>", r)
	}
	y = 4294967296
	r = x >> y
	if r != 0 {
		t.Errorf("127 %s 4294967296 = %d, want 0", ">>", r)
	}
	y = 18446744073709551615
	r = x >> y
	if r != 0 {
		t.Errorf("127 %s 18446744073709551615 = %d, want 0", ">>", r)
	}
}
func TestConstFoldint8uint32lsh(t *testing.T) {
	var x, r int8
	var y uint32
	x = -128
	y = 0
	r = x << y
	if r != -128 {
		t.Errorf("-128 %s 0 = %d, want -128", "<<", r)
	}
	y = 1
	r = x << y
	if r != 0 {
		t.Errorf("-128 %s 1 = %d, want 0", "<<", r)
	}
	y = 4294967295
	r = x << y
	if r != 0 {
		t.Errorf("-128 %s 4294967295 = %d, want 0", "<<", r)
	}
	x = -127
	y = 0
	r = x << y
	if r != -127 {
		t.Errorf("-127 %s 0 = %d, want -127", "<<", r)
	}
	y = 1
	r = x << y
	if r != 2 {
		t.Errorf("-127 %s 1 = %d, want 2", "<<", r)
	}
	y = 4294967295
	r = x << y
	if r != 0 {
		t.Errorf("-127 %s 4294967295 = %d, want 0", "<<", r)
	}
	x = -1
	y = 0
	r = x << y
	if r != -1 {
		t.Errorf("-1 %s 0 = %d, want -1", "<<", r)
	}
	y = 1
	r = x << y
	if r != -2 {
		t.Errorf("-1 %s 1 = %d, want -2", "<<", r)
	}
	y = 4294967295
	r = x << y
	if r != 0 {
		t.Errorf("-1 %s 4294967295 = %d, want 0", "<<", r)
	}
	x = 0
	y = 0
	r = x << y
	if r != 0 {
		t.Errorf("0 %s 0 = %d, want 0", "<<", r)
	}
	y = 1
	r = x << y
	if r != 0 {
		t.Errorf("0 %s 1 = %d, want 0", "<<", r)
	}
	y = 4294967295
	r = x << y
	if r != 0 {
		t.Errorf("0 %s 4294967295 = %d, want 0", "<<", r)
	}
	x = 1
	y = 0
	r = x << y
	if r != 1 {
		t.Errorf("1 %s 0 = %d, want 1", "<<", r)
	}
	y = 1
	r = x << y
	if r != 2 {
		t.Errorf("1 %s 1 = %d, want 2", "<<", r)
	}
	y = 4294967295
	r = x << y
	if r != 0 {
		t.Errorf("1 %s 4294967295 = %d, want 0", "<<", r)
	}
	x = 126
	y = 0
	r = x << y
	if r != 126 {
		t.Errorf("126 %s 0 = %d, want 126", "<<", r)
	}
	y = 1
	r = x << y
	if r != -4 {
		t.Errorf("126 %s 1 = %d, want -4", "<<", r)
	}
	y = 4294967295
	r = x << y
	if r != 0 {
		t.Errorf("126 %s 4294967295 = %d, want 0", "<<", r)
	}
	x = 127
	y = 0
	r = x << y
	if r != 127 {
		t.Errorf("127 %s 0 = %d, want 127", "<<", r)
	}
	y = 1
	r = x << y
	if r != -2 {
		t.Errorf("127 %s 1 = %d, want -2", "<<", r)
	}
	y = 4294967295
	r = x << y
	if r != 0 {
		t.Errorf("127 %s 4294967295 = %d, want 0", "<<", r)
	}
}
func TestConstFoldint8uint32rsh(t *testing.T) {
	var x, r int8
	var y uint32
	x = -128
	y = 0
	r = x >> y
	if r != -128 {
		t.Errorf("-128 %s 0 = %d, want -128", ">>", r)
	}
	y = 1
	r = x >> y
	if r != -64 {
		t.Errorf("-128 %s 1 = %d, want -64", ">>", r)
	}
	y = 4294967295
	r = x >> y
	if r != -1 {
		t.Errorf("-128 %s 4294967295 = %d, want -1", ">>", r)
	}
	x = -127
	y = 0
	r = x >> y
	if r != -127 {
		t.Errorf("-127 %s 0 = %d, want -127", ">>", r)
	}
	y = 1
	r = x >> y
	if r != -64 {
		t.Errorf("-127 %s 1 = %d, want -64", ">>", r)
	}
	y = 4294967295
	r = x >> y
	if r != -1 {
		t.Errorf("-127 %s 4294967295 = %d, want -1", ">>", r)
	}
	x = -1
	y = 0
	r = x >> y
	if r != -1 {
		t.Errorf("-1 %s 0 = %d, want -1", ">>", r)
	}
	y = 1
	r = x >> y
	if r != -1 {
		t.Errorf("-1 %s 1 = %d, want -1", ">>", r)
	}
	y = 4294967295
	r = x >> y
	if r != -1 {
		t.Errorf("-1 %s 4294967295 = %d, want -1", ">>", r)
	}
	x = 0
	y = 0
	r = x >> y
	if r != 0 {
		t.Errorf("0 %s 0 = %d, want 0", ">>", r)
	}
	y = 1
	r = x >> y
	if r != 0 {
		t.Errorf("0 %s 1 = %d, want 0", ">>", r)
	}
	y = 4294967295
	r = x >> y
	if r != 0 {
		t.Errorf("0 %s 4294967295 = %d, want 0", ">>", r)
	}
	x = 1
	y = 0
	r = x >> y
	if r != 1 {
		t.Errorf("1 %s 0 = %d, want 1", ">>", r)
	}
	y = 1
	r = x >> y
	if r != 0 {
		t.Errorf("1 %s 1 = %d, want 0", ">>", r)
	}
	y = 4294967295
	r = x >> y
	if r != 0 {
		t.Errorf("1 %s 4294967295 = %d, want 0", ">>", r)
	}
	x = 126
	y = 0
	r = x >> y
	if r != 126 {
		t.Errorf("126 %s 0 = %d, want 126", ">>", r)
	}
	y = 1
	r = x >> y
	if r != 63 {
		t.Errorf("126 %s 1 = %d, want 63", ">>", r)
	}
	y = 4294967295
	r = x >> y
	if r != 0 {
		t.Errorf("126 %s 4294967295 = %d, want 0", ">>", r)
	}
	x = 127
	y = 0
	r = x >> y
	if r != 127 {
		t.Errorf("127 %s 0 = %d, want 127", ">>", r)
	}
	y = 1
	r = x >> y
	if r != 63 {
		t.Errorf("127 %s 1 = %d, want 63", ">>", r)
	}
	y = 4294967295
	r = x >> y
	if r != 0 {
		t.Errorf("127 %s 4294967295 = %d, want 0", ">>", r)
	}
}
func TestConstFoldint8uint16lsh(t *testing.T) {
	var x, r int8
	var y uint16
	x = -128
	y = 0
	r = x << y
	if r != -128 {
		t.Errorf("-128 %s 0 = %d, want -128", "<<", r)
	}
	y = 1
	r = x << y
	if r != 0 {
		t.Errorf("-128 %s 1 = %d, want 0", "<<", r)
	}
	y = 65535
	r = x << y
	if r != 0 {
		t.Errorf("-128 %s 65535 = %d, want 0", "<<", r)
	}
	x = -127
	y = 0
	r = x << y
	if r != -127 {
		t.Errorf("-127 %s 0 = %d, want -127", "<<", r)
	}
	y = 1
	r = x << y
	if r != 2 {
		t.Errorf("-127 %s 1 = %d, want 2", "<<", r)
	}
	y = 65535
	r = x << y
	if r != 0 {
		t.Errorf("-127 %s 65535 = %d, want 0", "<<", r)
	}
	x = -1
	y = 0
	r = x << y
	if r != -1 {
		t.Errorf("-1 %s 0 = %d, want -1", "<<", r)
	}
	y = 1
	r = x << y
	if r != -2 {
		t.Errorf("-1 %s 1 = %d, want -2", "<<", r)
	}
	y = 65535
	r = x << y
	if r != 0 {
		t.Errorf("-1 %s 65535 = %d, want 0", "<<", r)
	}
	x = 0
	y = 0
	r = x << y
	if r != 0 {
		t.Errorf("0 %s 0 = %d, want 0", "<<", r)
	}
	y = 1
	r = x << y
	if r != 0 {
		t.Errorf("0 %s 1 = %d, want 0", "<<", r)
	}
	y = 65535
	r = x << y
	if r != 0 {
		t.Errorf("0 %s 65535 = %d, want 0", "<<", r)
	}
	x = 1
	y = 0
	r = x << y
	if r != 1 {
		t.Errorf("1 %s 0 = %d, want 1", "<<", r)
	}
	y = 1
	r = x << y
	if r != 2 {
		t.Errorf("1 %s 1 = %d, want 2", "<<", r)
	}
	y = 65535
	r = x << y
	if r != 0 {
		t.Errorf("1 %s 65535 = %d, want 0", "<<", r)
	}
	x = 126
	y = 0
	r = x << y
	if r != 126 {
		t.Errorf("126 %s 0 = %d, want 126", "<<", r)
	}
	y = 1
	r = x << y
	if r != -4 {
		t.Errorf("126 %s 1 = %d, want -4", "<<", r)
	}
	y = 65535
	r = x << y
	if r != 0 {
		t.Errorf("126 %s 65535 = %d, want 0", "<<", r)
	}
	x = 127
	y = 0
	r = x << y
	if r != 127 {
		t.Errorf("127 %s 0 = %d, want 127", "<<", r)
	}
	y = 1
	r = x << y
	if r != -2 {
		t.Errorf("127 %s 1 = %d, want -2", "<<", r)
	}
	y = 65535
	r = x << y
	if r != 0 {
		t.Errorf("127 %s 65535 = %d, want 0", "<<", r)
	}
}
func TestConstFoldint8uint16rsh(t *testing.T) {
	var x, r int8
	var y uint16
	x = -128
	y = 0
	r = x >> y
	if r != -128 {
		t.Errorf("-128 %s 0 = %d, want -128", ">>", r)
	}
	y = 1
	r = x >> y
	if r != -64 {
		t.Errorf("-128 %s 1 = %d, want -64", ">>", r)
	}
	y = 65535
	r = x >> y
	if r != -1 {
		t.Errorf("-128 %s 65535 = %d, want -1", ">>", r)
	}
	x = -127
	y = 0
	r = x >> y
	if r != -127 {
		t.Errorf("-127 %s 0 = %d, want -127", ">>", r)
	}
	y = 1
	r = x >> y
	if r != -64 {
		t.Errorf("-127 %s 1 = %d, want -64", ">>", r)
	}
	y = 65535
	r = x >> y
	if r != -1 {
		t.Errorf("-127 %s 65535 = %d, want -1", ">>", r)
	}
	x = -1
	y = 0
	r = x >> y
	if r != -1 {
		t.Errorf("-1 %s 0 = %d, want -1", ">>", r)
	}
	y = 1
	r = x >> y
	if r != -1 {
		t.Errorf("-1 %s 1 = %d, want -1", ">>", r)
	}
	y = 65535
	r = x >> y
	if r != -1 {
		t.Errorf("-1 %s 65535 = %d, want -1", ">>", r)
	}
	x = 0
	y = 0
	r = x >> y
	if r != 0 {
		t.Errorf("0 %s 0 = %d, want 0", ">>", r)
	}
	y = 1
	r = x >> y
	if r != 0 {
		t.Errorf("0 %s 1 = %d, want 0", ">>", r)
	}
	y = 65535
	r = x >> y
	if r != 0 {
		t.Errorf("0 %s 65535 = %d, want 0", ">>", r)
	}
	x = 1
	y = 0
	r = x >> y
	if r != 1 {
		t.Errorf("1 %s 0 = %d, want 1", ">>", r)
	}
	y = 1
	r = x >> y
	if r != 0 {
		t.Errorf("1 %s 1 = %d, want 0", ">>", r)
	}
	y = 65535
	r = x >> y
	if r != 0 {
		t.Errorf("1 %s 65535 = %d, want 0", ">>", r)
	}
	x = 126
	y = 0
	r = x >> y
	if r != 126 {
		t.Errorf("126 %s 0 = %d, want 126", ">>", r)
	}
	y = 1
	r = x >> y
	if r != 63 {
		t.Errorf("126 %s 1 = %d, want 63", ">>", r)
	}
	y = 65535
	r = x >> y
	if r != 0 {
		t.Errorf("126 %s 65535 = %d, want 0", ">>", r)
	}
	x = 127
	y = 0
	r = x >> y
	if r != 127 {
		t.Errorf("127 %s 0 = %d, want 127", ">>", r)
	}
	y = 1
	r = x >> y
	if r != 63 {
		t.Errorf("127 %s 1 = %d, want 63", ">>", r)
	}
	y = 65535
	r = x >> y
	if r != 0 {
		t.Errorf("127 %s 65535 = %d, want 0", ">>", r)
	}
}
func TestConstFoldint8uint8lsh(t *testing.T) {
	var x, r int8
	var y uint8
	x = -128
	y = 0
	r = x << y
	if r != -128 {
		t.Errorf("-128 %s 0 = %d, want -128", "<<", r)
	}
	y = 1
	r = x << y
	if r != 0 {
		t.Errorf("-128 %s 1 = %d, want 0", "<<", r)
	}
	y = 255
	r = x << y
	if r != 0 {
		t.Errorf("-128 %s 255 = %d, want 0", "<<", r)
	}
	x = -127
	y = 0
	r = x << y
	if r != -127 {
		t.Errorf("-127 %s 0 = %d, want -127", "<<", r)
	}
	y = 1
	r = x << y
	if r != 2 {
		t.Errorf("-127 %s 1 = %d, want 2", "<<", r)
	}
	y = 255
	r = x << y
	if r != 0 {
		t.Errorf("-127 %s 255 = %d, want 0", "<<", r)
	}
	x = -1
	y = 0
	r = x << y
	if r != -1 {
		t.Errorf("-1 %s 0 = %d, want -1", "<<", r)
	}
	y = 1
	r = x << y
	if r != -2 {
		t.Errorf("-1 %s 1 = %d, want -2", "<<", r)
	}
	y = 255
	r = x << y
	if r != 0 {
		t.Errorf("-1 %s 255 = %d, want 0", "<<", r)
	}
	x = 0
	y = 0
	r = x << y
	if r != 0 {
		t.Errorf("0 %s 0 = %d, want 0", "<<", r)
	}
	y = 1
	r = x << y
	if r != 0 {
		t.Errorf("0 %s 1 = %d, want 0", "<<", r)
	}
	y = 255
	r = x << y
	if r != 0 {
		t.Errorf("0 %s 255 = %d, want 0", "<<", r)
	}
	x = 1
	y = 0
	r = x << y
	if r != 1 {
		t.Errorf("1 %s 0 = %d, want 1", "<<", r)
	}
	y = 1
	r = x << y
	if r != 2 {
		t.Errorf("1 %s 1 = %d, want 2", "<<", r)
	}
	y = 255
	r = x << y
	if r != 0 {
		t.Errorf("1 %s 255 = %d, want 0", "<<", r)
	}
	x = 126
	y = 0
	r = x << y
	if r != 126 {
		t.Errorf("126 %s 0 = %d, want 126", "<<", r)
	}
	y = 1
	r = x << y
	if r != -4 {
		t.Errorf("126 %s 1 = %d, want -4", "<<", r)
	}
	y = 255
	r = x << y
	if r != 0 {
		t.Errorf("126 %s 255 = %d, want 0", "<<", r)
	}
	x = 127
	y = 0
	r = x << y
	if r != 127 {
		t.Errorf("127 %s 0 = %d, want 127", "<<", r)
	}
	y = 1
	r = x << y
	if r != -2 {
		t.Errorf("127 %s 1 = %d, want -2", "<<", r)
	}
	y = 255
	r = x << y
	if r != 0 {
		t.Errorf("127 %s 255 = %d, want 0", "<<", r)
	}
}
func TestConstFoldint8uint8rsh(t *testing.T) {
	var x, r int8
	var y uint8
	x = -128
	y = 0
	r = x >> y
	if r != -128 {
		t.Errorf("-128 %s 0 = %d, want -128", ">>", r)
	}
	y = 1
	r = x >> y
	if r != -64 {
		t.Errorf("-128 %s 1 = %d, want -64", ">>", r)
	}
	y = 255
	r = x >> y
	if r != -1 {
		t.Errorf("-128 %s 255 = %d, want -1", ">>", r)
	}
	x = -127
	y = 0
	r = x >> y
	if r != -127 {
		t.Errorf("-127 %s 0 = %d, want -127", ">>", r)
	}
	y = 1
	r = x >> y
	if r != -64 {
		t.Errorf("-127 %s 1 = %d, want -64", ">>", r)
	}
	y = 255
	r = x >> y
	if r != -1 {
		t.Errorf("-127 %s 255 = %d, want -1", ">>", r)
	}
	x = -1
	y = 0
	r = x >> y
	if r != -1 {
		t.Errorf("-1 %s 0 = %d, want -1", ">>", r)
	}
	y = 1
	r = x >> y
	if r != -1 {
		t.Errorf("-1 %s 1 = %d, want -1", ">>", r)
	}
	y = 255
	r = x >> y
	if r != -1 {
		t.Errorf("-1 %s 255 = %d, want -1", ">>", r)
	}
	x = 0
	y = 0
	r = x >> y
	if r != 0 {
		t.Errorf("0 %s 0 = %d, want 0", ">>", r)
	}
	y = 1
	r = x >> y
	if r != 0 {
		t.Errorf("0 %s 1 = %d, want 0", ">>", r)
	}
	y = 255
	r = x >> y
	if r != 0 {
		t.Errorf("0 %s 255 = %d, want 0", ">>", r)
	}
	x = 1
	y = 0
	r = x >> y
	if r != 1 {
		t.Errorf("1 %s 0 = %d, want 1", ">>", r)
	}
	y = 1
	r = x >> y
	if r != 0 {
		t.Errorf("1 %s 1 = %d, want 0", ">>", r)
	}
	y = 255
	r = x >> y
	if r != 0 {
		t.Errorf("1 %s 255 = %d, want 0", ">>", r)
	}
	x = 126
	y = 0
	r = x >> y
	if r != 126 {
		t.Errorf("126 %s 0 = %d, want 126", ">>", r)
	}
	y = 1
	r = x >> y
	if r != 63 {
		t.Errorf("126 %s 1 = %d, want 63", ">>", r)
	}
	y = 255
	r = x >> y
	if r != 0 {
		t.Errorf("126 %s 255 = %d, want 0", ">>", r)
	}
	x = 127
	y = 0
	r = x >> y
	if r != 127 {
		t.Errorf("127 %s 0 = %d, want 127", ">>", r)
	}
	y = 1
	r = x >> y
	if r != 63 {
		t.Errorf("127 %s 1 = %d, want 63", ">>", r)
	}
	y = 255
	r = x >> y
	if r != 0 {
		t.Errorf("127 %s 255 = %d, want 0", ">>", r)
	}
}
func TestConstFoldCompareuint64(t *testing.T) {
	{
		var x uint64 = 0
		var y uint64 = 0
		if !(x == y) {
			t.Errorf("!(%d == %d)", x, y)
		}
		if x != y {
			t.Errorf("%d != %d", x, y)
		}
		if x < y {
			t.Errorf("%d < %d", x, y)
		}
		if x > y {
			t.Errorf("%d > %d", x, y)
		}
		if !(x <= y) {
			t.Errorf("!(%d <= %d)", x, y)
		}
		if !(x >= y) {
			t.Errorf("!(%d >= %d)", x, y)
		}
	}
	{
		var x uint64 = 0
		var y uint64 = 1
		if x == y {
			t.Errorf("%d == %d", x, y)
		}
		if !(x != y) {
			t.Errorf("!(%d != %d)", x, y)
		}
		if !(x < y) {
			t.Errorf("!(%d < %d)", x, y)
		}
		if x > y {
			t.Errorf("%d > %d", x, y)
		}
		if !(x <= y) {
			t.Errorf("!(%d <= %d)", x, y)
		}
		if x >= y {
			t.Errorf("%d >= %d", x, y)
		}
	}
	{
		var x uint64 = 0
		var y uint64 = 4294967296
		if x == y {
			t.Errorf("%d == %d", x, y)
		}
		if !(x != y) {
			t.Errorf("!(%d != %d)", x, y)
		}
		if !(x < y) {
			t.Errorf("!(%d < %d)", x, y)
		}
		if x > y {
			t.Errorf("%d > %d", x, y)
		}
		if !(x <= y) {
			t.Errorf("!(%d <= %d)", x, y)
		}
		if x >= y {
			t.Errorf("%d >= %d", x, y)
		}
	}
	{
		var x uint64 = 0
		var y uint64 = 18446744073709551615
		if x == y {
			t.Errorf("%d == %d", x, y)
		}
		if !(x != y) {
			t.Errorf("!(%d != %d)", x, y)
		}
		if !(x < y) {
			t.Errorf("!(%d < %d)", x, y)
		}
		if x > y {
			t.Errorf("%d > %d", x, y)
		}
		if !(x <= y) {
			t.Errorf("!(%d <= %d)", x, y)
		}
		if x >= y {
			t.Errorf("%d >= %d", x, y)
		}
	}
	{
		var x uint64 = 1
		var y uint64 = 0
		if x == y {
			t.Errorf("%d == %d", x, y)
		}
		if !(x != y) {
			t.Errorf("!(%d != %d)", x, y)
		}
		if x < y {
			t.Errorf("%d < %d", x, y)
		}
		if !(x > y) {
			t.Errorf("!(%d > %d)", x, y)
		}
		if x <= y {
			t.Errorf("%d <= %d", x, y)
		}
		if !(x >= y) {
			t.Errorf("!(%d >= %d)", x, y)
		}
	}
	{
		var x uint64 = 1
		var y uint64 = 1
		if !(x == y) {
			t.Errorf("!(%d == %d)", x, y)
		}
		if x != y {
			t.Errorf("%d != %d", x, y)
		}
		if x < y {
			t.Errorf("%d < %d", x, y)
		}
		if x > y {
			t.Errorf("%d > %d", x, y)
		}
		if !(x <= y) {
			t.Errorf("!(%d <= %d)", x, y)
		}
		if !(x >= y) {
			t.Errorf("!(%d >= %d)", x, y)
		}
	}
	{
		var x uint64 = 1
		var y uint64 = 4294967296
		if x == y {
			t.Errorf("%d == %d", x, y)
		}
		if !(x != y) {
			t.Errorf("!(%d != %d)", x, y)
		}
		if !(x < y) {
			t.Errorf("!(%d < %d)", x, y)
		}
		if x > y {
			t.Errorf("%d > %d", x, y)
		}
		if !(x <= y) {
			t.Errorf("!(%d <= %d)", x, y)
		}
		if x >= y {
			t.Errorf("%d >= %d", x, y)
		}
	}
	{
		var x uint64 = 1
		var y uint64 = 18446744073709551615
		if x == y {
			t.Errorf("%d == %d", x, y)
		}
		if !(x != y) {
			t.Errorf("!(%d != %d)", x, y)
		}
		if !(x < y) {
			t.Errorf("!(%d < %d)", x, y)
		}
		if x > y {
			t.Errorf("%d > %d", x, y)
		}
		if !(x <= y) {
			t.Errorf("!(%d <= %d)", x, y)
		}
		if x >= y {
			t.Errorf("%d >= %d", x, y)
		}
	}
	{
		var x uint64 = 4294967296
		var y uint64 = 0
		if x == y {
			t.Errorf("%d == %d", x, y)
		}
		if !(x != y) {
			t.Errorf("!(%d != %d)", x, y)
		}
		if x < y {
			t.Errorf("%d < %d", x, y)
		}
		if !(x > y) {
			t.Errorf("!(%d > %d)", x, y)
		}
		if x <= y {
			t.Errorf("%d <= %d", x, y)
		}
		if !(x >= y) {
			t.Errorf("!(%d >= %d)", x, y)
		}
	}
	{
		var x uint64 = 4294967296
		var y uint64 = 1
		if x == y {
			t.Errorf("%d == %d", x, y)
		}
		if !(x != y) {
			t.Errorf("!(%d != %d)", x, y)
		}
		if x < y {
			t.Errorf("%d < %d", x, y)
		}
		if !(x > y) {
			t.Errorf("!(%d > %d)", x, y)
		}
		if x <= y {
			t.Errorf("%d <= %d", x, y)
		}
		if !(x >= y) {
			t.Errorf("!(%d >= %d)", x, y)
		}
	}
	{
		var x uint64 = 4294967296
		var y uint64 = 4294967296
		if !(x == y) {
			t.Errorf("!(%d == %d)", x, y)
		}
		if x != y {
			t.Errorf("%d != %d", x, y)
		}
		if x < y {
			t.Errorf("%d < %d", x, y)
		}
		if x > y {
			t.Errorf("%d > %d", x, y)
		}
		if !(x <= y) {
			t.Errorf("!(%d <= %d)", x, y)
		}
		if !(x >= y) {
			t.Errorf("!(%d >= %d)", x, y)
		}
	}
	{
		var x uint64 = 4294967296
		var y uint64 = 18446744073709551615
		if x == y {
			t.Errorf("%d == %d", x, y)
		}
		if !(x != y) {
			t.Errorf("!(%d != %d)", x, y)
		}
		if !(x < y) {
			t.Errorf("!(%d < %d)", x, y)
		}
		if x > y {
			t.Errorf("%d > %d", x, y)
		}
		if !(x <= y) {
			t.Errorf("!(%d <= %d)", x, y)
		}
		if x >= y {
			t.Errorf("%d >= %d", x, y)
		}
	}
	{
		var x uint64 = 18446744073709551615
		var y uint64 = 0
		if x == y {
			t.Errorf("%d == %d", x, y)
		}
		if !(x != y) {
			t.Errorf("!(%d != %d)", x, y)
		}
		if x < y {
			t.Errorf("%d < %d", x, y)
		}
		if !(x > y) {
			t.Errorf("!(%d > %d)", x, y)
		}
		if x <= y {
			t.Errorf("%d <= %d", x, y)
		}
		if !(x >= y) {
			t.Errorf("!(%d >= %d)", x, y)
		}
	}
	{
		var x uint64 = 18446744073709551615
		var y uint64 = 1
		if x == y {
			t.Errorf("%d == %d", x, y)
		}
		if !(x != y) {
			t.Errorf("!(%d != %d)", x, y)
		}
		if x < y {
			t.Errorf("%d < %d", x, y)
		}
		if !(x > y) {
			t.Errorf("!(%d > %d)", x, y)
		}
		if x <= y {
			t.Errorf("%d <= %d", x, y)
		}
		if !(x >= y) {
			t.Errorf("!(%d >= %d)", x, y)
		}
	}
	{
		var x uint64 = 18446744073709551615
		var y uint64 = 4294967296
		if x == y {
			t.Errorf("%d == %d", x, y)
		}
		if !(x != y) {
			t.Errorf("!(%d != %d)", x, y)
		}
		if x < y {
			t.Errorf("%d < %d", x, y)
		}
		if !(x > y) {
			t.Errorf("!(%d > %d)", x, y)
		}
		if x <= y {
			t.Errorf("%d <= %d", x, y)
		}
		if !(x >= y) {
			t.Errorf("!(%d >= %d)", x, y)
		}
	}
	{
		var x uint64 = 18446744073709551615
		var y uint64 = 18446744073709551615
		if !(x == y) {
			t.Errorf("!(%d == %d)", x, y)
		}
		if x != y {
			t.Errorf("%d != %d", x, y)
		}
		if x < y {
			t.Errorf("%d < %d", x, y)
		}
		if x > y {
			t.Errorf("%d > %d", x, y)
		}
		if !(x <= y) {
			t.Errorf("!(%d <= %d)", x, y)
		}
		if !(x >= y) {
			t.Errorf("!(%d >= %d)", x, y)
		}
	}
}
func TestConstFoldCompareint64(t *testing.T) {
	{
		var x int64 = -9223372036854775808
		var y int64 = -9223372036854775808
		if !(x == y) {
			t.Errorf("!(%d == %d)", x, y)
		}
		if x != y {
			t.Errorf("%d != %d", x, y)
		}
		if x < y {
			t.Errorf("%d < %d", x, y)
		}
		if x > y {
			t.Errorf("%d > %d", x, y)
		}
		if !(x <= y) {
			t.Errorf("!(%d <= %d)", x, y)
		}
		if !(x >= y) {
			t.Errorf("!(%d >= %d)", x, y)
		}
	}
	{
		var x int64 = -9223372036854775808
		var y int64 = -9223372036854775807
		if x == y {
			t.Errorf("%d == %d", x, y)
		}
		if !(x != y) {
			t.Errorf("!(%d != %d)", x, y)
		}
		if !(x < y) {
			t.Errorf("!(%d < %d)", x, y)
		}
		if x > y {
			t.Errorf("%d > %d", x, y)
		}
		if !(x <= y) {
			t.Errorf("!(%d <= %d)", x, y)
		}
		if x >= y {
			t.Errorf("%d >= %d", x, y)
		}
	}
	{
		var x int64 = -9223372036854775808
		var y int64 = -4294967296
		if x == y {
			t.Errorf("%d == %d", x, y)
		}
		if !(x != y) {
			t.Errorf("!(%d != %d)", x, y)
		}
		if !(x < y) {
			t.Errorf("!(%d < %d)", x, y)
		}
		if x > y {
			t.Errorf("%d > %d", x, y)
		}
		if !(x <= y) {
			t.Errorf("!(%d <= %d)", x, y)
		}
		if x >= y {
			t.Errorf("%d >= %d", x, y)
		}
	}
	{
		var x int64 = -9223372036854775808
		var y int64 = -1
		if x == y {
			t.Errorf("%d == %d", x, y)
		}
		if !(x != y) {
			t.Errorf("!(%d != %d)", x, y)
		}
		if !(x < y) {
			t.Errorf("!(%d < %d)", x, y)
		}
		if x > y {
			t.Errorf("%d > %d", x, y)
		}
		if !(x <= y) {
			t.Errorf("!(%d <= %d)", x, y)
		}
		if x >= y {
			t.Errorf("%d >= %d", x, y)
		}
	}
	{
		var x int64 = -9223372036854775808
		var y int64 = 0
		if x == y {
			t.Errorf("%d == %d", x, y)
		}
		if !(x != y) {
			t.Errorf("!(%d != %d)", x, y)
		}
		if !(x < y) {
			t.Errorf("!(%d < %d)", x, y)
		}
		if x > y {
			t.Errorf("%d > %d", x, y)
		}
		if !(x <= y) {
			t.Errorf("!(%d <= %d)", x, y)
		}
		if x >= y {
			t.Errorf("%d >= %d", x, y)
		}
	}
	{
		var x int64 = -9223372036854775808
		var y int64 = 1
		if x == y {
			t.Errorf("%d == %d", x, y)
		}
		if !(x != y) {
			t.Errorf("!(%d != %d)", x, y)
		}
		if !(x < y) {
			t.Errorf("!(%d < %d)", x, y)
		}
		if x > y {
			t.Errorf("%d > %d", x, y)
		}
		if !(x <= y) {
			t.Errorf("!(%d <= %d)", x, y)
		}
		if x >= y {
			t.Errorf("%d >= %d", x, y)
		}
	}
	{
		var x int64 = -9223372036854775808
		var y int64 = 4294967296
		if x == y {
			t.Errorf("%d == %d", x, y)
		}
		if !(x != y) {
			t.Errorf("!(%d != %d)", x, y)
		}
		if !(x < y) {
			t.Errorf("!(%d < %d)", x, y)
		}
		if x > y {
			t.Errorf("%d > %d", x, y)
		}
		if !(x <= y) {
			t.Errorf("!(%d <= %d)", x, y)
		}
		if x >= y {
			t.Errorf("%d >= %d", x, y)
		}
	}
	{
		var x int64 = -9223372036854775808
		var y int64 = 9223372036854775806
		if x == y {
			t.Errorf("%d == %d", x, y)
		}
		if !(x != y) {
			t.Errorf("!(%d != %d)", x, y)
		}
		if !(x < y) {
			t.Errorf("!(%d < %d)", x, y)
		}
		if x > y {
			t.Errorf("%d > %d", x, y)
		}
		if !(x <= y) {
			t.Errorf("!(%d <= %d)", x, y)
		}
		if x >= y {
			t.Errorf("%d >= %d", x, y)
		}
	}
	{
		var x int64 = -9223372036854775808
		var y int64 = 9223372036854775807
		if x == y {
			t.Errorf("%d == %d", x, y)
		}
		if !(x != y) {
			t.Errorf("!(%d != %d)", x, y)
		}
		if !(x < y) {
			t.Errorf("!(%d < %d)", x, y)
		}
		if x > y {
			t.Errorf("%d > %d", x, y)
		}
		if !(x <= y) {
			t.Errorf("!(%d <= %d)", x, y)
		}
		if x >= y {
			t.Errorf("%d >= %d", x, y)
		}
	}
	{
		var x int64 = -9223372036854775807
		var y int64 = -9223372036854775808
		if x == y {
			t.Errorf("%d == %d", x, y)
		}
		if !(x != y) {
			t.Errorf("!(%d != %d)", x, y)
		}
		if x < y {
			t.Errorf("%d < %d", x, y)
		}
		if !(x > y) {
			t.Errorf("!(%d > %d)", x, y)
		}
		if x <= y {
			t.Errorf("%d <= %d", x, y)
		}
		if !(x >= y) {
			t.Errorf("!(%d >= %d)", x, y)
		}
	}
	{
		var x int64 = -9223372036854775807
		var y int64 = -9223372036854775807
		if !(x == y) {
			t.Errorf("!(%d == %d)", x, y)
		}
		if x != y {
			t.Errorf("%d != %d", x, y)
		}
		if x < y {
			t.Errorf("%d < %d", x, y)
		}
		if x > y {
			t.Errorf("%d > %d", x, y)
		}
		if !(x <= y) {
			t.Errorf("!(%d <= %d)", x, y)
		}
		if !(x >= y) {
			t.Errorf("!(%d >= %d)", x, y)
		}
	}
	{
		var x int64 = -9223372036854775807
		var y int64 = -4294967296
		if x == y {
			t.Errorf("%d == %d", x, y)
		}
		if !(x != y) {
			t.Errorf("!(%d != %d)", x, y)
		}
		if !(x < y) {
			t.Errorf("!(%d < %d)", x, y)
		}
		if x > y {
			t.Errorf("%d > %d", x, y)
		}
		if !(x <= y) {
			t.Errorf("!(%d <= %d)", x, y)
		}
		if x >= y {
			t.Errorf("%d >= %d", x, y)
		}
	}
	{
		var x int64 = -9223372036854775807
		var y int64 = -1
		if x == y {
			t.Errorf("%d == %d", x, y)
		}
		if !(x != y) {
			t.Errorf("!(%d != %d)", x, y)
		}
		if !(x < y) {
			t.Errorf("!(%d < %d)", x, y)
		}
		if x > y {
			t.Errorf("%d > %d", x, y)
		}
		if !(x <= y) {
			t.Errorf("!(%d <= %d)", x, y)
		}
		if x >= y {
			t.Errorf("%d >= %d", x, y)
		}
	}
	{
		var x int64 = -9223372036854775807
		var y int64 = 0
		if x == y {
			t.Errorf("%d == %d", x, y)
		}
		if !(x != y) {
			t.Errorf("!(%d != %d)", x, y)
		}
		if !(x < y) {
			t.Errorf("!(%d < %d)", x, y)
		}
		if x > y {
			t.Errorf("%d > %d", x, y)
		}
		if !(x <= y) {
			t.Errorf("!(%d <= %d)", x, y)
		}
		if x >= y {
			t.Errorf("%d >= %d", x, y)
		}
	}
	{
		var x int64 = -9223372036854775807
		var y int64 = 1
		if x == y {
			t.Errorf("%d == %d", x, y)
		}
		if !(x != y) {
			t.Errorf("!(%d != %d)", x, y)
		}
		if !(x < y) {
			t.Errorf("!(%d < %d)", x, y)
		}
		if x > y {
			t.Errorf("%d > %d", x, y)
		}
		if !(x <= y) {
			t.Errorf("!(%d <= %d)", x, y)
		}
		if x >= y {
			t.Errorf("%d >= %d", x, y)
		}
	}
	{
		var x int64 = -9223372036854775807
		var y int64 = 4294967296
		if x == y {
			t.Errorf("%d == %d", x, y)
		}
		if !(x != y) {
			t.Errorf("!(%d != %d)", x, y)
		}
		if !(x < y) {
			t.Errorf("!(%d < %d)", x, y)
		}
		if x > y {
			t.Errorf("%d > %d", x, y)
		}
		if !(x <= y) {
			t.Errorf("!(%d <= %d)", x, y)
		}
		if x >= y {
			t.Errorf("%d >= %d", x, y)
		}
	}
	{
		var x int64 = -9223372036854775807
		var y int64 = 9223372036854775806
		if x == y {
			t.Errorf("%d == %d", x, y)
		}
		if !(x != y) {
			t.Errorf("!(%d != %d)", x, y)
		}
		if !(x < y) {
			t.Errorf("!(%d < %d)", x, y)
		}
		if x > y {
			t.Errorf("%d > %d", x, y)
		}
		if !(x <= y) {
			t.Errorf("!(%d <= %d)", x, y)
		}
		if x >= y {
			t.Errorf("%d >= %d", x, y)
		}
	}
	{
		var x int64 = -9223372036854775807
		var y int64 = 9223372036854775807
		if x == y {
			t.Errorf("%d == %d", x, y)
		}
		if !(x != y) {
			t.Errorf("!(%d != %d)", x, y)
		}
		if !(x < y) {
			t.Errorf("!(%d < %d)", x, y)
		}
		if x > y {
			t.Errorf("%d > %d", x, y)
		}
		if !(x <= y) {
			t.Errorf("!(%d <= %d)", x, y)
		}
		if x >= y {
			t.Errorf("%d >= %d", x, y)
		}
	}
	{
		var x int64 = -4294967296
		var y int64 = -9223372036854775808
		if x == y {
			t.Errorf("%d == %d", x, y)
		}
		if !(x != y) {
			t.Errorf("!(%d != %d)", x, y)
		}
		if x < y {
			t.Errorf("%d < %d", x, y)
		}
		if !(x > y) {
			t.Errorf("!(%d > %d)", x, y)
		}
		if x <= y {
			t.Errorf("%d <= %d", x, y)
		}
		if !(x >= y) {
			t.Errorf("!(%d >= %d)", x, y)
		}
	}
	{
		var x int64 = -4294967296
		var y int64 = -9223372036854775807
		if x == y {
			t.Errorf("%d == %d", x, y)
		}
		if !(x != y) {
			t.Errorf("!(%d != %d)", x, y)
		}
		if x < y {
			t.Errorf("%d < %d", x, y)
		}
		if !(x > y) {
			t.Errorf("!(%d > %d)", x, y)
		}
		if x <= y {
			t.Errorf("%d <= %d", x, y)
		}
		if !(x >= y) {
			t.Errorf("!(%d >= %d)", x, y)
		}
	}
	{
		var x int64 = -4294967296
		var y int64 = -4294967296
		if !(x == y) {
			t.Errorf("!(%d == %d)", x, y)
		}
		if x != y {
			t.Errorf("%d != %d", x, y)
		}
		if x < y {
			t.Errorf("%d < %d", x, y)
		}
		if x > y {
			t.Errorf("%d > %d", x, y)
		}
		if !(x <= y) {
			t.Errorf("!(%d <= %d)", x, y)
		}
		if !(x >= y) {
			t.Errorf("!(%d >= %d)", x, y)
		}
	}
	{
		var x int64 = -4294967296
		var y int64 = -1
		if x == y {
			t.Errorf("%d == %d", x, y)
		}
		if !(x != y) {
			t.Errorf("!(%d != %d)", x, y)
		}
		if !(x < y) {
			t.Errorf("!(%d < %d)", x, y)
		}
		if x > y {
			t.Errorf("%d > %d", x, y)
		}
		if !(x <= y) {
			t.Errorf("!(%d <= %d)", x, y)
		}
		if x >= y {
			t.Errorf("%d >= %d", x, y)
		}
	}
	{
		var x int64 = -4294967296
		var y int64 = 0
		if x == y {
			t.Errorf("%d == %d", x, y)
		}
		if !(x != y) {
			t.Errorf("!(%d != %d)", x, y)
		}
		if !(x < y) {
			t.Errorf("!(%d < %d)", x, y)
		}
		if x > y {
			t.Errorf("%d > %d", x, y)
		}
		if !(x <= y) {
			t.Errorf("!(%d <= %d)", x, y)
		}
		if x >= y {
			t.Errorf("%d >= %d", x, y)
		}
	}
	{
		var x int64 = -4294967296
		var y int64 = 1
		if x == y {
			t.Errorf("%d == %d", x, y)
		}
		if !(x != y) {
			t.Errorf("!(%d != %d)", x, y)
		}
		if !(x < y) {
			t.Errorf("!(%d < %d)", x, y)
		}
		if x > y {
			t.Errorf("%d > %d", x, y)
		}
		if !(x <= y) {
			t.Errorf("!(%d <= %d)", x, y)
		}
		if x >= y {
			t.Errorf("%d >= %d", x, y)
		}
	}
	{
		var x int64 = -4294967296
		var y int64 = 4294967296
		if x == y {
			t.Errorf("%d == %d", x, y)
		}
		if !(x != y) {
			t.Errorf("!(%d != %d)", x, y)
		}
		if !(x < y) {
			t.Errorf("!(%d < %d)", x, y)
		}
		if x > y {
			t.Errorf("%d > %d", x, y)
		}
		if !(x <= y) {
			t.Errorf("!(%d <= %d)", x, y)
		}
		if x >= y {
			t.Errorf("%d >= %d", x, y)
		}
	}
	{
		var x int64 = -4294967296
		var y int64 = 9223372036854775806
		if x == y {
			t.Errorf("%d == %d", x, y)
		}
		if !(x != y) {
			t.Errorf("!(%d != %d)", x, y)
		}
		if !(x < y) {
			t.Errorf("!(%d < %d)", x, y)
		}
		if x > y {
			t.Errorf("%d > %d", x, y)
		}
		if !(x <= y) {
			t.Errorf("!(%d <= %d)", x, y)
		}
		if x >= y {
			t.Errorf("%d >= %d", x, y)
		}
	}
	{
		var x int64 = -4294967296
		var y int64 = 9223372036854775807
		if x == y {
			t.Errorf("%d == %d", x, y)
		}
		if !(x != y) {
			t.Errorf("!(%d != %d)", x, y)
		}
		if !(x < y) {
			t.Errorf("!(%d < %d)", x, y)
		}
		if x > y {
			t.Errorf("%d > %d", x, y)
		}
		if !(x <= y) {
			t.Errorf("!(%d <= %d)", x, y)
		}
		if x >= y {
			t.Errorf("%d >= %d", x, y)
		}
	}
	{
		var x int64 = -1
		var y int64 = -9223372036854775808
		if x == y {
			t.Errorf("%d == %d", x, y)
		}
		if !(x != y) {
			t.Errorf("!(%d != %d)", x, y)
		}
		if x < y {
			t.Errorf("%d < %d", x, y)
		}
		if !(x > y) {
			t.Errorf("!(%d > %d)", x, y)
		}
		if x <= y {
			t.Errorf("%d <= %d", x, y)
		}
		if !(x >= y) {
			t.Errorf("!(%d >= %d)", x, y)
		}
	}
	{
		var x int64 = -1
		var y int64 = -9223372036854775807
		if x == y {
			t.Errorf("%d == %d", x, y)
		}
		if !(x != y) {
			t.Errorf("!(%d != %d)", x, y)
		}
		if x < y {
			t.Errorf("%d < %d", x, y)
		}
		if !(x > y) {
			t.Errorf("!(%d > %d)", x, y)
		}
		if x <= y {
			t.Errorf("%d <= %d", x, y)
		}
		if !(x >= y) {
			t.Errorf("!(%d >= %d)", x, y)
		}
	}
	{
		var x int64 = -1
		var y int64 = -4294967296
		if x == y {
			t.Errorf("%d == %d", x, y)
		}
		if !(x != y) {
			t.Errorf("!(%d != %d)", x, y)
		}
		if x < y {
			t.Errorf("%d < %d", x, y)
		}
		if !(x > y) {
			t.Errorf("!(%d > %d)", x, y)
		}
		if x <= y {
			t.Errorf("%d <= %d", x, y)
		}
		if !(x >= y) {
			t.Errorf("!(%d >= %d)", x, y)
		}
	}
	{
		var x int64 = -1
		var y int64 = -1
		if !(x == y) {
			t.Errorf("!(%d == %d)", x, y)
		}
		if x != y {
			t.Errorf("%d != %d", x, y)
		}
		if x < y {
			t.Errorf("%d < %d", x, y)
		}
		if x > y {
			t.Errorf("%d > %d", x, y)
		}
		if !(x <= y) {
			t.Errorf("!(%d <= %d)", x, y)
		}
		if !(x >= y) {
			t.Errorf("!(%d >= %d)", x, y)
		}
	}
	{
		var x int64 = -1
		var y int64 = 0
		if x == y {
			t.Errorf("%d == %d", x, y)
		}
		if !(x != y) {
			t.Errorf("!(%d != %d)", x, y)
		}
		if !(x < y) {
			t.Errorf("!(%d < %d)", x, y)
		}
		if x > y {
			t.Errorf("%d > %d", x, y)
		}
		if !(x <= y) {
			t.Errorf("!(%d <= %d)", x, y)
		}
		if x >= y {
			t.Errorf("%d >= %d", x, y)
		}
	}
	{
		var x int64 = -1
		var y int64 = 1
		if x == y {
			t.Errorf("%d == %d", x, y)
		}
		if !(x != y) {
			t.Errorf("!(%d != %d)", x, y)
		}
		if !(x < y) {
			t.Errorf("!(%d < %d)", x, y)
		}
		if x > y {
			t.Errorf("%d > %d", x, y)
		}
		if !(x <= y) {
			t.Errorf("!(%d <= %d)", x, y)
		}
		if x >= y {
			t.Errorf("%d >= %d", x, y)
		}
	}
	{
		var x int64 = -1
		var y int64 = 4294967296
		if x == y {
			t.Errorf("%d == %d", x, y)
		}
		if !(x != y) {
			t.Errorf("!(%d != %d)", x, y)
		}
		if !(x < y) {
			t.Errorf("!(%d < %d)", x, y)
		}
		if x > y {
			t.Errorf("%d > %d", x, y)
		}
		if !(x <= y) {
			t.Errorf("!(%d <= %d)", x, y)
		}
		if x >= y {
			t.Errorf("%d >= %d", x, y)
		}
	}
	{
		var x int64 = -1
		var y int64 = 9223372036854775806
		if x == y {
			t.Errorf("%d == %d", x, y)
		}
		if !(x != y) {
			t.Errorf("!(%d != %d)", x, y)
		}
		if !(x < y) {
			t.Errorf("!(%d < %d)", x, y)
		}
		if x > y {
			t.Errorf("%d > %d", x, y)
		}
		if !(x <= y) {
			t.Errorf("!(%d <= %d)", x, y)
		}
		if x >= y {
			t.Errorf("%d >= %d", x, y)
		}
	}
	{
		var x int64 = -1
		var y int64 = 9223372036854775807
		if x == y {
			t.Errorf("%d == %d", x, y)
		}
		if !(x != y) {
			t.Errorf("!(%d != %d)", x, y)
		}
		if !(x < y) {
			t.Errorf("!(%d < %d)", x, y)
		}
		if x > y {
			t.Errorf("%d > %d", x, y)
		}
		if !(x <= y) {
			t.Errorf("!(%d <= %d)", x, y)
		}
		if x >= y {
			t.Errorf("%d >= %d", x, y)
		}
	}
	{
		var x int64 = 0
		var y int64 = -9223372036854775808
		if x == y {
			t.Errorf("%d == %d", x, y)
		}
		if !(x != y) {
			t.Errorf("!(%d != %d)", x, y)
		}
		if x < y {
			t.Errorf("%d < %d", x, y)
		}
		if !(x > y) {
			t.Errorf("!(%d > %d)", x, y)
		}
		if x <= y {
			t.Errorf("%d <= %d", x, y)
		}
		if !(x >= y) {
			t.Errorf("!(%d >= %d)", x, y)
		}
	}
	{
		var x int64 = 0
		var y int64 = -9223372036854775807
		if x == y {
			t.Errorf("%d == %d", x, y)
		}
		if !(x != y) {
			t.Errorf("!(%d != %d)", x, y)
		}
		if x < y {
			t.Errorf("%d < %d", x, y)
		}
		if !(x > y) {
			t.Errorf("!(%d > %d)", x, y)
		}
		if x <= y {
			t.Errorf("%d <= %d", x, y)
		}
		if !(x >= y) {
			t.Errorf("!(%d >= %d)", x, y)
		}
	}
	{
		var x int64 = 0
		var y int64 = -4294967296
		if x == y {
			t.Errorf("%d == %d", x, y)
		}
		if !(x != y) {
			t.Errorf("!(%d != %d)", x, y)
		}
		if x < y {
			t.Errorf("%d < %d", x, y)
		}
		if !(x > y) {
			t.Errorf("!(%d > %d)", x, y)
		}
		if x <= y {
			t.Errorf("%d <= %d", x, y)
		}
		if !(x >= y) {
			t.Errorf("!(%d >= %d)", x, y)
		}
	}
	{
		var x int64 = 0
		var y int64 = -1
		if x == y {
			t.Errorf("%d == %d", x, y)
		}
		if !(x != y) {
			t.Errorf("!(%d != %d)", x, y)
		}
		if x < y {
			t.Errorf("%d < %d", x, y)
		}
		if !(x > y) {
			t.Errorf("!(%d > %d)", x, y)
		}
		if x <= y {
			t.Errorf("%d <= %d", x, y)
		}
		if !(x >= y) {
			t.Errorf("!(%d >= %d)", x, y)
		}
	}
	{
		var x int64 = 0
		var y int64 = 0
		if !(x == y) {
			t.Errorf("!(%d == %d)", x, y)
		}
		if x != y {
			t.Errorf("%d != %d", x, y)
		}
		if x < y {
			t.Errorf("%d < %d", x, y)
		}
		if x > y {
			t.Errorf("%d > %d", x, y)
		}
		if !(x <= y) {
			t.Errorf("!(%d <= %d)", x, y)
		}
		if !(x >= y) {
			t.Errorf("!(%d >= %d)", x, y)
		}
	}
	{
		var x int64 = 0
		var y int64 = 1
		if x == y {
			t.Errorf("%d == %d", x, y)
		}
		if !(x != y) {
			t.Errorf("!(%d != %d)", x, y)
		}
		if !(x < y) {
			t.Errorf("!(%d < %d)", x, y)
		}
		if x > y {
			t.Errorf("%d > %d", x, y)
		}
		if !(x <= y) {
			t.Errorf("!(%d <= %d)", x, y)
		}
		if x >= y {
			t.Errorf("%d >= %d", x, y)
		}
	}
	{
		var x int64 = 0
		var y int64 = 4294967296
		if x == y {
			t.Errorf("%d == %d", x, y)
		}
		if !(x != y) {
			t.Errorf("!(%d != %d)", x, y)
		}
		if !(x < y) {
			t.Errorf("!(%d < %d)", x, y)
		}
		if x > y {
			t.Errorf("%d > %d", x, y)
		}
		if !(x <= y) {
			t.Errorf("!(%d <= %d)", x, y)
		}
		if x >= y {
			t.Errorf("%d >= %d", x, y)
		}
	}
	{
		var x int64 = 0
		var y int64 = 9223372036854775806
		if x == y {
			t.Errorf("%d == %d", x, y)
		}
		if !(x != y) {
			t.Errorf("!(%d != %d)", x, y)
		}
		if !(x < y) {
			t.Errorf("!(%d < %d)", x, y)
		}
		if x > y {
			t.Errorf("%d > %d", x, y)
		}
		if !(x <= y) {
			t.Errorf("!(%d <= %d)", x, y)
		}
		if x >= y {
			t.Errorf("%d >= %d", x, y)
		}
	}
	{
		var x int64 = 0
		var y int64 = 9223372036854775807
		if x == y {
			t.Errorf("%d == %d", x, y)
		}
		if !(x != y) {
			t.Errorf("!(%d != %d)", x, y)
		}
		if !(x < y) {
			t.Errorf("!(%d < %d)", x, y)
		}
		if x > y {
			t.Errorf("%d > %d", x, y)
		}
		if !(x <= y) {
			t.Errorf("!(%d <= %d)", x, y)
		}
		if x >= y {
			t.Errorf("%d >= %d", x, y)
		}
	}
	{
		var x int64 = 1
		var y int64 = -9223372036854775808
		if x == y {
			t.Errorf("%d == %d", x, y)
		}
		if !(x != y) {
			t.Errorf("!(%d != %d)", x, y)
		}
		if x < y {
			t.Errorf("%d < %d", x, y)
		}
		if !(x > y) {
			t.Errorf("!(%d > %d)", x, y)
		}
		if x <= y {
			t.Errorf("%d <= %d", x, y)
		}
		if !(x >= y) {
			t.Errorf("!(%d >= %d)", x, y)
		}
	}
	{
		var x int64 = 1
		var y int64 = -9223372036854775807
		if x == y {
			t.Errorf("%d == %d", x, y)
		}
		if !(x != y) {
			t.Errorf("!(%d != %d)", x, y)
		}
		if x < y {
			t.Errorf("%d < %d", x, y)
		}
		if !(x > y) {
			t.Errorf("!(%d > %d)", x, y)
		}
		if x <= y {
			t.Errorf("%d <= %d", x, y)
		}
		if !(x >= y) {
			t.Errorf("!(%d >= %d)", x, y)
		}
	}
	{
		var x int64 = 1
		var y int64 = -4294967296
		if x == y {
			t.Errorf("%d == %d", x, y)
		}
		if !(x != y) {
			t.Errorf("!(%d != %d)", x, y)
		}
		if x < y {
			t.Errorf("%d < %d", x, y)
		}
		if !(x > y) {
			t.Errorf("!(%d > %d)", x, y)
		}
		if x <= y {
			t.Errorf("%d <= %d", x, y)
		}
		if !(x >= y) {
			t.Errorf("!(%d >= %d)", x, y)
		}
	}
	{
		var x int64 = 1
		var y int64 = -1
		if x == y {
			t.Errorf("%d == %d", x, y)
		}
		if !(x != y) {
			t.Errorf("!(%d != %d)", x, y)
		}
		if x < y {
			t.Errorf("%d < %d", x, y)
		}
		if !(x > y) {
			t.Errorf("!(%d > %d)", x, y)
		}
		if x <= y {
			t.Errorf("%d <= %d", x, y)
		}
		if !(x >= y) {
			t.Errorf("!(%d >= %d)", x, y)
		}
	}
	{
		var x int64 = 1
		var y int64 = 0
		if x == y {
			t.Errorf("%d == %d", x, y)
		}
		if !(x != y) {
			t.Errorf("!(%d != %d)", x, y)
		}
		if x < y {
			t.Errorf("%d < %d", x, y)
		}
		if !(x > y) {
			t.Errorf("!(%d > %d)", x, y)
		}
		if x <= y {
			t.Errorf("%d <= %d", x, y)
		}
		if !(x >= y) {
			t.Errorf("!(%d >= %d)", x, y)
		}
	}
	{
		var x int64 = 1
		var y int64 = 1
		if !(x == y) {
			t.Errorf("!(%d == %d)", x, y)
		}
		if x != y {
			t.Errorf("%d != %d", x, y)
		}
		if x < y {
			t.Errorf("%d < %d", x, y)
		}
		if x > y {
			t.Errorf("%d > %d", x, y)
		}
		if !(x <= y) {
			t.Errorf("!(%d <= %d)", x, y)
		}
		if !(x >= y) {
			t.Errorf("!(%d >= %d)", x, y)
		}
	}
	{
		var x int64 = 1
		var y int64 = 4294967296
		if x == y {
			t.Errorf("%d == %d", x, y)
		}
		if !(x != y) {
			t.Errorf("!(%d != %d)", x, y)
		}
		if !(x < y) {
			t.Errorf("!(%d < %d)", x, y)
		}
		if x > y {
			t.Errorf("%d > %d", x, y)
		}
		if !(x <= y) {
			t.Errorf("!(%d <= %d)", x, y)
		}
		if x >= y {
			t.Errorf("%d >= %d", x, y)
		}
	}
	{
		var x int64 = 1
		var y int64 = 9223372036854775806
		if x == y {
			t.Errorf("%d == %d", x, y)
		}
		if !(x != y) {
			t.Errorf("!(%d != %d)", x, y)
		}
		if !(x < y) {
			t.Errorf("!(%d < %d)", x, y)
		}
		if x > y {
			t.Errorf("%d > %d", x, y)
		}
		if !(x <= y) {
			t.Errorf("!(%d <= %d)", x, y)
		}
		if x >= y {
			t.Errorf("%d >= %d", x, y)
		}
	}
	{
		var x int64 = 1
		var y int64 = 9223372036854775807
		if x == y {
			t.Errorf("%d == %d", x, y)
		}
		if !(x != y) {
			t.Errorf("!(%d != %d)", x, y)
		}
		if !(x < y) {
			t.Errorf("!(%d < %d)", x, y)
		}
		if x > y {
			t.Errorf("%d > %d", x, y)
		}
		if !(x <= y) {
			t.Errorf("!(%d <= %d)", x, y)
		}
		if x >= y {
			t.Errorf("%d >= %d", x, y)
		}
	}
	{
		var x int64 = 4294967296
		var y int64 = -9223372036854775808
		if x == y {
			t.Errorf("%d == %d", x, y)
		}
		if !(x != y) {
			t.Errorf("!(%d != %d)", x, y)
		}
		if x < y {
			t.Errorf("%d < %d", x, y)
		}
		if !(x > y) {
			t.Errorf("!(%d > %d)", x, y)
		}
		if x <= y {
			t.Errorf("%d <= %d", x, y)
		}
		if !(x >= y) {
			t.Errorf("!(%d >= %d)", x, y)
		}
	}
	{
		var x int64 = 4294967296
		var y int64 = -9223372036854775807
		if x == y {
			t.Errorf("%d == %d", x, y)
		}
		if !(x != y) {
			t.Errorf("!(%d != %d)", x, y)
		}
		if x < y {
			t.Errorf("%d < %d", x, y)
		}
		if !(x > y) {
			t.Errorf("!(%d > %d)", x, y)
		}
		if x <= y {
			t.Errorf("%d <= %d", x, y)
		}
		if !(x >= y) {
			t.Errorf("!(%d >= %d)", x, y)
		}
	}
	{
		var x int64 = 4294967296
		var y int64 = -4294967296
		if x == y {
			t.Errorf("%d == %d", x, y)
		}
		if !(x != y) {
			t.Errorf("!(%d != %d)", x, y)
		}
		if x < y {
			t.Errorf("%d < %d", x, y)
		}
		if !(x > y) {
			t.Errorf("!(%d > %d)", x, y)
		}
		if x <= y {
			t.Errorf("%d <= %d", x, y)
		}
		if !(x >= y) {
			t.Errorf("!(%d >= %d)", x, y)
		}
	}
	{
		var x int64 = 4294967296
		var y int64 = -1
		if x == y {
			t.Errorf("%d == %d", x, y)
		}
		if !(x != y) {
			t.Errorf("!(%d != %d)", x, y)
		}
		if x < y {
			t.Errorf("%d < %d", x, y)
		}
		if !(x > y) {
			t.Errorf("!(%d > %d)", x, y)
		}
		if x <= y {
			t.Errorf("%d <= %d", x, y)
		}
		if !(x >= y) {
			t.Errorf("!(%d >= %d)", x, y)
		}
	}
	{
		var x int64 = 4294967296
		var y int64 = 0
		if x == y {
			t.Errorf("%d == %d", x, y)
		}
		if !(x != y) {
			t.Errorf("!(%d != %d)", x, y)
		}
		if x < y {
			t.Errorf("%d < %d", x, y)
		}
		if !(x > y) {
			t.Errorf("!(%d > %d)", x, y)
		}
		if x <= y {
			t.Errorf("%d <= %d", x, y)
		}
		if !(x >= y) {
			t.Errorf("!(%d >= %d)", x, y)
		}
	}
	{
		var x int64 = 4294967296
		var y int64 = 1
		if x == y {
			t.Errorf("%d == %d", x, y)
		}
		if !(x != y) {
			t.Errorf("!(%d != %d)", x, y)
		}
		if x < y {
			t.Errorf("%d < %d", x, y)
		}
		if !(x > y) {
			t.Errorf("!(%d > %d)", x, y)
		}
		if x <= y {
			t.Errorf("%d <= %d", x, y)
		}
		if !(x >= y) {
			t.Errorf("!(%d >= %d)", x, y)
		}
	}
	{
		var x int64 = 4294967296
		var y int64 = 4294967296
		if !(x == y) {
			t.Errorf("!(%d == %d)", x, y)
		}
		if x != y {
			t.Errorf("%d != %d", x, y)
		}
		if x < y {
			t.Errorf("%d < %d", x, y)
		}
		if x > y {
			t.Errorf("%d > %d", x, y)
		}
		if !(x <= y) {
			t.Errorf("!(%d <= %d)", x, y)
		}
		if !(x >= y) {
			t.Errorf("!(%d >= %d)", x, y)
		}
	}
	{
		var x int64 = 4294967296
		var y int64 = 9223372036854775806
		if x == y {
			t.Errorf("%d == %d", x, y)
		}
		if !(x != y) {
			t.Errorf("!(%d != %d)", x, y)
		}
		if !(x < y) {
			t.Errorf("!(%d < %d)", x, y)
		}
		if x > y {
			t.Errorf("%d > %d", x, y)
		}
		if !(x <= y) {
			t.Errorf("!(%d <= %d)", x, y)
		}
		if x >= y {
			t.Errorf("%d >= %d", x, y)
		}
	}
	{
		var x int64 = 4294967296
		var y int64 = 9223372036854775807
		if x == y {
			t.Errorf("%d == %d", x, y)
		}
		if !(x != y) {
			t.Errorf("!(%d != %d)", x, y)
		}
		if !(x < y) {
			t.Errorf("!(%d < %d)", x, y)
		}
		if x > y {
			t.Errorf("%d > %d", x, y)
		}
		if !(x <= y) {
			t.Errorf("!(%d <= %d)", x, y)
		}
		if x >= y {
			t.Errorf("%d >= %d", x, y)
		}
	}
	{
		var x int64 = 9223372036854775806
		var y int64 = -9223372036854775808
		if x == y {
			t.Errorf("%d == %d", x, y)
		}
		if !(x != y) {
			t.Errorf("!(%d != %d)", x, y)
		}
		if x < y {
			t.Errorf("%d < %d", x, y)
		}
		if !(x > y) {
			t.Errorf("!(%d > %d)", x, y)
		}
		if x <= y {
			t.Errorf("%d <= %d", x, y)
		}
		if !(x >= y) {
			t.Errorf("!(%d >= %d)", x, y)
		}
	}
	{
		var x int64 = 9223372036854775806
		var y int64 = -9223372036854775807
		if x == y {
			t.Errorf("%d == %d", x, y)
		}
		if !(x != y) {
			t.Errorf("!(%d != %d)", x, y)
		}
		if x < y {
			t.Errorf("%d < %d", x, y)
		}
		if !(x > y) {
			t.Errorf("!(%d > %d)", x, y)
		}
		if x <= y {
			t.Errorf("%d <= %d", x, y)
		}
		if !(x >= y) {
			t.Errorf("!(%d >= %d)", x, y)
		}
	}
	{
		var x int64 = 9223372036854775806
		var y int64 = -4294967296
		if x == y {
			t.Errorf("%d == %d", x, y)
		}
		if !(x != y) {
			t.Errorf("!(%d != %d)", x, y)
		}
		if x < y {
			t.Errorf("%d < %d", x, y)
		}
		if !(x > y) {
			t.Errorf("!(%d > %d)", x, y)
		}
		if x <= y {
			t.Errorf("%d <= %d", x, y)
		}
		if !(x >= y) {
			t.Errorf("!(%d >= %d)", x, y)
		}
	}
	{
		var x int64 = 9223372036854775806
		var y int64 = -1
		if x == y {
			t.Errorf("%d == %d", x, y)
		}
		if !(x != y) {
			t.Errorf("!(%d != %d)", x, y)
		}
		if x < y {
			t.Errorf("%d < %d", x, y)
		}
		if !(x > y) {
			t.Errorf("!(%d > %d)", x, y)
		}
		if x <= y {
			t.Errorf("%d <= %d", x, y)
		}
		if !(x >= y) {
			t.Errorf("!(%d >= %d)", x, y)
		}
	}
	{
		var x int64 = 9223372036854775806
		var y int64 = 0
		if x == y {
			t.Errorf("%d == %d", x, y)
		}
		if !(x != y) {
			t.Errorf("!(%d != %d)", x, y)
		}
		if x < y {
			t.Errorf("%d < %d", x, y)
		}
		if !(x > y) {
			t.Errorf("!(%d > %d)", x, y)
		}
		if x <= y {
			t.Errorf("%d <= %d", x, y)
		}
		if !(x >= y) {
			t.Errorf("!(%d >= %d)", x, y)
		}
	}
	{
		var x int64 = 9223372036854775806
		var y int64 = 1
		if x == y {
			t.Errorf("%d == %d", x, y)
		}
		if !(x != y) {
			t.Errorf("!(%d != %d)", x, y)
		}
		if x < y {
			t.Errorf("%d < %d", x, y)
		}
		if !(x > y) {
			t.Errorf("!(%d > %d)", x, y)
		}
		if x <= y {
			t.Errorf("%d <= %d", x, y)
		}
		if !(x >= y) {
			t.Errorf("!(%d >= %d)", x, y)
		}
	}
	{
		var x int64 = 9223372036854775806
		var y int64 = 4294967296
		if x == y {
			t.Errorf("%d == %d", x, y)
		}
		if !(x != y) {
			t.Errorf("!(%d != %d)", x, y)
		}
		if x < y {
			t.Errorf("%d < %d", x, y)
		}
		if !(x > y) {
			t.Errorf("!(%d > %d)", x, y)
		}
		if x <= y {
			t.Errorf("%d <= %d", x, y)
		}
		if !(x >= y) {
			t.Errorf("!(%d >= %d)", x, y)
		}
	}
	{
		var x int64 = 9223372036854775806
		var y int64 = 9223372036854775806
		if !(x == y) {
			t.Errorf("!(%d == %d)", x, y)
		}
		if x != y {
			t.Errorf("%d != %d", x, y)
		}
		if x < y {
			t.Errorf("%d < %d", x, y)
		}
		if x > y {
			t.Errorf("%d > %d", x, y)
		}
		if !(x <= y) {
			t.Errorf("!(%d <= %d)", x, y)
		}
		if !(x >= y) {
			t.Errorf("!(%d >= %d)", x, y)
		}
	}
	{
		var x int64 = 9223372036854775806
		var y int64 = 9223372036854775807
		if x == y {
			t.Errorf("%d == %d", x, y)
		}
		if !(x != y) {
			t.Errorf("!(%d != %d)", x, y)
		}
		if !(x < y) {
			t.Errorf("!(%d < %d)", x, y)
		}
		if x > y {
			t.Errorf("%d > %d", x, y)
		}
		if !(x <= y) {
			t.Errorf("!(%d <= %d)", x, y)
		}
		if x >= y {
			t.Errorf("%d >= %d", x, y)
		}
	}
	{
		var x int64 = 9223372036854775807
		var y int64 = -9223372036854775808
		if x == y {
			t.Errorf("%d == %d", x, y)
		}
		if !(x != y) {
			t.Errorf("!(%d != %d)", x, y)
		}
		if x < y {
			t.Errorf("%d < %d", x, y)
		}
		if !(x > y) {
			t.Errorf("!(%d > %d)", x, y)
		}
		if x <= y {
			t.Errorf("%d <= %d", x, y)
		}
		if !(x >= y) {
			t.Errorf("!(%d >= %d)", x, y)
		}
	}
	{
		var x int64 = 9223372036854775807
		var y int64 = -9223372036854775807
		if x == y {
			t.Errorf("%d == %d", x, y)
		}
		if !(x != y) {
			t.Errorf("!(%d != %d)", x, y)
		}
		if x < y {
			t.Errorf("%d < %d", x, y)
		}
		if !(x > y) {
			t.Errorf("!(%d > %d)", x, y)
		}
		if x <= y {
			t.Errorf("%d <= %d", x, y)
		}
		if !(x >= y) {
			t.Errorf("!(%d >= %d)", x, y)
		}
	}
	{
		var x int64 = 9223372036854775807
		var y int64 = -4294967296
		if x == y {
			t.Errorf("%d == %d", x, y)
		}
		if !(x != y) {
			t.Errorf("!(%d != %d)", x, y)
		}
		if x < y {
			t.Errorf("%d < %d", x, y)
		}
		if !(x > y) {
			t.Errorf("!(%d > %d)", x, y)
		}
		if x <= y {
			t.Errorf("%d <= %d", x, y)
		}
		if !(x >= y) {
			t.Errorf("!(%d >= %d)", x, y)
		}
	}
	{
		var x int64 = 9223372036854775807
		var y int64 = -1
		if x == y {
			t.Errorf("%d == %d", x, y)
		}
		if !(x != y) {
			t.Errorf("!(%d != %d)", x, y)
		}
		if x < y {
			t.Errorf("%d < %d", x, y)
		}
		if !(x > y) {
			t.Errorf("!(%d > %d)", x, y)
		}
		if x <= y {
			t.Errorf("%d <= %d", x, y)
		}
		if !(x >= y) {
			t.Errorf("!(%d >= %d)", x, y)
		}
	}
	{
		var x int64 = 9223372036854775807
		var y int64 = 0
		if x == y {
			t.Errorf("%d == %d", x, y)
		}
		if !(x != y) {
			t.Errorf("!(%d != %d)", x, y)
		}
		if x < y {
			t.Errorf("%d < %d", x, y)
		}
		if !(x > y) {
			t.Errorf("!(%d > %d)", x, y)
		}
		if x <= y {
			t.Errorf("%d <= %d", x, y)
		}
		if !(x >= y) {
			t.Errorf("!(%d >= %d)", x, y)
		}
	}
	{
		var x int64 = 9223372036854775807
		var y int64 = 1
		if x == y {
			t.Errorf("%d == %d", x, y)
		}
		if !(x != y) {
			t.Errorf("!(%d != %d)", x, y)
		}
		if x < y {
			t.Errorf("%d < %d", x, y)
		}
		if !(x > y) {
			t.Errorf("!(%d > %d)", x, y)
		}
		if x <= y {
			t.Errorf("%d <= %d", x, y)
		}
		if !(x >= y) {
			t.Errorf("!(%d >= %d)", x, y)
		}
	}
	{
		var x int64 = 9223372036854775807
		var y int64 = 4294967296
		if x == y {
			t.Errorf("%d == %d", x, y)
		}
		if !(x != y) {
			t.Errorf("!(%d != %d)", x, y)
		}
		if x < y {
			t.Errorf("%d < %d", x, y)
		}
		if !(x > y) {
			t.Errorf("!(%d > %d)", x, y)
		}
		if x <= y {
			t.Errorf("%d <= %d", x, y)
		}
		if !(x >= y) {
			t.Errorf("!(%d >= %d)", x, y)
		}
	}
	{
		var x int64 = 9223372036854775807
		var y int64 = 9223372036854775806
		if x == y {
			t.Errorf("%d == %d", x, y)
		}
		if !(x != y) {
			t.Errorf("!(%d != %d)", x, y)
		}
		if x < y {
			t.Errorf("%d < %d", x, y)
		}
		if !(x > y) {
			t.Errorf("!(%d > %d)", x, y)
		}
		if x <= y {
			t.Errorf("%d <= %d", x, y)
		}
		if !(x >= y) {
			t.Errorf("!(%d >= %d)", x, y)
		}
	}
	{
		var x int64 = 9223372036854775807
		var y int64 = 9223372036854775807
		if !(x == y) {
			t.Errorf("!(%d == %d)", x, y)
		}
		if x != y {
			t.Errorf("%d != %d", x, y)
		}
		if x < y {
			t.Errorf("%d < %d", x, y)
		}
		if x > y {
			t.Errorf("%d > %d", x, y)
		}
		if !(x <= y) {
			t.Errorf("!(%d <= %d)", x, y)
		}
		if !(x >= y) {
			t.Errorf("!(%d >= %d)", x, y)
		}
	}
}
func TestConstFoldCompareuint32(t *testing.T) {
	{
		var x uint32 = 0
		var y uint32 = 0
		if !(x == y) {
			t.Errorf("!(%d == %d)", x, y)
		}
		if x != y {
			t.Errorf("%d != %d", x, y)
		}
		if x < y {
			t.Errorf("%d < %d", x, y)
		}
		if x > y {
			t.Errorf("%d > %d", x, y)
		}
		if !(x <= y) {
			t.Errorf("!(%d <= %d)", x, y)
		}
		if !(x >= y) {
			t.Errorf("!(%d >= %d)", x, y)
		}
	}
	{
		var x uint32 = 0
		var y uint32 = 1
		if x == y {
			t.Errorf("%d == %d", x, y)
		}
		if !(x != y) {
			t.Errorf("!(%d != %d)", x, y)
		}
		if !(x < y) {
			t.Errorf("!(%d < %d)", x, y)
		}
		if x > y {
			t.Errorf("%d > %d", x, y)
		}
		if !(x <= y) {
			t.Errorf("!(%d <= %d)", x, y)
		}
		if x >= y {
			t.Errorf("%d >= %d", x, y)
		}
	}
	{
		var x uint32 = 0
		var y uint32 = 4294967295
		if x == y {
			t.Errorf("%d == %d", x, y)
		}
		if !(x != y) {
			t.Errorf("!(%d != %d)", x, y)
		}
		if !(x < y) {
			t.Errorf("!(%d < %d)", x, y)
		}
		if x > y {
			t.Errorf("%d > %d", x, y)
		}
		if !(x <= y) {
			t.Errorf("!(%d <= %d)", x, y)
		}
		if x >= y {
			t.Errorf("%d >= %d", x, y)
		}
	}
	{
		var x uint32 = 1
		var y uint32 = 0
		if x == y {
			t.Errorf("%d == %d", x, y)
		}
		if !(x != y) {
			t.Errorf("!(%d != %d)", x, y)
		}
		if x < y {
			t.Errorf("%d < %d", x, y)
		}
		if !(x > y) {
			t.Errorf("!(%d > %d)", x, y)
		}
		if x <= y {
			t.Errorf("%d <= %d", x, y)
		}
		if !(x >= y) {
			t.Errorf("!(%d >= %d)", x, y)
		}
	}
	{
		var x uint32 = 1
		var y uint32 = 1
		if !(x == y) {
			t.Errorf("!(%d == %d)", x, y)
		}
		if x != y {
			t.Errorf("%d != %d", x, y)
		}
		if x < y {
			t.Errorf("%d < %d", x, y)
		}
		if x > y {
			t.Errorf("%d > %d", x, y)
		}
		if !(x <= y) {
			t.Errorf("!(%d <= %d)", x, y)
		}
		if !(x >= y) {
			t.Errorf("!(%d >= %d)", x, y)
		}
	}
	{
		var x uint32 = 1
		var y uint32 = 4294967295
		if x == y {
			t.Errorf("%d == %d", x, y)
		}
		if !(x != y) {
			t.Errorf("!(%d != %d)", x, y)
		}
		if !(x < y) {
			t.Errorf("!(%d < %d)", x, y)
		}
		if x > y {
			t.Errorf("%d > %d", x, y)
		}
		if !(x <= y) {
			t.Errorf("!(%d <= %d)", x, y)
		}
		if x >= y {
			t.Errorf("%d >= %d", x, y)
		}
	}
	{
		var x uint32 = 4294967295
		var y uint32 = 0
		if x == y {
			t.Errorf("%d == %d", x, y)
		}
		if !(x != y) {
			t.Errorf("!(%d != %d)", x, y)
		}
		if x < y {
			t.Errorf("%d < %d", x, y)
		}
		if !(x > y) {
			t.Errorf("!(%d > %d)", x, y)
		}
		if x <= y {
			t.Errorf("%d <= %d", x, y)
		}
		if !(x >= y) {
			t.Errorf("!(%d >= %d)", x, y)
		}
	}
	{
		var x uint32 = 4294967295
		var y uint32 = 1
		if x == y {
			t.Errorf("%d == %d", x, y)
		}
		if !(x != y) {
			t.Errorf("!(%d != %d)", x, y)
		}
		if x < y {
			t.Errorf("%d < %d", x, y)
		}
		if !(x > y) {
			t.Errorf("!(%d > %d)", x, y)
		}
		if x <= y {
			t.Errorf("%d <= %d", x, y)
		}
		if !(x >= y) {
			t.Errorf("!(%d >= %d)", x, y)
		}
	}
	{
		var x uint32 = 4294967295
		var y uint32 = 4294967295
		if !(x == y) {
			t.Errorf("!(%d == %d)", x, y)
		}
		if x != y {
			t.Errorf("%d != %d", x, y)
		}
		if x < y {
			t.Errorf("%d < %d", x, y)
		}
		if x > y {
			t.Errorf("%d > %d", x, y)
		}
		if !(x <= y) {
			t.Errorf("!(%d <= %d)", x, y)
		}
		if !(x >= y) {
			t.Errorf("!(%d >= %d)", x, y)
		}
	}
}
func TestConstFoldCompareint32(t *testing.T) {
	{
		var x int32 = -2147483648
		var y int32 = -2147483648
		if !(x == y) {
			t.Errorf("!(%d == %d)", x, y)
		}
		if x != y {
			t.Errorf("%d != %d", x, y)
		}
		if x < y {
			t.Errorf("%d < %d", x, y)
		}
		if x > y {
			t.Errorf("%d > %d", x, y)
		}
		if !(x <= y) {
			t.Errorf("!(%d <= %d)", x, y)
		}
		if !(x >= y) {
			t.Errorf("!(%d >= %d)", x, y)
		}
	}
	{
		var x int32 = -2147483648
		var y int32 = -2147483647
		if x == y {
			t.Errorf("%d == %d", x, y)
		}
		if !(x != y) {
			t.Errorf("!(%d != %d)", x, y)
		}
		if !(x < y) {
			t.Errorf("!(%d < %d)", x, y)
		}
		if x > y {
			t.Errorf("%d > %d", x, y)
		}
		if !(x <= y) {
			t.Errorf("!(%d <= %d)", x, y)
		}
		if x >= y {
			t.Errorf("%d >= %d", x, y)
		}
	}
	{
		var x int32 = -2147483648
		var y int32 = -1
		if x == y {
			t.Errorf("%d == %d", x, y)
		}
		if !(x != y) {
			t.Errorf("!(%d != %d)", x, y)
		}
		if !(x < y) {
			t.Errorf("!(%d < %d)", x, y)
		}
		if x > y {
			t.Errorf("%d > %d", x, y)
		}
		if !(x <= y) {
			t.Errorf("!(%d <= %d)", x, y)
		}
		if x >= y {
			t.Errorf("%d >= %d", x, y)
		}
	}
	{
		var x int32 = -2147483648
		var y int32 = 0
		if x == y {
			t.Errorf("%d == %d", x, y)
		}
		if !(x != y) {
			t.Errorf("!(%d != %d)", x, y)
		}
		if !(x < y) {
			t.Errorf("!(%d < %d)", x, y)
		}
		if x > y {
			t.Errorf("%d > %d", x, y)
		}
		if !(x <= y) {
			t.Errorf("!(%d <= %d)", x, y)
		}
		if x >= y {
			t.Errorf("%d >= %d", x, y)
		}
	}
	{
		var x int32 = -2147483648
		var y int32 = 1
		if x == y {
			t.Errorf("%d == %d", x, y)
		}
		if !(x != y) {
			t.Errorf("!(%d != %d)", x, y)
		}
		if !(x < y) {
			t.Errorf("!(%d < %d)", x, y)
		}
		if x > y {
			t.Errorf("%d > %d", x, y)
		}
		if !(x <= y) {
			t.Errorf("!(%d <= %d)", x, y)
		}
		if x >= y {
			t.Errorf("%d >= %d", x, y)
		}
	}
	{
		var x int32 = -2147483648
		var y int32 = 2147483647
		if x == y {
			t.Errorf("%d == %d", x, y)
		}
		if !(x != y) {
			t.Errorf("!(%d != %d)", x, y)
		}
		if !(x < y) {
			t.Errorf("!(%d < %d)", x, y)
		}
		if x > y {
			t.Errorf("%d > %d", x, y)
		}
		if !(x <= y) {
			t.Errorf("!(%d <= %d)", x, y)
		}
		if x >= y {
			t.Errorf("%d >= %d", x, y)
		}
	}
	{
		var x int32 = -2147483647
		var y int32 = -2147483648
		if x == y {
			t.Errorf("%d == %d", x, y)
		}
		if !(x != y) {
			t.Errorf("!(%d != %d)", x, y)
		}
		if x < y {
			t.Errorf("%d < %d", x, y)
		}
		if !(x > y) {
			t.Errorf("!(%d > %d)", x, y)
		}
		if x <= y {
			t.Errorf("%d <= %d", x, y)
		}
		if !(x >= y) {
			t.Errorf("!(%d >= %d)", x, y)
		}
	}
	{
		var x int32 = -2147483647
		var y int32 = -2147483647
		if !(x == y) {
			t.Errorf("!(%d == %d)", x, y)
		}
		if x != y {
			t.Errorf("%d != %d", x, y)
		}
		if x < y {
			t.Errorf("%d < %d", x, y)
		}
		if x > y {
			t.Errorf("%d > %d", x, y)
		}
		if !(x <= y) {
			t.Errorf("!(%d <= %d)", x, y)
		}
		if !(x >= y) {
			t.Errorf("!(%d >= %d)", x, y)
		}
	}
	{
		var x int32 = -2147483647
		var y int32 = -1
		if x == y {
			t.Errorf("%d == %d", x, y)
		}
		if !(x != y) {
			t.Errorf("!(%d != %d)", x, y)
		}
		if !(x < y) {
			t.Errorf("!(%d < %d)", x, y)
		}
		if x > y {
			t.Errorf("%d > %d", x, y)
		}
		if !(x <= y) {
			t.Errorf("!(%d <= %d)", x, y)
		}
		if x >= y {
			t.Errorf("%d >= %d", x, y)
		}
	}
	{
		var x int32 = -2147483647
		var y int32 = 0
		if x == y {
			t.Errorf("%d == %d", x, y)
		}
		if !(x != y) {
			t.Errorf("!(%d != %d)", x, y)
		}
		if !(x < y) {
			t.Errorf("!(%d < %d)", x, y)
		}
		if x > y {
			t.Errorf("%d > %d", x, y)
		}
		if !(x <= y) {
			t.Errorf("!(%d <= %d)", x, y)
		}
		if x >= y {
			t.Errorf("%d >= %d", x, y)
		}
	}
	{
		var x int32 = -2147483647
		var y int32 = 1
		if x == y {
			t.Errorf("%d == %d", x, y)
		}
		if !(x != y) {
			t.Errorf("!(%d != %d)", x, y)
		}
		if !(x < y) {
			t.Errorf("!(%d < %d)", x, y)
		}
		if x > y {
			t.Errorf("%d > %d", x, y)
		}
		if !(x <= y) {
			t.Errorf("!(%d <= %d)", x, y)
		}
		if x >= y {
			t.Errorf("%d >= %d", x, y)
		}
	}
	{
		var x int32 = -2147483647
		var y int32 = 2147483647
		if x == y {
			t.Errorf("%d == %d", x, y)
		}
		if !(x != y) {
			t.Errorf("!(%d != %d)", x, y)
		}
		if !(x < y) {
			t.Errorf("!(%d < %d)", x, y)
		}
		if x > y {
			t.Errorf("%d > %d", x, y)
		}
		if !(x <= y) {
			t.Errorf("!(%d <= %d)", x, y)
		}
		if x >= y {
			t.Errorf("%d >= %d", x, y)
		}
	}
	{
		var x int32 = -1
		var y int32 = -2147483648
		if x == y {
			t.Errorf("%d == %d", x, y)
		}
		if !(x != y) {
			t.Errorf("!(%d != %d)", x, y)
		}
		if x < y {
			t.Errorf("%d < %d", x, y)
		}
		if !(x > y) {
			t.Errorf("!(%d > %d)", x, y)
		}
		if x <= y {
			t.Errorf("%d <= %d", x, y)
		}
		if !(x >= y) {
			t.Errorf("!(%d >= %d)", x, y)
		}
	}
	{
		var x int32 = -1
		var y int32 = -2147483647
		if x == y {
			t.Errorf("%d == %d", x, y)
		}
		if !(x != y) {
			t.Errorf("!(%d != %d)", x, y)
		}
		if x < y {
			t.Errorf("%d < %d", x, y)
		}
		if !(x > y) {
			t.Errorf("!(%d > %d)", x, y)
		}
		if x <= y {
			t.Errorf("%d <= %d", x, y)
		}
		if !(x >= y) {
			t.Errorf("!(%d >= %d)", x, y)
		}
	}
	{
		var x int32 = -1
		var y int32 = -1
		if !(x == y) {
			t.Errorf("!(%d == %d)", x, y)
		}
		if x != y {
			t.Errorf("%d != %d", x, y)
		}
		if x < y {
			t.Errorf("%d < %d", x, y)
		}
		if x > y {
			t.Errorf("%d > %d", x, y)
		}
		if !(x <= y) {
			t.Errorf("!(%d <= %d)", x, y)
		}
		if !(x >= y) {
			t.Errorf("!(%d >= %d)", x, y)
		}
	}
	{
		var x int32 = -1
		var y int32 = 0
		if x == y {
			t.Errorf("%d == %d", x, y)
		}
		if !(x != y) {
			t.Errorf("!(%d != %d)", x, y)
		}
		if !(x < y) {
			t.Errorf("!(%d < %d)", x, y)
		}
		if x > y {
			t.Errorf("%d > %d", x, y)
		}
		if !(x <= y) {
			t.Errorf("!(%d <= %d)", x, y)
		}
		if x >= y {
			t.Errorf("%d >= %d", x, y)
		}
	}
	{
		var x int32 = -1
		var y int32 = 1
		if x == y {
			t.Errorf("%d == %d", x, y)
		}
		if !(x != y) {
			t.Errorf("!(%d != %d)", x, y)
		}
		if !(x < y) {
			t.Errorf("!(%d < %d)", x, y)
		}
		if x > y {
			t.Errorf("%d > %d", x, y)
		}
		if !(x <= y) {
			t.Errorf("!(%d <= %d)", x, y)
		}
		if x >= y {
			t.Errorf("%d >= %d", x, y)
		}
	}
	{
		var x int32 = -1
		var y int32 = 2147483647
		if x == y {
			t.Errorf("%d == %d", x, y)
		}
		if !(x != y) {
			t.Errorf("!(%d != %d)", x, y)
		}
		if !(x < y) {
			t.Errorf("!(%d < %d)", x, y)
		}
		if x > y {
			t.Errorf("%d > %d", x, y)
		}
		if !(x <= y) {
			t.Errorf("!(%d <= %d)", x, y)
		}
		if x >= y {
			t.Errorf("%d >= %d", x, y)
		}
	}
	{
		var x int32 = 0
		var y int32 = -2147483648
		if x == y {
			t.Errorf("%d == %d", x, y)
		}
		if !(x != y) {
			t.Errorf("!(%d != %d)", x, y)
		}
		if x < y {
			t.Errorf("%d < %d", x, y)
		}
		if !(x > y) {
			t.Errorf("!(%d > %d)", x, y)
		}
		if x <= y {
			t.Errorf("%d <= %d", x, y)
		}
		if !(x >= y) {
			t.Errorf("!(%d >= %d)", x, y)
		}
	}
	{
		var x int32 = 0
		var y int32 = -2147483647
		if x == y {
			t.Errorf("%d == %d", x, y)
		}
		if !(x != y) {
			t.Errorf("!(%d != %d)", x, y)
		}
		if x < y {
			t.Errorf("%d < %d", x, y)
		}
		if !(x > y) {
			t.Errorf("!(%d > %d)", x, y)
		}
		if x <= y {
			t.Errorf("%d <= %d", x, y)
		}
		if !(x >= y) {
			t.Errorf("!(%d >= %d)", x, y)
		}
	}
	{
		var x int32 = 0
		var y int32 = -1
		if x == y {
			t.Errorf("%d == %d", x, y)
		}
		if !(x != y) {
			t.Errorf("!(%d != %d)", x, y)
		}
		if x < y {
			t.Errorf("%d < %d", x, y)
		}
		if !(x > y) {
			t.Errorf("!(%d > %d)", x, y)
		}
		if x <= y {
			t.Errorf("%d <= %d", x, y)
		}
		if !(x >= y) {
			t.Errorf("!(%d >= %d)", x, y)
		}
	}
	{
		var x int32 = 0
		var y int32 = 0
		if !(x == y) {
			t.Errorf("!(%d == %d)", x, y)
		}
		if x != y {
			t.Errorf("%d != %d", x, y)
		}
		if x < y {
			t.Errorf("%d < %d", x, y)
		}
		if x > y {
			t.Errorf("%d > %d", x, y)
		}
		if !(x <= y) {
			t.Errorf("!(%d <= %d)", x, y)
		}
		if !(x >= y) {
			t.Errorf("!(%d >= %d)", x, y)
		}
	}
	{
		var x int32 = 0
		var y int32 = 1
		if x == y {
			t.Errorf("%d == %d", x, y)
		}
		if !(x != y) {
			t.Errorf("!(%d != %d)", x, y)
		}
		if !(x < y) {
			t.Errorf("!(%d < %d)", x, y)
		}
		if x > y {
			t.Errorf("%d > %d", x, y)
		}
		if !(x <= y) {
			t.Errorf("!(%d <= %d)", x, y)
		}
		if x >= y {
			t.Errorf("%d >= %d", x, y)
		}
	}
	{
		var x int32 = 0
		var y int32 = 2147483647
		if x == y {
			t.Errorf("%d == %d", x, y)
		}
		if !(x != y) {
			t.Errorf("!(%d != %d)", x, y)
		}
		if !(x < y) {
			t.Errorf("!(%d < %d)", x, y)
		}
		if x > y {
			t.Errorf("%d > %d", x, y)
		}
		if !(x <= y) {
			t.Errorf("!(%d <= %d)", x, y)
		}
		if x >= y {
			t.Errorf("%d >= %d", x, y)
		}
	}
	{
		var x int32 = 1
		var y int32 = -2147483648
		if x == y {
			t.Errorf("%d == %d", x, y)
		}
		if !(x != y) {
			t.Errorf("!(%d != %d)", x, y)
		}
		if x < y {
			t.Errorf("%d < %d", x, y)
		}
		if !(x > y) {
			t.Errorf("!(%d > %d)", x, y)
		}
		if x <= y {
			t.Errorf("%d <= %d", x, y)
		}
		if !(x >= y) {
			t.Errorf("!(%d >= %d)", x, y)
		}
	}
	{
		var x int32 = 1
		var y int32 = -2147483647
		if x == y {
			t.Errorf("%d == %d", x, y)
		}
		if !(x != y) {
			t.Errorf("!(%d != %d)", x, y)
		}
		if x < y {
			t.Errorf("%d < %d", x, y)
		}
		if !(x > y) {
			t.Errorf("!(%d > %d)", x, y)
		}
		if x <= y {
			t.Errorf("%d <= %d", x, y)
		}
		if !(x >= y) {
			t.Errorf("!(%d >= %d)", x, y)
		}
	}
	{
		var x int32 = 1
		var y int32 = -1
		if x == y {
			t.Errorf("%d == %d", x, y)
		}
		if !(x != y) {
			t.Errorf("!(%d != %d)", x, y)
		}
		if x < y {
			t.Errorf("%d < %d", x, y)
		}
		if !(x > y) {
			t.Errorf("!(%d > %d)", x, y)
		}
		if x <= y {
			t.Errorf("%d <= %d", x, y)
		}
		if !(x >= y) {
			t.Errorf("!(%d >= %d)", x, y)
		}
	}
	{
		var x int32 = 1
		var y int32 = 0
		if x == y {
			t.Errorf("%d == %d", x, y)
		}
		if !(x != y) {
			t.Errorf("!(%d != %d)", x, y)
		}
		if x < y {
			t.Errorf("%d < %d", x, y)
		}
		if !(x > y) {
			t.Errorf("!(%d > %d)", x, y)
		}
		if x <= y {
			t.Errorf("%d <= %d", x, y)
		}
		if !(x >= y) {
			t.Errorf("!(%d >= %d)", x, y)
		}
	}
	{
		var x int32 = 1
		var y int32 = 1
		if !(x == y) {
			t.Errorf("!(%d == %d)", x, y)
		}
		if x != y {
			t.Errorf("%d != %d", x, y)
		}
		if x < y {
			t.Errorf("%d < %d", x, y)
		}
		if x > y {
			t.Errorf("%d > %d", x, y)
		}
		if !(x <= y) {
			t.Errorf("!(%d <= %d)", x, y)
		}
		if !(x >= y) {
			t.Errorf("!(%d >= %d)", x, y)
		}
	}
	{
		var x int32 = 1
		var y int32 = 2147483647
		if x == y {
			t.Errorf("%d == %d", x, y)
		}
		if !(x != y) {
			t.Errorf("!(%d != %d)", x, y)
		}
		if !(x < y) {
			t.Errorf("!(%d < %d)", x, y)
		}
		if x > y {
			t.Errorf("%d > %d", x, y)
		}
		if !(x <= y) {
			t.Errorf("!(%d <= %d)", x, y)
		}
		if x >= y {
			t.Errorf("%d >= %d", x, y)
		}
	}
	{
		var x int32 = 2147483647
		var y int32 = -2147483648
		if x == y {
			t.Errorf("%d == %d", x, y)
		}
		if !(x != y) {
			t.Errorf("!(%d != %d)", x, y)
		}
		if x < y {
			t.Errorf("%d < %d", x, y)
		}
		if !(x > y) {
			t.Errorf("!(%d > %d)", x, y)
		}
		if x <= y {
			t.Errorf("%d <= %d", x, y)
		}
		if !(x >= y) {
			t.Errorf("!(%d >= %d)", x, y)
		}
	}
	{
		var x int32 = 2147483647
		var y int32 = -2147483647
		if x == y {
			t.Errorf("%d == %d", x, y)
		}
		if !(x != y) {
			t.Errorf("!(%d != %d)", x, y)
		}
		if x < y {
			t.Errorf("%d < %d", x, y)
		}
		if !(x > y) {
			t.Errorf("!(%d > %d)", x, y)
		}
		if x <= y {
			t.Errorf("%d <= %d", x, y)
		}
		if !(x >= y) {
			t.Errorf("!(%d >= %d)", x, y)
		}
	}
	{
		var x int32 = 2147483647
		var y int32 = -1
		if x == y {
			t.Errorf("%d == %d", x, y)
		}
		if !(x != y) {
			t.Errorf("!(%d != %d)", x, y)
		}
		if x < y {
			t.Errorf("%d < %d", x, y)
		}
		if !(x > y) {
			t.Errorf("!(%d > %d)", x, y)
		}
		if x <= y {
			t.Errorf("%d <= %d", x, y)
		}
		if !(x >= y) {
			t.Errorf("!(%d >= %d)", x, y)
		}
	}
	{
		var x int32 = 2147483647
		var y int32 = 0
		if x == y {
			t.Errorf("%d == %d", x, y)
		}
		if !(x != y) {
			t.Errorf("!(%d != %d)", x, y)
		}
		if x < y {
			t.Errorf("%d < %d", x, y)
		}
		if !(x > y) {
			t.Errorf("!(%d > %d)", x, y)
		}
		if x <= y {
			t.Errorf("%d <= %d", x, y)
		}
		if !(x >= y) {
			t.Errorf("!(%d >= %d)", x, y)
		}
	}
	{
		var x int32 = 2147483647
		var y int32 = 1
		if x == y {
			t.Errorf("%d == %d", x, y)
		}
		if !(x != y) {
			t.Errorf("!(%d != %d)", x, y)
		}
		if x < y {
			t.Errorf("%d < %d", x, y)
		}
		if !(x > y) {
			t.Errorf("!(%d > %d)", x, y)
		}
		if x <= y {
			t.Errorf("%d <= %d", x, y)
		}
		if !(x >= y) {
			t.Errorf("!(%d >= %d)", x, y)
		}
	}
	{
		var x int32 = 2147483647
		var y int32 = 2147483647
		if !(x == y) {
			t.Errorf("!(%d == %d)", x, y)
		}
		if x != y {
			t.Errorf("%d != %d", x, y)
		}
		if x < y {
			t.Errorf("%d < %d", x, y)
		}
		if x > y {
			t.Errorf("%d > %d", x, y)
		}
		if !(x <= y) {
			t.Errorf("!(%d <= %d)", x, y)
		}
		if !(x >= y) {
			t.Errorf("!(%d >= %d)", x, y)
		}
	}
}
func TestConstFoldCompareuint16(t *testing.T) {
	{
		var x uint16 = 0
		var y uint16 = 0
		if !(x == y) {
			t.Errorf("!(%d == %d)", x, y)
		}
		if x != y {
			t.Errorf("%d != %d", x, y)
		}
		if x < y {
			t.Errorf("%d < %d", x, y)
		}
		if x > y {
			t.Errorf("%d > %d", x, y)
		}
		if !(x <= y) {
			t.Errorf("!(%d <= %d)", x, y)
		}
		if !(x >= y) {
			t.Errorf("!(%d >= %d)", x, y)
		}
	}
	{
		var x uint16 = 0
		var y uint16 = 1
		if x == y {
			t.Errorf("%d == %d", x, y)
		}
		if !(x != y) {
			t.Errorf("!(%d != %d)", x, y)
		}
		if !(x < y) {
			t.Errorf("!(%d < %d)", x, y)
		}
		if x > y {
			t.Errorf("%d > %d", x, y)
		}
		if !(x <= y) {
			t.Errorf("!(%d <= %d)", x, y)
		}
		if x >= y {
			t.Errorf("%d >= %d", x, y)
		}
	}
	{
		var x uint16 = 0
		var y uint16 = 65535
		if x == y {
			t.Errorf("%d == %d", x, y)
		}
		if !(x != y) {
			t.Errorf("!(%d != %d)", x, y)
		}
		if !(x < y) {
			t.Errorf("!(%d < %d)", x, y)
		}
		if x > y {
			t.Errorf("%d > %d", x, y)
		}
		if !(x <= y) {
			t.Errorf("!(%d <= %d)", x, y)
		}
		if x >= y {
			t.Errorf("%d >= %d", x, y)
		}
	}
	{
		var x uint16 = 1
		var y uint16 = 0
		if x == y {
			t.Errorf("%d == %d", x, y)
		}
		if !(x != y) {
			t.Errorf("!(%d != %d)", x, y)
		}
		if x < y {
			t.Errorf("%d < %d", x, y)
		}
		if !(x > y) {
			t.Errorf("!(%d > %d)", x, y)
		}
		if x <= y {
			t.Errorf("%d <= %d", x, y)
		}
		if !(x >= y) {
			t.Errorf("!(%d >= %d)", x, y)
		}
	}
	{
		var x uint16 = 1
		var y uint16 = 1
		if !(x == y) {
			t.Errorf("!(%d == %d)", x, y)
		}
		if x != y {
			t.Errorf("%d != %d", x, y)
		}
		if x < y {
			t.Errorf("%d < %d", x, y)
		}
		if x > y {
			t.Errorf("%d > %d", x, y)
		}
		if !(x <= y) {
			t.Errorf("!(%d <= %d)", x, y)
		}
		if !(x >= y) {
			t.Errorf("!(%d >= %d)", x, y)
		}
	}
	{
		var x uint16 = 1
		var y uint16 = 65535
		if x == y {
			t.Errorf("%d == %d", x, y)
		}
		if !(x != y) {
			t.Errorf("!(%d != %d)", x, y)
		}
		if !(x < y) {
			t.Errorf("!(%d < %d)", x, y)
		}
		if x > y {
			t.Errorf("%d > %d", x, y)
		}
		if !(x <= y) {
			t.Errorf("!(%d <= %d)", x, y)
		}
		if x >= y {
			t.Errorf("%d >= %d", x, y)
		}
	}
	{
		var x uint16 = 65535
		var y uint16 = 0
		if x == y {
			t.Errorf("%d == %d", x, y)
		}
		if !(x != y) {
			t.Errorf("!(%d != %d)", x, y)
		}
		if x < y {
			t.Errorf("%d < %d", x, y)
		}
		if !(x > y) {
			t.Errorf("!(%d > %d)", x, y)
		}
		if x <= y {
			t.Errorf("%d <= %d", x, y)
		}
		if !(x >= y) {
			t.Errorf("!(%d >= %d)", x, y)
		}
	}
	{
		var x uint16 = 65535
		var y uint16 = 1
		if x == y {
			t.Errorf("%d == %d", x, y)
		}
		if !(x != y) {
			t.Errorf("!(%d != %d)", x, y)
		}
		if x < y {
			t.Errorf("%d < %d", x, y)
		}
		if !(x > y) {
			t.Errorf("!(%d > %d)", x, y)
		}
		if x <= y {
			t.Errorf("%d <= %d", x, y)
		}
		if !(x >= y) {
			t.Errorf("!(%d >= %d)", x, y)
		}
	}
	{
		var x uint16 = 65535
		var y uint16 = 65535
		if !(x == y) {
			t.Errorf("!(%d == %d)", x, y)
		}
		if x != y {
			t.Errorf("%d != %d", x, y)
		}
		if x < y {
			t.Errorf("%d < %d", x, y)
		}
		if x > y {
			t.Errorf("%d > %d", x, y)
		}
		if !(x <= y) {
			t.Errorf("!(%d <= %d)", x, y)
		}
		if !(x >= y) {
			t.Errorf("!(%d >= %d)", x, y)
		}
	}
}
func TestConstFoldCompareint16(t *testing.T) {
	{
		var x int16 = -32768
		var y int16 = -32768
		if !(x == y) {
			t.Errorf("!(%d == %d)", x, y)
		}
		if x != y {
			t.Errorf("%d != %d", x, y)
		}
		if x < y {
			t.Errorf("%d < %d", x, y)
		}
		if x > y {
			t.Errorf("%d > %d", x, y)
		}
		if !(x <= y) {
			t.Errorf("!(%d <= %d)", x, y)
		}
		if !(x >= y) {
			t.Errorf("!(%d >= %d)", x, y)
		}
	}
	{
		var x int16 = -32768
		var y int16 = -32767
		if x == y {
			t.Errorf("%d == %d", x, y)
		}
		if !(x != y) {
			t.Errorf("!(%d != %d)", x, y)
		}
		if !(x < y) {
			t.Errorf("!(%d < %d)", x, y)
		}
		if x > y {
			t.Errorf("%d > %d", x, y)
		}
		if !(x <= y) {
			t.Errorf("!(%d <= %d)", x, y)
		}
		if x >= y {
			t.Errorf("%d >= %d", x, y)
		}
	}
	{
		var x int16 = -32768
		var y int16 = -1
		if x == y {
			t.Errorf("%d == %d", x, y)
		}
		if !(x != y) {
			t.Errorf("!(%d != %d)", x, y)
		}
		if !(x < y) {
			t.Errorf("!(%d < %d)", x, y)
		}
		if x > y {
			t.Errorf("%d > %d", x, y)
		}
		if !(x <= y) {
			t.Errorf("!(%d <= %d)", x, y)
		}
		if x >= y {
			t.Errorf("%d >= %d", x, y)
		}
	}
	{
		var x int16 = -32768
		var y int16 = 0
		if x == y {
			t.Errorf("%d == %d", x, y)
		}
		if !(x != y) {
			t.Errorf("!(%d != %d)", x, y)
		}
		if !(x < y) {
			t.Errorf("!(%d < %d)", x, y)
		}
		if x > y {
			t.Errorf("%d > %d", x, y)
		}
		if !(x <= y) {
			t.Errorf("!(%d <= %d)", x, y)
		}
		if x >= y {
			t.Errorf("%d >= %d", x, y)
		}
	}
	{
		var x int16 = -32768
		var y int16 = 1
		if x == y {
			t.Errorf("%d == %d", x, y)
		}
		if !(x != y) {
			t.Errorf("!(%d != %d)", x, y)
		}
		if !(x < y) {
			t.Errorf("!(%d < %d)", x, y)
		}
		if x > y {
			t.Errorf("%d > %d", x, y)
		}
		if !(x <= y) {
			t.Errorf("!(%d <= %d)", x, y)
		}
		if x >= y {
			t.Errorf("%d >= %d", x, y)
		}
	}
	{
		var x int16 = -32768
		var y int16 = 32766
		if x == y {
			t.Errorf("%d == %d", x, y)
		}
		if !(x != y) {
			t.Errorf("!(%d != %d)", x, y)
		}
		if !(x < y) {
			t.Errorf("!(%d < %d)", x, y)
		}
		if x > y {
			t.Errorf("%d > %d", x, y)
		}
		if !(x <= y) {
			t.Errorf("!(%d <= %d)", x, y)
		}
		if x >= y {
			t.Errorf("%d >= %d", x, y)
		}
	}
	{
		var x int16 = -32768
		var y int16 = 32767
		if x == y {
			t.Errorf("%d == %d", x, y)
		}
		if !(x != y) {
			t.Errorf("!(%d != %d)", x, y)
		}
		if !(x < y) {
			t.Errorf("!(%d < %d)", x, y)
		}
		if x > y {
			t.Errorf("%d > %d", x, y)
		}
		if !(x <= y) {
			t.Errorf("!(%d <= %d)", x, y)
		}
		if x >= y {
			t.Errorf("%d >= %d", x, y)
		}
	}
	{
		var x int16 = -32767
		var y int16 = -32768
		if x == y {
			t.Errorf("%d == %d", x, y)
		}
		if !(x != y) {
			t.Errorf("!(%d != %d)", x, y)
		}
		if x < y {
			t.Errorf("%d < %d", x, y)
		}
		if !(x > y) {
			t.Errorf("!(%d > %d)", x, y)
		}
		if x <= y {
			t.Errorf("%d <= %d", x, y)
		}
		if !(x >= y) {
			t.Errorf("!(%d >= %d)", x, y)
		}
	}
	{
		var x int16 = -32767
		var y int16 = -32767
		if !(x == y) {
			t.Errorf("!(%d == %d)", x, y)
		}
		if x != y {
			t.Errorf("%d != %d", x, y)
		}
		if x < y {
			t.Errorf("%d < %d", x, y)
		}
		if x > y {
			t.Errorf("%d > %d", x, y)
		}
		if !(x <= y) {
			t.Errorf("!(%d <= %d)", x, y)
		}
		if !(x >= y) {
			t.Errorf("!(%d >= %d)", x, y)
		}
	}
	{
		var x int16 = -32767
		var y int16 = -1
		if x == y {
			t.Errorf("%d == %d", x, y)
		}
		if !(x != y) {
			t.Errorf("!(%d != %d)", x, y)
		}
		if !(x < y) {
			t.Errorf("!(%d < %d)", x, y)
		}
		if x > y {
			t.Errorf("%d > %d", x, y)
		}
		if !(x <= y) {
			t.Errorf("!(%d <= %d)", x, y)
		}
		if x >= y {
			t.Errorf("%d >= %d", x, y)
		}
	}
	{
		var x int16 = -32767
		var y int16 = 0
		if x == y {
			t.Errorf("%d == %d", x, y)
		}
		if !(x != y) {
			t.Errorf("!(%d != %d)", x, y)
		}
		if !(x < y) {
			t.Errorf("!(%d < %d)", x, y)
		}
		if x > y {
			t.Errorf("%d > %d", x, y)
		}
		if !(x <= y) {
			t.Errorf("!(%d <= %d)", x, y)
		}
		if x >= y {
			t.Errorf("%d >= %d", x, y)
		}
	}
	{
		var x int16 = -32767
		var y int16 = 1
		if x == y {
			t.Errorf("%d == %d", x, y)
		}
		if !(x != y) {
			t.Errorf("!(%d != %d)", x, y)
		}
		if !(x < y) {
			t.Errorf("!(%d < %d)", x, y)
		}
		if x > y {
			t.Errorf("%d > %d", x, y)
		}
		if !(x <= y) {
			t.Errorf("!(%d <= %d)", x, y)
		}
		if x >= y {
			t.Errorf("%d >= %d", x, y)
		}
	}
	{
		var x int16 = -32767
		var y int16 = 32766
		if x == y {
			t.Errorf("%d == %d", x, y)
		}
		if !(x != y) {
			t.Errorf("!(%d != %d)", x, y)
		}
		if !(x < y) {
			t.Errorf("!(%d < %d)", x, y)
		}
		if x > y {
			t.Errorf("%d > %d", x, y)
		}
		if !(x <= y) {
			t.Errorf("!(%d <= %d)", x, y)
		}
		if x >= y {
			t.Errorf("%d >= %d", x, y)
		}
	}
	{
		var x int16 = -32767
		var y int16 = 32767
		if x == y {
			t.Errorf("%d == %d", x, y)
		}
		if !(x != y) {
			t.Errorf("!(%d != %d)", x, y)
		}
		if !(x < y) {
			t.Errorf("!(%d < %d)", x, y)
		}
		if x > y {
			t.Errorf("%d > %d", x, y)
		}
		if !(x <= y) {
			t.Errorf("!(%d <= %d)", x, y)
		}
		if x >= y {
			t.Errorf("%d >= %d", x, y)
		}
	}
	{
		var x int16 = -1
		var y int16 = -32768
		if x == y {
			t.Errorf("%d == %d", x, y)
		}
		if !(x != y) {
			t.Errorf("!(%d != %d)", x, y)
		}
		if x < y {
			t.Errorf("%d < %d", x, y)
		}
		if !(x > y) {
			t.Errorf("!(%d > %d)", x, y)
		}
		if x <= y {
			t.Errorf("%d <= %d", x, y)
		}
		if !(x >= y) {
			t.Errorf("!(%d >= %d)", x, y)
		}
	}
	{
		var x int16 = -1
		var y int16 = -32767
		if x == y {
			t.Errorf("%d == %d", x, y)
		}
		if !(x != y) {
			t.Errorf("!(%d != %d)", x, y)
		}
		if x < y {
			t.Errorf("%d < %d", x, y)
		}
		if !(x > y) {
			t.Errorf("!(%d > %d)", x, y)
		}
		if x <= y {
			t.Errorf("%d <= %d", x, y)
		}
		if !(x >= y) {
			t.Errorf("!(%d >= %d)", x, y)
		}
	}
	{
		var x int16 = -1
		var y int16 = -1
		if !(x == y) {
			t.Errorf("!(%d == %d)", x, y)
		}
		if x != y {
			t.Errorf("%d != %d", x, y)
		}
		if x < y {
			t.Errorf("%d < %d", x, y)
		}
		if x > y {
			t.Errorf("%d > %d", x, y)
		}
		if !(x <= y) {
			t.Errorf("!(%d <= %d)", x, y)
		}
		if !(x >= y) {
			t.Errorf("!(%d >= %d)", x, y)
		}
	}
	{
		var x int16 = -1
		var y int16 = 0
		if x == y {
			t.Errorf("%d == %d", x, y)
		}
		if !(x != y) {
			t.Errorf("!(%d != %d)", x, y)
		}
		if !(x < y) {
			t.Errorf("!(%d < %d)", x, y)
		}
		if x > y {
			t.Errorf("%d > %d", x, y)
		}
		if !(x <= y) {
			t.Errorf("!(%d <= %d)", x, y)
		}
		if x >= y {
			t.Errorf("%d >= %d", x, y)
		}
	}
	{
		var x int16 = -1
		var y int16 = 1
		if x == y {
			t.Errorf("%d == %d", x, y)
		}
		if !(x != y) {
			t.Errorf("!(%d != %d)", x, y)
		}
		if !(x < y) {
			t.Errorf("!(%d < %d)", x, y)
		}
		if x > y {
			t.Errorf("%d > %d", x, y)
		}
		if !(x <= y) {
			t.Errorf("!(%d <= %d)", x, y)
		}
		if x >= y {
			t.Errorf("%d >= %d", x, y)
		}
	}
	{
		var x int16 = -1
		var y int16 = 32766
		if x == y {
			t.Errorf("%d == %d", x, y)
		}
		if !(x != y) {
			t.Errorf("!(%d != %d)", x, y)
		}
		if !(x < y) {
			t.Errorf("!(%d < %d)", x, y)
		}
		if x > y {
			t.Errorf("%d > %d", x, y)
		}
		if !(x <= y) {
			t.Errorf("!(%d <= %d)", x, y)
		}
		if x >= y {
			t.Errorf("%d >= %d", x, y)
		}
	}
	{
		var x int16 = -1
		var y int16 = 32767
		if x == y {
			t.Errorf("%d == %d", x, y)
		}
		if !(x != y) {
			t.Errorf("!(%d != %d)", x, y)
		}
		if !(x < y) {
			t.Errorf("!(%d < %d)", x, y)
		}
		if x > y {
			t.Errorf("%d > %d", x, y)
		}
		if !(x <= y) {
			t.Errorf("!(%d <= %d)", x, y)
		}
		if x >= y {
			t.Errorf("%d >= %d", x, y)
		}
	}
	{
		var x int16 = 0
		var y int16 = -32768
		if x == y {
			t.Errorf("%d == %d", x, y)
		}
		if !(x != y) {
			t.Errorf("!(%d != %d)", x, y)
		}
		if x < y {
			t.Errorf("%d < %d", x, y)
		}
		if !(x > y) {
			t.Errorf("!(%d > %d)", x, y)
		}
		if x <= y {
			t.Errorf("%d <= %d", x, y)
		}
		if !(x >= y) {
			t.Errorf("!(%d >= %d)", x, y)
		}
	}
	{
		var x int16 = 0
		var y int16 = -32767
		if x == y {
			t.Errorf("%d == %d", x, y)
		}
		if !(x != y) {
			t.Errorf("!(%d != %d)", x, y)
		}
		if x < y {
			t.Errorf("%d < %d", x, y)
		}
		if !(x > y) {
			t.Errorf("!(%d > %d)", x, y)
		}
		if x <= y {
			t.Errorf("%d <= %d", x, y)
		}
		if !(x >= y) {
			t.Errorf("!(%d >= %d)", x, y)
		}
	}
	{
		var x int16 = 0
		var y int16 = -1
		if x == y {
			t.Errorf("%d == %d", x, y)
		}
		if !(x != y) {
			t.Errorf("!(%d != %d)", x, y)
		}
		if x < y {
			t.Errorf("%d < %d", x, y)
		}
		if !(x > y) {
			t.Errorf("!(%d > %d)", x, y)
		}
		if x <= y {
			t.Errorf("%d <= %d", x, y)
		}
		if !(x >= y) {
			t.Errorf("!(%d >= %d)", x, y)
		}
	}
	{
		var x int16 = 0
		var y int16 = 0
		if !(x == y) {
			t.Errorf("!(%d == %d)", x, y)
		}
		if x != y {
			t.Errorf("%d != %d", x, y)
		}
		if x < y {
			t.Errorf("%d < %d", x, y)
		}
		if x > y {
			t.Errorf("%d > %d", x, y)
		}
		if !(x <= y) {
			t.Errorf("!(%d <= %d)", x, y)
		}
		if !(x >= y) {
			t.Errorf("!(%d >= %d)", x, y)
		}
	}
	{
		var x int16 = 0
		var y int16 = 1
		if x == y {
			t.Errorf("%d == %d", x, y)
		}
		if !(x != y) {
			t.Errorf("!(%d != %d)", x, y)
		}
		if !(x < y) {
			t.Errorf("!(%d < %d)", x, y)
		}
		if x > y {
			t.Errorf("%d > %d", x, y)
		}
		if !(x <= y) {
			t.Errorf("!(%d <= %d)", x, y)
		}
		if x >= y {
			t.Errorf("%d >= %d", x, y)
		}
	}
	{
		var x int16 = 0
		var y int16 = 32766
		if x == y {
			t.Errorf("%d == %d", x, y)
		}
		if !(x != y) {
			t.Errorf("!(%d != %d)", x, y)
		}
		if !(x < y) {
			t.Errorf("!(%d < %d)", x, y)
		}
		if x > y {
			t.Errorf("%d > %d", x, y)
		}
		if !(x <= y) {
			t.Errorf("!(%d <= %d)", x, y)
		}
		if x >= y {
			t.Errorf("%d >= %d", x, y)
		}
	}
	{
		var x int16 = 0
		var y int16 = 32767
		if x == y {
			t.Errorf("%d == %d", x, y)
		}
		if !(x != y) {
			t.Errorf("!(%d != %d)", x, y)
		}
		if !(x < y) {
			t.Errorf("!(%d < %d)", x, y)
		}
		if x > y {
			t.Errorf("%d > %d", x, y)
		}
		if !(x <= y) {
			t.Errorf("!(%d <= %d)", x, y)
		}
		if x >= y {
			t.Errorf("%d >= %d", x, y)
		}
	}
	{
		var x int16 = 1
		var y int16 = -32768
		if x == y {
			t.Errorf("%d == %d", x, y)
		}
		if !(x != y) {
			t.Errorf("!(%d != %d)", x, y)
		}
		if x < y {
			t.Errorf("%d < %d", x, y)
		}
		if !(x > y) {
			t.Errorf("!(%d > %d)", x, y)
		}
		if x <= y {
			t.Errorf("%d <= %d", x, y)
		}
		if !(x >= y) {
			t.Errorf("!(%d >= %d)", x, y)
		}
	}
	{
		var x int16 = 1
		var y int16 = -32767
		if x == y {
			t.Errorf("%d == %d", x, y)
		}
		if !(x != y) {
			t.Errorf("!(%d != %d)", x, y)
		}
		if x < y {
			t.Errorf("%d < %d", x, y)
		}
		if !(x > y) {
			t.Errorf("!(%d > %d)", x, y)
		}
		if x <= y {
			t.Errorf("%d <= %d", x, y)
		}
		if !(x >= y) {
			t.Errorf("!(%d >= %d)", x, y)
		}
	}
	{
		var x int16 = 1
		var y int16 = -1
		if x == y {
			t.Errorf("%d == %d", x, y)
		}
		if !(x != y) {
			t.Errorf("!(%d != %d)", x, y)
		}
		if x < y {
			t.Errorf("%d < %d", x, y)
		}
		if !(x > y) {
			t.Errorf("!(%d > %d)", x, y)
		}
		if x <= y {
			t.Errorf("%d <= %d", x, y)
		}
		if !(x >= y) {
			t.Errorf("!(%d >= %d)", x, y)
		}
	}
	{
		var x int16 = 1
		var y int16 = 0
		if x == y {
			t.Errorf("%d == %d", x, y)
		}
		if !(x != y) {
			t.Errorf("!(%d != %d)", x, y)
		}
		if x < y {
			t.Errorf("%d < %d", x, y)
		}
		if !(x > y) {
			t.Errorf("!(%d > %d)", x, y)
		}
		if x <= y {
			t.Errorf("%d <= %d", x, y)
		}
		if !(x >= y) {
			t.Errorf("!(%d >= %d)", x, y)
		}
	}
	{
		var x int16 = 1
		var y int16 = 1
		if !(x == y) {
			t.Errorf("!(%d == %d)", x, y)
		}
		if x != y {
			t.Errorf("%d != %d", x, y)
		}
		if x < y {
			t.Errorf("%d < %d", x, y)
		}
		if x > y {
			t.Errorf("%d > %d", x, y)
		}
		if !(x <= y) {
			t.Errorf("!(%d <= %d)", x, y)
		}
		if !(x >= y) {
			t.Errorf("!(%d >= %d)", x, y)
		}
	}
	{
		var x int16 = 1
		var y int16 = 32766
		if x == y {
			t.Errorf("%d == %d", x, y)
		}
		if !(x != y) {
			t.Errorf("!(%d != %d)", x, y)
		}
		if !(x < y) {
			t.Errorf("!(%d < %d)", x, y)
		}
		if x > y {
			t.Errorf("%d > %d", x, y)
		}
		if !(x <= y) {
			t.Errorf("!(%d <= %d)", x, y)
		}
		if x >= y {
			t.Errorf("%d >= %d", x, y)
		}
	}
	{
		var x int16 = 1
		var y int16 = 32767
		if x == y {
			t.Errorf("%d == %d", x, y)
		}
		if !(x != y) {
			t.Errorf("!(%d != %d)", x, y)
		}
		if !(x < y) {
			t.Errorf("!(%d < %d)", x, y)
		}
		if x > y {
			t.Errorf("%d > %d", x, y)
		}
		if !(x <= y) {
			t.Errorf("!(%d <= %d)", x, y)
		}
		if x >= y {
			t.Errorf("%d >= %d", x, y)
		}
	}
	{
		var x int16 = 32766
		var y int16 = -32768
		if x == y {
			t.Errorf("%d == %d", x, y)
		}
		if !(x != y) {
			t.Errorf("!(%d != %d)", x, y)
		}
		if x < y {
			t.Errorf("%d < %d", x, y)
		}
		if !(x > y) {
			t.Errorf("!(%d > %d)", x, y)
		}
		if x <= y {
			t.Errorf("%d <= %d", x, y)
		}
		if !(x >= y) {
			t.Errorf("!(%d >= %d)", x, y)
		}
	}
	{
		var x int16 = 32766
		var y int16 = -32767
		if x == y {
			t.Errorf("%d == %d", x, y)
		}
		if !(x != y) {
			t.Errorf("!(%d != %d)", x, y)
		}
		if x < y {
			t.Errorf("%d < %d", x, y)
		}
		if !(x > y) {
			t.Errorf("!(%d > %d)", x, y)
		}
		if x <= y {
			t.Errorf("%d <= %d", x, y)
		}
		if !(x >= y) {
			t.Errorf("!(%d >= %d)", x, y)
		}
	}
	{
		var x int16 = 32766
		var y int16 = -1
		if x == y {
			t.Errorf("%d == %d", x, y)
		}
		if !(x != y) {
			t.Errorf("!(%d != %d)", x, y)
		}
		if x < y {
			t.Errorf("%d < %d", x, y)
		}
		if !(x > y) {
			t.Errorf("!(%d > %d)", x, y)
		}
		if x <= y {
			t.Errorf("%d <= %d", x, y)
		}
		if !(x >= y) {
			t.Errorf("!(%d >= %d)", x, y)
		}
	}
	{
		var x int16 = 32766
		var y int16 = 0
		if x == y {
			t.Errorf("%d == %d", x, y)
		}
		if !(x != y) {
			t.Errorf("!(%d != %d)", x, y)
		}
		if x < y {
			t.Errorf("%d < %d", x, y)
		}
		if !(x > y) {
			t.Errorf("!(%d > %d)", x, y)
		}
		if x <= y {
			t.Errorf("%d <= %d", x, y)
		}
		if !(x >= y) {
			t.Errorf("!(%d >= %d)", x, y)
		}
	}
	{
		var x int16 = 32766
		var y int16 = 1
		if x == y {
			t.Errorf("%d == %d", x, y)
		}
		if !(x != y) {
			t.Errorf("!(%d != %d)", x, y)
		}
		if x < y {
			t.Errorf("%d < %d", x, y)
		}
		if !(x > y) {
			t.Errorf("!(%d > %d)", x, y)
		}
		if x <= y {
			t.Errorf("%d <= %d", x, y)
		}
		if !(x >= y) {
			t.Errorf("!(%d >= %d)", x, y)
		}
	}
	{
		var x int16 = 32766
		var y int16 = 32766
		if !(x == y) {
			t.Errorf("!(%d == %d)", x, y)
		}
		if x != y {
			t.Errorf("%d != %d", x, y)
		}
		if x < y {
			t.Errorf("%d < %d", x, y)
		}
		if x > y {
			t.Errorf("%d > %d", x, y)
		}
		if !(x <= y) {
			t.Errorf("!(%d <= %d)", x, y)
		}
		if !(x >= y) {
			t.Errorf("!(%d >= %d)", x, y)
		}
	}
	{
		var x int16 = 32766
		var y int16 = 32767
		if x == y {
			t.Errorf("%d == %d", x, y)
		}
		if !(x != y) {
			t.Errorf("!(%d != %d)", x, y)
		}
		if !(x < y) {
			t.Errorf("!(%d < %d)", x, y)
		}
		if x > y {
			t.Errorf("%d > %d", x, y)
		}
		if !(x <= y) {
			t.Errorf("!(%d <= %d)", x, y)
		}
		if x >= y {
			t.Errorf("%d >= %d", x, y)
		}
	}
	{
		var x int16 = 32767
		var y int16 = -32768
		if x == y {
			t.Errorf("%d == %d", x, y)
		}
		if !(x != y) {
			t.Errorf("!(%d != %d)", x, y)
		}
		if x < y {
			t.Errorf("%d < %d", x, y)
		}
		if !(x > y) {
			t.Errorf("!(%d > %d)", x, y)
		}
		if x <= y {
			t.Errorf("%d <= %d", x, y)
		}
		if !(x >= y) {
			t.Errorf("!(%d >= %d)", x, y)
		}
	}
	{
		var x int16 = 32767
		var y int16 = -32767
		if x == y {
			t.Errorf("%d == %d", x, y)
		}
		if !(x != y) {
			t.Errorf("!(%d != %d)", x, y)
		}
		if x < y {
			t.Errorf("%d < %d", x, y)
		}
		if !(x > y) {
			t.Errorf("!(%d > %d)", x, y)
		}
		if x <= y {
			t.Errorf("%d <= %d", x, y)
		}
		if !(x >= y) {
			t.Errorf("!(%d >= %d)", x, y)
		}
	}
	{
		var x int16 = 32767
		var y int16 = -1
		if x == y {
			t.Errorf("%d == %d", x, y)
		}
		if !(x != y) {
			t.Errorf("!(%d != %d)", x, y)
		}
		if x < y {
			t.Errorf("%d < %d", x, y)
		}
		if !(x > y) {
			t.Errorf("!(%d > %d)", x, y)
		}
		if x <= y {
			t.Errorf("%d <= %d", x, y)
		}
		if !(x >= y) {
			t.Errorf("!(%d >= %d)", x, y)
		}
	}
	{
		var x int16 = 32767
		var y int16 = 0
		if x == y {
			t.Errorf("%d == %d", x, y)
		}
		if !(x != y) {
			t.Errorf("!(%d != %d)", x, y)
		}
		if x < y {
			t.Errorf("%d < %d", x, y)
		}
		if !(x > y) {
			t.Errorf("!(%d > %d)", x, y)
		}
		if x <= y {
			t.Errorf("%d <= %d", x, y)
		}
		if !(x >= y) {
			t.Errorf("!(%d >= %d)", x, y)
		}
	}
	{
		var x int16 = 32767
		var y int16 = 1
		if x == y {
			t.Errorf("%d == %d", x, y)
		}
		if !(x != y) {
			t.Errorf("!(%d != %d)", x, y)
		}
		if x < y {
			t.Errorf("%d < %d", x, y)
		}
		if !(x > y) {
			t.Errorf("!(%d > %d)", x, y)
		}
		if x <= y {
			t.Errorf("%d <= %d", x, y)
		}
		if !(x >= y) {
			t.Errorf("!(%d >= %d)", x, y)
		}
	}
	{
		var x int16 = 32767
		var y int16 = 32766
		if x == y {
			t.Errorf("%d == %d", x, y)
		}
		if !(x != y) {
			t.Errorf("!(%d != %d)", x, y)
		}
		if x < y {
			t.Errorf("%d < %d", x, y)
		}
		if !(x > y) {
			t.Errorf("!(%d > %d)", x, y)
		}
		if x <= y {
			t.Errorf("%d <= %d", x, y)
		}
		if !(x >= y) {
			t.Errorf("!(%d >= %d)", x, y)
		}
	}
	{
		var x int16 = 32767
		var y int16 = 32767
		if !(x == y) {
			t.Errorf("!(%d == %d)", x, y)
		}
		if x != y {
			t.Errorf("%d != %d", x, y)
		}
		if x < y {
			t.Errorf("%d < %d", x, y)
		}
		if x > y {
			t.Errorf("%d > %d", x, y)
		}
		if !(x <= y) {
			t.Errorf("!(%d <= %d)", x, y)
		}
		if !(x >= y) {
			t.Errorf("!(%d >= %d)", x, y)
		}
	}
}
func TestConstFoldCompareuint8(t *testing.T) {
	{
		var x uint8 = 0
		var y uint8 = 0
		if !(x == y) {
			t.Errorf("!(%d == %d)", x, y)
		}
		if x != y {
			t.Errorf("%d != %d", x, y)
		}
		if x < y {
			t.Errorf("%d < %d", x, y)
		}
		if x > y {
			t.Errorf("%d > %d", x, y)
		}
		if !(x <= y) {
			t.Errorf("!(%d <= %d)", x, y)
		}
		if !(x >= y) {
			t.Errorf("!(%d >= %d)", x, y)
		}
	}
	{
		var x uint8 = 0
		var y uint8 = 1
		if x == y {
			t.Errorf("%d == %d", x, y)
		}
		if !(x != y) {
			t.Errorf("!(%d != %d)", x, y)
		}
		if !(x < y) {
			t.Errorf("!(%d < %d)", x, y)
		}
		if x > y {
			t.Errorf("%d > %d", x, y)
		}
		if !(x <= y) {
			t.Errorf("!(%d <= %d)", x, y)
		}
		if x >= y {
			t.Errorf("%d >= %d", x, y)
		}
	}
	{
		var x uint8 = 0
		var y uint8 = 255
		if x == y {
			t.Errorf("%d == %d", x, y)
		}
		if !(x != y) {
			t.Errorf("!(%d != %d)", x, y)
		}
		if !(x < y) {
			t.Errorf("!(%d < %d)", x, y)
		}
		if x > y {
			t.Errorf("%d > %d", x, y)
		}
		if !(x <= y) {
			t.Errorf("!(%d <= %d)", x, y)
		}
		if x >= y {
			t.Errorf("%d >= %d", x, y)
		}
	}
	{
		var x uint8 = 1
		var y uint8 = 0
		if x == y {
			t.Errorf("%d == %d", x, y)
		}
		if !(x != y) {
			t.Errorf("!(%d != %d)", x, y)
		}
		if x < y {
			t.Errorf("%d < %d", x, y)
		}
		if !(x > y) {
			t.Errorf("!(%d > %d)", x, y)
		}
		if x <= y {
			t.Errorf("%d <= %d", x, y)
		}
		if !(x >= y) {
			t.Errorf("!(%d >= %d)", x, y)
		}
	}
	{
		var x uint8 = 1
		var y uint8 = 1
		if !(x == y) {
			t.Errorf("!(%d == %d)", x, y)
		}
		if x != y {
			t.Errorf("%d != %d", x, y)
		}
		if x < y {
			t.Errorf("%d < %d", x, y)
		}
		if x > y {
			t.Errorf("%d > %d", x, y)
		}
		if !(x <= y) {
			t.Errorf("!(%d <= %d)", x, y)
		}
		if !(x >= y) {
			t.Errorf("!(%d >= %d)", x, y)
		}
	}
	{
		var x uint8 = 1
		var y uint8 = 255
		if x == y {
			t.Errorf("%d == %d", x, y)
		}
		if !(x != y) {
			t.Errorf("!(%d != %d)", x, y)
		}
		if !(x < y) {
			t.Errorf("!(%d < %d)", x, y)
		}
		if x > y {
			t.Errorf("%d > %d", x, y)
		}
		if !(x <= y) {
			t.Errorf("!(%d <= %d)", x, y)
		}
		if x >= y {
			t.Errorf("%d >= %d", x, y)
		}
	}
	{
		var x uint8 = 255
		var y uint8 = 0
		if x == y {
			t.Errorf("%d == %d", x, y)
		}
		if !(x != y) {
			t.Errorf("!(%d != %d)", x, y)
		}
		if x < y {
			t.Errorf("%d < %d", x, y)
		}
		if !(x > y) {
			t.Errorf("!(%d > %d)", x, y)
		}
		if x <= y {
			t.Errorf("%d <= %d", x, y)
		}
		if !(x >= y) {
			t.Errorf("!(%d >= %d)", x, y)
		}
	}
	{
		var x uint8 = 255
		var y uint8 = 1
		if x == y {
			t.Errorf("%d == %d", x, y)
		}
		if !(x != y) {
			t.Errorf("!(%d != %d)", x, y)
		}
		if x < y {
			t.Errorf("%d < %d", x, y)
		}
		if !(x > y) {
			t.Errorf("!(%d > %d)", x, y)
		}
		if x <= y {
			t.Errorf("%d <= %d", x, y)
		}
		if !(x >= y) {
			t.Errorf("!(%d >= %d)", x, y)
		}
	}
	{
		var x uint8 = 255
		var y uint8 = 255
		if !(x == y) {
			t.Errorf("!(%d == %d)", x, y)
		}
		if x != y {
			t.Errorf("%d != %d", x, y)
		}
		if x < y {
			t.Errorf("%d < %d", x, y)
		}
		if x > y {
			t.Errorf("%d > %d", x, y)
		}
		if !(x <= y) {
			t.Errorf("!(%d <= %d)", x, y)
		}
		if !(x >= y) {
			t.Errorf("!(%d >= %d)", x, y)
		}
	}
}
func TestConstFoldCompareint8(t *testing.T) {
	{
		var x int8 = -128
		var y int8 = -128
		if !(x == y) {
			t.Errorf("!(%d == %d)", x, y)
		}
		if x != y {
			t.Errorf("%d != %d", x, y)
		}
		if x < y {
			t.Errorf("%d < %d", x, y)
		}
		if x > y {
			t.Errorf("%d > %d", x, y)
		}
		if !(x <= y) {
			t.Errorf("!(%d <= %d)", x, y)
		}
		if !(x >= y) {
			t.Errorf("!(%d >= %d)", x, y)
		}
	}
	{
		var x int8 = -128
		var y int8 = -127
		if x == y {
			t.Errorf("%d == %d", x, y)
		}
		if !(x != y) {
			t.Errorf("!(%d != %d)", x, y)
		}
		if !(x < y) {
			t.Errorf("!(%d < %d)", x, y)
		}
		if x > y {
			t.Errorf("%d > %d", x, y)
		}
		if !(x <= y) {
			t.Errorf("!(%d <= %d)", x, y)
		}
		if x >= y {
			t.Errorf("%d >= %d", x, y)
		}
	}
	{
		var x int8 = -128
		var y int8 = -1
		if x == y {
			t.Errorf("%d == %d", x, y)
		}
		if !(x != y) {
			t.Errorf("!(%d != %d)", x, y)
		}
		if !(x < y) {
			t.Errorf("!(%d < %d)", x, y)
		}
		if x > y {
			t.Errorf("%d > %d", x, y)
		}
		if !(x <= y) {
			t.Errorf("!(%d <= %d)", x, y)
		}
		if x >= y {
			t.Errorf("%d >= %d", x, y)
		}
	}
	{
		var x int8 = -128
		var y int8 = 0
		if x == y {
			t.Errorf("%d == %d", x, y)
		}
		if !(x != y) {
			t.Errorf("!(%d != %d)", x, y)
		}
		if !(x < y) {
			t.Errorf("!(%d < %d)", x, y)
		}
		if x > y {
			t.Errorf("%d > %d", x, y)
		}
		if !(x <= y) {
			t.Errorf("!(%d <= %d)", x, y)
		}
		if x >= y {
			t.Errorf("%d >= %d", x, y)
		}
	}
	{
		var x int8 = -128
		var y int8 = 1
		if x == y {
			t.Errorf("%d == %d", x, y)
		}
		if !(x != y) {
			t.Errorf("!(%d != %d)", x, y)
		}
		if !(x < y) {
			t.Errorf("!(%d < %d)", x, y)
		}
		if x > y {
			t.Errorf("%d > %d", x, y)
		}
		if !(x <= y) {
			t.Errorf("!(%d <= %d)", x, y)
		}
		if x >= y {
			t.Errorf("%d >= %d", x, y)
		}
	}
	{
		var x int8 = -128
		var y int8 = 126
		if x == y {
			t.Errorf("%d == %d", x, y)
		}
		if !(x != y) {
			t.Errorf("!(%d != %d)", x, y)
		}
		if !(x < y) {
			t.Errorf("!(%d < %d)", x, y)
		}
		if x > y {
			t.Errorf("%d > %d", x, y)
		}
		if !(x <= y) {
			t.Errorf("!(%d <= %d)", x, y)
		}
		if x >= y {
			t.Errorf("%d >= %d", x, y)
		}
	}
	{
		var x int8 = -128
		var y int8 = 127
		if x == y {
			t.Errorf("%d == %d", x, y)
		}
		if !(x != y) {
			t.Errorf("!(%d != %d)", x, y)
		}
		if !(x < y) {
			t.Errorf("!(%d < %d)", x, y)
		}
		if x > y {
			t.Errorf("%d > %d", x, y)
		}
		if !(x <= y) {
			t.Errorf("!(%d <= %d)", x, y)
		}
		if x >= y {
			t.Errorf("%d >= %d", x, y)
		}
	}
	{
		var x int8 = -127
		var y int8 = -128
		if x == y {
			t.Errorf("%d == %d", x, y)
		}
		if !(x != y) {
			t.Errorf("!(%d != %d)", x, y)
		}
		if x < y {
			t.Errorf("%d < %d", x, y)
		}
		if !(x > y) {
			t.Errorf("!(%d > %d)", x, y)
		}
		if x <= y {
			t.Errorf("%d <= %d", x, y)
		}
		if !(x >= y) {
			t.Errorf("!(%d >= %d)", x, y)
		}
	}
	{
		var x int8 = -127
		var y int8 = -127
		if !(x == y) {
			t.Errorf("!(%d == %d)", x, y)
		}
		if x != y {
			t.Errorf("%d != %d", x, y)
		}
		if x < y {
			t.Errorf("%d < %d", x, y)
		}
		if x > y {
			t.Errorf("%d > %d", x, y)
		}
		if !(x <= y) {
			t.Errorf("!(%d <= %d)", x, y)
		}
		if !(x >= y) {
			t.Errorf("!(%d >= %d)", x, y)
		}
	}
	{
		var x int8 = -127
		var y int8 = -1
		if x == y {
			t.Errorf("%d == %d", x, y)
		}
		if !(x != y) {
			t.Errorf("!(%d != %d)", x, y)
		}
		if !(x < y) {
			t.Errorf("!(%d < %d)", x, y)
		}
		if x > y {
			t.Errorf("%d > %d", x, y)
		}
		if !(x <= y) {
			t.Errorf("!(%d <= %d)", x, y)
		}
		if x >= y {
			t.Errorf("%d >= %d", x, y)
		}
	}
	{
		var x int8 = -127
		var y int8 = 0
		if x == y {
			t.Errorf("%d == %d", x, y)
		}
		if !(x != y) {
			t.Errorf("!(%d != %d)", x, y)
		}
		if !(x < y) {
			t.Errorf("!(%d < %d)", x, y)
		}
		if x > y {
			t.Errorf("%d > %d", x, y)
		}
		if !(x <= y) {
			t.Errorf("!(%d <= %d)", x, y)
		}
		if x >= y {
			t.Errorf("%d >= %d", x, y)
		}
	}
	{
		var x int8 = -127
		var y int8 = 1
		if x == y {
			t.Errorf("%d == %d", x, y)
		}
		if !(x != y) {
			t.Errorf("!(%d != %d)", x, y)
		}
		if !(x < y) {
			t.Errorf("!(%d < %d)", x, y)
		}
		if x > y {
			t.Errorf("%d > %d", x, y)
		}
		if !(x <= y) {
			t.Errorf("!(%d <= %d)", x, y)
		}
		if x >= y {
			t.Errorf("%d >= %d", x, y)
		}
	}
	{
		var x int8 = -127
		var y int8 = 126
		if x == y {
			t.Errorf("%d == %d", x, y)
		}
		if !(x != y) {
			t.Errorf("!(%d != %d)", x, y)
		}
		if !(x < y) {
			t.Errorf("!(%d < %d)", x, y)
		}
		if x > y {
			t.Errorf("%d > %d", x, y)
		}
		if !(x <= y) {
			t.Errorf("!(%d <= %d)", x, y)
		}
		if x >= y {
			t.Errorf("%d >= %d", x, y)
		}
	}
	{
		var x int8 = -127
		var y int8 = 127
		if x == y {
			t.Errorf("%d == %d", x, y)
		}
		if !(x != y) {
			t.Errorf("!(%d != %d)", x, y)
		}
		if !(x < y) {
			t.Errorf("!(%d < %d)", x, y)
		}
		if x > y {
			t.Errorf("%d > %d", x, y)
		}
		if !(x <= y) {
			t.Errorf("!(%d <= %d)", x, y)
		}
		if x >= y {
			t.Errorf("%d >= %d", x, y)
		}
	}
	{
		var x int8 = -1
		var y int8 = -128
		if x == y {
			t.Errorf("%d == %d", x, y)
		}
		if !(x != y) {
			t.Errorf("!(%d != %d)", x, y)
		}
		if x < y {
			t.Errorf("%d < %d", x, y)
		}
		if !(x > y) {
			t.Errorf("!(%d > %d)", x, y)
		}
		if x <= y {
			t.Errorf("%d <= %d", x, y)
		}
		if !(x >= y) {
			t.Errorf("!(%d >= %d)", x, y)
		}
	}
	{
		var x int8 = -1
		var y int8 = -127
		if x == y {
			t.Errorf("%d == %d", x, y)
		}
		if !(x != y) {
			t.Errorf("!(%d != %d)", x, y)
		}
		if x < y {
			t.Errorf("%d < %d", x, y)
		}
		if !(x > y) {
			t.Errorf("!(%d > %d)", x, y)
		}
		if x <= y {
			t.Errorf("%d <= %d", x, y)
		}
		if !(x >= y) {
			t.Errorf("!(%d >= %d)", x, y)
		}
	}
	{
		var x int8 = -1
		var y int8 = -1
		if !(x == y) {
			t.Errorf("!(%d == %d)", x, y)
		}
		if x != y {
			t.Errorf("%d != %d", x, y)
		}
		if x < y {
			t.Errorf("%d < %d", x, y)
		}
		if x > y {
			t.Errorf("%d > %d", x, y)
		}
		if !(x <= y) {
			t.Errorf("!(%d <= %d)", x, y)
		}
		if !(x >= y) {
			t.Errorf("!(%d >= %d)", x, y)
		}
	}
	{
		var x int8 = -1
		var y int8 = 0
		if x == y {
			t.Errorf("%d == %d", x, y)
		}
		if !(x != y) {
			t.Errorf("!(%d != %d)", x, y)
		}
		if !(x < y) {
			t.Errorf("!(%d < %d)", x, y)
		}
		if x > y {
			t.Errorf("%d > %d", x, y)
		}
		if !(x <= y) {
			t.Errorf("!(%d <= %d)", x, y)
		}
		if x >= y {
			t.Errorf("%d >= %d", x, y)
		}
	}
	{
		var x int8 = -1
		var y int8 = 1
		if x == y {
			t.Errorf("%d == %d", x, y)
		}
		if !(x != y) {
			t.Errorf("!(%d != %d)", x, y)
		}
		if !(x < y) {
			t.Errorf("!(%d < %d)", x, y)
		}
		if x > y {
			t.Errorf("%d > %d", x, y)
		}
		if !(x <= y) {
			t.Errorf("!(%d <= %d)", x, y)
		}
		if x >= y {
			t.Errorf("%d >= %d", x, y)
		}
	}
	{
		var x int8 = -1
		var y int8 = 126
		if x == y {
			t.Errorf("%d == %d", x, y)
		}
		if !(x != y) {
			t.Errorf("!(%d != %d)", x, y)
		}
		if !(x < y) {
			t.Errorf("!(%d < %d)", x, y)
		}
		if x > y {
			t.Errorf("%d > %d", x, y)
		}
		if !(x <= y) {
			t.Errorf("!(%d <= %d)", x, y)
		}
		if x >= y {
			t.Errorf("%d >= %d", x, y)
		}
	}
	{
		var x int8 = -1
		var y int8 = 127
		if x == y {
			t.Errorf("%d == %d", x, y)
		}
		if !(x != y) {
			t.Errorf("!(%d != %d)", x, y)
		}
		if !(x < y) {
			t.Errorf("!(%d < %d)", x, y)
		}
		if x > y {
			t.Errorf("%d > %d", x, y)
		}
		if !(x <= y) {
			t.Errorf("!(%d <= %d)", x, y)
		}
		if x >= y {
			t.Errorf("%d >= %d", x, y)
		}
	}
	{
		var x int8 = 0
		var y int8 = -128
		if x == y {
			t.Errorf("%d == %d", x, y)
		}
		if !(x != y) {
			t.Errorf("!(%d != %d)", x, y)
		}
		if x < y {
			t.Errorf("%d < %d", x, y)
		}
		if !(x > y) {
			t.Errorf("!(%d > %d)", x, y)
		}
		if x <= y {
			t.Errorf("%d <= %d", x, y)
		}
		if !(x >= y) {
			t.Errorf("!(%d >= %d)", x, y)
		}
	}
	{
		var x int8 = 0
		var y int8 = -127
		if x == y {
			t.Errorf("%d == %d", x, y)
		}
		if !(x != y) {
			t.Errorf("!(%d != %d)", x, y)
		}
		if x < y {
			t.Errorf("%d < %d", x, y)
		}
		if !(x > y) {
			t.Errorf("!(%d > %d)", x, y)
		}
		if x <= y {
			t.Errorf("%d <= %d", x, y)
		}
		if !(x >= y) {
			t.Errorf("!(%d >= %d)", x, y)
		}
	}
	{
		var x int8 = 0
		var y int8 = -1
		if x == y {
			t.Errorf("%d == %d", x, y)
		}
		if !(x != y) {
			t.Errorf("!(%d != %d)", x, y)
		}
		if x < y {
			t.Errorf("%d < %d", x, y)
		}
		if !(x > y) {
			t.Errorf("!(%d > %d)", x, y)
		}
		if x <= y {
			t.Errorf("%d <= %d", x, y)
		}
		if !(x >= y) {
			t.Errorf("!(%d >= %d)", x, y)
		}
	}
	{
		var x int8 = 0
		var y int8 = 0
		if !(x == y) {
			t.Errorf("!(%d == %d)", x, y)
		}
		if x != y {
			t.Errorf("%d != %d", x, y)
		}
		if x < y {
			t.Errorf("%d < %d", x, y)
		}
		if x > y {
			t.Errorf("%d > %d", x, y)
		}
		if !(x <= y) {
			t.Errorf("!(%d <= %d)", x, y)
		}
		if !(x >= y) {
			t.Errorf("!(%d >= %d)", x, y)
		}
	}
	{
		var x int8 = 0
		var y int8 = 1
		if x == y {
			t.Errorf("%d == %d", x, y)
		}
		if !(x != y) {
			t.Errorf("!(%d != %d)", x, y)
		}
		if !(x < y) {
			t.Errorf("!(%d < %d)", x, y)
		}
		if x > y {
			t.Errorf("%d > %d", x, y)
		}
		if !(x <= y) {
			t.Errorf("!(%d <= %d)", x, y)
		}
		if x >= y {
			t.Errorf("%d >= %d", x, y)
		}
	}
	{
		var x int8 = 0
		var y int8 = 126
		if x == y {
			t.Errorf("%d == %d", x, y)
		}
		if !(x != y) {
			t.Errorf("!(%d != %d)", x, y)
		}
		if !(x < y) {
			t.Errorf("!(%d < %d)", x, y)
		}
		if x > y {
			t.Errorf("%d > %d", x, y)
		}
		if !(x <= y) {
			t.Errorf("!(%d <= %d)", x, y)
		}
		if x >= y {
			t.Errorf("%d >= %d", x, y)
		}
	}
	{
		var x int8 = 0
		var y int8 = 127
		if x == y {
			t.Errorf("%d == %d", x, y)
		}
		if !(x != y) {
			t.Errorf("!(%d != %d)", x, y)
		}
		if !(x < y) {
			t.Errorf("!(%d < %d)", x, y)
		}
		if x > y {
			t.Errorf("%d > %d", x, y)
		}
		if !(x <= y) {
			t.Errorf("!(%d <= %d)", x, y)
		}
		if x >= y {
			t.Errorf("%d >= %d", x, y)
		}
	}
	{
		var x int8 = 1
		var y int8 = -128
		if x == y {
			t.Errorf("%d == %d", x, y)
		}
		if !(x != y) {
			t.Errorf("!(%d != %d)", x, y)
		}
		if x < y {
			t.Errorf("%d < %d", x, y)
		}
		if !(x > y) {
			t.Errorf("!(%d > %d)", x, y)
		}
		if x <= y {
			t.Errorf("%d <= %d", x, y)
		}
		if !(x >= y) {
			t.Errorf("!(%d >= %d)", x, y)
		}
	}
	{
		var x int8 = 1
		var y int8 = -127
		if x == y {
			t.Errorf("%d == %d", x, y)
		}
		if !(x != y) {
			t.Errorf("!(%d != %d)", x, y)
		}
		if x < y {
			t.Errorf("%d < %d", x, y)
		}
		if !(x > y) {
			t.Errorf("!(%d > %d)", x, y)
		}
		if x <= y {
			t.Errorf("%d <= %d", x, y)
		}
		if !(x >= y) {
			t.Errorf("!(%d >= %d)", x, y)
		}
	}
	{
		var x int8 = 1
		var y int8 = -1
		if x == y {
			t.Errorf("%d == %d", x, y)
		}
		if !(x != y) {
			t.Errorf("!(%d != %d)", x, y)
		}
		if x < y {
			t.Errorf("%d < %d", x, y)
		}
		if !(x > y) {
			t.Errorf("!(%d > %d)", x, y)
		}
		if x <= y {
			t.Errorf("%d <= %d", x, y)
		}
		if !(x >= y) {
			t.Errorf("!(%d >= %d)", x, y)
		}
	}
	{
		var x int8 = 1
		var y int8 = 0
		if x == y {
			t.Errorf("%d == %d", x, y)
		}
		if !(x != y) {
			t.Errorf("!(%d != %d)", x, y)
		}
		if x < y {
			t.Errorf("%d < %d", x, y)
		}
		if !(x > y) {
			t.Errorf("!(%d > %d)", x, y)
		}
		if x <= y {
			t.Errorf("%d <= %d", x, y)
		}
		if !(x >= y) {
			t.Errorf("!(%d >= %d)", x, y)
		}
	}
	{
		var x int8 = 1
		var y int8 = 1
		if !(x == y) {
			t.Errorf("!(%d == %d)", x, y)
		}
		if x != y {
			t.Errorf("%d != %d", x, y)
		}
		if x < y {
			t.Errorf("%d < %d", x, y)
		}
		if x > y {
			t.Errorf("%d > %d", x, y)
		}
		if !(x <= y) {
			t.Errorf("!(%d <= %d)", x, y)
		}
		if !(x >= y) {
			t.Errorf("!(%d >= %d)", x, y)
		}
	}
	{
		var x int8 = 1
		var y int8 = 126
		if x == y {
			t.Errorf("%d == %d", x, y)
		}
		if !(x != y) {
			t.Errorf("!(%d != %d)", x, y)
		}
		if !(x < y) {
			t.Errorf("!(%d < %d)", x, y)
		}
		if x > y {
			t.Errorf("%d > %d", x, y)
		}
		if !(x <= y) {
			t.Errorf("!(%d <= %d)", x, y)
		}
		if x >= y {
			t.Errorf("%d >= %d", x, y)
		}
	}
	{
		var x int8 = 1
		var y int8 = 127
		if x == y {
			t.Errorf("%d == %d", x, y)
		}
		if !(x != y) {
			t.Errorf("!(%d != %d)", x, y)
		}
		if !(x < y) {
			t.Errorf("!(%d < %d)", x, y)
		}
		if x > y {
			t.Errorf("%d > %d", x, y)
		}
		if !(x <= y) {
			t.Errorf("!(%d <= %d)", x, y)
		}
		if x >= y {
			t.Errorf("%d >= %d", x, y)
		}
	}
	{
		var x int8 = 126
		var y int8 = -128
		if x == y {
			t.Errorf("%d == %d", x, y)
		}
		if !(x != y) {
			t.Errorf("!(%d != %d)", x, y)
		}
		if x < y {
			t.Errorf("%d < %d", x, y)
		}
		if !(x > y) {
			t.Errorf("!(%d > %d)", x, y)
		}
		if x <= y {
			t.Errorf("%d <= %d", x, y)
		}
		if !(x >= y) {
			t.Errorf("!(%d >= %d)", x, y)
		}
	}
	{
		var x int8 = 126
		var y int8 = -127
		if x == y {
			t.Errorf("%d == %d", x, y)
		}
		if !(x != y) {
			t.Errorf("!(%d != %d)", x, y)
		}
		if x < y {
			t.Errorf("%d < %d", x, y)
		}
		if !(x > y) {
			t.Errorf("!(%d > %d)", x, y)
		}
		if x <= y {
			t.Errorf("%d <= %d", x, y)
		}
		if !(x >= y) {
			t.Errorf("!(%d >= %d)", x, y)
		}
	}
	{
		var x int8 = 126
		var y int8 = -1
		if x == y {
			t.Errorf("%d == %d", x, y)
		}
		if !(x != y) {
			t.Errorf("!(%d != %d)", x, y)
		}
		if x < y {
			t.Errorf("%d < %d", x, y)
		}
		if !(x > y) {
			t.Errorf("!(%d > %d)", x, y)
		}
		if x <= y {
			t.Errorf("%d <= %d", x, y)
		}
		if !(x >= y) {
			t.Errorf("!(%d >= %d)", x, y)
		}
	}
	{
		var x int8 = 126
		var y int8 = 0
		if x == y {
			t.Errorf("%d == %d", x, y)
		}
		if !(x != y) {
			t.Errorf("!(%d != %d)", x, y)
		}
		if x < y {
			t.Errorf("%d < %d", x, y)
		}
		if !(x > y) {
			t.Errorf("!(%d > %d)", x, y)
		}
		if x <= y {
			t.Errorf("%d <= %d", x, y)
		}
		if !(x >= y) {
			t.Errorf("!(%d >= %d)", x, y)
		}
	}
	{
		var x int8 = 126
		var y int8 = 1
		if x == y {
			t.Errorf("%d == %d", x, y)
		}
		if !(x != y) {
			t.Errorf("!(%d != %d)", x, y)
		}
		if x < y {
			t.Errorf("%d < %d", x, y)
		}
		if !(x > y) {
			t.Errorf("!(%d > %d)", x, y)
		}
		if x <= y {
			t.Errorf("%d <= %d", x, y)
		}
		if !(x >= y) {
			t.Errorf("!(%d >= %d)", x, y)
		}
	}
	{
		var x int8 = 126
		var y int8 = 126
		if !(x == y) {
			t.Errorf("!(%d == %d)", x, y)
		}
		if x != y {
			t.Errorf("%d != %d", x, y)
		}
		if x < y {
			t.Errorf("%d < %d", x, y)
		}
		if x > y {
			t.Errorf("%d > %d", x, y)
		}
		if !(x <= y) {
			t.Errorf("!(%d <= %d)", x, y)
		}
		if !(x >= y) {
			t.Errorf("!(%d >= %d)", x, y)
		}
	}
	{
		var x int8 = 126
		var y int8 = 127
		if x == y {
			t.Errorf("%d == %d", x, y)
		}
		if !(x != y) {
			t.Errorf("!(%d != %d)", x, y)
		}
		if !(x < y) {
			t.Errorf("!(%d < %d)", x, y)
		}
		if x > y {
			t.Errorf("%d > %d", x, y)
		}
		if !(x <= y) {
			t.Errorf("!(%d <= %d)", x, y)
		}
		if x >= y {
			t.Errorf("%d >= %d", x, y)
		}
	}
	{
		var x int8 = 127
		var y int8 = -128
		if x == y {
			t.Errorf("%d == %d", x, y)
		}
		if !(x != y) {
			t.Errorf("!(%d != %d)", x, y)
		}
		if x < y {
			t.Errorf("%d < %d", x, y)
		}
		if !(x > y) {
			t.Errorf("!(%d > %d)", x, y)
		}
		if x <= y {
			t.Errorf("%d <= %d", x, y)
		}
		if !(x >= y) {
			t.Errorf("!(%d >= %d)", x, y)
		}
	}
	{
		var x int8 = 127
		var y int8 = -127
		if x == y {
			t.Errorf("%d == %d", x, y)
		}
		if !(x != y) {
			t.Errorf("!(%d != %d)", x, y)
		}
		if x < y {
			t.Errorf("%d < %d", x, y)
		}
		if !(x > y) {
			t.Errorf("!(%d > %d)", x, y)
		}
		if x <= y {
			t.Errorf("%d <= %d", x, y)
		}
		if !(x >= y) {
			t.Errorf("!(%d >= %d)", x, y)
		}
	}
	{
		var x int8 = 127
		var y int8 = -1
		if x == y {
			t.Errorf("%d == %d", x, y)
		}
		if !(x != y) {
			t.Errorf("!(%d != %d)", x, y)
		}
		if x < y {
			t.Errorf("%d < %d", x, y)
		}
		if !(x > y) {
			t.Errorf("!(%d > %d)", x, y)
		}
		if x <= y {
			t.Errorf("%d <= %d", x, y)
		}
		if !(x >= y) {
			t.Errorf("!(%d >= %d)", x, y)
		}
	}
	{
		var x int8 = 127
		var y int8 = 0
		if x == y {
			t.Errorf("%d == %d", x, y)
		}
		if !(x != y) {
			t.Errorf("!(%d != %d)", x, y)
		}
		if x < y {
			t.Errorf("%d < %d", x, y)
		}
		if !(x > y) {
			t.Errorf("!(%d > %d)", x, y)
		}
		if x <= y {
			t.Errorf("%d <= %d", x, y)
		}
		if !(x >= y) {
			t.Errorf("!(%d >= %d)", x, y)
		}
	}
	{
		var x int8 = 127
		var y int8 = 1
		if x == y {
			t.Errorf("%d == %d", x, y)
		}
		if !(x != y) {
			t.Errorf("!(%d != %d)", x, y)
		}
		if x < y {
			t.Errorf("%d < %d", x, y)
		}
		if !(x > y) {
			t.Errorf("!(%d > %d)", x, y)
		}
		if x <= y {
			t.Errorf("%d <= %d", x, y)
		}
		if !(x >= y) {
			t.Errorf("!(%d >= %d)", x, y)
		}
	}
	{
		var x int8 = 127
		var y int8 = 126
		if x == y {
			t.Errorf("%d == %d", x, y)
		}
		if !(x != y) {
			t.Errorf("!(%d != %d)", x, y)
		}
		if x < y {
			t.Errorf("%d < %d", x, y)
		}
		if !(x > y) {
			t.Errorf("!(%d > %d)", x, y)
		}
		if x <= y {
			t.Errorf("%d <= %d", x, y)
		}
		if !(x >= y) {
			t.Errorf("!(%d >= %d)", x, y)
		}
	}
	{
		var x int8 = 127
		var y int8 = 127
		if !(x == y) {
			t.Errorf("!(%d == %d)", x, y)
		}
		if x != y {
			t.Errorf("%d != %d", x, y)
		}
		if x < y {
			t.Errorf("%d < %d", x, y)
		}
		if x > y {
			t.Errorf("%d > %d", x, y)
		}
		if !(x <= y) {
			t.Errorf("!(%d <= %d)", x, y)
		}
		if !(x >= y) {
			t.Errorf("!(%d >= %d)", x, y)
		}
	}
}
