# Tests golang.org/issue/47682
# Flags specified with -gcflags should appear after other flags generated by cmd/go.

cd m
go build -n -gcflags=-lang=go1.17
stderr ' -lang=go1.16.* -lang=go1.17'
! go build -gcflags='-c 0'
stderr 'compile: -c must be at least 1, got 0'

-- m/go.mod --
module example.com

go 1.16

-- m/main.go --
package main

func main() {
    var s = []int{1, 2, 3}
    var pa = (*[2]int)(s[1:])
    println(pa[1])
}
