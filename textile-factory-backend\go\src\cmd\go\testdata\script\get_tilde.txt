env GO111MODULE=off

# Paths containing windows short names should be rejected before attempting to fetch.
! go get vcs-test.golang.org/longna~1.dir/thing
stderr 'trailing tilde and digits'
! go get vcs-test.golang.org/longna~1/thing
stderr 'trailing tilde and digits'
! go get vcs-test.golang.org/~9999999/thing
stderr 'trailing tilde and digits'

[short] stop

# A path containing an element that is just a tilde, or a tilde followed by non-digits,
# should attempt to resolve.
! go get vcs-test.golang.org/~glenda/notfound
! stderr 'trailing tilde and digits'
stderr 'unrecognized import path'

! go get vcs-test.golang.org/~glenda2/notfound
! stderr 'trailing tilde and digits'
stderr 'unrecognized import path'

! go get vcs-test.golang.org/~/notfound
! stderr 'trailing tilde and digits'
stderr 'unrecognized import path'
