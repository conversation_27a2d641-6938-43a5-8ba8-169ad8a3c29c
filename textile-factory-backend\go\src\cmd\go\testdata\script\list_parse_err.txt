# 'go list' without -e should fail and print errors on stderr.
! go list ./p
stderr '^p[/\\]b.go:2:2: expected ''package'', found ''EOF''$'
! go list -f '{{range .Imports}}{{.}} {{end}}' ./p
stderr '^p[/\\]b.go:2:2: expected ''package'', found ''EOF''$'
! go list -test ./t
stderr '^go: can''t load test package: t[/\\]t_test.go:8:1: expected declaration, found ʕ'
! go list -test -f '{{range .Imports}}{{.}} {{end}}' ./t
stderr '^go: can''t load test package: t[/\\]t_test.go:8:1: expected declaration, found ʕ'

# 'go list -e' should report imports, even if some files have parse errors
# before the import block.
go list -e -f '{{range .Imports}}{{.}} {{end}}' ./p
stdout '^fmt '

# 'go list' should report the position of the error if there's only one.
go list -e -f '{{.Error.Pos}} => {{.Error.Err}}' ./p
stdout 'b.go:[0-9:]+ => expected ''package'', found ''EOF'''

# 'go test' should report the position of the error if there's only one.
go list -e -test -f '{{if .Error}}{{.Error.Pos}} => {{.Error.Err}}{{end}}' ./t
stdout 't_test.go:[0-9:]+ => expected declaration, found ʕ'

-- go.mod --
module m

go 1.13

-- p/a.go --
package a

import "fmt"

-- p/b.go --
// no package statement

-- t/t_test.go --
package t

import "testing"

func Test(t *testing.T) {}

// scan error
ʕ◔ϖ◔ʔ
