// Code generated by avx512test. DO NOT EDIT.

#include "../../../../../../runtime/textflag.h"

TEXT asmtest_avx512pf(SB), NOSPLIT, $0
	VGATHERPF0DPD K5, (R10)(Y29*8)                     // 6292fd45c60cea
	VGATHERPF0DPD K5, (SP)(Y4*2)                       // 62f2fd4dc60c64
	VGATHERPF0DPD K5, (DX)(Y10*4)                      // 62b2fd4dc60c92
	VGATHERPF0DPS K3, (BP)(Z10*2)                      // 62b27d4bc64c5500
	VGATHERPF0DPS K3, (R10)(Z29*8)                     // 62927d43c60cea
	VGATHERPF0DPS K3, (R14)(Z29*8)                     // 62927d43c60cee
	VGATHERPF0QPD K4, (DX)(Z10*4)                      // 62b2fd4cc70c92
	VGATHERPF0QPD K4, (AX)(Z4*1)                       // 62f2fd4cc70c20
	VGATHERPF0QPD K4, (SP)(Z4*2)                       // 62f2fd4cc70c64
	VGATHERPF0QPS K2, (BP)(Z10*2)                      // 62b27d4ac74c5500
	VGATHERPF0QPS K2, (R10)(Z29*8)                     // 62927d42c70cea
	VGATHERPF0QPS K2, (R14)(Z29*8)                     // 62927d42c70cee
	VGATHERPF1DPD K2, (R14)(Y29*8)                     // 6292fd42c614ee
	VGATHERPF1DPD K2, (AX)(Y4*1)                       // 62f2fd4ac61420
	VGATHERPF1DPD K2, (BP)(Y10*2)                      // 62b2fd4ac6545500
	VGATHERPF1DPS K3, (DX)(Z10*4)                      // 62b27d4bc61492
	VGATHERPF1DPS K3, (AX)(Z4*1)                       // 62f27d4bc61420
	VGATHERPF1DPS K3, (SP)(Z4*2)                       // 62f27d4bc61464
	VGATHERPF1QPD K3, (DX)(Z10*4)                      // 62b2fd4bc71492
	VGATHERPF1QPD K3, (AX)(Z4*1)                       // 62f2fd4bc71420
	VGATHERPF1QPD K3, (SP)(Z4*2)                       // 62f2fd4bc71464
	VGATHERPF1QPS K3, (BP)(Z10*2)                      // 62b27d4bc7545500
	VGATHERPF1QPS K3, (R10)(Z29*8)                     // 62927d43c714ea
	VGATHERPF1QPS K3, (R14)(Z29*8)                     // 62927d43c714ee
	VSCATTERPF0DPD K5, (R10)(Y29*8)                    // 6292fd45c62cea
	VSCATTERPF0DPD K5, (SP)(Y4*2)                      // 62f2fd4dc62c64
	VSCATTERPF0DPD K5, (DX)(Y10*4)                     // 62b2fd4dc62c92
	VSCATTERPF0DPS K3, (DX)(Z10*4)                     // 62b27d4bc62c92
	VSCATTERPF0DPS K3, (AX)(Z4*1)                      // 62f27d4bc62c20
	VSCATTERPF0DPS K3, (SP)(Z4*2)                      // 62f27d4bc62c64
	VSCATTERPF0QPD K4, (DX)(Z10*4)                     // 62b2fd4cc72c92
	VSCATTERPF0QPD K4, (AX)(Z4*1)                      // 62f2fd4cc72c20
	VSCATTERPF0QPD K4, (SP)(Z4*2)                      // 62f2fd4cc72c64
	VSCATTERPF0QPS K2, (BP)(Z10*2)                     // 62b27d4ac76c5500
	VSCATTERPF0QPS K2, (R10)(Z29*8)                    // 62927d42c72cea
	VSCATTERPF0QPS K2, (R14)(Z29*8)                    // 62927d42c72cee
	VSCATTERPF1DPD K2, (R14)(Y29*8)                    // 6292fd42c634ee
	VSCATTERPF1DPD K2, (AX)(Y4*1)                      // 62f2fd4ac63420
	VSCATTERPF1DPD K2, (BP)(Y10*2)                     // 62b2fd4ac6745500
	VSCATTERPF1DPS K3, (BP)(Z10*2)                     // 62b27d4bc6745500
	VSCATTERPF1DPS K3, (R10)(Z29*8)                    // 62927d43c634ea
	VSCATTERPF1DPS K3, (R14)(Z29*8)                    // 62927d43c634ee
	VSCATTERPF1QPD K3, (DX)(Z10*4)                     // 62b2fd4bc73492
	VSCATTERPF1QPD K3, (AX)(Z4*1)                      // 62f2fd4bc73420
	VSCATTERPF1QPD K3, (SP)(Z4*2)                      // 62f2fd4bc73464
	VSCATTERPF1QPS K3, (BP)(Z10*2)                     // 62b27d4bc7745500
	VSCATTERPF1QPS K3, (R10)(Z29*8)                    // 62927d43c734ea
	VSCATTERPF1QPS K3, (R14)(Z29*8)                    // 62927d43c734ee
	RET
