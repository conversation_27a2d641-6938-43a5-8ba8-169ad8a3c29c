@SETLOCAL
@IF NOT DEFINED NODE_PATH (
  @SET "NODE_PATH=D:\learn\uniapptest\testvue3\node_modules\.pnpm\pixelmatch@5.3.0\node_modules\pixelmatch\bin\node_modules;D:\learn\uniapptest\testvue3\node_modules\.pnpm\pixelmatch@5.3.0\node_modules\pixelmatch\node_modules;D:\learn\uniapptest\testvue3\node_modules\.pnpm\pixelmatch@5.3.0\node_modules;D:\learn\uniapptest\testvue3\node_modules\.pnpm\node_modules"
) ELSE (
  @SET "NODE_PATH=D:\learn\uniapptest\testvue3\node_modules\.pnpm\pixelmatch@5.3.0\node_modules\pixelmatch\bin\node_modules;D:\learn\uniapptest\testvue3\node_modules\.pnpm\pixelmatch@5.3.0\node_modules\pixelmatch\node_modules;D:\learn\uniapptest\testvue3\node_modules\.pnpm\pixelmatch@5.3.0\node_modules;D:\learn\uniapptest\testvue3\node_modules\.pnpm\node_modules;%NODE_PATH%"
)
@IF EXIST "%~dp0\node.exe" (
  "%~dp0\node.exe"  "%~dp0\..\..\bin\pixelmatch" %*
) ELSE (
  @SET PATHEXT=%PATHEXT:;.JS;=;%
  node  "%~dp0\..\..\bin\pixelmatch" %*
)
