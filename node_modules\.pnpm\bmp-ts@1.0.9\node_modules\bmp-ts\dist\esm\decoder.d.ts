/// <reference types="node" resolution-mode="require"/>
import { BmpCompression, BmpColor, BmpDecoderOptions, BmpImage } from './types.js';
export default class BmpDecoder implements BmpImage {
    flag: string;
    fileSize: number;
    reserved1: number;
    reserved2: number;
    offset: number;
    headerSize: number;
    width: number;
    height: number;
    planes: number;
    bitPP: number;
    compression?: BmpCompression;
    rawSize: number;
    hr: number;
    vr: number;
    colors: number;
    importantColors: number;
    palette: BmpColor[];
    data: Buffer;
    private maskRed;
    private maskGreen;
    private maskBlue;
    private maskAlpha;
    private readonly toRGBA;
    private pos;
    private bottomUp;
    private readonly buffer;
    private readonly locRed;
    private readonly locGreen;
    private readonly locBlue;
    private readonly locAlpha;
    private shiftRed;
    private shiftGreen;
    private shiftBlue;
    private shiftAlpha;
    constructor(buffer: Buffer, { toRGBA }?: BmpDecoderOptions);
    private parseHeader;
    private parseRGBA;
    private bit1;
    private bit4;
    private bit8;
    private bit16;
    private bit24;
    private bit32;
    private scanImage;
    private readUInt32LE;
    private setPixelData;
}
