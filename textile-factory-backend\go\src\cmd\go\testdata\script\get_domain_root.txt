# Tests issue #9357
# go get foo.io (not foo.io/subdir) was not working consistently.

[!net:go-get-issue-9357.appspot.com] skip
[!git] skip
env GO111MODULE=off

# go-get-issue-9357.appspot.com is running
# the code at github.com/rsc/go-get-issue-9357,
# a trivial Go on App Engine app that serves a
# <meta> tag for the domain root.
go get -d go-get-issue-9357.appspot.com
go get go-get-issue-9357.appspot.com
go get -u go-get-issue-9357.appspot.com

rm $GOPATH/src/go-get-issue-9357.appspot.com
go get go-get-issue-9357.appspot.com

rm $GOPATH/src/go-get-issue-9357.appspot.com
go get -u go-get-issue-9357.appspot.com
