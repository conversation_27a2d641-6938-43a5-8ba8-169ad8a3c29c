# Issue 52863.

# We manually create a .syso and a .a file in package a,
# such that the .syso file only works when linked against the .a file.
# Package a has #cgo LDFLAGS to make this happen.
#
# Package c imports package a, and uses cgo itself.
# The generation of the _cgo_import.go for package c will fail,
# because it won't know that it has to link against a/libb.a
# (because we don't gather the #cgo LDFLAGS from all transitively
# imported packages).
#
# The _cgo_import.go file is only needed for internal linking.
# When generating _cgo_import.go for package c fails, an ordinary
# external link should still work. But an internal link is expected
# to fail, because the failure to create _cgo_import.go should cause
# the linker to report an inability to internally link.

[short] skip
[!cgo] skip
[!exec:ar] skip

cc -c -o a/b.syso b/b.c
cc -c -o b/lib.o b/lib.c
exec ar rc a/libb.a b/lib.o
go build
! go build -ldflags=-linkmode=internal
stderr 'some packages could not be built to support internal linking.*m/c|requires external linking|does not support internal cgo'

-- go.mod --
module m

-- a/a.go --
package a

// #cgo LDFLAGS: -L. -lb
// extern int CFn(int);
import "C"

func GoFn(v int) int { return int(C.CFn(C.int(v))) }

-- b/b.c --
extern int LibFn(int);
int CFn(int i) { return LibFn(i); }

-- b/lib.c --
int LibFn(int i) { return i; }

-- c/c.go --
package c

// static int D(int i) { return i; }
import "C"

import "m/a"

func Fn(i int) (int, int) {
     return a.GoFn(i), int(C.D(C.int(i)))
}

-- main.go --
package main

import "m/c"

func main() {
	println(c.Fn(0))
}
