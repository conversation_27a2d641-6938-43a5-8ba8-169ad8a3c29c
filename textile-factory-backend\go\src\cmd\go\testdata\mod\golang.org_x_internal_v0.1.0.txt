written by hand — loosely derived from golang.org/x/crypto/internal/subtle,
but splitting the internal package across a module boundary

-- .mod --
module golang.org/x/internal
-- .info --
{"Version":"v0.1.0","Name":"","Short":"","Time":"2018-07-25T17:24:00Z"}
-- go.mod --
module golang.org/x/internal
-- subtle/aliasing.go --
// Copyright 2018 The Go Authors. All rights reserved.
// Use of this source code is governed by a BSD-style
// license that can be found in the LICENSE file.

// +build !appengine

// This is a tiny version of golang.org/x/crypto/internal/subtle.

package subtle

import "unsafe"

func AnyOverlap(x, y []byte) bool {
	return len(x) > 0 && len(y) > 0 &&
		uintptr(unsafe.Pointer(&x[0])) <= uintptr(unsafe.Pointer(&y[len(y)-1])) &&
		uintptr(unsafe.Pointer(&y[0])) <= uintptr(unsafe.Pointer(&x[len(x)-1]))
}
-- subtle/aliasing_appengine.go --
// Copyright 2018 The Go Authors. All rights reserved.
// Use of this source code is governed by a BSD-style
// license that can be found in the LICENSE file.

// +build appengine

package subtle

import "reflect"

func AnyOverlap(x, y []byte) bool {
	return len(x) > 0 && len(y) > 0 &&
		reflect.ValueOf(&x[0]).Pointer() <= reflect.ValueOf(&y[len(y)-1]).Pointer() &&
		reflect.ValueOf(&y[0]).Pointer() <= reflect.ValueOf(&x[len(x)-1]).Pointer()
}
