<script lang="ts" setup>
import { ref, onMounted } from 'vue'

// 用户信息
const userInfo = ref({
  username: '',
  role: '',
  roleText: ''
})

// 获取用户信息
const getUserInfo = () => {
  const info = uni.getStorageSync('userInfo')
  if (info) {
    userInfo.value = {
      username: info.username,
      role: info.role,
      roleText: info.role === 'worker' ? '织工' : '机修工'
    }
  }
}

// 退出登录
const handleLogout = () => {
  uni.showModal({
    title: '提示',
    content: '确定要退出登录吗？',
    success: (res) => {
      if (res.confirm) {
        uni.removeStorageSync('userInfo')
        uni.reLaunch({
          url: '/pages/login/login'
        })
      }
    }
  })
}

onMounted(() => {
  getUserInfo()
})
</script>

<template>
  <view class="profile-container">
    <!-- 用户信息卡片 -->
    <view class="user-card">
      <view class="avatar">
        <text class="avatar-text">{{ userInfo.username.charAt(0).toUpperCase() }}</text>
      </view>
      <view class="user-info">
        <text class="username">{{ userInfo.username }}</text>
        <text class="role">{{ userInfo.roleText }}</text>
      </view>
    </view>

    <!-- 功能菜单 -->
    <view class="menu-section">
      <view class="menu-item">
        <view class="menu-icon">📊</view>
        <text class="menu-text">工作统计</text>
        <text class="menu-arrow">></text>
      </view>
      
      <view class="menu-item">
        <view class="menu-icon">⚙️</view>
        <text class="menu-text">设置</text>
        <text class="menu-arrow">></text>
      </view>
      
      <view class="menu-item">
        <view class="menu-icon">❓</view>
        <text class="menu-text">帮助与反馈</text>
        <text class="menu-arrow">></text>
      </view>
      
      <view class="menu-item">
        <view class="menu-icon">ℹ️</view>
        <text class="menu-text">关于我们</text>
        <text class="menu-arrow">></text>
      </view>
    </view>

    <!-- 退出登录 -->
    <view class="logout-section">
      <button class="logout-btn" @click="handleLogout">
        退出登录
      </button>
    </view>
  </view>
</template>

<style lang="scss">
.profile-container {
  min-height: 100vh;
  background: #f5f5f5;
  padding: 20rpx;
}

.user-card {
  background: linear-gradient(135deg, #2196F3 0%, #1976D2 100%);
  border-radius: 20rpx;
  padding: 40rpx;
  margin-bottom: 30rpx;
  display: flex;
  align-items: center;
  box-shadow: 0 8rpx 25rpx rgba(33, 150, 243, 0.3);
}

.avatar {
  width: 120rpx;
  height: 120rpx;
  border-radius: 60rpx;
  background: rgba(255, 255, 255, 0.2);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 30rpx;
}

.avatar-text {
  font-size: 48rpx;
  font-weight: bold;
  color: #ffffff;
}

.user-info {
  flex: 1;
}

.username {
  display: block;
  font-size: 36rpx;
  font-weight: bold;
  color: #ffffff;
  margin-bottom: 10rpx;
}

.role {
  display: block;
  font-size: 28rpx;
  color: rgba(255, 255, 255, 0.8);
}

.menu-section {
  background: #ffffff;
  border-radius: 20rpx;
  margin-bottom: 30rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 15rpx rgba(0, 0, 0, 0.1);
}

.menu-item {
  display: flex;
  align-items: center;
  padding: 30rpx 40rpx;
  border-bottom: 1rpx solid #f0f0f0;
  
  &:last-child {
    border-bottom: none;
  }
  
  &:active {
    background: #f8f8f8;
  }
}

.menu-icon {
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 32rpx;
  margin-right: 30rpx;
}

.menu-text {
  flex: 1;
  font-size: 32rpx;
  color: #333333;
}

.menu-arrow {
  font-size: 28rpx;
  color: #999999;
}

.logout-section {
  padding: 0 20rpx;
}

.logout-btn {
  width: 100%;
  height: 88rpx;
  background: #ffffff;
  color: #ff4757;
  border: 2rpx solid #ff4757;
  border-radius: 12rpx;
  font-size: 32rpx;
  font-weight: bold;
  
  &:active {
    background: #ff4757;
    color: #ffffff;
  }
}
</style>