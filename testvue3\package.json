{"name": "textile-factory-management", "version": "1.0.0", "description": "织厂机器异常管理系统", "main": "main.js", "scripts": {"dev:app": "uni", "dev:app-android": "uni -p app-android", "dev:app-ios": "uni -p app-ios", "dev:custom": "uni -p", "dev:h5": "uni -p h5", "dev:h5:ssr": "uni -p h5:ssr", "dev:mp-alipay": "uni -p mp-alipay", "dev:mp-baidu": "uni -p mp-baidu", "dev:mp-kuaishou": "uni -p mp-kua<PERSON>ou", "dev:mp-lark": "uni -p mp-lark", "dev:mp-qq": "uni -p mp-qq", "dev:mp-toutiao": "uni -p mp-to<PERSON><PERSON>", "dev:mp-weixin": "uni -p mp-weixin", "dev:mp-xhs": "uni -p mp-xhs", "dev:quickapp-webview": "uni -p quickapp-webview", "build:app": "uni build", "build:app-android": "uni build -p app-android", "build:app-ios": "uni build -p app-ios", "build:custom": "uni build -p", "build:h5": "uni build -p h5", "build:h5:ssr": "uni build -p h5:ssr", "build:mp-alipay": "uni build -p mp-alipay", "build:mp-baidu": "uni build -p mp-baidu", "build:mp-kuaishou": "uni build -p mp-kuaishou", "build:mp-lark": "uni build -p mp-lark", "build:mp-qq": "uni build -p mp-qq", "build:mp-toutiao": "uni build -p mp-to<PERSON>ao", "build:mp-weixin": "uni build -p mp-weixin", "build:mp-xhs": "uni build -p mp-xhs", "build:quickapp-webview": "uni build -p quickapp-webview", "type-check": "vue-tsc --noEmit", "server": "node server.js", "dev:all": "concurrently \"npm run server\" \"npm run dev:h5\""}, "dependencies": {"@dcloudio/uni-app": "3.0.0-4020920240930001", "@dcloudio/uni-app-plus": "3.0.0-4020920240930001", "@dcloudio/uni-components": "3.0.0-4020920240930001", "@dcloudio/uni-h5": "3.0.0-4020920240930001", "@dcloudio/uni-mp-alipay": "3.0.0-4020920240930001", "@dcloudio/uni-mp-baidu": "3.0.0-4020920240930001", "@dcloudio/uni-mp-kuaishou": "3.0.0-4020920240930001", "@dcloudio/uni-mp-lark": "3.0.0-4020920240930001", "@dcloudio/uni-mp-qq": "3.0.0-4020920240930001", "@dcloudio/uni-mp-toutiao": "3.0.0-4020920240930001", "@dcloudio/uni-mp-weixin": "3.0.0-4020920240930001", "@dcloudio/uni-mp-xhs": "3.0.0-4020920240930001", "@dcloudio/uni-quickapp-webview": "3.0.0-4020920240930001", "vue": "^3.4.21", "vue-i18n": "^9.1.9", "express": "^4.18.2", "cors": "^2.8.5"}, "devDependencies": {"@dcloudio/types": "^3.4.8", "@dcloudio/uni-automator": "3.0.0-4020920240930001", "@dcloudio/uni-cli-shared": "3.0.0-4020920240930001", "@dcloudio/uni-stacktracey": "3.0.0-4020920240930001", "@dcloudio/vite-plugin-uni": "3.0.0-4020920240930001", "@vue/tsconfig": "^0.5.1", "jimp": "^1.6.0", "sass": "^1.89.2", "typescript": "^5.4.0", "vite": "5.1.4", "vue-tsc": "^2.0.6", "concurrently": "^7.6.0"}, "uni-app": {"scripts": {}}}