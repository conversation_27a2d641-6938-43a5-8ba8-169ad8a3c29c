<script lang="ts" setup>
import { ref, reactive, onLoad } from 'vue'

// 机器信息
const machineInfo = ref<any>({})

// 异常上报表单
const reportForm = reactive({
  machineCode: '',
  description: '',
  severity: 'medium', // low: 轻微, medium: 中等, high: 严重, critical: 紧急
  images: [] as string[],
  remarks: ''
})

// 严重程度选项
const severityOptions = [
  { value: 'low', label: '轻微', color: '#4CAF50' },
  { value: 'medium', label: '中等', color: '#FF9800' },
  { value: 'high', label: '严重', color: '#F44336' },
  { value: 'critical', label: '紧急', color: '#9C27B0' }
]

// 提交状态
const isSubmitting = ref(false)

// 模拟机器数据
const mockMachineData = {
  'M001': {
    code: 'M001',
    name: '织机A-01',
    location: 'A区-1号位',
    model: 'TX-2000',
    installDate: '2023-03-15',
    lastMaintenance: '2024-01-10'
  },
  'M002': {
    code: 'M002',
    name: '织机B-03',
    location: 'B区-3号位',
    model: 'TX-2000',
    installDate: '2023-04-20',
    lastMaintenance: '2024-01-12'
  },
  'M003': {
    code: 'M003',
    name: '织机C-05',
    location: 'C区-5号位',
    model: 'TX-3000',
    installDate: '2023-05-10',
    lastMaintenance: '2024-01-14'
  }
}

// 获取严重程度信息
const getSeverityInfo = (severity: string) => {
  return severityOptions.find(item => item.value === severity) || severityOptions[1]
}

// 严重程度选择变化
const onSeverityChange = (e: any) => {
  reportForm.severity = e.detail.value
}

// 选择图片
const chooseImage = () => {
  uni.chooseImage({
    count: 3,
    sizeType: ['compressed'],
    sourceType: ['camera', 'album'],
    success: (res) => {
      reportForm.images = [...reportForm.images, ...res.tempFilePaths].slice(0, 3)
    },
    fail: (err) => {
      console.error('选择图片失败:', err)
      uni.showToast({
        title: '选择图片失败',
        icon: 'none'
      })
    }
  })
}

// 删除图片
const removeImage = (index: number) => {
  reportForm.images.splice(index, 1)
}

// 预览图片
const previewImage = (index: number) => {
  uni.previewImage({
    urls: reportForm.images,
    current: index
  })
}

// 提交异常上报
const submitReport = async () => {
  if (!reportForm.description.trim()) {
    uni.showToast({
      title: '请填写异常描述',
      icon: 'none'
    })
    return
  }

  isSubmitting.value = true
  
  try {
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 2000))
    
    // 构建上报数据
    const reportData = {
      machineCode: reportForm.machineCode,
      machineName: machineInfo.value.name,
      description: reportForm.description,
      severity: reportForm.severity,
      images: reportForm.images,
      remarks: reportForm.remarks,
      reportTime: new Date().toLocaleString(),
      reporter: uni.getStorageSync('userInfo')?.username || '未知用户'
    }
    
    console.log('上报数据:', reportData)
    
    uni.showToast({
      title: '上报成功',
      icon: 'success'
    })
    
    // 延迟返回上一页
    setTimeout(() => {
      uni.navigateBack()
    }, 1500)
    
  } catch (error) {
    console.error('上报失败:', error)
    uni.showToast({
      title: '上报失败，请重试',
      icon: 'none'
    })
  } finally {
    isSubmitting.value = false
  }
}

// 页面加载时处理参数
onLoad((options: any) => {
  if (options.machineCode) {
    reportForm.machineCode = options.machineCode
    
    // 根据机器码获取机器信息
    const machine = mockMachineData[options.machineCode as keyof typeof mockMachineData]
    if (machine) {
      machineInfo.value = machine
    } else {
      // 如果没有找到机器信息，显示基本信息
      machineInfo.value = {
        code: options.machineCode,
        name: `机器-${options.machineCode}`,
        location: '未知位置',
        model: '未知型号'
      }
    }
  } else {
    // 如果没有机器码，返回上一页
    uni.showToast({
      title: '缺少机器信息',
      icon: 'none'
    })
    setTimeout(() => {
      uni.navigateBack()
    }, 1500)
  }
})
</script>

<template>
  <view class="report-page">
    <!-- 机器信息卡片 -->
    <view class="machine-card">
      <view class="card-header">
        <text class="card-title">机器信息</text>
        <view class="machine-code">
          <text class="code-text">{{ machineInfo.code }}</text>
        </view>
      </view>
      
      <view class="machine-details">
        <view class="detail-row">
          <text class="detail-label">设备名称：</text>
          <text class="detail-value">{{ machineInfo.name }}</text>
        </view>
        <view class="detail-row">
          <text class="detail-label">设备位置：</text>
          <text class="detail-value">{{ machineInfo.location }}</text>
        </view>
        <view class="detail-row" v-if="machineInfo.model">
          <text class="detail-label">设备型号：</text>
          <text class="detail-value">{{ machineInfo.model }}</text>
        </view>
        <view class="detail-row" v-if="machineInfo.lastMaintenance">
          <text class="detail-label">上次维护：</text>
          <text class="detail-value">{{ machineInfo.lastMaintenance }}</text>
        </view>
      </view>
    </view>
    
    <!-- 异常上报表单 -->
    <view class="form-container">
      <view class="form-section">
        <text class="section-title">异常描述 *</text>
        <textarea 
          class="description-input"
          v-model="reportForm.description"
          placeholder="请详细描述机器异常现象，如异响、温度异常、运行不稳定等"
          placeholder-class="placeholder"
          maxlength="500"
          show-confirm-bar="false"
        ></textarea>
        <text class="char-count">{{ reportForm.description.length }}/500</text>
      </view>
      
      <view class="form-section">
        <text class="section-title">严重程度</text>
        <picker 
          class="severity-picker"
          :value="reportForm.severity"
          :range="severityOptions"
          range-key="label"
          @change="onSeverityChange"
        >
          <view class="picker-content">
            <view 
              class="severity-tag"
              :style="{ backgroundColor: getSeverityInfo(reportForm.severity).color }"
            >
              <text class="severity-text">{{ getSeverityInfo(reportForm.severity).label }}</text>
            </view>
            <text class="picker-arrow">▼</text>
          </view>
        </picker>
      </view>
      
      <view class="form-section">
        <text class="section-title">现场照片 (最多3张)</text>
        <view class="image-section">
          <view class="image-list">
            <view 
              class="image-item" 
              v-for="(image, index) in reportForm.images" 
              :key="index"
              @click="previewImage(index)"
            >
              <image class="image" :src="image" mode="aspectFill"></image>
              <view class="image-remove" @click.stop="removeImage(index)">
                <text class="remove-icon">×</text>
              </view>
            </view>
            
            <view 
              class="image-add" 
              v-if="reportForm.images.length < 3"
              @click="chooseImage"
            >
              <text class="add-icon">+</text>
              <text class="add-text">添加照片</text>
            </view>
          </view>
        </view>
      </view>
      
      <view class="form-section">
        <text class="section-title">备注信息</text>
        <textarea 
          class="remarks-input"
          v-model="reportForm.remarks"
          placeholder="其他需要说明的信息（选填）"
          placeholder-class="placeholder"
          maxlength="200"
          show-confirm-bar="false"
        ></textarea>
        <text class="char-count">{{ reportForm.remarks.length }}/200</text>
      </view>
    </view>
    
    <!-- 提交按钮 -->
    <view class="submit-section">
      <button 
        class="submit-btn"
        :class="{ 'submitting': isSubmitting }"
        @click="submitReport"
        :disabled="isSubmitting"
      >
        {{ isSubmitting ? '提交中...' : '提交异常上报' }}
      </button>
    </view>
  </view>
</template>

<style lang="scss">
.report-page {
  min-height: 100vh;
  background: #f5f5f5;
  padding: 30rpx;
  padding-bottom: 120rpx;
}

.machine-card {
  background: #ffffff;
  border-radius: 16rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24rpx;
}

.card-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333333;
}

.machine-code {
  background: linear-gradient(135deg, #2196F3 0%, #1976D2 100%);
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
}

.code-text {
  font-size: 24rpx;
  color: #ffffff;
  font-weight: 500;
}

.machine-details {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

.detail-row {
  display: flex;
  align-items: center;
}

.detail-label {
  font-size: 26rpx;
  color: #666666;
  width: 160rpx;
}

.detail-value {
  font-size: 26rpx;
  color: #333333;
  flex: 1;
}

.form-container {
  display: flex;
  flex-direction: column;
  gap: 30rpx;
}

.form-section {
  background: #ffffff;
  border-radius: 16rpx;
  padding: 30rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.section-title {
  font-size: 28rpx;
  font-weight: bold;
  color: #333333;
  margin-bottom: 20rpx;
  display: block;
}

.description-input,
.remarks-input {
  width: 100%;
  min-height: 200rpx;
  border: 2rpx solid #e0e0e0;
  border-radius: 12rpx;
  padding: 20rpx;
  font-size: 26rpx;
  color: #333333;
  background: #fafafa;
  line-height: 1.5;
  
  &:focus {
    border-color: #2196F3;
    background: #ffffff;
  }
}

.remarks-input {
  min-height: 120rpx;
}

.placeholder {
  color: #999999;
}

.char-count {
  font-size: 22rpx;
  color: #999999;
  text-align: right;
  margin-top: 12rpx;
}

.severity-picker {
  width: 100%;
}

.picker-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx;
  border: 2rpx solid #e0e0e0;
  border-radius: 12rpx;
  background: #fafafa;
}

.severity-tag {
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
}

.severity-text {
  font-size: 24rpx;
  color: #ffffff;
  font-weight: 500;
}

.picker-arrow {
  font-size: 20rpx;
  color: #999999;
}

.image-section {
  margin-top: 20rpx;
}

.image-list {
  display: flex;
  flex-wrap: wrap;
  gap: 20rpx;
}

.image-item {
  position: relative;
  width: 200rpx;
  height: 200rpx;
  border-radius: 12rpx;
  overflow: hidden;
}

.image {
  width: 100%;
  height: 100%;
}

.image-remove {
  position: absolute;
  top: 8rpx;
  right: 8rpx;
  width: 40rpx;
  height: 40rpx;
  background: rgba(0, 0, 0, 0.6);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.remove-icon {
  font-size: 24rpx;
  color: #ffffff;
  font-weight: bold;
}

.image-add {
  width: 200rpx;
  height: 200rpx;
  border: 2rpx dashed #cccccc;
  border-radius: 12rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background: #fafafa;
  
  &:active {
    background: #f0f0f0;
  }
}

.add-icon {
  font-size: 48rpx;
  color: #cccccc;
  margin-bottom: 8rpx;
}

.add-text {
  font-size: 22rpx;
  color: #999999;
}

.submit-section {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: #ffffff;
  padding: 30rpx;
  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.1);
}

.submit-btn {
  width: 100%;
  height: 88rpx;
  background: linear-gradient(135deg, #4CAF50 0%, #388E3C 100%);
  color: #ffffff;
  border: none;
  border-radius: 12rpx;
  font-size: 32rpx;
  font-weight: bold;
  box-shadow: 0 6rpx 20rpx rgba(76, 175, 80, 0.3);
  
  &:active {
    transform: translateY(2rpx);
  }
  
  &.submitting {
    background: #cccccc;
    box-shadow: none;
  }
  
  &[disabled] {
    background: #cccccc;
    box-shadow: none;
  }
}
</style>