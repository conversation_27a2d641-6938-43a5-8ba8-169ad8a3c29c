// Code generated by avx512test. DO NOT EDIT.

#include "../../../../../../runtime/textflag.h"

TEXT asmtest_vpclmulqdq_avx512f(SB), NOSPLIT, $0
	VPCLMULQDQ $127, X22, X21, X15                     // 6233550044fe7f or 6233d50044fe7f
	VPCLMULQDQ $127, X7, X21, X15                      // 6273550044ff7f or 6273d50044ff7f
	VPCLMULQDQ $127, X19, X21, X15                     // 6233550044fb7f or 6233d50044fb7f
	VPCLMULQDQ $127, -17(BP)(SI*8), X21, X15           // 6273550044bcf5efffffff7f or 6273d50044bcf5efffffff7f
	VPCLMULQDQ $127, (R15), X21, X15                   // 62535500443f7f or 6253d500443f7f
	VPCLMULQDQ $127, X22, X0, X15                      // 62337d0844fe7f or 6233fd0844fe7f
	VPCLMULQDQ $127, X19, X0, X15                      // 62337d0844fb7f or 6233fd0844fb7f
	VPCLMULQDQ $127, X22, X28, X15                     // 62331d0044fe7f or 62339d0044fe7f
	VPCLMULQDQ $127, X7, X28, X15                      // 62731d0044ff7f or 62739d0044ff7f
	VPCLMULQDQ $127, X19, X28, X15                     // 62331d0044fb7f or 62339d0044fb7f
	VPCLMULQDQ $127, -17(BP)(SI*8), X28, X15           // 62731d0044bcf5efffffff7f or 62739d0044bcf5efffffff7f
	VPCLMULQDQ $127, (R15), X28, X15                   // 62531d00443f7f or 62539d00443f7f
	VPCLMULQDQ $127, X22, X21, X0                      // 62b3550044c67f or 62b3d50044c67f
	VPCLMULQDQ $127, X7, X21, X0                       // 62f3550044c77f or 62f3d50044c77f
	VPCLMULQDQ $127, X19, X21, X0                      // 62b3550044c37f or 62b3d50044c37f
	VPCLMULQDQ $127, -17(BP)(SI*8), X21, X0            // 62f355004484f5efffffff7f or 62f3d5004484f5efffffff7f
	VPCLMULQDQ $127, (R15), X21, X0                    // 62d3550044077f or 62d3d50044077f
	VPCLMULQDQ $127, X22, X0, X0                       // 62b37d0844c67f or 62b3fd0844c67f
	VPCLMULQDQ $127, X19, X0, X0                       // 62b37d0844c37f or 62b3fd0844c37f
	VPCLMULQDQ $127, X22, X28, X0                      // 62b31d0044c67f or 62b39d0044c67f
	VPCLMULQDQ $127, X7, X28, X0                       // 62f31d0044c77f or 62f39d0044c77f
	VPCLMULQDQ $127, X19, X28, X0                      // 62b31d0044c37f or 62b39d0044c37f
	VPCLMULQDQ $127, -17(BP)(SI*8), X28, X0            // 62f31d004484f5efffffff7f or 62f39d004484f5efffffff7f
	VPCLMULQDQ $127, (R15), X28, X0                    // 62d31d0044077f or 62d39d0044077f
	VPCLMULQDQ $127, X22, X21, X16                     // 62a3550044c67f or 62a3d50044c67f
	VPCLMULQDQ $127, X7, X21, X16                      // 62e3550044c77f or 62e3d50044c77f
	VPCLMULQDQ $127, X19, X21, X16                     // 62a3550044c37f or 62a3d50044c37f
	VPCLMULQDQ $127, -17(BP)(SI*8), X21, X16           // 62e355004484f5efffffff7f or 62e3d5004484f5efffffff7f
	VPCLMULQDQ $127, (R15), X21, X16                   // 62c3550044077f or 62c3d50044077f
	VPCLMULQDQ $127, X22, X0, X16                      // 62a37d0844c67f or 62a3fd0844c67f
	VPCLMULQDQ $127, X7, X0, X16                       // 62e37d0844c77f or 62e3fd0844c77f
	VPCLMULQDQ $127, X19, X0, X16                      // 62a37d0844c37f or 62a3fd0844c37f
	VPCLMULQDQ $127, -17(BP)(SI*8), X0, X16            // 62e37d084484f5efffffff7f or 62e3fd084484f5efffffff7f
	VPCLMULQDQ $127, (R15), X0, X16                    // 62c37d0844077f or 62c3fd0844077f
	VPCLMULQDQ $127, X22, X28, X16                     // 62a31d0044c67f or 62a39d0044c67f
	VPCLMULQDQ $127, X7, X28, X16                      // 62e31d0044c77f or 62e39d0044c77f
	VPCLMULQDQ $127, X19, X28, X16                     // 62a31d0044c37f or 62a39d0044c37f
	VPCLMULQDQ $127, -17(BP)(SI*8), X28, X16           // 62e31d004484f5efffffff7f or 62e39d004484f5efffffff7f
	VPCLMULQDQ $127, (R15), X28, X16                   // 62c31d0044077f or 62c39d0044077f
	VPCLMULQDQ $0, Y15, Y2, Y31                        // 62436d2844ff00 or 6243ed2844ff00
	VPCLMULQDQ $0, Y22, Y2, Y31                        // 62236d2844fe00 or 6223ed2844fe00
	VPCLMULQDQ $0, Y20, Y2, Y31                        // 62236d2844fc00 or 6223ed2844fc00
	VPCLMULQDQ $0, 99(R15)(R15*4), Y2, Y31             // 62036d2844bcbf6300000000 or 6203ed2844bcbf6300000000
	VPCLMULQDQ $0, 15(DX), Y2, Y31                     // 62636d2844ba0f00000000 or 6263ed2844ba0f00000000
	VPCLMULQDQ $0, Y15, Y13, Y31                       // 6243152844ff00 or 6243952844ff00
	VPCLMULQDQ $0, Y22, Y13, Y31                       // 6223152844fe00 or 6223952844fe00
	VPCLMULQDQ $0, Y20, Y13, Y31                       // 6223152844fc00 or 6223952844fc00
	VPCLMULQDQ $0, 99(R15)(R15*4), Y13, Y31            // 6203152844bcbf6300000000 or 6203952844bcbf6300000000
	VPCLMULQDQ $0, 15(DX), Y13, Y31                    // 6263152844ba0f00000000 or 6263952844ba0f00000000
	VPCLMULQDQ $0, Y15, Y27, Y31                       // 6243252044ff00 or 6243a52044ff00
	VPCLMULQDQ $0, Y22, Y27, Y31                       // 6223252044fe00 or 6223a52044fe00
	VPCLMULQDQ $0, Y20, Y27, Y31                       // 6223252044fc00 or 6223a52044fc00
	VPCLMULQDQ $0, 99(R15)(R15*4), Y27, Y31            // 6203252044bcbf6300000000 or 6203a52044bcbf6300000000
	VPCLMULQDQ $0, 15(DX), Y27, Y31                    // 6263252044ba0f00000000 or 6263a52044ba0f00000000
	VPCLMULQDQ $0, Y22, Y2, Y3                         // 62b36d2844de00 or 62b3ed2844de00
	VPCLMULQDQ $0, Y20, Y2, Y3                         // 62b36d2844dc00 or 62b3ed2844dc00
	VPCLMULQDQ $0, Y22, Y13, Y3                        // 62b3152844de00 or 62b3952844de00
	VPCLMULQDQ $0, Y20, Y13, Y3                        // 62b3152844dc00 or 62b3952844dc00
	VPCLMULQDQ $0, Y15, Y27, Y3                        // 62d3252044df00 or 62d3a52044df00
	VPCLMULQDQ $0, Y22, Y27, Y3                        // 62b3252044de00 or 62b3a52044de00
	VPCLMULQDQ $0, Y20, Y27, Y3                        // 62b3252044dc00 or 62b3a52044dc00
	VPCLMULQDQ $0, 99(R15)(R15*4), Y27, Y3             // 62932520449cbf6300000000 or 6293a520449cbf6300000000
	VPCLMULQDQ $0, 15(DX), Y27, Y3                     // 62f32520449a0f00000000 or 62f3a520449a0f00000000
	VPCLMULQDQ $0, Y22, Y2, Y14                        // 62336d2844f600 or 6233ed2844f600
	VPCLMULQDQ $0, Y20, Y2, Y14                        // 62336d2844f400 or 6233ed2844f400
	VPCLMULQDQ $0, Y22, Y13, Y14                       // 6233152844f600 or 6233952844f600
	VPCLMULQDQ $0, Y20, Y13, Y14                       // 6233152844f400 or 6233952844f400
	VPCLMULQDQ $0, Y15, Y27, Y14                       // 6253252044f700 or 6253a52044f700
	VPCLMULQDQ $0, Y22, Y27, Y14                       // 6233252044f600 or 6233a52044f600
	VPCLMULQDQ $0, Y20, Y27, Y14                       // 6233252044f400 or 6233a52044f400
	VPCLMULQDQ $0, 99(R15)(R15*4), Y27, Y14            // 6213252044b4bf6300000000 or 6213a52044b4bf6300000000
	VPCLMULQDQ $0, 15(DX), Y27, Y14                    // 6273252044b20f00000000 or 6273a52044b20f00000000
	VPCLMULQDQ $97, Z9, Z0, Z24                        // 62437d4844c161 or 6243fd4844c161
	VPCLMULQDQ $97, Z3, Z0, Z24                        // 62637d4844c361 or 6263fd4844c361
	VPCLMULQDQ $97, 7(SI)(DI*1), Z0, Z24               // 62637d4844843e0700000061 or 6263fd4844843e0700000061
	VPCLMULQDQ $97, 15(DX)(BX*8), Z0, Z24              // 62637d484484da0f00000061 or 6263fd484484da0f00000061
	VPCLMULQDQ $97, Z9, Z26, Z24                       // 62432d4044c161 or 6243ad4044c161
	VPCLMULQDQ $97, Z3, Z26, Z24                       // 62632d4044c361 or 6263ad4044c361
	VPCLMULQDQ $97, 7(SI)(DI*1), Z26, Z24              // 62632d4044843e0700000061 or 6263ad4044843e0700000061
	VPCLMULQDQ $97, 15(DX)(BX*8), Z26, Z24             // 62632d404484da0f00000061 or 6263ad404484da0f00000061
	VPCLMULQDQ $97, Z9, Z0, Z12                        // 62537d4844e161 or 6253fd4844e161
	VPCLMULQDQ $97, Z3, Z0, Z12                        // 62737d4844e361 or 6273fd4844e361
	VPCLMULQDQ $97, 7(SI)(DI*1), Z0, Z12               // 62737d4844a43e0700000061 or 6273fd4844a43e0700000061
	VPCLMULQDQ $97, 15(DX)(BX*8), Z0, Z12              // 62737d4844a4da0f00000061 or 6273fd4844a4da0f00000061
	VPCLMULQDQ $97, Z9, Z26, Z12                       // 62532d4044e161 or 6253ad4044e161
	VPCLMULQDQ $97, Z3, Z26, Z12                       // 62732d4044e361 or 6273ad4044e361
	VPCLMULQDQ $97, 7(SI)(DI*1), Z26, Z12              // 62732d4044a43e0700000061 or 6273ad4044a43e0700000061
	VPCLMULQDQ $97, 15(DX)(BX*8), Z26, Z12             // 62732d4044a4da0f00000061 or 6273ad4044a4da0f00000061
	RET
