Android
=======

For details on developing Go for Android, see the documentation in the
mobile subrepository:

	https://github.com/golang/mobile

To run the standard library tests, enable Cgo and use an appropriate
C compiler from the Android NDK. For example,

	CGO_ENABLED=1 \
	GOOS=android \
	GOARCH=arm64 \
	CC_FOR_TARGET=$NDK/toolchains/llvm/prebuilt/linux-x86_64/bin/aarch64-linux-android21-clang \
	./all.bash

To run tests on the Android device, add the bin directory to PATH so the
go tool can find the go_android_$GOARCH_exec wrapper generated by
make.bash. For example, to run the go1 benchmarks

	export PATH=$GOROOT/bin:$PATH
	cd $GOROOT/test/bench/go1/
	GOOS=android GOARCH=arm64 go test -bench=. -count=N -timeout=T

