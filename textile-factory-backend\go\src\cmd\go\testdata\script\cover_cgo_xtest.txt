[short] skip
[!cgo] skip
[compiler:gccgo] skip # gccgo has no cover tool

# Test cgo coverage with an external test.

go test -short -cover cgocover2
stdout  'coverage:.*[1-9][0-9.]+%'
! stderr '[^0-9]0\.0%'

-- go.mod --
module cgocover2

go 1.16
-- p.go --
package p

/*
void
f(void)
{
}
*/
import "C"

var b bool

func F() {
	if b {
		for {
		}
	}
	C.f()
}
-- x_test.go --
package p_test

import (
	. "cgocover2"
	"testing"
)

func TestF(t *testing.T) {
	F()
}
