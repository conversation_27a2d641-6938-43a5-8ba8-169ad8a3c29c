[!net:github.com] skip
[!net:golang.org] skip
[!net:gopkg.in] skip
[!git] skip

env GO111MODULE=on
env GOPROXY=
env GOSUMDB=

go mod download github.com/docker/distribution@v0.0.0-20150410205453-85de3967aa93
mkdir x/Godeps
cp $GOPATH/pkg/mod/github.com/docker/distribution@v0.0.0-20150410205453-85de3967aa93/Godeps/Godeps.json x/Godeps
cd x
go mod init github.com/docker/distribution
cmpenv go.mod go.mod.want

[!net:google.golang.org] skip
[!net:cloud.google.com] skip

go mod download github.com/fishy/gcsbucket@v0.0.0-20180217031846-618d60fe84e0
cp $GOPATH/pkg/mod/github.com/fishy/gcsbucket@v0.0.0-20180217031846-618d60fe84e0/Gopkg.lock ../y
cd ../y
go mod init github.com/fishy/gcsbucket
cmpenv go.mod go.mod.want

-- x/go.mod.want --
module github.com/docker/distribution

go $goversion

require (
	github.com/AdRoll/goamz v0.0.0-20150130162828-d3664b76d905
	github.com/MSOpenTech/azure-sdk-for-go v0.0.0-20150323223030-d90753bcad2e
	github.com/Sirupsen/logrus v0.7.3
	github.com/bugsnag/bugsnag-go v1.0.3-0.20141110184014-b1d153021fcd
	github.com/bugsnag/osext v0.0.0-20130617224835-0dd3f918b21b
	github.com/bugsnag/panicwrap v0.0.0-20141110184334-e5f9854865b9
	github.com/codegangsta/cli v1.4.2-0.20150131031259-6086d7927ec3
	github.com/docker/docker v1.4.2-0.20150204013315-165ea5c158cf
	github.com/docker/libtrust v0.0.0-20150114040149-fa567046d9b1
	github.com/garyburd/redigo v0.0.0-20150301180006-535138d7bcd7
	github.com/gorilla/context v0.0.0-20140604161150-14f550f51af5
	github.com/gorilla/handlers v0.0.0-20140825150757-0e84b7d810c1
	github.com/gorilla/mux v0.0.0-20140926153814-e444e69cbd2e
	github.com/jlhawn/go-crypto v0.0.0-20150401213827-cd738dde20f0
	github.com/yvasiyarov/go-metrics v0.0.0-20140926110328-57bccd1ccd43
	github.com/yvasiyarov/gorelic v0.0.7-0.20141212073537-a9bba5b9ab50
	github.com/yvasiyarov/newrelic_platform_go v0.0.0-20140908184405-b21fdbd4370f
	golang.org/x/net v0.0.0-20150202051010-1dfe7915deaf
	gopkg.in/check.v1 v1.0.0-20141024133853-64131543e789
	gopkg.in/yaml.v2 v2.0.0-20150116202057-bef53efd0c76
)
-- y/go.mod.want --
module github.com/fishy/gcsbucket

go $goversion

require (
	cloud.google.com/go v0.18.0
	github.com/fishy/fsdb v0.0.0-20180217030800-5527ded01371
	github.com/golang/protobuf v1.0.0
	github.com/googleapis/gax-go v2.0.0+incompatible
	golang.org/x/net v0.0.0-20180216171745-136a25c244d3
	golang.org/x/oauth2 v0.0.0-20180207181906-543e37812f10
	golang.org/x/text v0.3.1-0.20180208041248-4e4a3210bb54
	google.golang.org/api v0.0.0-20180217000815-c7a403bb5fe1
	google.golang.org/appengine v1.0.0
	google.golang.org/genproto v0.0.0-20180206005123-2b5a72b8730b
	google.golang.org/grpc v1.10.0
)
