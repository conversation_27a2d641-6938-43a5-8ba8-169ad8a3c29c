[short] skip

# Install an echo command because Windows doesn't have it.
env GOBIN=$WORK/tmp/bin
go install echo.go
env PATH=$GOBIN${:}$PATH

# Test go generate handles a simple command
go generate ./generate/simple.go
stdout 'Success'

# Test go generate handles a command alias
go generate './generate/alias.go'
stdout 'Now is the time for all good men'

# Test go generate's variable substitution
go generate './generate/substitution.go'
stdout $GOARCH' substitution.go:7 pabc xyzp/substitution.go/123'

# Test go generate's run and skip flags
go generate -run y.s './generate/flag.go'
stdout 'yes' # flag.go should select yes
! stdout 'no' # flag.go should not select no

go generate -skip th..sand './generate/flag.go'
stdout 'yes' # flag.go should select yes
! stdout 'no' # flag.go should not select no

go generate -run . -skip th..sand './generate/flag.go'
stdout 'yes' # flag.go should select yes
! stdout 'no' # flag.go should not select no

# Test go generate provides the right "$GOPACKAGE" name in an x_test
go generate './generate/env_test.go'
stdout 'main_test'

# Test go generate provides the right "$PWD"
go generate './generate/env_pwd.go'
stdout $WORK'[/\\]gopath[/\\]src[/\\]generate'

-- echo.go --
package main

import (
	"fmt"
	"os"
	"strings"
)

func main() {
	fmt.Println(strings.Join(os.Args[1:], " "))
	fmt.Println()
}
-- generate/simple.go --
// Copyright 2014 The Go Authors. All rights reserved.
// Use of this source code is governed by a BSD-style
// license that can be found in the LICENSE file.

// Simple test for go generate.

// We include a build tag that go generate should ignore.

// +build ignore

//go:generate echo Success

package p
-- generate/alias.go --
// Copyright 2014 The Go Authors. All rights reserved.
// Use of this source code is governed by a BSD-style
// license that can be found in the LICENSE file.

// Test that go generate handles command aliases.

//go:generate -command run echo Now is the time
//go:generate run for all good men

package p
-- generate/substitution.go --
// Copyright 2014 The Go Authors. All rights reserved.
// Use of this source code is governed by a BSD-style
// license that can be found in the LICENSE file.

// Test go generate variable substitution.

//go:generate echo $GOARCH $GOFILE:$GOLINE ${GOPACKAGE}abc xyz$GOPACKAGE/$GOFILE/123

package p
-- generate/flag.go --
// Copyright 2015 The Go Authors. All rights reserved.
// Use of this source code is governed by a BSD-style
// license that can be found in the LICENSE file.

// Test -run flag

//go:generate echo oh yes my man
//go:generate echo no, no, a thousand times no

package p
-- generate/env_test.go --
package main_test

//go:generate echo $GOPACKAGE
-- generate/env_pwd.go --
package p

//go:generate echo $PWD
