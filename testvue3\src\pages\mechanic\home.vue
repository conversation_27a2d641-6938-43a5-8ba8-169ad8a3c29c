<script lang="ts" setup>
import { ref, onMounted, onUnmounted } from 'vue'
import scannerUtils from '@/utils/scannerUtils.js'
import { API } from '@/utils/api.js'

// 用户信息
const userInfo = ref<any>({})

// 机器列表数据
const machineList = ref([])
const isLoadingMachines = ref(false)

// 统计数据
const stats = ref({
  total: 0,
  normal: 0,
  warning: 0,
  error: 0,
  maintenance: 0
})

// 获取状态信息
const getStatusInfo = (status: string) => {
  const statusMap = {
    '正常': { text: '正常', color: '#4CAF50', bgColor: '#E8F5E8' },
    '异常': { text: '异常', color: '#F44336', bgColor: '#FFEBEE' },
    '维修中': { text: '维修中', color: '#2196F3', bgColor: '#E3F2FD' }
  }
  return statusMap[status as keyof typeof statusMap] || { text: '未知', color: '#999999', bgColor: '#F5F5F5' }
}

// 扫码维修
const handleScanRepair = async () => {
  try {
    // 使用维修模式初始化扫码功能
    const initResult = await scannerUtils.initScanner((result) => {
      if (result.success) {
        console.log('扫码结果:', result)
        
        if (result.apiError) {
          // API调用失败，但仍可以跳转到维修页面
          uni.showToast({
            title: result.apiError,
            icon: 'none',
            duration: 2000
          })
          // 跳转到维修操作页面，传递机器码
          uni.navigateTo({
            url: `/pages/repair/repair?qrCode=${result.result}`
          })
        } else if (result.data) {
          // API调用成功，显示机器的异常信息
          const { machine, pending_anomalies, repairing_anomalies } = result.data
          
          if (pending_anomalies.length > 0 || repairing_anomalies.length > 0) {
            // 有待维修或维修中的异常，可以进行维修
            uni.navigateTo({
              url: `/pages/repair/repair?qrCode=${result.result}&machineData=${encodeURIComponent(JSON.stringify(result.data))}`
            })
          } else {
            // 没有异常需要维修
            uni.showModal({
              title: '提示',
              content: `机器 ${machine.name} 当前状态正常，无需维修`,
              showCancel: false
            })
          }
        }
      } else {
        console.error('扫码失败:', result.error)
        uni.showToast({
          title: result.error || '扫码失败',
          icon: 'none'
        })
      }
    }, true, 'repair') // 设置为维修模式
    
    if (initResult.success) {
      uni.showToast({
        title: '请扫描机器二维码',
        icon: 'none',
        duration: 2000
      })
    }
  } catch (error) {
    console.error('初始化扫码失败:', error)
    uni.showToast({
      title: '扫码功能初始化失败',
      icon: 'none'
    })
  }
}

// 查看机器详情
const viewMachineDetail = (machine: any) => {
  const statusInfo = getStatusInfo(machine.status)
  let content = `位置：${machine.location}\n状态：${statusInfo.text}`
  
  if (machine.anomaly_count > 0) {
    content += `\n异常数量：${machine.anomaly_count}个`
  }
  
  uni.showModal({
    title: machine.name,
    content: content,
    showCancel: true,
    confirmText: '查看异常',
    cancelText: '关闭',
    success: (res) => {
      if (res.confirm && machine.anomaly_count > 0) {
        // 查看机器的异常列表
        uni.navigateTo({
          url: `/pages/repair/list?machineId=${machine.id}`
        })
      }
    }
  })
}

// 开始维修
const startRepair = (machine: any) => {
  uni.navigateTo({
    url: `/pages/repair/repair?machineId=${machine.id}&machineCode=${machine.code}`
  })
}

// 加载机器列表
const loadMachineList = async () => {
  try {
    isLoadingMachines.value = true
    const response = await API.machines.getList({
      page: 1,
      page_size: 50 // 加载所有机器
    })
    
    if (response && response.machines) {
      // 处理机器数据，添加异常数量信息
      machineList.value = response.machines.map(machine => ({
        ...machine,
        anomaly_count: parseInt(machine.qr_code) || 0 // 后端临时将异常数量存在qr_code字段
      }))
      
      // 更新统计数据
      updateStats()
    }
  } catch (error) {
    console.error('加载机器列表失败:', error)
    uni.showToast({
      title: '加载机器列表失败',
      icon: 'none'
    })
  } finally {
    isLoadingMachines.value = false
  }
}

// 更新统计数据
const updateStats = () => {
  const total = machineList.value.length
  const normal = machineList.value.filter(m => m.status === '正常').length
  const error = machineList.value.filter(m => m.status === '异常').length
  const maintenance = machineList.value.filter(m => m.status === '维修中').length
  
  stats.value = {
    total,
    normal,
    warning: 0, // 暂时不使用警告状态
    error,
    maintenance
  }
}

// 获取用户信息
const loadUserInfo = async () => {
  try {
    const response = await API.auth.getUserInfo()
    if (response) {
      userInfo.value = response
      uni.setStorageSync('userInfo', response)
    }
  } catch (error) {
    console.error('获取用户信息失败:', error)
    // 如果获取用户信息失败，可能是token过期，跳转到登录页
    uni.reLaunch({
      url: '/pages/login/login'
    })
  }
}

// 退出登录
const handleLogout = () => {
  uni.showModal({
    title: '确认退出',
    content: '确定要退出登录吗？',
    success: (res) => {
      if (res.confirm) {
        // 清除本地存储的认证信息
        uni.removeStorageSync('userInfo')
        uni.removeStorageSync('token')
        uni.reLaunch({
          url: '/pages/login/login'
        })
      }
    }
  })
}

// 页面加载时初始化
onMounted(async () => {
  // 设置页面为活跃状态
  scannerUtils.setPageActive(true)
  
  // 检查本地存储的用户信息
  const user = uni.getStorageSync('userInfo')
  if (user) {
    userInfo.value = user
  }
  
  // 加载最新的用户信息和机器列表
  await loadUserInfo()
  await loadMachineList()
})

// 页面卸载时清理扫码资源
onUnmounted(() => {
  scannerUtils.destroy()
})
</script>

<script lang="ts">
export default {
  onShow() {
    console.log('机修工页面显示，激活扫码功能')
    scannerUtils.setPageActive(true)
  },
  onHide() {
    console.log('机修工页面隐藏，停用扫码功能')
    scannerUtils.setPageActive(false)
  },
  onUnload() {
    console.log('机修工页面卸载，清理扫码资源')
    scannerUtils.destroy()
  }
}
</script>

<template>
  <view class="mechanic-home">
    <!-- 用户信息头部 -->
    <view class="user-header">
      <view class="user-info">
        <view class="avatar">
          <text class="avatar-text">{{ userInfo.username?.charAt(0)?.toUpperCase() }}</text>
        </view>
        <view class="user-details">
          <text class="username">{{ userInfo.username }}</text>
          <text class="role">机修工</text>
        </view>
      </view>
      <view class="logout-btn" @click="handleLogout">
        <text class="logout-text">退出</text>
      </view>
    </view>
    
    <!-- 扫码维修提示 -->
    <view class="scan-tip" @click="handleScanRepair">
      <view class="scan-icon">
        <text class="icon">📱</text>
      </view>
      <view class="scan-text">
        <text class="tip-title">扫码开始维修</text>
        <text class="tip-desc">扫描机器二维码直接进入维修页面</text>
      </view>
      <view class="scan-arrow">
        <text class="arrow">→</text>
      </view>
    </view>
    
    <!-- 统计概览 -->
    <view class="stats-overview">
      <text class="section-title">设备状态概览</text>
      <view class="stats-grid">
        <view class="stat-card">
          <text class="stat-number">{{ stats.total }}</text>
          <text class="stat-label">总设备</text>
        </view>
        <view class="stat-card normal">
          <text class="stat-number">{{ stats.normal }}</text>
          <text class="stat-label">正常</text>
        </view>
        <view class="stat-card warning">
          <text class="stat-number">{{ stats.warning }}</text>
          <text class="stat-label">警告</text>
        </view>
        <view class="stat-card error">
          <text class="stat-number">{{ stats.error }}</text>
          <text class="stat-label">故障</text>
        </view>
      </view>
    </view>
    
    <!-- 机器列表 -->
    <view class="machine-section">
      <view class="section-header">
        <text class="section-title">设备列表</text>
        <text class="refresh-btn" @click="loadMachineList">{{ isLoadingMachines ? '加载中...' : '刷新' }}</text>
      </view>
      
      <view class="machine-list" v-if="machineList.length > 0">
        <view 
          class="machine-card" 
          v-for="machine in machineList" 
          :key="machine.id"
          @click="viewMachineDetail(machine)"
        >
          <view class="machine-header">
            <view class="machine-info">
              <text class="machine-name">{{ machine.name }}</text>
              <text class="machine-location">{{ machine.location }}</text>
            </view>
            <view 
              class="status-badge" 
              :style="{ 
                backgroundColor: getStatusInfo(machine.status).bgColor,
                color: getStatusInfo(machine.status).color 
              }"
            >
              <text class="status-text">{{ getStatusInfo(machine.status).text }}</text>
            </view>
          </view>
          
          <view class="machine-metrics">
            <view class="metric-item">
              <text class="metric-label">编号</text>
              <text class="metric-value">{{ machine.code }}</text>
            </view>
            <view class="metric-item">
              <text class="metric-label">异常数量</text>
              <text class="metric-value">{{ machine.anomaly_count }}个</text>
            </view>
            <view class="metric-item">
              <text class="metric-label">状态</text>
              <text class="metric-value">{{ getStatusInfo(machine.status).text }}</text>
            </view>
          </view>
          
          <view class="machine-actions" v-if="machine.status === '异常' && machine.anomaly_count > 0">
            <button 
              class="repair-btn" 
              @click.stop="startRepair(machine)"
            >
              开始维修
            </button>
          </view>
        </view>
      </view>
      
      <view class="empty-state" v-else-if="!isLoadingMachines">
        <text class="empty-text">暂无设备数据</text>
        <text class="empty-desc">请检查网络连接或联系管理员</text>
      </view>
      
      <view class="loading-state" v-if="isLoadingMachines">
        <text class="loading-text">加载设备列表中...</text>
      </view>
    </view>
  </view>
</template>

<style lang="scss">
.mechanic-home {
  min-height: 100vh;
  background: #f5f5f5;
  padding-bottom: 40rpx;
}

.user-header {
  background: linear-gradient(135deg, #2196F3 0%, #1976D2 100%);
  padding: 40rpx 30rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.user-info {
  display: flex;
  align-items: center;
}

.avatar {
  width: 80rpx;
  height: 80rpx;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 24rpx;
}

.avatar-text {
  font-size: 32rpx;
  font-weight: bold;
  color: #ffffff;
}

.user-details {
  display: flex;
  flex-direction: column;
}

.username {
  font-size: 32rpx;
  font-weight: bold;
  color: #ffffff;
  margin-bottom: 8rpx;
}

.role {
  font-size: 24rpx;
  color: rgba(255, 255, 255, 0.8);
}

.logout-btn {
  padding: 16rpx 24rpx;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 20rpx;
}

.logout-text {
  font-size: 24rpx;
  color: #ffffff;
}

.scan-tip {
  margin: 30rpx;
  background: linear-gradient(135deg, #FF9800 0%, #F57C00 100%);
  border-radius: 16rpx;
  padding: 30rpx;
  display: flex;
  align-items: center;
  box-shadow: 0 4rpx 20rpx rgba(255, 152, 0, 0.3);
  
  &:active {
    transform: scale(0.98);
  }
}

.scan-icon {
  width: 60rpx;
  height: 60rpx;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 12rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 24rpx;
}

.icon {
  font-size: 28rpx;
}

.scan-text {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.tip-title {
  font-size: 28rpx;
  font-weight: bold;
  color: #ffffff;
  margin-bottom: 8rpx;
}

.tip-desc {
  font-size: 22rpx;
  color: rgba(255, 255, 255, 0.8);
}

.scan-arrow {
  width: 40rpx;
  height: 40rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.arrow {
  font-size: 24rpx;
  color: rgba(255, 255, 255, 0.8);
}

.stats-overview {
  padding: 0 30rpx 30rpx;
}

.section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333333;
  margin-bottom: 24rpx;
}

.stats-grid {
  display: flex;
  gap: 15rpx;
}

.stat-card {
  flex: 1;
  background: #ffffff;
  border-radius: 12rpx;
  padding: 24rpx 16rpx;
  text-align: center;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
  
  &.normal {
    border-left: 6rpx solid #4CAF50;
  }
  
  &.warning {
    border-left: 6rpx solid #FF9800;
  }
  
  &.error {
    border-left: 6rpx solid #F44336;
  }
}

.stat-number {
  display: block;
  font-size: 36rpx;
  font-weight: bold;
  color: #333333;
  margin-bottom: 8rpx;
}

.stat-label {
  font-size: 22rpx;
  color: #666666;
}

.machine-section {
  padding: 0 30rpx;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24rpx;
}

.refresh-btn {
  font-size: 24rpx;
  color: #2196F3;
}

.empty-state, .loading-state {
  padding: 60rpx 30rpx;
  text-align: center;
  background: #ffffff;
  border-radius: 12rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.empty-text, .loading-text {
  font-size: 28rpx;
  color: #666666;
  margin-bottom: 12rpx;
}

.empty-desc {
  font-size: 24rpx;
  color: #999999;
  line-height: 1.5;
}

.machine-list {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.machine-card {
  background: #ffffff;
  border-radius: 16rpx;
  padding: 30rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
  
  &:active {
    transform: scale(0.98);
  }
}

.machine-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 20rpx;
}

.machine-info {
  display: flex;
  flex-direction: column;
}

.machine-name {
  font-size: 32rpx;
  font-weight: bold;
  color: #333333;
  margin-bottom: 8rpx;
}

.machine-location {
  font-size: 24rpx;
  color: #666666;
}

.status-badge {
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  font-size: 22rpx;
}

.status-text {
  font-weight: 500;
}

.machine-metrics {
  display: flex;
  justify-content: space-between;
  margin-bottom: 20rpx;
}

.metric-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  flex: 1;
}

.metric-label {
  font-size: 22rpx;
  color: #666666;
  margin-bottom: 8rpx;
}

.metric-value {
  font-size: 28rpx;
  font-weight: bold;
  color: #333333;
}

.machine-footer {
  padding: 20rpx;
  background: #f8f8f8;
  border-radius: 12rpx;
  margin-bottom: 20rpx;
}

.error-desc {
  font-size: 24rpx;
  color: #666666;
  line-height: 1.4;
}

.machine-actions {
  display: flex;
  justify-content: flex-end;
}

.repair-btn {
  background: linear-gradient(135deg, #4CAF50 0%, #388E3C 100%);
  color: #ffffff;
  border: none;
  border-radius: 20rpx;
  padding: 16rpx 32rpx;
  font-size: 24rpx;
  font-weight: 500;
  
  &:active {
    transform: scale(0.95);
  }
}
</style>
