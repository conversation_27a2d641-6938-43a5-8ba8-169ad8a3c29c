"use strict";function e(e){return e&&e.__esModule&&Object.prototype.hasOwnProperty.call(e,"default")?e.default:e}const t=e=>{if(!e)return null;const n={};switch(e.type){case"ELEMENT":{let a={};const u=s(e.value.attributes),c=t(e.value.children);if(u&&(a=Object.assign(a,u)),c){const t=r(e.value.children);a=Object.assign(a,t)}n[e.value.type]=a;break}case"ATTRIBUTE":{const t=e.value;n[t.name]=t.value;break}case"CONTENT":return{content:e.value}}return n},r=e=>e&&Array.isArray(e)&&0!==e.length?n(e)?{content:e[0].value}:{children:e.map(t)}:null,n=e=>e&&Array.isArray(e)&&1===e.length&&"CONTENT"===e[0].type,s=e=>{if(e&&Array.isArray(e)){return e.map(t).reduce(((e,t)=>Object.assign(e,t)),{})}return null};var a={convert:e=>t(e.value.children[0])};var u={Token:(e,t)=>({type:e,value:t})};const{Token:c}=u,l=c("EOF"),E=e=>" "===e||"\n"===e||"\r"===e||"\t"===e,o=e=>e.replace(/'/g,"'");var T={createLexer:function(e){let t=null,r=(e=>{let t=0;for(;t<e.length&&E(e[t]);)t++;return((e,t)=>{if(e.startsWith("<?xml",t)){const r=e.length;for(;t<r;)if("?"!==e[t])t++;else{if(">"===e[t+1])return t+2;t++}}return t})(e,t)})(e),n=[];const s=()=>e[r],a=()=>t!==l&&r<e.length,u=e=>'"'===e||"'"===e,T=()=>{for(;a()&&E(e[r]);)r++},i=t=>{if(a()){if("<"===e[r]){let t="<";return r++,a()&&"/"===e[r]?(r++,t="</"):a()&&"!"===e[r]&&"-"===e[r+1]&&"-"===e[r+2]&&(r++,r++,r++,t="\x3c!--"),t}if("/"===s()){let e="/";return r++,a()&&">"===s()&&(r++,e="/>"),e}if("="===e[r]||">"===e[r]){const t=e[r];return r++,t}}return p(!!t)},p=t=>{const n=t?/[^>=<]/u:/[a-zA-Z0-9_:-]/;let s=r;for(;a()&&e[r].match(n);)r++;return o(e.substring(s,r))},N=()=>{const E=r;T();const f=r-E;if(a())if(t&&"OPEN_BRACKET"===t.type){T();const e=i(!1);t=c("ELEMENT_TYPE",e),n.push(e)}else if(t&&"ASSIGN"===t.type){a()&&u(s())&&r++;const n=e[r-1];let l=r;for(;a()&&s()!==n;)r++;const E=o(e.substring(l,r));r++,t=c("ATTRIB_VALUE",E)}else{T();let u=i(!0);switch(u){case"=":t="ATTRIB_NAME"===t.type?c("ASSIGN"):c("CONTENT",u);break;case"</":{const s=r;for(;">"!==e[r];)r++;t=c("CLOSE_ELEMENT",e.substring(s,r)),r++,n.pop();break}case"/>":{const e=n.pop();t=c("CLOSE_ELEMENT",e);break}case"\x3c!--":{const t=["!","-","-"];for(;a()&&(">"!==t[2]||"-"!==t[1]||"-"!==t[0]);)t.shift(),t.push(e[r]),r++;return N()}case">":t=c("CLOSE_BRACKET");break;case"<":t=c("OPEN_BRACKET");break;default:if(u&&u.length>0){if("CLOSE_BRACKET"===t.type){let e="";"<"!==s()&&(e=p(!0)),t=c("CONTENT",u+e)}else if("ATTRIB_NAME"!==t.type&&"CONTENT"!==t.type)"CLOSE_ELEMENT"===t.type?(u=" ".repeat(f)+u,t=c("CONTENT",u)):t=c("ATTRIB_NAME",u);else{const e=" ".repeat(f)+u;t=c("CONTENT",e)}break}{const t='Unknown Syntax : "'+e[r]+'"';throw new Error(t)}}}else t=l;return t};return{peek:s,next:N,hasNext:a}}};const{createLexer:i}=T,{Token:p}=u,[N,f,h,O]=["ROOT","ELEMENT","ATTRIBUTE","CONTENT"],y=(e,t)=>({type:e,value:t}),C=e=>y(O,e),v=(e,t,r)=>y(f,{type:e,attributes:t,children:r}),A=(e,t)=>y(h,{name:e,value:t}),L=(e,t)=>{const r=[];for(;e.hasNext();){const n=e.next();switch(n.type){case"OPEN_BRACKET":{const t=e.next(),[n,s]=_(e);let a=[];"CLOSE_ELEMENT"!==s.type&&(a=L(e,t)),a&&a.length>0&&"CONTENT"===a[0].type&&(a=b(a)),r.push(v(t.value,n,a));break}case"CLOSE_ELEMENT":if(n.value===t.value)return r;break;case"CONTENT":r.push(C(n.value));break;case"EOF":return r;default:throw new Error(`Unknown Lexem type: ${n.type} "${n.value}, scoping element: ${t.value}"`)}}return r},_=e=>{const t=[];let r=e.peek();if(!e.hasNext()||r&&"CLOSE_BRACKET"===r.type||r&&"CLOSE_ELEMENT"===r.type)return[t,r];for(r=e.next();e.hasNext()&&r&&"CLOSE_BRACKET"!==r.type&&"CLOSE_ELEMENT"!==r.type;){const n=r;e.next();const s=e.next(),a=A(n.value,s.value);t.push(a),r=e.next()}return[t,r]};function b(e){let t=[],r="";return e.forEach((e=>{"CONTENT"===e.type?r+=e.value:(r.length&&(t.push(C(r)),r=""),t.push(e))})),r.length&&t.push(C(r)),t}var x={AttribNode:A,ContentNode:C,ElementNode:v,Node:y,transpile:function(e,t){const r=(e=>y(N,{children:L(e,p(N,"ROOT"))}))(i(e));return t?t.convert(r):r}};const k=a,{transpile:d}=x;var g=e({convertXML:function(e,t){return d(e,t||k)},createAST:function(e){return d(e)}});module.exports=g;
