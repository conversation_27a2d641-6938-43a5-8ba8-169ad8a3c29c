go get example.net/badgo@v1.0.0
go get example.net/badgo@v1.1.0
go get example.net/badgo@v1.2.0
go get example.net/badgo@v1.3.0
go get example.net/badgo@v1.4.0
go get example.net/badgo@v1.5.0
! go get example.net/badgo@v1.6.0
stderr 'invalid go version .X.Y.: must match format 1.23'

-- go.mod --
module m

replace (
	example.net/badgo v1.0.0 => ./v1.0.0
	example.net/badgo v1.1.0 => ./v1.1.0
	example.net/badgo v1.2.0 => ./v1.2.0
	example.net/badgo v1.3.0 => ./v1.3.0
	example.net/badgo v1.4.0 => ./v1.4.0
	example.net/badgo v1.5.0 => ./v1.5.0
	example.net/badgo v1.6.0 => ./v1.6.0
)

-- v1.0.0/go.mod --
module example.net/badgo
go 1.17.0

-- v1.1.0/go.mod --
module example.net/badgo
go 1.17rc2

-- v1.2.0/go.mod --
module example.net/badgo
go 1.17.1

-- v1.3.0/go.mod --
module example.net/badgo
go v1.17.0

-- v1.4.0/go.mod --
module example.net/badgo
go v1.17.0-rc.2

-- v1.5.0/go.mod --
module example.net/badgo
go v1.17.1

-- v1.6.0/go.mod --
module example.net/badgo
go X.Y

