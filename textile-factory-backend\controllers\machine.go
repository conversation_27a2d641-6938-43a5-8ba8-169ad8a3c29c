package controllers

import (
	"net/http"
	"strconv"
	"textile-factory-backend/config"
	"textile-factory-backend/models"
	"github.com/gin-gonic/gin"
)

func GetMachineList(c *gin.Context) {
	var machines []models.Machine
	
	page, _ := strconv.Atoi(c<PERSON>("page", "1"))
	pageSize, _ := strconv.Atoi(c<PERSON>("page_size", "10"))
	status := c.Query("status")
	
	query := config.DB.Model(&models.Machine{})
	
	if status != "" {
		query = query.Where("status = ?", status)
	}
	
	var total int64
	query.Count(&total)
	
	offset := (page - 1) * pageSize
	result := query.Offset(offset).Limit(pageSize).Find(&machines)
	
	if result.Error != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "查询机器列表失败"})
		return
	}
	
	// 获取每台机器的异常数量
	for i := range machines {
		var anomalyCount int64
		config.DB.Model(&models.Anomaly{}).Where("machine_id = ? AND status != '已完成'", machines[i].ID).Count(&anomalyCount)
		machines[i].QRCode = strconv.FormatInt(anomalyCount, 10) // 临时使用QRCode字段存储异常数量
	}
	
	c.JSON(http.StatusOK, gin.H{
		"machines": machines,
		"total":    total,
		"page":     page,
		"page_size": pageSize,
	})
}

func GetMachineByCode(c *gin.Context) {
	code := c.Param("code")
	
	var machine models.Machine
	result := config.DB.Where("code = ?", code).First(&machine)
	
	if result.Error != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "机器不存在"})
		return
	}
	
	// 获取机器的异常记录
	var anomalies []models.Anomaly
	config.DB.Where("machine_id = ?", machine.ID).
		Preload("User").
		Order("created_at DESC").
		Find(&anomalies)
	
	c.JSON(http.StatusOK, gin.H{
		"machine":   machine,
		"anomalies": anomalies,
	})
}

func GetMachineByQRCode(c *gin.Context) {
	qrCode := c.Param("qr_code")
	
	var machine models.Machine
	result := config.DB.Where("qr_code = ?", qrCode).First(&machine)
	
	if result.Error != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "机器不存在"})
		return
	}
	
	c.JSON(http.StatusOK, machine)
}

func UpdateMachineStatus(c *gin.Context) {
	id := c.Param("id")
	
	var req struct {
		Status string `json:"status" binding:"required"`
	}
	
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}
	
	result := config.DB.Model(&models.Machine{}).Where("id = ?", id).Update("status", req.Status)
	
	if result.Error != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "更新机器状态失败"})
		return
	}
	
	if result.RowsAffected == 0 {
		c.JSON(http.StatusNotFound, gin.H{"error": "机器不存在"})
		return
	}
	
	c.JSON(http.StatusOK, gin.H{"message": "机器状态更新成功"})
}