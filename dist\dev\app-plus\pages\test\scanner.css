/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.scanner-test {
  padding: 0.625rem;
  background-color: #f5f5f5;
  min-height: 100vh;
}
.header {
  text-align: center;
  margin-bottom: 0.9375rem;
}
.header .title {
  font-size: 1.125rem;
  font-weight: bold;
  color: #2196F3;
}
.device-card, .status-card, .result-card, .info-card {
  background: white;
  border-radius: 0.5rem;
  padding: 0.9375rem;
  margin-bottom: 0.625rem;
  box-shadow: 0 0.125rem 0.375rem rgba(0, 0, 0, 0.1);
}
.card-title {
  font-size: 1rem;
  font-weight: bold;
  color: #333;
  margin-bottom: 0.625rem;
}
.device-info {
  display: flex;
  flex-direction: column;
  gap: 0.3125rem;
  margin-bottom: 0.625rem;
}
.info-item {
  font-size: 0.875rem;
  color: #666;
}
.config-btn {
  background: #FF9800;
  color: white;
  border: none;
  border-radius: 0.25rem;
  padding: 0.5rem 0.75rem;
  font-size: 0.75rem;
}
.status-label, .result-label {
  font-size: 0.875rem;
  color: #666;
  margin-bottom: 0.3125rem;
}
.status-text {
  font-size: 1rem;
  font-weight: bold;
}
.status-text.success {
  color: #4CAF50;
}
.status-text.error {
  color: #F44336;
}
.result-text {
  font-size: 0.9375rem;
  color: #333;
  word-break: break-all;
  background: #f8f8f8;
  padding: 0.625rem;
  border-radius: 0.25rem;
}
.button-group {
  display: flex;
  flex-direction: column;
  gap: 0.625rem;
  margin-bottom: 0.9375rem;
}
.scan-btn {
  height: 2.75rem;
  border-radius: 1.375rem;
  font-size: 1rem;
  font-weight: bold;
  border: none;
}
.scan-btn.primary {
  background: #2196F3;
  color: white;
}
.scan-btn.primary:disabled {
  background: #ccc;
}
.scan-btn.report {
  background: #4CAF50;
  color: white;
}
.scan-btn.report:disabled {
  background: #ccc;
}
.scan-btn.repair {
  background: #9C27B0;
  color: white;
}
.scan-btn.repair:disabled {
  background: #ccc;
}
.scan-btn.secondary {
  background: #FF9800;
  color: white;
}
.scan-btn.danger {
  background: #F44336;
  color: white;
}
.info-card .info-title {
  font-size: 0.9375rem;
  font-weight: bold;
  color: #333;
  margin-bottom: 0.625rem;
}
.info-card .info-text {
  display: block;
  font-size: 0.8125rem;
  color: #666;
  line-height: 1.6;
  margin-bottom: 0.3125rem;
}