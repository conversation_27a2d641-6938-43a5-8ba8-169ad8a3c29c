// Copyright 2017 The Go Authors. All rights reserved.
// Use of this source code is governed by a BSD-style
// license that can be found in the LICENSE file.

// This input extends auto-generated amd64enc.s test suite
// with manually added tests.

#include "../../../../../runtime/textflag.h"

TEXT asmtest(SB),DUPOK|NOSPLIT,$0
	// AVX2GATHER: basic combinations.
	VPGATHERDQ Y2, (BP)(X7*2), Y1           // c4e2ed904c7d00
	VPGATHERDQ X12, (R13)(X14*2), X11       // c40299905c7500
	VPGATHERDQ Y12, (R13)(X14*2), Y11       // c4029d905c7500
	VPGATHERDQ Y0, 8(X4*1), Y6              // c4e2fd90342508000000
	VPGATHERDQ Y0, -8(X4*1), Y6             // c4e2fd903425f8ffffff
	VPGATHERDQ Y0, 0(X4*1), Y6              // c4e2fd90342500000000
	VPGATHERDQ Y0, 664(X4*1), Y6            // c4e2fd90342598020000
	VPGATHERDQ Y0, 8(X4*8), Y6              // c4e2fd9034e508000000
	VPGATHERDQ Y0, -8(X4*8), Y6             // c4e2fd9034e5f8ffffff
	VPGATHERDQ Y0, 0(X4*8), Y6              // c4e2fd9034e500000000
	VPGATHERDQ Y0, 664(X4*8), Y6            // c4e2fd9034e598020000
	VPGATHERDQ Y0, 8(X14*1), Y6             // c4a2fd90343508000000
	VPGATHERDQ Y0, -8(X14*1), Y6            // c4a2fd903435f8ffffff
	VPGATHERDQ Y0, 0(X14*1), Y6             // c4a2fd90343500000000
	VPGATHERDQ Y0, 664(X14*1), Y6           // c4a2fd90343598020000
	VPGATHERDQ Y0, 8(X14*8), Y6             // c4a2fd9034f508000000
	VPGATHERDQ Y0, -8(X14*8), Y6            // c4a2fd9034f5f8ffffff
	VPGATHERDQ Y0, 0(X14*8), Y6             // c4a2fd9034f500000000
	VPGATHERDQ Y0, 664(X14*8), Y6           // c4a2fd9034f598020000
	VPGATHERDQ X2, (BP)(X7*2), X1           // c4e2e9904c7d00
	VPGATHERDQ Y2, (BP)(X7*2), Y1           // c4e2ed904c7d00
	VPGATHERDQ X12, (R13)(X14*2), X11       // c40299905c7500
	VPGATHERDQ Y12, (R13)(X14*2), Y11       // c4029d905c7500
	VPGATHERDQ Y0, 8(X4*1), Y6              // c4e2fd90342508000000
	VPGATHERDQ Y0, -8(X4*1), Y6             // c4e2fd903425f8ffffff
	VPGATHERDQ Y0, 0(X4*1), Y6              // c4e2fd90342500000000
	VPGATHERDQ Y0, 664(X4*1), Y6            // c4e2fd90342598020000
	VPGATHERDQ Y0, 8(X4*8), Y6              // c4e2fd9034e508000000
	VPGATHERDQ Y0, -8(X4*8), Y6             // c4e2fd9034e5f8ffffff
	VPGATHERDQ Y0, 0(X4*8), Y6              // c4e2fd9034e500000000
	VPGATHERDQ Y0, 664(X4*8), Y6            // c4e2fd9034e598020000
	VPGATHERDQ Y0, 8(X14*1), Y6             // c4a2fd90343508000000
	VPGATHERDQ Y0, -8(X14*1), Y6            // c4a2fd903435f8ffffff
	VPGATHERDQ Y0, 0(X14*1), Y6             // c4a2fd90343500000000
	VPGATHERDQ Y0, 664(X14*1), Y6           // c4a2fd90343598020000
	VPGATHERDQ Y0, 8(X14*8), Y6             // c4a2fd9034f508000000
	VPGATHERDQ Y0, -8(X14*8), Y6            // c4a2fd9034f5f8ffffff
	VPGATHERDQ Y0, 0(X14*8), Y6             // c4a2fd9034f500000000
	VPGATHERDQ Y0, 664(X14*8), Y6           // c4a2fd9034f598020000
	VPGATHERQQ X2, (BP)(X7*2), X1           // c4e2e9914c7d00
	VPGATHERQQ Y2, (BP)(Y7*2), Y1           // c4e2ed914c7d00
	VPGATHERQQ X12, (R13)(X14*2), X11       // c40299915c7500
	VPGATHERQQ Y12, (R13)(Y14*2), Y11       // c4029d915c7500
	VPGATHERQQ X2, (BP)(X7*2), X1           // c4e2e9914c7d00
	VPGATHERQQ Y2, (BP)(Y7*2), Y1           // c4e2ed914c7d00
	VPGATHERQQ X12, (R13)(X14*2), X11       // c40299915c7500
	VPGATHERQQ Y12, (R13)(Y14*2), Y11       // c4029d915c7500
	VGATHERDPD X2, (BP)(X7*2), X1           // c4e2e9924c7d00
	VGATHERDPD Y2, (BP)(X7*2), Y1           // c4e2ed924c7d00
	VGATHERDPD X12, (R13)(X14*2), X11       // c40299925c7500
	VGATHERDPD Y12, (R13)(X14*2), Y11       // c4029d925c7500
	VGATHERDPD Y0, 8(X4*1), Y6              // c4e2fd92342508000000
	VGATHERDPD Y0, -8(X4*1), Y6             // c4e2fd923425f8ffffff
	VGATHERDPD Y0, 0(X4*1), Y6              // c4e2fd92342500000000
	VGATHERDPD Y0, 664(X4*1), Y6            // c4e2fd92342598020000
	VGATHERDPD Y0, 8(X4*8), Y6              // c4e2fd9234e508000000
	VGATHERDPD Y0, -8(X4*8), Y6             // c4e2fd9234e5f8ffffff
	VGATHERDPD Y0, 0(X4*8), Y6              // c4e2fd9234e500000000
	VGATHERDPD Y0, 664(X4*8), Y6            // c4e2fd9234e598020000
	VGATHERDPD Y0, 8(X14*1), Y6             // c4a2fd92343508000000
	VGATHERDPD Y0, -8(X14*1), Y6            // c4a2fd923435f8ffffff
	VGATHERDPD Y0, 0(X14*1), Y6             // c4a2fd92343500000000
	VGATHERDPD Y0, 664(X14*1), Y6           // c4a2fd92343598020000
	VGATHERDPD Y0, 8(X14*8), Y6             // c4a2fd9234f508000000
	VGATHERDPD Y0, -8(X14*8), Y6            // c4a2fd9234f5f8ffffff
	VGATHERDPD Y0, 0(X14*8), Y6             // c4a2fd9234f500000000
	VGATHERDPD Y0, 664(X14*8), Y6           // c4a2fd9234f598020000
	VGATHERDPD X2, (BP)(X7*2), X1           // c4e2e9924c7d00
	VGATHERDPD Y2, (BP)(X7*2), Y1           // c4e2ed924c7d00
	VGATHERDPD X12, (R13)(X14*2), X11       // c40299925c7500
	VGATHERDPD Y12, (R13)(X14*2), Y11       // c4029d925c7500
	VGATHERDPD Y0, 8(X4*1), Y6              // c4e2fd92342508000000
	VGATHERDPD Y0, -8(X4*1), Y6             // c4e2fd923425f8ffffff
	VGATHERDPD Y0, 0(X4*1), Y6              // c4e2fd92342500000000
	VGATHERDPD Y0, 664(X4*1), Y6            // c4e2fd92342598020000
	VGATHERDPD Y0, 8(X4*8), Y6              // c4e2fd9234e508000000
	VGATHERDPD Y0, -8(X4*8), Y6             // c4e2fd9234e5f8ffffff
	VGATHERDPD Y0, 0(X4*8), Y6              // c4e2fd9234e500000000
	VGATHERDPD Y0, 664(X4*8), Y6            // c4e2fd9234e598020000
	VGATHERDPD Y0, 8(X14*1), Y6             // c4a2fd92343508000000
	VGATHERDPD Y0, -8(X14*1), Y6            // c4a2fd923435f8ffffff
	VGATHERDPD Y0, 0(X14*1), Y6             // c4a2fd92343500000000
	VGATHERDPD Y0, 664(X14*1), Y6           // c4a2fd92343598020000
	VGATHERDPD Y0, 8(X14*8), Y6             // c4a2fd9234f508000000
	VGATHERDPD Y0, -8(X14*8), Y6            // c4a2fd9234f5f8ffffff
	VGATHERDPD Y0, 0(X14*8), Y6             // c4a2fd9234f500000000
	VGATHERDPD Y0, 664(X14*8), Y6           // c4a2fd9234f598020000
	VGATHERQPD X2, (BP)(X7*2), X1           // c4e2e9934c7d00
	VGATHERQPD Y2, (BP)(Y7*2), Y1           // c4e2ed934c7d00
	VGATHERQPD X12, (R13)(X14*2), X11       // c40299935c7500
	VGATHERQPD Y12, (R13)(Y14*2), Y11       // c4029d935c7500
	VGATHERQPD X2, (BP)(X7*2), X1           // c4e2e9934c7d00
	VGATHERQPD Y2, (BP)(Y7*2), Y1           // c4e2ed934c7d00
	VGATHERQPD X12, (R13)(X14*2), X11       // c40299935c7500
	VGATHERQPD Y12, (R13)(Y14*2), Y11       // c4029d935c7500
	VGATHERDPS X2, (BP)(X7*2), X1           // c4e269924c7d00
	VGATHERDPS Y2, (BP)(Y7*2), Y1           // c4e26d924c7d00
	VGATHERDPS X12, (R13)(X14*2), X11       // c40219925c7500
	VGATHERDPS Y12, (R13)(Y14*2), Y11       // c4021d925c7500
	VGATHERDPS X3, 8(X4*1), X6              // c4e26192342508000000
	VGATHERDPS X3, -8(X4*1), X6             // c4e261923425f8ffffff
	VGATHERDPS X3, 0(X4*1), X6              // c4e26192342500000000
	VGATHERDPS X3, 664(X4*1), X6            // c4e26192342598020000
	VGATHERDPS X3, 8(X4*8), X6              // c4e2619234e508000000
	VGATHERDPS X3, -8(X4*8), X6             // c4e2619234e5f8ffffff
	VGATHERDPS X3, 0(X4*8), X6              // c4e2619234e500000000
	VGATHERDPS X3, 664(X4*8), X6            // c4e2619234e598020000
	VGATHERDPS X3, 8(X14*1), X6             // c4a26192343508000000
	VGATHERDPS X3, -8(X14*1), X6            // c4a261923435f8ffffff
	VGATHERDPS X3, 0(X14*1), X6             // c4a26192343500000000
	VGATHERDPS X3, 664(X14*1), X6           // c4a26192343598020000
	VGATHERDPS X3, 8(X14*8), X6             // c4a2619234f508000000
	VGATHERDPS X3, -8(X14*8), X6            // c4a2619234f5f8ffffff
	VGATHERDPS X3, 0(X14*8), X6             // c4a2619234f500000000
	VGATHERDPS X3, 664(X14*8), X6           // c4a2619234f598020000
	VGATHERDPS X2, (BP)(X7*2), X1           // c4e269924c7d00
	VGATHERDPS Y2, (BP)(Y7*2), Y1           // c4e26d924c7d00
	VGATHERDPS X12, (R13)(X14*2), X11       // c40219925c7500
	VGATHERDPS Y12, (R13)(Y14*2), Y11       // c4021d925c7500
	VGATHERDPS X5, 8(X4*1), X6              // c4e25192342508000000
	VGATHERDPS X3, -8(X4*1), X6             // c4e261923425f8ffffff
	VGATHERDPS X3, 0(X4*1), X6              // c4e26192342500000000
	VGATHERDPS X3, 664(X4*1), X6            // c4e26192342598020000
	VGATHERDPS X3, 8(X4*8), X6              // c4e2619234e508000000
	VGATHERDPS X3, -8(X4*8), X6             // c4e2619234e5f8ffffff
	VGATHERDPS X3, 0(X4*8), X6              // c4e2619234e500000000
	VGATHERDPS X3, 664(X4*8), X6            // c4e2619234e598020000
	VGATHERDPS X3, 8(X14*1), X6             // c4a26192343508000000
	VGATHERDPS X3, -8(X14*1), X6            // c4a261923435f8ffffff
	VGATHERDPS X3, 0(X14*1), X6             // c4a26192343500000000
	VGATHERDPS X3, 664(X14*1), X6           // c4a26192343598020000
	VGATHERDPS X3, 8(X14*8), X6             // c4a2619234f508000000
	VGATHERDPS X3, -8(X14*8), X6            // c4a2619234f5f8ffffff
	VGATHERDPS X3, 0(X14*8), X6             // c4a2619234f500000000
	VGATHERDPS X3, 664(X14*8), X6           // c4a2619234f598020000
	VGATHERQPS X2, (BP)(X7*2), X1           // c4e269934c7d00
	VGATHERQPS X2, (BP)(Y7*2), X1           // c4e26d934c7d00
	VGATHERQPS X12, (R13)(X14*2), X11       // c40219935c7500
	VGATHERQPS X12, (R13)(Y14*2), X11       // c4021d935c7500
	VGATHERQPS X2, (BP)(X7*2), X1           // c4e269934c7d00
	VGATHERQPS X2, (BP)(Y7*2), X1           // c4e26d934c7d00
	VGATHERQPS X12, (R13)(X14*2), X11       // c40219935c7500
	VGATHERQPS X12, (R13)(Y14*2), X11       // c4021d935c7500
	VPGATHERDD X2, (BP)(X7*2), X1           // c4e269904c7d00
	VPGATHERDD Y2, (BP)(Y7*2), Y1           // c4e26d904c7d00
	VPGATHERDD X12, (R13)(X14*2), X11       // c40219905c7500
	VPGATHERDD Y12, (R13)(Y14*2), Y11       // c4021d905c7500
	VPGATHERDD X3, 8(X4*1), X6              // c4e26190342508000000
	VPGATHERDD X3, -8(X4*1), X6             // c4e261903425f8ffffff
	VPGATHERDD X3, 0(X4*1), X6              // c4e26190342500000000
	VPGATHERDD X3, 664(X4*1), X6            // c4e26190342598020000
	VPGATHERDD X3, 8(X4*8), X6              // c4e2619034e508000000
	VPGATHERDD X3, -8(X4*8), X6             // c4e2619034e5f8ffffff
	VPGATHERDD X3, 0(X4*8), X6              // c4e2619034e500000000
	VPGATHERDD X3, 664(X4*8), X6            // c4e2619034e598020000
	VPGATHERDD X3, 8(X14*1), X6             // c4a26190343508000000
	VPGATHERDD X3, -8(X14*1), X6            // c4a261903435f8ffffff
	VPGATHERDD X3, 0(X14*1), X6             // c4a26190343500000000
	VPGATHERDD X3, 664(X14*1), X6           // c4a26190343598020000
	VPGATHERDD X3, 8(X14*8), X6             // c4a2619034f508000000
	VPGATHERDD X3, -8(X14*8), X6            // c4a2619034f5f8ffffff
	VPGATHERDD X3, 0(X14*8), X6             // c4a2619034f500000000
	VPGATHERDD X3, 664(X14*8), X6           // c4a2619034f598020000
	VPGATHERDD X2, (BP)(X7*2), X1           // c4e269904c7d00
	VPGATHERDD Y2, (BP)(Y7*2), Y1           // c4e26d904c7d00
	VPGATHERDD X12, (R13)(X14*2), X11       // c40219905c7500
	VPGATHERDD Y12, (R13)(Y14*2), Y11       // c4021d905c7500
	VPGATHERDD X3, 8(X4*1), X6              // c4e26190342508000000
	VPGATHERDD X3, -8(X4*1), X6             // c4e261903425f8ffffff
	VPGATHERDD X3, 0(X4*1), X6              // c4e26190342500000000
	VPGATHERDD X3, 664(X4*1), X6            // c4e26190342598020000
	VPGATHERDD X3, 8(X4*8), X6              // c4e2619034e508000000
	VPGATHERDD X3, -8(X4*8), X6             // c4e2619034e5f8ffffff
	VPGATHERDD X3, 0(X4*8), X6              // c4e2619034e500000000
	VPGATHERDD X3, 664(X4*8), X6            // c4e2619034e598020000
	VPGATHERDD X3, 8(X14*1), X6             // c4a26190343508000000
	VPGATHERDD X3, -8(X14*1), X6            // c4a261903435f8ffffff
	VPGATHERDD X3, 0(X14*1), X6             // c4a26190343500000000
	VPGATHERDD X3, 664(X14*1), X6           // c4a26190343598020000
	VPGATHERDD X3, 8(X14*8), X6             // c4a2619034f508000000
	VPGATHERDD X3, -8(X14*8), X6            // c4a2619034f5f8ffffff
	VPGATHERDD X3, 0(X14*8), X6             // c4a2619034f500000000
	VPGATHERDD X3, 664(X14*8), X6           // c4a2619034f598020000
	VPGATHERQD X2, (BP)(X7*2), X1           // c4e269914c7d00
	VPGATHERQD X2, (BP)(Y7*2), X1           // c4e26d914c7d00
	VPGATHERQD X12, (R13)(X14*2), X11       // c40219915c7500
	VPGATHERQD X12, (R13)(Y14*2), X11       // c4021d915c7500
	VPGATHERQD X2, (BP)(X7*2), X1           // c4e269914c7d00
	VPGATHERQD X2, (BP)(Y7*2), X1           // c4e26d914c7d00
	VPGATHERQD X12, (R13)(X14*2), X11       // c40219915c7500
	VPGATHERQD X12, (R13)(Y14*2), X11       // c4021d915c7500
	VPGATHERQQ X0, 0(X1*1), X2              // c4e2f991140d00000000
	VPGATHERQQ Y0, 0(Y1*1), Y2              // c4e2fd91140d00000000
	VPGATHERQQ X8, 0(X9*1), X10             // c422b991140d00000000
	VPGATHERQQ Y8, 0(Y9*1), Y10             // c422bd91140d00000000
	VPGATHERQQ X0, 0(X1*4), X2              // c4e2f991148d00000000
	VPGATHERQQ Y0, 0(Y1*4), Y2              // c4e2fd91148d00000000
	VPGATHERQQ X8, 0(X9*4), X10             // c422b991148d00000000
	VPGATHERQQ Y8, 0(Y9*4), Y10             // c422bd91148d00000000
	// AVX2GATHER: test SP/BP base with different displacements.
	VPGATHERQQ X0, (SP)(X1*1), X2           // c4e2f991140c
	VPGATHERQQ X0, 16(SP)(X1*1), X2         // c4e2f991540c10
	VPGATHERQQ X0, 512(SP)(X1*1), X2        // c4e2f991940c00020000
	VPGATHERQQ X0, (R12)(X1*1), X2          // c4c2f991140c
	VPGATHERQQ X0, 16(R12)(X1*1), X2        // c4c2f991540c10
	VPGATHERQQ X0, 512(R12)(X1*1), X2       // c4c2f991940c00020000
	VPGATHERQQ X0, (BP)(X1*1), X2           // c4e2f991540d00
	VPGATHERQQ X0, 16(BP)(X1*1), X2         // c4e2f991540d10
	VPGATHERQQ X0, 512(BP)(X1*1), X2        // c4e2f991940d00020000
	VPGATHERQQ X0, (R13)(X1*1), X2          // c4c2f991540d00
	VPGATHERQQ X0, 16(R13)(X1*1), X2        // c4c2f991540d10
	VPGATHERQQ X0, 512(R13)(X1*1), X2       // c4c2f991940d00020000
	VPGATHERQQ Y0, (SP)(Y1*1), Y2           // c4e2fd91140c
	VPGATHERQQ Y0, 16(SP)(Y1*1), Y2         // c4e2fd91540c10
	VPGATHERQQ Y0, 512(SP)(Y1*1), Y2        // c4e2fd91940c00020000
	VPGATHERQQ Y0, (R12)(Y1*1), Y2          // c4c2fd91140c
	VPGATHERQQ Y0, 16(R12)(Y1*1), Y2        // c4c2fd91540c10
	VPGATHERQQ Y0, 512(R12)(Y1*1), Y2       // c4c2fd91940c00020000
	VPGATHERQQ Y0, (BP)(Y1*1), Y2           // c4e2fd91540d00
	VPGATHERQQ Y0, 16(BP)(Y1*1), Y2         // c4e2fd91540d10
	VPGATHERQQ Y0, 512(BP)(Y1*1), Y2        // c4e2fd91940d00020000
	VPGATHERQQ Y0, (R13)(Y1*1), Y2          // c4c2fd91540d00
	VPGATHERQQ Y0, 16(R13)(Y1*1), Y2        // c4c2fd91540d10
	VPGATHERQQ Y0, 512(R13)(Y1*1), Y2       // c4c2fd91940d00020000
	// Test low-8 register for /is4 "hr" operand.
	VPBLENDVB X0, (BX), X1, X2              // c4e3714c1300
	// <XMM0>/Yxr0 tests.
	SHA256RNDS2 X0, (BX), X2   // 0f38cb13
	SHA256RNDS2 X0, (R11), X2  // 410f38cb13
	SHA256RNDS2 X0, X2, X2     // 0f38cbd2
	SHA256RNDS2 X0, X11, X2    // 410f38cbd3
	SHA256RNDS2 X0, (BX), X11  // 440f38cb1b
	SHA256RNDS2 X0, (R11), X11 // 450f38cb1b
	SHA256RNDS2 X0, X2, X11    // 440f38cbda
	SHA256RNDS2 X0, X11, X11   // 450f38cbdb
	// Rest SHA instructions tests.
	SHA1MSG1 (BX), X2        // 0f38c913
	SHA1MSG1 (R11), X2       // 410f38c913
	SHA1MSG1 X2, X2          // 0f38c9d2
	SHA1MSG1 X11, X2         // 410f38c9d3
	SHA1MSG1 (BX), X11       // 440f38c91b
	SHA1MSG1 (R11), X11      // 450f38c91b
	SHA1MSG1 X2, X11         // 440f38c9da
	SHA1MSG1 X11, X11        // 450f38c9db
	SHA1MSG2 (BX), X2        // 0f38ca13
	SHA1MSG2 (R11), X2       // 410f38ca13
	SHA1MSG2 X2, X2          // 0f38cad2
	SHA1MSG2 X11, X2         // 410f38cad3
	SHA1MSG2 (BX), X11       // 440f38ca1b
	SHA1MSG2 (R11), X11      // 450f38ca1b
	SHA1MSG2 X2, X11         // 440f38cada
	SHA1MSG2 X11, X11        // 450f38cadb
	SHA1NEXTE (BX), X2       // 0f38c813
	SHA1NEXTE (R11), X2      // 410f38c813
	SHA1NEXTE X2, X2         // 0f38c8d2
	SHA1NEXTE X11, X2        // 410f38c8d3
	SHA1NEXTE (BX), X11      // 440f38c81b
	SHA1NEXTE (R11), X11     // 450f38c81b
	SHA1NEXTE X2, X11        // 440f38c8da
	SHA1NEXTE X11, X11       // 450f38c8db
	SHA1RNDS4 $0, (BX), X2   // 0f3acc1300
	SHA1RNDS4 $0, (R11), X2  // 410f3acc1300
	SHA1RNDS4 $1, X2, X2     // 0f3accd201
	SHA1RNDS4 $1, X11, X2    // 410f3accd301
	SHA1RNDS4 $2, (BX), X11  // 440f3acc1b02
	SHA1RNDS4 $2, (R11), X11 // 450f3acc1b02
	SHA1RNDS4 $3, X2, X11    // 440f3accda03
	SHA1RNDS4 $3, X11, X11   // 450f3accdb03
	SHA256MSG1 (BX), X2      // 0f38cc13
	SHA256MSG1 (R11), X2     // 410f38cc13
	SHA256MSG1 X2, X2        // 0f38ccd2
	SHA256MSG1 X11, X2       // 410f38ccd3
	SHA256MSG1 (BX), X11     // 440f38cc1b
	SHA256MSG1 (R11), X11    // 450f38cc1b
	SHA256MSG1 X2, X11       // 440f38ccda
	SHA256MSG1 X11, X11      // 450f38ccdb
	SHA256MSG2 (BX), X2      // 0f38cd13
	SHA256MSG2 (R11), X2     // 410f38cd13
	SHA256MSG2 X2, X2        // 0f38cdd2
	SHA256MSG2 X11, X2       // 410f38cdd3
	SHA256MSG2 (BX), X11     // 440f38cd1b
	SHA256MSG2 (R11), X11    // 450f38cd1b
	SHA256MSG2 X2, X11       // 440f38cdda
	SHA256MSG2 X11, X11      // 450f38cddb
	// Test VPERMQ with both uint8 and int8 immediate args
	VPERMQ $-40, Y8, Y8 // c443fd00c0d8
	VPERMQ $216, Y8, Y8 // c443fd00c0d8
	// Test that VPERMPD that shares ytab list with VPERMQ continues to work too.
	VPERMPD $-40, Y7, Y7 // c4e3fd01ffd8
	VPERMPD $216, Y7, Y7 // c4e3fd01ffd8
	// Check that LEAL is permitted to use overflowing offset.
	LEAL 2400959708(BP)(R10*1), BP // 428dac15dcbc1b8f
	LEAL 3395469782(AX)(R10*1), AX // 428d8410d6c162ca
	// Make sure MOV CR/DR continues to work after changing its movtabs.
	MOVQ CR0, AX // 0f20c0
	MOVQ CR0, DX // 0f20c2
	MOVQ CR4, DI // 0f20e7
	MOVQ AX, CR0 // 0f22c0
	MOVQ DX, CR0 // 0f22c2
	MOVQ DI, CR4 // 0f22e7
	MOVQ DR0, AX // 0f21c0
	MOVQ DR6, DX // 0f21f2
	MOVQ DR7, SI // 0f21fe
	// Test other movtab entries.
	PUSHQ GS // 0fa8
	PUSHQ FS // 0fa0
	POPQ FS  // 0fa1
	POPQ GS  // 0fa9
	// All instructions below semantically have unsigned operands,
	// but previous assembler permitted negative arguments.
	// This behavior is preserved for compatibility reasons.
	VPSHUFD $-79, X7, X7         // c5f970ffb1
	RORXL $-1, (AX), DX          // c4e37bf010ff
	RORXQ $-1, (AX), DX          // c4e3fbf010ff
	VPSHUFD $-1, X1, X2          // c5f970d1ff
	VPSHUFD $-1, Y1, Y2          // c5fd70d1ff
	VPSHUFHW $-1, X1, X2         // c5fa70d1ff
	VPSHUFHW $-1, Y1, Y2         // c5fe70d1ff
	VPSHUFLW $-1, X1, X2         // c5fb70d1ff
	VPSHUFLW $-1, Y1, Y2         // c5ff70d1ff
	VROUNDPD $-1, X1, X2         // c4e37909d1ff
	VROUNDPS $-1, Y1, Y2         // c4e37d08d1ff
	VPSLLD $-1, X1, X2           // c5e972f1ff
	VPSLLD $-1, Y1, Y2           // c5ed72f1ff
	VPSLLDQ $-1, X1, X2          // c5e973f9ff
	VPSLLDQ $-1, Y1, Y2          // c5ed73f9ff
	VPSLLQ $-1, X1, X2           // c5e973f1ff
	VPSLLQ $-1, Y1, Y2           // c5ed73f1ff
	VPSRLD $-1, X1, X2           // c5e972d1ff
	VPSRLD $-1, Y1, Y2           // c5ed72d1ff
	VPSRLDQ $-1, X1, X2          // c5e973d9ff
	VPSRLDQ $-1, Y1, Y2          // c5ed73d9ff
	VPSRLQ $-1, X1, X2           // c5e973d1ff
	VPSRLQ $-1, Y1, Y2           // c5ed73d1ff
	VPEXTRW $-1, X1, (AX)        // c4e3791508ff
	VPEXTRW $-1, X1, AX          // c4e37915c8ff
	VEXTRACTF128 $-1, Y1, X2     // c4e37d19caff
	VEXTRACTI128 $-1, Y1, X2     // c4e37d39caff
	VAESKEYGENASSIST $-1, X1, X2 // c4e379dfd1ff
	VPCMPESTRI $-1, X1, X2       // c4e37961d1ff
	VPCMPESTRM $-1, X1, X2       // c4e37960d1ff
	VPCMPISTRI $-1, X1, X2       // c4e37963d1ff
	VPCMPISTRM $-1, X1, X2       // c4e37962d1ff
	VPERMPD $-1, Y1, Y2          // c4e3fd01d1ff
	VPERMILPD $-1, X1, X2        // c4e37905d1ff
	VPERMILPD $-1, Y1, Y2        // c4e37d05d1ff
	VPERMILPS $-1, X1, X2        // c4e37904d1ff
	VPERMILPS $-1, Y1, Y2        // c4e37d04d1ff
	VCVTPS2PH $-1, X1, X2        // c4e3791dcaff
	VCVTPS2PH $-1, Y1, X2        // c4e37d1dcaff
	VPSLLW $-1, X1, X2           // c5e971f1ff
	VPSLLW $-1, Y1, Y2           // c5ed71f1ff
	VPSRAD $-1, X1, X2           // c5e972e1ff
	VPSRAD $-1, Y1, Y2           // c5ed72e1ff
	VPSRAW $-1, X1, X2           // c5e971e1ff
	VPSRAW $-1, Y1, Y2           // c5ed71e1ff
	VPSRLW $-1, X1, X1           // c5f171d1ff
	VPSRLW $-1, Y1, Y2           // c5ed71d1ff
	VEXTRACTPS $-1, X1, AX       // c4e37917c8ff
	VPEXTRB $-1, X1, AX          // c4e37914c8ff
	VPEXTRD $-1, X1, AX          // c4e37916c8ff
	VPEXTRQ $-1, X1, AX          // c4e3f916c8ff
	// EVEX: High-16 X registers.
	VADDPD X30, X1, X0          // 6291f50858c6
	VADDPD X2, X29, X0          // 62f1950058c2
	VADDPD X30, X29, X0         // 6291950058c6
	VADDPD X2, X1, X28          // 6261f50858e2
	VADDPD X30, X1, X28         // 6201f50858e6
	VADDPD X2, X29, X28         // 6261950058e2
	VADDPD X30, X29, X28        // 6201950058e6
	VADDPD X30, X11, X10        // 6211a50858d6
	VADDPD X12, X29, X10        // 6251950058d4
	VADDPD X30, X29, X10        // 6211950058d6
	VADDPD X12, X11, X28        // 6241a50858e4
	VADDPD X30, X11, X28        // 6201a50858e6
	VADDPD X12, X29, X28        // 6241950058e4
	VADDPD X30, X29, X28        // 6201950058e6
	VADDPD (AX), X29, X0        // 62f195005800
	VADDPD (AX), X1, X28        // 6261f5085820
	VADDPD (AX), X29, X28       // 626195005820
	VADDPD (AX), X29, X10       // 627195005810
	VADDPD (AX), X10, X28       // 6261ad085820
	VADDPD (CX)(AX*1), X29, X0  // 62f19500580401
	VADDPD (CX)(AX*1), X1, X28  // 6261f508582401
	VADDPD (CX)(AX*1), X29, X28 // 62619500582401
	VADDPD (CX)(AX*1), X29, X10 // 62719500581401
	VADDPD (CX)(AX*1), X10, X28 // 6261ad08582401
	VADDPD (CX)(AX*2), X29, X0  // 62f19500580441
	VADDPD (CX)(AX*2), X1, X28  // 6261f508582441
	VADDPD (CX)(AX*2), X29, X28 // 62619500582441
	VADDPD (CX)(AX*2), X29, X10 // 62719500581441
	VADDPD (CX)(AX*2), X10, X28 // 6261ad08582441
	// EVEX: displacement without Disp8.
	VADDPD 15(AX), X29, X0        // 62f1950058800f000000
	VADDPD 15(AX), X1, X28        // 6261f50858a00f000000
	VADDPD 15(AX), X29, X28       // 6261950058a00f000000
	VADDPD 15(AX), X29, X10       // 6271950058900f000000
	VADDPD 15(AX), X10, X28       // 6261ad0858a00f000000
	VADDPD 15(CX)(AX*1), X29, X0  // 62f195005884010f000000
	VADDPD 15(CX)(AX*1), X1, X28  // 6261f50858a4010f000000
	VADDPD 15(CX)(AX*1), X29, X28 // 6261950058a4010f000000
	VADDPD 15(CX)(AX*1), X29, X10 // 627195005894010f000000
	VADDPD 15(CX)(AX*1), X10, X28 // 6261ad0858a4010f000000
	VADDPD 15(CX)(AX*2), X29, X0  // 62f195005884410f000000
	VADDPD 15(CX)(AX*2), X1, X28  // 6261f50858a4410f000000
	VADDPD 15(CX)(AX*2), X29, X28 // 6261950058a4410f000000
	VADDPD 15(CX)(AX*2), X29, X10 // 627195005894410f000000
	VADDPD 15(CX)(AX*2), X10, X28 // 6261ad0858a4410f000000
	// EVEX: compressed displacement (Disp8).
	VADDPD 2032(DX), X29, X0        // 62f1950058427f
	VADDPD 2032(DX), X1, X29        // 6261f508586a7f
	VADDPD 2032(DX), X29, X28       // 6261950058627f
	VADDPD 2032(DX)(AX*2), X29, X0  // 62f195005844427f
	VADDPD 2032(DX)(AX*2), X1, X29  // 6261f508586c427f
	VADDPD 2032(DX)(AX*2), X29, X28 // 626195005864427f
	VADDPD 4064(DX), Y0, Y29        // 6261fd28586a7f
	VADDPD 4064(DX), Y29, Y1        // 62f19520584a7f
	VADDPD 4064(DX), Y28, Y29       // 62619d20586a7f
	VADDPD 4064(DX)(AX*2), Y0, Y29  // 6261fd28586c427f
	VADDPD 4064(DX)(AX*2), Y29, Y1  // 62f19520584c427f
	VADDPD 8128(DX), Z0, Z29        // 6261fd48586a7f
	VADDPD 8128(DX), Z29, Z1        // 62f19540584a7f
	VADDPD 8128(DX), Z28, Z29       // 62619d40586a7f
	VADDPD 8128(DX)(AX*2), Z0, Z29  // 6261fd48586c427f
	VADDPD 8128(DX)(AX*2), Z29, Z1  // 62f19540584c427f
	// EVEX: compressed displacement that does not fit into 8bits.
	VADDPD 2048(DX), X29, X0        // 62f19500588200080000
	VADDPD 2048(DX), X1, X29        // 6261f50858aa00080000
	VADDPD 2048(DX), X29, X28       // 6261950058a200080000
	VADDPD 2048(DX)(AX*2), X29, X0  // 62f1950058844200080000
	VADDPD 2048(DX)(AX*2), X1, X29  // 6261f50858ac4200080000
	VADDPD 2048(DX)(AX*2), X29, X28 // 6261950058a44200080000
	VADDPD 4096(DX), Y0, Y29        // 6261fd2858aa00100000
	VADDPD 4096(DX), Y29, Y1        // 62f19520588a00100000
	VADDPD 4096(DX), Y28, Y29       // 62619d2058aa00100000
	VADDPD 4096(DX)(AX*2), Y0, Y29  // 6261fd2858ac4200100000
	VADDPD 4096(DX)(AX*2), Y29, Y1  // 62f19520588c4200100000
	VADDPD 8192(DX), Z0, Z29        // 6261fd4858aa00200000
	VADDPD 8192(DX), Z29, Z1        // 62f19540588a00200000
	VADDPD 8192(DX), Z28, Z29       // 62619d4058aa00200000
	VADDPD 8192(DX)(AX*2), Z0, Z29  // 6261fd4858ac4200200000
	VADDPD 8192(DX)(AX*2), Z29, Z1  // 62f19540588c4200200000
	// EVEX: Y registers; VL=256.
	VADDPD Y30, Y1, Y0              // 6291f52858c6
	VADDPD Y0, Y29, Y2              // 62f1952058d0
	VADDPD Y0, Y29, Y30             // 6261952058f0
	VADDPD Y28, Y1, Y2              // 6291f52858d4
	VADDPD Y28, Y1, Y30             // 6201f52858f4
	VADDPD Y28, Y29, Y2             // 6291952058d4
	VADDPD Y28, Y29, Y30            // 6201952058f4
	VADDPD Y10, Y11, Y30            // 6241a52858f2
	VADDPD Y10, Y29, Y12            // 6251952058e2
	VADDPD Y10, Y29, Y30            // 6241952058f2
	VADDPD Y28, Y11, Y12            // 6211a52858e4
	VADDPD Y28, Y11, Y30            // 6201a52858f4
	VADDPD Y28, Y29, Y12            // 6211952058e4
	VADDPD Y28, Y29, Y30            // 6201952058f4
	VADDPD (AX), Y29, Y0            // 62f195205800
	VADDPD (AX), Y1, Y28            // 6261f5285820
	VADDPD (AX), Y29, Y28           // 626195205820
	VADDPD (AX), Y29, Y10           // 627195205810
	VADDPD (AX), Y10, Y28           // 6261ad285820
	VADDPD (CX)(AX*1), Y29, Y0      // 62f19520580401
	VADDPD (CX)(AX*1), Y1, Y28      // 6261f528582401
	VADDPD (CX)(AX*1), Y29, Y28     // 62619520582401
	VADDPD (CX)(AX*1), Y29, Y10     // 62719520581401
	VADDPD (CX)(AX*1), Y10, Y28     // 6261ad28582401
	VADDPD (CX)(AX*2), Y29, Y0      // 62f19520580441
	VADDPD (CX)(AX*2), Y1, Y28      // 6261f528582441
	VADDPD (CX)(AX*2), Y29, Y28     // 62619520582441
	VADDPD (CX)(AX*2), Y29, Y10     // 62719520581441
	VADDPD (CX)(AX*2), Y10, Y28     // 6261ad28582441
	VADDPD 15(AX), Y0, Y29          // 6261fd2858a80f000000
	VADDPD 15(AX), Y28, Y1          // 62f19d2058880f000000
	VADDPD 15(AX), Y28, Y29         // 62619d2058a80f000000
	VADDPD 15(AX), Y10, Y29         // 6261ad2858a80f000000
	VADDPD 15(AX), Y28, Y10         // 62719d2058900f000000
	VADDPD 15(CX)(AX*1), Y0, Y29    // 6261fd2858ac010f000000
	VADDPD 15(CX)(AX*1), Y28, Y1    // 62f19d20588c010f000000
	VADDPD 15(CX)(AX*1), Y28, Y29   // 62619d2058ac010f000000
	VADDPD 15(CX)(AX*1), Y10, Y29   // 6261ad2858ac010f000000
	VADDPD 15(CX)(AX*1), Y28, Y10   // 62719d205894010f000000
	VADDPD 15(CX)(AX*2), Y0, Y29    // 6261fd2858ac410f000000
	VADDPD 15(CX)(AX*2), Y28, Y1    // 62f19d20588c410f000000
	VADDPD 15(CX)(AX*2), Y28, Y29   // 62619d2058ac410f000000
	VADDPD 15(CX)(AX*2), Y10, Y29   // 6261ad2858ac410f000000
	VADDPD 15(CX)(AX*2), Y28, Y10   // 62719d205894410f000000
	VADDPD 2048(DX), Y0, Y29        // 6261fd28586a40
	VADDPD 2048(DX), Y29, Y1        // 62f19520584a40
	VADDPD 2048(DX), Y28, Y29       // 62619d20586a40
	VADDPD 2048(DX)(AX*2), Y0, Y29  // 6261fd28586c4240
	VADDPD 2048(DX)(AX*2), Y29, Y1  // 62f19520584c4240
	VADDPD 2048(DX)(AX*2), Y28, Y29 // 62619d20586c4240
	// EVEX: Z registers; VL=512.
	VADDPD Z30, Z0, Z1              // 6291fd4858ce
	VADDPD Z0, Z2, Z29              // 6261ed4858e8
	VADDPD Z0, Z30, Z29             // 62618d4058e8
	VADDPD Z28, Z2, Z1              // 6291ed4858cc
	VADDPD Z28, Z30, Z1             // 62918d4058cc
	VADDPD Z28, Z2, Z29             // 6201ed4858ec
	VADDPD Z28, Z30, Z29            // 62018d4058ec
	VADDPD Z10, Z30, Z11            // 62518d4058da
	VADDPD Z10, Z12, Z29            // 62419d4858ea
	VADDPD Z10, Z30, Z29            // 62418d4058ea
	VADDPD Z28, Z12, Z11            // 62119d4858dc
	VADDPD Z28, Z30, Z11            // 62118d4058dc
	VADDPD Z28, Z12, Z29            // 62019d4858ec
	VADDPD Z28, Z30, Z29            // 62018d4058ec
	VADDPD (AX), Z0, Z29            // 6261fd485828
	VADDPD (AX), Z28, Z1            // 62f19d405808
	VADDPD (AX), Z28, Z29           // 62619d405828
	VADDPD (AX), Z10, Z29           // 6261ad485828
	VADDPD (AX), Z28, Z10           // 62719d405810
	VADDPD (CX)(AX*1), Z0, Z29      // 6261fd48582c01
	VADDPD (CX)(AX*1), Z28, Z1      // 62f19d40580c01
	VADDPD (CX)(AX*1), Z28, Z29     // 62619d40582c01
	VADDPD (CX)(AX*1), Z10, Z29     // 6261ad48582c01
	VADDPD (CX)(AX*1), Z28, Z10     // 62719d40581401
	VADDPD (CX)(AX*2), Z0, Z29      // 6261fd48582c41
	VADDPD (CX)(AX*2), Z28, Z1      // 62f19d40580c41
	VADDPD (CX)(AX*2), Z28, Z29     // 62619d40582c41
	VADDPD (CX)(AX*2), Z10, Z29     // 6261ad48582c41
	VADDPD (CX)(AX*2), Z28, Z10     // 62719d40581441
	VADDPD 15(AX), Z29, Z0          // 62f1954058800f000000
	VADDPD 15(AX), Z1, Z28          // 6261f54858a00f000000
	VADDPD 15(AX), Z29, Z28         // 6261954058a00f000000
	VADDPD 15(AX), Z29, Z10         // 6271954058900f000000
	VADDPD 15(AX), Z10, Z28         // 6261ad4858a00f000000
	VADDPD 15(CX)(AX*1), Z29, Z0    // 62f195405884010f000000
	VADDPD 15(CX)(AX*1), Z1, Z28    // 6261f54858a4010f000000
	VADDPD 15(CX)(AX*1), Z29, Z28   // 6261954058a4010f000000
	VADDPD 15(CX)(AX*1), Z29, Z10   // 627195405894010f000000
	VADDPD 15(CX)(AX*1), Z10, Z28   // 6261ad4858a4010f000000
	VADDPD 15(CX)(AX*2), Z29, Z0    // 62f195405884410f000000
	VADDPD 15(CX)(AX*2), Z1, Z28    // 6261f54858a4410f000000
	VADDPD 15(CX)(AX*2), Z29, Z28   // 6261954058a4410f000000
	VADDPD 15(CX)(AX*2), Z29, Z10   // 627195405894410f000000
	VADDPD 15(CX)(AX*2), Z10, Z28   // 6261ad4858a4410f000000
	VADDPD 2048(DX), Z29, Z0        // 62f19540584220
	VADDPD 2048(DX), Z1, Z29        // 6261f548586a20
	VADDPD 2048(DX), Z29, Z28       // 62619540586220
	VADDPD 2048(DX)(AX*2), Z29, Z0  // 62f1954058444220
	VADDPD 2048(DX)(AX*2), Z1, Z29  // 6261f548586c4220
	VADDPD 2048(DX)(AX*2), Z29, Z28 // 6261954058644220
	// EVEX: KOP (opmask) instructions.
	KMOVB K0, K0          // c5f990c0
	KMOVB K7, K7          // c5f990ff
	KMOVB K5, K1          // c5f990cd
	KMOVB K1, K5          // c5f990e9
	KMOVB (AX), K1        // c5f99008
	KMOVB K0, (AX)        // c5f99100
	KMOVB K7, (R10)       // c4c179913a
	KMOVB K5, AX          // c5f993c5
	KMOVB K7, R10         // c57993d7
	KMOVB AX, K5          // c5f992e8
	KMOVB R10, K7         // c4c17992fa
	KMOVW K0, K0          // c5f890c0
	KMOVW K7, K7          // c5f890ff
	KMOVW K5, K1          // c5f890cd
	KMOVW K1, K5          // c5f890e9
	KMOVW (AX), K1        // c5f89008
	KMOVW K0, (AX)        // c5f89100
	KMOVW K7, (R10)       // c4c178913a
	KMOVW K5, AX          // c5f893c5
	KMOVW K7, R10         // c57893d7
	KMOVW AX, K5          // c5f892e8
	KMOVW R10, K7         // c4c17892fa
	KMOVD K0, K0          // c4e1f990c0
	KMOVD K7, K7          // c4e1f990ff
	KMOVD K5, K1          // c4e1f990cd
	KMOVD K1, K5          // c4e1f990e9
	KMOVD (AX), K1        // c4e1f99008
	KMOVD AX, K5          // c5fb92e8
	KMOVD R10, K7         // c4c17b92fa
	KMOVD K0, (AX)        // c4e1f99100
	KMOVD K7, (R10)       // c4c1f9913a
	KMOVD K5, AX          // c5fb93c5
	KMOVD K7, R10         // c57b93d7
	KMOVQ K0, K0          // c4e1f890c0
	KMOVQ K7, K7          // c4e1f890ff
	KMOVQ K5, K1          // c4e1f890cd
	KMOVQ K1, K5          // c4e1f890e9
	KMOVQ (AX), K1        // c4e1f89008
	KMOVQ AX, K5          // c4e1fb92e8
	KMOVQ R10, K7         // c4c1fb92fa
	KMOVQ K0, (AX)        // c4e1f89100
	KMOVQ K7, (R10)       // c4c1f8913a
	KMOVQ K5, AX          // c4e1fb93c5
	KMOVQ K7, R10         // c461fb93d7
	KNOTB K7, K0          // c5f944c7
	KNOTB K1, K5          // c5f944e9
	KNOTW K7, K0          // c5f844c7
	KNOTW K1, K5          // c5f844e9
	KNOTD K7, K0          // c4e1f944c7
	KNOTD K1, K5          // c4e1f944e9
	KNOTQ K7, K0          // c4e1f844c7
	KNOTQ K1, K5          // c4e1f844e9
	KORB K7, K5, K0       // c5d545c7
	KORB K0, K7, K5       // c5c545e8
	KORW K7, K5, K0       // c5d445c7
	KORW K0, K7, K5       // c5c445e8
	KORD K7, K5, K0       // c4e1d545c7
	KORD K0, K7, K5       // c4e1c545e8
	KORQ K7, K5, K0       // c4e1d445c7
	KORQ K0, K7, K5       // c4e1c445e8
	KSHIFTLB $0, K7, K0   // c4e37932c700
	KSHIFTLB $196, K1, K5 // c4e37932e9c4
	KSHIFTLW $0, K7, K0   // c4e3f932c700
	KSHIFTLW $196, K1, K5 // c4e3f932e9c4
	KSHIFTLD $0, K7, K0   // c4e37933c700
	KSHIFTLD $196, K1, K5 // c4e37933e9c4
	KSHIFTLQ $0, K7, K0   // c4e3f933c700
	KSHIFTLQ $196, K1, K5 // c4e3f933e9c4
	// EVEX: masking with K1-K7.
	VADDPD X2, X1, K1, X0            // 62f1f50958c2
	VADDPD X12, X1, K4, X10          // 6251f50c58d4
	VADDPD X22, X1, K7, X20          // 62a1f50f58e6
	VADDPD (AX), X1, K1, X1          // 62f1f5095808
	VADDPD 8(R10), X10, K4, X10      // 6251ad0c589208000000
	VADDPD (R10)(AX*4), X20, K7, X20 // 62c1dd07582482
	VADDPD Y2, Y1, K1, Y0            // 62f1f52958c2
	VADDPD Y12, Y1, K4, Y10          // 6251f52c58d4
	VADDPD Y22, Y1, K7, Y20          // 62a1f52f58e6
	VADDPD (AX), Y1, K1, Y1          // 62f1f5295808
	VADDPD 8(R10), Y10, K4, Y10      // 6251ad2c589208000000
	VADDPD (R10)(AX*4), Y20, K7, Y20 // 62c1dd27582482
	VADDPD Z2, Z1, K1, Z0            // 62f1f54958c2
	VADDPD Z12, Z1, K4, Z10          // 6251f54c58d4
	VADDPD Z22, Z1, K7, Z20          // 62a1f54f58e6
	VADDPD (AX), Z1, K1, Z1          // 62f1f5495808
	VADDPD 8(R10), Z10, K4, Z10      // 6251ad4c589208000000
	VADDPD (R10)(AX*4), Z20, K7, Z20 // 62c1dd47582482
	// EVEX gather (also tests Z as VSIB index).
	VPGATHERDD 360(AX)(X2*4), K1, X1    // 62f27d09904c905a
	VPGATHERDD 640(BP)(X15*8), K3, X14  // 62327d0b90b4fd80020000
	VPGATHERDD 960(R10)(X25*2), K7, X24 // 62027d0790844ac0030000
	VPGATHERDD 1280(R10)(X1*4), K4, X0  // 62d27d0c90848a00050000
	VPGATHERDD 360(AX)(Y2*4), K1, Y1    // 62f27d29904c905a
	VPGATHERDD 640(BP)(Y15*8), K3, Y14  // 62327d2b90b4fd80020000
	VPGATHERDD 960(R10)(Y25*2), K7, Y24 // 62027d2790844ac0030000
	VPGATHERDD 1280(R10)(Y1*4), K4, Y0  // 62d27d2c90848a00050000
	VPGATHERDD 360(AX)(Z2*4), K1, Z1    // 62f27d49904c905a
	VPGATHERDD 640(BP)(Z15*8), K3, Z14  // 62327d4b90b4fd80020000
	VPGATHERDD 960(R10)(Z25*2), K7, Z24 // 62027d4790844ac0030000
	VPGATHERDD 1280(R10)(Z1*4), K4, Z0  // 62d27d4c90848a00050000
	VPGATHERDQ 360(AX)(X2*4), K1, X1    // 62f2fd09904c902d
	VPGATHERDQ 640(BP)(X15*8), K3, X14  // 6232fd0b9074fd50
	VPGATHERDQ 960(R10)(X25*2), K7, X24 // 6202fd0790444a78
	VPGATHERDQ 1280(R10)(X1*4), K4, X0  // 62d2fd0c90848a00050000
	VPGATHERDQ 360(AX)(X2*4), K1, Y1    // 62f2fd29904c902d
	VPGATHERDQ 640(BP)(X15*8), K3, Y14  // 6232fd2b9074fd50
	VPGATHERDQ 960(R10)(X25*2), K7, Y24 // 6202fd2790444a78
	VPGATHERDQ 1280(R10)(X1*4), K4, Y0  // 62d2fd2c90848a00050000
	VPGATHERDQ 360(AX)(Y2*4), K1, Z1    // 62f2fd49904c902d
	VPGATHERDQ 640(BP)(Y15*8), K3, Z14  // 6232fd4b9074fd50
	VPGATHERDQ 960(R10)(Y25*2), K7, Z24 // 6202fd4790444a78
	VPGATHERDQ 1280(R10)(Y1*4), K4, Z0  // 62d2fd4c90848a00050000
	VGATHERDPD 360(R15)(X30*2), K6, X20 // 6282fd069264772d
	VGATHERDPD 640(R15)(X20*2), K6, X10 // 6252fd0692546750
	VGATHERDPD 960(R15)(X10*2), K6, X20 // 6282fd0e92645778
	VGATHERDPD 1280(R15)(X0*2), K6, X10 // 6252fd0e92944700050000
	VGATHERDPD 360(R15)(X30*2), K6, Y20 // 6282fd269264772d
	VGATHERDPD 640(R15)(X20*2), K6, Y10 // 6252fd2692546750
	VGATHERDPD 960(R15)(X10*2), K6, Y20 // 6282fd2e92645778
	VGATHERDPD 1280(R15)(X0*2), K6, Y10 // 6252fd2e92944700050000
	VGATHERDPD 360(R15)(Y30*2), K6, Z20 // 6282fd469264772d
	VGATHERDPD 640(R15)(Y20*2), K6, Z10 // 6252fd4692546750
	VGATHERDPD 960(R15)(Y10*2), K6, Z20 // 6282fd4e92645778
	VGATHERDPD 1280(R15)(Y0*2), K6, Z10 // 6252fd4e92944700050000
	VGATHERDPS 360(R15)(X30*2), K6, X20 // 62827d069264775a
	VGATHERDPS 640(R15)(X20*2), K6, X10 // 62527d0692946780020000
	VGATHERDPS 960(R15)(X10*2), K6, X20 // 62827d0e92a457c0030000
	VGATHERDPS 1280(R15)(X0*2), K6, X10 // 62527d0e92944700050000
	VGATHERDPS 360(R15)(Y30*2), K6, Y20 // 62827d269264775a
	VGATHERDPS 640(R15)(Y20*2), K6, Y10 // 62527d2692946780020000
	VGATHERDPS 960(R15)(Y10*2), K6, Y20 // 62827d2e92a457c0030000
	VGATHERDPS 1280(R15)(Y0*2), K6, Y10 // 62527d2e92944700050000
	VGATHERDPS 360(R15)(Z30*2), K6, Z20 // 62827d469264775a
	VGATHERDPS 640(R15)(Z20*2), K6, Z10 // 62527d4692946780020000
	VGATHERDPS 960(R15)(Z10*2), K6, Z20 // 62827d4e92a457c0030000
	VGATHERDPS 1280(R15)(Z0*2), K6, Z10 // 62527d4e92944700050000
	VGATHERQPS 360(R15)(X30*2), K6, X20 // 62827d069364775a
	VGATHERQPS 640(R15)(X20*2), K6, X10 // 62527d0693946780020000
	VGATHERQPS 960(R15)(X10*2), K6, X20 // 62827d0e93a457c0030000
	VGATHERQPS 1280(R15)(X0*2), K6, X10 // 62527d0e93944700050000
	VGATHERQPS 360(R15)(Y30*2), K6, X20 // 62827d269364775a
	VGATHERQPS 640(R15)(Y20*2), K6, X10 // 62527d2693946780020000
	VGATHERQPS 960(R15)(Y10*2), K6, X20 // 62827d2e93a457c0030000
	VGATHERQPS 1280(R15)(Y0*2), K6, X10 // 62527d2e93944700050000
	VGATHERQPS 360(R15)(Z30*2), K6, Y20 // 62827d469364775a
	VGATHERQPS 640(R15)(Z20*2), K6, Y10 // 62527d4693946780020000
	VGATHERQPS 960(R15)(Z10*2), K6, Y20 // 62827d4e93a457c0030000
	VGATHERQPS 1280(R15)(Z0*2), K6, Y10 // 62527d4e93944700050000
	VPGATHERQD 360(R15)(X30*2), K6, X20 // 62827d069164775a
	VPGATHERQD 640(R15)(X20*2), K6, X10 // 62527d0691946780020000
	VPGATHERQD 960(R15)(X10*2), K6, X20 // 62827d0e91a457c0030000
	VPGATHERQD 1280(R15)(X0*2), K6, X10 // 62527d0e91944700050000
	VPGATHERQD 360(R15)(Y30*2), K6, X20 // 62827d269164775a
	VPGATHERQD 640(R15)(Y20*2), K6, X10 // 62527d2691946780020000
	VPGATHERQD 960(R15)(Y10*2), K6, X20 // 62827d2e91a457c0030000
	VPGATHERQD 1280(R15)(Y0*2), K6, X10 // 62527d2e91944700050000
	VPGATHERQD 360(R15)(Z30*2), K6, Y20 // 62827d469164775a
	VPGATHERQD 640(R15)(Z20*2), K6, Y10 // 62527d4691946780020000
	VPGATHERQD 960(R15)(Z10*2), K6, Y20 // 62827d4e91a457c0030000
	VPGATHERQD 1280(R15)(Z0*2), K6, Y10 // 62527d4e91944700050000
	VPGATHERQQ 360(R15)(X30*2), K6, X20 // 6282fd069164772d
	VPGATHERQQ 640(R15)(X20*2), K6, X10 // 6252fd0691546750
	VPGATHERQQ 960(R15)(X10*2), K6, X20 // 6282fd0e91645778
	VPGATHERQQ 1280(R15)(X0*2), K6, X10 // 6252fd0e91944700050000
	VPGATHERQQ 360(R15)(Y30*2), K6, Y20 // 6282fd269164772d
	VPGATHERQQ 640(R15)(Y20*2), K6, Y10 // 6252fd2691546750
	VPGATHERQQ 960(R15)(Y10*2), K6, Y20 // 6282fd2e91645778
	VPGATHERQQ 1280(R15)(Y0*2), K6, Y10 // 6252fd2e91944700050000
	VPGATHERQQ 360(R15)(Z30*2), K6, Z20 // 6282fd469164772d
	VPGATHERQQ 640(R15)(Z20*2), K6, Z10 // 6252fd4691546750
	VPGATHERQQ 960(R15)(Z10*2), K6, Z20 // 6282fd4e91645778
	VPGATHERQQ 1280(R15)(Z0*2), K6, Z10 // 6252fd4e91944700050000
	VGATHERQPD 360(R15)(X30*2), K6, X20 // 6282fd069364772d
	VGATHERQPD 640(R15)(X20*2), K6, X10 // 6252fd0693546750
	VGATHERQPD 960(R15)(X10*2), K6, X20 // 6282fd0e93645778
	VGATHERQPD 1280(R15)(X0*2), K6, X10 // 6252fd0e93944700050000
	VGATHERQPD 360(R15)(Y30*2), K6, Y20 // 6282fd269364772d
	VGATHERQPD 640(R15)(Y20*2), K6, Y10 // 6252fd2693546750
	VGATHERQPD 960(R15)(Y10*2), K6, Y20 // 6282fd2e93645778
	VGATHERQPD 1280(R15)(Y0*2), K6, Y10 // 6252fd2e93944700050000
	VGATHERQPD 360(R15)(Z30*2), K6, Z20 // 6282fd469364772d
	VGATHERQPD 640(R15)(Z20*2), K6, Z10 // 6252fd4693546750
	VGATHERQPD 960(R15)(Z10*2), K6, Z20 // 6282fd4e93645778
	VGATHERQPD 1280(R15)(Z0*2), K6, Z10 // 6252fd4e93944700050000
	// EVEX: corner cases for High-16 registers.
	VADDPD X31, X16, X15          // 6211fd0058ff
	VADDPD X23, X15, X16          // 62a1850858c7
	VADDPD Y31, Y16, Y15          // 6211fd2058ff
	VADDPD Y23, Y15, Y16          // 62a1852858c7
	VADDPD Z31, Z16, Z15          // 6211fd4058ff
	VADDPD Z23, Z15, Z16          // 62a1854858c7
	VGATHERQPD (DX)(X16*1),K1,X31 // 6262fd01933c02
	VGATHERQPD (DX)(X31*1),K1,X16 // 62a2fd0193043a
	VGATHERQPD (DX)(X15*1),K1,X23 // 62a2fd09933c3a
	VGATHERQPD (DX)(X23*1),K1,X15 // 6272fd01933c3a
	VGATHERQPD (DX)(Y16*1),K1,Y31 // 6262fd21933c02
	VGATHERQPD (DX)(Y31*1),K1,Y16 // 62a2fd2193043a
	VGATHERQPD (DX)(Y15*1),K1,Y23 // 62a2fd29933c3a
	VGATHERQPD (DX)(Y23*1),K1,Y15 // 6272fd21933c3a
	VGATHERQPD (DX)(Z16*1),K1,Z31 // 6262fd41933c02
	VGATHERQPD (DX)(Z31*1),K1,Z16 // 62a2fd4193043a
	VGATHERQPD (DX)(Z15*1),K1,Z23 // 62a2fd49933c3a
	VGATHERQPD (DX)(Z23*1),K1,Z15 // 6272fd41933c3a
	// EVEX: VCVTPD2DQ with Y suffix (VL=2).
	VCVTPD2DQY (BX), X20  // 62e1ff28e623
	VCVTPD2DQY (R11), X30 // 6241ff28e633
	// XED encoder uses EVEX.X=0 for these; most x86 tools use EVEX.X=1.
	// Either way is OK.
	VMOVQ SP, X20  // 62e1fd086ee4 or 62a1fd086ee4
	VMOVQ BP, X20  // 62e1fd086ee5 or 62a1fd086ee5
	VMOVQ R14, X20 // 62c1fd086ee6 or 6281fd086ee6
	// "VMOVQ r/m64, xmm1"/6E vs "VMOVQ xmm2/m64, xmm1"/7E with mem operand.
	VMOVQ (AX), X20           // 62e1fd086e20 or 62e1fe087e20
	VMOVQ 7(DX), X20          // 62e1fd086ea207000000 or 62e1fe087ea207000000
	VMOVQ -15(R11)(CX*1), X20 // 62c1fd086ea40bf1ffffff or 62c1fe087ea40bf1ffffff
	VMOVQ (SP)(AX*2), X20     // 62e1fd086e2444 or 62e1fe087e2444
	// "VMOVQ xmm1, r/m64"/7E vs "VMOVQ xmm1, xmm2/m64"/D6 with mem operand.
	VMOVQ X20, (AX)           // 62e1fd087e20 or 62e1fd08d620
	VMOVQ X20, 7(DX)          // 62e1fd087ea207000000 or 62e1fd08d6a207000000
	VMOVQ X20, -15(R11)(CX*1) // 62c1fd087ea40bf1ffffff or 62c1fd08d6a40bf1ffffff
	VMOVQ X20, (SP)(AX*2)     // 62e1fd087e2444 or 62e1fd08d62444
	// VMOVHPD: overlapping VEX and EVEX variants.
	VMOVHPD (AX), X5, X5             // c5d11628 or c4e1d11628 or 62f1d5281628 or 62f1d5481628
	VMOVHPD 7(DX), X5, X5            // c5d1166a07 or 62f1d52816aa07000000 or 62f1d54816aa07000000
	VMOVHPD -15(R11)(CX*1), X5, X5   // c4c151166c0bf1 or 62d1d52816ac0bf1ffffff or 62d1d54816ac0bf1ffffff
	VMOVHPD (SP)(AX*2), X5, X5       // c5d1162c44 or c4e1d1162c44 or 62f1d528162c44 or 62f1d548162c44
	VMOVHPD (AX), X8, X5             // c5b91628 or c4e1b91628 or 62f1bd281628 or 62f1bd481628
	VMOVHPD 7(DX), X8, X5            // c5b9166a07 or 62f1bd2816aa07000000 or 62f1bd4816aa07000000
	VMOVHPD -15(R11)(CX*1), X8, X5   // c4c139166c0bf1 or 62d1bd4816ac0bf1ffffff
	VMOVHPD (SP)(AX*2), X8, X5       // c5b9162c44 or c4e1b9162c44 or 62f1bd28162c44 or 62f1bd48162c44
	VMOVHPD (AX), X20, X5            // 62f1dd001628 or 62f1dd201628 or 62f1dd401628
	VMOVHPD 7(DX), X20, X5           // 62f1dd0016aa07000000 or 62f1dd2016aa07000000 or 62f1dd4016aa07000000
	VMOVHPD -15(R11)(CX*1), X20, X5  // 62d1dd0016ac0bf1ffffff or 62d1dd2016ac0bf1ffffff or 62d1dd4016ac0bf1ffffff
	VMOVHPD (SP)(AX*2), X20, X5      // 62f1dd00162c44 or 62f1dd20162c44 or 62f1dd40162c44
	VMOVHPD (AX), X5, X8             // c5511600 or c461d11600 or 6271d5281600 or 6271d5481600
	VMOVHPD 7(DX), X5, X8            // c551164207 or 6271d528168207000000 or 6271d548168207000000
	VMOVHPD -15(R11)(CX*1), X5, X8   // c4415116440bf1 or 6251d52816840bf1ffffff or 6251d54816840bf1ffffff
	VMOVHPD (SP)(AX*2), X5, X8       // c551160444 or 6271d528160444 or 6271d548160444
	VMOVHPD (AX), X8, X8             // c5391600 or 6271bd281600 or 6271bd481600
	VMOVHPD 7(DX), X8, X8            // c539164207 or 6271bd28168207000000 or 6271bd48168207000000
	VMOVHPD -15(R11)(CX*1), X8, X8   // c4413916440bf1 or 6251bd2816840bf1ffffff or 6251bd4816840bf1ffffff
	VMOVHPD (SP)(AX*2), X8, X8       // c539160444 or 6271bd28160444 or 6271bd48160444
	VMOVHPD (AX), X20, X8            // 6271dd001600 or 6271dd201600 or 6271dd401600
	VMOVHPD 7(DX), X20, X8           // 6271dd00168207000000 or 6271dd20168207000000 or 6271dd40168207000000
	VMOVHPD -15(R11)(CX*1), X20, X8  // 6251dd0016840bf1ffffff or 6251dd2016840bf1ffffff or 6251dd4016840bf1ffffff
	VMOVHPD (SP)(AX*2), X20, X8      // 6271dd00160444 or 6271dd20160444 or 6271dd40160444
	VMOVHPD (AX), X5, X20            // 62e1d5081620 or 62e1d5281620 or 62e1d5481620
	VMOVHPD 7(DX), X5, X20           // 62e1d50816a207000000 or 62e1d52816a207000000 or 62e1d54816a207000000
	VMOVHPD -15(R11)(CX*1), X5, X20  // 62c1d50816a40bf1ffffff or 62c1d52816a40bf1ffffff or 62c1d54816a40bf1ffffff
	VMOVHPD (SP)(AX*2), X5, X20      // 62e1d508162444 or 62e1d528162444 or 62e1d548162444
	VMOVHPD (AX), X8, X20            // 62e1bd081620 or 62e1bd281620 or 62e1bd481620
	VMOVHPD 7(DX), X8, X20           // 62e1bd0816a207000000 or 62e1bd2816a207000000 or 62e1bd4816a207000000
	VMOVHPD -15(R11)(CX*1), X8, X20  // 62c1bd0816a40bf1ffffff or 62c1bd2816a40bf1ffffff or 62c1bd4816a40bf1ffffff
	VMOVHPD (SP)(AX*2), X8, X20      // 62e1bd08162444 or 62e1bd28162444 or 62e1bd48162444
	VMOVHPD (AX), X20, X20           // 62e1dd001620 or 62e1dd201620 or 62e1dd401620
	VMOVHPD 7(DX), X20, X20          // 62e1dd0016a207000000 or 62e1dd2016a207000000 or 62e1dd4016a207000000
	VMOVHPD -15(R11)(CX*1), X20, X20 // 62c1dd0016a40bf1ffffff or 62c1dd2016a40bf1ffffff or 62c1dd4016a40bf1ffffff
	VMOVHPD (SP)(AX*2), X20, X20     // 62e1dd00162444 or 62e1dd20162444 or 62e1dd40162444
	VMOVHPD X5, (AX)                 // c5f91728 or 62f1fd281728 or 62f1fd481728
	VMOVHPD X8, (AX)                 // c5791700 or 6271fd281700 or 6271fd481700
	VMOVHPD X20, (AX)                // 62e1fd081720 or 62e1fd281720 or 62e1fd481720
	VMOVHPD X5, 7(DX)                // c5f9176a07 or 62f1fd2817aa07000000 or 62f1fd4817aa07000000
	VMOVHPD X8, 7(DX)                // c579174207 or 6271fd28178207000000 or 6271fd48178207000000
	VMOVHPD X20, 7(DX)               // 62e1fd0817a207000000 or 62e1fd2817a207000000 or 62e1fd4817a207000000
	VMOVHPD X5, -15(R11)(CX*1)       // c4c179176c0bf1 or 62d1fd2817ac0bf1ffffff or 62d1fd4817ac0bf1ffffff
	VMOVHPD X8, -15(R11)(CX*1)       // c4417917440bf1 or 6251fd2817840bf1ffffff or 6251fd4817840bf1ffffff
	VMOVHPD X20, -15(R11)(CX*1)      // 62c1fd0817a40bf1ffffff or 62c1fd2817a40bf1ffffff or 62c1fd4817a40bf1ffffff
	VMOVHPD X5, (SP)(AX*2)           // c5f9172c44 or 62f1fd28172c44 or 62f1fd48172c44
	VMOVHPD X8, (SP)(AX*2)           // c579170444 or 6271fd28170444 or 6271fd48170444
	VMOVHPD X20, (SP)(AX*2)          // 62e1fd08172444 or 62e1fd28172444 or 62e1fd48172444
	// VMOVLPD: overlapping VEX and EVEX variants.
	VMOVLPD (AX), X5, X5             // c5d11228 or 62f1d5281228 or 62f1d5481228
	VMOVLPD 7(DX), X5, X5            // c5d1126a07 or 62f1d52812aa07000000 or 62f1d54812aa07000000
	VMOVLPD -15(R11)(CX*1), X5, X5   // c4c151126c0bf1 or 62d1d52812ac0bf1ffffff or 62d1d54812ac0bf1ffffff
	VMOVLPD (SP)(AX*2), X5, X5       // c5d1122c44 or 62f1d528122c44 or 62f1d548122c44
	VMOVLPD (AX), X8, X5             // c5b91228 or 62f1bd281228 or 62f1bd481228
	VMOVLPD 7(DX), X8, X5            // c5b9126a07 or 62f1bd2812aa07000000 or 62f1bd4812aa07000000
	VMOVLPD -15(R11)(CX*1), X8, X5   // c4c139126c0bf1 or 62d1bd2812ac0bf1ffffff or 62d1bd4812ac0bf1ffffff
	VMOVLPD (SP)(AX*2), X8, X5       // c5b9122c44 or 62f1bd28122c44 or 62f1bd48122c44
	VMOVLPD (AX), X20, X5            // 62f1dd001228 or 62f1dd201228 or 62f1dd401228
	VMOVLPD 7(DX), X20, X5           // 62f1dd0012aa07000000 or 62f1dd2012aa07000000 or 62f1dd4012aa07000000
	VMOVLPD -15(R11)(CX*1), X20, X5  // 62d1dd0012ac0bf1ffffff or 62d1dd2012ac0bf1ffffff or 62d1dd4012ac0bf1ffffff
	VMOVLPD (SP)(AX*2), X20, X5      // 62f1dd00122c44 or 62f1dd20122c44 or 62f1dd40122c44
	VMOVLPD (AX), X5, X8             // c5511200 or 6271d5281200 or 6271d5481200
	VMOVLPD 7(DX), X5, X8            // c551124207 or 6271d528128207000000 or 6271d548128207000000
	VMOVLPD -15(R11)(CX*1), X5, X8   // c4415112440bf1 or 6251d52812840bf1ffffff or 6251d54812840bf1ffffff
	VMOVLPD (SP)(AX*2), X5, X8       // c551120444 or 6271d528120444 or 6271d548120444
	VMOVLPD (AX), X8, X8             // c5391200 or 6271bd281200 or 6271bd481200
	VMOVLPD 7(DX), X8, X8            // c539124207 or 6271bd28128207000000 or 6271bd48128207000000
	VMOVLPD -15(R11)(CX*1), X8, X8   // c4413912440bf1 or 6251bd2812840bf1ffffff or 6251bd4812840bf1ffffff
	VMOVLPD (SP)(AX*2), X8, X8       // c539120444 or 6271bd28120444 or 6271bd48120444
	VMOVLPD (AX), X20, X8            // 6271dd001200 or 6271dd201200 or 6271dd401200
	VMOVLPD 7(DX), X20, X8           // 6271dd00128207000000 or 6271dd20128207000000 or 6271dd40128207000000
	VMOVLPD -15(R11)(CX*1), X20, X8  // 6251dd0012840bf1ffffff or 6251dd2012840bf1ffffff or 6251dd4012840bf1ffffff
	VMOVLPD (SP)(AX*2), X20, X8      // 6271dd00120444 or 6271dd20120444 or 6271dd40120444
	VMOVLPD (AX), X5, X20            // 62e1d5081220 or 62e1d5281220 or 62e1d5481220
	VMOVLPD 7(DX), X5, X20           // 62e1d50812a207000000 or 62e1d52812a207000000 or 62e1d54812a207000000
	VMOVLPD -15(R11)(CX*1), X5, X20  // 62c1d50812a40bf1ffffff or 62c1d52812a40bf1ffffff or 62c1d54812a40bf1ffffff
	VMOVLPD (SP)(AX*2), X5, X20      // 62e1d508122444 or 62e1d528122444 or 62e1d548122444
	VMOVLPD (AX), X8, X20            // 62e1bd081220 or 62e1bd281220 or 62e1bd481220
	VMOVLPD 7(DX), X8, X20           // 62e1bd0812a207000000 or 62e1bd2812a207000000 or 62e1bd4812a207000000
	VMOVLPD -15(R11)(CX*1), X8, X20  // 62c1bd0812a40bf1ffffff or 62c1bd2812a40bf1ffffff or 62c1bd4812a40bf1ffffff
	VMOVLPD (SP)(AX*2), X8, X20      // 62e1bd08122444 or 62e1bd28122444 or 62e1bd48122444
	VMOVLPD (AX), X20, X20           // 62e1dd001220 or 62e1dd201220 or 62e1dd401220
	VMOVLPD 7(DX), X20, X20          // 62e1dd0012a207000000 or 62e1dd2012a207000000 or 62e1dd4012a207000000
	VMOVLPD -15(R11)(CX*1), X20, X20 // 62c1dd0012a40bf1ffffff or 62c1dd2012a40bf1ffffff or 62c1dd4012a40bf1ffffff
	VMOVLPD (SP)(AX*2), X20, X20     // 62e1dd00122444 or 62e1dd20122444 or 62e1dd40122444
	VMOVLPD X5, (AX)                 // c5f91328 or 62f1fd281328 or 62f1fd481328
	VMOVLPD X8, (AX)                 // c5791300 or 6271fd281300 or 6271fd481300
	VMOVLPD X20, (AX)                // 62e1fd081320 or 62e1fd281320 or 62e1fd481320
	VMOVLPD X5, 7(DX)                // c5f9136a07 or 62f1fd2813aa07000000 or 62f1fd4813aa07000000
	VMOVLPD X8, 7(DX)                // c579134207 or 6271fd28138207000000 or 6271fd48138207000000
	VMOVLPD X20, 7(DX)               // 62e1fd0813a207000000 or 62e1fd2813a207000000 or 62e1fd4813a207000000
	VMOVLPD X5, -15(R11)(CX*1)       // c4c179136c0bf1 or 62d1fd2813ac0bf1ffffff or 62d1fd4813ac0bf1ffffff
	VMOVLPD X8, -15(R11)(CX*1)       // c4417913440bf1 or 6251fd2813840bf1ffffff or 6251fd4813840bf1ffffff
	VMOVLPD X20, -15(R11)(CX*1)      // 62c1fd0813a40bf1ffffff or 62c1fd2813a40bf1ffffff or 62c1fd4813a40bf1ffffff
	VMOVLPD X5, (SP)(AX*2)           // c5f9132c44 or 62f1fd28132c44 or 62f1fd48132c44
	VMOVLPD X8, (SP)(AX*2)           // c579130444 or 6271fd28130444 or 6271fd48130444
	VMOVLPD X20, (SP)(AX*2)          // 62e1fd08132444 or 62e1fd28132444 or 62e1fd48132444
	// "VPEXTRW imm8u, xmm1, r32/m16"/15 vs "VPEXTRW imm8u, xmm2, r32"/C5.
	VPEXTRW $17, X20, AX              // 62b17d08c5c411 or 62e37d0815e011 or 62e3fd0815e011
	VPEXTRW $127, X20, AX             // 62b17d08c5c47f or 62e37d0815e07f or 62e3fd0815e07f
	VPEXTRW $17, X20, SP              // 62b17d08c5e411 or 62e37d0815e411 or 62e3fd0815e411
	VPEXTRW $127, X20, SP             // 62b17d08c5e47f or 62e37d0815e47f or 62e3fd0815e47f
	VPEXTRW $17, X20, BP              // 62b17d08c5ec11 or 62e37d0815e511 or 62e3fd0815e511
	VPEXTRW $127, X20, BP             // 62b17d08c5ec7f or 62e37d0815e57f or 62e3fd0815e57f
	VPEXTRW $17, X20, R14             // 62317d08c5f411 or 62c37d0815e611 or 62c3fd0815e611
	VPEXTRW $127, X20, R14            // 62317d08c5f47f or 62c37d0815e67f or 62c3fd0815e67f
	VPEXTRW $17, X20, (AX)            // 62e37d08152011 or 62e3fd08152011
	VPEXTRW $127, X20, (AX)           // 62e37d0815207f or 62e3fd0815207f
	VPEXTRW $17, X20, 7(DX)           // 62e37d0815a20700000011 or 62e3fd0815a20700000011
	VPEXTRW $127, X20, 7(DX)          // 62e37d0815a2070000007f or 62e3fd0815a2070000007f
	VPEXTRW $17, X20, -15(R11)(CX*1)  // 62c37d0815a40bf1ffffff11 or 62c3fd0815a40bf1ffffff11
	VPEXTRW $127, X20, -15(R11)(CX*1) // 62c37d0815a40bf1ffffff7f or 62c3fd0815a40bf1ffffff7f
	VPEXTRW $17, X20, (SP)(AX*2)      // 62e37d0815244411 or 62e3fd0815244411
	VPEXTRW $127, X20, (SP)(AX*2)     // 62e37d081524447f or 62e3fd081524447f
	// EVEX: embedded zeroing.
	VADDPD.Z X30, X1, K7, X0  // 6291f58f58c6
	VMAXPD.Z (AX), Z2, K1, Z1 // 62f1edc95f08
	// EVEX: embedded rounding.
	VADDPD.RU_SAE Z3, Z2, K1, Z1   // 62f1ed5958cb
	VADDPD.RD_SAE Z3, Z2, K1, Z1   // 62f1ed3958cb
	VADDPD.RZ_SAE Z3, Z2, K1, Z1   // 62f1ed7958cb
	VADDPD.RN_SAE Z3, Z2, K1, Z1   // 62f1ed1958cb
	VADDPD.RU_SAE.Z Z3, Z2, K1, Z1 // 62f1edd958cb
	VADDPD.RD_SAE.Z Z3, Z2, K1, Z1 // 62f1edb958cb
	VADDPD.RZ_SAE.Z Z3, Z2, K1, Z1 // 62f1edf958cb
	VADDPD.RN_SAE.Z Z3, Z2, K1, Z1 // 62f1ed9958cb
	// EVEX: embedded broadcasting.
	VADDPD.BCST (AX), X2, K1, X1   // 62f1ed195808
	VADDPD.BCST.Z (AX), X2, K1, X1 // 62f1ed995808
	VADDPD.BCST (AX), Y2, K1, Y1   // 62f1ed395808
	VADDPD.BCST.Z (AX), Y2, K1, Y1 // 62f1edb95808
	VADDPD.BCST (AX), Z2, K1, Z1   // 62f1ed595808
	VADDPD.BCST.Z (AX), Z2, K1, Z1 // 62f1edd95808
	VMAXPD.BCST (AX), Z2, K1, Z1   // 62f1ed595f08
	VMAXPD.BCST.Z (AX), Z2, K1, Z1 // 62f1edd95f08
	// EVEX: suppress all exceptions (SAE).
	VMAXPD.SAE   Z3, Z2, K1, Z1   // 62f1ed595fcb or 62f1ed195fcb
	VMAXPD.SAE.Z Z3, Z2, K1, Z1   // 62f1edd95fcb or 62f1ed995fcb
	VMAXPD (AX), Z2, K1, Z1       // 62f1ed495f08
	VCMPSD.SAE $0, X0, X2, K0     // 62f1ef18c2c000
	VCMPSD.SAE $0, X0, X2, K1, K0 // 62f1ef19c2c000
	// EVEX: broadcast-affected compressed displacement (Disp8).
	VADDPD.BCST 1016(DX), X0, X29       // 6261fd18586a7f
	VADDPD.BCST 1016(DX), X29, X1       // 62f19510584a7f
	VADDPD.BCST 1016(DX), X28, X29      // 62619d10586a7f
	VADDPD.BCST 1016(DX)(AX*2), X0, X29 // 6261fd18586c427f
	VADDPD.BCST 1016(DX)(AX*2), X29, X1 // 62f19510584c427f
	VADDPD.BCST 1016(DX), Y0, Y29       // 6261fd38586a7f
	VADDPD.BCST 1016(DX), Y29, Y1       // 62f19530584a7f
	VADDPD.BCST 1016(DX), Y28, Y29      // 62619d30586a7f
	VADDPD.BCST 1016(DX)(AX*2), Y0, Y29 // 6261fd38586c427f
	VADDPD.BCST 1016(DX)(AX*2), Y29, Y1 // 62f19530584c427f
	VADDPD.BCST 1016(DX), Z0, Z29       // 6261fd58586a7f
	VADDPD.BCST 1016(DX), Z29, Z1       // 62f19550584a7f
	VADDPD.BCST 1016(DX), Z28, Z29      // 62619d50586a7f
	VADDPD.BCST 1016(DX)(AX*2), Z0, Z29 // 6261fd58586c427f
	VADDPD.BCST 1016(DX)(AX*2), Z29, Z1 // 62f19550584c427f
	VADDPS.BCST 508(DX), Z0, Z29        // 62617c58586a7f
	VADDPS.BCST 508(DX), Z1, Z29        // 62617458586a7f
	VADDPS.BCST 508(DX), Z28, Z29       // 62611c50586a7f
	VADDPS.BCST 508(DX)(AX*2), Z0, Z29  // 62617c58586c427f
	VADDPS.BCST 508(DX)(AX*2), Z1, Z29  // 62617458586c427f
	// EVEX: broadcast-affected compressed displacement that does not fit into 8bits.
	VADDPD.BCST 2032(DX), X0, X29        // 6261fd1858aaf0070000
	VADDPD.BCST 2032(DX), X29, X1        // 62f19510588af0070000
	VADDPD.BCST 2032(DX), X28, X29       // 62619d1058aaf0070000
	VADDPD.BCST 2032(DX)(AX*2), X0, X29  // 6261fd1858ac42f0070000
	VADDPD.BCST 2032(DX)(AX*2), X29, X1  // 62f19510588c42f0070000
	VADDPD.BCST 2032(DX), Y0, Y29        // 6261fd3858aaf0070000
	VADDPD.BCST 2032(DX), Y29, Y1        // 62f19530588af0070000
	VADDPD.BCST 2032(DX), Y28, Y29       // 62619d3058aaf0070000
	VADDPD.BCST 2032(DX)(AX*2), Y0, Y29  // 6261fd3858ac42f0070000
	VADDPD.BCST 2032(DX)(AX*2), Y29, Y1  // 62f19530588c42f0070000
	VADDPD.BCST 2032(DX), Z0, Z29        // 6261fd5858aaf0070000
	VADDPD.BCST 2032(DX), Z29, Z1        // 62f19550588af0070000
	VADDPD.BCST 2032(DX), Z28, Z29       // 62619d5058aaf0070000
	VADDPD.BCST 2032(DX)(AX*2), Z0, Z29  // 6261fd5858ac42f0070000
	VADDPD.BCST 2032(DX)(AX*2), Z29, Z1  // 62f19550588c42f0070000
	VADDPS.BCST 2032(DX), Z0, Z29        // 62617c5858aaf0070000
	VADDPS.BCST 2032(DX), Z1, Z29        // 6261745858aaf0070000
	VADDPS.BCST 2032(DX), Z28, Z29       // 62611c5058aaf0070000
	VADDPS.BCST 2032(DX)(AX*2), Z0, Z29  // 62617c5858ac42f0070000
	VADDPS.BCST 2032(DX)(AX*2), Z1, Z29  // 6261745858ac42f0070000
	// Forced EVEX encoding due to suffixes.
	VADDPD.BCST 2032(DX), X0, X0 // 62f1fd185882f0070000
	VADDPD.BCST 2032(DX), Y0, Y0 // 62f1fd385882f0070000
	// Test new Z-cases one-by-one.
	//
	// Zevex_i_r_k_rm.
	VCVTPS2PH $1, Z2, K5, Y21 // 62b37d4d1dd501
	VCVTPS2PH $2, Z21, K4, Y2 // 62e37d4c1dea02
	// Zevex_i_r_rm.
	VCVTPS2PH $1, Z2, Y21 // 62b37d481dd501
	VCVTPS2PH $2, Z21, Y2 // 62e37d481dea02
	// Zevex_i_rm_k_r.
	VFPCLASSPDX $1, X2, K5, K3   // 62f3fd0d66da01
	VFPCLASSPDX $2, X21, K4, K1  // 62b3fd0c66cd02
	VFPCLASSPDX $1, (AX), K5, K3 // 62f3fd0d661801
	VFPCLASSPDX $2, (CX), K4, K1 // 62f3fd0c660902
	// Zevex_i_rm_k_vo.
	VPROLD $1, X2, K5, X21   // 62f1550572ca01
	VPROLD $2, Y21, K5, Y2   // 62b16d2d72cd02
	VPROLD $1, (AX), K5, X21 // 62f15505720801
	VPROLD $2, (CX), K5, Y2  // 62f16d2d720902
	// Zevex_i_rm_r.
	VFPCLASSPDX $1, X2, K3   // 62f3fd0866da01
	VFPCLASSPDX $2, X21, K1  // 62b3fd0866cd02
	VFPCLASSPDX $1, (AX), K3 // 62f3fd08661801
	VFPCLASSPDX $2, (CX), K1 // 62f3fd08660902
	// Zevex_i_rm_v_k_r.
	VALIGND $1, X2, X9, K5, X21   // 62e3350d03ea01
	VALIGND $2, Y21, Y2, K5, Y9   // 62336d2d03cd02
	VALIGND $3, Z9, Z21, K5, Z2   // 62d3554503d103
	VALIGND $1, (AX), X9, K5, X21 // 62e3350d032801
	VALIGND $2, (CX), Y2, K5, Y9  // 62736d2d030902
	VALIGND $3, (AX), Z21, K5, Z2 // 62f35545031003
	// Zevex_i_rm_v_r.
	VALIGND $1, X2, X9, X21   // 62e3350803ea01
	VALIGND $2, Y21, Y2, Y9   // 62336d2803cd02
	VALIGND $3, Z9, Z21, Z2   // 62d3554003d103
	VALIGND $1, (AX), X9, X21 // 62e33508032801
	VALIGND $2, (CX), Y2, Y9  // 62736d28030902
	VALIGND $3, (AX), Z21, Z2 // 62f35540031003
	// Zevex_i_rm_vo.
	VPROLD $1, X2, X21   // 62f1550072ca01
	VPROLD $2, Y21, Y2   // 62b16d2872cd02
	VPROLD $1, (AX), X21 // 62f15500720801
	VPROLD $2, (CX), Y2  // 62f16d28720902
	// Zevex_k_rmo.
	VGATHERPF0DPD K5, (AX)(Y2*2)   // 62f2fd4dc60c50
	VGATHERPF0DPD K3, (CX)(Y21*2)  // 62f2fd43c60c69
	VSCATTERPF1DPD K5, (AX)(Y2*2)  // 62f2fd4dc63450
	VSCATTERPF1DPD K3, (CX)(Y21*2) // 62f2fd43c63469
	// Zevex_r_k_rm.
	VPSCATTERDD X2, K5, (AX)(X21*2) // 62f27d05a01468
	VPSCATTERDD X21, K5, (AX)(X2*2) // 62e27d0da02c50
	VPSCATTERDD Y2, K5, (AX)(Y21*2) // 62f27d25a01468
	VPSCATTERDD Y21, K5, (AX)(Y2*2) // 62e27d2da02c50
	VPSCATTERDD Z2, K5, (AX)(Z21*2) // 62f27d45a01468
	VPSCATTERDD Z21, K5, (AX)(Z2*2) // 62e27d4da02c50
	// Zevex_r_v_k_rm.
	VMOVSD X2, X9, K5, X21 // 62b1b70d11d5 or 62e1b70d10ea
	VMOVSD X21, X2, K5, X9 // 62c1ef0d11e9 or 6231ef0d10cd
	VMOVSD X9, X21, K5, X2 // 6271d70511ca or 62d1d70510d1
	// Zevex_r_v_rm.
	VMOVSD X2, X9, X21 // 62b1b70811d5 or 62e1b70810ea
	VMOVSD X21, X2, X9 // 62c1ef0811e9 or 6231ef0810cd
	VMOVSD X9, X21, X2 // 6271d70011ca or 62d1d70010d1
	VPMOVDB X2, X21    // 62b27e0831d5
	VPMOVDB X21, X2    // 62e27e0831ea
	VPMOVDB X2, (AX)   // 62f27e083110
	VPMOVDB X21, (AX)  // 62e27e083128
	// Zevex_rm_k_r.
	VMOVDDUP X2, K5, X21   // 62e1ff0d12ea
	VMOVDDUP X21, K5, X2   // 62b1ff0d12d5
	VMOVDDUP (AX), K5, X21 // 62e1ff0d1228
	VMOVDDUP (CX), K5, X2  // 62f1ff0d1211
	VMOVDDUP Y2, K5, Y21   // 62e1ff2d12ea
	VMOVDDUP Y21, K5, Y2   // 62b1ff2d12d5
	VMOVDDUP (AX), K5, Y21 // 62e1ff2d1228
	VMOVDDUP (CX), K5, Y2  // 62f1ff2d1211
	VMOVDDUP Z2, K5, Z21   // 62e1ff4d12ea
	VMOVDDUP Z21, K5, Z2   // 62b1ff4d12d5
	VMOVDDUP (AX), K5, Z21 // 62e1ff4d1228
	VMOVDDUP (CX), K5, Z2  // 62f1ff4d1211
	// Zevex_rm_v_k_r.
	VADDPD Z2, Z9, K5, Z21 // 62e1b54d58ea
	VADDPD Z21, Z2, K5, Z9 // 6231ed4d58cd
	VADDPD Z9, Z21, K5, Z2 // 62d1d54558d1
	// Zevex_rm_v_r.
	VADDPD Z2, Z9, Z21 // 62e1b54858ea
	VADDPD Z21, Z2, Z9 // 6231ed4858cd
	VADDPD Z9, Z21, Z2 // 62d1d54058d1

	CLWB (BX) // 660fae33
	CLDEMOTE (BX) // 0f1c03
	TPAUSE BX // 660faef3
	UMONITOR BX // f30faef3
	UMWAIT BX // f20faef3

	RDPID DX                                // f30fc7fa
	RDPID R11                               // f3410fc7fb

	// End of tests.
	RET
