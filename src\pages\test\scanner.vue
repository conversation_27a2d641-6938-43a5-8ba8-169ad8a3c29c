<script lang="ts" setup>
import { ref, onMounted, onUnmounted } from 'vue'
import scannerUtils from '@/utils/scannerUtils.js'

const scanResult = ref('')
const scanStatus = ref('未初始化')
const isScanning = ref(false)

// 扫码回调函数
const handleScanResult = (result: any) => {
  isScanning.value = false
  if (result.success) {
    scanResult.value = result.result
    scanStatus.value = '扫码成功'
    uni.showToast({
      title: '扫码成功',
      icon: 'success'
    })
  } else {
    scanStatus.value = `扫码失败: ${result.error}`
    uni.showToast({
      title: result.error || '扫码失败',
      icon: 'none'
    })
  }
}

// 开始扫码
const startScan = async () => {
  isScanning.value = true
  scanStatus.value = '正在初始化扫码...'
  
  try {
    const initResult = await scannerUtils.initScanner(handleScanResult, true)
    scanStatus.value = initResult.message
    
    if (initResult.success) {
      // 如果是新大陆扫码头，显示提示
      if (initResult.message.includes('扫码枪')) {
        uni.showToast({
          title: '请使用扫码枪扫描',
          icon: 'none',
          duration: 3000
        })
      }
    }
  } catch (error) {
    isScanning.value = false
    scanStatus.value = `初始化失败: ${error.message}`
  }
}

// 手动触发扫码（用于测试降级功能）
const manualScan = () => {
  scannerUtils.startScan()
}

// 清除结果
const clearResult = () => {
  scanResult.value = ''
  scanStatus.value = '已清除'
}

onMounted(() => {
  scannerUtils.setPageActive(true)
})

onUnmounted(() => {
  scannerUtils.destroy()
})
</script>

<script>
export default {
  onShow() {
    console.log('扫码测试页面显示')
    if (typeof scannerUtils !== 'undefined') {
      scannerUtils.setPageActive(true)
    }
  },
  onHide() {
    console.log('扫码测试页面隐藏')
    if (typeof scannerUtils !== 'undefined') {
      scannerUtils.setPageActive(false)
    }
  }
}
</script>

<template>
  <view class="scanner-test">
    <view class="header">
      <text class="title">扫码功能测试</text>
    </view>
    
    <view class="status-card">
      <text class="status-label">状态：</text>
      <text class="status-text" :class="{ 'success': scanStatus.includes('成功'), 'error': scanStatus.includes('失败') }">
        {{ scanStatus }}
      </text>
    </view>
    
    <view class="result-card" v-if="scanResult">
      <text class="result-label">扫码结果：</text>
      <text class="result-text">{{ scanResult }}</text>
    </view>
    
    <view class="button-group">
      <button 
        class="scan-btn primary" 
        @click="startScan" 
        :disabled="isScanning"
      >
        {{ isScanning ? '扫码中...' : '开始扫码' }}
      </button>
      
      <button 
        class="scan-btn secondary" 
        @click="manualScan"
      >
        手动扫码(降级测试)
      </button>
      
      <button 
        class="scan-btn danger" 
        @click="clearResult"
        v-if="scanResult"
      >
        清除结果
      </button>
    </view>
    
    <view class="info-card">
      <text class="info-title">使用说明：</text>
      <text class="info-text">1. 点击"开始扫码"初始化扫码功能</text>
      <text class="info-text">2. 在PDA设备上使用扫码枪扫描</text>
      <text class="info-text">3. 在其他设备上会自动降级到系统扫码</text>
      <text class="info-text">4. "手动扫码"按钮用于测试降级功能</text>
    </view>
  </view>
</template>

<style lang="scss">
.scanner-test {
  padding: 20rpx;
  background-color: #f5f5f5;
  min-height: 100vh;
}

.header {
  text-align: center;
  margin-bottom: 40rpx;
  
  .title {
    font-size: 36rpx;
    font-weight: bold;
    color: #2196F3;
  }
}

.status-card, .result-card, .info-card {
  background: white;
  border-radius: 16rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 4rpx 12rpx rgba(0,0,0,0.1);
}

.status-label, .result-label {
  font-size: 28rpx;
  color: #666;
  margin-bottom: 10rpx;
}

.status-text {
  font-size: 32rpx;
  font-weight: bold;
  
  &.success {
    color: #4CAF50;
  }
  
  &.error {
    color: #F44336;
  }
}

.result-text {
  font-size: 30rpx;
  color: #333;
  word-break: break-all;
  background: #f8f8f8;
  padding: 20rpx;
  border-radius: 8rpx;
}

.button-group {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
  margin-bottom: 40rpx;
}

.scan-btn {
  height: 88rpx;
  border-radius: 44rpx;
  font-size: 32rpx;
  font-weight: bold;
  border: none;
  
  &.primary {
    background: #2196F3;
    color: white;
    
    &:disabled {
      background: #ccc;
    }
  }
  
  &.secondary {
    background: #FF9800;
    color: white;
  }
  
  &.danger {
    background: #F44336;
    color: white;
  }
}

.info-card {
  .info-title {
    font-size: 30rpx;
    font-weight: bold;
    color: #333;
    margin-bottom: 20rpx;
  }
  
  .info-text {
    display: block;
    font-size: 26rpx;
    color: #666;
    line-height: 1.6;
    margin-bottom: 10rpx;
  }
}
</style>
