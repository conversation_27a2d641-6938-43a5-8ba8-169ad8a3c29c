<script lang="ts" setup>
import { ref, onMounted, onUnmounted } from 'vue'
import scannerUtils from '@/utils/scannerUtils.js'
import SunmiConfig from '@/utils/sunmiConfig.js'

const scanResult = ref('')
const scanStatus = ref('未初始化')
const isScanning = ref(false)
const deviceInfo = ref<any>({})
const scannerType = ref('未知')

// 扫码回调函数
const handleScanResult = (result: any) => {
  isScanning.value = false
  if (result.success) {
    scanResult.value = result.result
    scanStatus.value = '扫码成功'
    
    // 显示详细信息
    let message = `扫码结果: ${result.result}`
    if (result.mode) {
      message += `\n模式: ${result.mode}`
    }
    if (result.data) {
      message += `\nAPI数据: ${JSON.stringify(result.data, null, 2)}`
    }
    if (result.apiError) {
      message += `\nAPI错误: ${result.apiError}`
    }
    
    uni.showModal({
      title: '扫码成功',
      content: message,
      showCancel: false
    })
  } else {
    scanStatus.value = `扫码失败: ${result.error}`
    uni.showToast({
      title: result.error || '扫码失败',
      icon: 'none'
    })
  }
}

// 检查设备信息
const checkDevice = async () => {
  try {
    const configResult = await SunmiConfig.checkScannerConfig()
    deviceInfo.value = configResult.deviceInfo || {}
    
    if (configResult.success) {
      scannerType.value = SunmiConfig.isSunmiDevice() ? '商米设备' : '其他设备'
      scanStatus.value = '设备检查通过'
    } else {
      scannerType.value = '不支持的设备'
      scanStatus.value = configResult.error || '设备检查失败'
    }
  } catch (error) {
    console.error('检查设备失败:', error)
    scanStatus.value = '设备检查异常'
  }
}

// 开始扫码
const startScan = async () => {
  isScanning.value = true
  scanStatus.value = '正在初始化扫码...'
  
  try {
    const initResult = await scannerUtils.initScanner(handleScanResult, true, 'general')
    scanStatus.value = initResult.message
    
    if (initResult.success) {
      // 显示扫码提示
      uni.showToast({
        title: '请使用扫码枪扫描',
        icon: 'none',
        duration: 3000
      })
    }
  } catch (error) {
    isScanning.value = false
    scanStatus.value = `初始化失败: ${error.message}`
  }
}

// 测试上报模式扫码
const startReportScan = async () => {
  isScanning.value = true
  scanStatus.value = '正在初始化上报扫码...'
  
  try {
    const initResult = await scannerUtils.initScanner(handleScanResult, true, 'report')
    scanStatus.value = initResult.message
    
    if (initResult.success) {
      uni.showToast({
        title: '上报模式已启用，请扫描机器码',
        icon: 'none',
        duration: 3000
      })
    }
  } catch (error) {
    isScanning.value = false
    scanStatus.value = `上报模式初始化失败: ${error.message}`
  }
}

// 测试维修模式扫码
const startRepairScan = async () => {
  isScanning.value = true
  scanStatus.value = '正在初始化维修扫码...'
  
  try {
    const initResult = await scannerUtils.initScanner(handleScanResult, true, 'repair')
    scanStatus.value = initResult.message
    
    if (initResult.success) {
      uni.showToast({
        title: '维修模式已启用，请扫描机器码',
        icon: 'none',
        duration: 3000
      })
    }
  } catch (error) {
    isScanning.value = false
    scanStatus.value = `维修模式初始化失败: ${error.message}`
  }
}

// 手动触发扫码（用于测试降级功能）
const manualScan = () => {
  scannerUtils.startScan()
}

// 清除结果
const clearResult = () => {
  scanResult.value = ''
  scanStatus.value = '已清除'
  isScanning.value = false
}

// 配置商米扫码头
const configureSunmi = async () => {
  try {
    const result = await SunmiConfig.configureOutputMode()
    uni.showModal({
      title: '配置提示',
      content: result.message,
      showCancel: false
    })
  } catch (error) {
    uni.showToast({
      title: '配置失败',
      icon: 'none'
    })
  }
}

// 页面生命周期
onMounted(async () => {
  scannerUtils.setPageActive(true)
  await checkDevice()
  startScan()
})

onUnmounted(() => {
  scannerUtils.destroy()
})
</script>

<script lang="ts">
export default {
  onShow() {
    console.log('扫码测试页面显示')
    if (typeof scannerUtils !== 'undefined') {
      scannerUtils.setPageActive(true)
    }
  },
  onHide() {
    console.log('扫码测试页面隐藏')
    if (typeof scannerUtils !== 'undefined') {
      scannerUtils.setPageActive(false)
    }
  }
}
</script>

<template>
  <view class="scanner-test">
    <view class="header">
      <text class="title">商米扫码功能测试</text>
    </view>
    
    <!-- 设备信息卡片 -->
    <view class="device-card">
      <text class="card-title">设备信息</text>
      <view class="device-info">
        <text class="info-item">设备类型：{{ scannerType }}</text>
        <text class="info-item">品牌：{{ deviceInfo.brand || '未知' }}</text>
        <text class="info-item">型号：{{ deviceInfo.model || '未知' }}</text>
        <text class="info-item">系统：{{ deviceInfo.system || '未知' }}</text>
      </view>
      <button class="config-btn" @click="configureSunmi">扫码配置说明</button>
    </view>
    
    <!-- 状态卡片 -->
    <view class="status-card">
      <text class="status-label">状态：</text>
      <text class="status-text" :class="{ 
        'success': scanStatus.includes('成功'), 
        'error': scanStatus.includes('失败') || scanStatus.includes('异常')
      }">
        {{ scanStatus }}
      </text>
    </view>
    
    <!-- 结果卡片 -->
    <view class="result-card" v-if="scanResult">
      <text class="result-label">扫码结果：</text>
      <text class="result-text">{{ scanResult }}</text>
    </view>
    
    <!-- 按钮组 -->
    <view class="button-group">
      <button 
        class="scan-btn primary" 
        @click="startScan" 
        :disabled="isScanning"
      >
        {{ isScanning ? '扫码中...' : '通用扫码测试' }}
      </button>
      
      <button 
        class="scan-btn report" 
        @click="startReportScan"
        :disabled="isScanning"
      >
        上报模式测试
      </button>
      
      <button 
        class="scan-btn repair" 
        @click="startRepairScan"
        :disabled="isScanning"
      >
        维修模式测试
      </button>
      
      <button 
        class="scan-btn secondary" 
        @click="manualScan"
      >
        手动扫码(降级测试)
      </button>
      
      <button 
        class="scan-btn danger" 
        @click="clearResult"
        v-if="scanResult"
      >
        清除结果
      </button>
    </view>
    
    <!-- 使用说明 -->
    <view class="info-card">
      <text class="info-title">使用说明：</text>
      <text class="info-text">1. 商米PDA设备会优先使用商米扫码头</text>
      <text class="info-text">2. 如果商米扫码头不可用，会降级到新大陆扫码头</text>
      <text class="info-text">3. 最终降级方案是系统uni.scanCode</text>
      <text class="info-text">4. 上报/维修模式会调用后端API验证</text>
      <text class="info-text">5. 确保设备扫码配置中已开启广播输出</text>
    </view>
  </view>
</template>

<style lang="scss">
.scanner-test {
  padding: 20rpx;
  background-color: #f5f5f5;
  min-height: 100vh;
}

.header {
  text-align: center;
  margin-bottom: 30rpx;
  
  .title {
    font-size: 36rpx;
    font-weight: bold;
    color: #2196F3;
  }
}

.device-card, .status-card, .result-card, .info-card {
  background: white;
  border-radius: 16rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 4rpx 12rpx rgba(0,0,0,0.1);
}

.card-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
}

.device-info {
  display: flex;
  flex-direction: column;
  gap: 10rpx;
  margin-bottom: 20rpx;
}

.info-item {
  font-size: 28rpx;
  color: #666;
}

.config-btn {
  background: #FF9800;
  color: white;
  border: none;
  border-radius: 8rpx;
  padding: 16rpx 24rpx;
  font-size: 24rpx;
}

.status-label, .result-label {
  font-size: 28rpx;
  color: #666;
  margin-bottom: 10rpx;
}

.status-text {
  font-size: 32rpx;
  font-weight: bold;
  
  &.success {
    color: #4CAF50;
  }
  
  &.error {
    color: #F44336;
  }
}

.result-text {
  font-size: 30rpx;
  color: #333;
  word-break: break-all;
  background: #f8f8f8;
  padding: 20rpx;
  border-radius: 8rpx;
}

.button-group {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
  margin-bottom: 30rpx;
}

.scan-btn {
  height: 88rpx;
  border-radius: 44rpx;
  font-size: 32rpx;
  font-weight: bold;
  border: none;
  
  &.primary {
    background: #2196F3;
    color: white;
    
    &:disabled {
      background: #ccc;
    }
  }
  
  &.report {
    background: #4CAF50;
    color: white;
    
    &:disabled {
      background: #ccc;
    }
  }
  
  &.repair {
    background: #9C27B0;
    color: white;
    
    &:disabled {
      background: #ccc;
    }
  }
  
  &.secondary {
    background: #FF9800;
    color: white;
  }
  
  &.danger {
    background: #F44336;
    color: white;
  }
}

.info-card {
  .info-title {
    font-size: 30rpx;
    font-weight: bold;
    color: #333;
    margin-bottom: 20rpx;
  }
  
  .info-text {
    display: block;
    font-size: 26rpx;
    color: #666;
    line-height: 1.6;
    margin-bottom: 10rpx;
  }
}
</style>
