[compiler:gccgo] skip 'gccgo has no cover tool'
[short] skip

go test -short -coverpkg=strings strings regexp
! stdout '[^0-9]0\.0%'
stdout  'strings.*coverage:.*[1-9][0-9.]+%'
stdout  'regexp.*coverage:.*[1-9][0-9.]+%'

go test -short -cover strings math regexp
! stdout '[^0-9]0\.0%'
stdout  'strings.*coverage:.*[1-9][0-9.]+%'
stdout  'math.*coverage:.*[1-9][0-9.]+%'
stdout  'regexp.*coverage:.*[1-9][0-9.]+%'