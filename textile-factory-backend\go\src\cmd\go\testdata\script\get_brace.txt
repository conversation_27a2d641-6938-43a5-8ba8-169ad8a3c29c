env GO111MODULE=off

[!git] skip

# Set up some empty repositories.
cd $WORK/_origin/foo
exec git init
exec git config user.name 'Nameless Gopher'
exec git config user.email '<EMAIL>'
exec git commit --allow-empty -m 'create master branch'

cd $WORK
cd '_origin/{confusing}'
exec git init
exec git config user.name 'Nameless Gopher'
exec git config user.email '<EMAIL>'
exec git commit --allow-empty -m 'create master branch'

# Clone the empty repositories into GOPATH.
# This tells the Go command where to find them: it takes the place of a user's meta-tag redirector.
mkdir $GOPATH/src/example.com
cd $GOPATH/src/example.com
exec git clone $WORK/_origin/foo
exec git clone $WORK/_origin/{confusing}

# Commit contents to the repositories.
cd $WORK/_origin/foo
exec git add main.go
exec git commit -m 'add main'

cd $WORK
cd '_origin/{confusing}'
exec git add confusing.go
exec git commit -m 'just try to delete this!'

# 'go get' should refuse to download or update the confusingly-named repo.
cd $GOPATH/src/example.com/foo
! go get -u 'example.com/{confusing}'
stderr 'invalid char'
! go get -u example.com/foo
stderr 'invalid import path'
! exists example.com/{confusing}

-- $WORK/_origin/foo/main.go --
package main
import _ "example.com/{confusing}"

func main() {}

-- $WORK/_origin/{confusing}/confusing.go --
package confusing
