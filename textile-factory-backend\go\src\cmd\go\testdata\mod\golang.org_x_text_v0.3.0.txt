written by hand - just enough to compile rsc.io/sampler, rsc.io/quote

-- .mod --
module golang.org/x/text
-- .info --
{"Version":"v0.3.0","Name":"","Short":"","Time":"2017-09-16T03:28:32Z"}
-- go.mod --
module golang.org/x/text
-- unused/unused.go --
package unused
-- language/lang.go --
// Copyright 2018 The Go Authors. All rights reserved.
// Use of this source code is governed by a BSD-style
// license that can be found in the LICENSE file.

// This is a tiny version of golang.org/x/text.

package language

import "strings"

type Tag string

func Make(s string) Tag { return Tag(s) }

func (t Tag) String() string { return string(t) }

func NewMatcher(tags []Tag) Matcher { return &matcher{tags} }

type Matcher interface {
	Match(...Tag) (Tag, int, int)
}

type matcher struct {
	tags []Tag
}

func (m *matcher) Match(prefs ...Tag) (Tag, int, int) {
	for _, pref := range prefs {
		for _, tag := range m.tags {
			if tag == pref || strings.HasPrefix(string(pref), string(tag+"-")) || strings.HasPrefix(string(tag), string(pref+"-")) {
				return tag, 0, 0
			}
		}
	}
	return m.tags[0], 0, 0
}
