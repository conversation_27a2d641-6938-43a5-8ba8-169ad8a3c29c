# 织厂机器异常管理系统产品需求文档

## 1. Product Overview
织厂机器异常管理系统是一个专为纺织工厂设计的移动端应用，用于管理和跟踪机器异常情况。
- 解决织厂机器故障上报和维修流程不规范的问题，提高维修效率和机器运行状态的可视化管理。
- 主要用户为织工和机修工，通过扫码技术实现快速故障上报和维修流程管理。
- 目标是提升织厂设备管理效率，减少机器停机时间，优化维修资源配置。

## 2. Core Features

### 2.1 User Roles
| Role | Registration Method | Core Permissions |
|------|---------------------|------------------|
| 织工 | 管理员分配账号 | 扫码上报机器异常，查看上报历史 |
| 机修工 | 管理员分配账号 | 查看机器列表和状态，扫码开始维修，完成维修操作 |

### 2.2 Feature Module
我们的织厂机器异常管理系统包含以下主要页面：
1. **登录页面**：用户身份验证，角色识别
2. **织工首页**：扫码上报功能，历史记录查看
3. **机修工首页**：机器列表展示，状态监控，扫码维修入口
4. **异常上报页面**：填写异常详情，提交维修申请
5. **维修操作页面**：维修流程管理，维修完成确认

### 2.3 Page Details
| Page Name | Module Name | Feature description |
|-----------|-------------|---------------------|
| 登录页面 | 用户认证 | 输入用户名密码登录，验证用户身份和角色权限 |
| 织工首页 | 扫码上报 | 扫描机器二维码，快速进入异常上报流程 |
| 织工首页 | 历史记录 | 查看个人上报的异常记录和处理状态 |
| 机修工首页 | 机器列表 | 显示所有机器的运行状态、基本信息、异常数量 |
| 机修工首页 | 扫码维修 | 顶部扫码按钮，扫描机器码直接进入维修页面 |
| 异常上报页面 | 机器信息 | 显示扫码获取的机器编号、位置等基本信息 |
| 异常上报页面 | 异常描述 | 填写异常现象、严重程度、备注信息 |
| 异常上报页面 | 提交上报 | 调用后端接口提交异常报告，发送维修通知 |
| 维修操作页面 | 维修信息 | 显示异常详情、机器信息、上报时间等 |
| 维修操作页面 | 维修记录 | 填写维修过程、更换部件、维修时长等信息 |
| 维修操作页面 | 完成维修 | 确认维修完成，更新机器状态，关闭工单 |

## 3. Core Process

**织工操作流程：**
织工发现机器异常 → 打开App扫描机器二维码 → 系统识别机器信息 → 填写异常描述和备注 → 点击上报维修按钮 → 系统调用后端接口提交 → 发送通知给机修工

**机修工操作流程：**
机修工收到异常通知 → 打开App查看机器列表 → 找到异常机器或直接扫码 → 进入维修页面查看异常详情 → 开始维修操作 → 填写维修记录 → 点击维修结束按钮 → 更新机器状态

```mermaid
graph TD
    A[登录页面] --> B{角色判断}
    B -->|织工| C[织工首页]
    B -->|机修工| D[机修工首页]
    C --> E[扫码上报]
    E --> F[异常上报页面]
    F --> G[提交完成]
    D --> H[机器列表]
    D --> I[扫码维修]
    H --> J[维修操作页面]
    I --> J
    J --> K[维修完成]
```

## 4. User Interface Design

### 4.1 Design Style
- 主色调：工业蓝 (#2196F3)，辅助色：橙色 (#FF9800) 用于警告状态
- 按钮样式：圆角矩形，3D阴影效果，突出操作性
- 字体：系统默认字体，标题16px，正文14px，备注12px
- 布局风格：卡片式设计，顶部导航栏，底部操作按钮
- 图标风格：线性图标配合实心图标，扫码、维修、机器状态等功能图标

### 4.2 Page Design Overview
| Page Name | Module Name | UI Elements |
|-----------|-------------|-------------|
| 登录页面 | 用户认证 | 简洁白色背景，居中登录表单，蓝色登录按钮，工厂logo |
| 织工首页 | 扫码上报 | 大号扫码按钮居中，下方显示最近上报记录卡片列表 |
| 机修工首页 | 机器列表 | 顶部扫码提示条，机器状态卡片网格布局，绿色/红色状态指示 |
| 异常上报页面 | 表单填写 | 机器信息展示区，异常描述输入框，严重程度选择器，提交按钮 |
| 维修操作页面 | 维修管理 | 异常信息卡片，维修记录表单，开始/结束维修状态按钮 |

### 4.3 Responsiveness
移动端优先设计，适配iOS和Android平台，支持触摸操作优化，扫码功能需要相机权限。