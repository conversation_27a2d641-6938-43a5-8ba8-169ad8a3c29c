// Copyright 2017 The Go Authors. All rights reserved.
// Use of this source code is governed by a BSD-style
// license that can be found in the LICENSE file.

// The cases are auto-generated by disassembler.
// The uncommented cases means they can be handled by assembler
// and they are consistent with disassembler decoding.
// TODO means they cannot be handled by current assembler.

#include "../../../../../runtime/textflag.h"

TEXT asmtest(SB),DUPOK|NOSPLIT,$-8

	AND $(1<<63), R1                           // AND $-9223372036854775808, R1     // 21004192
	ADCW ZR, R8, R10                           // 0a011f1a
	ADC R0, R2, R12                            // 4c00009a
	ADCSW R9, R21, R6                          // a602093a
	ADCS R23, R22, R22                         // d60217ba
	ADDW R5.UXTH, R8, R9                       // 0921250b
	ADD R8.SXTB<<3, R23, R14                   // ee8e288b
	ADDW $3076, R17, R3                        // 23123011
	ADDW $(3076<<12), R17, R3                  // ADDW $12599296, R17, R3           // 23127011
	ADD $2280, R25, R11                        // 2ba32391
	ADD $(2280<<12), R25, R11                  // ADD $9338880, R25, R11            // 2ba36391
	ADDW R13->5, R11, R7                       // 67158d0b
	ADD R25<<54, R17, R16                      // 30da198b
	ADDSW R12.SXTX<<1, R29, R7                 // a7e72c2b
	ADDS R24.UXTX<<4, R25, R21                 // 357338ab
	ADDSW $(3525<<12), R3, R11                 // ADDSW $14438400, R3, R11          // 6b147731
	ADDS $(3525<<12), R3, R11                  // ADDS $14438400, R3, R11           // 6b1477b1
	ADDSW R7->22, R14, R13                     // cd59872b
	ADDS R14>>7, ZR, R4                        // e41f4eab
	AND $-9223372036854775808, R1, R1          // 21004192
	ANDW $4026540031, R29, R2                  // a2430412
	AND $34903429696192636, R12, R19           // 93910e92
	ANDW R9@>7, R19, R26                       // 7a1ec90a
	AND R9@>7, R19, R26                        // 7a1ec98a
	TSTW $2863311530, R24                      // 1ff30172
	TST R2, R0                                 // 1f0002ea
	TST $7, R2                                 // 5f0840f2
	ANDS R2, R0, ZR                            // 1f0002ea
	ANDS $7, R2, ZR                            // 5f0840f2
	ANDSW $2863311530, R24, ZR                 // 1ff30172
	ANDSW $2863311530, R24, R23                // 17f30172
	ANDS $-140737488289793, R2, R5             // 458051f2
	ANDSW R26->24, R21, R15                    // af629a6a
	ANDS R30@>44, R3, R26                      // 7ab0deea
	ASRW R12, R27, R25                         // 792bcc1a
	ASR R14, R27, R7                           // 672bce9a
	ASR $11, R27, R25                          // 79ff4b93
	ASRW $11, R27, R25                         // 797f0b13
	BLT -1(PC)                                 // ebffff54
	JMP -1(PC)                                 // ffffff17
	BFIW $16, R20, $6, R0                      // 80161033
	BFI $27, R21, $21, R25                     // b95265b3
	BFXILW $3, R27, $23, R14                   // 6e670333
	BFXIL $26, R8, $16, R20                    // 14a55ab3
	BICW R7@>15, R5, R16                       // b03ce70a
	BIC R12@>13, R12, R19                      // 9335ec8a
	BICSW R25->20, R3, R20                     // 7450b96a
	BICS R19->12, R1, R23                      // 3730b3ea
	BICS R19, R1, R23                          // 370033ea
	BICS R19>>0, R1, R23                       // 370073ea
	CALL -1(PC)                                // ffffff97
	CALL (R15)                                 // e0013fd6
	JMP  (R29)                                 // a0031fd6
	BRK $35943                                 // e08c31d4
	CBNZW R2, -1(PC)                           // e2ffff35
	CBNZ R7, -1(PC)                            // e7ffffb5
	CBZW R15, -1(PC)                           // efffff34
	CBZ R1, -1(PC)                             // e1ffffb4
	CCMN MI, ZR, R1, $4                        // e44341ba
	CCMNW AL, R26, $20, $11                    // 4beb543a
	CCMN PL, R24, $6, $1                       // 015b46ba
	CCMNW EQ, R20, R6, $6                      // 8602463a
	CCMN LE, R30, R12, $6                      // c6d34cba
	CCMPW VS, R29, $15, $7                     // a76b4f7a
	CCMP LE, R7, $19, $3                       // e3d853fa
	CCMPW HS, R19, R6, $0                      // 6022467a
	CCMP LT, R30, R6, $7                       // c7b346fa
	CCMN  MI, ZR, R1, $4                       // e44341ba
	CSINCW HS, ZR, R27, R14                    // ee279b1a
	CSINC VC, R2, R1, R1                       // 4174819a
	CSINVW EQ, R2, R21, R17                    // 5100955a
	CSINV LO, R2, R19, R23                     // 573093da
	CINCW LO, R27, R14                         // 6e279b1a
	CINCW HS, R27, ZR                          // 7f379b1a
	CINVW EQ, R2, R17                          // 5110825a
	CINV VS, R12, R7                           // 87718cda
	CINV VS, R30, R30                          // de739eda
	CLREX $4                                   // 5f3403d5
	CLREX $0                                   // 5f3003d5
	CLSW R15, R6                               // e615c05a
	CLS R15, ZR                                // ff15c0da
	CLZW R1, R14                               // 2e10c05a
	CLZ R21, R9                                // a912c0da
	CMNW R21.UXTB<<4, R15                      // ff11352b
	CMN R0.UXTW<<4, R16                        // 1f5220ab
	CMNW R13>>8, R9                            // 3f214d2b
	CMN R6->17, R3                             // 7f4486ab
	CMNW $(2<<12), R5                          // CMNW $8192, R5                // bf084031
	CMN $(8<<12), R12                          // CMN $32768, R12               // 9f2140b1
	CMN R6->0, R3                              // 7f0086ab
	CMN R6, R3                                 // 7f0006ab
	CMNW R30, R5                               // bf001e2b
	CMNW $2, R5                                // bf080031
	CMN ZR, R3                                 // 7f001fab
	CMN R0, R3                                 // 7f0000ab
	CMPW R6.UXTB, R23                          // ff02266b
	CMP R25.SXTH<<2, R26                       // 5fab39eb
	CMP $3817, R29                             // bfa73bf1
	CMP R7>>23, R3                             // 7f5c47eb
	CNEGW PL, R9, R14                          // 2e45895a
	CSNEGW HS, R5, R9, R14                     // ae24895a
	CSNEG PL, R14, R21, R3                     // c35595da
	CNEG  LO, R7, R15                          // ef2487da
	CRC32B R17, R8, R16                        // 1041d11a
	CRC32H R3, R21, R27                        // bb46c31a
	CRC32W R22, R30, R9                        // c94bd61a
	CRC32X R20, R4, R15                        // 8f4cd49a
	CRC32CB R19, R27, R22                      // 7653d31a
	CRC32CH R21, R0, R20                       // 1454d51a
	CRC32CW R9, R3, R21                        // 7558c91a
	CRC32CX R11, R0, R24                       // 185ccb9a
	CSELW LO, R4, R20, R12                     // 8c30941a
	CSEL GE, R0, R12, R14                      // 0ea08c9a
	CSETW GE, R3                               // e3b79f1a
	CSET LT, R30                               // fea79f9a
	CSETMW VC, R5                              // e5639f5a
	CSETM VS, R4                               // e4739fda
	CSINCW LE, R5, R24, R26                    // bad4981a
	CSINC VS, R26, R16, R17                    // 5167909a
	CSINVW AL, R23, R21, R5                    // e5e2955a
	CSINV LO, R2, R11, R14                     // 4e308bda
	CSNEGW HS, R16, R29, R10                   // 0a269d5a
	CSNEG NE, R21, R19, R11                    // ab1693da
	DC IVAC, R1                                // 217608d5
	DCPS1 $11378                               // 418ea5d4
	DCPS2 $10699                               // 6239a5d4
	DCPS3 $24415                               // e3ebabd4
	DMB $1                                     // bf3103d5
	DMB $0                                     // bf3003d5
	DRPS                                       // e003bfd6
	DSB  $1                                    // 9f3103d5
	EONW R21<<29, R6, R9                       // c974354a
	EON R14>>46, R4, R9                        // 89b86eca
	EOR $-2287828610704211969, R27, R22        // 76e343d2
	EORW R12->27, R10, R19                     // 536d8c4a
	EOR R2<<59, R30, R17                       // d1ef02ca
	ERET                                       // e0039fd6
	EXTRW $7, R8, R10, R25                     // 591d8813
	EXTR $35, R22, R12, R8                     // 888dd693
	SEVL                                       // bf2003d5
	HINT $6                                    // df2003d5
	HINT $0                                    // 1f2003d5
	HLT $65509                                 // a0fc5fd4
	HVC $61428                                 // 82fe1dd4
	ISB $1                                     // df3103d5
	ISB $15                                    // df3f03d5
	LDARW (R12), R29                           // 9dfddf88
	LDARW (R30), R22                           // d6ffdf88
	LDARW (RSP), R22                           // f6ffdf88
	LDAR (R27), R22                            // 76ffdfc8
	LDARB (R25), R2                            // 22ffdf08
	LDARH (R5), R7                             // a7fcdf48
	LDAXPW (R10), (R20, R16)                   // 54c17f88
	LDAXP (R25), (R30, R11)                    // 3eaf7fc8
	LDAXRW (R15), R2                           // e2fd5f88
	LDAXR (R15), R21                           // f5fd5fc8
	LDAXRB (R19), R16                          // 70fe5f08
	LDAXRH (R5), R8                            // a8fc5f48
	//TODO LDNP 0xcc(RSP), ZR, R12             // ecff5928
	//TODO LDNP 0x40(R28), R9, R5              // 852744a8
	//TODO LDPSW -0xd0(R2), R0, R12            // 4c00e668
	//TODO LDPSW 0x5c(R4), R8, R5              // 85a0cb69
	//TODO LDPSW 0x6c(R12), R2, R27            // 9b894d69
	MOVWU.P -84(R15), R9                       // e9c55ab8
	MOVD.P -46(R10), R8                        // 48255df8
	MOVD.P (R10), R8                           // 480540f8
	MOVWU.W -141(R3), R16                      // 703c57b8
	MOVD.W -134(R0), R29                       // 1dac57f8
	MOVWU 4156(R1), R25                        // 393c50b9
	MOVD 14616(R10), R9                        // 498d5cf9
	MOVWU (R4)(R12.SXTW<<2), R7                // 87d86cb8
	MOVD (R7)(R11.UXTW<<3), R25                // f9586bf8
	MOVBU.P 42(R2), R12                        // 4ca44238
	MOVBU.W -27(R2), R14                       // 4e5c5e38
	MOVBU 2916(R24), R3                        // 03936d39
	MOVBU (R19)(R14<<0), R23                   // 777a6e38
	MOVBU (R2)(R8.SXTX), R19                   // 53e86838
	MOVBU (R27)(R23), R14                      // 6e6b7738
	MOVHU.P 107(R14), R13                      // cdb54678
	MOVHU.W 192(R3), R2                        // 620c4c78
	MOVHU 6844(R4), R19                        // 93787579
	MOVHU (R5)(R25.SXTW), R15                  // afc87978
	//TODO MOVBW.P 77(R19), R11                // 6bd6c438
	MOVB.P 36(RSP), R27                        // fb478238
	//TODO MOVBW.W -57(R19), R13               // 6d7edc38
	MOVB.W -178(R16), R24                      // 18ee9438
	//TODO MOVBW 430(R8), R22                  // 16b9c639
	MOVB 997(R9), R23                          // 37958f39
	//TODO MOVBW (R2<<1)(R21), R15             // af7ae238
	//TODO MOVBW (R26)(R0), R21                // 1568fa38
	MOVB (R5)(R15), R16                        // b068af38
	MOVB (R19)(R26.SXTW), R19                  // 73caba38
	MOVB (R29)(R30), R14                       // ae6bbe38
	//TODO MOVHW.P 218(R22), R25               // d9a6cd78
	MOVH.P 179(R23), R5                        // e5368b78
	//TODO MOVHW.W 136(R2), R27                // 5b8cc878
	MOVH.W -63(R25), R22                       // 361f9c78
	//TODO MOVHW 5708(R25), R21                // 359bec79
	MOVH 54(R2), R13                           // 4d6c8079
	//TODO MOVHW (R22)(R24.SXTX), R4           // c4eaf878
	MOVH (R26)(R30.UXTW<<1), ZR                // 5f5bbe78
	MOVW.P -58(R16), R2                        // 02669cb8
	MOVW.W -216(R19), R8                       // 688e92b8
	MOVW 4764(R23), R10                        // ea9e92b9
	MOVW (R8)(R3.UXTW), R17                    // 1149a3b8
	//TODO LDTR -0x1e(R3), R4                  // 64285eb8
	//TODO LDTR -0xe5(R3), R10                 // 6ab851f8
	//TODO LDTRB 0xf0(R13), R10                // aa094f38
	//TODO LDTRH 0xe8(R13), R23                // b7894e78
	//TODO LDTRSB -0x24(R20), R5               // 85cadd38
	//TODO LDTRSB -0x75(R9), R13               // 2db99838
	//TODO LDTRSH 0xef(R3), LR                 // 7ef8ce78
	//TODO LDTRSH 0x96(R19), R24               // 786a8978
	//TODO LDTRSW 0x1e(LR), R5                 // c5eb81b8
	//TODO LDUR 0xbf(R13), R1                  // a1f14bb8
	//TODO LDUR -0x3c(R22), R3                 // c3425cf8
	//TODO LDURB -0xff(R17), R14               // 2e125038
	//TODO LDURH 0x80(R1), R6                  // 26004878
	//TODO LDURSB 0xde(LR), R3                 // c3e3cd38
	//TODO LDURSB 0x96(R9), R7                 // 27618938
	//TODO LDURSH -0x49(R11), R28              // 7c71db78
	//TODO LDURSH -0x1f(R0), R29               // 1d109e78
	//TODO LDURSW 0x48(R6), R20                // d48084b8
	LDXPW (R24), (R23, R11)                    // 172f7f88
	LDXP (R0), (R16, R13)                      // 10347fc8
	LDXRW (RSP), R30                           // fe7f5f88
	LDXR (R27), R12                            // 6c7f5fc8
	LDXRB (R0), R4                             // 047c5f08
	LDXRH (R12), R26                           // 9a7d5f48
	LSLW R11, R10, R15                         // 4f21cb1a
	LSL R27, R24, R21                          // 1523db9a
	LSLW $5, R7, R22                           // f6681b53
	LSL $57, R17, R2                           // 221a47d3
	LSRW R9, R3, R12                           // 6c24c91a
	LSR R10, R5, R2                            // a224ca9a
	LSRW $1, R3, R16                           // 707c0153
	LSR $12, R1, R20                           // 34fc4cd3
	MADDW R13, R23, R3, R10                    // 6a5c0d1b
	MADD R5, R23, R10, R4                      // 445d059b
	MNEGW R0, R9, R21                          // 35fd001b
	MNEG R14, R27, R23                         // 77ff0e9b
	MOVD  R2, R7                               // e70302aa
	MOVW $-24, R20                             // f4028012
	MOVD $-51096, ZR                           // fff29892
	MOVW $2507014144, R20                      // d4adb252
	MOVD $1313925191285342208, R7              // 8747e2d2
	ORRW $16252928, ZR, R21                    // f5130d32
	MOVD $-4260607558625, R11                  // eb6b16b2
	MOVD R30, R7                               // e7031eaa
	MOVKW $(3905<<0), R21                      // MOVKW $3905, R21              // 35e88172
	MOVKW $(3905<<16), R21                     // MOVKW $255918080, R21         // 35e8a172
	MOVK $(3905<<32), R21                      // MOVK $16771847290880, R21     // 35e8c1f2
	MOVD $0, R5                                // e5031faa
	MSR $1, SPSel                              // bf4100d5
	MSR $9, DAIFSet                            // df4903d5
	MSR $6, DAIFClr                            // ff4603d5
	MRS ELR_EL1, R8                            // 284038d5
	MSR R16, ELR_EL1                           // 304018d5
	MRS DCZID_EL0, R3                          // e3003bd5
	MSUBW R1, R1, R12, R5                      // 8585011b
	MSUB R19, R16, R26, R2                     // 42c3139b
	MULW R26, R5, R22                          // b67c1a1b
	MUL R4, R3, R0                             // 607c049b
	MVNW R3@>13, R8                            // e837e32a
	MVN R13>>31, R9                            // e97f6daa
	NEGSW R23<<1, R30                          // fe07176b
	NEGS R20>>35, R22                          // f68f54eb
	NGCW R13, R8                               // e8030d5a
	NGC R2, R7                                 // e70302da
	NGCSW R10, R5                              // e5030a7a
	NGCS R24, R16                              // f00318fa
	NOOP                                        // 1f2003d5
	ORNW R4@>11, R16, R3                       // 032ee42a
	ORN R22@>19, R3, R3                        // 634cf6aa
	ORRW $4294443071, R15, R24                 // f8490d32
	ORR $-3458764513820540929, R12, R22        // 96f542b2
	ORRW R13<<4, R8, R26                       // 1a110d2a
	ORR R3<<22, R5, R6                         // a65803aa
	PRFM (R8), $25                             // 190180f9
	PRFM (R2), PLDL1KEEP                       // 400080f9
	//TODO PRFM (R27)(R30.SXTW<<3), PLDL2STRM  // 63dbbff8
	//TODO PRFUM 22(R16), PSTL1KEEP            // 106281f8
	RBITW R9, R22                              // 3601c05a
	RBIT R11, R4                               // 6401c0da
	RET                                        // c0035fd6
	REVW R8, R10                               // 0a09c05a
	REV R1, R2                                 // 220cc0da
	REV16W R21, R19                            // b306c05a
	REV16 R25, R4                              // 2407c0da
	REV32 R27, R21                             // 750bc0da
	EXTRW $27, R4, R25, R19                    // 336f8413
	EXTR $17, R10, R29, R15                    // af47ca93
	ROR $14, R14, R15                          // cf39ce93
	RORW $28, R14, R15                         // cf718e13
	RORW R3, R12, R3                           // 832dc31a
	ROR R0, R23, R2                            // e22ec09a
	SBCW R4, R8, R24                           // 1801045a
	SBC R25, R10, R26                          // 5a0119da
	SBCSW R27, R19, R19                        // 73021b7a
	SBCS R5, R9, R5                            // 250105fa
	SBFIZW $9, R10, $18, R22                   // 56451713
	SBFIZ $6, R11, $15, R20                    // 74397a93
	SBFXW $8, R15, $10, R20                    // f4450813
	SBFX $2, R27, $54, R7                      // 67df4293
	SDIVW R22, R14, R9                         // c90dd61a
	SDIV R13, R21, R9                          // a90ecd9a
	SEV                                        // 9f2003d5
	SEVL                                       // bf2003d5
	SMADDL R3, R7, R11, R9                     // 691d239b
	SMSUBL R5, R19, R11, R29                   // 7dcd259b
	SMNEGL R26, R3, R15                        // 6ffc3a9b
	SMULH R17, R21, R21                        // b57e519b
	SMULL R0, R5, R0                           // a07c209b
	SMC $37977                                 // 238b12d4
	STLRW R16, (R22)                           // d0fe9f88
	STLR R3, (R24)                             // 03ff9fc8
	STLRB R11, (R22)                           // cbfe9f08
	STLRH R16, (R23)                           // f0fe9f48
	STLXR R7, (R27), R8                        // 67ff08c8
	STLXRW R13, (R15), R14                     // edfd0e88
	STLXRB R24, (R23), R8                      // f8fe0808
	STLXRH R19, (R27), R11                     // 73ff0b48
	STLXP (R6, R3), (R10), R2                  // 468d22c8
	STLXPW (R6, R11), (R22), R21               // c6ae3588
	//TODO STNPW 44(R1), R3, R10               // 2a8c0528
	//TODO STNP 0x108(R3), ZR, R7              // 67fc10a8
	LDP.P -384(R3), (R22, R26)                 // 7668e8a8
	LDP.W 280(R8), (R19, R11)                  // 13add1a9
	STP.P (R22, R27), 352(R0)                  // 166c96a8
	STP.W (R17, R11), 96(R8)                   // 112d86a9
	MOVW.P R20, -28(R1)                        // 34441eb8
	MOVD.P R17, 191(R16)                       // 11f60bf8
	MOVW.W R1, -171(R14)                       // c15d15b8
	MOVD.W R14, -220(R13)                      // ae4d12f8
	MOVW R3, 14828(R24)                        // 03ef39b9
	MOVD R0, 20736(R17)                        // 208228f9
	MOVB.P ZR, -117(R7)                        // ffb41838
	MOVB.W R27, -96(R13)                       // bb0d1a38
	MOVB R17, 2200(R13)                        // b1612239
	MOVH.P R7, -72(R4)                         // 87841b78
	MOVH.W R12, -125(R14)                      // cc3d1878
	MOVH R19, 3686(R26)                        // 53cf1c79
	MOVW R21, 34(R0)                           // 152002b8
	MOVD R25, -137(R17)                        // 397217f8
	MOVW R4, (R12)(R22.UXTW<<2)                // 845936b8
	MOVD R27, (R5)(R15.UXTW<<3)                // bb582ff8
	MOVB R2, (R10)(R16)                        // 42693038
	MOVB R2, (R29)(R26)                        // a26b3a38
	MOVH R11, -80(R23)                         // eb021b78
	MOVH R11, (R27)(R14.SXTW<<1)               // 6bdb2e78
	MOVB R19, (R0)(R4)                         // 13682438
	MOVB R1, (R6)(R4)                          // c1682438
	MOVH R3, (R11)(R13<<1)                     // 63792d78
	//TODO STTR 55(R4), R29                    // 9d7803b8
	//TODO STTR 124(R5), R25                   // b9c807f8
	//TODO STTRB -28(R23), R16                 // f04a1e38
	//TODO STTRH 9(R10), R19                   // 53990078
	STXP (R1, R2), (R3), R10                   // 61082ac8
	STXP (R1, R2), (RSP), R10                  // e10b2ac8
	STXPW (R1, R2), (R3), R10                  // 61082a88
	STXPW (R1, R2), (RSP), R10                 // e10b2a88
	STXRW R2, (R19), R20                       // 627e1488
	STXR R15, (R21), R13                       // af7e0dc8
	STXRB R7, (R9), R24                        // 277d1808
	STXRH R12, (R3), R8                        // 6c7c0848
	SUBW R20.UXTW<<2, R23, R19                 // f34a344b
	SUB R5.SXTW<<2, R1, R26                    // 3ac825cb
	SUB $(1923<<12), R4, R27                   // SUB $7876608, R4, R27         // 9b0c5ed1
	SUBW $(1923<<12), R4, R27                  // SUBW $7876608, R4, R27        // 9b0c5e51
	SUBW R12<<29, R7, R8                       // e8740c4b
	SUB R12<<61, R7, R8                        // e8f40ccb
	SUBSW R2.SXTH<<3, R13, R6                  // a6ad226b
	SUBS R21.UXTX<<2, R27, R4                  // 646b35eb
	SUBSW $(44<<12), R6, R9                    // SUBSW $180224, R6, R9         // c9b04071
	SUBS $(1804<<12), R13, R9                  // SUBS $7389184, R13, R9        // a9315cf1
	SUBSW R22->28, R6, R7                      // c770966b
	SUBSW R22>>28, R6, R7                      // c770566b
	SUBS R26<<15, R6, R16                      // d03c1aeb
	SVC $0                                     // 010000d4
	SVC $7165                                  // a17f03d4
	SXTBW R8, R25                              // 191d0013
	SXTB R13, R9                               // a91d4093
	SXTHW R8, R8                               // 083d0013
	SXTH R17, R25                              // 393e4093
	SXTW R0, R27                               // 1b7c4093
	SYSL $285440, R12                          // 0c5b2cd5
	TLBI VAE1IS, R1                            // 218308d5
	TSTW $0x80000007, R9                       // TSTW $2147483655, R9          // 3f0d0172
	TST $0xfffffff0, LR                        // TST $4294967280, R30          // df6f7cf2
	TSTW R10@>21, R2                           // 5f54ca6a
	TST R17<<11, R24                           // 1f2f11ea
	ANDSW $0x80000007, R9, ZR                  // ANDSW   $2147483655, R9, ZR   // 3f0d0172
	ANDS $0xfffffff0, LR, ZR                   // ANDS    $4294967280, R30, ZR  // df6f7cf2
	ANDSW R10@>21, R2, ZR                      // 5f54ca6a
	ANDS R17<<11, R24, ZR                      // 1f2f11ea
	UBFIZW $3, R19, $14, R14                   // 6e361d53
	UBFIZ $3, R22, $14, R4                     // c4367dd3
	UBFXW $3, R7, $20, R15                     // ef580353
	UBFX $33, R17, $25, R5                     // 25e661d3
	UDIVW R8, R21, R15                         // af0ac81a
	UDIV R2, R19, R21                          // 750ac29a
	UMADDL R0, R20, R17, R17                   // 3152a09b
	UMSUBL R22, R4, R3, R7                     // 6790b69b
	UMNEGL R3, R19, R1                         // 61fea39b
	UMULH R24, R20, R24                        // 987ed89b
	UMULL R19, R22, R19                        // d37eb39b
	UXTBW R2, R6                               // 461c0053
	UXTHW R7, R20                              // f43c0053
	VCNT V0.B8, V0.B8                          // 0058200e
	VCNT V0.B16, V0.B16                        // 0058204e
	WFE                                        // 5f2003d5
	WFI                                        // 7f2003d5
	YIELD                                      // 3f2003d5
	//TODO FABD F0, F5, F11                    // abd4a07e
	//TODO VFABD V30.S2, V8.S2, V24.S2         // 18d5be2e
	//TODO VFABS V5.S4, V24.S4                 // b8f8a04e
	FABSS F2, F28                              // 5cc0201e
	FABSD F0, F14                              // 0ec0601e
	//TODO FACGE F25, F16, F0                  // 00ee797e
	//TODO VFACGE V11.S2, V15.S2, V9.S2        // e9ed2b2e
	//TODO FACGT F20, F16, F27                 // 1beef47e
	//TODO VFACGT V15.S4, V25.S4, V22.S4       // 36efaf6e
	//TODO VFADD V21.D2, V10.D2, V21.D2        // 55d5754e
	FADDS F12, F2, F10                         // 4a282c1e
	FADDD F24, F14, F12                        // cc29781e
	//TODO VFADDP V4.D2, F13                   // 8dd8707e
	//TODO VFADDP V30.S4, V3.S4, V11.S4        // 6bd43e6e
	FCCMPS LE, F17, F12, $14                   // 8ed5311e
	FCCMPD HI, F11, F15, $15                   // ef856b1e
	FCCMPES HS, F28, F13, $13                  // bd253c1e
	FCCMPED LT, F20, F4, $9                    // 99b4741e
	//TODO FCMEQ F7, F11, F26                  // 7ae5675e
	//TODO VFCMEQ V29.S4, V26.S4, V30.S4       // 5ee73d4e
	//TODO FCMEQ $0, F17, F22                  // 36daa05e
	//TODO VFCMEQ $0, V17.D2, V22.D2           // 36dae04e
	//TODO FCMGE F29, F31, F13                 // ede77d7e
	//TODO VFCMGE V8.S2, V31.S2, V2.S2         // e2e7282e
	//TODO FCMGE $0, F18, F27                  // e2e7282e
	//TODO VFCMGE $0, V14.S2, V8.S2            // c8c9a02e
	//TODO FCMGT F20, F2, F8                   // 48e4b47e
	//TODO VFCMGT V26.D2, V15.D2, V23.D2       // f7e5fa6e
	//TODO FCMGT $0, F14, F3                   // c3c9e05e
	//TODO VFCMGT $0, V6.S2, V28.S2            // dcc8a00e
	//TODO FCMLE $0, F26, F25                  // 59dba07e
	//TODO VFCMLE $0, V28.S2, V20.S2           // 94dba02e
	//TODO FCMLT $0, F17, F3                   // 23eae05e
	//TODO VFCMLT $0, V8.S4, V7.S4             // 07e9a04e
	FCMPS F3, F17                              // 2022231e
	FCMPS $(0.0), F8                           // 0821201e
	FCMPD F11, F27                             // 60236b1e
	FCMPD $(0.0), F25                          // 2823601e
	FCMPES F16, F30                            // d023301e
	FCMPES $(0.0), F29                         // b823201e
	FCMPED F13, F10                            // 50216d1e
	FCMPED $(0.0), F25                         // 3823601e
	FCSELS EQ, F26, F27, F25                   // 590f3b1e
	FCSELD PL, F8, F22, F7                     // 075d761e
	//TODO FCVTAS F4, F28                      // 9cc8215e
	//TODO VFCVTAS V21.D2, V27.D2              // bbca614e
	//TODO FCVTAS F27, R7                      // 6703241e
	//TODO FCVTAS F19, R26                     // 7a02249e
	//TODO FCVTAS F4, R0                       // 8000641e
	//TODO FCVTAS F3, R19                      // 7300649e
	//TODO FCVTAU F18, F28                     // 5cca217e
	//TODO VFCVTAU V30.S4, V27.S4              // dbcb216e
	//TODO FCVTAU F0, R2                       // 0200251e
	//TODO FCVTAU F0, R24                      // 1800259e
	//TODO FCVTAU F31, R10                     // ea03651e
	//TODO FCVTAU F3, R8                       // 6800659e
	//TODO VFCVTL V11.S2, V21.D2               // 7579610e
	//TODO VFCVTL2 V15.H8, V25.S4              // f979214e
	//TODO FCVTMS F21, F28                     // bcba215e
	//TODO VFCVTMS V5.D2, V2.D2                // a2b8614e
	//TODO FCVTMS F31, R19                     // f303301e
	//TODO FCVTMS F23, R16                     // f002309e
	//TODO FCVTMS F16, R22                     // 1602701e
	//TODO FCVTMS F14, R19                     // d301709e
	//TODO FCVTMU F14, F8                      // c8b9217e
	//TODO VFCVTMU V7.D2, V1.D2                // e1b8616e
	//TODO FCVTMU F2, R0                       // 4000311e
	//TODO FCVTMU F23, R19                     // f302319e
	//TODO FCVTMU F16, R17                     // 1102711e
	//TODO FCVTMU F12, R19                     // 9301719e
	//TODO VFCVTN V23.D2, V26.S2               // fa6a610e
	//TODO VFCVTN2 V2.D2, V31.S4               // 5f68614e
	//TODO FCVTNS F3, F27                      // 7ba8215e
	//TODO VFCVTNS V11.S2, V12.S2              // 6ca9210e
	//TODO FCVTNS F14, R9                      // c901201e
	//TODO FCVTNS F0, R27                      // 1b00209e
	//TODO FCVTNS F23, R0                      // e002601e
	//TODO FCVTNS F6, R30                      // de00609e
	//TODO FCVTNU F12, F9                      // 89a9217e
	//TODO VFCVTNU V3.D2, V20.D2               // 74a8616e
	//TODO FCVTNU F20, R11                     // 8b02211e
	//TODO FCVTNU F23, R19                     // f302219e
	//TODO FCVTNU F4, R5                       // 8500611e
	//TODO FCVTNU F11, R19                     // 7301619e
	//TODO FCVTPS F20, F26                     // 9aaae15e
	//TODO VFCVTPS V29.S4, V13.S4              // adaba14e
	//TODO FCVTPS F5, R29                      // bd00281e
	//TODO FCVTPS F3, R3                       // 6300289e
	//TODO FCVTPS F4, R25                      // 9900681e
	//TODO FCVTPS F29, R15                     // af03689e
	//TODO FCVTPU F13, F3                      // a3a9e17e
	//TODO VFCVTPU V6.S4, V24.S4               // d8a8a16e
	//TODO FCVTPU F17, R17                     // 3102291e
	//TODO FCVTPU F7, R23                      // f700299e
	//TODO FCVTPU F10, R3                      // 4301691e
	//TODO FCVTPU F24, R27                     // 1b03699e
	//TODO FCVTXN F14, F0                      // c069617e
	//TODO VFCVTXN V1.D2, V17.S2               // 3168612e
	//TODO VFCVTXN2 V0.D2, V21.S4              // 1568616e
	//TODO FCVTZS $26, F29, F19                // b3ff665f
	//TODO VFCVTZS $45, V14.D2, V18.D2         // d2fd534f
	//TODO FCVTZS F8, F7                       // 07b9a15e
	//TODO VFCVTZS V2.S2, V4.S2                // 44b8a10e
	//TODO FCVTZS $26, F7, R11                 // eb98181e
	//TODO FCVTZS $7, F4, ZR                   // 9fe4189e
	//TODO FCVTZS $28, F13, R14                // ae91581e
	//TODO FCVTZS $8, F27, R3                  // 63e3589e
	FCVTZSSW F7, R15                           // ef00381e
	FCVTZSS F16, ZR                            // 1f02389e
	FCVTZSDW F19, R3                           // 6302781e
	FCVTZSD F7, R7                             // e700789e
	//TODO FCVTZU $17, F18, F28                // 5cfe2f7f
	//TODO VFCVTZU $19, V20.D2, V11.D2         // 8bfe6d6f
	//TODO FCVTZU F22, F8                      // c8bae17e
	//TODO VFCVTZU V0.S4, V1.S4                // 01b8a16e
	//TODO FCVTZU $14, F24, R20                // 14cb191e
	//TODO FCVTZU $6, F25, R17                 // 31eb199e
	//TODO FCVTZU $5, F17, R10                 // 2aee591e
	//TODO FCVTZU $6, F7, R19                  // f3e8599e
	FCVTZUSW F2, R9                            // 4900391e
	FCVTZUS F12, R29                           // 9d01399e
	FCVTZUDW F27, R22                          // 7603791e
	FCVTZUD F25, R22                           // 3603799e
	//TODO VFDIV V6.D2, V1.D2, V27.D2          // 3bfc666e
	FDIVS F16, F10, F20                        // 5419301e
	FDIVD F11, F25, F30                        // 3e1b6b1e
	FMADDS F15, F2, F8, F1                     // 01090f1f
	FMADDD F15, F21, F25, F9                   // 29574f1f
	//TODO VFMAX V23.D2, V27.D2, V14.D2        // 6ef7774e
	FMAXS F5, F28, F27                         // 9b4b251e
	FMAXD F12, F31, F31                        // ff4b6c1e
	//TODO VFMAXNM V3.D2, V12.D2, V27.D2       // 9bc5634e
	FMAXNMS F11, F24, F12                      // 0c6b2b1e
	FMAXNMD F20, F6, F16                       // d068741e
	//TODO VFMAXNMP V3.S2, F2                  // 62c8307e
	//TODO VFMAXNMP V25.S2, V4.S2, V2.S2       // 82c4392e
	//TODO VFMAXNMV V14.S4, F15                // cfc9306e
	//TODO VFMAXP V3.S2, F27                   // 7bf8307e
	//TODO VFMAXP V29.S2, V30.S2, V9.S2        // c9f73d2e
	//TODO VFMAXV V13.S4, F14                  // aef9306e
	//TODO VFMIN V19.D2, V30.D2, V7.D2         // c7f7f34e
	FMINS F26, F18, F30                        // 5e5a3a1e
	FMIND F29, F4, F21                         // 95587d1e
	//TODO VFMINNM V21.S4, V5.S4, V1.S4        // a1c4b54e
	FMINNMS F23, F20, F1                       // 817a371e
	FMINNMD F8, F3, F24                        // 7878681e
	//TODO VFMINNMP V16.D2, F12                // 0ccaf07e
	//TODO VFMINNMP V10.S4, V25.S4, V27.S4     // 3bc7aa6e
	//TODO VFMINNMV V8.S4, F3                  // 03c9b06e
	//TODO VFMINP V10.S2, F20                  // 54f9b07e
	//TODO VFMINP V1.D2, V10.D2, V3.D2         // 43f5e16e
	//TODO VFMINV V11.S4, F9                   // 69f9b06e
	//TODO VFMLA V6.S[0], F2, F14              // 4e10865f
	//TODO VFMLA V28.S[2], V2.S2, V30.S2       // 5e189c0f
	VFMLA V29.S2, V20.S2, V14.S2               // 8ece3d0e
	//TODO VFMLS V24.D[1], F3, F17             // 7158d85f
	//TODO VFMLS V10.S[0], V11.S2, V10.S2      // 6a518a0f
	VFMLS V29.S2, V27.S2, V17.S2               // 71cfbd0e
	//TODO FMOVS $(-1.625), F13                // 0d503f1e
	//TODO FMOVD $12.5, F30                    // 1e30651e
	//TODO VFMOV R7, V25.D[1]                  // f900af9e
	FMOVD F2, R15                              // 4f00669e
	FMOVD R3, F11                              // 6b00679e
	FMOVS F20, R29                             // 9d02261e
	FMOVS R8, F15                              // 0f01271e
	FMOVD F2, F9                               // 4940601e
	FMOVS F4, F27                              // 9b40201e
	//TODO VFMOV $3.125, V8.D2                 // 28f5006f
	FMSUBS F13, F21, F13, F19                  // b3d50d1f
	FMSUBD F11, F7, F15, F31                   // ff9d4b1f
	//TODO VFMUL V9.S[2], F21, F19             // b39a895f
	//TODO VFMUL V26.S[2], V26.S2, V2.S2       // 429b9a0f
	//TODO VFMUL V21.D2, V17.D2, V25.D2        // 39de756e
	FMULS F0, F6, F24                          // d808201e
	FMULD F5, F29, F9                          // a90b651e
	//TODO VFMULX V26.S[2], F20, F8            // 889a9a7f
	//TODO VFMULX V12.D[1], V21.D2, V31.D2     // bf9acc6f
	//TODO FMULX F16, F1, F31                  // 3fdc705e
	//TODO VFMULX V29.S2, V13.S2, V31.S2       // bfdd3d0e
	//TODO VFNEG V18.S2, V12.S2                // 4cfaa02e
	FNEGS F16, F5                              // 0542211e
	FNEGD F31, F31                             // ff43611e
	FNMADDS F17, F22, F6, F20                  // d458311f
	FNMADDD F15, F0, F26, F20                  // 54036f1f
	FNMSUBS F14, F16, F27, F14                 // 6ec32e1f
	FNMSUBD F29, F25, F8, F10                  // 0ae57d1f
	FNMULS F24, F22, F18                       // d28a381e
	FNMULD F14, F30, F7                        // c78b6e1e
	//TODO FRECPE F9, F2                       // 22d9e15e
	//TODO VFRECPE V0.S2, V28.S2               // 1cd8a10e
	//TODO FRECPS F28, F10, F9                 // 49fd3c5e
	//TODO VFRECPS V27.D2, V12.D2, V24.D2      // 98fd7b4e
	//TODO FRECPX F28, F3                      // 83fbe15e
	//TODO VFRINTA V14.S2, V25.S2              // d989212e
	FRINTAS F0, F21                            // 1540261e
	FRINTAD F8, F22                            // 1641661e
	//TODO VFRINTI V21.D2, V31.D2              // bf9ae16e
	FRINTIS F17, F17                           // 31c2271e
	FRINTID F9, F15                            // 2fc1671e
	//TODO VFRINTM V9.D2, V27.D2               // 3b99614e
	FRINTMS F24, F16                           // 1043251e
	FRINTMD F5, F2                             // a240651e
	//TODO VFRINTN V30.S4, V2.S4               // c28b214e
	FRINTNS F26, F14                           // 4e43241e
	FRINTND F28, F12                           // 8c43641e
	//TODO VFRINTP V27.D2, V31.D2              // 7f8be14e
	FRINTPS F27, F4                            // 64c3241e
	FRINTPD F6, F22                            // d6c0641e
	//TODO VFRINTX V25.D2, V0.D2               // 209b616e
	FRINTXS F26, F10                           // 4a43271e
	FRINTXD F16, F12                           // 0c42671e
	//TODO VFRINTZ V25.S4, V27.S4              // 3b9ba14e
	FRINTZS F3, F28                            // 7cc0251e
	FRINTZD F24, F6                            // 06c3651e
	//TODO FRSQRTE F29, F5                     // a5dbe17e
	//TODO VFRSQRTE V18.S2, V1.S2              // 41daa12e
	//TODO FRSQRTS F17, F7, F24                // f8fcf15e
	//TODO VFRSQRTS V14.S2, V10.S2, V24.S2     // 58fdae0e
	//TODO VFSQRT V2.D2, V21.D2                // 55f8e16e
	FSQRTS F0, F9                              // 09c0211e
	FSQRTD F14, F27                            // dbc1611e
	FSUBS F25, F23, F0                         // e03a391e
	FSUBD F11, F13, F24                        // b8396b1e
	//TODO SCVTFSS F30, F20                    // d4db215e
	//TODO VSCVTF V7.S2, V17.S2                // f1d8210e
	SCVTFWS R3, F16                            // 7000221e
	SCVTFWD R20, F4                            // 8402621e
	SCVTFS R16, F12                            // 0c02229e
	SCVTFD R26, F14                            // 4e03629e
	UCVTFWS R6, F4                             // c400231e
	UCVTFWD R10, F23                           // 5701631e
	UCVTFS R24, F29                            // 1d03239e
	UCVTFD R20, F11                            // 8b02639e
	VADD V16, V19, V14                                          // 6e86f05e
	VADD V5.H8, V18.H8, V9.H8                                   // 4986654e
	VADDP V7.H8, V25.H8, V17.H8                                 // 31bf674e
	VADDV V3.H8, V0                                             // 60b8714e
	AESD V22.B16, V19.B16                                       // d35a284e
	AESE V31.B16, V29.B16                                       // fd4b284e
	AESIMC V12.B16, V27.B16                                     // 9b79284e
	AESMC V14.B16, V28.B16                                      // dc69284e
	VAND V4.B16, V4.B16, V9.B16                                 // 891c244e
	VCMEQ V24.S4, V13.S4, V12.S4                                // ac8db86e
	VCNT V13.B8, V11.B8                                         // ab59200e
	VMOV V31.B[15], V18                                         // f2071f5e
	VDUP V31.B[15], V18                                         // f2071f5e
	VDUP V31.B[13], V20.B16                                     // f4071b4e
	VEOR V4.B8, V18.B8, V7.B8                                   // 471e242e
	VEXT $4, V2.B8, V1.B8, V3.B8                                // 2320022e
	VEXT $8, V2.B16, V1.B16, V3.B16                             // 2340026e
	VMOV V11.B[11], V16.B[12]                                   // 705d196e
	VMOV R20, V21.B[2]                                          // 951e054e
	VLD1 (R2), [V21.B16]                                        // 5570404c
	VLD1 (R24), [V18.D1, V19.D1, V20.D1]                        // 126f400c
	VLD1 (R29), [V14.D1, V15.D1, V16.D1, V17.D1]                // ae2f400c
	VLD1.P 16(R23), [V1.B16]                                    // e172df4c
	VLD1.P (R6)(R11), [V31.D1]                                  // df7ccb0c
	VLD1.P 16(R7), [V31.D1, V0.D1]                              // ffacdf0c
	VLD1.P (R19)(R4), [V24.B8, V25.B8]                          // 78a2c40c
	VLD1.P (R20)(R8), [V7.H8, V8.H8, V9.H8]                     // 8766c84c
	VLD1.P 32(R30), [V5.B8, V6.B8, V7.B8, V8.B8]                // c523df0c
	VLD1 (R19), V14.B[15]                                       // 6e1e404d
	VLD1 (R29), V0.H[1]                                         // a04b400d
	VLD1 (R27), V2.S[0]                                         // 6283400d
	VLD1 (R21), V5.D[1]                                         // a586404d
	VLD1.P 1(R19), V10.B[14]                                    // 6a1adf4d
	VLD1.P (R3)(R14), V16.B[11]                                 // 700cce4d
	VLD1.P 2(R1), V28.H[2]                                      // 3c50df0d
	VLD1.P (R13)(R20), V9.H[2]                                  // a951d40d
	VLD1.P 4(R17), V1.S[3]                                      // 2192df4d
	VLD1.P (R14)(R2), V17.S[2]                                  // d181c24d
	VLD1.P 8(R5), V30.D[1]                                      // be84df4d
	VLD1.P (R27)(R13), V27.D[0]                                 // 7b87cd0d
	//TODO FMOVS.P -29(RSP), F8                                 // e8375ebc
	//TODO FMOVS.W 71(R29), F28                                 // bc7f44bc
	FMOVS 6160(R4), F23                                         // 971058bd
	VMOV V18.B[10], V27                                         // 5b06155e
	VDUP V18.B[10], V27                                         // 5b06155e
	VMOV V12.B[2], V28.B[12]                                    // 9c15196e
	VMOV R30, V4.B[13]                                          // c41f1b4e
	VMOV V2.B16, V4.B16                                         // 441ca24e
	VMOV V13.S[0], R20                                          // b43d040e
	VMOV V13.D[0], R20                                          // b43d084e
	VMOVI $146, V22.B16                                         // 56e6044f
	VORR V25.B16, V22.B16, V15.B16                              // cf1eb94e
	VPMULL V2.D1, V1.D1, V3.Q1                                  // 23e0e20e
	VPMULL2 V2.D2, V1.D2, V4.Q1                                 // 24e0e24e
	VPMULL V2.B8, V1.B8, V3.H8                                  // 23e0220e
	VPMULL2 V2.B16, V1.B16, V4.H8                               // 24e0224e
	VRBIT V10.B16, V21.B16                                      // 5559606e
	VREV32 V2.H8, V1.H8                                         // 4108606e
	VREV16 V2.B8, V1.B8                                         // 4118200e
	VREV16 V5.B16, V16.B16                                      // b018204e
	SCVTFWS R6, F17                                             // d100221e
	SCVTFWD R3, F15                                             // 6f00621e
	SCVTFS R20, F25                                             // 9902229e
	SCVTFD R13, F9                                              // a901629e
	SHA1C V8.S4, V8, V2                                         // 0201085e
	SHA1H V17, V25                                              // 390a285e
	SHA1M V0.S4, V27, V27                                       // 7b23005e
	SHA1P V3.S4, V20, V27                                       // 9b12035e
	SHA1SU0 V17.S4, V13.S4, V16.S4                              // b031115e
	SHA1SU1 V24.S4, V23.S4                                      // 171b285e
	SHA256H2 V6.S4, V16, V11                                    // 0b52065e
	SHA256H V4.S4, V2, V11                                      // 4b40045e
	SHA256SU0 V0.S4, V16.S4                                     // 1028285e
	SHA256SU1 V31.S4, V3.S4, V15.S4                             // 6f601f5e
	VSHL $7, V22.D2, V25.D2                                     // d956474f
	VST1 [V14.H4, V15.H4, V16.H4], (R27)                        // 6e67000c
	VST1 [V2.S4, V3.S4, V4.S4, V5.S4], (R14)                    // c229004c
	VST1.P [V25.S4], (R7)(R29)                                  // f9789d4c
	VST1.P [V25.D2, V26.D2], 32(R7)                             // f9ac9f4c
	VST1.P [V14.D1, V15.D1], (R7)(R23)                          // eeac970c
	VST1.P [V25.D2, V26.D2, V27.D2], 48(R27)                    // 796f9f4c
	VST1.P [V13.H8, V14.H8, V15.H8], (R3)(R14)                  // 6d648e4c
	VST1.P [V16.S4, V17.S4, V18.S4, V19.S4], 64(R6)             // d0289f4c
	VST1.P [V19.H4, V20.H4, V21.H4, V22.H4], (R4)(R16)          // 9324900c
	VST1 V12.B[3], (R1)                                         // 2c0c000d
	VST1 V12.B[3], (R1)                                         // 2c0c000d
	VST1 V25.S[2], (R20)                                        // 9982004d
	VST1 V9.D[1], (RSP)                                         // e987004d
	VST1.P V30.B[6], 1(R3)                                      // 7e189f0d
	VST1.P V8.B[0], (R3)(R21)                                   // 6800950d
	VST1.P V15.H[5], 2(R10)                                     // 4f499f4d
	VST1.P V1.H[7], (R23)(R11)                                  // e15a8b4d
	VST1.P V26.S[0], 4(R11)                                     // 7a819f0d
	VST1.P V9.S[1], (R16)(R21)                                  // 0992950d
	VST1.P V16.D[0], 8(R9)                                      // 30859f0d
	VST1.P V23.D[1], (R21)(R16)                                 // b786904d
	VSUB V1, V12, V23                                           // 9785e17e
	VUADDLV V31.S4, V11                                         // eb3bb06e
	UCVTFWS R11, F19                                            // 7301231e
	UCVTFWD R26, F13                                            // 4d03631e
	UCVTFS R23, F11                                             // eb02239e
	UCVTFD R5, F29                                              // bd00639e
	VMOV V0.B[1], R11                                           // 0b3c030e
	VMOV V1.H[3], R12                                           // 2c3c0e0e
	VUSHR $6, V22.H8, V23.H8                                    // d7061a6f

	RET
