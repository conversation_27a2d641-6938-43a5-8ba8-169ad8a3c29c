{"name": "@jimp/plugin-quantize", "version": "1.6.0", "repository": "jimp-dev/jimp", "engines": {"node": ">=18"}, "scripts": {"lint": "eslint .", "test": "vitest", "build": "tshy", "dev": "tshy --watch", "clean": "rm -rf node_modules .tshy .tshy-build dist .turbo"}, "author": "<PERSON> <<EMAIL>>", "license": "MIT", "devDependencies": {"@jimp/config-eslint": "1.6.0", "@jimp/config-typescript": "1.6.0", "@jimp/core": "1.6.0", "@jimp/js-jpeg": "1.6.0", "@jimp/js-png": "1.6.0", "@jimp/test-utils": "1.6.0", "@jimp/types": "1.6.0", "eslint": "^9.9.1", "tshy": "^3.0.2", "typescript": "^5.5.4", "vite-plugin-node-polyfills": "^0.22.0", "vitest": "^2.0.5"}, "tshy": {"exclude": ["**/*.test.ts"], "exports": {"./package.json": "./package.json", ".": "./src/index.ts"}}, "exports": {"./package.json": "./package.json", ".": {"import": {"types": "./dist/esm/index.d.ts", "default": "./dist/esm/index.js"}, "require": {"types": "./dist/commonjs/index.d.ts", "default": "./dist/commonjs/index.js"}}}, "main": "./dist/commonjs/index.js", "types": "./dist/commonjs/index.d.ts", "type": "module", "publishConfig": {"access": "public"}, "sideEffects": false, "dependencies": {"image-q": "^4.0.0", "zod": "^3.23.8"}, "module": "./dist/esm/index.js", "gitHead": "c88abe6046dccbdb6e4f5f00c3dd403c81d83515"}