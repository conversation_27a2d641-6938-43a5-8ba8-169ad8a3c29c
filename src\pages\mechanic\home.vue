<script lang="ts" setup>
import { ref, onMounted, onUnmounted } from 'vue'
import scannerUtils from '@/utils/scannerUtils.js'

// 用户信息
const userInfo = ref<any>({})

// 机器列表数据
const machineList = ref([
  {
    id: 1,
    code: 'M001',
    name: '织机A-01',
    location: 'A区-1号位',
    status: 'error', // normal: 正常, warning: 警告, error: 故障, maintenance: 维修中
    temperature: 65,
    speed: 1200,
    efficiency: 85,
    lastMaintenance: '2024-01-10',
    errorCount: 2,
    errorDescription: '机器异响，疑似轴承问题'
  },
  {
    id: 2,
    code: 'M002',
    name: '织机B-03',
    location: 'B区-3号位',
    status: 'normal',
    temperature: 58,
    speed: 1350,
    efficiency: 92,
    lastMaintenance: '2024-01-12',
    errorCount: 0
  },
  {
    id: 3,
    code: 'M003',
    name: '织机C-05',
    location: 'C区-5号位',
    status: 'maintenance',
    temperature: 72,
    speed: 0,
    efficiency: 0,
    lastMaintenance: '2024-01-14',
    errorCount: 1,
    errorDescription: '温度过高，正在检查冷却系统'
  },
  {
    id: 4,
    code: 'M004',
    name: '织机A-08',
    location: 'A区-8号位',
    status: 'warning',
    temperature: 68,
    speed: 1100,
    efficiency: 78,
    lastMaintenance: '2024-01-08',
    errorCount: 1,
    errorDescription: '效率下降，需要检查'
  }
])

// 统计数据
const stats = ref({
  total: 4,
  normal: 1,
  warning: 1,
  error: 1,
  maintenance: 1
})

// 获取状态信息
const getStatusInfo = (status: string) => {
  const statusMap = {
    normal: { text: '正常', color: '#4CAF50', bgColor: '#E8F5E8' },
    warning: { text: '警告', color: '#FF9800', bgColor: '#FFF3E0' },
    error: { text: '故障', color: '#F44336', bgColor: '#FFEBEE' },
    maintenance: { text: '维修中', color: '#2196F3', bgColor: '#E3F2FD' }
  }
  return statusMap[status as keyof typeof statusMap] || { text: '未知', color: '#999999', bgColor: '#F5F5F5' }
}

// 扫码维修
const handleScanRepair = () => {
  // 使用新大陆扫码头或降级到uni.scanCode
  scannerUtils.initScanner((result) => {
    if (result.success) {
      console.log('扫码结果:', result.result)
      // 跳转到维修操作页面，传递机器码
      uni.navigateTo({
        url: `/pages/repair/repair?machineCode=${result.result}`
      })
    } else {
      console.error('扫码失败:', result.error)
      uni.showToast({
        title: result.error || '扫码失败',
        icon: 'none'
      })
    }
  }, true)
}

// 查看机器详情
const viewMachineDetail = (machine: any) => {
  const statusInfo = getStatusInfo(machine.status)
  let content = `位置：${machine.location}\n状态：${statusInfo.text}\n温度：${machine.temperature}°C\n转速：${machine.speed} RPM\n效率：${machine.efficiency}%\n上次维护：${machine.lastMaintenance}`
  
  if (machine.errorDescription) {
    content += `\n异常描述：${machine.errorDescription}`
  }
  
  uni.showModal({
    title: machine.name,
    content: content,
    showCancel: false
  })
}

// 开始维修
const startRepair = (machine: any) => {
  uni.navigateTo({
    url: `/pages/repair/repair?machineCode=${machine.code}&machineId=${machine.id}`
  })
}

// 退出登录
const handleLogout = () => {
  uni.showModal({
    title: '确认退出',
    content: '确定要退出登录吗？',
    success: (res) => {
      if (res.confirm) {
        uni.removeStorageSync('userInfo')
        uni.reLaunch({
          url: '/pages/login/login'
        })
      }
    }
  })
}

// 页面加载时获取用户信息
onMounted(() => {
  const user = uni.getStorageSync('userInfo')
  if (user) {
    userInfo.value = user
  } else {
    // 如果没有用户信息，跳转到登录页
    uni.reLaunch({
      url: '/pages/login/login'
    })
  }

  // 设置页面为活跃状态
  scannerUtils.setPageActive(true)
  // 使用新大陆扫码头或降级到uni.scanCode
  scannerUtils.initScanner((result) => {
    if (result.success) {
      console.log('扫码结果:', result.result)
      // 跳转到维修操作页面，传递机器码
      uni.navigateTo({
        url: `/pages/repair/repair?machineCode=${result.result}`
      })
    } else {
      console.error('扫码失败:', result.error)
      uni.showToast({
        title: result.error || '扫码失败',
        icon: 'none'
      })
    }
  }, true)
})

// 页面卸载时清理扫码资源
onUnmounted(() => {
  scannerUtils.destroy()
})
</script>

<script>
export default {
  onShow() {
    console.log('机修工页面显示，激活扫码功能')
    scannerUtils.setPageActive(true)
  },
  onHide() {
    console.log('机修工页面隐藏，停用扫码功能')
    scannerUtils.setPageActive(false)
  },
  onUnload() {
    console.log('机修工页面卸载，清理扫码资源')
    scannerUtils.destroy()
  }
}
</script>

<template>
  <view class="mechanic-home">
    <!-- 用户信息头部 -->
    <view class="user-header">
      <view class="user-info">
        <view class="avatar">
          <text class="avatar-text">{{ userInfo.username?.charAt(0)?.toUpperCase() }}</text>
        </view>
        <view class="user-details">
          <text class="username">{{ userInfo.username }}</text>
          <text class="role">机修工</text>
        </view>
      </view>
      <view class="logout-btn" @click="handleLogout">
        <text class="logout-text">退出</text>
      </view>
    </view>
    
    <!-- 扫码维修提示 -->
    <view class="scan-tip" @click="handleScanRepair">
      <view class="scan-icon">
        <text class="icon">📱</text>
      </view>
      <view class="scan-text">
        <text class="tip-title">扫码开始维修</text>
        <text class="tip-desc">扫描机器二维码直接进入维修页面</text>
      </view>
      <view class="scan-arrow">
        <text class="arrow">→</text>
      </view>
    </view>
    
    <!-- 统计概览 -->
    <view class="stats-overview">
      <text class="section-title">设备状态概览</text>
      <view class="stats-grid">
        <view class="stat-card">
          <text class="stat-number">{{ stats.total }}</text>
          <text class="stat-label">总设备</text>
        </view>
        <view class="stat-card normal">
          <text class="stat-number">{{ stats.normal }}</text>
          <text class="stat-label">正常</text>
        </view>
        <view class="stat-card warning">
          <text class="stat-number">{{ stats.warning }}</text>
          <text class="stat-label">警告</text>
        </view>
        <view class="stat-card error">
          <text class="stat-number">{{ stats.error }}</text>
          <text class="stat-label">故障</text>
        </view>
      </view>
    </view>
    
    <!-- 机器列表 -->
    <view class="machine-section">
      <text class="section-title">设备列表</text>
      
      <view class="machine-list">
        <view 
          class="machine-card" 
          v-for="machine in machineList" 
          :key="machine.id"
          @click="viewMachineDetail(machine)"
        >
          <view class="machine-header">
            <view class="machine-info">
              <text class="machine-name">{{ machine.name }}</text>
              <text class="machine-location">{{ machine.location }}</text>
            </view>
            <view 
              class="status-badge" 
              :style="{ 
                backgroundColor: getStatusInfo(machine.status).bgColor,
                color: getStatusInfo(machine.status).color 
              }"
            >
              <text class="status-text">{{ getStatusInfo(machine.status).text }}</text>
            </view>
          </view>
          
          <view class="machine-metrics">
            <view class="metric-item">
              <text class="metric-label">温度</text>
              <text class="metric-value">{{ machine.temperature }}°C</text>
            </view>
            <view class="metric-item">
              <text class="metric-label">转速</text>
              <text class="metric-value">{{ machine.speed }} RPM</text>
            </view>
            <view class="metric-item">
              <text class="metric-label">效率</text>
              <text class="metric-value">{{ machine.efficiency }}%</text>
            </view>
          </view>
          
          <view class="machine-footer" v-if="machine.errorDescription">
            <text class="error-desc">{{ machine.errorDescription }}</text>
          </view>
          
          <view class="machine-actions" v-if="machine.status === 'error'">
            <button 
              class="repair-btn" 
              @click.stop="startRepair(machine)"
            >
              开始维修
            </button>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<style lang="scss">
.mechanic-home {
  min-height: 100vh;
  background: #f5f5f5;
  padding-bottom: 40rpx;
}

.user-header {
  background: linear-gradient(135deg, #2196F3 0%, #1976D2 100%);
  padding: 40rpx 30rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.user-info {
  display: flex;
  align-items: center;
}

.avatar {
  width: 80rpx;
  height: 80rpx;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 24rpx;
}

.avatar-text {
  font-size: 32rpx;
  font-weight: bold;
  color: #ffffff;
}

.user-details {
  display: flex;
  flex-direction: column;
}

.username {
  font-size: 32rpx;
  font-weight: bold;
  color: #ffffff;
  margin-bottom: 8rpx;
}

.role {
  font-size: 24rpx;
  color: rgba(255, 255, 255, 0.8);
}

.logout-btn {
  padding: 16rpx 24rpx;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 20rpx;
}

.logout-text {
  font-size: 24rpx;
  color: #ffffff;
}

.scan-tip {
  margin: 30rpx;
  background: linear-gradient(135deg, #FF9800 0%, #F57C00 100%);
  border-radius: 16rpx;
  padding: 30rpx;
  display: flex;
  align-items: center;
  box-shadow: 0 4rpx 20rpx rgba(255, 152, 0, 0.3);
  
  &:active {
    transform: scale(0.98);
  }
}

.scan-icon {
  width: 60rpx;
  height: 60rpx;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 12rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 24rpx;
}

.icon {
  font-size: 28rpx;
}

.scan-text {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.tip-title {
  font-size: 28rpx;
  font-weight: bold;
  color: #ffffff;
  margin-bottom: 8rpx;
}

.tip-desc {
  font-size: 22rpx;
  color: rgba(255, 255, 255, 0.8);
}

.scan-arrow {
  width: 40rpx;
  height: 40rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.arrow {
  font-size: 24rpx;
  color: rgba(255, 255, 255, 0.8);
}

.stats-overview {
  padding: 0 30rpx 30rpx;
}

.section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333333;
  margin-bottom: 24rpx;
}

.stats-grid {
  display: flex;
  gap: 15rpx;
}

.stat-card {
  flex: 1;
  background: #ffffff;
  border-radius: 12rpx;
  padding: 24rpx 16rpx;
  text-align: center;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
  
  &.normal {
    border-left: 6rpx solid #4CAF50;
  }
  
  &.warning {
    border-left: 6rpx solid #FF9800;
  }
  
  &.error {
    border-left: 6rpx solid #F44336;
  }
}

.stat-number {
  display: block;
  font-size: 36rpx;
  font-weight: bold;
  color: #333333;
  margin-bottom: 8rpx;
}

.stat-label {
  font-size: 22rpx;
  color: #666666;
}

.machine-section {
  padding: 0 30rpx;
}

.machine-list {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.machine-card {
  background: #ffffff;
  border-radius: 16rpx;
  padding: 30rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
  
  &:active {
    transform: scale(0.98);
  }
}

.machine-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 20rpx;
}

.machine-info {
  display: flex;
  flex-direction: column;
}

.machine-name {
  font-size: 32rpx;
  font-weight: bold;
  color: #333333;
  margin-bottom: 8rpx;
}

.machine-location {
  font-size: 24rpx;
  color: #666666;
}

.status-badge {
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  font-size: 22rpx;
}

.status-text {
  font-weight: 500;
}

.machine-metrics {
  display: flex;
  justify-content: space-between;
  margin-bottom: 20rpx;
}

.metric-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  flex: 1;
}

.metric-label {
  font-size: 22rpx;
  color: #666666;
  margin-bottom: 8rpx;
}

.metric-value {
  font-size: 28rpx;
  font-weight: bold;
  color: #333333;
}

.machine-footer {
  padding: 20rpx;
  background: #f8f8f8;
  border-radius: 12rpx;
  margin-bottom: 20rpx;
}

.error-desc {
  font-size: 24rpx;
  color: #666666;
  line-height: 1.4;
}

.machine-actions {
  display: flex;
  justify-content: flex-end;
}

.repair-btn {
  background: linear-gradient(135deg, #4CAF50 0%, #388E3C 100%);
  color: #ffffff;
  border: none;
  border-radius: 20rpx;
  padding: 16rpx 32rpx;
  font-size: 24rpx;
  font-weight: 500;
  
  &:active {
    transform: scale(0.95);
  }
}
</style>
