# Issue #7573
# cmd/cgo: undefined reference when linking a C-library using gccgo

[!cgo] skip
[!exec:gccgo] skip

go build -n -compiler gccgo
stderr 'gccgo.*\-L [^ ]*alibpath \-lalib' # make sure that Go-inline "#cgo LDFLAGS:" ("-L alibpath -lalib") passed to gccgo linking stage

-- go.mod --
module m
-- cgoref.go --
package main
// #cgo LDFLAGS: -L alibpath -lalib
// void f(void) {}
import "C"

func main() { C.f() }
