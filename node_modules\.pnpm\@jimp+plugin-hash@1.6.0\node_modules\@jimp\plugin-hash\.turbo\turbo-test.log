

> @jimp/plugin-hash@1.1.2 test /Users/<USER>/Documents/jimp/plugins/plugin-hash
> vitest "--watch=false" "-u"


[7m[1m[36m RUN [39m[22m[27m [36mv2.0.5[39m [90m/Users/<USER>/Documents/jimp/plugins/plugin-hash[39m

[?25l [90m·[39m [2msrc/[22mindex[2m.test.ts[22m[2m (8)[22m
   [90m·[39m hash[2m (6)[22m
     [90m·[39m base 2
     [90m·[39m base 10 (decimal)
     [90m·[39m base 16 (hex)
     [90m·[39m base 64
     [90m·[39m base 23
     [90m·[39m base 17
   [90m·[39m pHash[2m (2)[22m
     [90m·[39m should calculate the distance using distanceFromHash
     [90m·[39m should calculate the distance using compareHashes.
[?25l[?25l[?25l[?25l[?25l[?25l[?25l[?25l[?25l[?25l[?25l[?25l[?25l[?25l[?25l[?25l[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.test.ts[22m[2m (8)[22m
   [33m❯[39m hash[2m (6)[22m
     [32m✓[39m base 2[33m 314[2mms[22m[39m
     [33m⠙[39m base 10 (decimal)
     [90m·[39m base 16 (hex)
     [90m·[39m base 64
     [90m·[39m base 23
     [90m·[39m base 17
   [90m·[39m pHash[2m (2)[22m
     [90m·[39m should calculate the distance using distanceFromHash
     [90m·[39m should calculate the distance using compareHashes.
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.test.ts[22m[2m (8)[22m
   [33m❯[39m hash[2m (6)[22m
     [32m✓[39m base 2[33m 314[2mms[22m[39m
     [33m⠹[39m base 10 (decimal)
     [90m·[39m base 16 (hex)
     [90m·[39m base 64
     [90m·[39m base 23
     [90m·[39m base 17
   [90m·[39m pHash[2m (2)[22m
     [90m·[39m should calculate the distance using distanceFromHash
     [90m·[39m should calculate the distance using compareHashes.
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.test.ts[22m[2m (8)[22m
   [33m❯[39m hash[2m (6)[22m
     [32m✓[39m base 2[33m 314[2mms[22m[39m
     [33m⠸[39m base 10 (decimal)
     [90m·[39m base 16 (hex)
     [90m·[39m base 64
     [90m·[39m base 23
     [90m·[39m base 17
   [90m·[39m pHash[2m (2)[22m
     [90m·[39m should calculate the distance using distanceFromHash
     [90m·[39m should calculate the distance using compareHashes.
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.test.ts[22m[2m (8)[22m
   [33m❯[39m hash[2m (6)[22m
     [32m✓[39m base 2[33m 314[2mms[22m[39m
     [33m⠼[39m base 10 (decimal)
     [90m·[39m base 16 (hex)
     [90m·[39m base 64
     [90m·[39m base 23
     [90m·[39m base 17
   [90m·[39m pHash[2m (2)[22m
     [90m·[39m should calculate the distance using distanceFromHash
     [90m·[39m should calculate the distance using compareHashes.
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.test.ts[22m[2m (8)[22m
   [33m❯[39m hash[2m (6)[22m
     [32m✓[39m base 2[33m 314[2mms[22m[39m
     [33m⠴[39m base 10 (decimal)
     [90m·[39m base 16 (hex)
     [90m·[39m base 64
     [90m·[39m base 23
     [90m·[39m base 17
   [90m·[39m pHash[2m (2)[22m
     [90m·[39m should calculate the distance using distanceFromHash
     [90m·[39m should calculate the distance using compareHashes.
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.test.ts[22m[2m (8)[22m
   [33m❯[39m hash[2m (6)[22m
     [32m✓[39m base 2[33m 314[2mms[22m[39m
     [33m⠦[39m base 10 (decimal)
     [90m·[39m base 16 (hex)
     [90m·[39m base 64
     [90m·[39m base 23
     [90m·[39m base 17
   [90m·[39m pHash[2m (2)[22m
     [90m·[39m should calculate the distance using distanceFromHash
     [90m·[39m should calculate the distance using compareHashes.
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.test.ts[22m[2m (8)[22m
   [33m❯[39m hash[2m (6)[22m
     [32m✓[39m base 2[33m 314[2mms[22m[39m
     [33m⠧[39m base 10 (decimal)
     [90m·[39m base 16 (hex)
     [90m·[39m base 64
     [90m·[39m base 23
     [90m·[39m base 17
   [90m·[39m pHash[2m (2)[22m
     [90m·[39m should calculate the distance using distanceFromHash
     [90m·[39m should calculate the distance using compareHashes.
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.test.ts[22m[2m (8)[22m
   [33m❯[39m hash[2m (6)[22m
     [32m✓[39m base 2[33m 314[2mms[22m[39m
     [33m⠇[39m base 10 (decimal)
     [90m·[39m base 16 (hex)
     [90m·[39m base 64
     [90m·[39m base 23
     [90m·[39m base 17
   [90m·[39m pHash[2m (2)[22m
     [90m·[39m should calculate the distance using distanceFromHash
     [90m·[39m should calculate the distance using compareHashes.
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.test.ts[22m[2m (8)[22m
   [33m❯[39m hash[2m (6)[22m
     [32m✓[39m base 2[33m 314[2mms[22m[39m
     [33m⠏[39m base 10 (decimal)
     [90m·[39m base 16 (hex)
     [90m·[39m base 64
     [90m·[39m base 23
     [90m·[39m base 17
   [90m·[39m pHash[2m (2)[22m
     [90m·[39m should calculate the distance using distanceFromHash
     [90m·[39m should calculate the distance using compareHashes.
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.test.ts[22m[2m (8)[22m
   [33m❯[39m hash[2m (6)[22m
     [32m✓[39m base 2[33m 314[2mms[22m[39m
     [33m⠋[39m base 10 (decimal)
     [90m·[39m base 16 (hex)
     [90m·[39m base 64
     [90m·[39m base 23
     [90m·[39m base 17
   [90m·[39m pHash[2m (2)[22m
     [90m·[39m should calculate the distance using distanceFromHash
     [90m·[39m should calculate the distance using compareHashes.
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.test.ts[22m[2m (8)[22m
   [33m❯[39m hash[2m (6)[22m
     [32m✓[39m base 2[33m 314[2mms[22m[39m
     [33m⠙[39m base 10 (decimal)
     [90m·[39m base 16 (hex)
     [90m·[39m base 64
     [90m·[39m base 23
     [90m·[39m base 17
   [90m·[39m pHash[2m (2)[22m
     [90m·[39m should calculate the distance using distanceFromHash
     [90m·[39m should calculate the distance using compareHashes.
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.test.ts[22m[2m (8)[22m
   [33m❯[39m hash[2m (6)[22m
     [32m✓[39m base 2[33m 314[2mms[22m[39m
     [33m⠹[39m base 10 (decimal)
     [90m·[39m base 16 (hex)
     [90m·[39m base 64
     [90m·[39m base 23
     [90m·[39m base 17
   [90m·[39m pHash[2m (2)[22m
     [90m·[39m should calculate the distance using distanceFromHash
     [90m·[39m should calculate the distance using compareHashes.
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.test.ts[22m[2m (8)[22m
   [33m❯[39m hash[2m (6)[22m
     [32m✓[39m base 2[33m 314[2mms[22m[39m
     [33m⠸[39m base 10 (decimal)
     [90m·[39m base 16 (hex)
     [90m·[39m base 64
     [90m·[39m base 23
     [90m·[39m base 17
   [90m·[39m pHash[2m (2)[22m
     [90m·[39m should calculate the distance using distanceFromHash
     [90m·[39m should calculate the distance using compareHashes.
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.test.ts[22m[2m (8)[22m
   [33m❯[39m hash[2m (6)[22m
     [32m✓[39m base 2[33m 314[2mms[22m[39m
     [33m⠼[39m base 10 (decimal)
     [90m·[39m base 16 (hex)
     [90m·[39m base 64
     [90m·[39m base 23
     [90m·[39m base 17
   [90m·[39m pHash[2m (2)[22m
     [90m·[39m should calculate the distance using distanceFromHash
     [90m·[39m should calculate the distance using compareHashes.
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.test.ts[22m[2m (8)[22m
   [33m❯[39m hash[2m (6)[22m
     [32m✓[39m base 2[33m 314[2mms[22m[39m
     [33m⠴[39m base 10 (decimal)
     [90m·[39m base 16 (hex)
     [90m·[39m base 64
     [90m·[39m base 23
     [90m·[39m base 17
   [90m·[39m pHash[2m (2)[22m
     [90m·[39m should calculate the distance using distanceFromHash
     [90m·[39m should calculate the distance using compareHashes.
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.test.ts[22m[2m (8)[22m
   [33m❯[39m hash[2m (6)[22m
     [32m✓[39m base 2[33m 314[2mms[22m[39m
     [32m✓[39m base 10 (decimal)
     [33m⠙[39m base 16 (hex)
     [90m·[39m base 64
     [90m·[39m base 23
     [90m·[39m base 17
   [90m·[39m pHash[2m (2)[22m
     [90m·[39m should calculate the distance using distanceFromHash
     [90m·[39m should calculate the distance using compareHashes.
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.test.ts[22m[2m (8)[22m
   [33m❯[39m hash[2m (6)[22m
     [32m✓[39m base 2[33m 314[2mms[22m[39m
     [32m✓[39m base 10 (decimal)
     [33m⠹[39m base 16 (hex)
     [90m·[39m base 64
     [90m·[39m base 23
     [90m·[39m base 17
   [90m·[39m pHash[2m (2)[22m
     [90m·[39m should calculate the distance using distanceFromHash
     [90m·[39m should calculate the distance using compareHashes.
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.test.ts[22m[2m (8)[22m
   [33m❯[39m hash[2m (6)[22m
     [32m✓[39m base 2[33m 314[2mms[22m[39m
     [32m✓[39m base 10 (decimal)
     [33m⠸[39m base 16 (hex)
     [90m·[39m base 64
     [90m·[39m base 23
     [90m·[39m base 17
   [90m·[39m pHash[2m (2)[22m
     [90m·[39m should calculate the distance using distanceFromHash
     [90m·[39m should calculate the distance using compareHashes.
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.test.ts[22m[2m (8)[22m
   [33m❯[39m hash[2m (6)[22m
     [32m✓[39m base 2[33m 314[2mms[22m[39m
     [32m✓[39m base 10 (decimal)
     [33m⠼[39m base 16 (hex)
     [90m·[39m base 64
     [90m·[39m base 23
     [90m·[39m base 17
   [90m·[39m pHash[2m (2)[22m
     [90m·[39m should calculate the distance using distanceFromHash
     [90m·[39m should calculate the distance using compareHashes.
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.test.ts[22m[2m (8)[22m
   [33m❯[39m hash[2m (6)[22m
     [32m✓[39m base 2[33m 314[2mms[22m[39m
     [32m✓[39m base 10 (decimal)
     [33m⠴[39m base 16 (hex)
     [90m·[39m base 64
     [90m·[39m base 23
     [90m·[39m base 17
   [90m·[39m pHash[2m (2)[22m
     [90m·[39m should calculate the distance using distanceFromHash
     [90m·[39m should calculate the distance using compareHashes.
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.test.ts[22m[2m (8)[22m
   [33m❯[39m hash[2m (6)[22m
     [32m✓[39m base 2[33m 314[2mms[22m[39m
     [32m✓[39m base 10 (decimal)
     [33m⠦[39m base 16 (hex)
     [90m·[39m base 64
     [90m·[39m base 23
     [90m·[39m base 17
   [90m·[39m pHash[2m (2)[22m
     [90m·[39m should calculate the distance using distanceFromHash
     [90m·[39m should calculate the distance using compareHashes.
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.test.ts[22m[2m (8)[22m
   [33m❯[39m hash[2m (6)[22m
     [32m✓[39m base 2[33m 314[2mms[22m[39m
     [32m✓[39m base 10 (decimal)
     [33m⠧[39m base 16 (hex)
     [90m·[39m base 64
     [90m·[39m base 23
     [90m·[39m base 17
   [90m·[39m pHash[2m (2)[22m
     [90m·[39m should calculate the distance using distanceFromHash
     [90m·[39m should calculate the distance using compareHashes.
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.test.ts[22m[2m (8)[22m
   [33m❯[39m hash[2m (6)[22m
     [32m✓[39m base 2[33m 314[2mms[22m[39m
     [32m✓[39m base 10 (decimal)
     [33m⠇[39m base 16 (hex)
     [90m·[39m base 64
     [90m·[39m base 23
     [90m·[39m base 17
   [90m·[39m pHash[2m (2)[22m
     [90m·[39m should calculate the distance using distanceFromHash
     [90m·[39m should calculate the distance using compareHashes.
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.test.ts[22m[2m (8)[22m
   [33m❯[39m hash[2m (6)[22m
     [32m✓[39m base 2[33m 314[2mms[22m[39m
     [32m✓[39m base 10 (decimal)
     [33m⠏[39m base 16 (hex)
     [90m·[39m base 64
     [90m·[39m base 23
     [90m·[39m base 17
   [90m·[39m pHash[2m (2)[22m
     [90m·[39m should calculate the distance using distanceFromHash
     [90m·[39m should calculate the distance using compareHashes.
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.test.ts[22m[2m (8)[22m
   [33m❯[39m hash[2m (6)[22m
     [32m✓[39m base 2[33m 314[2mms[22m[39m
     [32m✓[39m base 10 (decimal)
     [33m⠋[39m base 16 (hex)
     [90m·[39m base 64
     [90m·[39m base 23
     [90m·[39m base 17
   [90m·[39m pHash[2m (2)[22m
     [90m·[39m should calculate the distance using distanceFromHash
     [90m·[39m should calculate the distance using compareHashes.
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.test.ts[22m[2m (8)[22m
   [33m❯[39m hash[2m (6)[22m
     [32m✓[39m base 2[33m 314[2mms[22m[39m
     [32m✓[39m base 10 (decimal)
     [33m⠙[39m base 16 (hex)
     [90m·[39m base 64
     [90m·[39m base 23
     [90m·[39m base 17
   [90m·[39m pHash[2m (2)[22m
     [90m·[39m should calculate the distance using distanceFromHash
     [90m·[39m should calculate the distance using compareHashes.
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.test.ts[22m[2m (8)[22m
   [33m❯[39m hash[2m (6)[22m
     [32m✓[39m base 2[33m 314[2mms[22m[39m
     [32m✓[39m base 10 (decimal)
     [33m⠹[39m base 16 (hex)
     [90m·[39m base 64
     [90m·[39m base 23
     [90m·[39m base 17
   [90m·[39m pHash[2m (2)[22m
     [90m·[39m should calculate the distance using distanceFromHash
     [90m·[39m should calculate the distance using compareHashes.
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.test.ts[22m[2m (8)[22m
   [33m❯[39m hash[2m (6)[22m
     [32m✓[39m base 2[33m 314[2mms[22m[39m
     [32m✓[39m base 10 (decimal)
     [33m⠸[39m base 16 (hex)
     [90m·[39m base 64
     [90m·[39m base 23
     [90m·[39m base 17
   [90m·[39m pHash[2m (2)[22m
     [90m·[39m should calculate the distance using distanceFromHash
     [90m·[39m should calculate the distance using compareHashes.
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.test.ts[22m[2m (8)[22m
   [33m❯[39m hash[2m (6)[22m
     [32m✓[39m base 2[33m 314[2mms[22m[39m
     [32m✓[39m base 10 (decimal)
     [33m⠼[39m base 16 (hex)
     [90m·[39m base 64
     [90m·[39m base 23
     [90m·[39m base 17
   [90m·[39m pHash[2m (2)[22m
     [90m·[39m should calculate the distance using distanceFromHash
     [90m·[39m should calculate the distance using compareHashes.
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.test.ts[22m[2m (8)[22m
   [33m❯[39m hash[2m (6)[22m
     [32m✓[39m base 2[33m 314[2mms[22m[39m
     [32m✓[39m base 10 (decimal)
     [33m⠴[39m base 16 (hex)
     [90m·[39m base 64
     [90m·[39m base 23
     [90m·[39m base 17
   [90m·[39m pHash[2m (2)[22m
     [90m·[39m should calculate the distance using distanceFromHash
     [90m·[39m should calculate the distance using compareHashes.
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.test.ts[22m[2m (8)[22m
   [33m❯[39m hash[2m (6)[22m
     [32m✓[39m base 2[33m 314[2mms[22m[39m
     [32m✓[39m base 10 (decimal)
     [33m⠦[39m base 16 (hex)
     [90m·[39m base 64
     [90m·[39m base 23
     [90m·[39m base 17
   [90m·[39m pHash[2m (2)[22m
     [90m·[39m should calculate the distance using distanceFromHash
     [90m·[39m should calculate the distance using compareHashes.
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.test.ts[22m[2m (8)[22m
   [33m❯[39m hash[2m (6)[22m
     [32m✓[39m base 2[33m 314[2mms[22m[39m
     [32m✓[39m base 10 (decimal)
     [33m⠧[39m base 16 (hex)
     [90m·[39m base 64
     [90m·[39m base 23
     [90m·[39m base 17
   [90m·[39m pHash[2m (2)[22m
     [90m·[39m should calculate the distance using distanceFromHash
     [90m·[39m should calculate the distance using compareHashes.
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.test.ts[22m[2m (8)[22m
   [33m❯[39m hash[2m (6)[22m
     [32m✓[39m base 2[33m 314[2mms[22m[39m
     [32m✓[39m base 10 (decimal)
     [33m⠇[39m base 16 (hex)
     [90m·[39m base 64
     [90m·[39m base 23
     [90m·[39m base 17
   [90m·[39m pHash[2m (2)[22m
     [90m·[39m should calculate the distance using distanceFromHash
     [90m·[39m should calculate the distance using compareHashes.
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.test.ts[22m[2m (8)[22m
   [33m❯[39m hash[2m (6)[22m
     [32m✓[39m base 2[33m 314[2mms[22m[39m
     [32m✓[39m base 10 (decimal)
     [33m⠏[39m base 16 (hex)
     [90m·[39m base 64
     [90m·[39m base 23
     [90m·[39m base 17
   [90m·[39m pHash[2m (2)[22m
     [90m·[39m should calculate the distance using distanceFromHash
     [90m·[39m should calculate the distance using compareHashes.
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.test.ts[22m[2m (8)[22m
   [33m❯[39m hash[2m (6)[22m
     [32m✓[39m base 2[33m 314[2mms[22m[39m
     [32m✓[39m base 10 (decimal)
     [33m⠋[39m base 16 (hex)
     [90m·[39m base 64
     [90m·[39m base 23
     [90m·[39m base 17
   [90m·[39m pHash[2m (2)[22m
     [90m·[39m should calculate the distance using distanceFromHash
     [90m·[39m should calculate the distance using compareHashes.
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.test.ts[22m[2m (8)[22m
   [33m❯[39m hash[2m (6)[22m
     [32m✓[39m base 2[33m 314[2mms[22m[39m
     [32m✓[39m base 10 (decimal)
     [33m⠙[39m base 16 (hex)
     [90m·[39m base 64
     [90m·[39m base 23
     [90m·[39m base 17
   [90m·[39m pHash[2m (2)[22m
     [90m·[39m should calculate the distance using distanceFromHash
     [90m·[39m should calculate the distance using compareHashes.
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.test.ts[22m[2m (8)[22m
   [33m❯[39m hash[2m (6)[22m
     [32m✓[39m base 2[33m 314[2mms[22m[39m
     [32m✓[39m base 10 (decimal)
     [33m⠹[39m base 16 (hex)
     [90m·[39m base 64
     [90m·[39m base 23
     [90m·[39m base 17
   [90m·[39m pHash[2m (2)[22m
     [90m·[39m should calculate the distance using distanceFromHash
     [90m·[39m should calculate the distance using compareHashes.
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.test.ts[22m[2m (8)[22m
   [33m❯[39m hash[2m (6)[22m
     [32m✓[39m base 2[33m 314[2mms[22m[39m
     [32m✓[39m base 10 (decimal)
     [33m⠸[39m base 16 (hex)
     [90m·[39m base 64
     [90m·[39m base 23
     [90m·[39m base 17
   [90m·[39m pHash[2m (2)[22m
     [90m·[39m should calculate the distance using distanceFromHash
     [90m·[39m should calculate the distance using compareHashes.
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.test.ts[22m[2m (8)[22m
   [33m❯[39m hash[2m (6)[22m
     [32m✓[39m base 2[33m 314[2mms[22m[39m
     [32m✓[39m base 10 (decimal)
     [33m⠼[39m base 16 (hex)
     [90m·[39m base 64
     [90m·[39m base 23
     [90m·[39m base 17
   [90m·[39m pHash[2m (2)[22m
     [90m·[39m should calculate the distance using distanceFromHash
     [90m·[39m should calculate the distance using compareHashes.
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.test.ts[22m[2m (8)[22m
   [33m❯[39m hash[2m (6)[22m
     [32m✓[39m base 2[33m 314[2mms[22m[39m
     [32m✓[39m base 10 (decimal)
     [32m✓[39m base 16 (hex)[33m 459[2mms[22m[39m
     [33m⠙[39m base 64
     [90m·[39m base 23
     [90m·[39m base 17
   [90m·[39m pHash[2m (2)[22m
     [90m·[39m should calculate the distance using distanceFromHash
     [90m·[39m should calculate the distance using compareHashes.
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.test.ts[22m[2m (8)[22m
   [33m❯[39m hash[2m (6)[22m
     [32m✓[39m base 2[33m 314[2mms[22m[39m
     [32m✓[39m base 10 (decimal)
     [32m✓[39m base 16 (hex)[33m 459[2mms[22m[39m
     [33m⠹[39m base 64
     [90m·[39m base 23
     [90m·[39m base 17
   [90m·[39m pHash[2m (2)[22m
     [90m·[39m should calculate the distance using distanceFromHash
     [90m·[39m should calculate the distance using compareHashes.
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.test.ts[22m[2m (8)[22m
   [33m❯[39m hash[2m (6)[22m
     [32m✓[39m base 2[33m 314[2mms[22m[39m
     [32m✓[39m base 10 (decimal)
     [32m✓[39m base 16 (hex)[33m 459[2mms[22m[39m
     [33m⠸[39m base 64
     [90m·[39m base 23
     [90m·[39m base 17
   [90m·[39m pHash[2m (2)[22m
     [90m·[39m should calculate the distance using distanceFromHash
     [90m·[39m should calculate the distance using compareHashes.
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.test.ts[22m[2m (8)[22m
   [33m❯[39m hash[2m (6)[22m
     [32m✓[39m base 2[33m 314[2mms[22m[39m
     [32m✓[39m base 10 (decimal)
     [32m✓[39m base 16 (hex)[33m 459[2mms[22m[39m
     [33m⠼[39m base 64
     [90m·[39m base 23
     [90m·[39m base 17
   [90m·[39m pHash[2m (2)[22m
     [90m·[39m should calculate the distance using distanceFromHash
     [90m·[39m should calculate the distance using compareHashes.
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.test.ts[22m[2m (8)[22m
   [33m❯[39m hash[2m (6)[22m
     [32m✓[39m base 2[33m 314[2mms[22m[39m
     [32m✓[39m base 10 (decimal)
     [32m✓[39m base 16 (hex)[33m 459[2mms[22m[39m
     [33m⠴[39m base 64
     [90m·[39m base 23
     [90m·[39m base 17
   [90m·[39m pHash[2m (2)[22m
     [90m·[39m should calculate the distance using distanceFromHash
     [90m·[39m should calculate the distance using compareHashes.
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.test.ts[22m[2m (8)[22m
   [33m❯[39m hash[2m (6)[22m
     [32m✓[39m base 2[33m 314[2mms[22m[39m
     [32m✓[39m base 10 (decimal)
     [32m✓[39m base 16 (hex)[33m 459[2mms[22m[39m
     [33m⠦[39m base 64
     [90m·[39m base 23
     [90m·[39m base 17
   [90m·[39m pHash[2m (2)[22m
     [90m·[39m should calculate the distance using distanceFromHash
     [90m·[39m should calculate the distance using compareHashes.
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.test.ts[22m[2m (8)[22m
   [33m❯[39m hash[2m (6)[22m
     [32m✓[39m base 2[33m 314[2mms[22m[39m
     [32m✓[39m base 10 (decimal)
     [32m✓[39m base 16 (hex)[33m 459[2mms[22m[39m
     [32m✓[39m base 64
     [33m⠙[39m base 23
     [90m·[39m base 17
   [90m·[39m pHash[2m (2)[22m
     [90m·[39m should calculate the distance using distanceFromHash
     [90m·[39m should calculate the distance using compareHashes.
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.test.ts[22m[2m (8)[22m
   [33m❯[39m hash[2m (6)[22m
     [32m✓[39m base 2[33m 314[2mms[22m[39m
     [32m✓[39m base 10 (decimal)
     [32m✓[39m base 16 (hex)[33m 459[2mms[22m[39m
     [32m✓[39m base 64
     [33m⠹[39m base 23
     [90m·[39m base 17
   [90m·[39m pHash[2m (2)[22m
     [90m·[39m should calculate the distance using distanceFromHash
     [90m·[39m should calculate the distance using compareHashes.
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.test.ts[22m[2m (8)[22m
   [33m❯[39m hash[2m (6)[22m
     [32m✓[39m base 2[33m 314[2mms[22m[39m
     [32m✓[39m base 10 (decimal)
     [32m✓[39m base 16 (hex)[33m 459[2mms[22m[39m
     [32m✓[39m base 64
     [33m⠸[39m base 23
     [90m·[39m base 17
   [90m·[39m pHash[2m (2)[22m
     [90m·[39m should calculate the distance using distanceFromHash
     [90m·[39m should calculate the distance using compareHashes.
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.test.ts[22m[2m (8)[22m
   [33m❯[39m hash[2m (6)[22m
     [32m✓[39m base 2[33m 314[2mms[22m[39m
     [32m✓[39m base 10 (decimal)
     [32m✓[39m base 16 (hex)[33m 459[2mms[22m[39m
     [32m✓[39m base 64
     [33m⠼[39m base 23
     [90m·[39m base 17
   [90m·[39m pHash[2m (2)[22m
     [90m·[39m should calculate the distance using distanceFromHash
     [90m·[39m should calculate the distance using compareHashes.
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.test.ts[22m[2m (8)[22m
   [33m❯[39m hash[2m (6)[22m
     [32m✓[39m base 2[33m 314[2mms[22m[39m
     [32m✓[39m base 10 (decimal)
     [32m✓[39m base 16 (hex)[33m 459[2mms[22m[39m
     [32m✓[39m base 64
     [33m⠴[39m base 23
     [90m·[39m base 17
   [90m·[39m pHash[2m (2)[22m
     [90m·[39m should calculate the distance using distanceFromHash
     [90m·[39m should calculate the distance using compareHashes.
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.test.ts[22m[2m (8)[22m
   [33m❯[39m hash[2m (6)[22m
     [32m✓[39m base 2[33m 314[2mms[22m[39m
     [32m✓[39m base 10 (decimal)
     [32m✓[39m base 16 (hex)[33m 459[2mms[22m[39m
     [32m✓[39m base 64
     [33m⠦[39m base 23
     [90m·[39m base 17
   [90m·[39m pHash[2m (2)[22m
     [90m·[39m should calculate the distance using distanceFromHash
     [90m·[39m should calculate the distance using compareHashes.
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.test.ts[22m[2m (8)[22m
   [33m❯[39m hash[2m (6)[22m
     [32m✓[39m base 2[33m 314[2mms[22m[39m
     [32m✓[39m base 10 (decimal)
     [32m✓[39m base 16 (hex)[33m 459[2mms[22m[39m
     [32m✓[39m base 64
     [33m⠧[39m base 23
     [90m·[39m base 17
   [90m·[39m pHash[2m (2)[22m
     [90m·[39m should calculate the distance using distanceFromHash
     [90m·[39m should calculate the distance using compareHashes.
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.test.ts[22m[2m (8)[22m
   [33m❯[39m hash[2m (6)[22m
     [32m✓[39m base 2[33m 314[2mms[22m[39m
     [32m✓[39m base 10 (decimal)
     [32m✓[39m base 16 (hex)[33m 459[2mms[22m[39m
     [32m✓[39m base 64
     [33m⠇[39m base 23
     [90m·[39m base 17
   [90m·[39m pHash[2m (2)[22m
     [90m·[39m should calculate the distance using distanceFromHash
     [90m·[39m should calculate the distance using compareHashes.
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.test.ts[22m[2m (8)[22m
   [33m❯[39m hash[2m (6)[22m
     [32m✓[39m base 2[33m 314[2mms[22m[39m
     [32m✓[39m base 10 (decimal)
     [32m✓[39m base 16 (hex)[33m 459[2mms[22m[39m
     [32m✓[39m base 64
     [33m⠏[39m base 23
     [90m·[39m base 17
   [90m·[39m pHash[2m (2)[22m
     [90m·[39m should calculate the distance using distanceFromHash
     [90m·[39m should calculate the distance using compareHashes.
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.test.ts[22m[2m (8)[22m
   [33m❯[39m hash[2m (6)[22m
     [32m✓[39m base 2[33m 314[2mms[22m[39m
     [32m✓[39m base 10 (decimal)
     [32m✓[39m base 16 (hex)[33m 459[2mms[22m[39m
     [32m✓[39m base 64
     [33m⠋[39m base 23
     [90m·[39m base 17
   [90m·[39m pHash[2m (2)[22m
     [90m·[39m should calculate the distance using distanceFromHash
     [90m·[39m should calculate the distance using compareHashes.
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.test.ts[22m[2m (8)[22m
   [33m❯[39m hash[2m (6)[22m
     [32m✓[39m base 2[33m 314[2mms[22m[39m
     [32m✓[39m base 10 (decimal)
     [32m✓[39m base 16 (hex)[33m 459[2mms[22m[39m
     [32m✓[39m base 64
     [33m⠙[39m base 23
     [90m·[39m base 17
   [90m·[39m pHash[2m (2)[22m
     [90m·[39m should calculate the distance using distanceFromHash
     [90m·[39m should calculate the distance using compareHashes.
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.test.ts[22m[2m (8)[22m
   [33m❯[39m hash[2m (6)[22m
     [32m✓[39m base 2[33m 314[2mms[22m[39m
     [32m✓[39m base 10 (decimal)
     [32m✓[39m base 16 (hex)[33m 459[2mms[22m[39m
     [32m✓[39m base 64
     [33m⠹[39m base 23
     [90m·[39m base 17
   [90m·[39m pHash[2m (2)[22m
     [90m·[39m should calculate the distance using distanceFromHash
     [90m·[39m should calculate the distance using compareHashes.
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.test.ts[22m[2m (8)[22m
   [33m❯[39m hash[2m (6)[22m
     [32m✓[39m base 2[33m 314[2mms[22m[39m
     [32m✓[39m base 10 (decimal)
     [32m✓[39m base 16 (hex)[33m 459[2mms[22m[39m
     [32m✓[39m base 64
     [33m⠸[39m base 23
     [90m·[39m base 17
   [90m·[39m pHash[2m (2)[22m
     [90m·[39m should calculate the distance using distanceFromHash
     [90m·[39m should calculate the distance using compareHashes.
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.test.ts[22m[2m (8)[22m
   [33m❯[39m hash[2m (6)[22m
     [32m✓[39m base 2[33m 314[2mms[22m[39m
     [32m✓[39m base 10 (decimal)
     [32m✓[39m base 16 (hex)[33m 459[2mms[22m[39m
     [32m✓[39m base 64
     [33m⠼[39m base 23
     [90m·[39m base 17
   [90m·[39m pHash[2m (2)[22m
     [90m·[39m should calculate the distance using distanceFromHash
     [90m·[39m should calculate the distance using compareHashes.
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.test.ts[22m[2m (8)[22m
   [33m❯[39m hash[2m (6)[22m
     [32m✓[39m base 2[33m 314[2mms[22m[39m
     [32m✓[39m base 10 (decimal)
     [32m✓[39m base 16 (hex)[33m 459[2mms[22m[39m
     [32m✓[39m base 64
     [33m⠴[39m base 23
     [90m·[39m base 17
   [90m·[39m pHash[2m (2)[22m
     [90m·[39m should calculate the distance using distanceFromHash
     [90m·[39m should calculate the distance using compareHashes.
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.test.ts[22m[2m (8)[22m
   [33m❯[39m hash[2m (6)[22m
     [32m✓[39m base 2[33m 314[2mms[22m[39m
     [32m✓[39m base 10 (decimal)
     [32m✓[39m base 16 (hex)[33m 459[2mms[22m[39m
     [32m✓[39m base 64
     [33m⠦[39m base 23
     [90m·[39m base 17
   [90m·[39m pHash[2m (2)[22m
     [90m·[39m should calculate the distance using distanceFromHash
     [90m·[39m should calculate the distance using compareHashes.
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.test.ts[22m[2m (8)[22m
   [33m❯[39m hash[2m (6)[22m
     [32m✓[39m base 2[33m 314[2mms[22m[39m
     [32m✓[39m base 10 (decimal)
     [32m✓[39m base 16 (hex)[33m 459[2mms[22m[39m
     [32m✓[39m base 64
     [33m⠧[39m base 23
     [90m·[39m base 17
   [90m·[39m pHash[2m (2)[22m
     [90m·[39m should calculate the distance using distanceFromHash
     [90m·[39m should calculate the distance using compareHashes.
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.test.ts[22m[2m (8)[22m
   [33m❯[39m hash[2m (6)[22m
     [32m✓[39m base 2[33m 314[2mms[22m[39m
     [32m✓[39m base 10 (decimal)
     [32m✓[39m base 16 (hex)[33m 459[2mms[22m[39m
     [32m✓[39m base 64
     [33m⠇[39m base 23
     [90m·[39m base 17
   [90m·[39m pHash[2m (2)[22m
     [90m·[39m should calculate the distance using distanceFromHash
     [90m·[39m should calculate the distance using compareHashes.
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.test.ts[22m[2m (8)[22m
   [33m❯[39m hash[2m (6)[22m
     [32m✓[39m base 2[33m 314[2mms[22m[39m
     [32m✓[39m base 10 (decimal)
     [32m✓[39m base 16 (hex)[33m 459[2mms[22m[39m
     [32m✓[39m base 64
     [33m⠏[39m base 23
     [90m·[39m base 17
   [90m·[39m pHash[2m (2)[22m
     [90m·[39m should calculate the distance using distanceFromHash
     [90m·[39m should calculate the distance using compareHashes.
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.test.ts[22m[2m (8)[22m
   [33m❯[39m hash[2m (6)[22m
     [32m✓[39m base 2[33m 314[2mms[22m[39m
     [32m✓[39m base 10 (decimal)
     [32m✓[39m base 16 (hex)[33m 459[2mms[22m[39m
     [32m✓[39m base 64
     [33m⠋[39m base 23
     [90m·[39m base 17
   [90m·[39m pHash[2m (2)[22m
     [90m·[39m should calculate the distance using distanceFromHash
     [90m·[39m should calculate the distance using compareHashes.
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.test.ts[22m[2m (8)[22m
   [33m❯[39m hash[2m (6)[22m
     [32m✓[39m base 2[33m 314[2mms[22m[39m
     [32m✓[39m base 10 (decimal)
     [32m✓[39m base 16 (hex)[33m 459[2mms[22m[39m
     [32m✓[39m base 64
     [33m⠙[39m base 23
     [90m·[39m base 17
   [90m·[39m pHash[2m (2)[22m
     [90m·[39m should calculate the distance using distanceFromHash
     [90m·[39m should calculate the distance using compareHashes.
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.test.ts[22m[2m (8)[22m
   [33m❯[39m hash[2m (6)[22m
     [32m✓[39m base 2[33m 314[2mms[22m[39m
     [32m✓[39m base 10 (decimal)
     [32m✓[39m base 16 (hex)[33m 459[2mms[22m[39m
     [32m✓[39m base 64
     [33m⠹[39m base 23
     [90m·[39m base 17
   [90m·[39m pHash[2m (2)[22m
     [90m·[39m should calculate the distance using distanceFromHash
     [90m·[39m should calculate the distance using compareHashes.
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.test.ts[22m[2m (8)[22m
   [33m❯[39m hash[2m (6)[22m
     [32m✓[39m base 2[33m 314[2mms[22m[39m
     [32m✓[39m base 10 (decimal)
     [32m✓[39m base 16 (hex)[33m 459[2mms[22m[39m
     [32m✓[39m base 64
     [33m⠸[39m base 23
     [90m·[39m base 17
   [90m·[39m pHash[2m (2)[22m
     [90m·[39m should calculate the distance using distanceFromHash
     [90m·[39m should calculate the distance using compareHashes.
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.test.ts[22m[2m (8)[22m
   [33m❯[39m hash[2m (6)[22m
     [32m✓[39m base 2[33m 314[2mms[22m[39m
     [32m✓[39m base 10 (decimal)
     [32m✓[39m base 16 (hex)[33m 459[2mms[22m[39m
     [32m✓[39m base 64
     [33m⠼[39m base 23
     [90m·[39m base 17
   [90m·[39m pHash[2m (2)[22m
     [90m·[39m should calculate the distance using distanceFromHash
     [90m·[39m should calculate the distance using compareHashes.
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.test.ts[22m[2m (8)[22m
   [33m❯[39m hash[2m (6)[22m
     [32m✓[39m base 2[33m 314[2mms[22m[39m
     [32m✓[39m base 10 (decimal)
     [32m✓[39m base 16 (hex)[33m 459[2mms[22m[39m
     [32m✓[39m base 64
     [33m⠴[39m base 23
     [90m·[39m base 17
   [90m·[39m pHash[2m (2)[22m
     [90m·[39m should calculate the distance using distanceFromHash
     [90m·[39m should calculate the distance using compareHashes.
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.test.ts[22m[2m (8)[22m
   [33m❯[39m hash[2m (6)[22m
     [32m✓[39m base 2[33m 314[2mms[22m[39m
     [32m✓[39m base 10 (decimal)
     [32m✓[39m base 16 (hex)[33m 459[2mms[22m[39m
     [32m✓[39m base 64
     [33m⠦[39m base 23
     [90m·[39m base 17
   [90m·[39m pHash[2m (2)[22m
     [90m·[39m should calculate the distance using distanceFromHash
     [90m·[39m should calculate the distance using compareHashes.
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.test.ts[22m[2m (8)[22m
   [33m❯[39m hash[2m (6)[22m
     [32m✓[39m base 2[33m 314[2mms[22m[39m
     [32m✓[39m base 10 (decimal)
     [32m✓[39m base 16 (hex)[33m 459[2mms[22m[39m
     [32m✓[39m base 64
     [33m⠧[39m base 23
     [90m·[39m base 17
   [90m·[39m pHash[2m (2)[22m
     [90m·[39m should calculate the distance using distanceFromHash
     [90m·[39m should calculate the distance using compareHashes.
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.test.ts[22m[2m (8)[22m
   [33m❯[39m hash[2m (6)[22m
     [32m✓[39m base 2[33m 314[2mms[22m[39m
     [32m✓[39m base 10 (decimal)
     [32m✓[39m base 16 (hex)[33m 459[2mms[22m[39m
     [32m✓[39m base 64
     [33m⠇[39m base 23
     [90m·[39m base 17
   [90m·[39m pHash[2m (2)[22m
     [90m·[39m should calculate the distance using distanceFromHash
     [90m·[39m should calculate the distance using compareHashes.
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.test.ts[22m[2m (8)[22m
   [33m❯[39m hash[2m (6)[22m
     [32m✓[39m base 2[33m 314[2mms[22m[39m
     [32m✓[39m base 10 (decimal)
     [32m✓[39m base 16 (hex)[33m 459[2mms[22m[39m
     [32m✓[39m base 64
     [33m⠏[39m base 23
     [90m·[39m base 17
   [90m·[39m pHash[2m (2)[22m
     [90m·[39m should calculate the distance using distanceFromHash
     [90m·[39m should calculate the distance using compareHashes.
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.test.ts[22m[2m (8)[22m
   [33m❯[39m hash[2m (6)[22m
     [32m✓[39m base 2[33m 314[2mms[22m[39m
     [32m✓[39m base 10 (decimal)
     [32m✓[39m base 16 (hex)[33m 459[2mms[22m[39m
     [32m✓[39m base 64
     [33m⠋[39m base 23
     [90m·[39m base 17
   [90m·[39m pHash[2m (2)[22m
     [90m·[39m should calculate the distance using distanceFromHash
     [90m·[39m should calculate the distance using compareHashes.
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.test.ts[22m[2m (8)[22m
   [33m❯[39m hash[2m (6)[22m
     [32m✓[39m base 2[33m 314[2mms[22m[39m
     [32m✓[39m base 10 (decimal)
     [32m✓[39m base 16 (hex)[33m 459[2mms[22m[39m
     [32m✓[39m base 64
     [33m⠙[39m base 23
     [90m·[39m base 17
   [90m·[39m pHash[2m (2)[22m
     [90m·[39m should calculate the distance using distanceFromHash
     [90m·[39m should calculate the distance using compareHashes.
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.test.ts[22m[2m (8)[22m
   [33m❯[39m hash[2m (6)[22m
     [32m✓[39m base 2[33m 314[2mms[22m[39m
     [32m✓[39m base 10 (decimal)
     [32m✓[39m base 16 (hex)[33m 459[2mms[22m[39m
     [32m✓[39m base 64
     [33m⠹[39m base 23
     [90m·[39m base 17
   [90m·[39m pHash[2m (2)[22m
     [90m·[39m should calculate the distance using distanceFromHash
     [90m·[39m should calculate the distance using compareHashes.
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.test.ts[22m[2m (8)[22m
   [33m❯[39m hash[2m (6)[22m
     [32m✓[39m base 2[33m 314[2mms[22m[39m
     [32m✓[39m base 10 (decimal)
     [32m✓[39m base 16 (hex)[33m 459[2mms[22m[39m
     [32m✓[39m base 64
     [33m⠸[39m base 23
     [90m·[39m base 17
   [90m·[39m pHash[2m (2)[22m
     [90m·[39m should calculate the distance using distanceFromHash
     [90m·[39m should calculate the distance using compareHashes.
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.test.ts[22m[2m (8)[22m
   [33m❯[39m hash[2m (6)[22m
     [32m✓[39m base 2[33m 314[2mms[22m[39m
     [32m✓[39m base 10 (decimal)
     [32m✓[39m base 16 (hex)[33m 459[2mms[22m[39m
     [32m✓[39m base 64
     [33m⠼[39m base 23
     [90m·[39m base 17
   [90m·[39m pHash[2m (2)[22m
     [90m·[39m should calculate the distance using distanceFromHash
     [90m·[39m should calculate the distance using compareHashes.
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.test.ts[22m[2m (8)[22m
   [33m❯[39m hash[2m (6)[22m
     [32m✓[39m base 2[33m 314[2mms[22m[39m
     [32m✓[39m base 10 (decimal)
     [32m✓[39m base 16 (hex)[33m 459[2mms[22m[39m
     [32m✓[39m base 64
     [33m⠴[39m base 23
     [90m·[39m base 17
   [90m·[39m pHash[2m (2)[22m
     [90m·[39m should calculate the distance using distanceFromHash
     [90m·[39m should calculate the distance using compareHashes.
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.test.ts[22m[2m (8)[22m
   [33m❯[39m hash[2m (6)[22m
     [32m✓[39m base 2[33m 314[2mms[22m[39m
     [32m✓[39m base 10 (decimal)
     [32m✓[39m base 16 (hex)[33m 459[2mms[22m[39m
     [32m✓[39m base 64
     [33m⠦[39m base 23
     [90m·[39m base 17
   [90m·[39m pHash[2m (2)[22m
     [90m·[39m should calculate the distance using distanceFromHash
     [90m·[39m should calculate the distance using compareHashes.
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.test.ts[22m[2m (8)[22m
   [33m❯[39m hash[2m (6)[22m
     [32m✓[39m base 2[33m 314[2mms[22m[39m
     [32m✓[39m base 10 (decimal)
     [32m✓[39m base 16 (hex)[33m 459[2mms[22m[39m
     [32m✓[39m base 64
     [33m⠧[39m base 23
     [90m·[39m base 17
   [90m·[39m pHash[2m (2)[22m
     [90m·[39m should calculate the distance using distanceFromHash
     [90m·[39m should calculate the distance using compareHashes.
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.test.ts[22m[2m (8)[22m
   [33m❯[39m hash[2m (6)[22m
     [32m✓[39m base 2[33m 314[2mms[22m[39m
     [32m✓[39m base 10 (decimal)
     [32m✓[39m base 16 (hex)[33m 459[2mms[22m[39m
     [32m✓[39m base 64
     [33m⠇[39m base 23
     [90m·[39m base 17
   [90m·[39m pHash[2m (2)[22m
     [90m·[39m should calculate the distance using distanceFromHash
     [90m·[39m should calculate the distance using compareHashes.
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.test.ts[22m[2m (8)[22m
   [33m❯[39m hash[2m (6)[22m
     [32m✓[39m base 2[33m 314[2mms[22m[39m
     [32m✓[39m base 10 (decimal)
     [32m✓[39m base 16 (hex)[33m 459[2mms[22m[39m
     [32m✓[39m base 64
     [33m⠏[39m base 23
     [90m·[39m base 17
   [90m·[39m pHash[2m (2)[22m
     [90m·[39m should calculate the distance using distanceFromHash
     [90m·[39m should calculate the distance using compareHashes.
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.test.ts[22m[2m (8)[22m
   [33m❯[39m hash[2m (6)[22m
     [32m✓[39m base 2[33m 314[2mms[22m[39m
     [32m✓[39m base 10 (decimal)
     [32m✓[39m base 16 (hex)[33m 459[2mms[22m[39m
     [32m✓[39m base 64
     [33m⠋[39m base 23
     [90m·[39m base 17
   [90m·[39m pHash[2m (2)[22m
     [90m·[39m should calculate the distance using distanceFromHash
     [90m·[39m should calculate the distance using compareHashes.
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.test.ts[22m[2m (8)[22m
   [33m❯[39m hash[2m (6)[22m
     [32m✓[39m base 2[33m 314[2mms[22m[39m
     [32m✓[39m base 10 (decimal)
     [32m✓[39m base 16 (hex)[33m 459[2mms[22m[39m
     [32m✓[39m base 64
     [33m⠙[39m base 23
     [90m·[39m base 17
   [90m·[39m pHash[2m (2)[22m
     [90m·[39m should calculate the distance using distanceFromHash
     [90m·[39m should calculate the distance using compareHashes.
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.test.ts[22m[2m (8)[22m
   [33m❯[39m hash[2m (6)[22m
     [32m✓[39m base 2[33m 314[2mms[22m[39m
     [32m✓[39m base 10 (decimal)
     [32m✓[39m base 16 (hex)[33m 459[2mms[22m[39m
     [32m✓[39m base 64
     [33m⠹[39m base 23
     [90m·[39m base 17
   [90m·[39m pHash[2m (2)[22m
     [90m·[39m should calculate the distance using distanceFromHash
     [90m·[39m should calculate the distance using compareHashes.
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.test.ts[22m[2m (8)[22m
   [33m❯[39m hash[2m (6)[22m
     [32m✓[39m base 2[33m 314[2mms[22m[39m
     [32m✓[39m base 10 (decimal)
     [32m✓[39m base 16 (hex)[33m 459[2mms[22m[39m
     [32m✓[39m base 64
     [33m⠸[39m base 23
     [90m·[39m base 17
   [90m·[39m pHash[2m (2)[22m
     [90m·[39m should calculate the distance using distanceFromHash
     [90m·[39m should calculate the distance using compareHashes.
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.test.ts[22m[2m (8)[22m
   [33m❯[39m hash[2m (6)[22m
     [32m✓[39m base 2[33m 314[2mms[22m[39m
     [32m✓[39m base 10 (decimal)
     [32m✓[39m base 16 (hex)[33m 459[2mms[22m[39m
     [32m✓[39m base 64
     [33m⠼[39m base 23
     [90m·[39m base 17
   [90m·[39m pHash[2m (2)[22m
     [90m·[39m should calculate the distance using distanceFromHash
     [90m·[39m should calculate the distance using compareHashes.
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.test.ts[22m[2m (8)[22m
   [33m❯[39m hash[2m (6)[22m
     [32m✓[39m base 2[33m 314[2mms[22m[39m
     [32m✓[39m base 10 (decimal)
     [32m✓[39m base 16 (hex)[33m 459[2mms[22m[39m
     [32m✓[39m base 64
     [33m⠴[39m base 23
     [90m·[39m base 17
   [90m·[39m pHash[2m (2)[22m
     [90m·[39m should calculate the distance using distanceFromHash
     [90m·[39m should calculate the distance using compareHashes.
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.test.ts[22m[2m (8)[22m
   [33m❯[39m hash[2m (6)[22m
     [32m✓[39m base 2[33m 314[2mms[22m[39m
     [32m✓[39m base 10 (decimal)
     [32m✓[39m base 16 (hex)[33m 459[2mms[22m[39m
     [32m✓[39m base 64
     [33m⠦[39m base 23
     [90m·[39m base 17
   [90m·[39m pHash[2m (2)[22m
     [90m·[39m should calculate the distance using distanceFromHash
     [90m·[39m should calculate the distance using compareHashes.
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.test.ts[22m[2m (8)[22m
   [33m❯[39m hash[2m (6)[22m
     [32m✓[39m base 2[33m 314[2mms[22m[39m
     [32m✓[39m base 10 (decimal)
     [32m✓[39m base 16 (hex)[33m 459[2mms[22m[39m
     [32m✓[39m base 64
     [33m⠧[39m base 23
     [90m·[39m base 17
   [90m·[39m pHash[2m (2)[22m
     [90m·[39m should calculate the distance using distanceFromHash
     [90m·[39m should calculate the distance using compareHashes.
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.test.ts[22m[2m (8)[22m
   [33m❯[39m hash[2m (6)[22m
     [32m✓[39m base 2[33m 314[2mms[22m[39m
     [32m✓[39m base 10 (decimal)
     [32m✓[39m base 16 (hex)[33m 459[2mms[22m[39m
     [32m✓[39m base 64
     [33m⠇[39m base 23
     [90m·[39m base 17
   [90m·[39m pHash[2m (2)[22m
     [90m·[39m should calculate the distance using distanceFromHash
     [90m·[39m should calculate the distance using compareHashes.
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.test.ts[22m[2m (8)[22m
   [33m❯[39m hash[2m (6)[22m
     [32m✓[39m base 2[33m 314[2mms[22m[39m
     [32m✓[39m base 10 (decimal)
     [32m✓[39m base 16 (hex)[33m 459[2mms[22m[39m
     [32m✓[39m base 64
     [33m⠏[39m base 23
     [90m·[39m base 17
   [90m·[39m pHash[2m (2)[22m
     [90m·[39m should calculate the distance using distanceFromHash
     [90m·[39m should calculate the distance using compareHashes.
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.test.ts[22m[2m (8)[22m
   [33m❯[39m hash[2m (6)[22m
     [32m✓[39m base 2[33m 314[2mms[22m[39m
     [32m✓[39m base 10 (decimal)
     [32m✓[39m base 16 (hex)[33m 459[2mms[22m[39m
     [32m✓[39m base 64
     [33m⠋[39m base 23
     [90m·[39m base 17
   [90m·[39m pHash[2m (2)[22m
     [90m·[39m should calculate the distance using distanceFromHash
     [90m·[39m should calculate the distance using compareHashes.
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.test.ts[22m[2m (8)[22m
   [33m❯[39m hash[2m (6)[22m
     [32m✓[39m base 2[33m 314[2mms[22m[39m
     [32m✓[39m base 10 (decimal)
     [32m✓[39m base 16 (hex)[33m 459[2mms[22m[39m
     [32m✓[39m base 64
     [33m⠙[39m base 23
     [90m·[39m base 17
   [90m·[39m pHash[2m (2)[22m
     [90m·[39m should calculate the distance using distanceFromHash
     [90m·[39m should calculate the distance using compareHashes.
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.test.ts[22m[2m (8)[22m
   [33m❯[39m hash[2m (6)[22m
     [32m✓[39m base 2[33m 314[2mms[22m[39m
     [32m✓[39m base 10 (decimal)
     [32m✓[39m base 16 (hex)[33m 459[2mms[22m[39m
     [32m✓[39m base 64
     [33m⠹[39m base 23
     [90m·[39m base 17
   [90m·[39m pHash[2m (2)[22m
     [90m·[39m should calculate the distance using distanceFromHash
     [90m·[39m should calculate the distance using compareHashes.
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.test.ts[22m[2m (8)[22m
   [33m❯[39m hash[2m (6)[22m
     [32m✓[39m base 2[33m 314[2mms[22m[39m
     [32m✓[39m base 10 (decimal)
     [32m✓[39m base 16 (hex)[33m 459[2mms[22m[39m
     [32m✓[39m base 64
     [33m⠸[39m base 23
     [90m·[39m base 17
   [90m·[39m pHash[2m (2)[22m
     [90m·[39m should calculate the distance using distanceFromHash
     [90m·[39m should calculate the distance using compareHashes.
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.test.ts[22m[2m (8)[22m
   [33m❯[39m hash[2m (6)[22m
     [32m✓[39m base 2[33m 314[2mms[22m[39m
     [32m✓[39m base 10 (decimal)
     [32m✓[39m base 16 (hex)[33m 459[2mms[22m[39m
     [32m✓[39m base 64
     [33m⠼[39m base 23
     [90m·[39m base 17
   [90m·[39m pHash[2m (2)[22m
     [90m·[39m should calculate the distance using distanceFromHash
     [90m·[39m should calculate the distance using compareHashes.
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.test.ts[22m[2m (8)[22m
   [33m❯[39m hash[2m (6)[22m
     [32m✓[39m base 2[33m 314[2mms[22m[39m
     [32m✓[39m base 10 (decimal)
     [32m✓[39m base 16 (hex)[33m 459[2mms[22m[39m
     [32m✓[39m base 64
     [33m⠴[39m base 23
     [90m·[39m base 17
   [90m·[39m pHash[2m (2)[22m
     [90m·[39m should calculate the distance using distanceFromHash
     [90m·[39m should calculate the distance using compareHashes.
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.test.ts[22m[2m (8)[22m
   [33m❯[39m hash[2m (6)[22m
     [32m✓[39m base 2[33m 314[2mms[22m[39m
     [32m✓[39m base 10 (decimal)
     [32m✓[39m base 16 (hex)[33m 459[2mms[22m[39m
     [32m✓[39m base 64
     [33m⠦[39m base 23
     [90m·[39m base 17
   [90m·[39m pHash[2m (2)[22m
     [90m·[39m should calculate the distance using distanceFromHash
     [90m·[39m should calculate the distance using compareHashes.
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.test.ts[22m[2m (8)[22m
   [33m❯[39m hash[2m (6)[22m
     [32m✓[39m base 2[33m 314[2mms[22m[39m
     [32m✓[39m base 10 (decimal)
     [32m✓[39m base 16 (hex)[33m 459[2mms[22m[39m
     [32m✓[39m base 64
     [33m⠧[39m base 23
     [90m·[39m base 17
   [90m·[39m pHash[2m (2)[22m
     [90m·[39m should calculate the distance using distanceFromHash
     [90m·[39m should calculate the distance using compareHashes.
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.test.ts[22m[2m (8)[22m
   [33m❯[39m hash[2m (6)[22m
     [32m✓[39m base 2[33m 314[2mms[22m[39m
     [32m✓[39m base 10 (decimal)
     [32m✓[39m base 16 (hex)[33m 459[2mms[22m[39m
     [32m✓[39m base 64
     [33m⠇[39m base 23
     [90m·[39m base 17
   [90m·[39m pHash[2m (2)[22m
     [90m·[39m should calculate the distance using distanceFromHash
     [90m·[39m should calculate the distance using compareHashes.
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.test.ts[22m[2m (8)[22m
   [33m❯[39m hash[2m (6)[22m
     [32m✓[39m base 2[33m 314[2mms[22m[39m
     [32m✓[39m base 10 (decimal)
     [32m✓[39m base 16 (hex)[33m 459[2mms[22m[39m
     [32m✓[39m base 64
     [33m⠏[39m base 23
     [90m·[39m base 17
   [90m·[39m pHash[2m (2)[22m
     [90m·[39m should calculate the distance using distanceFromHash
     [90m·[39m should calculate the distance using compareHashes.
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.test.ts[22m[2m (8)[22m
   [33m❯[39m hash[2m (6)[22m
     [32m✓[39m base 2[33m 314[2mms[22m[39m
     [32m✓[39m base 10 (decimal)
     [32m✓[39m base 16 (hex)[33m 459[2mms[22m[39m
     [32m✓[39m base 64
     [33m⠋[39m base 23
     [90m·[39m base 17
   [90m·[39m pHash[2m (2)[22m
     [90m·[39m should calculate the distance using distanceFromHash
     [90m·[39m should calculate the distance using compareHashes.
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.test.ts[22m[2m (8)[22m
   [33m❯[39m hash[2m (6)[22m
     [32m✓[39m base 2[33m 314[2mms[22m[39m
     [32m✓[39m base 10 (decimal)
     [32m✓[39m base 16 (hex)[33m 459[2mms[22m[39m
     [32m✓[39m base 64
     [33m⠙[39m base 23
     [90m·[39m base 17
   [90m·[39m pHash[2m (2)[22m
     [90m·[39m should calculate the distance using distanceFromHash
     [90m·[39m should calculate the distance using compareHashes.
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.test.ts[22m[2m (8)[22m
   [33m❯[39m hash[2m (6)[22m
     [32m✓[39m base 2[33m 314[2mms[22m[39m
     [32m✓[39m base 10 (decimal)
     [32m✓[39m base 16 (hex)[33m 459[2mms[22m[39m
     [32m✓[39m base 64
     [33m⠹[39m base 23
     [90m·[39m base 17
   [90m·[39m pHash[2m (2)[22m
     [90m·[39m should calculate the distance using distanceFromHash
     [90m·[39m should calculate the distance using compareHashes.
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.test.ts[22m[2m (8)[22m
   [33m❯[39m hash[2m (6)[22m
     [32m✓[39m base 2[33m 314[2mms[22m[39m
     [32m✓[39m base 10 (decimal)
     [32m✓[39m base 16 (hex)[33m 459[2mms[22m[39m
     [32m✓[39m base 64
     [33m⠸[39m base 23
     [90m·[39m base 17
   [90m·[39m pHash[2m (2)[22m
     [90m·[39m should calculate the distance using distanceFromHash
     [90m·[39m should calculate the distance using compareHashes.
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.test.ts[22m[2m (8)[22m
   [33m❯[39m hash[2m (6)[22m
     [32m✓[39m base 2[33m 314[2mms[22m[39m
     [32m✓[39m base 10 (decimal)
     [32m✓[39m base 16 (hex)[33m 459[2mms[22m[39m
     [32m✓[39m base 64
     [33m⠼[39m base 23
     [90m·[39m base 17
   [90m·[39m pHash[2m (2)[22m
     [90m·[39m should calculate the distance using distanceFromHash
     [90m·[39m should calculate the distance using compareHashes.
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.test.ts[22m[2m (8)[22m
   [33m❯[39m hash[2m (6)[22m
     [32m✓[39m base 2[33m 314[2mms[22m[39m
     [32m✓[39m base 10 (decimal)
     [32m✓[39m base 16 (hex)[33m 459[2mms[22m[39m
     [32m✓[39m base 64
     [33m⠴[39m base 23
     [90m·[39m base 17
   [90m·[39m pHash[2m (2)[22m
     [90m·[39m should calculate the distance using distanceFromHash
     [90m·[39m should calculate the distance using compareHashes.
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.test.ts[22m[2m (8)[22m
   [33m❯[39m hash[2m (6)[22m
     [32m✓[39m base 2[33m 314[2mms[22m[39m
     [32m✓[39m base 10 (decimal)
     [32m✓[39m base 16 (hex)[33m 459[2mms[22m[39m
     [32m✓[39m base 64
     [33m⠦[39m base 23
     [90m·[39m base 17
   [90m·[39m pHash[2m (2)[22m
     [90m·[39m should calculate the distance using distanceFromHash
     [90m·[39m should calculate the distance using compareHashes.
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.test.ts[22m[2m (8)[22m
   [33m❯[39m hash[2m (6)[22m
     [32m✓[39m base 2[33m 314[2mms[22m[39m
     [32m✓[39m base 10 (decimal)
     [32m✓[39m base 16 (hex)[33m 459[2mms[22m[39m
     [32m✓[39m base 64
     [33m⠧[39m base 23
     [90m·[39m base 17
   [90m·[39m pHash[2m (2)[22m
     [90m·[39m should calculate the distance using distanceFromHash
     [90m·[39m should calculate the distance using compareHashes.
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.test.ts[22m[2m (8)[22m
   [33m❯[39m hash[2m (6)[22m
     [32m✓[39m base 2[33m 314[2mms[22m[39m
     [32m✓[39m base 10 (decimal)
     [32m✓[39m base 16 (hex)[33m 459[2mms[22m[39m
     [32m✓[39m base 64
     [33m⠇[39m base 23
     [90m·[39m base 17
   [90m·[39m pHash[2m (2)[22m
     [90m·[39m should calculate the distance using distanceFromHash
     [90m·[39m should calculate the distance using compareHashes.
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.test.ts[22m[2m (8)[22m
   [33m❯[39m hash[2m (6)[22m
     [32m✓[39m base 2[33m 314[2mms[22m[39m
     [32m✓[39m base 10 (decimal)
     [32m✓[39m base 16 (hex)[33m 459[2mms[22m[39m
     [32m✓[39m base 64
     [33m⠏[39m base 23
     [90m·[39m base 17
   [90m·[39m pHash[2m (2)[22m
     [90m·[39m should calculate the distance using distanceFromHash
     [90m·[39m should calculate the distance using compareHashes.
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.test.ts[22m[2m (8)[22m
   [33m❯[39m hash[2m (6)[22m
     [32m✓[39m base 2[33m 314[2mms[22m[39m
     [32m✓[39m base 10 (decimal)
     [32m✓[39m base 16 (hex)[33m 459[2mms[22m[39m
     [32m✓[39m base 64
     [33m⠋[39m base 23
     [90m·[39m base 17
   [90m·[39m pHash[2m (2)[22m
     [90m·[39m should calculate the distance using distanceFromHash
     [90m·[39m should calculate the distance using compareHashes.
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.test.ts[22m[2m (8)[22m
   [33m❯[39m hash[2m (6)[22m
     [32m✓[39m base 2[33m 314[2mms[22m[39m
     [32m✓[39m base 10 (decimal)
     [32m✓[39m base 16 (hex)[33m 459[2mms[22m[39m
     [32m✓[39m base 64
     [33m⠙[39m base 23
     [90m·[39m base 17
   [90m·[39m pHash[2m (2)[22m
     [90m·[39m should calculate the distance using distanceFromHash
     [90m·[39m should calculate the distance using compareHashes.
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.test.ts[22m[2m (8)[22m
   [33m❯[39m hash[2m (6)[22m
     [32m✓[39m base 2[33m 314[2mms[22m[39m
     [32m✓[39m base 10 (decimal)
     [32m✓[39m base 16 (hex)[33m 459[2mms[22m[39m
     [32m✓[39m base 64
     [33m⠹[39m base 23
     [90m·[39m base 17
   [90m·[39m pHash[2m (2)[22m
     [90m·[39m should calculate the distance using distanceFromHash
     [90m·[39m should calculate the distance using compareHashes.
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.test.ts[22m[2m (8)[22m
   [33m❯[39m hash[2m (6)[22m
     [32m✓[39m base 2[33m 314[2mms[22m[39m
     [32m✓[39m base 10 (decimal)
     [32m✓[39m base 16 (hex)[33m 459[2mms[22m[39m
     [32m✓[39m base 64
     [33m⠸[39m base 23
     [90m·[39m base 17
   [90m·[39m pHash[2m (2)[22m
     [90m·[39m should calculate the distance using distanceFromHash
     [90m·[39m should calculate the distance using compareHashes.
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.test.ts[22m[2m (8)[22m
   [33m❯[39m hash[2m (6)[22m
     [32m✓[39m base 2[33m 314[2mms[22m[39m
     [32m✓[39m base 10 (decimal)
     [32m✓[39m base 16 (hex)[33m 459[2mms[22m[39m
     [32m✓[39m base 64
     [33m⠼[39m base 23
     [90m·[39m base 17
   [90m·[39m pHash[2m (2)[22m
     [90m·[39m should calculate the distance using distanceFromHash
     [90m·[39m should calculate the distance using compareHashes.
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.test.ts[22m[2m (8)[22m
   [33m❯[39m hash[2m (6)[22m
     [32m✓[39m base 2[33m 314[2mms[22m[39m
     [32m✓[39m base 10 (decimal)
     [32m✓[39m base 16 (hex)[33m 459[2mms[22m[39m
     [32m✓[39m base 64
     [33m⠴[39m base 23
     [90m·[39m base 17
   [90m·[39m pHash[2m (2)[22m
     [90m·[39m should calculate the distance using distanceFromHash
     [90m·[39m should calculate the distance using compareHashes.
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.test.ts[22m[2m (8)[22m
   [33m❯[39m hash[2m (6)[22m
     [32m✓[39m base 2[33m 314[2mms[22m[39m
     [32m✓[39m base 10 (decimal)
     [32m✓[39m base 16 (hex)[33m 459[2mms[22m[39m
     [32m✓[39m base 64
     [33m⠦[39m base 23
     [90m·[39m base 17
   [90m·[39m pHash[2m (2)[22m
     [90m·[39m should calculate the distance using distanceFromHash
     [90m·[39m should calculate the distance using compareHashes.
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.test.ts[22m[2m (8)[22m
   [33m❯[39m hash[2m (6)[22m
     [32m✓[39m base 2[33m 314[2mms[22m[39m
     [32m✓[39m base 10 (decimal)
     [32m✓[39m base 16 (hex)[33m 459[2mms[22m[39m
     [32m✓[39m base 64
     [33m⠧[39m base 23
     [90m·[39m base 17
   [90m·[39m pHash[2m (2)[22m
     [90m·[39m should calculate the distance using distanceFromHash
     [90m·[39m should calculate the distance using compareHashes.
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.test.ts[22m[2m (8)[22m
   [33m❯[39m hash[2m (6)[22m
     [32m✓[39m base 2[33m 314[2mms[22m[39m
     [32m✓[39m base 10 (decimal)
     [32m✓[39m base 16 (hex)[33m 459[2mms[22m[39m
     [32m✓[39m base 64
     [33m⠇[39m base 23
     [90m·[39m base 17
   [90m·[39m pHash[2m (2)[22m
     [90m·[39m should calculate the distance using distanceFromHash
     [90m·[39m should calculate the distance using compareHashes.
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.test.ts[22m[2m (8)[22m
   [33m❯[39m hash[2m (6)[22m
     [32m✓[39m base 2[33m 314[2mms[22m[39m
     [32m✓[39m base 10 (decimal)
     [32m✓[39m base 16 (hex)[33m 459[2mms[22m[39m
     [32m✓[39m base 64
     [33m⠏[39m base 23
     [90m·[39m base 17
   [90m·[39m pHash[2m (2)[22m
     [90m·[39m should calculate the distance using distanceFromHash
     [90m·[39m should calculate the distance using compareHashes.
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.test.ts[22m[2m (8)[22m
   [33m❯[39m hash[2m (6)[22m
     [32m✓[39m base 2[33m 314[2mms[22m[39m
     [32m✓[39m base 10 (decimal)
     [32m✓[39m base 16 (hex)[33m 459[2mms[22m[39m
     [32m✓[39m base 64
     [33m⠋[39m base 23
     [90m·[39m base 17
   [90m·[39m pHash[2m (2)[22m
     [90m·[39m should calculate the distance using distanceFromHash
     [90m·[39m should calculate the distance using compareHashes.
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.test.ts[22m[2m (8)[22m
   [33m❯[39m hash[2m (6)[22m
     [32m✓[39m base 2[33m 314[2mms[22m[39m
     [32m✓[39m base 10 (decimal)
     [32m✓[39m base 16 (hex)[33m 459[2mms[22m[39m
     [32m✓[39m base 64
     [33m⠙[39m base 23
     [90m·[39m base 17
   [90m·[39m pHash[2m (2)[22m
     [90m·[39m should calculate the distance using distanceFromHash
     [90m·[39m should calculate the distance using compareHashes.
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.test.ts[22m[2m (8)[22m
   [33m❯[39m hash[2m (6)[22m
     [32m✓[39m base 2[33m 314[2mms[22m[39m
     [32m✓[39m base 10 (decimal)
     [32m✓[39m base 16 (hex)[33m 459[2mms[22m[39m
     [32m✓[39m base 64
     [33m⠹[39m base 23
     [90m·[39m base 17
   [90m·[39m pHash[2m (2)[22m
     [90m·[39m should calculate the distance using distanceFromHash
     [90m·[39m should calculate the distance using compareHashes.
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.test.ts[22m[2m (8)[22m
   [33m❯[39m hash[2m (6)[22m
     [32m✓[39m base 2[33m 314[2mms[22m[39m
     [32m✓[39m base 10 (decimal)
     [32m✓[39m base 16 (hex)[33m 459[2mms[22m[39m
     [32m✓[39m base 64
     [33m⠸[39m base 23
     [90m·[39m base 17
   [90m·[39m pHash[2m (2)[22m
     [90m·[39m should calculate the distance using distanceFromHash
     [90m·[39m should calculate the distance using compareHashes.
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.test.ts[22m[2m (8)[22m
   [33m❯[39m hash[2m (6)[22m
     [32m✓[39m base 2[33m 314[2mms[22m[39m
     [32m✓[39m base 10 (decimal)
     [32m✓[39m base 16 (hex)[33m 459[2mms[22m[39m
     [32m✓[39m base 64
     [33m⠼[39m base 23
     [90m·[39m base 17
   [90m·[39m pHash[2m (2)[22m
     [90m·[39m should calculate the distance using distanceFromHash
     [90m·[39m should calculate the distance using compareHashes.
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.test.ts[22m[2m (8)[22m
   [33m❯[39m hash[2m (6)[22m
     [32m✓[39m base 2[33m 314[2mms[22m[39m
     [32m✓[39m base 10 (decimal)
     [32m✓[39m base 16 (hex)[33m 459[2mms[22m[39m
     [32m✓[39m base 64
     [33m⠴[39m base 23
     [90m·[39m base 17
   [90m·[39m pHash[2m (2)[22m
     [90m·[39m should calculate the distance using distanceFromHash
     [90m·[39m should calculate the distance using compareHashes.
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.test.ts[22m[2m (8)[22m
   [33m❯[39m hash[2m (6)[22m
     [32m✓[39m base 2[33m 314[2mms[22m[39m
     [32m✓[39m base 10 (decimal)
     [32m✓[39m base 16 (hex)[33m 459[2mms[22m[39m
     [32m✓[39m base 64
     [33m⠦[39m base 23
     [90m·[39m base 17
   [90m·[39m pHash[2m (2)[22m
     [90m·[39m should calculate the distance using distanceFromHash
     [90m·[39m should calculate the distance using compareHashes.
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.test.ts[22m[2m (8)[22m
   [33m❯[39m hash[2m (6)[22m
     [32m✓[39m base 2[33m 314[2mms[22m[39m
     [32m✓[39m base 10 (decimal)
     [32m✓[39m base 16 (hex)[33m 459[2mms[22m[39m
     [32m✓[39m base 64
     [33m⠧[39m base 23
     [90m·[39m base 17
   [90m·[39m pHash[2m (2)[22m
     [90m·[39m should calculate the distance using distanceFromHash
     [90m·[39m should calculate the distance using compareHashes.
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.test.ts[22m[2m (8)[22m
   [33m❯[39m hash[2m (6)[22m
     [32m✓[39m base 2[33m 314[2mms[22m[39m
     [32m✓[39m base 10 (decimal)
     [32m✓[39m base 16 (hex)[33m 459[2mms[22m[39m
     [32m✓[39m base 64
     [33m⠇[39m base 23
     [90m·[39m base 17
   [90m·[39m pHash[2m (2)[22m
     [90m·[39m should calculate the distance using distanceFromHash
     [90m·[39m should calculate the distance using compareHashes.
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.test.ts[22m[2m (8)[22m
   [33m❯[39m hash[2m (6)[22m
     [32m✓[39m base 2[33m 314[2mms[22m[39m
     [32m✓[39m base 10 (decimal)
     [32m✓[39m base 16 (hex)[33m 459[2mms[22m[39m
     [32m✓[39m base 64
     [33m⠏[39m base 23
     [90m·[39m base 17
   [90m·[39m pHash[2m (2)[22m
     [90m·[39m should calculate the distance using distanceFromHash
     [90m·[39m should calculate the distance using compareHashes.
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.test.ts[22m[2m (8)[22m
   [33m❯[39m hash[2m (6)[22m
     [32m✓[39m base 2[33m 314[2mms[22m[39m
     [32m✓[39m base 10 (decimal)
     [32m✓[39m base 16 (hex)[33m 459[2mms[22m[39m
     [32m✓[39m base 64
     [33m⠋[39m base 23
     [90m·[39m base 17
   [90m·[39m pHash[2m (2)[22m
     [90m·[39m should calculate the distance using distanceFromHash
     [90m·[39m should calculate the distance using compareHashes.
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.test.ts[22m[2m (8)[22m
   [33m❯[39m hash[2m (6)[22m
     [32m✓[39m base 2[33m 314[2mms[22m[39m
     [32m✓[39m base 10 (decimal)
     [32m✓[39m base 16 (hex)[33m 459[2mms[22m[39m
     [32m✓[39m base 64
     [33m⠙[39m base 23
     [90m·[39m base 17
   [90m·[39m pHash[2m (2)[22m
     [90m·[39m should calculate the distance using distanceFromHash
     [90m·[39m should calculate the distance using compareHashes.
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.test.ts[22m[2m (8)[22m
   [33m❯[39m hash[2m (6)[22m
     [32m✓[39m base 2[33m 314[2mms[22m[39m
     [32m✓[39m base 10 (decimal)
     [32m✓[39m base 16 (hex)[33m 459[2mms[22m[39m
     [32m✓[39m base 64
     [33m⠹[39m base 23
     [90m·[39m base 17
   [90m·[39m pHash[2m (2)[22m
     [90m·[39m should calculate the distance using distanceFromHash
     [90m·[39m should calculate the distance using compareHashes.
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.test.ts[22m[2m (8)[22m
   [33m❯[39m hash[2m (6)[22m
     [32m✓[39m base 2[33m 314[2mms[22m[39m
     [32m✓[39m base 10 (decimal)
     [32m✓[39m base 16 (hex)[33m 459[2mms[22m[39m
     [32m✓[39m base 64
     [33m⠸[39m base 23
     [90m·[39m base 17
   [90m·[39m pHash[2m (2)[22m
     [90m·[39m should calculate the distance using distanceFromHash
     [90m·[39m should calculate the distance using compareHashes.
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.test.ts[22m[2m (8)[22m
   [33m❯[39m hash[2m (6)[22m
     [32m✓[39m base 2[33m 314[2mms[22m[39m
     [32m✓[39m base 10 (decimal)
     [32m✓[39m base 16 (hex)[33m 459[2mms[22m[39m
     [32m✓[39m base 64
     [33m⠼[39m base 23
     [90m·[39m base 17
   [90m·[39m pHash[2m (2)[22m
     [90m·[39m should calculate the distance using distanceFromHash
     [90m·[39m should calculate the distance using compareHashes.
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.test.ts[22m[2m (8)[22m
   [33m❯[39m hash[2m (6)[22m
     [32m✓[39m base 2[33m 314[2mms[22m[39m
     [32m✓[39m base 10 (decimal)
     [32m✓[39m base 16 (hex)[33m 459[2mms[22m[39m
     [32m✓[39m base 64
     [33m⠴[39m base 23
     [90m·[39m base 17
   [90m·[39m pHash[2m (2)[22m
     [90m·[39m should calculate the distance using distanceFromHash
     [90m·[39m should calculate the distance using compareHashes.
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.test.ts[22m[2m (8)[22m
   [33m❯[39m hash[2m (6)[22m
     [32m✓[39m base 2[33m 314[2mms[22m[39m
     [32m✓[39m base 10 (decimal)
     [32m✓[39m base 16 (hex)[33m 459[2mms[22m[39m
     [32m✓[39m base 64
     [33m⠦[39m base 23
     [90m·[39m base 17
   [90m·[39m pHash[2m (2)[22m
     [90m·[39m should calculate the distance using distanceFromHash
     [90m·[39m should calculate the distance using compareHashes.
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.test.ts[22m[2m (8)[22m
   [33m❯[39m hash[2m (6)[22m
     [32m✓[39m base 2[33m 314[2mms[22m[39m
     [32m✓[39m base 10 (decimal)
     [32m✓[39m base 16 (hex)[33m 459[2mms[22m[39m
     [32m✓[39m base 64
     [33m⠧[39m base 23
     [90m·[39m base 17
   [90m·[39m pHash[2m (2)[22m
     [90m·[39m should calculate the distance using distanceFromHash
     [90m·[39m should calculate the distance using compareHashes.
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.test.ts[22m[2m (8)[22m
   [33m❯[39m hash[2m (6)[22m
     [32m✓[39m base 2[33m 314[2mms[22m[39m
     [32m✓[39m base 10 (decimal)
     [32m✓[39m base 16 (hex)[33m 459[2mms[22m[39m
     [32m✓[39m base 64
     [33m⠇[39m base 23
     [90m·[39m base 17
   [90m·[39m pHash[2m (2)[22m
     [90m·[39m should calculate the distance using distanceFromHash
     [90m·[39m should calculate the distance using compareHashes.
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.test.ts[22m[2m (8)[22m
   [33m❯[39m hash[2m (6)[22m
     [32m✓[39m base 2[33m 314[2mms[22m[39m
     [32m✓[39m base 10 (decimal)
     [32m✓[39m base 16 (hex)[33m 459[2mms[22m[39m
     [32m✓[39m base 64
     [33m⠏[39m base 23
     [90m·[39m base 17
   [90m·[39m pHash[2m (2)[22m
     [90m·[39m should calculate the distance using distanceFromHash
     [90m·[39m should calculate the distance using compareHashes.
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.test.ts[22m[2m (8)[22m
   [33m❯[39m hash[2m (6)[22m
     [32m✓[39m base 2[33m 314[2mms[22m[39m
     [32m✓[39m base 10 (decimal)
     [32m✓[39m base 16 (hex)[33m 459[2mms[22m[39m
     [32m✓[39m base 64
     [33m⠋[39m base 23
     [90m·[39m base 17
   [90m·[39m pHash[2m (2)[22m
     [90m·[39m should calculate the distance using distanceFromHash
     [90m·[39m should calculate the distance using compareHashes.
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.test.ts[22m[2m (8)[22m
   [33m❯[39m hash[2m (6)[22m
     [32m✓[39m base 2[33m 314[2mms[22m[39m
     [32m✓[39m base 10 (decimal)
     [32m✓[39m base 16 (hex)[33m 459[2mms[22m[39m
     [32m✓[39m base 64
     [33m⠙[39m base 23
     [90m·[39m base 17
   [90m·[39m pHash[2m (2)[22m
     [90m·[39m should calculate the distance using distanceFromHash
     [90m·[39m should calculate the distance using compareHashes.
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.test.ts[22m[2m (8)[22m
   [33m❯[39m hash[2m (6)[22m
     [32m✓[39m base 2[33m 314[2mms[22m[39m
     [32m✓[39m base 10 (decimal)
     [32m✓[39m base 16 (hex)[33m 459[2mms[22m[39m
     [32m✓[39m base 64
     [33m⠹[39m base 23
     [90m·[39m base 17
   [90m·[39m pHash[2m (2)[22m
     [90m·[39m should calculate the distance using distanceFromHash
     [90m·[39m should calculate the distance using compareHashes.
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.test.ts[22m[2m (8)[22m
   [33m❯[39m hash[2m (6)[22m
     [32m✓[39m base 2[33m 314[2mms[22m[39m
     [32m✓[39m base 10 (decimal)
     [32m✓[39m base 16 (hex)[33m 459[2mms[22m[39m
     [32m✓[39m base 64
     [33m⠸[39m base 23
     [90m·[39m base 17
   [90m·[39m pHash[2m (2)[22m
     [90m·[39m should calculate the distance using distanceFromHash
     [90m·[39m should calculate the distance using compareHashes.
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.test.ts[22m[2m (8)[22m
   [33m❯[39m hash[2m (6)[22m
     [32m✓[39m base 2[33m 314[2mms[22m[39m
     [32m✓[39m base 10 (decimal)
     [32m✓[39m base 16 (hex)[33m 459[2mms[22m[39m
     [32m✓[39m base 64
     [33m⠼[39m base 23
     [90m·[39m base 17
   [90m·[39m pHash[2m (2)[22m
     [90m·[39m should calculate the distance using distanceFromHash
     [90m·[39m should calculate the distance using compareHashes.
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.test.ts[22m[2m (8)[22m
   [33m❯[39m hash[2m (6)[22m
     [32m✓[39m base 2[33m 314[2mms[22m[39m
     [32m✓[39m base 10 (decimal)
     [32m✓[39m base 16 (hex)[33m 459[2mms[22m[39m
     [32m✓[39m base 64
     [33m⠴[39m base 23
     [90m·[39m base 17
   [90m·[39m pHash[2m (2)[22m
     [90m·[39m should calculate the distance using distanceFromHash
     [90m·[39m should calculate the distance using compareHashes.
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.test.ts[22m[2m (8)[22m
   [33m❯[39m hash[2m (6)[22m
     [32m✓[39m base 2[33m 314[2mms[22m[39m
     [32m✓[39m base 10 (decimal)
     [32m✓[39m base 16 (hex)[33m 459[2mms[22m[39m
     [32m✓[39m base 64
     [33m⠦[39m base 23
     [90m·[39m base 17
   [90m·[39m pHash[2m (2)[22m
     [90m·[39m should calculate the distance using distanceFromHash
     [90m·[39m should calculate the distance using compareHashes.
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.test.ts[22m[2m (8)[22m
   [33m❯[39m hash[2m (6)[22m
     [32m✓[39m base 2[33m 314[2mms[22m[39m
     [32m✓[39m base 10 (decimal)
     [32m✓[39m base 16 (hex)[33m 459[2mms[22m[39m
     [32m✓[39m base 64
     [33m⠧[39m base 23
     [90m·[39m base 17
   [90m·[39m pHash[2m (2)[22m
     [90m·[39m should calculate the distance using distanceFromHash
     [90m·[39m should calculate the distance using compareHashes.
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.test.ts[22m[2m (8)[22m
   [33m❯[39m hash[2m (6)[22m
     [32m✓[39m base 2[33m 314[2mms[22m[39m
     [32m✓[39m base 10 (decimal)
     [32m✓[39m base 16 (hex)[33m 459[2mms[22m[39m
     [32m✓[39m base 64
     [33m⠇[39m base 23
     [90m·[39m base 17
   [90m·[39m pHash[2m (2)[22m
     [90m·[39m should calculate the distance using distanceFromHash
     [90m·[39m should calculate the distance using compareHashes.
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.test.ts[22m[2m (8)[22m
   [33m❯[39m hash[2m (6)[22m
     [32m✓[39m base 2[33m 314[2mms[22m[39m
     [32m✓[39m base 10 (decimal)
     [32m✓[39m base 16 (hex)[33m 459[2mms[22m[39m
     [32m✓[39m base 64
     [33m⠏[39m base 23
     [90m·[39m base 17
   [90m·[39m pHash[2m (2)[22m
     [90m·[39m should calculate the distance using distanceFromHash
     [90m·[39m should calculate the distance using compareHashes.
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.test.ts[22m[2m (8)[22m
   [33m❯[39m hash[2m (6)[22m
     [32m✓[39m base 2[33m 314[2mms[22m[39m
     [32m✓[39m base 10 (decimal)
     [32m✓[39m base 16 (hex)[33m 459[2mms[22m[39m
     [32m✓[39m base 64
     [33m⠋[39m base 23
     [90m·[39m base 17
   [90m·[39m pHash[2m (2)[22m
     [90m·[39m should calculate the distance using distanceFromHash
     [90m·[39m should calculate the distance using compareHashes.
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.test.ts[22m[2m (8)[22m
   [33m❯[39m hash[2m (6)[22m
     [32m✓[39m base 2[33m 314[2mms[22m[39m
     [32m✓[39m base 10 (decimal)
     [32m✓[39m base 16 (hex)[33m 459[2mms[22m[39m
     [32m✓[39m base 64
     [33m⠙[39m base 23
     [90m·[39m base 17
   [90m·[39m pHash[2m (2)[22m
     [90m·[39m should calculate the distance using distanceFromHash
     [90m·[39m should calculate the distance using compareHashes.
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.test.ts[22m[2m (8)[22m
   [33m❯[39m hash[2m (6)[22m
     [32m✓[39m base 2[33m 314[2mms[22m[39m
     [32m✓[39m base 10 (decimal)
     [32m✓[39m base 16 (hex)[33m 459[2mms[22m[39m
     [32m✓[39m base 64
     [33m⠹[39m base 23
     [90m·[39m base 17
   [90m·[39m pHash[2m (2)[22m
     [90m·[39m should calculate the distance using distanceFromHash
     [90m·[39m should calculate the distance using compareHashes.
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.test.ts[22m[2m (8)[22m
   [33m❯[39m hash[2m (6)[22m
     [32m✓[39m base 2[33m 314[2mms[22m[39m
     [32m✓[39m base 10 (decimal)
     [32m✓[39m base 16 (hex)[33m 459[2mms[22m[39m
     [32m✓[39m base 64
     [33m⠸[39m base 23
     [90m·[39m base 17
   [90m·[39m pHash[2m (2)[22m
     [90m·[39m should calculate the distance using distanceFromHash
     [90m·[39m should calculate the distance using compareHashes.
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.test.ts[22m[2m (8)[22m
   [33m❯[39m hash[2m (6)[22m
     [32m✓[39m base 2[33m 314[2mms[22m[39m
     [32m✓[39m base 10 (decimal)
     [32m✓[39m base 16 (hex)[33m 459[2mms[22m[39m
     [32m✓[39m base 64
     [33m⠼[39m base 23
     [90m·[39m base 17
   [90m·[39m pHash[2m (2)[22m
     [90m·[39m should calculate the distance using distanceFromHash
     [90m·[39m should calculate the distance using compareHashes.
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.test.ts[22m[2m (8)[22m
   [33m❯[39m hash[2m (6)[22m
     [32m✓[39m base 2[33m 314[2mms[22m[39m
     [32m✓[39m base 10 (decimal)
     [32m✓[39m base 16 (hex)[33m 459[2mms[22m[39m
     [32m✓[39m base 64
     [33m⠴[39m base 23
     [90m·[39m base 17
   [90m·[39m pHash[2m (2)[22m
     [90m·[39m should calculate the distance using distanceFromHash
     [90m·[39m should calculate the distance using compareHashes.
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.test.ts[22m[2m (8)[22m
   [33m❯[39m hash[2m (6)[22m
     [32m✓[39m base 2[33m 314[2mms[22m[39m
     [32m✓[39m base 10 (decimal)
     [32m✓[39m base 16 (hex)[33m 459[2mms[22m[39m
     [32m✓[39m base 64
     [33m⠦[39m base 23
     [90m·[39m base 17
   [90m·[39m pHash[2m (2)[22m
     [90m·[39m should calculate the distance using distanceFromHash
     [90m·[39m should calculate the distance using compareHashes.
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.test.ts[22m[2m (8)[22m
   [33m❯[39m hash[2m (6)[22m
     [32m✓[39m base 2[33m 314[2mms[22m[39m
     [32m✓[39m base 10 (decimal)
     [32m✓[39m base 16 (hex)[33m 459[2mms[22m[39m
     [32m✓[39m base 64
     [33m⠧[39m base 23
     [90m·[39m base 17
   [90m·[39m pHash[2m (2)[22m
     [90m·[39m should calculate the distance using distanceFromHash
     [90m·[39m should calculate the distance using compareHashes.
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.test.ts[22m[2m (8)[22m
   [33m❯[39m hash[2m (6)[22m
     [32m✓[39m base 2[33m 314[2mms[22m[39m
     [32m✓[39m base 10 (decimal)
     [32m✓[39m base 16 (hex)[33m 459[2mms[22m[39m
     [32m✓[39m base 64
     [33m⠇[39m base 23
     [90m·[39m base 17
   [90m·[39m pHash[2m (2)[22m
     [90m·[39m should calculate the distance using distanceFromHash
     [90m·[39m should calculate the distance using compareHashes.
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.test.ts[22m[2m (8)[22m
   [33m❯[39m hash[2m (6)[22m
     [32m✓[39m base 2[33m 314[2mms[22m[39m
     [32m✓[39m base 10 (decimal)
     [32m✓[39m base 16 (hex)[33m 459[2mms[22m[39m
     [32m✓[39m base 64
     [33m⠏[39m base 23
     [90m·[39m base 17
   [90m·[39m pHash[2m (2)[22m
     [90m·[39m should calculate the distance using distanceFromHash
     [90m·[39m should calculate the distance using compareHashes.
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.test.ts[22m[2m (8)[22m
   [33m❯[39m hash[2m (6)[22m
     [32m✓[39m base 2[33m 314[2mms[22m[39m
     [32m✓[39m base 10 (decimal)
     [32m✓[39m base 16 (hex)[33m 459[2mms[22m[39m
     [32m✓[39m base 64
     [33m⠋[39m base 23
     [90m·[39m base 17
   [90m·[39m pHash[2m (2)[22m
     [90m·[39m should calculate the distance using distanceFromHash
     [90m·[39m should calculate the distance using compareHashes.
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.test.ts[22m[2m (8)[22m
   [33m❯[39m hash[2m (6)[22m
     [32m✓[39m base 2[33m 314[2mms[22m[39m
     [32m✓[39m base 10 (decimal)
     [32m✓[39m base 16 (hex)[33m 459[2mms[22m[39m
     [32m✓[39m base 64
     [33m⠙[39m base 23
     [90m·[39m base 17
   [90m·[39m pHash[2m (2)[22m
     [90m·[39m should calculate the distance using distanceFromHash
     [90m·[39m should calculate the distance using compareHashes.
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.test.ts[22m[2m (8)[22m
   [33m❯[39m hash[2m (6)[22m
     [32m✓[39m base 2[33m 314[2mms[22m[39m
     [32m✓[39m base 10 (decimal)
     [32m✓[39m base 16 (hex)[33m 459[2mms[22m[39m
     [32m✓[39m base 64
     [33m⠹[39m base 23
     [90m·[39m base 17
   [90m·[39m pHash[2m (2)[22m
     [90m·[39m should calculate the distance using distanceFromHash
     [90m·[39m should calculate the distance using compareHashes.
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.test.ts[22m[2m (8)[22m
   [33m❯[39m hash[2m (6)[22m
     [32m✓[39m base 2[33m 314[2mms[22m[39m
     [32m✓[39m base 10 (decimal)
     [32m✓[39m base 16 (hex)[33m 459[2mms[22m[39m
     [32m✓[39m base 64
     [33m⠸[39m base 23
     [90m·[39m base 17
   [90m·[39m pHash[2m (2)[22m
     [90m·[39m should calculate the distance using distanceFromHash
     [90m·[39m should calculate the distance using compareHashes.
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.test.ts[22m[2m (8)[22m
   [33m❯[39m hash[2m (6)[22m
     [32m✓[39m base 2[33m 314[2mms[22m[39m
     [32m✓[39m base 10 (decimal)
     [32m✓[39m base 16 (hex)[33m 459[2mms[22m[39m
     [32m✓[39m base 64
     [33m⠼[39m base 23
     [90m·[39m base 17
   [90m·[39m pHash[2m (2)[22m
     [90m·[39m should calculate the distance using distanceFromHash
     [90m·[39m should calculate the distance using compareHashes.
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.test.ts[22m[2m (8)[22m
   [33m❯[39m hash[2m (6)[22m
     [32m✓[39m base 2[33m 314[2mms[22m[39m
     [32m✓[39m base 10 (decimal)
     [32m✓[39m base 16 (hex)[33m 459[2mms[22m[39m
     [32m✓[39m base 64
     [33m⠴[39m base 23
     [90m·[39m base 17
   [90m·[39m pHash[2m (2)[22m
     [90m·[39m should calculate the distance using distanceFromHash
     [90m·[39m should calculate the distance using compareHashes.
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.test.ts[22m[2m (8)[22m
   [33m❯[39m hash[2m (6)[22m
     [32m✓[39m base 2[33m 314[2mms[22m[39m
     [32m✓[39m base 10 (decimal)
     [32m✓[39m base 16 (hex)[33m 459[2mms[22m[39m
     [32m✓[39m base 64
     [33m⠦[39m base 23
     [90m·[39m base 17
   [90m·[39m pHash[2m (2)[22m
     [90m·[39m should calculate the distance using distanceFromHash
     [90m·[39m should calculate the distance using compareHashes.
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.test.ts[22m[2m (8)[22m
   [33m❯[39m hash[2m (6)[22m
     [32m✓[39m base 2[33m 314[2mms[22m[39m
     [32m✓[39m base 10 (decimal)
     [32m✓[39m base 16 (hex)[33m 459[2mms[22m[39m
     [32m✓[39m base 64
     [33m⠧[39m base 23
     [90m·[39m base 17
   [90m·[39m pHash[2m (2)[22m
     [90m·[39m should calculate the distance using distanceFromHash
     [90m·[39m should calculate the distance using compareHashes.
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.test.ts[22m[2m (8)[22m
   [33m❯[39m hash[2m (6)[22m
     [32m✓[39m base 2[33m 314[2mms[22m[39m
     [32m✓[39m base 10 (decimal)
     [32m✓[39m base 16 (hex)[33m 459[2mms[22m[39m
     [32m✓[39m base 64
     [33m⠇[39m base 23
     [90m·[39m base 17
   [90m·[39m pHash[2m (2)[22m
     [90m·[39m should calculate the distance using distanceFromHash
     [90m·[39m should calculate the distance using compareHashes.
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.test.ts[22m[2m (8)[22m
   [33m❯[39m hash[2m (6)[22m
     [32m✓[39m base 2[33m 314[2mms[22m[39m
     [32m✓[39m base 10 (decimal)
     [32m✓[39m base 16 (hex)[33m 459[2mms[22m[39m
     [32m✓[39m base 64
     [33m⠏[39m base 23
     [90m·[39m base 17
   [90m·[39m pHash[2m (2)[22m
     [90m·[39m should calculate the distance using distanceFromHash
     [90m·[39m should calculate the distance using compareHashes.
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.test.ts[22m[2m (8)[22m
   [33m❯[39m hash[2m (6)[22m
     [32m✓[39m base 2[33m 314[2mms[22m[39m
     [32m✓[39m base 10 (decimal)
     [32m✓[39m base 16 (hex)[33m 459[2mms[22m[39m
     [32m✓[39m base 64
     [33m⠋[39m base 23
     [90m·[39m base 17
   [90m·[39m pHash[2m (2)[22m
     [90m·[39m should calculate the distance using distanceFromHash
     [90m·[39m should calculate the distance using compareHashes.
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.test.ts[22m[2m (8)[22m
   [33m❯[39m hash[2m (6)[22m
     [32m✓[39m base 2[33m 314[2mms[22m[39m
     [32m✓[39m base 10 (decimal)
     [32m✓[39m base 16 (hex)[33m 459[2mms[22m[39m
     [32m✓[39m base 64
     [33m⠙[39m base 23
     [90m·[39m base 17
   [90m·[39m pHash[2m (2)[22m
     [90m·[39m should calculate the distance using distanceFromHash
     [90m·[39m should calculate the distance using compareHashes.
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.test.ts[22m[2m (8)[22m
   [33m❯[39m hash[2m (6)[22m
     [32m✓[39m base 2[33m 314[2mms[22m[39m
     [32m✓[39m base 10 (decimal)
     [32m✓[39m base 16 (hex)[33m 459[2mms[22m[39m
     [32m✓[39m base 64
     [33m⠹[39m base 23
     [90m·[39m base 17
   [90m·[39m pHash[2m (2)[22m
     [90m·[39m should calculate the distance using distanceFromHash
     [90m·[39m should calculate the distance using compareHashes.
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.test.ts[22m[2m (8)[22m
   [33m❯[39m hash[2m (6)[22m
     [32m✓[39m base 2[33m 314[2mms[22m[39m
     [32m✓[39m base 10 (decimal)
     [32m✓[39m base 16 (hex)[33m 459[2mms[22m[39m
     [32m✓[39m base 64
     [33m⠸[39m base 23
     [90m·[39m base 17
   [90m·[39m pHash[2m (2)[22m
     [90m·[39m should calculate the distance using distanceFromHash
     [90m·[39m should calculate the distance using compareHashes.
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.test.ts[22m[2m (8)[22m
   [33m❯[39m hash[2m (6)[22m
     [32m✓[39m base 2[33m 314[2mms[22m[39m
     [32m✓[39m base 10 (decimal)
     [32m✓[39m base 16 (hex)[33m 459[2mms[22m[39m
     [32m✓[39m base 64
     [33m⠼[39m base 23
     [90m·[39m base 17
   [90m·[39m pHash[2m (2)[22m
     [90m·[39m should calculate the distance using distanceFromHash
     [90m·[39m should calculate the distance using compareHashes.
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.test.ts[22m[2m (8)[22m
   [33m❯[39m hash[2m (6)[22m
     [32m✓[39m base 2[33m 314[2mms[22m[39m
     [32m✓[39m base 10 (decimal)
     [32m✓[39m base 16 (hex)[33m 459[2mms[22m[39m
     [32m✓[39m base 64
     [33m⠴[39m base 23
     [90m·[39m base 17
   [90m·[39m pHash[2m (2)[22m
     [90m·[39m should calculate the distance using distanceFromHash
     [90m·[39m should calculate the distance using compareHashes.
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.test.ts[22m[2m (8)[22m
   [33m❯[39m hash[2m (6)[22m
     [32m✓[39m base 2[33m 314[2mms[22m[39m
     [32m✓[39m base 10 (decimal)
     [32m✓[39m base 16 (hex)[33m 459[2mms[22m[39m
     [32m✓[39m base 64
     [33m⠦[39m base 23
     [90m·[39m base 17
   [90m·[39m pHash[2m (2)[22m
     [90m·[39m should calculate the distance using distanceFromHash
     [90m·[39m should calculate the distance using compareHashes.
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.test.ts[22m[2m (8)[22m
   [33m❯[39m hash[2m (6)[22m
     [32m✓[39m base 2[33m 314[2mms[22m[39m
     [32m✓[39m base 10 (decimal)
     [32m✓[39m base 16 (hex)[33m 459[2mms[22m[39m
     [32m✓[39m base 64
     [33m⠧[39m base 23
     [90m·[39m base 17
   [90m·[39m pHash[2m (2)[22m
     [90m·[39m should calculate the distance using distanceFromHash
     [90m·[39m should calculate the distance using compareHashes.
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.test.ts[22m[2m (8)[22m
   [33m❯[39m hash[2m (6)[22m
     [32m✓[39m base 2[33m 314[2mms[22m[39m
     [32m✓[39m base 10 (decimal)
     [32m✓[39m base 16 (hex)[33m 459[2mms[22m[39m
     [32m✓[39m base 64
     [33m⠇[39m base 23
     [90m·[39m base 17
   [90m·[39m pHash[2m (2)[22m
     [90m·[39m should calculate the distance using distanceFromHash
     [90m·[39m should calculate the distance using compareHashes.
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.test.ts[22m[2m (8)[22m
   [33m❯[39m hash[2m (6)[22m
     [32m✓[39m base 2[33m 314[2mms[22m[39m
     [32m✓[39m base 10 (decimal)
     [32m✓[39m base 16 (hex)[33m 459[2mms[22m[39m
     [32m✓[39m base 64
     [33m⠏[39m base 23
     [90m·[39m base 17
   [90m·[39m pHash[2m (2)[22m
     [90m·[39m should calculate the distance using distanceFromHash
     [90m·[39m should calculate the distance using compareHashes.
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.test.ts[22m[2m (8)[22m
   [33m❯[39m hash[2m (6)[22m
     [32m✓[39m base 2[33m 314[2mms[22m[39m
     [32m✓[39m base 10 (decimal)
     [32m✓[39m base 16 (hex)[33m 459[2mms[22m[39m
     [32m✓[39m base 64
     [33m⠋[39m base 23
     [90m·[39m base 17
   [90m·[39m pHash[2m (2)[22m
     [90m·[39m should calculate the distance using distanceFromHash
     [90m·[39m should calculate the distance using compareHashes.
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.test.ts[22m[2m (8)[22m
   [33m❯[39m hash[2m (6)[22m
     [32m✓[39m base 2[33m 314[2mms[22m[39m
     [32m✓[39m base 10 (decimal)
     [32m✓[39m base 16 (hex)[33m 459[2mms[22m[39m
     [32m✓[39m base 64
     [33m⠙[39m base 23
     [90m·[39m base 17
   [90m·[39m pHash[2m (2)[22m
     [90m·[39m should calculate the distance using distanceFromHash
     [90m·[39m should calculate the distance using compareHashes.
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.test.ts[22m[2m (8)[22m
   [33m❯[39m hash[2m (6)[22m
     [32m✓[39m base 2[33m 314[2mms[22m[39m
     [32m✓[39m base 10 (decimal)
     [32m✓[39m base 16 (hex)[33m 459[2mms[22m[39m
     [32m✓[39m base 64
     [33m⠹[39m base 23
     [90m·[39m base 17
   [90m·[39m pHash[2m (2)[22m
     [90m·[39m should calculate the distance using distanceFromHash
     [90m·[39m should calculate the distance using compareHashes.
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.test.ts[22m[2m (8)[22m
   [33m❯[39m hash[2m (6)[22m
     [32m✓[39m base 2[33m 314[2mms[22m[39m
     [32m✓[39m base 10 (decimal)
     [32m✓[39m base 16 (hex)[33m 459[2mms[22m[39m
     [32m✓[39m base 64
     [33m⠸[39m base 23
     [90m·[39m base 17
   [90m·[39m pHash[2m (2)[22m
     [90m·[39m should calculate the distance using distanceFromHash
     [90m·[39m should calculate the distance using compareHashes.
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.test.ts[22m[2m (8)[22m
   [33m❯[39m hash[2m (6)[22m
     [32m✓[39m base 2[33m 314[2mms[22m[39m
     [32m✓[39m base 10 (decimal)
     [32m✓[39m base 16 (hex)[33m 459[2mms[22m[39m
     [32m✓[39m base 64
     [33m⠼[39m base 23
     [90m·[39m base 17
   [90m·[39m pHash[2m (2)[22m
     [90m·[39m should calculate the distance using distanceFromHash
     [90m·[39m should calculate the distance using compareHashes.
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.test.ts[22m[2m (8)[22m
   [33m❯[39m hash[2m (6)[22m
     [32m✓[39m base 2[33m 314[2mms[22m[39m
     [32m✓[39m base 10 (decimal)
     [32m✓[39m base 16 (hex)[33m 459[2mms[22m[39m
     [32m✓[39m base 64
     [33m⠴[39m base 23
     [90m·[39m base 17
   [90m·[39m pHash[2m (2)[22m
     [90m·[39m should calculate the distance using distanceFromHash
     [90m·[39m should calculate the distance using compareHashes.
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.test.ts[22m[2m (8)[22m
   [32m✓[39m hash[2m (6)[22m[33m 3886[2mms[22m[39m
     [32m✓[39m base 2[33m 314[2mms[22m[39m
     [32m✓[39m base 10 (decimal)
     [32m✓[39m base 16 (hex)[33m 459[2mms[22m[39m
     [32m✓[39m base 64
     [32m✓[39m base 23[33m 2609[2mms[22m[39m
     [32m✓[39m base 17
   [33m❯[39m pHash[2m (2)[22m
     [33m⠙[39m should calculate the distance using distanceFromHash
     [90m·[39m should calculate the distance using compareHashes.
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.test.ts[22m[2m (8)[22m
   [32m✓[39m hash[2m (6)[22m[33m 3886[2mms[22m[39m
     [32m✓[39m base 2[33m 314[2mms[22m[39m
     [32m✓[39m base 10 (decimal)
     [32m✓[39m base 16 (hex)[33m 459[2mms[22m[39m
     [32m✓[39m base 64
     [32m✓[39m base 23[33m 2609[2mms[22m[39m
     [32m✓[39m base 17
   [33m❯[39m pHash[2m (2)[22m
     [33m⠹[39m should calculate the distance using distanceFromHash
     [90m·[39m should calculate the distance using compareHashes.
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.test.ts[22m[2m (8)[22m
   [32m✓[39m hash[2m (6)[22m[33m 3886[2mms[22m[39m
     [32m✓[39m base 2[33m 314[2mms[22m[39m
     [32m✓[39m base 10 (decimal)
     [32m✓[39m base 16 (hex)[33m 459[2mms[22m[39m
     [32m✓[39m base 64
     [32m✓[39m base 23[33m 2609[2mms[22m[39m
     [32m✓[39m base 17
   [33m❯[39m pHash[2m (2)[22m
     [33m⠸[39m should calculate the distance using distanceFromHash
     [90m·[39m should calculate the distance using compareHashes.
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.test.ts[22m[2m (8)[22m
   [32m✓[39m hash[2m (6)[22m[33m 3886[2mms[22m[39m
     [32m✓[39m base 2[33m 314[2mms[22m[39m
     [32m✓[39m base 10 (decimal)
     [32m✓[39m base 16 (hex)[33m 459[2mms[22m[39m
     [32m✓[39m base 64
     [32m✓[39m base 23[33m 2609[2mms[22m[39m
     [32m✓[39m base 17
   [33m❯[39m pHash[2m (2)[22m
     [33m⠼[39m should calculate the distance using distanceFromHash
     [90m·[39m should calculate the distance using compareHashes.
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.test.ts[22m[2m (8)[22m
   [32m✓[39m hash[2m (6)[22m[33m 3886[2mms[22m[39m
     [32m✓[39m base 2[33m 314[2mms[22m[39m
     [32m✓[39m base 10 (decimal)
     [32m✓[39m base 16 (hex)[33m 459[2mms[22m[39m
     [32m✓[39m base 64
     [32m✓[39m base 23[33m 2609[2mms[22m[39m
     [32m✓[39m base 17
   [33m❯[39m pHash[2m (2)[22m
     [33m⠴[39m should calculate the distance using distanceFromHash
     [90m·[39m should calculate the distance using compareHashes.
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.test.ts[22m[2m (8)[22m
   [32m✓[39m hash[2m (6)[22m[33m 3886[2mms[22m[39m
     [32m✓[39m base 2[33m 314[2mms[22m[39m
     [32m✓[39m base 10 (decimal)
     [32m✓[39m base 16 (hex)[33m 459[2mms[22m[39m
     [32m✓[39m base 64
     [32m✓[39m base 23[33m 2609[2mms[22m[39m
     [32m✓[39m base 17
   [33m❯[39m pHash[2m (2)[22m
     [33m⠦[39m should calculate the distance using distanceFromHash
     [90m·[39m should calculate the distance using compareHashes.
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.test.ts[22m[2m (8)[22m
   [32m✓[39m hash[2m (6)[22m[33m 3886[2mms[22m[39m
     [32m✓[39m base 2[33m 314[2mms[22m[39m
     [32m✓[39m base 10 (decimal)
     [32m✓[39m base 16 (hex)[33m 459[2mms[22m[39m
     [32m✓[39m base 64
     [32m✓[39m base 23[33m 2609[2mms[22m[39m
     [32m✓[39m base 17
   [33m❯[39m pHash[2m (2)[22m
     [33m⠧[39m should calculate the distance using distanceFromHash
     [90m·[39m should calculate the distance using compareHashes.
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.test.ts[22m[2m (8)[22m
   [32m✓[39m hash[2m (6)[22m[33m 3886[2mms[22m[39m
     [32m✓[39m base 2[33m 314[2mms[22m[39m
     [32m✓[39m base 10 (decimal)
     [32m✓[39m base 16 (hex)[33m 459[2mms[22m[39m
     [32m✓[39m base 64
     [32m✓[39m base 23[33m 2609[2mms[22m[39m
     [32m✓[39m base 17
   [33m❯[39m pHash[2m (2)[22m
     [33m⠇[39m should calculate the distance using distanceFromHash
     [90m·[39m should calculate the distance using compareHashes.
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.test.ts[22m[2m (8)[22m
   [32m✓[39m hash[2m (6)[22m[33m 3886[2mms[22m[39m
     [32m✓[39m base 2[33m 314[2mms[22m[39m
     [32m✓[39m base 10 (decimal)
     [32m✓[39m base 16 (hex)[33m 459[2mms[22m[39m
     [32m✓[39m base 64
     [32m✓[39m base 23[33m 2609[2mms[22m[39m
     [32m✓[39m base 17
   [33m❯[39m pHash[2m (2)[22m
     [33m⠏[39m should calculate the distance using distanceFromHash
     [90m·[39m should calculate the distance using compareHashes.
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.test.ts[22m[2m (8)[22m
   [32m✓[39m hash[2m (6)[22m[33m 3886[2mms[22m[39m
     [32m✓[39m base 2[33m 314[2mms[22m[39m
     [32m✓[39m base 10 (decimal)
     [32m✓[39m base 16 (hex)[33m 459[2mms[22m[39m
     [32m✓[39m base 64
     [32m✓[39m base 23[33m 2609[2mms[22m[39m
     [32m✓[39m base 17
   [33m❯[39m pHash[2m (2)[22m
     [32m✓[39m should calculate the distance using distanceFromHash
     [33m⠙[39m should calculate the distance using compareHashes.
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.test.ts[22m[2m (8)[22m
   [32m✓[39m hash[2m (6)[22m[33m 3886[2mms[22m[39m
     [32m✓[39m base 2[33m 314[2mms[22m[39m
     [32m✓[39m base 10 (decimal)
     [32m✓[39m base 16 (hex)[33m 459[2mms[22m[39m
     [32m✓[39m base 64
     [32m✓[39m base 23[33m 2609[2mms[22m[39m
     [32m✓[39m base 17
   [33m❯[39m pHash[2m (2)[22m
     [32m✓[39m should calculate the distance using distanceFromHash
     [33m⠹[39m should calculate the distance using compareHashes.
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.test.ts[22m[2m (8)[22m
   [32m✓[39m hash[2m (6)[22m[33m 3886[2mms[22m[39m
     [32m✓[39m base 2[33m 314[2mms[22m[39m
     [32m✓[39m base 10 (decimal)
     [32m✓[39m base 16 (hex)[33m 459[2mms[22m[39m
     [32m✓[39m base 64
     [32m✓[39m base 23[33m 2609[2mms[22m[39m
     [32m✓[39m base 17
   [33m❯[39m pHash[2m (2)[22m
     [32m✓[39m should calculate the distance using distanceFromHash
     [33m⠸[39m should calculate the distance using compareHashes.
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.test.ts[22m[2m (8)[22m
   [32m✓[39m hash[2m (6)[22m[33m 3886[2mms[22m[39m
     [32m✓[39m base 2[33m 314[2mms[22m[39m
     [32m✓[39m base 10 (decimal)
     [32m✓[39m base 16 (hex)[33m 459[2mms[22m[39m
     [32m✓[39m base 64
     [32m✓[39m base 23[33m 2609[2mms[22m[39m
     [32m✓[39m base 17
   [33m❯[39m pHash[2m (2)[22m
     [32m✓[39m should calculate the distance using distanceFromHash
     [33m⠼[39m should calculate the distance using compareHashes.
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.test.ts[22m[2m (8)[22m
   [32m✓[39m hash[2m (6)[22m[33m 3886[2mms[22m[39m
     [32m✓[39m base 2[33m 314[2mms[22m[39m
     [32m✓[39m base 10 (decimal)
     [32m✓[39m base 16 (hex)[33m 459[2mms[22m[39m
     [32m✓[39m base 64
     [32m✓[39m base 23[33m 2609[2mms[22m[39m
     [32m✓[39m base 17
   [33m❯[39m pHash[2m (2)[22m
     [32m✓[39m should calculate the distance using distanceFromHash
     [33m⠴[39m should calculate the distance using compareHashes.
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.test.ts[22m[2m (8)[22m
   [32m✓[39m hash[2m (6)[22m[33m 3886[2mms[22m[39m
     [32m✓[39m base 2[33m 314[2mms[22m[39m
     [32m✓[39m base 10 (decimal)
     [32m✓[39m base 16 (hex)[33m 459[2mms[22m[39m
     [32m✓[39m base 64
     [32m✓[39m base 23[33m 2609[2mms[22m[39m
     [32m✓[39m base 17
   [33m❯[39m pHash[2m (2)[22m
     [32m✓[39m should calculate the distance using distanceFromHash
     [33m⠦[39m should calculate the distance using compareHashes.
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (8)[22m[33m 4169[2mms[22m[39m
   [32m✓[39m hash[2m (6)[22m[33m 3886[2mms[22m[39m
     [32m✓[39m base 2[33m 314[2mms[22m[39m
     [32m✓[39m base 10 (decimal)
     [32m✓[39m base 16 (hex)[33m 459[2mms[22m[39m
     [32m✓[39m base 64
     [32m✓[39m base 23[33m 2609[2mms[22m[39m
     [32m✓[39m base 17
   [32m✓[39m pHash[2m (2)[22m
     [32m✓[39m should calculate the distance using distanceFromHash
     [32m✓[39m should calculate the distance using compareHashes.
[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (8)[22m[33m 4169[2mms[22m[39m
   [32m✓[39m hash[2m (6)[22m[33m 3886[2mms[22m[39m
     [32m✓[39m base 2[33m 314[2mms[22m[39m
     [32m✓[39m base 10 (decimal)
     [32m✓[39m base 16 (hex)[33m 459[2mms[22m[39m
     [32m✓[39m base 64
     [32m✓[39m base 23[33m 2609[2mms[22m[39m
     [32m✓[39m base 17
   [32m✓[39m pHash[2m (2)[22m
     [32m✓[39m should calculate the distance using distanceFromHash
     [32m✓[39m should calculate the distance using compareHashes.

[2m Test Files [22m [1m[32m1 passed[39m[22m[90m (1)[39m
[2m      Tests [22m [1m[32m8 passed[39m[22m[90m (8)[39m
[2m   Start at [22m 01:23:24
[2m   Duration [22m 6.14s[2m (transform 557ms, setup 0ms, collect 1.13s, tests 4.17s, environment 0ms, prepare 225ms)[22m

[?25h[?25h
