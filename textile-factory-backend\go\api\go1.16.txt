pkg archive/zip, method (*ReadCloser) Open(string) (fs.File, error)
pkg archive/zip, method (*Reader) Open(string) (fs.File, error)
pkg crypto/x509, method (SystemRootsError) Unwrap() error
pkg debug/elf, const DT_ADDRRNGHI = 1879047935
pkg debug/elf, const DT_ADDRRNGHI DynTag
pkg debug/elf, const DT_ADDRRNGLO = 1879047680
pkg debug/elf, const DT_ADDRRNGLO DynTag
pkg debug/elf, const DT_AUDIT = 1879047932
pkg debug/elf, const DT_AUDIT DynTag
pkg debug/elf, const DT_AUXILIARY = 2147483645
pkg debug/elf, const DT_AUXILIARY DynTag
pkg debug/elf, const DT_CHECKSUM = 1879047672
pkg debug/elf, const DT_CHECKSUM DynTag
pkg debug/elf, const DT_CONFIG = 1879047930
pkg debug/elf, const DT_CONFIG DynTag
pkg debug/elf, const DT_DEPAUDIT = 1879047931
pkg debug/elf, const DT_DEPAUDIT DynTag
pkg debug/elf, const DT_FEATURE = 1879047676
pkg debug/elf, const DT_FEATURE DynTag
pkg debug/elf, const DT_FILTER = 2147483647
pkg debug/elf, const DT_FILTER DynTag
pkg debug/elf, const DT_FLAGS_1 = 1879048187
pkg debug/elf, const DT_FLAGS_1 DynTag
pkg debug/elf, const DT_GNU_CONFLICT = 1879047928
pkg debug/elf, const DT_GNU_CONFLICT DynTag
pkg debug/elf, const DT_GNU_CONFLICTSZ = 1879047670
pkg debug/elf, const DT_GNU_CONFLICTSZ DynTag
pkg debug/elf, const DT_GNU_HASH = 1879047925
pkg debug/elf, const DT_GNU_HASH DynTag
pkg debug/elf, const DT_GNU_LIBLIST = 1879047929
pkg debug/elf, const DT_GNU_LIBLIST DynTag
pkg debug/elf, const DT_GNU_LIBLISTSZ = 1879047671
pkg debug/elf, const DT_GNU_LIBLISTSZ DynTag
pkg debug/elf, const DT_GNU_PRELINKED = 1879047669
pkg debug/elf, const DT_GNU_PRELINKED DynTag
pkg debug/elf, const DT_MIPS_AUX_DYNAMIC = 1879048241
pkg debug/elf, const DT_MIPS_AUX_DYNAMIC DynTag
pkg debug/elf, const DT_MIPS_BASE_ADDRESS = 1879048198
pkg debug/elf, const DT_MIPS_BASE_ADDRESS DynTag
pkg debug/elf, const DT_MIPS_COMPACT_SIZE = 1879048239
pkg debug/elf, const DT_MIPS_COMPACT_SIZE DynTag
pkg debug/elf, const DT_MIPS_CONFLICT = 1879048200
pkg debug/elf, const DT_MIPS_CONFLICT DynTag
pkg debug/elf, const DT_MIPS_CONFLICTNO = 1879048203
pkg debug/elf, const DT_MIPS_CONFLICTNO DynTag
pkg debug/elf, const DT_MIPS_CXX_FLAGS = 1879048226
pkg debug/elf, const DT_MIPS_CXX_FLAGS DynTag
pkg debug/elf, const DT_MIPS_DELTA_CLASS = 1879048215
pkg debug/elf, const DT_MIPS_DELTA_CLASS DynTag
pkg debug/elf, const DT_MIPS_DELTA_CLASSSYM = 1879048224
pkg debug/elf, const DT_MIPS_DELTA_CLASSSYM DynTag
pkg debug/elf, const DT_MIPS_DELTA_CLASSSYM_NO = 1879048225
pkg debug/elf, const DT_MIPS_DELTA_CLASSSYM_NO DynTag
pkg debug/elf, const DT_MIPS_DELTA_CLASS_NO = 1879048216
pkg debug/elf, const DT_MIPS_DELTA_CLASS_NO DynTag
pkg debug/elf, const DT_MIPS_DELTA_INSTANCE = 1879048217
pkg debug/elf, const DT_MIPS_DELTA_INSTANCE DynTag
pkg debug/elf, const DT_MIPS_DELTA_INSTANCE_NO = 1879048218
pkg debug/elf, const DT_MIPS_DELTA_INSTANCE_NO DynTag
pkg debug/elf, const DT_MIPS_DELTA_RELOC = 1879048219
pkg debug/elf, const DT_MIPS_DELTA_RELOC DynTag
pkg debug/elf, const DT_MIPS_DELTA_RELOC_NO = 1879048220
pkg debug/elf, const DT_MIPS_DELTA_RELOC_NO DynTag
pkg debug/elf, const DT_MIPS_DELTA_SYM = 1879048221
pkg debug/elf, const DT_MIPS_DELTA_SYM DynTag
pkg debug/elf, const DT_MIPS_DELTA_SYM_NO = 1879048222
pkg debug/elf, const DT_MIPS_DELTA_SYM_NO DynTag
pkg debug/elf, const DT_MIPS_DYNSTR_ALIGN = 1879048235
pkg debug/elf, const DT_MIPS_DYNSTR_ALIGN DynTag
pkg debug/elf, const DT_MIPS_FLAGS = 1879048197
pkg debug/elf, const DT_MIPS_FLAGS DynTag
pkg debug/elf, const DT_MIPS_GOTSYM = 1879048211
pkg debug/elf, const DT_MIPS_GOTSYM DynTag
pkg debug/elf, const DT_MIPS_GP_VALUE = 1879048240
pkg debug/elf, const DT_MIPS_GP_VALUE DynTag
pkg debug/elf, const DT_MIPS_HIDDEN_GOTIDX = 1879048231
pkg debug/elf, const DT_MIPS_HIDDEN_GOTIDX DynTag
pkg debug/elf, const DT_MIPS_HIPAGENO = 1879048212
pkg debug/elf, const DT_MIPS_HIPAGENO DynTag
pkg debug/elf, const DT_MIPS_ICHECKSUM = 1879048195
pkg debug/elf, const DT_MIPS_ICHECKSUM DynTag
pkg debug/elf, const DT_MIPS_INTERFACE = 1879048234
pkg debug/elf, const DT_MIPS_INTERFACE DynTag
pkg debug/elf, const DT_MIPS_INTERFACE_SIZE = 1879048236
pkg debug/elf, const DT_MIPS_INTERFACE_SIZE DynTag
pkg debug/elf, const DT_MIPS_IVERSION = 1879048196
pkg debug/elf, const DT_MIPS_IVERSION DynTag
pkg debug/elf, const DT_MIPS_LIBLIST = 1879048201
pkg debug/elf, const DT_MIPS_LIBLIST DynTag
pkg debug/elf, const DT_MIPS_LIBLISTNO = 1879048208
pkg debug/elf, const DT_MIPS_LIBLISTNO DynTag
pkg debug/elf, const DT_MIPS_LOCALPAGE_GOTIDX = 1879048229
pkg debug/elf, const DT_MIPS_LOCALPAGE_GOTIDX DynTag
pkg debug/elf, const DT_MIPS_LOCAL_GOTIDX = 1879048230
pkg debug/elf, const DT_MIPS_LOCAL_GOTIDX DynTag
pkg debug/elf, const DT_MIPS_LOCAL_GOTNO = 1879048202
pkg debug/elf, const DT_MIPS_LOCAL_GOTNO DynTag
pkg debug/elf, const DT_MIPS_MSYM = 1879048199
pkg debug/elf, const DT_MIPS_MSYM DynTag
pkg debug/elf, const DT_MIPS_OPTIONS = 1879048233
pkg debug/elf, const DT_MIPS_OPTIONS DynTag
pkg debug/elf, const DT_MIPS_PERF_SUFFIX = 1879048238
pkg debug/elf, const DT_MIPS_PERF_SUFFIX DynTag
pkg debug/elf, const DT_MIPS_PIXIE_INIT = 1879048227
pkg debug/elf, const DT_MIPS_PIXIE_INIT DynTag
pkg debug/elf, const DT_MIPS_PLTGOT = 1879048242
pkg debug/elf, const DT_MIPS_PLTGOT DynTag
pkg debug/elf, const DT_MIPS_PROTECTED_GOTIDX = 1879048232
pkg debug/elf, const DT_MIPS_PROTECTED_GOTIDX DynTag
pkg debug/elf, const DT_MIPS_RLD_MAP = 1879048214
pkg debug/elf, const DT_MIPS_RLD_MAP DynTag
pkg debug/elf, const DT_MIPS_RLD_MAP_REL = 1879048245
pkg debug/elf, const DT_MIPS_RLD_MAP_REL DynTag
pkg debug/elf, const DT_MIPS_RLD_TEXT_RESOLVE_ADDR = 1879048237
pkg debug/elf, const DT_MIPS_RLD_TEXT_RESOLVE_ADDR DynTag
pkg debug/elf, const DT_MIPS_RLD_VERSION = 1879048193
pkg debug/elf, const DT_MIPS_RLD_VERSION DynTag
pkg debug/elf, const DT_MIPS_RWPLT = 1879048244
pkg debug/elf, const DT_MIPS_RWPLT DynTag
pkg debug/elf, const DT_MIPS_SYMBOL_LIB = 1879048228
pkg debug/elf, const DT_MIPS_SYMBOL_LIB DynTag
pkg debug/elf, const DT_MIPS_SYMTABNO = 1879048209
pkg debug/elf, const DT_MIPS_SYMTABNO DynTag
pkg debug/elf, const DT_MIPS_TIME_STAMP = 1879048194
pkg debug/elf, const DT_MIPS_TIME_STAMP DynTag
pkg debug/elf, const DT_MIPS_UNREFEXTNO = 1879048210
pkg debug/elf, const DT_MIPS_UNREFEXTNO DynTag
pkg debug/elf, const DT_MOVEENT = 1879047674
pkg debug/elf, const DT_MOVEENT DynTag
pkg debug/elf, const DT_MOVESZ = 1879047675
pkg debug/elf, const DT_MOVESZ DynTag
pkg debug/elf, const DT_MOVETAB = 1879047934
pkg debug/elf, const DT_MOVETAB DynTag
pkg debug/elf, const DT_PLTPAD = 1879047933
pkg debug/elf, const DT_PLTPAD DynTag
pkg debug/elf, const DT_PLTPADSZ = 1879047673
pkg debug/elf, const DT_PLTPADSZ DynTag
pkg debug/elf, const DT_POSFLAG_1 = 1879047677
pkg debug/elf, const DT_POSFLAG_1 DynTag
pkg debug/elf, const DT_PPC64_GLINK = 1879048192
pkg debug/elf, const DT_PPC64_GLINK DynTag
pkg debug/elf, const DT_PPC64_OPD = 1879048193
pkg debug/elf, const DT_PPC64_OPD DynTag
pkg debug/elf, const DT_PPC64_OPDSZ = 1879048194
pkg debug/elf, const DT_PPC64_OPDSZ DynTag
pkg debug/elf, const DT_PPC64_OPT = 1879048195
pkg debug/elf, const DT_PPC64_OPT DynTag
pkg debug/elf, const DT_PPC_GOT = 1879048192
pkg debug/elf, const DT_PPC_GOT DynTag
pkg debug/elf, const DT_PPC_OPT = 1879048193
pkg debug/elf, const DT_PPC_OPT DynTag
pkg debug/elf, const DT_RELACOUNT = 1879048185
pkg debug/elf, const DT_RELACOUNT DynTag
pkg debug/elf, const DT_RELCOUNT = 1879048186
pkg debug/elf, const DT_RELCOUNT DynTag
pkg debug/elf, const DT_SPARC_REGISTER = 1879048193
pkg debug/elf, const DT_SPARC_REGISTER DynTag
pkg debug/elf, const DT_SYMINENT = 1879047679
pkg debug/elf, const DT_SYMINENT DynTag
pkg debug/elf, const DT_SYMINFO = 1879047935
pkg debug/elf, const DT_SYMINFO DynTag
pkg debug/elf, const DT_SYMINSZ = 1879047678
pkg debug/elf, const DT_SYMINSZ DynTag
pkg debug/elf, const DT_SYMTAB_SHNDX = 34
pkg debug/elf, const DT_SYMTAB_SHNDX DynTag
pkg debug/elf, const DT_TLSDESC_GOT = 1879047927
pkg debug/elf, const DT_TLSDESC_GOT DynTag
pkg debug/elf, const DT_TLSDESC_PLT = 1879047926
pkg debug/elf, const DT_TLSDESC_PLT DynTag
pkg debug/elf, const DT_USED = 2147483646
pkg debug/elf, const DT_USED DynTag
pkg debug/elf, const DT_VALRNGHI = 1879047679
pkg debug/elf, const DT_VALRNGHI DynTag
pkg debug/elf, const DT_VALRNGLO = 1879047424
pkg debug/elf, const DT_VALRNGLO DynTag
pkg debug/elf, const DT_VERDEF = 1879048188
pkg debug/elf, const DT_VERDEF DynTag
pkg debug/elf, const DT_VERDEFNUM = 1879048189
pkg debug/elf, const DT_VERDEFNUM DynTag
pkg debug/elf, const PT_AARCH64_ARCHEXT = 1879048192
pkg debug/elf, const PT_AARCH64_ARCHEXT ProgType
pkg debug/elf, const PT_AARCH64_UNWIND = 1879048193
pkg debug/elf, const PT_AARCH64_UNWIND ProgType
pkg debug/elf, const PT_ARM_ARCHEXT = 1879048192
pkg debug/elf, const PT_ARM_ARCHEXT ProgType
pkg debug/elf, const PT_ARM_EXIDX = 1879048193
pkg debug/elf, const PT_ARM_EXIDX ProgType
pkg debug/elf, const PT_GNU_EH_FRAME = 1685382480
pkg debug/elf, const PT_GNU_EH_FRAME ProgType
pkg debug/elf, const PT_GNU_MBIND_HI = 1685386580
pkg debug/elf, const PT_GNU_MBIND_HI ProgType
pkg debug/elf, const PT_GNU_MBIND_LO = 1685382485
pkg debug/elf, const PT_GNU_MBIND_LO ProgType
pkg debug/elf, const PT_GNU_PROPERTY = 1685382483
pkg debug/elf, const PT_GNU_PROPERTY ProgType
pkg debug/elf, const PT_GNU_RELRO = 1685382482
pkg debug/elf, const PT_GNU_RELRO ProgType
pkg debug/elf, const PT_GNU_STACK = 1685382481
pkg debug/elf, const PT_GNU_STACK ProgType
pkg debug/elf, const PT_MIPS_ABIFLAGS = 1879048195
pkg debug/elf, const PT_MIPS_ABIFLAGS ProgType
pkg debug/elf, const PT_MIPS_OPTIONS = 1879048194
pkg debug/elf, const PT_MIPS_OPTIONS ProgType
pkg debug/elf, const PT_MIPS_REGINFO = 1879048192
pkg debug/elf, const PT_MIPS_REGINFO ProgType
pkg debug/elf, const PT_MIPS_RTPROC = 1879048193
pkg debug/elf, const PT_MIPS_RTPROC ProgType
pkg debug/elf, const PT_OPENBSD_BOOTDATA = 1705253862
pkg debug/elf, const PT_OPENBSD_BOOTDATA ProgType
pkg debug/elf, const PT_OPENBSD_RANDOMIZE = 1705237478
pkg debug/elf, const PT_OPENBSD_RANDOMIZE ProgType
pkg debug/elf, const PT_OPENBSD_WXNEEDED = 1705237479
pkg debug/elf, const PT_OPENBSD_WXNEEDED ProgType
pkg debug/elf, const PT_PAX_FLAGS = 1694766464
pkg debug/elf, const PT_PAX_FLAGS ProgType
pkg debug/elf, const PT_S390_PGSTE = 1879048192
pkg debug/elf, const PT_S390_PGSTE ProgType
pkg debug/elf, const PT_SUNWSTACK = 1879048187
pkg debug/elf, const PT_SUNWSTACK ProgType
pkg debug/elf, const PT_SUNW_EH_FRAME = 1685382480
pkg debug/elf, const PT_SUNW_EH_FRAME ProgType
pkg embed, method (FS) Open(string) (fs.File, error)
pkg embed, method (FS) ReadDir(string) ([]fs.DirEntry, error)
pkg embed, method (FS) ReadFile(string) ([]uint8, error)
pkg embed, type FS struct
pkg flag, func Func(string, string, func(string) error)
pkg flag, method (*FlagSet) Func(string, string, func(string) error)
pkg go/build, type Package struct, EmbedPatterns []string
pkg go/build, type Package struct, EmbedPatternPos map[string][]token.Position
pkg go/build, type Package struct, IgnoredOtherFiles []string
pkg go/build, type Package struct, TestEmbedPatterns []string
pkg go/build, type Package struct, TestEmbedPatternPos map[string][]token.Position
pkg go/build, type Package struct, XTestEmbedPatterns []string
pkg go/build, type Package struct, XTestEmbedPatternPos map[string][]token.Position
pkg go/build/constraint, func IsGoBuild(string) bool
pkg go/build/constraint, func IsPlusBuild(string) bool
pkg go/build/constraint, func Parse(string) (Expr, error)
pkg go/build/constraint, func PlusBuildLines(Expr) ([]string, error)
pkg go/build/constraint, method (*AndExpr) Eval(func(string) bool) bool
pkg go/build/constraint, method (*AndExpr) String() string
pkg go/build/constraint, method (*NotExpr) Eval(func(string) bool) bool
pkg go/build/constraint, method (*NotExpr) String() string
pkg go/build/constraint, method (*OrExpr) Eval(func(string) bool) bool
pkg go/build/constraint, method (*OrExpr) String() string
pkg go/build/constraint, method (*SyntaxError) Error() string
pkg go/build/constraint, method (*TagExpr) Eval(func(string) bool) bool
pkg go/build/constraint, method (*TagExpr) String() string
pkg go/build/constraint, type AndExpr struct
pkg go/build/constraint, type AndExpr struct, X Expr
pkg go/build/constraint, type AndExpr struct, Y Expr
pkg go/build/constraint, type Expr interface, Eval(func(string) bool) bool
pkg go/build/constraint, type Expr interface, String() string
pkg go/build/constraint, type Expr interface, unexported methods
pkg go/build/constraint, type NotExpr struct
pkg go/build/constraint, type NotExpr struct, X Expr
pkg go/build/constraint, type OrExpr struct
pkg go/build/constraint, type OrExpr struct, X Expr
pkg go/build/constraint, type OrExpr struct, Y Expr
pkg go/build/constraint, type SyntaxError struct
pkg go/build/constraint, type SyntaxError struct, Err string
pkg go/build/constraint, type SyntaxError struct, Offset int
pkg go/build/constraint, type TagExpr struct
pkg go/build/constraint, type TagExpr struct, Tag string
pkg html/template, func ParseFS(fs.FS, ...string) (*Template, error)
pkg html/template, method (*Template) ParseFS(fs.FS, ...string) (*Template, error)
pkg io, func NopCloser(Reader) ReadCloser
pkg io, func ReadAll(Reader) ([]uint8, error)
pkg io, type ReadSeekCloser interface { Close, Read, Seek }
pkg io, type ReadSeekCloser interface, Close() error
pkg io, type ReadSeekCloser interface, Read([]uint8) (int, error)
pkg io, type ReadSeekCloser interface, Seek(int64, int) (int64, error)
pkg io, var Discard Writer
pkg io/fs, const ModeAppend = 1073741824
pkg io/fs, const ModeAppend FileMode
pkg io/fs, const ModeCharDevice = 2097152
pkg io/fs, const ModeCharDevice FileMode
pkg io/fs, const ModeDevice = 67108864
pkg io/fs, const ModeDevice FileMode
pkg io/fs, const ModeDir = 2147483648
pkg io/fs, const ModeDir FileMode
pkg io/fs, const ModeExclusive = *********
pkg io/fs, const ModeExclusive FileMode
pkg io/fs, const ModeIrregular = 524288
pkg io/fs, const ModeIrregular FileMode
pkg io/fs, const ModeNamedPipe = 33554432
pkg io/fs, const ModeNamedPipe FileMode
pkg io/fs, const ModePerm = 511
pkg io/fs, const ModePerm FileMode
pkg io/fs, const ModeSetgid = 4194304
pkg io/fs, const ModeSetgid FileMode
pkg io/fs, const ModeSetuid = 8388608
pkg io/fs, const ModeSetuid FileMode
pkg io/fs, const ModeSocket = 16777216
pkg io/fs, const ModeSocket FileMode
pkg io/fs, const ModeSticky = 1048576
pkg io/fs, const ModeSticky FileMode
pkg io/fs, const ModeSymlink = 134217728
pkg io/fs, const ModeSymlink FileMode
pkg io/fs, const ModeTemporary = 268435456
pkg io/fs, const ModeTemporary FileMode
pkg io/fs, const ModeType = 2401763328
pkg io/fs, const ModeType FileMode
pkg io/fs, func Glob(FS, string) ([]string, error)
pkg io/fs, func ReadDir(FS, string) ([]DirEntry, error)
pkg io/fs, func ReadFile(FS, string) ([]uint8, error)
pkg io/fs, func Stat(FS, string) (FileInfo, error)
pkg io/fs, func Sub(FS, string) (FS, error)
pkg io/fs, func ValidPath(string) bool
pkg io/fs, func WalkDir(FS, string, WalkDirFunc) error
pkg io/fs, method (*PathError) Error() string
pkg io/fs, method (*PathError) Timeout() bool
pkg io/fs, method (*PathError) Unwrap() error
pkg io/fs, method (FileMode) IsDir() bool
pkg io/fs, method (FileMode) IsRegular() bool
pkg io/fs, method (FileMode) Perm() FileMode
pkg io/fs, method (FileMode) String() string
pkg io/fs, method (FileMode) Type() FileMode
pkg io/fs, type DirEntry interface { Info, IsDir, Name, Type }
pkg io/fs, type DirEntry interface, Info() (FileInfo, error)
pkg io/fs, type DirEntry interface, IsDir() bool
pkg io/fs, type DirEntry interface, Name() string
pkg io/fs, type DirEntry interface, Type() FileMode
pkg io/fs, type FS interface { Open }
pkg io/fs, type FS interface, Open(string) (File, error)
pkg io/fs, type File interface { Close, Read, Stat }
pkg io/fs, type File interface, Close() error
pkg io/fs, type File interface, Read([]uint8) (int, error)
pkg io/fs, type File interface, Stat() (FileInfo, error)
pkg io/fs, type FileInfo interface { IsDir, ModTime, Mode, Name, Size, Sys }
pkg io/fs, type FileInfo interface, IsDir() bool
pkg io/fs, type FileInfo interface, ModTime() time.Time
pkg io/fs, type FileInfo interface, Mode() FileMode
pkg io/fs, type FileInfo interface, Name() string
pkg io/fs, type FileInfo interface, Size() int64
pkg io/fs, type FileInfo interface, Sys() interface{}
pkg io/fs, type FileMode uint32
pkg io/fs, type GlobFS interface { Glob, Open }
pkg io/fs, type GlobFS interface, Glob(string) ([]string, error)
pkg io/fs, type GlobFS interface, Open(string) (File, error)
pkg io/fs, type PathError struct
pkg io/fs, type PathError struct, Err error
pkg io/fs, type PathError struct, Op string
pkg io/fs, type PathError struct, Path string
pkg io/fs, type ReadDirFS interface { Open, ReadDir }
pkg io/fs, type ReadDirFS interface, Open(string) (File, error)
pkg io/fs, type ReadDirFS interface, ReadDir(string) ([]DirEntry, error)
pkg io/fs, type ReadDirFile interface { Close, Read, ReadDir, Stat }
pkg io/fs, type ReadDirFile interface, Close() error
pkg io/fs, type ReadDirFile interface, Read([]uint8) (int, error)
pkg io/fs, type ReadDirFile interface, ReadDir(int) ([]DirEntry, error)
pkg io/fs, type ReadDirFile interface, Stat() (FileInfo, error)
pkg io/fs, type ReadFileFS interface { Open, ReadFile }
pkg io/fs, type ReadFileFS interface, Open(string) (File, error)
pkg io/fs, type ReadFileFS interface, ReadFile(string) ([]uint8, error)
pkg io/fs, type StatFS interface { Open, Stat }
pkg io/fs, type StatFS interface, Open(string) (File, error)
pkg io/fs, type StatFS interface, Stat(string) (FileInfo, error)
pkg io/fs, type SubFS interface { Open, Sub }
pkg io/fs, type SubFS interface, Open(string) (File, error)
pkg io/fs, type SubFS interface, Sub(string) (FS, error)
pkg io/fs, type WalkDirFunc func(string, DirEntry, error) error
pkg io/fs, var ErrClosed error
pkg io/fs, var ErrExist error
pkg io/fs, var ErrInvalid error
pkg io/fs, var ErrNotExist error
pkg io/fs, var ErrPermission error
pkg io/fs, var SkipDir error
pkg log, func Default() *Logger
pkg net, var ErrClosed error
pkg net/http, func FS(fs.FS) FileSystem
pkg net/http, type Transport struct, GetProxyConnectHeader func(context.Context, *url.URL, string) (Header, error)
pkg os, const ModeAppend fs.FileMode
pkg os, const ModeCharDevice fs.FileMode
pkg os, const ModeDevice fs.FileMode
pkg os, const ModeDir fs.FileMode
pkg os, const ModeExclusive fs.FileMode
pkg os, const ModeIrregular fs.FileMode
pkg os, const ModeNamedPipe fs.FileMode
pkg os, const ModePerm fs.FileMode
pkg os, const ModeSetgid fs.FileMode
pkg os, const ModeSetuid fs.FileMode
pkg os, const ModeSocket fs.FileMode
pkg os, const ModeSticky fs.FileMode
pkg os, const ModeSymlink fs.FileMode
pkg os, const ModeTemporary fs.FileMode
pkg os, const ModeType fs.FileMode
pkg os, func Chmod(string, fs.FileMode) error
pkg os, func CreateTemp(string, string) (*File, error)
pkg os, func DirFS(string) fs.FS
pkg os, func Lstat(string) (fs.FileInfo, error)
pkg os, func Mkdir(string, fs.FileMode) error
pkg os, func MkdirAll(string, fs.FileMode) error
pkg os, func MkdirTemp(string, string) (string, error)
pkg os, func OpenFile(string, int, fs.FileMode) (*File, error)
pkg os, func ReadDir(string) ([]fs.DirEntry, error)
pkg os, func ReadFile(string) ([]uint8, error)
pkg os, func SameFile(fs.FileInfo, fs.FileInfo) bool
pkg os, func Stat(string) (fs.FileInfo, error)
pkg os, func WriteFile(string, []uint8, fs.FileMode) error
pkg os, method (*File) Chmod(fs.FileMode) error
pkg os, method (*File) ReadDir(int) ([]fs.DirEntry, error)
pkg os, method (*File) Readdir(int) ([]fs.FileInfo, error)
pkg os, method (*File) Stat() (fs.FileInfo, error)
pkg os, type DirEntry = fs.DirEntry
pkg os, type FileInfo = fs.FileInfo
pkg os, type FileMode = fs.FileMode
pkg os, type PathError = fs.PathError
pkg os, var ErrProcessDone error
pkg os/signal, func NotifyContext(context.Context, ...os.Signal) (context.Context, context.CancelFunc)
pkg path/filepath, func WalkDir(string, fs.WalkDirFunc) error
pkg runtime/metrics, const KindBad = 0
pkg runtime/metrics, const KindBad ValueKind
pkg runtime/metrics, const KindFloat64 = 2
pkg runtime/metrics, const KindFloat64 ValueKind
pkg runtime/metrics, const KindFloat64Histogram = 3
pkg runtime/metrics, const KindFloat64Histogram ValueKind
pkg runtime/metrics, const KindUint64 = 1
pkg runtime/metrics, const KindUint64 ValueKind
pkg runtime/metrics, func All() []Description
pkg runtime/metrics, func Read([]Sample)
pkg runtime/metrics, method (Value) Float64() float64
pkg runtime/metrics, method (Value) Float64Histogram() *Float64Histogram
pkg runtime/metrics, method (Value) Kind() ValueKind
pkg runtime/metrics, method (Value) Uint64() uint64
pkg runtime/metrics, type Description struct
pkg runtime/metrics, type Description struct, Cumulative bool
pkg runtime/metrics, type Description struct, Description string
pkg runtime/metrics, type Description struct, Kind ValueKind
pkg runtime/metrics, type Description struct, Name string
pkg runtime/metrics, type Float64Histogram struct
pkg runtime/metrics, type Float64Histogram struct, Buckets []float64
pkg runtime/metrics, type Float64Histogram struct, Counts []uint64
pkg runtime/metrics, type Sample struct
pkg runtime/metrics, type Sample struct, Name string
pkg runtime/metrics, type Sample struct, Value Value
pkg runtime/metrics, type Value struct
pkg runtime/metrics, type ValueKind int
pkg syscall (linux-386), func AllThreadsSyscall(uintptr, uintptr, uintptr, uintptr) (uintptr, uintptr, Errno)
pkg syscall (linux-386), func AllThreadsSyscall6(uintptr, uintptr, uintptr, uintptr, uintptr, uintptr, uintptr) (uintptr, uintptr, Errno)
pkg syscall (linux-386), func Setegid(int) error
pkg syscall (linux-386), func Seteuid(int) error
pkg syscall (linux-386-cgo), func AllThreadsSyscall(uintptr, uintptr, uintptr, uintptr) (uintptr, uintptr, Errno)
pkg syscall (linux-386-cgo), func AllThreadsSyscall6(uintptr, uintptr, uintptr, uintptr, uintptr, uintptr, uintptr) (uintptr, uintptr, Errno)
pkg syscall (linux-386-cgo), func Setegid(int) error
pkg syscall (linux-386-cgo), func Seteuid(int) error
pkg syscall (linux-amd64), func AllThreadsSyscall(uintptr, uintptr, uintptr, uintptr) (uintptr, uintptr, Errno)
pkg syscall (linux-amd64), func AllThreadsSyscall6(uintptr, uintptr, uintptr, uintptr, uintptr, uintptr, uintptr) (uintptr, uintptr, Errno)
pkg syscall (linux-amd64), func Setegid(int) error
pkg syscall (linux-amd64), func Seteuid(int) error
pkg syscall (linux-amd64-cgo), func AllThreadsSyscall(uintptr, uintptr, uintptr, uintptr) (uintptr, uintptr, Errno)
pkg syscall (linux-amd64-cgo), func AllThreadsSyscall6(uintptr, uintptr, uintptr, uintptr, uintptr, uintptr, uintptr) (uintptr, uintptr, Errno)
pkg syscall (linux-amd64-cgo), func Setegid(int) error
pkg syscall (linux-amd64-cgo), func Seteuid(int) error
pkg syscall (linux-arm), func AllThreadsSyscall(uintptr, uintptr, uintptr, uintptr) (uintptr, uintptr, Errno)
pkg syscall (linux-arm), func AllThreadsSyscall6(uintptr, uintptr, uintptr, uintptr, uintptr, uintptr, uintptr) (uintptr, uintptr, Errno)
pkg syscall (linux-arm), func Setegid(int) error
pkg syscall (linux-arm), func Seteuid(int) error
pkg syscall (linux-arm-cgo), func AllThreadsSyscall(uintptr, uintptr, uintptr, uintptr) (uintptr, uintptr, Errno)
pkg syscall (linux-arm-cgo), func AllThreadsSyscall6(uintptr, uintptr, uintptr, uintptr, uintptr, uintptr, uintptr) (uintptr, uintptr, Errno)
pkg syscall (linux-arm-cgo), func Setegid(int) error
pkg syscall (linux-arm-cgo), func Seteuid(int) error
pkg syscall (windows-386), method (*DLLError) Unwrap() error
pkg syscall (windows-386), type SysProcAttr struct, NoInheritHandles bool
pkg syscall (windows-amd64), method (*DLLError) Unwrap() error
pkg syscall (windows-amd64), type SysProcAttr struct, NoInheritHandles bool
pkg testing/fstest, func TestFS(fs.FS, ...string) error
pkg testing/fstest, method (MapFS) Glob(string) ([]string, error)
pkg testing/fstest, method (MapFS) Open(string) (fs.File, error)
pkg testing/fstest, method (MapFS) ReadDir(string) ([]fs.DirEntry, error)
pkg testing/fstest, method (MapFS) ReadFile(string) ([]uint8, error)
pkg testing/fstest, method (MapFS) Stat(string) (fs.FileInfo, error)
pkg testing/fstest, method (MapFS) Sub(string) (fs.FS, error)
pkg testing/fstest, type MapFS map[string]*MapFile
pkg testing/fstest, type MapFile struct
pkg testing/fstest, type MapFile struct, Data []uint8
pkg testing/fstest, type MapFile struct, ModTime time.Time
pkg testing/fstest, type MapFile struct, Mode fs.FileMode
pkg testing/fstest, type MapFile struct, Sys interface{}
pkg testing/iotest, func ErrReader(error) io.Reader
pkg testing/iotest, func TestReader(io.Reader, []uint8) error
pkg text/template, func ParseFS(fs.FS, ...string) (*Template, error)
pkg text/template, method (*Template) ParseFS(fs.FS, ...string) (*Template, error)
pkg text/template/parse, const NodeComment = 20
pkg text/template/parse, const NodeComment NodeType
pkg text/template/parse, const ParseComments = 1
pkg text/template/parse, const ParseComments Mode
pkg text/template/parse, method (*CommentNode) Copy() Node
pkg text/template/parse, method (*CommentNode) String() string
pkg text/template/parse, method (CommentNode) Position() Pos
pkg text/template/parse, method (CommentNode) Type() NodeType
pkg text/template/parse, type CommentNode struct
pkg text/template/parse, type CommentNode struct, Text string
pkg text/template/parse, type CommentNode struct, embedded NodeType
pkg text/template/parse, type CommentNode struct, embedded Pos
pkg text/template/parse, type Mode uint
pkg text/template/parse, type Tree struct, Mode Mode
pkg unicode, const Version = "13.0.0"
pkg unicode, var Chorasmian *RangeTable
pkg unicode, var Dives_Akuru *RangeTable
pkg unicode, var Khitan_Small_Script *RangeTable
pkg unicode, var Yezidi *RangeTable
# all deprecations up to and including Go 1.16
pkg archive/tar, const TypeRegA //deprecated
pkg archive/tar, type Header struct, Xattrs //deprecated
pkg archive/zip, method (*File) ModTime //deprecated
pkg archive/zip, method (*File) SetModTime //deprecated
pkg archive/zip, method (*FileHeader) ModTime //deprecated
pkg archive/zip, method (*FileHeader) SetModTime //deprecated
pkg archive/zip, type FileHeader struct, CompressedSize //deprecated
pkg archive/zip, type FileHeader struct, ModifiedDate //deprecated
pkg archive/zip, type FileHeader struct, ModifiedTime //deprecated
pkg archive/zip, type FileHeader struct, UncompressedSize //deprecated
pkg compress/flate, type ReadError //deprecated
pkg compress/flate, type WriteError //deprecated
pkg crypto/rc4, method (*Cipher) Reset //deprecated
pkg crypto/tls, const VersionSSL30 //deprecated
pkg crypto/tls, method (*Config) BuildNameToCertificate //deprecated
pkg crypto/tls, type Config struct, NameToCertificate //deprecated
pkg crypto/tls, type Config struct, SessionTicketKey //deprecated
pkg crypto/tls, type ConnectionState struct, NegotiatedProtocolIsMutual //deprecated
pkg crypto/tls, type ConnectionState struct, TLSUnique //deprecated
pkg crypto/x509, func DecryptPEMBlock //deprecated
pkg crypto/x509, func EncryptPEMBlock //deprecated
pkg crypto/x509, func IsEncryptedPEMBlock //deprecated
pkg crypto/x509, type CertificateRequest struct, Attributes //deprecated
pkg database/sql/driver, type ColumnConverter //deprecated
pkg database/sql/driver, type Conn interface, Begin //deprecated
pkg database/sql/driver, type Execer //deprecated
pkg database/sql/driver, type Queryer //deprecated
pkg database/sql/driver, type Stmt interface, Exec //deprecated
pkg database/sql/driver, type Stmt interface, Query //deprecated
pkg debug/gosym, method (*LineTable) LineToPC //deprecated
pkg debug/gosym, method (*LineTable) PCToLine //deprecated
pkg encoding/csv, type Reader struct, TrailingComma //deprecated
pkg encoding/csv, var ErrTrailingComma //deprecated
pkg encoding/json, type InvalidUTF8Error //deprecated
pkg encoding/json, type UnmarshalFieldError //deprecated
pkg go/build, const AllowBinary //deprecated
pkg go/doc, type Package struct, Bugs //deprecated
pkg go/importer, func For //deprecated
pkg go/importer, func ForCompiler //deprecated
pkg go/types, func NewInterface //deprecated
pkg go/types, method (*Interface) Embedded //deprecated
pkg image, var ZP //deprecated
pkg image, var ZR //deprecated
pkg image/jpeg, type Reader //deprecated
pkg net, type Dialer struct, Cancel //deprecated
pkg net, type Dialer struct, DualStack //deprecated
pkg net/http, method (*Transport) CancelRequest //deprecated
pkg net/http, type CloseNotifier //deprecated
pkg net/http, type ProtocolError //deprecated
pkg net/http, type Request struct, Cancel //deprecated
pkg net/http, type Transport struct, Dial //deprecated
pkg net/http, type Transport struct, DialTLS //deprecated
pkg net/http, var ErrHeaderTooLong //deprecated
pkg net/http, var ErrMissingContentLength //deprecated
pkg net/http, var ErrShortBody //deprecated
pkg net/http, var ErrUnexpectedTrailer //deprecated
pkg net/http, var ErrWriteAfterFlush //deprecated
pkg net/http/httptest, type ResponseRecorder struct, HeaderMap //deprecated
pkg net/http/httputil, func NewClientConn //deprecated
pkg net/http/httputil, func NewProxyClientConn //deprecated
pkg net/http/httputil, func NewServerConn //deprecated
pkg net/http/httputil, type ClientConn //deprecated
pkg net/http/httputil, type ServerConn //deprecated
pkg net/http/httputil, var ErrClosed //deprecated
pkg net/http/httputil, var ErrPersistEOF //deprecated
pkg net/http/httputil, var ErrPipeline //deprecated
pkg os, const SEEK_CUR //deprecated
pkg os, const SEEK_END //deprecated
pkg os, const SEEK_SET //deprecated
pkg path/filepath, func HasPrefix //deprecated
pkg regexp, method (*Regexp) Copy //deprecated
pkg runtime, func CPUProfile //deprecated
pkg syscall (darwin-amd64), func BpfBuflen //deprecated
pkg syscall (darwin-amd64), func BpfDatalink //deprecated
pkg syscall (darwin-amd64), func BpfHeadercmpl //deprecated
pkg syscall (darwin-amd64), func BpfInterface //deprecated
pkg syscall (darwin-amd64), func BpfJump //deprecated
pkg syscall (darwin-amd64), func BpfStats //deprecated
pkg syscall (darwin-amd64), func BpfStmt //deprecated
pkg syscall (darwin-amd64), func BpfTimeout //deprecated
pkg syscall (darwin-amd64), func CheckBpfVersion //deprecated
pkg syscall (darwin-amd64), func FlushBpf //deprecated
pkg syscall (darwin-amd64), func ParseRoutingMessage //deprecated
pkg syscall (darwin-amd64), func ParseRoutingSockaddr //deprecated
pkg syscall (darwin-amd64), func RouteRIB //deprecated
pkg syscall (darwin-amd64), func SetBpf //deprecated
pkg syscall (darwin-amd64), func SetBpfBuflen //deprecated
pkg syscall (darwin-amd64), func SetBpfDatalink //deprecated
pkg syscall (darwin-amd64), func SetBpfHeadercmpl //deprecated
pkg syscall (darwin-amd64), func SetBpfImmediate //deprecated
pkg syscall (darwin-amd64), func SetBpfInterface //deprecated
pkg syscall (darwin-amd64), func SetBpfPromisc //deprecated
pkg syscall (darwin-amd64), func SetBpfTimeout //deprecated
pkg syscall (darwin-amd64), func StringSlicePtr //deprecated
pkg syscall (darwin-amd64), type InterfaceAddrMessage //deprecated
pkg syscall (darwin-amd64), type InterfaceMessage //deprecated
pkg syscall (darwin-amd64), type InterfaceMulticastAddrMessage //deprecated
pkg syscall (darwin-amd64), type RouteMessage //deprecated
pkg syscall (darwin-amd64), type RoutingMessage //deprecated
pkg syscall (darwin-amd64-cgo), func BpfBuflen //deprecated
pkg syscall (darwin-amd64-cgo), func BpfDatalink //deprecated
pkg syscall (darwin-amd64-cgo), func BpfHeadercmpl //deprecated
pkg syscall (darwin-amd64-cgo), func BpfInterface //deprecated
pkg syscall (darwin-amd64-cgo), func BpfJump //deprecated
pkg syscall (darwin-amd64-cgo), func BpfStats //deprecated
pkg syscall (darwin-amd64-cgo), func BpfStmt //deprecated
pkg syscall (darwin-amd64-cgo), func BpfTimeout //deprecated
pkg syscall (darwin-amd64-cgo), func CheckBpfVersion //deprecated
pkg syscall (darwin-amd64-cgo), func FlushBpf //deprecated
pkg syscall (darwin-amd64-cgo), func ParseRoutingMessage //deprecated
pkg syscall (darwin-amd64-cgo), func ParseRoutingSockaddr //deprecated
pkg syscall (darwin-amd64-cgo), func RouteRIB //deprecated
pkg syscall (darwin-amd64-cgo), func SetBpf //deprecated
pkg syscall (darwin-amd64-cgo), func SetBpfBuflen //deprecated
pkg syscall (darwin-amd64-cgo), func SetBpfDatalink //deprecated
pkg syscall (darwin-amd64-cgo), func SetBpfHeadercmpl //deprecated
pkg syscall (darwin-amd64-cgo), func SetBpfImmediate //deprecated
pkg syscall (darwin-amd64-cgo), func SetBpfInterface //deprecated
pkg syscall (darwin-amd64-cgo), func SetBpfPromisc //deprecated
pkg syscall (darwin-amd64-cgo), func SetBpfTimeout //deprecated
pkg syscall (darwin-amd64-cgo), func StringSlicePtr //deprecated
pkg syscall (darwin-amd64-cgo), type InterfaceAddrMessage //deprecated
pkg syscall (darwin-amd64-cgo), type InterfaceMessage //deprecated
pkg syscall (darwin-amd64-cgo), type InterfaceMulticastAddrMessage //deprecated
pkg syscall (darwin-amd64-cgo), type RouteMessage //deprecated
pkg syscall (darwin-amd64-cgo), type RoutingMessage //deprecated
pkg syscall (freebsd-386), func BpfBuflen //deprecated
pkg syscall (freebsd-386), func BpfDatalink //deprecated
pkg syscall (freebsd-386), func BpfHeadercmpl //deprecated
pkg syscall (freebsd-386), func BpfInterface //deprecated
pkg syscall (freebsd-386), func BpfJump //deprecated
pkg syscall (freebsd-386), func BpfStats //deprecated
pkg syscall (freebsd-386), func BpfStmt //deprecated
pkg syscall (freebsd-386), func BpfTimeout //deprecated
pkg syscall (freebsd-386), func CheckBpfVersion //deprecated
pkg syscall (freebsd-386), func FlushBpf //deprecated
pkg syscall (freebsd-386), func ParseRoutingMessage //deprecated
pkg syscall (freebsd-386), func ParseRoutingSockaddr //deprecated
pkg syscall (freebsd-386), func RouteRIB //deprecated
pkg syscall (freebsd-386), func SetBpf //deprecated
pkg syscall (freebsd-386), func SetBpfBuflen //deprecated
pkg syscall (freebsd-386), func SetBpfDatalink //deprecated
pkg syscall (freebsd-386), func SetBpfHeadercmpl //deprecated
pkg syscall (freebsd-386), func SetBpfImmediate //deprecated
pkg syscall (freebsd-386), func SetBpfInterface //deprecated
pkg syscall (freebsd-386), func SetBpfPromisc //deprecated
pkg syscall (freebsd-386), func SetBpfTimeout //deprecated
pkg syscall (freebsd-386), func StringSlicePtr //deprecated
pkg syscall (freebsd-386), type InterfaceAddrMessage //deprecated
pkg syscall (freebsd-386), type InterfaceAnnounceMessage //deprecated
pkg syscall (freebsd-386), type InterfaceMessage //deprecated
pkg syscall (freebsd-386), type InterfaceMulticastAddrMessage //deprecated
pkg syscall (freebsd-386), type RouteMessage //deprecated
pkg syscall (freebsd-386), type RoutingMessage //deprecated
pkg syscall (freebsd-386-cgo), func BpfBuflen //deprecated
pkg syscall (freebsd-386-cgo), func BpfDatalink //deprecated
pkg syscall (freebsd-386-cgo), func BpfHeadercmpl //deprecated
pkg syscall (freebsd-386-cgo), func BpfInterface //deprecated
pkg syscall (freebsd-386-cgo), func BpfJump //deprecated
pkg syscall (freebsd-386-cgo), func BpfStats //deprecated
pkg syscall (freebsd-386-cgo), func BpfStmt //deprecated
pkg syscall (freebsd-386-cgo), func BpfTimeout //deprecated
pkg syscall (freebsd-386-cgo), func CheckBpfVersion //deprecated
pkg syscall (freebsd-386-cgo), func FlushBpf //deprecated
pkg syscall (freebsd-386-cgo), func ParseRoutingMessage //deprecated
pkg syscall (freebsd-386-cgo), func ParseRoutingSockaddr //deprecated
pkg syscall (freebsd-386-cgo), func RouteRIB //deprecated
pkg syscall (freebsd-386-cgo), func SetBpf //deprecated
pkg syscall (freebsd-386-cgo), func SetBpfBuflen //deprecated
pkg syscall (freebsd-386-cgo), func SetBpfDatalink //deprecated
pkg syscall (freebsd-386-cgo), func SetBpfHeadercmpl //deprecated
pkg syscall (freebsd-386-cgo), func SetBpfImmediate //deprecated
pkg syscall (freebsd-386-cgo), func SetBpfInterface //deprecated
pkg syscall (freebsd-386-cgo), func SetBpfPromisc //deprecated
pkg syscall (freebsd-386-cgo), func SetBpfTimeout //deprecated
pkg syscall (freebsd-386-cgo), func StringSlicePtr //deprecated
pkg syscall (freebsd-386-cgo), type InterfaceAddrMessage //deprecated
pkg syscall (freebsd-386-cgo), type InterfaceAnnounceMessage //deprecated
pkg syscall (freebsd-386-cgo), type InterfaceMessage //deprecated
pkg syscall (freebsd-386-cgo), type InterfaceMulticastAddrMessage //deprecated
pkg syscall (freebsd-386-cgo), type RouteMessage //deprecated
pkg syscall (freebsd-386-cgo), type RoutingMessage //deprecated
pkg syscall (freebsd-amd64), func BpfBuflen //deprecated
pkg syscall (freebsd-amd64), func BpfDatalink //deprecated
pkg syscall (freebsd-amd64), func BpfHeadercmpl //deprecated
pkg syscall (freebsd-amd64), func BpfInterface //deprecated
pkg syscall (freebsd-amd64), func BpfJump //deprecated
pkg syscall (freebsd-amd64), func BpfStats //deprecated
pkg syscall (freebsd-amd64), func BpfStmt //deprecated
pkg syscall (freebsd-amd64), func BpfTimeout //deprecated
pkg syscall (freebsd-amd64), func CheckBpfVersion //deprecated
pkg syscall (freebsd-amd64), func FlushBpf //deprecated
pkg syscall (freebsd-amd64), func ParseRoutingMessage //deprecated
pkg syscall (freebsd-amd64), func ParseRoutingSockaddr //deprecated
pkg syscall (freebsd-amd64), func RouteRIB //deprecated
pkg syscall (freebsd-amd64), func SetBpf //deprecated
pkg syscall (freebsd-amd64), func SetBpfBuflen //deprecated
pkg syscall (freebsd-amd64), func SetBpfDatalink //deprecated
pkg syscall (freebsd-amd64), func SetBpfHeadercmpl //deprecated
pkg syscall (freebsd-amd64), func SetBpfImmediate //deprecated
pkg syscall (freebsd-amd64), func SetBpfInterface //deprecated
pkg syscall (freebsd-amd64), func SetBpfPromisc //deprecated
pkg syscall (freebsd-amd64), func SetBpfTimeout //deprecated
pkg syscall (freebsd-amd64), func StringSlicePtr //deprecated
pkg syscall (freebsd-amd64), type InterfaceAddrMessage //deprecated
pkg syscall (freebsd-amd64), type InterfaceAnnounceMessage //deprecated
pkg syscall (freebsd-amd64), type InterfaceMessage //deprecated
pkg syscall (freebsd-amd64), type InterfaceMulticastAddrMessage //deprecated
pkg syscall (freebsd-amd64), type RouteMessage //deprecated
pkg syscall (freebsd-amd64), type RoutingMessage //deprecated
pkg syscall (freebsd-amd64-cgo), func BpfBuflen //deprecated
pkg syscall (freebsd-amd64-cgo), func BpfDatalink //deprecated
pkg syscall (freebsd-amd64-cgo), func BpfHeadercmpl //deprecated
pkg syscall (freebsd-amd64-cgo), func BpfInterface //deprecated
pkg syscall (freebsd-amd64-cgo), func BpfJump //deprecated
pkg syscall (freebsd-amd64-cgo), func BpfStats //deprecated
pkg syscall (freebsd-amd64-cgo), func BpfStmt //deprecated
pkg syscall (freebsd-amd64-cgo), func BpfTimeout //deprecated
pkg syscall (freebsd-amd64-cgo), func CheckBpfVersion //deprecated
pkg syscall (freebsd-amd64-cgo), func FlushBpf //deprecated
pkg syscall (freebsd-amd64-cgo), func ParseRoutingMessage //deprecated
pkg syscall (freebsd-amd64-cgo), func ParseRoutingSockaddr //deprecated
pkg syscall (freebsd-amd64-cgo), func RouteRIB //deprecated
pkg syscall (freebsd-amd64-cgo), func SetBpf //deprecated
pkg syscall (freebsd-amd64-cgo), func SetBpfBuflen //deprecated
pkg syscall (freebsd-amd64-cgo), func SetBpfDatalink //deprecated
pkg syscall (freebsd-amd64-cgo), func SetBpfHeadercmpl //deprecated
pkg syscall (freebsd-amd64-cgo), func SetBpfImmediate //deprecated
pkg syscall (freebsd-amd64-cgo), func SetBpfInterface //deprecated
pkg syscall (freebsd-amd64-cgo), func SetBpfPromisc //deprecated
pkg syscall (freebsd-amd64-cgo), func SetBpfTimeout //deprecated
pkg syscall (freebsd-amd64-cgo), func StringSlicePtr //deprecated
pkg syscall (freebsd-amd64-cgo), type InterfaceAddrMessage //deprecated
pkg syscall (freebsd-amd64-cgo), type InterfaceAnnounceMessage //deprecated
pkg syscall (freebsd-amd64-cgo), type InterfaceMessage //deprecated
pkg syscall (freebsd-amd64-cgo), type InterfaceMulticastAddrMessage //deprecated
pkg syscall (freebsd-amd64-cgo), type RouteMessage //deprecated
pkg syscall (freebsd-amd64-cgo), type RoutingMessage //deprecated
pkg syscall (freebsd-arm), func BpfBuflen //deprecated
pkg syscall (freebsd-arm), func BpfDatalink //deprecated
pkg syscall (freebsd-arm), func BpfHeadercmpl //deprecated
pkg syscall (freebsd-arm), func BpfInterface //deprecated
pkg syscall (freebsd-arm), func BpfJump //deprecated
pkg syscall (freebsd-arm), func BpfStats //deprecated
pkg syscall (freebsd-arm), func BpfStmt //deprecated
pkg syscall (freebsd-arm), func BpfTimeout //deprecated
pkg syscall (freebsd-arm), func CheckBpfVersion //deprecated
pkg syscall (freebsd-arm), func FlushBpf //deprecated
pkg syscall (freebsd-arm), func ParseRoutingMessage //deprecated
pkg syscall (freebsd-arm), func ParseRoutingSockaddr //deprecated
pkg syscall (freebsd-arm), func RouteRIB //deprecated
pkg syscall (freebsd-arm), func SetBpf //deprecated
pkg syscall (freebsd-arm), func SetBpfBuflen //deprecated
pkg syscall (freebsd-arm), func SetBpfDatalink //deprecated
pkg syscall (freebsd-arm), func SetBpfHeadercmpl //deprecated
pkg syscall (freebsd-arm), func SetBpfImmediate //deprecated
pkg syscall (freebsd-arm), func SetBpfInterface //deprecated
pkg syscall (freebsd-arm), func SetBpfPromisc //deprecated
pkg syscall (freebsd-arm), func SetBpfTimeout //deprecated
pkg syscall (freebsd-arm), func StringSlicePtr //deprecated
pkg syscall (freebsd-arm), type InterfaceAddrMessage //deprecated
pkg syscall (freebsd-arm), type InterfaceAnnounceMessage //deprecated
pkg syscall (freebsd-arm), type InterfaceMessage //deprecated
pkg syscall (freebsd-arm), type InterfaceMulticastAddrMessage //deprecated
pkg syscall (freebsd-arm), type RouteMessage //deprecated
pkg syscall (freebsd-arm), type RoutingMessage //deprecated
pkg syscall (freebsd-arm-cgo), func BpfBuflen //deprecated
pkg syscall (freebsd-arm-cgo), func BpfDatalink //deprecated
pkg syscall (freebsd-arm-cgo), func BpfHeadercmpl //deprecated
pkg syscall (freebsd-arm-cgo), func BpfInterface //deprecated
pkg syscall (freebsd-arm-cgo), func BpfJump //deprecated
pkg syscall (freebsd-arm-cgo), func BpfStats //deprecated
pkg syscall (freebsd-arm-cgo), func BpfStmt //deprecated
pkg syscall (freebsd-arm-cgo), func BpfTimeout //deprecated
pkg syscall (freebsd-arm-cgo), func CheckBpfVersion //deprecated
pkg syscall (freebsd-arm-cgo), func FlushBpf //deprecated
pkg syscall (freebsd-arm-cgo), func ParseRoutingMessage //deprecated
pkg syscall (freebsd-arm-cgo), func ParseRoutingSockaddr //deprecated
pkg syscall (freebsd-arm-cgo), func RouteRIB //deprecated
pkg syscall (freebsd-arm-cgo), func SetBpf //deprecated
pkg syscall (freebsd-arm-cgo), func SetBpfBuflen //deprecated
pkg syscall (freebsd-arm-cgo), func SetBpfDatalink //deprecated
pkg syscall (freebsd-arm-cgo), func SetBpfHeadercmpl //deprecated
pkg syscall (freebsd-arm-cgo), func SetBpfImmediate //deprecated
pkg syscall (freebsd-arm-cgo), func SetBpfInterface //deprecated
pkg syscall (freebsd-arm-cgo), func SetBpfPromisc //deprecated
pkg syscall (freebsd-arm-cgo), func SetBpfTimeout //deprecated
pkg syscall (freebsd-arm-cgo), func StringSlicePtr //deprecated
pkg syscall (freebsd-arm-cgo), type InterfaceAddrMessage //deprecated
pkg syscall (freebsd-arm-cgo), type InterfaceAnnounceMessage //deprecated
pkg syscall (freebsd-arm-cgo), type InterfaceMessage //deprecated
pkg syscall (freebsd-arm-cgo), type InterfaceMulticastAddrMessage //deprecated
pkg syscall (freebsd-arm-cgo), type RouteMessage //deprecated
pkg syscall (freebsd-arm-cgo), type RoutingMessage //deprecated
pkg syscall (linux-386), func AttachLsf //deprecated
pkg syscall (linux-386), func DetachLsf //deprecated
pkg syscall (linux-386), func LsfJump //deprecated
pkg syscall (linux-386), func LsfSocket //deprecated
pkg syscall (linux-386), func LsfStmt //deprecated
pkg syscall (linux-386), func SetLsfPromisc //deprecated
pkg syscall (linux-386), func StringSlicePtr //deprecated
pkg syscall (linux-386-cgo), func AttachLsf //deprecated
pkg syscall (linux-386-cgo), func DetachLsf //deprecated
pkg syscall (linux-386-cgo), func LsfJump //deprecated
pkg syscall (linux-386-cgo), func LsfSocket //deprecated
pkg syscall (linux-386-cgo), func LsfStmt //deprecated
pkg syscall (linux-386-cgo), func SetLsfPromisc //deprecated
pkg syscall (linux-386-cgo), func StringSlicePtr //deprecated
pkg syscall (linux-amd64), func AttachLsf //deprecated
pkg syscall (linux-amd64), func DetachLsf //deprecated
pkg syscall (linux-amd64), func LsfJump //deprecated
pkg syscall (linux-amd64), func LsfSocket //deprecated
pkg syscall (linux-amd64), func LsfStmt //deprecated
pkg syscall (linux-amd64), func SetLsfPromisc //deprecated
pkg syscall (linux-amd64), func StringSlicePtr //deprecated
pkg syscall (linux-amd64-cgo), func AttachLsf //deprecated
pkg syscall (linux-amd64-cgo), func DetachLsf //deprecated
pkg syscall (linux-amd64-cgo), func LsfJump //deprecated
pkg syscall (linux-amd64-cgo), func LsfSocket //deprecated
pkg syscall (linux-amd64-cgo), func LsfStmt //deprecated
pkg syscall (linux-amd64-cgo), func SetLsfPromisc //deprecated
pkg syscall (linux-amd64-cgo), func StringSlicePtr //deprecated
pkg syscall (linux-arm), func AttachLsf //deprecated
pkg syscall (linux-arm), func DetachLsf //deprecated
pkg syscall (linux-arm), func LsfJump //deprecated
pkg syscall (linux-arm), func LsfSocket //deprecated
pkg syscall (linux-arm), func LsfStmt //deprecated
pkg syscall (linux-arm), func SetLsfPromisc //deprecated
pkg syscall (linux-arm), func StringSlicePtr //deprecated
pkg syscall (linux-arm-cgo), func AttachLsf //deprecated
pkg syscall (linux-arm-cgo), func DetachLsf //deprecated
pkg syscall (linux-arm-cgo), func LsfJump //deprecated
pkg syscall (linux-arm-cgo), func LsfSocket //deprecated
pkg syscall (linux-arm-cgo), func LsfStmt //deprecated
pkg syscall (linux-arm-cgo), func SetLsfPromisc //deprecated
pkg syscall (linux-arm-cgo), func StringSlicePtr //deprecated
pkg syscall (netbsd-386), func BpfBuflen //deprecated
pkg syscall (netbsd-386), func BpfDatalink //deprecated
pkg syscall (netbsd-386), func BpfHeadercmpl //deprecated
pkg syscall (netbsd-386), func BpfInterface //deprecated
pkg syscall (netbsd-386), func BpfJump //deprecated
pkg syscall (netbsd-386), func BpfStats //deprecated
pkg syscall (netbsd-386), func BpfStmt //deprecated
pkg syscall (netbsd-386), func BpfTimeout //deprecated
pkg syscall (netbsd-386), func CheckBpfVersion //deprecated
pkg syscall (netbsd-386), func FlushBpf //deprecated
pkg syscall (netbsd-386), func ParseRoutingMessage //deprecated
pkg syscall (netbsd-386), func ParseRoutingSockaddr //deprecated
pkg syscall (netbsd-386), func RouteRIB //deprecated
pkg syscall (netbsd-386), func SetBpf //deprecated
pkg syscall (netbsd-386), func SetBpfBuflen //deprecated
pkg syscall (netbsd-386), func SetBpfDatalink //deprecated
pkg syscall (netbsd-386), func SetBpfHeadercmpl //deprecated
pkg syscall (netbsd-386), func SetBpfImmediate //deprecated
pkg syscall (netbsd-386), func SetBpfInterface //deprecated
pkg syscall (netbsd-386), func SetBpfPromisc //deprecated
pkg syscall (netbsd-386), func SetBpfTimeout //deprecated
pkg syscall (netbsd-386), func StringSlicePtr //deprecated
pkg syscall (netbsd-386), type InterfaceAddrMessage //deprecated
pkg syscall (netbsd-386), type InterfaceAnnounceMessage //deprecated
pkg syscall (netbsd-386), type InterfaceMessage //deprecated
pkg syscall (netbsd-386), type RouteMessage //deprecated
pkg syscall (netbsd-386), type RoutingMessage //deprecated
pkg syscall (netbsd-386-cgo), func BpfBuflen //deprecated
pkg syscall (netbsd-386-cgo), func BpfDatalink //deprecated
pkg syscall (netbsd-386-cgo), func BpfHeadercmpl //deprecated
pkg syscall (netbsd-386-cgo), func BpfInterface //deprecated
pkg syscall (netbsd-386-cgo), func BpfJump //deprecated
pkg syscall (netbsd-386-cgo), func BpfStats //deprecated
pkg syscall (netbsd-386-cgo), func BpfStmt //deprecated
pkg syscall (netbsd-386-cgo), func BpfTimeout //deprecated
pkg syscall (netbsd-386-cgo), func CheckBpfVersion //deprecated
pkg syscall (netbsd-386-cgo), func FlushBpf //deprecated
pkg syscall (netbsd-386-cgo), func ParseRoutingMessage //deprecated
pkg syscall (netbsd-386-cgo), func ParseRoutingSockaddr //deprecated
pkg syscall (netbsd-386-cgo), func RouteRIB //deprecated
pkg syscall (netbsd-386-cgo), func SetBpf //deprecated
pkg syscall (netbsd-386-cgo), func SetBpfBuflen //deprecated
pkg syscall (netbsd-386-cgo), func SetBpfDatalink //deprecated
pkg syscall (netbsd-386-cgo), func SetBpfHeadercmpl //deprecated
pkg syscall (netbsd-386-cgo), func SetBpfImmediate //deprecated
pkg syscall (netbsd-386-cgo), func SetBpfInterface //deprecated
pkg syscall (netbsd-386-cgo), func SetBpfPromisc //deprecated
pkg syscall (netbsd-386-cgo), func SetBpfTimeout //deprecated
pkg syscall (netbsd-386-cgo), func StringSlicePtr //deprecated
pkg syscall (netbsd-386-cgo), type InterfaceAddrMessage //deprecated
pkg syscall (netbsd-386-cgo), type InterfaceAnnounceMessage //deprecated
pkg syscall (netbsd-386-cgo), type InterfaceMessage //deprecated
pkg syscall (netbsd-386-cgo), type RouteMessage //deprecated
pkg syscall (netbsd-386-cgo), type RoutingMessage //deprecated
pkg syscall (netbsd-amd64), func BpfBuflen //deprecated
pkg syscall (netbsd-amd64), func BpfDatalink //deprecated
pkg syscall (netbsd-amd64), func BpfHeadercmpl //deprecated
pkg syscall (netbsd-amd64), func BpfInterface //deprecated
pkg syscall (netbsd-amd64), func BpfJump //deprecated
pkg syscall (netbsd-amd64), func BpfStats //deprecated
pkg syscall (netbsd-amd64), func BpfStmt //deprecated
pkg syscall (netbsd-amd64), func BpfTimeout //deprecated
pkg syscall (netbsd-amd64), func CheckBpfVersion //deprecated
pkg syscall (netbsd-amd64), func FlushBpf //deprecated
pkg syscall (netbsd-amd64), func ParseRoutingMessage //deprecated
pkg syscall (netbsd-amd64), func ParseRoutingSockaddr //deprecated
pkg syscall (netbsd-amd64), func RouteRIB //deprecated
pkg syscall (netbsd-amd64), func SetBpf //deprecated
pkg syscall (netbsd-amd64), func SetBpfBuflen //deprecated
pkg syscall (netbsd-amd64), func SetBpfDatalink //deprecated
pkg syscall (netbsd-amd64), func SetBpfHeadercmpl //deprecated
pkg syscall (netbsd-amd64), func SetBpfImmediate //deprecated
pkg syscall (netbsd-amd64), func SetBpfInterface //deprecated
pkg syscall (netbsd-amd64), func SetBpfPromisc //deprecated
pkg syscall (netbsd-amd64), func SetBpfTimeout //deprecated
pkg syscall (netbsd-amd64), func StringSlicePtr //deprecated
pkg syscall (netbsd-amd64), type InterfaceAddrMessage //deprecated
pkg syscall (netbsd-amd64), type InterfaceAnnounceMessage //deprecated
pkg syscall (netbsd-amd64), type InterfaceMessage //deprecated
pkg syscall (netbsd-amd64), type RouteMessage //deprecated
pkg syscall (netbsd-amd64), type RoutingMessage //deprecated
pkg syscall (netbsd-amd64-cgo), func BpfBuflen //deprecated
pkg syscall (netbsd-amd64-cgo), func BpfDatalink //deprecated
pkg syscall (netbsd-amd64-cgo), func BpfHeadercmpl //deprecated
pkg syscall (netbsd-amd64-cgo), func BpfInterface //deprecated
pkg syscall (netbsd-amd64-cgo), func BpfJump //deprecated
pkg syscall (netbsd-amd64-cgo), func BpfStats //deprecated
pkg syscall (netbsd-amd64-cgo), func BpfStmt //deprecated
pkg syscall (netbsd-amd64-cgo), func BpfTimeout //deprecated
pkg syscall (netbsd-amd64-cgo), func CheckBpfVersion //deprecated
pkg syscall (netbsd-amd64-cgo), func FlushBpf //deprecated
pkg syscall (netbsd-amd64-cgo), func ParseRoutingMessage //deprecated
pkg syscall (netbsd-amd64-cgo), func ParseRoutingSockaddr //deprecated
pkg syscall (netbsd-amd64-cgo), func RouteRIB //deprecated
pkg syscall (netbsd-amd64-cgo), func SetBpf //deprecated
pkg syscall (netbsd-amd64-cgo), func SetBpfBuflen //deprecated
pkg syscall (netbsd-amd64-cgo), func SetBpfDatalink //deprecated
pkg syscall (netbsd-amd64-cgo), func SetBpfHeadercmpl //deprecated
pkg syscall (netbsd-amd64-cgo), func SetBpfImmediate //deprecated
pkg syscall (netbsd-amd64-cgo), func SetBpfInterface //deprecated
pkg syscall (netbsd-amd64-cgo), func SetBpfPromisc //deprecated
pkg syscall (netbsd-amd64-cgo), func SetBpfTimeout //deprecated
pkg syscall (netbsd-amd64-cgo), func StringSlicePtr //deprecated
pkg syscall (netbsd-amd64-cgo), type InterfaceAddrMessage //deprecated
pkg syscall (netbsd-amd64-cgo), type InterfaceAnnounceMessage //deprecated
pkg syscall (netbsd-amd64-cgo), type InterfaceMessage //deprecated
pkg syscall (netbsd-amd64-cgo), type RouteMessage //deprecated
pkg syscall (netbsd-amd64-cgo), type RoutingMessage //deprecated
pkg syscall (netbsd-arm), func BpfBuflen //deprecated
pkg syscall (netbsd-arm), func BpfDatalink //deprecated
pkg syscall (netbsd-arm), func BpfHeadercmpl //deprecated
pkg syscall (netbsd-arm), func BpfInterface //deprecated
pkg syscall (netbsd-arm), func BpfJump //deprecated
pkg syscall (netbsd-arm), func BpfStats //deprecated
pkg syscall (netbsd-arm), func BpfStmt //deprecated
pkg syscall (netbsd-arm), func BpfTimeout //deprecated
pkg syscall (netbsd-arm), func CheckBpfVersion //deprecated
pkg syscall (netbsd-arm), func FlushBpf //deprecated
pkg syscall (netbsd-arm), func ParseRoutingMessage //deprecated
pkg syscall (netbsd-arm), func ParseRoutingSockaddr //deprecated
pkg syscall (netbsd-arm), func RouteRIB //deprecated
pkg syscall (netbsd-arm), func SetBpf //deprecated
pkg syscall (netbsd-arm), func SetBpfBuflen //deprecated
pkg syscall (netbsd-arm), func SetBpfDatalink //deprecated
pkg syscall (netbsd-arm), func SetBpfHeadercmpl //deprecated
pkg syscall (netbsd-arm), func SetBpfImmediate //deprecated
pkg syscall (netbsd-arm), func SetBpfInterface //deprecated
pkg syscall (netbsd-arm), func SetBpfPromisc //deprecated
pkg syscall (netbsd-arm), func SetBpfTimeout //deprecated
pkg syscall (netbsd-arm), func StringSlicePtr //deprecated
pkg syscall (netbsd-arm), type InterfaceAddrMessage //deprecated
pkg syscall (netbsd-arm), type InterfaceAnnounceMessage //deprecated
pkg syscall (netbsd-arm), type InterfaceMessage //deprecated
pkg syscall (netbsd-arm), type RouteMessage //deprecated
pkg syscall (netbsd-arm), type RoutingMessage //deprecated
pkg syscall (netbsd-arm-cgo), func BpfBuflen //deprecated
pkg syscall (netbsd-arm-cgo), func BpfDatalink //deprecated
pkg syscall (netbsd-arm-cgo), func BpfHeadercmpl //deprecated
pkg syscall (netbsd-arm-cgo), func BpfInterface //deprecated
pkg syscall (netbsd-arm-cgo), func BpfJump //deprecated
pkg syscall (netbsd-arm-cgo), func BpfStats //deprecated
pkg syscall (netbsd-arm-cgo), func BpfStmt //deprecated
pkg syscall (netbsd-arm-cgo), func BpfTimeout //deprecated
pkg syscall (netbsd-arm-cgo), func CheckBpfVersion //deprecated
pkg syscall (netbsd-arm-cgo), func FlushBpf //deprecated
pkg syscall (netbsd-arm-cgo), func ParseRoutingMessage //deprecated
pkg syscall (netbsd-arm-cgo), func ParseRoutingSockaddr //deprecated
pkg syscall (netbsd-arm-cgo), func RouteRIB //deprecated
pkg syscall (netbsd-arm-cgo), func SetBpf //deprecated
pkg syscall (netbsd-arm-cgo), func SetBpfBuflen //deprecated
pkg syscall (netbsd-arm-cgo), func SetBpfDatalink //deprecated
pkg syscall (netbsd-arm-cgo), func SetBpfHeadercmpl //deprecated
pkg syscall (netbsd-arm-cgo), func SetBpfImmediate //deprecated
pkg syscall (netbsd-arm-cgo), func SetBpfInterface //deprecated
pkg syscall (netbsd-arm-cgo), func SetBpfPromisc //deprecated
pkg syscall (netbsd-arm-cgo), func SetBpfTimeout //deprecated
pkg syscall (netbsd-arm-cgo), func StringSlicePtr //deprecated
pkg syscall (netbsd-arm-cgo), type InterfaceAddrMessage //deprecated
pkg syscall (netbsd-arm-cgo), type InterfaceAnnounceMessage //deprecated
pkg syscall (netbsd-arm-cgo), type InterfaceMessage //deprecated
pkg syscall (netbsd-arm-cgo), type RouteMessage //deprecated
pkg syscall (netbsd-arm-cgo), type RoutingMessage //deprecated
pkg syscall (netbsd-arm64), func BpfBuflen //deprecated
pkg syscall (netbsd-arm64), func BpfDatalink //deprecated
pkg syscall (netbsd-arm64), func BpfHeadercmpl //deprecated
pkg syscall (netbsd-arm64), func BpfInterface //deprecated
pkg syscall (netbsd-arm64), func BpfJump //deprecated
pkg syscall (netbsd-arm64), func BpfStats //deprecated
pkg syscall (netbsd-arm64), func BpfStmt //deprecated
pkg syscall (netbsd-arm64), func BpfTimeout //deprecated
pkg syscall (netbsd-arm64), func CheckBpfVersion //deprecated
pkg syscall (netbsd-arm64), func FlushBpf //deprecated
pkg syscall (netbsd-arm64), func ParseRoutingMessage //deprecated
pkg syscall (netbsd-arm64), func ParseRoutingSockaddr //deprecated
pkg syscall (netbsd-arm64), func RouteRIB //deprecated
pkg syscall (netbsd-arm64), func SetBpf //deprecated
pkg syscall (netbsd-arm64), func SetBpfBuflen //deprecated
pkg syscall (netbsd-arm64), func SetBpfDatalink //deprecated
pkg syscall (netbsd-arm64), func SetBpfHeadercmpl //deprecated
pkg syscall (netbsd-arm64), func SetBpfImmediate //deprecated
pkg syscall (netbsd-arm64), func SetBpfInterface //deprecated
pkg syscall (netbsd-arm64), func SetBpfPromisc //deprecated
pkg syscall (netbsd-arm64), func SetBpfTimeout //deprecated
pkg syscall (netbsd-arm64), func StringSlicePtr //deprecated
pkg syscall (netbsd-arm64), type InterfaceAddrMessage //deprecated
pkg syscall (netbsd-arm64), type InterfaceAnnounceMessage //deprecated
pkg syscall (netbsd-arm64), type InterfaceMessage //deprecated
pkg syscall (netbsd-arm64), type RouteMessage //deprecated
pkg syscall (netbsd-arm64), type RoutingMessage //deprecated
pkg syscall (netbsd-arm64-cgo), func BpfBuflen //deprecated
pkg syscall (netbsd-arm64-cgo), func BpfDatalink //deprecated
pkg syscall (netbsd-arm64-cgo), func BpfHeadercmpl //deprecated
pkg syscall (netbsd-arm64-cgo), func BpfInterface //deprecated
pkg syscall (netbsd-arm64-cgo), func BpfJump //deprecated
pkg syscall (netbsd-arm64-cgo), func BpfStats //deprecated
pkg syscall (netbsd-arm64-cgo), func BpfStmt //deprecated
pkg syscall (netbsd-arm64-cgo), func BpfTimeout //deprecated
pkg syscall (netbsd-arm64-cgo), func CheckBpfVersion //deprecated
pkg syscall (netbsd-arm64-cgo), func FlushBpf //deprecated
pkg syscall (netbsd-arm64-cgo), func ParseRoutingMessage //deprecated
pkg syscall (netbsd-arm64-cgo), func ParseRoutingSockaddr //deprecated
pkg syscall (netbsd-arm64-cgo), func RouteRIB //deprecated
pkg syscall (netbsd-arm64-cgo), func SetBpf //deprecated
pkg syscall (netbsd-arm64-cgo), func SetBpfBuflen //deprecated
pkg syscall (netbsd-arm64-cgo), func SetBpfDatalink //deprecated
pkg syscall (netbsd-arm64-cgo), func SetBpfHeadercmpl //deprecated
pkg syscall (netbsd-arm64-cgo), func SetBpfImmediate //deprecated
pkg syscall (netbsd-arm64-cgo), func SetBpfInterface //deprecated
pkg syscall (netbsd-arm64-cgo), func SetBpfPromisc //deprecated
pkg syscall (netbsd-arm64-cgo), func SetBpfTimeout //deprecated
pkg syscall (netbsd-arm64-cgo), func StringSlicePtr //deprecated
pkg syscall (netbsd-arm64-cgo), type InterfaceAddrMessage //deprecated
pkg syscall (netbsd-arm64-cgo), type InterfaceAnnounceMessage //deprecated
pkg syscall (netbsd-arm64-cgo), type InterfaceMessage //deprecated
pkg syscall (netbsd-arm64-cgo), type RouteMessage //deprecated
pkg syscall (netbsd-arm64-cgo), type RoutingMessage //deprecated
pkg syscall (openbsd-386), func BpfBuflen //deprecated
pkg syscall (openbsd-386), func BpfDatalink //deprecated
pkg syscall (openbsd-386), func BpfHeadercmpl //deprecated
pkg syscall (openbsd-386), func BpfInterface //deprecated
pkg syscall (openbsd-386), func BpfJump //deprecated
pkg syscall (openbsd-386), func BpfStats //deprecated
pkg syscall (openbsd-386), func BpfStmt //deprecated
pkg syscall (openbsd-386), func BpfTimeout //deprecated
pkg syscall (openbsd-386), func CheckBpfVersion //deprecated
pkg syscall (openbsd-386), func FlushBpf //deprecated
pkg syscall (openbsd-386), func ParseRoutingMessage //deprecated
pkg syscall (openbsd-386), func ParseRoutingSockaddr //deprecated
pkg syscall (openbsd-386), func RouteRIB //deprecated
pkg syscall (openbsd-386), func SetBpf //deprecated
pkg syscall (openbsd-386), func SetBpfBuflen //deprecated
pkg syscall (openbsd-386), func SetBpfDatalink //deprecated
pkg syscall (openbsd-386), func SetBpfHeadercmpl //deprecated
pkg syscall (openbsd-386), func SetBpfImmediate //deprecated
pkg syscall (openbsd-386), func SetBpfInterface //deprecated
pkg syscall (openbsd-386), func SetBpfPromisc //deprecated
pkg syscall (openbsd-386), func SetBpfTimeout //deprecated
pkg syscall (openbsd-386), func StringSlicePtr //deprecated
pkg syscall (openbsd-386), type InterfaceAddrMessage //deprecated
pkg syscall (openbsd-386), type InterfaceAnnounceMessage //deprecated
pkg syscall (openbsd-386), type InterfaceMessage //deprecated
pkg syscall (openbsd-386), type RouteMessage //deprecated
pkg syscall (openbsd-386), type RoutingMessage //deprecated
pkg syscall (openbsd-386-cgo), func BpfBuflen //deprecated
pkg syscall (openbsd-386-cgo), func BpfDatalink //deprecated
pkg syscall (openbsd-386-cgo), func BpfHeadercmpl //deprecated
pkg syscall (openbsd-386-cgo), func BpfInterface //deprecated
pkg syscall (openbsd-386-cgo), func BpfJump //deprecated
pkg syscall (openbsd-386-cgo), func BpfStats //deprecated
pkg syscall (openbsd-386-cgo), func BpfStmt //deprecated
pkg syscall (openbsd-386-cgo), func BpfTimeout //deprecated
pkg syscall (openbsd-386-cgo), func CheckBpfVersion //deprecated
pkg syscall (openbsd-386-cgo), func FlushBpf //deprecated
pkg syscall (openbsd-386-cgo), func ParseRoutingMessage //deprecated
pkg syscall (openbsd-386-cgo), func ParseRoutingSockaddr //deprecated
pkg syscall (openbsd-386-cgo), func RouteRIB //deprecated
pkg syscall (openbsd-386-cgo), func SetBpf //deprecated
pkg syscall (openbsd-386-cgo), func SetBpfBuflen //deprecated
pkg syscall (openbsd-386-cgo), func SetBpfDatalink //deprecated
pkg syscall (openbsd-386-cgo), func SetBpfHeadercmpl //deprecated
pkg syscall (openbsd-386-cgo), func SetBpfImmediate //deprecated
pkg syscall (openbsd-386-cgo), func SetBpfInterface //deprecated
pkg syscall (openbsd-386-cgo), func SetBpfPromisc //deprecated
pkg syscall (openbsd-386-cgo), func SetBpfTimeout //deprecated
pkg syscall (openbsd-386-cgo), func StringSlicePtr //deprecated
pkg syscall (openbsd-386-cgo), type InterfaceAddrMessage //deprecated
pkg syscall (openbsd-386-cgo), type InterfaceAnnounceMessage //deprecated
pkg syscall (openbsd-386-cgo), type InterfaceMessage //deprecated
pkg syscall (openbsd-386-cgo), type RouteMessage //deprecated
pkg syscall (openbsd-386-cgo), type RoutingMessage //deprecated
pkg syscall (openbsd-amd64), func BpfBuflen //deprecated
pkg syscall (openbsd-amd64), func BpfDatalink //deprecated
pkg syscall (openbsd-amd64), func BpfHeadercmpl //deprecated
pkg syscall (openbsd-amd64), func BpfInterface //deprecated
pkg syscall (openbsd-amd64), func BpfJump //deprecated
pkg syscall (openbsd-amd64), func BpfStats //deprecated
pkg syscall (openbsd-amd64), func BpfStmt //deprecated
pkg syscall (openbsd-amd64), func BpfTimeout //deprecated
pkg syscall (openbsd-amd64), func CheckBpfVersion //deprecated
pkg syscall (openbsd-amd64), func FlushBpf //deprecated
pkg syscall (openbsd-amd64), func ParseRoutingMessage //deprecated
pkg syscall (openbsd-amd64), func ParseRoutingSockaddr //deprecated
pkg syscall (openbsd-amd64), func RouteRIB //deprecated
pkg syscall (openbsd-amd64), func SetBpf //deprecated
pkg syscall (openbsd-amd64), func SetBpfBuflen //deprecated
pkg syscall (openbsd-amd64), func SetBpfDatalink //deprecated
pkg syscall (openbsd-amd64), func SetBpfHeadercmpl //deprecated
pkg syscall (openbsd-amd64), func SetBpfImmediate //deprecated
pkg syscall (openbsd-amd64), func SetBpfInterface //deprecated
pkg syscall (openbsd-amd64), func SetBpfPromisc //deprecated
pkg syscall (openbsd-amd64), func SetBpfTimeout //deprecated
pkg syscall (openbsd-amd64), func StringSlicePtr //deprecated
pkg syscall (openbsd-amd64), type InterfaceAddrMessage //deprecated
pkg syscall (openbsd-amd64), type InterfaceAnnounceMessage //deprecated
pkg syscall (openbsd-amd64), type InterfaceMessage //deprecated
pkg syscall (openbsd-amd64), type RouteMessage //deprecated
pkg syscall (openbsd-amd64), type RoutingMessage //deprecated
pkg syscall (openbsd-amd64-cgo), func BpfBuflen //deprecated
pkg syscall (openbsd-amd64-cgo), func BpfDatalink //deprecated
pkg syscall (openbsd-amd64-cgo), func BpfHeadercmpl //deprecated
pkg syscall (openbsd-amd64-cgo), func BpfInterface //deprecated
pkg syscall (openbsd-amd64-cgo), func BpfJump //deprecated
pkg syscall (openbsd-amd64-cgo), func BpfStats //deprecated
pkg syscall (openbsd-amd64-cgo), func BpfStmt //deprecated
pkg syscall (openbsd-amd64-cgo), func BpfTimeout //deprecated
pkg syscall (openbsd-amd64-cgo), func CheckBpfVersion //deprecated
pkg syscall (openbsd-amd64-cgo), func FlushBpf //deprecated
pkg syscall (openbsd-amd64-cgo), func ParseRoutingMessage //deprecated
pkg syscall (openbsd-amd64-cgo), func ParseRoutingSockaddr //deprecated
pkg syscall (openbsd-amd64-cgo), func RouteRIB //deprecated
pkg syscall (openbsd-amd64-cgo), func SetBpf //deprecated
pkg syscall (openbsd-amd64-cgo), func SetBpfBuflen //deprecated
pkg syscall (openbsd-amd64-cgo), func SetBpfDatalink //deprecated
pkg syscall (openbsd-amd64-cgo), func SetBpfHeadercmpl //deprecated
pkg syscall (openbsd-amd64-cgo), func SetBpfImmediate //deprecated
pkg syscall (openbsd-amd64-cgo), func SetBpfInterface //deprecated
pkg syscall (openbsd-amd64-cgo), func SetBpfPromisc //deprecated
pkg syscall (openbsd-amd64-cgo), func SetBpfTimeout //deprecated
pkg syscall (openbsd-amd64-cgo), func StringSlicePtr //deprecated
pkg syscall (openbsd-amd64-cgo), type InterfaceAddrMessage //deprecated
pkg syscall (openbsd-amd64-cgo), type InterfaceAnnounceMessage //deprecated
pkg syscall (openbsd-amd64-cgo), type InterfaceMessage //deprecated
pkg syscall (openbsd-amd64-cgo), type RouteMessage //deprecated
pkg syscall (openbsd-amd64-cgo), type RoutingMessage //deprecated
pkg syscall (windows-386), func FormatMessage //deprecated
pkg syscall (windows-386), func StringToUTF16 //deprecated
pkg syscall (windows-386), func StringToUTF16Ptr //deprecated
pkg syscall (windows-amd64), func FormatMessage //deprecated
pkg syscall (windows-amd64), func StringToUTF16 //deprecated
pkg syscall (windows-amd64), func StringToUTF16Ptr //deprecated
pkg syscall, func StringBytePtr //deprecated
pkg syscall, func StringByteSlice //deprecated
# darwin arm64 port
pkg log/syslog (darwin-arm64), const LOG_ALERT = 1
pkg log/syslog (darwin-arm64), const LOG_ALERT Priority
pkg log/syslog (darwin-arm64), const LOG_AUTH = 32
pkg log/syslog (darwin-arm64), const LOG_AUTH Priority
pkg log/syslog (darwin-arm64), const LOG_AUTHPRIV = 80
pkg log/syslog (darwin-arm64), const LOG_AUTHPRIV Priority
pkg log/syslog (darwin-arm64), const LOG_CRIT = 2
pkg log/syslog (darwin-arm64), const LOG_CRIT Priority
pkg log/syslog (darwin-arm64), const LOG_CRON = 72
pkg log/syslog (darwin-arm64), const LOG_CRON Priority
pkg log/syslog (darwin-arm64), const LOG_DAEMON = 24
pkg log/syslog (darwin-arm64), const LOG_DAEMON Priority
pkg log/syslog (darwin-arm64), const LOG_DEBUG = 7
pkg log/syslog (darwin-arm64), const LOG_DEBUG Priority
pkg log/syslog (darwin-arm64), const LOG_EMERG = 0
pkg log/syslog (darwin-arm64), const LOG_EMERG Priority
pkg log/syslog (darwin-arm64), const LOG_ERR = 3
pkg log/syslog (darwin-arm64), const LOG_ERR Priority
pkg log/syslog (darwin-arm64), const LOG_FTP = 88
pkg log/syslog (darwin-arm64), const LOG_FTP Priority
pkg log/syslog (darwin-arm64), const LOG_INFO = 6
pkg log/syslog (darwin-arm64), const LOG_INFO Priority
pkg log/syslog (darwin-arm64), const LOG_KERN = 0
pkg log/syslog (darwin-arm64), const LOG_KERN Priority
pkg log/syslog (darwin-arm64), const LOG_LOCAL0 = 128
pkg log/syslog (darwin-arm64), const LOG_LOCAL0 Priority
pkg log/syslog (darwin-arm64), const LOG_LOCAL1 = 136
pkg log/syslog (darwin-arm64), const LOG_LOCAL1 Priority
pkg log/syslog (darwin-arm64), const LOG_LOCAL2 = 144
pkg log/syslog (darwin-arm64), const LOG_LOCAL2 Priority
pkg log/syslog (darwin-arm64), const LOG_LOCAL3 = 152
pkg log/syslog (darwin-arm64), const LOG_LOCAL3 Priority
pkg log/syslog (darwin-arm64), const LOG_LOCAL4 = 160
pkg log/syslog (darwin-arm64), const LOG_LOCAL4 Priority
pkg log/syslog (darwin-arm64), const LOG_LOCAL5 = 168
pkg log/syslog (darwin-arm64), const LOG_LOCAL5 Priority
pkg log/syslog (darwin-arm64), const LOG_LOCAL6 = 176
pkg log/syslog (darwin-arm64), const LOG_LOCAL6 Priority
pkg log/syslog (darwin-arm64), const LOG_LOCAL7 = 184
pkg log/syslog (darwin-arm64), const LOG_LOCAL7 Priority
pkg log/syslog (darwin-arm64), const LOG_LPR = 48
pkg log/syslog (darwin-arm64), const LOG_LPR Priority
pkg log/syslog (darwin-arm64), const LOG_MAIL = 16
pkg log/syslog (darwin-arm64), const LOG_MAIL Priority
pkg log/syslog (darwin-arm64), const LOG_NEWS = 56
pkg log/syslog (darwin-arm64), const LOG_NEWS Priority
pkg log/syslog (darwin-arm64), const LOG_NOTICE = 5
pkg log/syslog (darwin-arm64), const LOG_NOTICE Priority
pkg log/syslog (darwin-arm64), const LOG_SYSLOG = 40
pkg log/syslog (darwin-arm64), const LOG_SYSLOG Priority
pkg log/syslog (darwin-arm64), const LOG_USER = 8
pkg log/syslog (darwin-arm64), const LOG_USER Priority
pkg log/syslog (darwin-arm64), const LOG_UUCP = 64
pkg log/syslog (darwin-arm64), const LOG_UUCP Priority
pkg log/syslog (darwin-arm64), const LOG_WARNING = 4
pkg log/syslog (darwin-arm64), const LOG_WARNING Priority
pkg log/syslog (darwin-arm64), func Dial(string, string, Priority, string) (*Writer, error)
pkg log/syslog (darwin-arm64), func New(Priority, string) (*Writer, error)
pkg log/syslog (darwin-arm64), func NewLogger(Priority, int) (*log.Logger, error)
pkg log/syslog (darwin-arm64), method (*Writer) Alert(string) error
pkg log/syslog (darwin-arm64), method (*Writer) Close() error
pkg log/syslog (darwin-arm64), method (*Writer) Crit(string) error
pkg log/syslog (darwin-arm64), method (*Writer) Debug(string) error
pkg log/syslog (darwin-arm64), method (*Writer) Emerg(string) error
pkg log/syslog (darwin-arm64), method (*Writer) Err(string) error
pkg log/syslog (darwin-arm64), method (*Writer) Info(string) error
pkg log/syslog (darwin-arm64), method (*Writer) Notice(string) error
pkg log/syslog (darwin-arm64), method (*Writer) Warning(string) error
pkg log/syslog (darwin-arm64), method (*Writer) Write([]uint8) (int, error)
pkg log/syslog (darwin-arm64), type Priority int
pkg log/syslog (darwin-arm64), type Writer struct
pkg log/syslog (darwin-arm64-cgo), const LOG_ALERT = 1
pkg log/syslog (darwin-arm64-cgo), const LOG_ALERT Priority
pkg log/syslog (darwin-arm64-cgo), const LOG_AUTH = 32
pkg log/syslog (darwin-arm64-cgo), const LOG_AUTH Priority
pkg log/syslog (darwin-arm64-cgo), const LOG_AUTHPRIV = 80
pkg log/syslog (darwin-arm64-cgo), const LOG_AUTHPRIV Priority
pkg log/syslog (darwin-arm64-cgo), const LOG_CRIT = 2
pkg log/syslog (darwin-arm64-cgo), const LOG_CRIT Priority
pkg log/syslog (darwin-arm64-cgo), const LOG_CRON = 72
pkg log/syslog (darwin-arm64-cgo), const LOG_CRON Priority
pkg log/syslog (darwin-arm64-cgo), const LOG_DAEMON = 24
pkg log/syslog (darwin-arm64-cgo), const LOG_DAEMON Priority
pkg log/syslog (darwin-arm64-cgo), const LOG_DEBUG = 7
pkg log/syslog (darwin-arm64-cgo), const LOG_DEBUG Priority
pkg log/syslog (darwin-arm64-cgo), const LOG_EMERG = 0
pkg log/syslog (darwin-arm64-cgo), const LOG_EMERG Priority
pkg log/syslog (darwin-arm64-cgo), const LOG_ERR = 3
pkg log/syslog (darwin-arm64-cgo), const LOG_ERR Priority
pkg log/syslog (darwin-arm64-cgo), const LOG_FTP = 88
pkg log/syslog (darwin-arm64-cgo), const LOG_FTP Priority
pkg log/syslog (darwin-arm64-cgo), const LOG_INFO = 6
pkg log/syslog (darwin-arm64-cgo), const LOG_INFO Priority
pkg log/syslog (darwin-arm64-cgo), const LOG_KERN = 0
pkg log/syslog (darwin-arm64-cgo), const LOG_KERN Priority
pkg log/syslog (darwin-arm64-cgo), const LOG_LOCAL0 = 128
pkg log/syslog (darwin-arm64-cgo), const LOG_LOCAL0 Priority
pkg log/syslog (darwin-arm64-cgo), const LOG_LOCAL1 = 136
pkg log/syslog (darwin-arm64-cgo), const LOG_LOCAL1 Priority
pkg log/syslog (darwin-arm64-cgo), const LOG_LOCAL2 = 144
pkg log/syslog (darwin-arm64-cgo), const LOG_LOCAL2 Priority
pkg log/syslog (darwin-arm64-cgo), const LOG_LOCAL3 = 152
pkg log/syslog (darwin-arm64-cgo), const LOG_LOCAL3 Priority
pkg log/syslog (darwin-arm64-cgo), const LOG_LOCAL4 = 160
pkg log/syslog (darwin-arm64-cgo), const LOG_LOCAL4 Priority
pkg log/syslog (darwin-arm64-cgo), const LOG_LOCAL5 = 168
pkg log/syslog (darwin-arm64-cgo), const LOG_LOCAL5 Priority
pkg log/syslog (darwin-arm64-cgo), const LOG_LOCAL6 = 176
pkg log/syslog (darwin-arm64-cgo), const LOG_LOCAL6 Priority
pkg log/syslog (darwin-arm64-cgo), const LOG_LOCAL7 = 184
pkg log/syslog (darwin-arm64-cgo), const LOG_LOCAL7 Priority
pkg log/syslog (darwin-arm64-cgo), const LOG_LPR = 48
pkg log/syslog (darwin-arm64-cgo), const LOG_LPR Priority
pkg log/syslog (darwin-arm64-cgo), const LOG_MAIL = 16
pkg log/syslog (darwin-arm64-cgo), const LOG_MAIL Priority
pkg log/syslog (darwin-arm64-cgo), const LOG_NEWS = 56
pkg log/syslog (darwin-arm64-cgo), const LOG_NEWS Priority
pkg log/syslog (darwin-arm64-cgo), const LOG_NOTICE = 5
pkg log/syslog (darwin-arm64-cgo), const LOG_NOTICE Priority
pkg log/syslog (darwin-arm64-cgo), const LOG_SYSLOG = 40
pkg log/syslog (darwin-arm64-cgo), const LOG_SYSLOG Priority
pkg log/syslog (darwin-arm64-cgo), const LOG_USER = 8
pkg log/syslog (darwin-arm64-cgo), const LOG_USER Priority
pkg log/syslog (darwin-arm64-cgo), const LOG_UUCP = 64
pkg log/syslog (darwin-arm64-cgo), const LOG_UUCP Priority
pkg log/syslog (darwin-arm64-cgo), const LOG_WARNING = 4
pkg log/syslog (darwin-arm64-cgo), const LOG_WARNING Priority
pkg log/syslog (darwin-arm64-cgo), func Dial(string, string, Priority, string) (*Writer, error)
pkg log/syslog (darwin-arm64-cgo), func New(Priority, string) (*Writer, error)
pkg log/syslog (darwin-arm64-cgo), func NewLogger(Priority, int) (*log.Logger, error)
pkg log/syslog (darwin-arm64-cgo), method (*Writer) Alert(string) error
pkg log/syslog (darwin-arm64-cgo), method (*Writer) Close() error
pkg log/syslog (darwin-arm64-cgo), method (*Writer) Crit(string) error
pkg log/syslog (darwin-arm64-cgo), method (*Writer) Debug(string) error
pkg log/syslog (darwin-arm64-cgo), method (*Writer) Emerg(string) error
pkg log/syslog (darwin-arm64-cgo), method (*Writer) Err(string) error
pkg log/syslog (darwin-arm64-cgo), method (*Writer) Info(string) error
pkg log/syslog (darwin-arm64-cgo), method (*Writer) Notice(string) error
pkg log/syslog (darwin-arm64-cgo), method (*Writer) Warning(string) error
pkg log/syslog (darwin-arm64-cgo), method (*Writer) Write([]uint8) (int, error)
pkg log/syslog (darwin-arm64-cgo), type Priority int
pkg log/syslog (darwin-arm64-cgo), type Writer struct
pkg math/bits (darwin-arm64), const UintSize = 64
pkg math/bits (darwin-arm64-cgo), const UintSize = 64
pkg os (darwin-arm64), const DevNull = "/dev/null"
pkg os (darwin-arm64), const O_APPEND = 8
pkg os (darwin-arm64), const O_CREATE = 512
pkg os (darwin-arm64), const O_EXCL = 2048
pkg os (darwin-arm64), const O_SYNC = 128
pkg os (darwin-arm64), const O_TRUNC = 1024
pkg os (darwin-arm64), const PathListSeparator = 58
pkg os (darwin-arm64), const PathSeparator = 47
pkg os (darwin-arm64-cgo), const DevNull = "/dev/null"
pkg os (darwin-arm64-cgo), const O_APPEND = 8
pkg os (darwin-arm64-cgo), const O_CREATE = 512
pkg os (darwin-arm64-cgo), const O_EXCL = 2048
pkg os (darwin-arm64-cgo), const O_SYNC = 128
pkg os (darwin-arm64-cgo), const O_TRUNC = 1024
pkg os (darwin-arm64-cgo), const PathListSeparator = 58
pkg os (darwin-arm64-cgo), const PathSeparator = 47
pkg path/filepath (darwin-arm64), const ListSeparator = 58
pkg path/filepath (darwin-arm64), const Separator = 47
pkg path/filepath (darwin-arm64-cgo), const ListSeparator = 58
pkg path/filepath (darwin-arm64-cgo), const Separator = 47
pkg runtime (darwin-arm64), const GOARCH = "arm64"
pkg runtime (darwin-arm64), const GOOS = "darwin"
pkg runtime (darwin-arm64-cgo), const GOARCH = "arm64"
pkg runtime (darwin-arm64-cgo), const GOOS = "darwin"
pkg strconv (darwin-arm64), const IntSize = 64
pkg strconv (darwin-arm64-cgo), const IntSize = 64
pkg syscall (darwin-arm64), const AF_APPLETALK = 16
pkg syscall (darwin-arm64), const AF_APPLETALK ideal-int
pkg syscall (darwin-arm64), const AF_CCITT = 10
pkg syscall (darwin-arm64), const AF_CCITT ideal-int
pkg syscall (darwin-arm64), const AF_CHAOS = 5
pkg syscall (darwin-arm64), const AF_CHAOS ideal-int
pkg syscall (darwin-arm64), const AF_CNT = 21
pkg syscall (darwin-arm64), const AF_CNT ideal-int
pkg syscall (darwin-arm64), const AF_COIP = 20
pkg syscall (darwin-arm64), const AF_COIP ideal-int
pkg syscall (darwin-arm64), const AF_DATAKIT = 9
pkg syscall (darwin-arm64), const AF_DATAKIT ideal-int
pkg syscall (darwin-arm64), const AF_DECnet = 12
pkg syscall (darwin-arm64), const AF_DECnet ideal-int
pkg syscall (darwin-arm64), const AF_DLI = 13
pkg syscall (darwin-arm64), const AF_DLI ideal-int
pkg syscall (darwin-arm64), const AF_E164 = 28
pkg syscall (darwin-arm64), const AF_E164 ideal-int
pkg syscall (darwin-arm64), const AF_ECMA = 8
pkg syscall (darwin-arm64), const AF_ECMA ideal-int
pkg syscall (darwin-arm64), const AF_HYLINK = 15
pkg syscall (darwin-arm64), const AF_HYLINK ideal-int
pkg syscall (darwin-arm64), const AF_IEEE80211 = 37
pkg syscall (darwin-arm64), const AF_IEEE80211 ideal-int
pkg syscall (darwin-arm64), const AF_IMPLINK = 3
pkg syscall (darwin-arm64), const AF_IMPLINK ideal-int
pkg syscall (darwin-arm64), const AF_INET6 = 30
pkg syscall (darwin-arm64), const AF_IPX = 23
pkg syscall (darwin-arm64), const AF_IPX ideal-int
pkg syscall (darwin-arm64), const AF_ISDN = 28
pkg syscall (darwin-arm64), const AF_ISDN ideal-int
pkg syscall (darwin-arm64), const AF_ISO = 7
pkg syscall (darwin-arm64), const AF_ISO ideal-int
pkg syscall (darwin-arm64), const AF_LAT = 14
pkg syscall (darwin-arm64), const AF_LAT ideal-int
pkg syscall (darwin-arm64), const AF_LINK = 18
pkg syscall (darwin-arm64), const AF_LINK ideal-int
pkg syscall (darwin-arm64), const AF_LOCAL = 1
pkg syscall (darwin-arm64), const AF_LOCAL ideal-int
pkg syscall (darwin-arm64), const AF_MAX = 40
pkg syscall (darwin-arm64), const AF_MAX ideal-int
pkg syscall (darwin-arm64), const AF_NATM = 31
pkg syscall (darwin-arm64), const AF_NATM ideal-int
pkg syscall (darwin-arm64), const AF_NDRV = 27
pkg syscall (darwin-arm64), const AF_NDRV ideal-int
pkg syscall (darwin-arm64), const AF_NETBIOS = 33
pkg syscall (darwin-arm64), const AF_NETBIOS ideal-int
pkg syscall (darwin-arm64), const AF_NS = 6
pkg syscall (darwin-arm64), const AF_NS ideal-int
pkg syscall (darwin-arm64), const AF_OSI = 7
pkg syscall (darwin-arm64), const AF_OSI ideal-int
pkg syscall (darwin-arm64), const AF_PPP = 34
pkg syscall (darwin-arm64), const AF_PPP ideal-int
pkg syscall (darwin-arm64), const AF_PUP = 4
pkg syscall (darwin-arm64), const AF_PUP ideal-int
pkg syscall (darwin-arm64), const AF_RESERVED_36 = 36
pkg syscall (darwin-arm64), const AF_RESERVED_36 ideal-int
pkg syscall (darwin-arm64), const AF_ROUTE = 17
pkg syscall (darwin-arm64), const AF_ROUTE ideal-int
pkg syscall (darwin-arm64), const AF_SIP = 24
pkg syscall (darwin-arm64), const AF_SIP ideal-int
pkg syscall (darwin-arm64), const AF_SNA = 11
pkg syscall (darwin-arm64), const AF_SNA ideal-int
pkg syscall (darwin-arm64), const AF_SYSTEM = 32
pkg syscall (darwin-arm64), const AF_SYSTEM ideal-int
pkg syscall (darwin-arm64), const AF_UTUN = 38
pkg syscall (darwin-arm64), const AF_UTUN ideal-int
pkg syscall (darwin-arm64), const B0 = 0
pkg syscall (darwin-arm64), const B0 ideal-int
pkg syscall (darwin-arm64), const B110 = 110
pkg syscall (darwin-arm64), const B110 ideal-int
pkg syscall (darwin-arm64), const B115200 = 115200
pkg syscall (darwin-arm64), const B115200 ideal-int
pkg syscall (darwin-arm64), const B1200 = 1200
pkg syscall (darwin-arm64), const B1200 ideal-int
pkg syscall (darwin-arm64), const B134 = 134
pkg syscall (darwin-arm64), const B134 ideal-int
pkg syscall (darwin-arm64), const B14400 = 14400
pkg syscall (darwin-arm64), const B14400 ideal-int
pkg syscall (darwin-arm64), const B150 = 150
pkg syscall (darwin-arm64), const B150 ideal-int
pkg syscall (darwin-arm64), const B1800 = 1800
pkg syscall (darwin-arm64), const B1800 ideal-int
pkg syscall (darwin-arm64), const B19200 = 19200
pkg syscall (darwin-arm64), const B19200 ideal-int
pkg syscall (darwin-arm64), const B200 = 200
pkg syscall (darwin-arm64), const B200 ideal-int
pkg syscall (darwin-arm64), const B230400 = 230400
pkg syscall (darwin-arm64), const B230400 ideal-int
pkg syscall (darwin-arm64), const B2400 = 2400
pkg syscall (darwin-arm64), const B2400 ideal-int
pkg syscall (darwin-arm64), const B28800 = 28800
pkg syscall (darwin-arm64), const B28800 ideal-int
pkg syscall (darwin-arm64), const B300 = 300
pkg syscall (darwin-arm64), const B300 ideal-int
pkg syscall (darwin-arm64), const B38400 = 38400
pkg syscall (darwin-arm64), const B38400 ideal-int
pkg syscall (darwin-arm64), const B4800 = 4800
pkg syscall (darwin-arm64), const B4800 ideal-int
pkg syscall (darwin-arm64), const B50 = 50
pkg syscall (darwin-arm64), const B50 ideal-int
pkg syscall (darwin-arm64), const B57600 = 57600
pkg syscall (darwin-arm64), const B57600 ideal-int
pkg syscall (darwin-arm64), const B600 = 600
pkg syscall (darwin-arm64), const B600 ideal-int
pkg syscall (darwin-arm64), const B7200 = 7200
pkg syscall (darwin-arm64), const B7200 ideal-int
pkg syscall (darwin-arm64), const B75 = 75
pkg syscall (darwin-arm64), const B75 ideal-int
pkg syscall (darwin-arm64), const B76800 = 76800
pkg syscall (darwin-arm64), const B76800 ideal-int
pkg syscall (darwin-arm64), const B9600 = 9600
pkg syscall (darwin-arm64), const B9600 ideal-int
pkg syscall (darwin-arm64), const BIOCFLUSH = 536887912
pkg syscall (darwin-arm64), const BIOCFLUSH ideal-int
pkg syscall (darwin-arm64), const BIOCGBLEN = 1074020966
pkg syscall (darwin-arm64), const BIOCGBLEN ideal-int
pkg syscall (darwin-arm64), const BIOCGDLT = 1074020970
pkg syscall (darwin-arm64), const BIOCGDLT ideal-int
pkg syscall (darwin-arm64), const BIOCGDLTLIST = 3222028921
pkg syscall (darwin-arm64), const BIOCGDLTLIST ideal-int
pkg syscall (darwin-arm64), const BIOCGETIF = 1075855979
pkg syscall (darwin-arm64), const BIOCGETIF ideal-int
pkg syscall (darwin-arm64), const BIOCGHDRCMPLT = 1074020980
pkg syscall (darwin-arm64), const BIOCGHDRCMPLT ideal-int
pkg syscall (darwin-arm64), const BIOCGRSIG = 1074020978
pkg syscall (darwin-arm64), const BIOCGRSIG ideal-int
pkg syscall (darwin-arm64), const BIOCGRTIMEOUT = 1074807406
pkg syscall (darwin-arm64), const BIOCGRTIMEOUT ideal-int
pkg syscall (darwin-arm64), const BIOCGSEESENT = 1074020982
pkg syscall (darwin-arm64), const BIOCGSEESENT ideal-int
pkg syscall (darwin-arm64), const BIOCGSTATS = 1074283119
pkg syscall (darwin-arm64), const BIOCGSTATS ideal-int
pkg syscall (darwin-arm64), const BIOCIMMEDIATE = 2147762800
pkg syscall (darwin-arm64), const BIOCIMMEDIATE ideal-int
pkg syscall (darwin-arm64), const BIOCPROMISC = 536887913
pkg syscall (darwin-arm64), const BIOCPROMISC ideal-int
pkg syscall (darwin-arm64), const BIOCSBLEN = 3221504614
pkg syscall (darwin-arm64), const BIOCSBLEN ideal-int
pkg syscall (darwin-arm64), const BIOCSDLT = 2147762808
pkg syscall (darwin-arm64), const BIOCSDLT ideal-int
pkg syscall (darwin-arm64), const BIOCSETF = 2148549223
pkg syscall (darwin-arm64), const BIOCSETF ideal-int
pkg syscall (darwin-arm64), const BIOCSETIF = 2149597804
pkg syscall (darwin-arm64), const BIOCSETIF ideal-int
pkg syscall (darwin-arm64), const BIOCSHDRCMPLT = 2147762805
pkg syscall (darwin-arm64), const BIOCSHDRCMPLT ideal-int
pkg syscall (darwin-arm64), const BIOCSRSIG = 2147762803
pkg syscall (darwin-arm64), const BIOCSRSIG ideal-int
pkg syscall (darwin-arm64), const BIOCSRTIMEOUT = 2148549229
pkg syscall (darwin-arm64), const BIOCSRTIMEOUT ideal-int
pkg syscall (darwin-arm64), const BIOCSSEESENT = 2147762807
pkg syscall (darwin-arm64), const BIOCSSEESENT ideal-int
pkg syscall (darwin-arm64), const BIOCVERSION = 1074020977
pkg syscall (darwin-arm64), const BIOCVERSION ideal-int
pkg syscall (darwin-arm64), const BPF_A = 16
pkg syscall (darwin-arm64), const BPF_A ideal-int
pkg syscall (darwin-arm64), const BPF_ABS = 32
pkg syscall (darwin-arm64), const BPF_ABS ideal-int
pkg syscall (darwin-arm64), const BPF_ADD = 0
pkg syscall (darwin-arm64), const BPF_ADD ideal-int
pkg syscall (darwin-arm64), const BPF_ALIGNMENT = 4
pkg syscall (darwin-arm64), const BPF_ALIGNMENT ideal-int
pkg syscall (darwin-arm64), const BPF_ALU = 4
pkg syscall (darwin-arm64), const BPF_ALU ideal-int
pkg syscall (darwin-arm64), const BPF_AND = 80
pkg syscall (darwin-arm64), const BPF_AND ideal-int
pkg syscall (darwin-arm64), const BPF_B = 16
pkg syscall (darwin-arm64), const BPF_B ideal-int
pkg syscall (darwin-arm64), const BPF_DIV = 48
pkg syscall (darwin-arm64), const BPF_DIV ideal-int
pkg syscall (darwin-arm64), const BPF_H = 8
pkg syscall (darwin-arm64), const BPF_H ideal-int
pkg syscall (darwin-arm64), const BPF_IMM = 0
pkg syscall (darwin-arm64), const BPF_IMM ideal-int
pkg syscall (darwin-arm64), const BPF_IND = 64
pkg syscall (darwin-arm64), const BPF_IND ideal-int
pkg syscall (darwin-arm64), const BPF_JA = 0
pkg syscall (darwin-arm64), const BPF_JA ideal-int
pkg syscall (darwin-arm64), const BPF_JEQ = 16
pkg syscall (darwin-arm64), const BPF_JEQ ideal-int
pkg syscall (darwin-arm64), const BPF_JGE = 48
pkg syscall (darwin-arm64), const BPF_JGE ideal-int
pkg syscall (darwin-arm64), const BPF_JGT = 32
pkg syscall (darwin-arm64), const BPF_JGT ideal-int
pkg syscall (darwin-arm64), const BPF_JMP = 5
pkg syscall (darwin-arm64), const BPF_JMP ideal-int
pkg syscall (darwin-arm64), const BPF_JSET = 64
pkg syscall (darwin-arm64), const BPF_JSET ideal-int
pkg syscall (darwin-arm64), const BPF_K = 0
pkg syscall (darwin-arm64), const BPF_K ideal-int
pkg syscall (darwin-arm64), const BPF_LD = 0
pkg syscall (darwin-arm64), const BPF_LD ideal-int
pkg syscall (darwin-arm64), const BPF_LDX = 1
pkg syscall (darwin-arm64), const BPF_LDX ideal-int
pkg syscall (darwin-arm64), const BPF_LEN = 128
pkg syscall (darwin-arm64), const BPF_LEN ideal-int
pkg syscall (darwin-arm64), const BPF_LSH = 96
pkg syscall (darwin-arm64), const BPF_LSH ideal-int
pkg syscall (darwin-arm64), const BPF_MAJOR_VERSION = 1
pkg syscall (darwin-arm64), const BPF_MAJOR_VERSION ideal-int
pkg syscall (darwin-arm64), const BPF_MAXBUFSIZE = 524288
pkg syscall (darwin-arm64), const BPF_MAXBUFSIZE ideal-int
pkg syscall (darwin-arm64), const BPF_MAXINSNS = 512
pkg syscall (darwin-arm64), const BPF_MAXINSNS ideal-int
pkg syscall (darwin-arm64), const BPF_MEM = 96
pkg syscall (darwin-arm64), const BPF_MEM ideal-int
pkg syscall (darwin-arm64), const BPF_MEMWORDS = 16
pkg syscall (darwin-arm64), const BPF_MEMWORDS ideal-int
pkg syscall (darwin-arm64), const BPF_MINBUFSIZE = 32
pkg syscall (darwin-arm64), const BPF_MINBUFSIZE ideal-int
pkg syscall (darwin-arm64), const BPF_MINOR_VERSION = 1
pkg syscall (darwin-arm64), const BPF_MINOR_VERSION ideal-int
pkg syscall (darwin-arm64), const BPF_MISC = 7
pkg syscall (darwin-arm64), const BPF_MISC ideal-int
pkg syscall (darwin-arm64), const BPF_MSH = 160
pkg syscall (darwin-arm64), const BPF_MSH ideal-int
pkg syscall (darwin-arm64), const BPF_MUL = 32
pkg syscall (darwin-arm64), const BPF_MUL ideal-int
pkg syscall (darwin-arm64), const BPF_NEG = 128
pkg syscall (darwin-arm64), const BPF_NEG ideal-int
pkg syscall (darwin-arm64), const BPF_OR = 64
pkg syscall (darwin-arm64), const BPF_OR ideal-int
pkg syscall (darwin-arm64), const BPF_RELEASE = 199606
pkg syscall (darwin-arm64), const BPF_RELEASE ideal-int
pkg syscall (darwin-arm64), const BPF_RET = 6
pkg syscall (darwin-arm64), const BPF_RET ideal-int
pkg syscall (darwin-arm64), const BPF_RSH = 112
pkg syscall (darwin-arm64), const BPF_RSH ideal-int
pkg syscall (darwin-arm64), const BPF_ST = 2
pkg syscall (darwin-arm64), const BPF_ST ideal-int
pkg syscall (darwin-arm64), const BPF_STX = 3
pkg syscall (darwin-arm64), const BPF_STX ideal-int
pkg syscall (darwin-arm64), const BPF_SUB = 16
pkg syscall (darwin-arm64), const BPF_SUB ideal-int
pkg syscall (darwin-arm64), const BPF_TAX = 0
pkg syscall (darwin-arm64), const BPF_TAX ideal-int
pkg syscall (darwin-arm64), const BPF_TXA = 128
pkg syscall (darwin-arm64), const BPF_TXA ideal-int
pkg syscall (darwin-arm64), const BPF_W = 0
pkg syscall (darwin-arm64), const BPF_W ideal-int
pkg syscall (darwin-arm64), const BPF_X = 8
pkg syscall (darwin-arm64), const BPF_X ideal-int
pkg syscall (darwin-arm64), const BRKINT = 2
pkg syscall (darwin-arm64), const BRKINT ideal-int
pkg syscall (darwin-arm64), const CFLUSH = 15
pkg syscall (darwin-arm64), const CFLUSH ideal-int
pkg syscall (darwin-arm64), const CLOCAL = 32768
pkg syscall (darwin-arm64), const CLOCAL ideal-int
pkg syscall (darwin-arm64), const CREAD = 2048
pkg syscall (darwin-arm64), const CREAD ideal-int
pkg syscall (darwin-arm64), const CS5 = 0
pkg syscall (darwin-arm64), const CS5 ideal-int
pkg syscall (darwin-arm64), const CS6 = 256
pkg syscall (darwin-arm64), const CS6 ideal-int
pkg syscall (darwin-arm64), const CS7 = 512
pkg syscall (darwin-arm64), const CS7 ideal-int
pkg syscall (darwin-arm64), const CS8 = 768
pkg syscall (darwin-arm64), const CS8 ideal-int
pkg syscall (darwin-arm64), const CSIZE = 768
pkg syscall (darwin-arm64), const CSIZE ideal-int
pkg syscall (darwin-arm64), const CSTART = 17
pkg syscall (darwin-arm64), const CSTART ideal-int
pkg syscall (darwin-arm64), const CSTATUS = 20
pkg syscall (darwin-arm64), const CSTATUS ideal-int
pkg syscall (darwin-arm64), const CSTOP = 19
pkg syscall (darwin-arm64), const CSTOP ideal-int
pkg syscall (darwin-arm64), const CSTOPB = 1024
pkg syscall (darwin-arm64), const CSTOPB ideal-int
pkg syscall (darwin-arm64), const CSUSP = 26
pkg syscall (darwin-arm64), const CSUSP ideal-int
pkg syscall (darwin-arm64), const CTL_MAXNAME = 12
pkg syscall (darwin-arm64), const CTL_MAXNAME ideal-int
pkg syscall (darwin-arm64), const CTL_NET = 4
pkg syscall (darwin-arm64), const CTL_NET ideal-int
pkg syscall (darwin-arm64), const DLT_APPLE_IP_OVER_IEEE1394 = 138
pkg syscall (darwin-arm64), const DLT_APPLE_IP_OVER_IEEE1394 ideal-int
pkg syscall (darwin-arm64), const DLT_ARCNET = 7
pkg syscall (darwin-arm64), const DLT_ARCNET ideal-int
pkg syscall (darwin-arm64), const DLT_ATM_CLIP = 19
pkg syscall (darwin-arm64), const DLT_ATM_CLIP ideal-int
pkg syscall (darwin-arm64), const DLT_ATM_RFC1483 = 11
pkg syscall (darwin-arm64), const DLT_ATM_RFC1483 ideal-int
pkg syscall (darwin-arm64), const DLT_AX25 = 3
pkg syscall (darwin-arm64), const DLT_AX25 ideal-int
pkg syscall (darwin-arm64), const DLT_CHAOS = 5
pkg syscall (darwin-arm64), const DLT_CHAOS ideal-int
pkg syscall (darwin-arm64), const DLT_CHDLC = 104
pkg syscall (darwin-arm64), const DLT_CHDLC ideal-int
pkg syscall (darwin-arm64), const DLT_C_HDLC = 104
pkg syscall (darwin-arm64), const DLT_C_HDLC ideal-int
pkg syscall (darwin-arm64), const DLT_EN10MB = 1
pkg syscall (darwin-arm64), const DLT_EN10MB ideal-int
pkg syscall (darwin-arm64), const DLT_EN3MB = 2
pkg syscall (darwin-arm64), const DLT_EN3MB ideal-int
pkg syscall (darwin-arm64), const DLT_FDDI = 10
pkg syscall (darwin-arm64), const DLT_FDDI ideal-int
pkg syscall (darwin-arm64), const DLT_IEEE802 = 6
pkg syscall (darwin-arm64), const DLT_IEEE802 ideal-int
pkg syscall (darwin-arm64), const DLT_IEEE802_11 = 105
pkg syscall (darwin-arm64), const DLT_IEEE802_11 ideal-int
pkg syscall (darwin-arm64), const DLT_IEEE802_11_RADIO = 127
pkg syscall (darwin-arm64), const DLT_IEEE802_11_RADIO ideal-int
pkg syscall (darwin-arm64), const DLT_IEEE802_11_RADIO_AVS = 163
pkg syscall (darwin-arm64), const DLT_IEEE802_11_RADIO_AVS ideal-int
pkg syscall (darwin-arm64), const DLT_LINUX_SLL = 113
pkg syscall (darwin-arm64), const DLT_LINUX_SLL ideal-int
pkg syscall (darwin-arm64), const DLT_LOOP = 108
pkg syscall (darwin-arm64), const DLT_LOOP ideal-int
pkg syscall (darwin-arm64), const DLT_NULL = 0
pkg syscall (darwin-arm64), const DLT_NULL ideal-int
pkg syscall (darwin-arm64), const DLT_PFLOG = 117
pkg syscall (darwin-arm64), const DLT_PFLOG ideal-int
pkg syscall (darwin-arm64), const DLT_PFSYNC = 18
pkg syscall (darwin-arm64), const DLT_PFSYNC ideal-int
pkg syscall (darwin-arm64), const DLT_PPP = 9
pkg syscall (darwin-arm64), const DLT_PPP ideal-int
pkg syscall (darwin-arm64), const DLT_PPP_BSDOS = 16
pkg syscall (darwin-arm64), const DLT_PPP_BSDOS ideal-int
pkg syscall (darwin-arm64), const DLT_PPP_SERIAL = 50
pkg syscall (darwin-arm64), const DLT_PPP_SERIAL ideal-int
pkg syscall (darwin-arm64), const DLT_PRONET = 4
pkg syscall (darwin-arm64), const DLT_PRONET ideal-int
pkg syscall (darwin-arm64), const DLT_RAW = 12
pkg syscall (darwin-arm64), const DLT_RAW ideal-int
pkg syscall (darwin-arm64), const DLT_SLIP = 8
pkg syscall (darwin-arm64), const DLT_SLIP ideal-int
pkg syscall (darwin-arm64), const DLT_SLIP_BSDOS = 15
pkg syscall (darwin-arm64), const DLT_SLIP_BSDOS ideal-int
pkg syscall (darwin-arm64), const DT_BLK = 6
pkg syscall (darwin-arm64), const DT_BLK ideal-int
pkg syscall (darwin-arm64), const DT_CHR = 2
pkg syscall (darwin-arm64), const DT_CHR ideal-int
pkg syscall (darwin-arm64), const DT_DIR = 4
pkg syscall (darwin-arm64), const DT_DIR ideal-int
pkg syscall (darwin-arm64), const DT_FIFO = 1
pkg syscall (darwin-arm64), const DT_FIFO ideal-int
pkg syscall (darwin-arm64), const DT_LNK = 10
pkg syscall (darwin-arm64), const DT_LNK ideal-int
pkg syscall (darwin-arm64), const DT_REG = 8
pkg syscall (darwin-arm64), const DT_REG ideal-int
pkg syscall (darwin-arm64), const DT_SOCK = 12
pkg syscall (darwin-arm64), const DT_SOCK ideal-int
pkg syscall (darwin-arm64), const DT_UNKNOWN = 0
pkg syscall (darwin-arm64), const DT_UNKNOWN ideal-int
pkg syscall (darwin-arm64), const DT_WHT = 14
pkg syscall (darwin-arm64), const DT_WHT ideal-int
pkg syscall (darwin-arm64), const E2BIG = 7
pkg syscall (darwin-arm64), const EACCES = 13
pkg syscall (darwin-arm64), const EADDRINUSE = 48
pkg syscall (darwin-arm64), const EADDRNOTAVAIL = 49
pkg syscall (darwin-arm64), const EAFNOSUPPORT = 47
pkg syscall (darwin-arm64), const EAGAIN = 35
pkg syscall (darwin-arm64), const EALREADY = 37
pkg syscall (darwin-arm64), const EAUTH = 80
pkg syscall (darwin-arm64), const EAUTH Errno
pkg syscall (darwin-arm64), const EBADARCH = 86
pkg syscall (darwin-arm64), const EBADARCH Errno
pkg syscall (darwin-arm64), const EBADEXEC = 85
pkg syscall (darwin-arm64), const EBADEXEC Errno
pkg syscall (darwin-arm64), const EBADF = 9
pkg syscall (darwin-arm64), const EBADMACHO = 88
pkg syscall (darwin-arm64), const EBADMACHO Errno
pkg syscall (darwin-arm64), const EBADMSG = 94
pkg syscall (darwin-arm64), const EBADMSG Errno
pkg syscall (darwin-arm64), const EBADRPC = 72
pkg syscall (darwin-arm64), const EBADRPC Errno
pkg syscall (darwin-arm64), const EBUSY = 16
pkg syscall (darwin-arm64), const ECANCELED = 89
pkg syscall (darwin-arm64), const ECHILD = 10
pkg syscall (darwin-arm64), const ECHO = 8
pkg syscall (darwin-arm64), const ECHO ideal-int
pkg syscall (darwin-arm64), const ECHOCTL = 64
pkg syscall (darwin-arm64), const ECHOCTL ideal-int
pkg syscall (darwin-arm64), const ECHOE = 2
pkg syscall (darwin-arm64), const ECHOE ideal-int
pkg syscall (darwin-arm64), const ECHOK = 4
pkg syscall (darwin-arm64), const ECHOK ideal-int
pkg syscall (darwin-arm64), const ECHOKE = 1
pkg syscall (darwin-arm64), const ECHOKE ideal-int
pkg syscall (darwin-arm64), const ECHONL = 16
pkg syscall (darwin-arm64), const ECHONL ideal-int
pkg syscall (darwin-arm64), const ECHOPRT = 32
pkg syscall (darwin-arm64), const ECHOPRT ideal-int
pkg syscall (darwin-arm64), const ECONNABORTED = 53
pkg syscall (darwin-arm64), const ECONNREFUSED = 61
pkg syscall (darwin-arm64), const ECONNRESET = 54
pkg syscall (darwin-arm64), const EDEADLK = 11
pkg syscall (darwin-arm64), const EDESTADDRREQ = 39
pkg syscall (darwin-arm64), const EDEVERR = 83
pkg syscall (darwin-arm64), const EDEVERR Errno
pkg syscall (darwin-arm64), const EDOM = 33
pkg syscall (darwin-arm64), const EDQUOT = 69
pkg syscall (darwin-arm64), const EEXIST = 17
pkg syscall (darwin-arm64), const EFAULT = 14
pkg syscall (darwin-arm64), const EFBIG = 27
pkg syscall (darwin-arm64), const EFTYPE = 79
pkg syscall (darwin-arm64), const EFTYPE Errno
pkg syscall (darwin-arm64), const EHOSTDOWN = 64
pkg syscall (darwin-arm64), const EHOSTUNREACH = 65
pkg syscall (darwin-arm64), const EIDRM = 90
pkg syscall (darwin-arm64), const EILSEQ = 92
pkg syscall (darwin-arm64), const EINPROGRESS = 36
pkg syscall (darwin-arm64), const EINTR = 4
pkg syscall (darwin-arm64), const EINVAL = 22
pkg syscall (darwin-arm64), const EIO = 5
pkg syscall (darwin-arm64), const EISCONN = 56
pkg syscall (darwin-arm64), const EISDIR = 21
pkg syscall (darwin-arm64), const ELAST = 106
pkg syscall (darwin-arm64), const ELAST Errno
pkg syscall (darwin-arm64), const ELOOP = 62
pkg syscall (darwin-arm64), const EMFILE = 24
pkg syscall (darwin-arm64), const EMLINK = 31
pkg syscall (darwin-arm64), const EMSGSIZE = 40
pkg syscall (darwin-arm64), const EMULTIHOP = 95
pkg syscall (darwin-arm64), const EMULTIHOP Errno
pkg syscall (darwin-arm64), const ENAMETOOLONG = 63
pkg syscall (darwin-arm64), const ENEEDAUTH = 81
pkg syscall (darwin-arm64), const ENEEDAUTH Errno
pkg syscall (darwin-arm64), const ENETDOWN = 50
pkg syscall (darwin-arm64), const ENETRESET = 52
pkg syscall (darwin-arm64), const ENETUNREACH = 51
pkg syscall (darwin-arm64), const ENFILE = 23
pkg syscall (darwin-arm64), const ENOATTR = 93
pkg syscall (darwin-arm64), const ENOATTR Errno
pkg syscall (darwin-arm64), const ENOBUFS = 55
pkg syscall (darwin-arm64), const ENODATA = 96
pkg syscall (darwin-arm64), const ENODATA Errno
pkg syscall (darwin-arm64), const ENODEV = 19
pkg syscall (darwin-arm64), const ENOEXEC = 8
pkg syscall (darwin-arm64), const ENOLCK = 77
pkg syscall (darwin-arm64), const ENOLINK = 97
pkg syscall (darwin-arm64), const ENOLINK Errno
pkg syscall (darwin-arm64), const ENOMEM = 12
pkg syscall (darwin-arm64), const ENOMSG = 91
pkg syscall (darwin-arm64), const ENOPOLICY = 103
pkg syscall (darwin-arm64), const ENOPOLICY Errno
pkg syscall (darwin-arm64), const ENOPROTOOPT = 42
pkg syscall (darwin-arm64), const ENOSPC = 28
pkg syscall (darwin-arm64), const ENOSR = 98
pkg syscall (darwin-arm64), const ENOSR Errno
pkg syscall (darwin-arm64), const ENOSTR = 99
pkg syscall (darwin-arm64), const ENOSTR Errno
pkg syscall (darwin-arm64), const ENOSYS = 78
pkg syscall (darwin-arm64), const ENOTBLK = 15
pkg syscall (darwin-arm64), const ENOTCONN = 57
pkg syscall (darwin-arm64), const ENOTDIR = 20
pkg syscall (darwin-arm64), const ENOTEMPTY = 66
pkg syscall (darwin-arm64), const ENOTRECOVERABLE = 104
pkg syscall (darwin-arm64), const ENOTRECOVERABLE Errno
pkg syscall (darwin-arm64), const ENOTSOCK = 38
pkg syscall (darwin-arm64), const ENOTSUP = 45
pkg syscall (darwin-arm64), const ENOTTY = 25
pkg syscall (darwin-arm64), const ENXIO = 6
pkg syscall (darwin-arm64), const EOPNOTSUPP = 102
pkg syscall (darwin-arm64), const EOVERFLOW = 84
pkg syscall (darwin-arm64), const EOWNERDEAD = 105
pkg syscall (darwin-arm64), const EOWNERDEAD Errno
pkg syscall (darwin-arm64), const EPERM = 1
pkg syscall (darwin-arm64), const EPFNOSUPPORT = 46
pkg syscall (darwin-arm64), const EPIPE = 32
pkg syscall (darwin-arm64), const EPROCLIM = 67
pkg syscall (darwin-arm64), const EPROCLIM Errno
pkg syscall (darwin-arm64), const EPROCUNAVAIL = 76
pkg syscall (darwin-arm64), const EPROCUNAVAIL Errno
pkg syscall (darwin-arm64), const EPROGMISMATCH = 75
pkg syscall (darwin-arm64), const EPROGMISMATCH Errno
pkg syscall (darwin-arm64), const EPROGUNAVAIL = 74
pkg syscall (darwin-arm64), const EPROGUNAVAIL Errno
pkg syscall (darwin-arm64), const EPROTO = 100
pkg syscall (darwin-arm64), const EPROTO Errno
pkg syscall (darwin-arm64), const EPROTONOSUPPORT = 43
pkg syscall (darwin-arm64), const EPROTOTYPE = 41
pkg syscall (darwin-arm64), const EPWROFF = 82
pkg syscall (darwin-arm64), const EPWROFF Errno
pkg syscall (darwin-arm64), const EQFULL = 106
pkg syscall (darwin-arm64), const EQFULL Errno
pkg syscall (darwin-arm64), const ERANGE = 34
pkg syscall (darwin-arm64), const EREMOTE = 71
pkg syscall (darwin-arm64), const EROFS = 30
pkg syscall (darwin-arm64), const ERPCMISMATCH = 73
pkg syscall (darwin-arm64), const ERPCMISMATCH Errno
pkg syscall (darwin-arm64), const ESHLIBVERS = 87
pkg syscall (darwin-arm64), const ESHLIBVERS Errno
pkg syscall (darwin-arm64), const ESHUTDOWN = 58
pkg syscall (darwin-arm64), const ESOCKTNOSUPPORT = 44
pkg syscall (darwin-arm64), const ESPIPE = 29
pkg syscall (darwin-arm64), const ESRCH = 3
pkg syscall (darwin-arm64), const ESTALE = 70
pkg syscall (darwin-arm64), const ETIME = 101
pkg syscall (darwin-arm64), const ETIME Errno
pkg syscall (darwin-arm64), const ETIMEDOUT = 60
pkg syscall (darwin-arm64), const ETOOMANYREFS = 59
pkg syscall (darwin-arm64), const ETXTBSY = 26
pkg syscall (darwin-arm64), const EUSERS = 68
pkg syscall (darwin-arm64), const EVFILT_AIO = -3
pkg syscall (darwin-arm64), const EVFILT_AIO ideal-int
pkg syscall (darwin-arm64), const EVFILT_FS = -9
pkg syscall (darwin-arm64), const EVFILT_FS ideal-int
pkg syscall (darwin-arm64), const EVFILT_MACHPORT = -8
pkg syscall (darwin-arm64), const EVFILT_MACHPORT ideal-int
pkg syscall (darwin-arm64), const EVFILT_PROC = -5
pkg syscall (darwin-arm64), const EVFILT_PROC ideal-int
pkg syscall (darwin-arm64), const EVFILT_READ = -1
pkg syscall (darwin-arm64), const EVFILT_READ ideal-int
pkg syscall (darwin-arm64), const EVFILT_SIGNAL = -6
pkg syscall (darwin-arm64), const EVFILT_SIGNAL ideal-int
pkg syscall (darwin-arm64), const EVFILT_SYSCOUNT = 14
pkg syscall (darwin-arm64), const EVFILT_SYSCOUNT ideal-int
pkg syscall (darwin-arm64), const EVFILT_THREADMARKER = 14
pkg syscall (darwin-arm64), const EVFILT_THREADMARKER ideal-int
pkg syscall (darwin-arm64), const EVFILT_TIMER = -7
pkg syscall (darwin-arm64), const EVFILT_TIMER ideal-int
pkg syscall (darwin-arm64), const EVFILT_USER = -10
pkg syscall (darwin-arm64), const EVFILT_USER ideal-int
pkg syscall (darwin-arm64), const EVFILT_VM = -12
pkg syscall (darwin-arm64), const EVFILT_VM ideal-int
pkg syscall (darwin-arm64), const EVFILT_VNODE = -4
pkg syscall (darwin-arm64), const EVFILT_VNODE ideal-int
pkg syscall (darwin-arm64), const EVFILT_WRITE = -2
pkg syscall (darwin-arm64), const EVFILT_WRITE ideal-int
pkg syscall (darwin-arm64), const EV_ADD = 1
pkg syscall (darwin-arm64), const EV_ADD ideal-int
pkg syscall (darwin-arm64), const EV_CLEAR = 32
pkg syscall (darwin-arm64), const EV_CLEAR ideal-int
pkg syscall (darwin-arm64), const EV_DELETE = 2
pkg syscall (darwin-arm64), const EV_DELETE ideal-int
pkg syscall (darwin-arm64), const EV_DISABLE = 8
pkg syscall (darwin-arm64), const EV_DISABLE ideal-int
pkg syscall (darwin-arm64), const EV_DISPATCH = 128
pkg syscall (darwin-arm64), const EV_DISPATCH ideal-int
pkg syscall (darwin-arm64), const EV_ENABLE = 4
pkg syscall (darwin-arm64), const EV_ENABLE ideal-int
pkg syscall (darwin-arm64), const EV_EOF = 32768
pkg syscall (darwin-arm64), const EV_EOF ideal-int
pkg syscall (darwin-arm64), const EV_ERROR = 16384
pkg syscall (darwin-arm64), const EV_ERROR ideal-int
pkg syscall (darwin-arm64), const EV_FLAG0 = 4096
pkg syscall (darwin-arm64), const EV_FLAG0 ideal-int
pkg syscall (darwin-arm64), const EV_FLAG1 = 8192
pkg syscall (darwin-arm64), const EV_FLAG1 ideal-int
pkg syscall (darwin-arm64), const EV_ONESHOT = 16
pkg syscall (darwin-arm64), const EV_ONESHOT ideal-int
pkg syscall (darwin-arm64), const EV_OOBAND = 8192
pkg syscall (darwin-arm64), const EV_OOBAND ideal-int
pkg syscall (darwin-arm64), const EV_POLL = 4096
pkg syscall (darwin-arm64), const EV_POLL ideal-int
pkg syscall (darwin-arm64), const EV_RECEIPT = 64
pkg syscall (darwin-arm64), const EV_RECEIPT ideal-int
pkg syscall (darwin-arm64), const EV_SYSFLAGS = 61440
pkg syscall (darwin-arm64), const EV_SYSFLAGS ideal-int
pkg syscall (darwin-arm64), const EWOULDBLOCK = 35
pkg syscall (darwin-arm64), const EXDEV = 18
pkg syscall (darwin-arm64), const EXTA = 19200
pkg syscall (darwin-arm64), const EXTA ideal-int
pkg syscall (darwin-arm64), const EXTB = 38400
pkg syscall (darwin-arm64), const EXTB ideal-int
pkg syscall (darwin-arm64), const EXTPROC = 2048
pkg syscall (darwin-arm64), const EXTPROC ideal-int
pkg syscall (darwin-arm64), const FD_CLOEXEC = 1
pkg syscall (darwin-arm64), const FD_CLOEXEC ideal-int
pkg syscall (darwin-arm64), const FD_SETSIZE = 1024
pkg syscall (darwin-arm64), const FD_SETSIZE ideal-int
pkg syscall (darwin-arm64), const FLUSHO = 8388608
pkg syscall (darwin-arm64), const FLUSHO ideal-int
pkg syscall (darwin-arm64), const F_ADDFILESIGS = 61
pkg syscall (darwin-arm64), const F_ADDFILESIGS ideal-int
pkg syscall (darwin-arm64), const F_ADDSIGS = 59
pkg syscall (darwin-arm64), const F_ADDSIGS ideal-int
pkg syscall (darwin-arm64), const F_ALLOCATEALL = 4
pkg syscall (darwin-arm64), const F_ALLOCATEALL ideal-int
pkg syscall (darwin-arm64), const F_ALLOCATECONTIG = 2
pkg syscall (darwin-arm64), const F_ALLOCATECONTIG ideal-int
pkg syscall (darwin-arm64), const F_CHKCLEAN = 41
pkg syscall (darwin-arm64), const F_CHKCLEAN ideal-int
pkg syscall (darwin-arm64), const F_DUPFD = 0
pkg syscall (darwin-arm64), const F_DUPFD ideal-int
pkg syscall (darwin-arm64), const F_DUPFD_CLOEXEC = 67
pkg syscall (darwin-arm64), const F_DUPFD_CLOEXEC ideal-int
pkg syscall (darwin-arm64), const F_FINDSIGS = 78
pkg syscall (darwin-arm64), const F_FINDSIGS ideal-int
pkg syscall (darwin-arm64), const F_FLUSH_DATA = 40
pkg syscall (darwin-arm64), const F_FLUSH_DATA ideal-int
pkg syscall (darwin-arm64), const F_FREEZE_FS = 53
pkg syscall (darwin-arm64), const F_FREEZE_FS ideal-int
pkg syscall (darwin-arm64), const F_FULLFSYNC = 51
pkg syscall (darwin-arm64), const F_FULLFSYNC ideal-int
pkg syscall (darwin-arm64), const F_GETCODEDIR = 72
pkg syscall (darwin-arm64), const F_GETCODEDIR ideal-int
pkg syscall (darwin-arm64), const F_GETFD = 1
pkg syscall (darwin-arm64), const F_GETFD ideal-int
pkg syscall (darwin-arm64), const F_GETFL = 3
pkg syscall (darwin-arm64), const F_GETFL ideal-int
pkg syscall (darwin-arm64), const F_GETLK = 7
pkg syscall (darwin-arm64), const F_GETLK ideal-int
pkg syscall (darwin-arm64), const F_GETLKPID = 66
pkg syscall (darwin-arm64), const F_GETLKPID ideal-int
pkg syscall (darwin-arm64), const F_GETNOSIGPIPE = 74
pkg syscall (darwin-arm64), const F_GETNOSIGPIPE ideal-int
pkg syscall (darwin-arm64), const F_GETOWN = 5
pkg syscall (darwin-arm64), const F_GETOWN ideal-int
pkg syscall (darwin-arm64), const F_GETPATH = 50
pkg syscall (darwin-arm64), const F_GETPATH ideal-int
pkg syscall (darwin-arm64), const F_GETPATH_MTMINFO = 71
pkg syscall (darwin-arm64), const F_GETPATH_MTMINFO ideal-int
pkg syscall (darwin-arm64), const F_GETPROTECTIONCLASS = 63
pkg syscall (darwin-arm64), const F_GETPROTECTIONCLASS ideal-int
pkg syscall (darwin-arm64), const F_GETPROTECTIONLEVEL = 77
pkg syscall (darwin-arm64), const F_GETPROTECTIONLEVEL ideal-int
pkg syscall (darwin-arm64), const F_GLOBAL_NOCACHE = 55
pkg syscall (darwin-arm64), const F_GLOBAL_NOCACHE ideal-int
pkg syscall (darwin-arm64), const F_LOG2PHYS = 49
pkg syscall (darwin-arm64), const F_LOG2PHYS ideal-int
pkg syscall (darwin-arm64), const F_LOG2PHYS_EXT = 65
pkg syscall (darwin-arm64), const F_LOG2PHYS_EXT ideal-int
pkg syscall (darwin-arm64), const F_NOCACHE = 48
pkg syscall (darwin-arm64), const F_NOCACHE ideal-int
pkg syscall (darwin-arm64), const F_NODIRECT = 62
pkg syscall (darwin-arm64), const F_NODIRECT ideal-int
pkg syscall (darwin-arm64), const F_OK = 0
pkg syscall (darwin-arm64), const F_OK ideal-int
pkg syscall (darwin-arm64), const F_PATHPKG_CHECK = 52
pkg syscall (darwin-arm64), const F_PATHPKG_CHECK ideal-int
pkg syscall (darwin-arm64), const F_PEOFPOSMODE = 3
pkg syscall (darwin-arm64), const F_PEOFPOSMODE ideal-int
pkg syscall (darwin-arm64), const F_PREALLOCATE = 42
pkg syscall (darwin-arm64), const F_PREALLOCATE ideal-int
pkg syscall (darwin-arm64), const F_RDADVISE = 44
pkg syscall (darwin-arm64), const F_RDADVISE ideal-int
pkg syscall (darwin-arm64), const F_RDAHEAD = 45
pkg syscall (darwin-arm64), const F_RDAHEAD ideal-int
pkg syscall (darwin-arm64), const F_RDLCK = 1
pkg syscall (darwin-arm64), const F_RDLCK ideal-int
pkg syscall (darwin-arm64), const F_SETBACKINGSTORE = 70
pkg syscall (darwin-arm64), const F_SETBACKINGSTORE ideal-int
pkg syscall (darwin-arm64), const F_SETFD = 2
pkg syscall (darwin-arm64), const F_SETFD ideal-int
pkg syscall (darwin-arm64), const F_SETFL = 4
pkg syscall (darwin-arm64), const F_SETFL ideal-int
pkg syscall (darwin-arm64), const F_SETLK = 8
pkg syscall (darwin-arm64), const F_SETLK ideal-int
pkg syscall (darwin-arm64), const F_SETLKW = 9
pkg syscall (darwin-arm64), const F_SETLKW ideal-int
pkg syscall (darwin-arm64), const F_SETLKWTIMEOUT = 10
pkg syscall (darwin-arm64), const F_SETLKWTIMEOUT ideal-int
pkg syscall (darwin-arm64), const F_SETNOSIGPIPE = 73
pkg syscall (darwin-arm64), const F_SETNOSIGPIPE ideal-int
pkg syscall (darwin-arm64), const F_SETOWN = 6
pkg syscall (darwin-arm64), const F_SETOWN ideal-int
pkg syscall (darwin-arm64), const F_SETPROTECTIONCLASS = 64
pkg syscall (darwin-arm64), const F_SETPROTECTIONCLASS ideal-int
pkg syscall (darwin-arm64), const F_SETSIZE = 43
pkg syscall (darwin-arm64), const F_SETSIZE ideal-int
pkg syscall (darwin-arm64), const F_SINGLE_WRITER = 76
pkg syscall (darwin-arm64), const F_SINGLE_WRITER ideal-int
pkg syscall (darwin-arm64), const F_THAW_FS = 54
pkg syscall (darwin-arm64), const F_THAW_FS ideal-int
pkg syscall (darwin-arm64), const F_TRANSCODEKEY = 75
pkg syscall (darwin-arm64), const F_TRANSCODEKEY ideal-int
pkg syscall (darwin-arm64), const F_UNLCK = 2
pkg syscall (darwin-arm64), const F_UNLCK ideal-int
pkg syscall (darwin-arm64), const F_VOLPOSMODE = 4
pkg syscall (darwin-arm64), const F_VOLPOSMODE ideal-int
pkg syscall (darwin-arm64), const F_WRLCK = 3
pkg syscall (darwin-arm64), const F_WRLCK ideal-int
pkg syscall (darwin-arm64), const HUPCL = 16384
pkg syscall (darwin-arm64), const HUPCL ideal-int
pkg syscall (darwin-arm64), const ICANON = 256
pkg syscall (darwin-arm64), const ICANON ideal-int
pkg syscall (darwin-arm64), const ICMP6_FILTER = 18
pkg syscall (darwin-arm64), const ICMP6_FILTER ideal-int
pkg syscall (darwin-arm64), const ICRNL = 256
pkg syscall (darwin-arm64), const ICRNL ideal-int
pkg syscall (darwin-arm64), const IEXTEN = 1024
pkg syscall (darwin-arm64), const IEXTEN ideal-int
pkg syscall (darwin-arm64), const IFF_ALLMULTI = 512
pkg syscall (darwin-arm64), const IFF_ALLMULTI ideal-int
pkg syscall (darwin-arm64), const IFF_ALTPHYS = 16384
pkg syscall (darwin-arm64), const IFF_ALTPHYS ideal-int
pkg syscall (darwin-arm64), const IFF_DEBUG = 4
pkg syscall (darwin-arm64), const IFF_DEBUG ideal-int
pkg syscall (darwin-arm64), const IFF_LINK0 = 4096
pkg syscall (darwin-arm64), const IFF_LINK0 ideal-int
pkg syscall (darwin-arm64), const IFF_LINK1 = 8192
pkg syscall (darwin-arm64), const IFF_LINK1 ideal-int
pkg syscall (darwin-arm64), const IFF_LINK2 = 16384
pkg syscall (darwin-arm64), const IFF_LINK2 ideal-int
pkg syscall (darwin-arm64), const IFF_LOOPBACK = 8
pkg syscall (darwin-arm64), const IFF_MULTICAST = 32768
pkg syscall (darwin-arm64), const IFF_NOARP = 128
pkg syscall (darwin-arm64), const IFF_NOARP ideal-int
pkg syscall (darwin-arm64), const IFF_NOTRAILERS = 32
pkg syscall (darwin-arm64), const IFF_NOTRAILERS ideal-int
pkg syscall (darwin-arm64), const IFF_OACTIVE = 1024
pkg syscall (darwin-arm64), const IFF_OACTIVE ideal-int
pkg syscall (darwin-arm64), const IFF_POINTOPOINT = 16
pkg syscall (darwin-arm64), const IFF_POINTOPOINT ideal-int
pkg syscall (darwin-arm64), const IFF_PROMISC = 256
pkg syscall (darwin-arm64), const IFF_PROMISC ideal-int
pkg syscall (darwin-arm64), const IFF_RUNNING = 64
pkg syscall (darwin-arm64), const IFF_RUNNING ideal-int
pkg syscall (darwin-arm64), const IFF_SIMPLEX = 2048
pkg syscall (darwin-arm64), const IFF_SIMPLEX ideal-int
pkg syscall (darwin-arm64), const IFNAMSIZ = 16
pkg syscall (darwin-arm64), const IFNAMSIZ ideal-int
pkg syscall (darwin-arm64), const IFT_1822 = 2
pkg syscall (darwin-arm64), const IFT_1822 ideal-int
pkg syscall (darwin-arm64), const IFT_AAL5 = 49
pkg syscall (darwin-arm64), const IFT_AAL5 ideal-int
pkg syscall (darwin-arm64), const IFT_ARCNET = 35
pkg syscall (darwin-arm64), const IFT_ARCNET ideal-int
pkg syscall (darwin-arm64), const IFT_ARCNETPLUS = 36
pkg syscall (darwin-arm64), const IFT_ARCNETPLUS ideal-int
pkg syscall (darwin-arm64), const IFT_ATM = 37
pkg syscall (darwin-arm64), const IFT_ATM ideal-int
pkg syscall (darwin-arm64), const IFT_BRIDGE = 209
pkg syscall (darwin-arm64), const IFT_BRIDGE ideal-int
pkg syscall (darwin-arm64), const IFT_CARP = 248
pkg syscall (darwin-arm64), const IFT_CARP ideal-int
pkg syscall (darwin-arm64), const IFT_CELLULAR = 255
pkg syscall (darwin-arm64), const IFT_CELLULAR ideal-int
pkg syscall (darwin-arm64), const IFT_CEPT = 19
pkg syscall (darwin-arm64), const IFT_CEPT ideal-int
pkg syscall (darwin-arm64), const IFT_DS3 = 30
pkg syscall (darwin-arm64), const IFT_DS3 ideal-int
pkg syscall (darwin-arm64), const IFT_ENC = 244
pkg syscall (darwin-arm64), const IFT_ENC ideal-int
pkg syscall (darwin-arm64), const IFT_EON = 25
pkg syscall (darwin-arm64), const IFT_EON ideal-int
pkg syscall (darwin-arm64), const IFT_ETHER = 6
pkg syscall (darwin-arm64), const IFT_ETHER ideal-int
pkg syscall (darwin-arm64), const IFT_FAITH = 56
pkg syscall (darwin-arm64), const IFT_FAITH ideal-int
pkg syscall (darwin-arm64), const IFT_FDDI = 15
pkg syscall (darwin-arm64), const IFT_FDDI ideal-int
pkg syscall (darwin-arm64), const IFT_FRELAY = 32
pkg syscall (darwin-arm64), const IFT_FRELAY ideal-int
pkg syscall (darwin-arm64), const IFT_FRELAYDCE = 44
pkg syscall (darwin-arm64), const IFT_FRELAYDCE ideal-int
pkg syscall (darwin-arm64), const IFT_GIF = 55
pkg syscall (darwin-arm64), const IFT_GIF ideal-int
pkg syscall (darwin-arm64), const IFT_HDH1822 = 3
pkg syscall (darwin-arm64), const IFT_HDH1822 ideal-int
pkg syscall (darwin-arm64), const IFT_HIPPI = 47
pkg syscall (darwin-arm64), const IFT_HIPPI ideal-int
pkg syscall (darwin-arm64), const IFT_HSSI = 46
pkg syscall (darwin-arm64), const IFT_HSSI ideal-int
pkg syscall (darwin-arm64), const IFT_HY = 14
pkg syscall (darwin-arm64), const IFT_HY ideal-int
pkg syscall (darwin-arm64), const IFT_IEEE1394 = 144
pkg syscall (darwin-arm64), const IFT_IEEE1394 ideal-int
pkg syscall (darwin-arm64), const IFT_IEEE8023ADLAG = 136
pkg syscall (darwin-arm64), const IFT_IEEE8023ADLAG ideal-int
pkg syscall (darwin-arm64), const IFT_ISDNBASIC = 20
pkg syscall (darwin-arm64), const IFT_ISDNBASIC ideal-int
pkg syscall (darwin-arm64), const IFT_ISDNPRIMARY = 21
pkg syscall (darwin-arm64), const IFT_ISDNPRIMARY ideal-int
pkg syscall (darwin-arm64), const IFT_ISO88022LLC = 41
pkg syscall (darwin-arm64), const IFT_ISO88022LLC ideal-int
pkg syscall (darwin-arm64), const IFT_ISO88023 = 7
pkg syscall (darwin-arm64), const IFT_ISO88023 ideal-int
pkg syscall (darwin-arm64), const IFT_ISO88024 = 8
pkg syscall (darwin-arm64), const IFT_ISO88024 ideal-int
pkg syscall (darwin-arm64), const IFT_ISO88025 = 9
pkg syscall (darwin-arm64), const IFT_ISO88025 ideal-int
pkg syscall (darwin-arm64), const IFT_ISO88026 = 10
pkg syscall (darwin-arm64), const IFT_ISO88026 ideal-int
pkg syscall (darwin-arm64), const IFT_L2VLAN = 135
pkg syscall (darwin-arm64), const IFT_L2VLAN ideal-int
pkg syscall (darwin-arm64), const IFT_LAPB = 16
pkg syscall (darwin-arm64), const IFT_LAPB ideal-int
pkg syscall (darwin-arm64), const IFT_LOCALTALK = 42
pkg syscall (darwin-arm64), const IFT_LOCALTALK ideal-int
pkg syscall (darwin-arm64), const IFT_LOOP = 24
pkg syscall (darwin-arm64), const IFT_LOOP ideal-int
pkg syscall (darwin-arm64), const IFT_MIOX25 = 38
pkg syscall (darwin-arm64), const IFT_MIOX25 ideal-int
pkg syscall (darwin-arm64), const IFT_MODEM = 48
pkg syscall (darwin-arm64), const IFT_MODEM ideal-int
pkg syscall (darwin-arm64), const IFT_NSIP = 27
pkg syscall (darwin-arm64), const IFT_NSIP ideal-int
pkg syscall (darwin-arm64), const IFT_OTHER = 1
pkg syscall (darwin-arm64), const IFT_OTHER ideal-int
pkg syscall (darwin-arm64), const IFT_P10 = 12
pkg syscall (darwin-arm64), const IFT_P10 ideal-int
pkg syscall (darwin-arm64), const IFT_P80 = 13
pkg syscall (darwin-arm64), const IFT_P80 ideal-int
pkg syscall (darwin-arm64), const IFT_PARA = 34
pkg syscall (darwin-arm64), const IFT_PARA ideal-int
pkg syscall (darwin-arm64), const IFT_PDP = 255
pkg syscall (darwin-arm64), const IFT_PDP ideal-int
pkg syscall (darwin-arm64), const IFT_PFLOG = 245
pkg syscall (darwin-arm64), const IFT_PFLOG ideal-int
pkg syscall (darwin-arm64), const IFT_PFSYNC = 246
pkg syscall (darwin-arm64), const IFT_PFSYNC ideal-int
pkg syscall (darwin-arm64), const IFT_PPP = 23
pkg syscall (darwin-arm64), const IFT_PPP ideal-int
pkg syscall (darwin-arm64), const IFT_PROPMUX = 54
pkg syscall (darwin-arm64), const IFT_PROPMUX ideal-int
pkg syscall (darwin-arm64), const IFT_PROPVIRTUAL = 53
pkg syscall (darwin-arm64), const IFT_PROPVIRTUAL ideal-int
pkg syscall (darwin-arm64), const IFT_PTPSERIAL = 22
pkg syscall (darwin-arm64), const IFT_PTPSERIAL ideal-int
pkg syscall (darwin-arm64), const IFT_RS232 = 33
pkg syscall (darwin-arm64), const IFT_RS232 ideal-int
pkg syscall (darwin-arm64), const IFT_SDLC = 17
pkg syscall (darwin-arm64), const IFT_SDLC ideal-int
pkg syscall (darwin-arm64), const IFT_SIP = 31
pkg syscall (darwin-arm64), const IFT_SIP ideal-int
pkg syscall (darwin-arm64), const IFT_SLIP = 28
pkg syscall (darwin-arm64), const IFT_SLIP ideal-int
pkg syscall (darwin-arm64), const IFT_SMDSDXI = 43
pkg syscall (darwin-arm64), const IFT_SMDSDXI ideal-int
pkg syscall (darwin-arm64), const IFT_SMDSICIP = 52
pkg syscall (darwin-arm64), const IFT_SMDSICIP ideal-int
pkg syscall (darwin-arm64), const IFT_SONET = 39
pkg syscall (darwin-arm64), const IFT_SONET ideal-int
pkg syscall (darwin-arm64), const IFT_SONETPATH = 50
pkg syscall (darwin-arm64), const IFT_SONETPATH ideal-int
pkg syscall (darwin-arm64), const IFT_SONETVT = 51
pkg syscall (darwin-arm64), const IFT_SONETVT ideal-int
pkg syscall (darwin-arm64), const IFT_STARLAN = 11
pkg syscall (darwin-arm64), const IFT_STARLAN ideal-int
pkg syscall (darwin-arm64), const IFT_STF = 57
pkg syscall (darwin-arm64), const IFT_STF ideal-int
pkg syscall (darwin-arm64), const IFT_T1 = 18
pkg syscall (darwin-arm64), const IFT_T1 ideal-int
pkg syscall (darwin-arm64), const IFT_ULTRA = 29
pkg syscall (darwin-arm64), const IFT_ULTRA ideal-int
pkg syscall (darwin-arm64), const IFT_V35 = 45
pkg syscall (darwin-arm64), const IFT_V35 ideal-int
pkg syscall (darwin-arm64), const IFT_X25 = 5
pkg syscall (darwin-arm64), const IFT_X25 ideal-int
pkg syscall (darwin-arm64), const IFT_X25DDN = 4
pkg syscall (darwin-arm64), const IFT_X25DDN ideal-int
pkg syscall (darwin-arm64), const IFT_X25PLE = 40
pkg syscall (darwin-arm64), const IFT_X25PLE ideal-int
pkg syscall (darwin-arm64), const IFT_XETHER = 26
pkg syscall (darwin-arm64), const IFT_XETHER ideal-int
pkg syscall (darwin-arm64), const IGNBRK = 1
pkg syscall (darwin-arm64), const IGNBRK ideal-int
pkg syscall (darwin-arm64), const IGNCR = 128
pkg syscall (darwin-arm64), const IGNCR ideal-int
pkg syscall (darwin-arm64), const IGNPAR = 4
pkg syscall (darwin-arm64), const IGNPAR ideal-int
pkg syscall (darwin-arm64), const IMAXBEL = 8192
pkg syscall (darwin-arm64), const IMAXBEL ideal-int
pkg syscall (darwin-arm64), const INLCR = 64
pkg syscall (darwin-arm64), const INLCR ideal-int
pkg syscall (darwin-arm64), const INPCK = 16
pkg syscall (darwin-arm64), const INPCK ideal-int
pkg syscall (darwin-arm64), const IN_CLASSA_HOST = 16777215
pkg syscall (darwin-arm64), const IN_CLASSA_HOST ideal-int
pkg syscall (darwin-arm64), const IN_CLASSA_MAX = 128
pkg syscall (darwin-arm64), const IN_CLASSA_MAX ideal-int
pkg syscall (darwin-arm64), const IN_CLASSA_NET = 4278190080
pkg syscall (darwin-arm64), const IN_CLASSA_NET ideal-int
pkg syscall (darwin-arm64), const IN_CLASSA_NSHIFT = 24
pkg syscall (darwin-arm64), const IN_CLASSA_NSHIFT ideal-int
pkg syscall (darwin-arm64), const IN_CLASSB_HOST = 65535
pkg syscall (darwin-arm64), const IN_CLASSB_HOST ideal-int
pkg syscall (darwin-arm64), const IN_CLASSB_MAX = 65536
pkg syscall (darwin-arm64), const IN_CLASSB_MAX ideal-int
pkg syscall (darwin-arm64), const IN_CLASSB_NET = 4294901760
pkg syscall (darwin-arm64), const IN_CLASSB_NET ideal-int
pkg syscall (darwin-arm64), const IN_CLASSB_NSHIFT = 16
pkg syscall (darwin-arm64), const IN_CLASSB_NSHIFT ideal-int
pkg syscall (darwin-arm64), const IN_CLASSC_HOST = 255
pkg syscall (darwin-arm64), const IN_CLASSC_HOST ideal-int
pkg syscall (darwin-arm64), const IN_CLASSC_NET = 4294967040
pkg syscall (darwin-arm64), const IN_CLASSC_NET ideal-int
pkg syscall (darwin-arm64), const IN_CLASSC_NSHIFT = 8
pkg syscall (darwin-arm64), const IN_CLASSC_NSHIFT ideal-int
pkg syscall (darwin-arm64), const IN_CLASSD_HOST = 268435455
pkg syscall (darwin-arm64), const IN_CLASSD_HOST ideal-int
pkg syscall (darwin-arm64), const IN_CLASSD_NET = 4026531840
pkg syscall (darwin-arm64), const IN_CLASSD_NET ideal-int
pkg syscall (darwin-arm64), const IN_CLASSD_NSHIFT = 28
pkg syscall (darwin-arm64), const IN_CLASSD_NSHIFT ideal-int
pkg syscall (darwin-arm64), const IN_LINKLOCALNETNUM = 2851995648
pkg syscall (darwin-arm64), const IN_LINKLOCALNETNUM ideal-int
pkg syscall (darwin-arm64), const IN_LOOPBACKNET = 127
pkg syscall (darwin-arm64), const IN_LOOPBACKNET ideal-int
pkg syscall (darwin-arm64), const IPPROTO_3PC = 34
pkg syscall (darwin-arm64), const IPPROTO_3PC ideal-int
pkg syscall (darwin-arm64), const IPPROTO_ADFS = 68
pkg syscall (darwin-arm64), const IPPROTO_ADFS ideal-int
pkg syscall (darwin-arm64), const IPPROTO_AH = 51
pkg syscall (darwin-arm64), const IPPROTO_AH ideal-int
pkg syscall (darwin-arm64), const IPPROTO_AHIP = 61
pkg syscall (darwin-arm64), const IPPROTO_AHIP ideal-int
pkg syscall (darwin-arm64), const IPPROTO_APES = 99
pkg syscall (darwin-arm64), const IPPROTO_APES ideal-int
pkg syscall (darwin-arm64), const IPPROTO_ARGUS = 13
pkg syscall (darwin-arm64), const IPPROTO_ARGUS ideal-int
pkg syscall (darwin-arm64), const IPPROTO_AX25 = 93
pkg syscall (darwin-arm64), const IPPROTO_AX25 ideal-int
pkg syscall (darwin-arm64), const IPPROTO_BHA = 49
pkg syscall (darwin-arm64), const IPPROTO_BHA ideal-int
pkg syscall (darwin-arm64), const IPPROTO_BLT = 30
pkg syscall (darwin-arm64), const IPPROTO_BLT ideal-int
pkg syscall (darwin-arm64), const IPPROTO_BRSATMON = 76
pkg syscall (darwin-arm64), const IPPROTO_BRSATMON ideal-int
pkg syscall (darwin-arm64), const IPPROTO_CFTP = 62
pkg syscall (darwin-arm64), const IPPROTO_CFTP ideal-int
pkg syscall (darwin-arm64), const IPPROTO_CHAOS = 16
pkg syscall (darwin-arm64), const IPPROTO_CHAOS ideal-int
pkg syscall (darwin-arm64), const IPPROTO_CMTP = 38
pkg syscall (darwin-arm64), const IPPROTO_CMTP ideal-int
pkg syscall (darwin-arm64), const IPPROTO_CPHB = 73
pkg syscall (darwin-arm64), const IPPROTO_CPHB ideal-int
pkg syscall (darwin-arm64), const IPPROTO_CPNX = 72
pkg syscall (darwin-arm64), const IPPROTO_CPNX ideal-int
pkg syscall (darwin-arm64), const IPPROTO_DDP = 37
pkg syscall (darwin-arm64), const IPPROTO_DDP ideal-int
pkg syscall (darwin-arm64), const IPPROTO_DGP = 86
pkg syscall (darwin-arm64), const IPPROTO_DGP ideal-int
pkg syscall (darwin-arm64), const IPPROTO_DIVERT = 254
pkg syscall (darwin-arm64), const IPPROTO_DIVERT ideal-int
pkg syscall (darwin-arm64), const IPPROTO_DONE = 257
pkg syscall (darwin-arm64), const IPPROTO_DONE ideal-int
pkg syscall (darwin-arm64), const IPPROTO_DSTOPTS = 60
pkg syscall (darwin-arm64), const IPPROTO_DSTOPTS ideal-int
pkg syscall (darwin-arm64), const IPPROTO_EGP = 8
pkg syscall (darwin-arm64), const IPPROTO_EGP ideal-int
pkg syscall (darwin-arm64), const IPPROTO_EMCON = 14
pkg syscall (darwin-arm64), const IPPROTO_EMCON ideal-int
pkg syscall (darwin-arm64), const IPPROTO_ENCAP = 98
pkg syscall (darwin-arm64), const IPPROTO_ENCAP ideal-int
pkg syscall (darwin-arm64), const IPPROTO_EON = 80
pkg syscall (darwin-arm64), const IPPROTO_EON ideal-int
pkg syscall (darwin-arm64), const IPPROTO_ESP = 50
pkg syscall (darwin-arm64), const IPPROTO_ESP ideal-int
pkg syscall (darwin-arm64), const IPPROTO_ETHERIP = 97
pkg syscall (darwin-arm64), const IPPROTO_ETHERIP ideal-int
pkg syscall (darwin-arm64), const IPPROTO_FRAGMENT = 44
pkg syscall (darwin-arm64), const IPPROTO_FRAGMENT ideal-int
pkg syscall (darwin-arm64), const IPPROTO_GGP = 3
pkg syscall (darwin-arm64), const IPPROTO_GGP ideal-int
pkg syscall (darwin-arm64), const IPPROTO_GMTP = 100
pkg syscall (darwin-arm64), const IPPROTO_GMTP ideal-int
pkg syscall (darwin-arm64), const IPPROTO_GRE = 47
pkg syscall (darwin-arm64), const IPPROTO_GRE ideal-int
pkg syscall (darwin-arm64), const IPPROTO_HELLO = 63
pkg syscall (darwin-arm64), const IPPROTO_HELLO ideal-int
pkg syscall (darwin-arm64), const IPPROTO_HMP = 20
pkg syscall (darwin-arm64), const IPPROTO_HMP ideal-int
pkg syscall (darwin-arm64), const IPPROTO_HOPOPTS = 0
pkg syscall (darwin-arm64), const IPPROTO_HOPOPTS ideal-int
pkg syscall (darwin-arm64), const IPPROTO_ICMP = 1
pkg syscall (darwin-arm64), const IPPROTO_ICMP ideal-int
pkg syscall (darwin-arm64), const IPPROTO_ICMPV6 = 58
pkg syscall (darwin-arm64), const IPPROTO_ICMPV6 ideal-int
pkg syscall (darwin-arm64), const IPPROTO_IDP = 22
pkg syscall (darwin-arm64), const IPPROTO_IDP ideal-int
pkg syscall (darwin-arm64), const IPPROTO_IDPR = 35
pkg syscall (darwin-arm64), const IPPROTO_IDPR ideal-int
pkg syscall (darwin-arm64), const IPPROTO_IDRP = 45
pkg syscall (darwin-arm64), const IPPROTO_IDRP ideal-int
pkg syscall (darwin-arm64), const IPPROTO_IGMP = 2
pkg syscall (darwin-arm64), const IPPROTO_IGMP ideal-int
pkg syscall (darwin-arm64), const IPPROTO_IGP = 85
pkg syscall (darwin-arm64), const IPPROTO_IGP ideal-int
pkg syscall (darwin-arm64), const IPPROTO_IGRP = 88
pkg syscall (darwin-arm64), const IPPROTO_IGRP ideal-int
pkg syscall (darwin-arm64), const IPPROTO_IL = 40
pkg syscall (darwin-arm64), const IPPROTO_IL ideal-int
pkg syscall (darwin-arm64), const IPPROTO_INLSP = 52
pkg syscall (darwin-arm64), const IPPROTO_INLSP ideal-int
pkg syscall (darwin-arm64), const IPPROTO_INP = 32
pkg syscall (darwin-arm64), const IPPROTO_INP ideal-int
pkg syscall (darwin-arm64), const IPPROTO_IPCOMP = 108
pkg syscall (darwin-arm64), const IPPROTO_IPCOMP ideal-int
pkg syscall (darwin-arm64), const IPPROTO_IPCV = 71
pkg syscall (darwin-arm64), const IPPROTO_IPCV ideal-int
pkg syscall (darwin-arm64), const IPPROTO_IPEIP = 94
pkg syscall (darwin-arm64), const IPPROTO_IPEIP ideal-int
pkg syscall (darwin-arm64), const IPPROTO_IPIP = 4
pkg syscall (darwin-arm64), const IPPROTO_IPIP ideal-int
pkg syscall (darwin-arm64), const IPPROTO_IPPC = 67
pkg syscall (darwin-arm64), const IPPROTO_IPPC ideal-int
pkg syscall (darwin-arm64), const IPPROTO_IPV4 = 4
pkg syscall (darwin-arm64), const IPPROTO_IPV4 ideal-int
pkg syscall (darwin-arm64), const IPPROTO_IRTP = 28
pkg syscall (darwin-arm64), const IPPROTO_IRTP ideal-int
pkg syscall (darwin-arm64), const IPPROTO_KRYPTOLAN = 65
pkg syscall (darwin-arm64), const IPPROTO_KRYPTOLAN ideal-int
pkg syscall (darwin-arm64), const IPPROTO_LARP = 91
pkg syscall (darwin-arm64), const IPPROTO_LARP ideal-int
pkg syscall (darwin-arm64), const IPPROTO_LEAF1 = 25
pkg syscall (darwin-arm64), const IPPROTO_LEAF1 ideal-int
pkg syscall (darwin-arm64), const IPPROTO_LEAF2 = 26
pkg syscall (darwin-arm64), const IPPROTO_LEAF2 ideal-int
pkg syscall (darwin-arm64), const IPPROTO_MAX = 256
pkg syscall (darwin-arm64), const IPPROTO_MAX ideal-int
pkg syscall (darwin-arm64), const IPPROTO_MAXID = 52
pkg syscall (darwin-arm64), const IPPROTO_MAXID ideal-int
pkg syscall (darwin-arm64), const IPPROTO_MEAS = 19
pkg syscall (darwin-arm64), const IPPROTO_MEAS ideal-int
pkg syscall (darwin-arm64), const IPPROTO_MHRP = 48
pkg syscall (darwin-arm64), const IPPROTO_MHRP ideal-int
pkg syscall (darwin-arm64), const IPPROTO_MICP = 95
pkg syscall (darwin-arm64), const IPPROTO_MICP ideal-int
pkg syscall (darwin-arm64), const IPPROTO_MTP = 92
pkg syscall (darwin-arm64), const IPPROTO_MTP ideal-int
pkg syscall (darwin-arm64), const IPPROTO_MUX = 18
pkg syscall (darwin-arm64), const IPPROTO_MUX ideal-int
pkg syscall (darwin-arm64), const IPPROTO_ND = 77
pkg syscall (darwin-arm64), const IPPROTO_ND ideal-int
pkg syscall (darwin-arm64), const IPPROTO_NHRP = 54
pkg syscall (darwin-arm64), const IPPROTO_NHRP ideal-int
pkg syscall (darwin-arm64), const IPPROTO_NONE = 59
pkg syscall (darwin-arm64), const IPPROTO_NONE ideal-int
pkg syscall (darwin-arm64), const IPPROTO_NSP = 31
pkg syscall (darwin-arm64), const IPPROTO_NSP ideal-int
pkg syscall (darwin-arm64), const IPPROTO_NVPII = 11
pkg syscall (darwin-arm64), const IPPROTO_NVPII ideal-int
pkg syscall (darwin-arm64), const IPPROTO_OSPFIGP = 89
pkg syscall (darwin-arm64), const IPPROTO_OSPFIGP ideal-int
pkg syscall (darwin-arm64), const IPPROTO_PGM = 113
pkg syscall (darwin-arm64), const IPPROTO_PGM ideal-int
pkg syscall (darwin-arm64), const IPPROTO_PIGP = 9
pkg syscall (darwin-arm64), const IPPROTO_PIGP ideal-int
pkg syscall (darwin-arm64), const IPPROTO_PIM = 103
pkg syscall (darwin-arm64), const IPPROTO_PIM ideal-int
pkg syscall (darwin-arm64), const IPPROTO_PRM = 21
pkg syscall (darwin-arm64), const IPPROTO_PRM ideal-int
pkg syscall (darwin-arm64), const IPPROTO_PUP = 12
pkg syscall (darwin-arm64), const IPPROTO_PUP ideal-int
pkg syscall (darwin-arm64), const IPPROTO_PVP = 75
pkg syscall (darwin-arm64), const IPPROTO_PVP ideal-int
pkg syscall (darwin-arm64), const IPPROTO_RAW = 255
pkg syscall (darwin-arm64), const IPPROTO_RAW ideal-int
pkg syscall (darwin-arm64), const IPPROTO_RCCMON = 10
pkg syscall (darwin-arm64), const IPPROTO_RCCMON ideal-int
pkg syscall (darwin-arm64), const IPPROTO_RDP = 27
pkg syscall (darwin-arm64), const IPPROTO_RDP ideal-int
pkg syscall (darwin-arm64), const IPPROTO_ROUTING = 43
pkg syscall (darwin-arm64), const IPPROTO_ROUTING ideal-int
pkg syscall (darwin-arm64), const IPPROTO_RSVP = 46
pkg syscall (darwin-arm64), const IPPROTO_RSVP ideal-int
pkg syscall (darwin-arm64), const IPPROTO_RVD = 66
pkg syscall (darwin-arm64), const IPPROTO_RVD ideal-int
pkg syscall (darwin-arm64), const IPPROTO_SATEXPAK = 64
pkg syscall (darwin-arm64), const IPPROTO_SATEXPAK ideal-int
pkg syscall (darwin-arm64), const IPPROTO_SATMON = 69
pkg syscall (darwin-arm64), const IPPROTO_SATMON ideal-int
pkg syscall (darwin-arm64), const IPPROTO_SCCSP = 96
pkg syscall (darwin-arm64), const IPPROTO_SCCSP ideal-int
pkg syscall (darwin-arm64), const IPPROTO_SCTP = 132
pkg syscall (darwin-arm64), const IPPROTO_SCTP ideal-int
pkg syscall (darwin-arm64), const IPPROTO_SDRP = 42
pkg syscall (darwin-arm64), const IPPROTO_SDRP ideal-int
pkg syscall (darwin-arm64), const IPPROTO_SEP = 33
pkg syscall (darwin-arm64), const IPPROTO_SEP ideal-int
pkg syscall (darwin-arm64), const IPPROTO_SRPC = 90
pkg syscall (darwin-arm64), const IPPROTO_SRPC ideal-int
pkg syscall (darwin-arm64), const IPPROTO_ST = 7
pkg syscall (darwin-arm64), const IPPROTO_ST ideal-int
pkg syscall (darwin-arm64), const IPPROTO_SVMTP = 82
pkg syscall (darwin-arm64), const IPPROTO_SVMTP ideal-int
pkg syscall (darwin-arm64), const IPPROTO_SWIPE = 53
pkg syscall (darwin-arm64), const IPPROTO_SWIPE ideal-int
pkg syscall (darwin-arm64), const IPPROTO_TCF = 87
pkg syscall (darwin-arm64), const IPPROTO_TCF ideal-int
pkg syscall (darwin-arm64), const IPPROTO_TP = 29
pkg syscall (darwin-arm64), const IPPROTO_TP ideal-int
pkg syscall (darwin-arm64), const IPPROTO_TPXX = 39
pkg syscall (darwin-arm64), const IPPROTO_TPXX ideal-int
pkg syscall (darwin-arm64), const IPPROTO_TRUNK1 = 23
pkg syscall (darwin-arm64), const IPPROTO_TRUNK1 ideal-int
pkg syscall (darwin-arm64), const IPPROTO_TRUNK2 = 24
pkg syscall (darwin-arm64), const IPPROTO_TRUNK2 ideal-int
pkg syscall (darwin-arm64), const IPPROTO_TTP = 84
pkg syscall (darwin-arm64), const IPPROTO_TTP ideal-int
pkg syscall (darwin-arm64), const IPPROTO_VINES = 83
pkg syscall (darwin-arm64), const IPPROTO_VINES ideal-int
pkg syscall (darwin-arm64), const IPPROTO_VISA = 70
pkg syscall (darwin-arm64), const IPPROTO_VISA ideal-int
pkg syscall (darwin-arm64), const IPPROTO_VMTP = 81
pkg syscall (darwin-arm64), const IPPROTO_VMTP ideal-int
pkg syscall (darwin-arm64), const IPPROTO_WBEXPAK = 79
pkg syscall (darwin-arm64), const IPPROTO_WBEXPAK ideal-int
pkg syscall (darwin-arm64), const IPPROTO_WBMON = 78
pkg syscall (darwin-arm64), const IPPROTO_WBMON ideal-int
pkg syscall (darwin-arm64), const IPPROTO_WSN = 74
pkg syscall (darwin-arm64), const IPPROTO_WSN ideal-int
pkg syscall (darwin-arm64), const IPPROTO_XNET = 15
pkg syscall (darwin-arm64), const IPPROTO_XNET ideal-int
pkg syscall (darwin-arm64), const IPPROTO_XTP = 36
pkg syscall (darwin-arm64), const IPPROTO_XTP ideal-int
pkg syscall (darwin-arm64), const IPV6_2292DSTOPTS = 23
pkg syscall (darwin-arm64), const IPV6_2292DSTOPTS ideal-int
pkg syscall (darwin-arm64), const IPV6_2292HOPLIMIT = 20
pkg syscall (darwin-arm64), const IPV6_2292HOPLIMIT ideal-int
pkg syscall (darwin-arm64), const IPV6_2292HOPOPTS = 22
pkg syscall (darwin-arm64), const IPV6_2292HOPOPTS ideal-int
pkg syscall (darwin-arm64), const IPV6_2292NEXTHOP = 21
pkg syscall (darwin-arm64), const IPV6_2292NEXTHOP ideal-int
pkg syscall (darwin-arm64), const IPV6_2292PKTINFO = 19
pkg syscall (darwin-arm64), const IPV6_2292PKTINFO ideal-int
pkg syscall (darwin-arm64), const IPV6_2292PKTOPTIONS = 25
pkg syscall (darwin-arm64), const IPV6_2292PKTOPTIONS ideal-int
pkg syscall (darwin-arm64), const IPV6_2292RTHDR = 24
pkg syscall (darwin-arm64), const IPV6_2292RTHDR ideal-int
pkg syscall (darwin-arm64), const IPV6_BINDV6ONLY = 27
pkg syscall (darwin-arm64), const IPV6_BINDV6ONLY ideal-int
pkg syscall (darwin-arm64), const IPV6_BOUND_IF = 125
pkg syscall (darwin-arm64), const IPV6_BOUND_IF ideal-int
pkg syscall (darwin-arm64), const IPV6_CHECKSUM = 26
pkg syscall (darwin-arm64), const IPV6_CHECKSUM ideal-int
pkg syscall (darwin-arm64), const IPV6_DEFAULT_MULTICAST_HOPS = 1
pkg syscall (darwin-arm64), const IPV6_DEFAULT_MULTICAST_HOPS ideal-int
pkg syscall (darwin-arm64), const IPV6_DEFAULT_MULTICAST_LOOP = 1
pkg syscall (darwin-arm64), const IPV6_DEFAULT_MULTICAST_LOOP ideal-int
pkg syscall (darwin-arm64), const IPV6_DEFHLIM = 64
pkg syscall (darwin-arm64), const IPV6_DEFHLIM ideal-int
pkg syscall (darwin-arm64), const IPV6_FAITH = 29
pkg syscall (darwin-arm64), const IPV6_FAITH ideal-int
pkg syscall (darwin-arm64), const IPV6_FLOWINFO_MASK = 4294967055
pkg syscall (darwin-arm64), const IPV6_FLOWINFO_MASK ideal-int
pkg syscall (darwin-arm64), const IPV6_FLOWLABEL_MASK = 4294905600
pkg syscall (darwin-arm64), const IPV6_FLOWLABEL_MASK ideal-int
pkg syscall (darwin-arm64), const IPV6_FRAGTTL = 120
pkg syscall (darwin-arm64), const IPV6_FRAGTTL ideal-int
pkg syscall (darwin-arm64), const IPV6_FW_ADD = 30
pkg syscall (darwin-arm64), const IPV6_FW_ADD ideal-int
pkg syscall (darwin-arm64), const IPV6_FW_DEL = 31
pkg syscall (darwin-arm64), const IPV6_FW_DEL ideal-int
pkg syscall (darwin-arm64), const IPV6_FW_FLUSH = 32
pkg syscall (darwin-arm64), const IPV6_FW_FLUSH ideal-int
pkg syscall (darwin-arm64), const IPV6_FW_GET = 34
pkg syscall (darwin-arm64), const IPV6_FW_GET ideal-int
pkg syscall (darwin-arm64), const IPV6_FW_ZERO = 33
pkg syscall (darwin-arm64), const IPV6_FW_ZERO ideal-int
pkg syscall (darwin-arm64), const IPV6_HLIMDEC = 1
pkg syscall (darwin-arm64), const IPV6_HLIMDEC ideal-int
pkg syscall (darwin-arm64), const IPV6_IPSEC_POLICY = 28
pkg syscall (darwin-arm64), const IPV6_IPSEC_POLICY ideal-int
pkg syscall (darwin-arm64), const IPV6_JOIN_GROUP = 12
pkg syscall (darwin-arm64), const IPV6_LEAVE_GROUP = 13
pkg syscall (darwin-arm64), const IPV6_MAXHLIM = 255
pkg syscall (darwin-arm64), const IPV6_MAXHLIM ideal-int
pkg syscall (darwin-arm64), const IPV6_MAXOPTHDR = 2048
pkg syscall (darwin-arm64), const IPV6_MAXOPTHDR ideal-int
pkg syscall (darwin-arm64), const IPV6_MAXPACKET = 65535
pkg syscall (darwin-arm64), const IPV6_MAXPACKET ideal-int
pkg syscall (darwin-arm64), const IPV6_MAX_GROUP_SRC_FILTER = 512
pkg syscall (darwin-arm64), const IPV6_MAX_GROUP_SRC_FILTER ideal-int
pkg syscall (darwin-arm64), const IPV6_MAX_MEMBERSHIPS = 4095
pkg syscall (darwin-arm64), const IPV6_MAX_MEMBERSHIPS ideal-int
pkg syscall (darwin-arm64), const IPV6_MAX_SOCK_SRC_FILTER = 128
pkg syscall (darwin-arm64), const IPV6_MAX_SOCK_SRC_FILTER ideal-int
pkg syscall (darwin-arm64), const IPV6_MIN_MEMBERSHIPS = 31
pkg syscall (darwin-arm64), const IPV6_MIN_MEMBERSHIPS ideal-int
pkg syscall (darwin-arm64), const IPV6_MMTU = 1280
pkg syscall (darwin-arm64), const IPV6_MMTU ideal-int
pkg syscall (darwin-arm64), const IPV6_MULTICAST_HOPS = 10
pkg syscall (darwin-arm64), const IPV6_MULTICAST_IF = 9
pkg syscall (darwin-arm64), const IPV6_MULTICAST_LOOP = 11
pkg syscall (darwin-arm64), const IPV6_PORTRANGE = 14
pkg syscall (darwin-arm64), const IPV6_PORTRANGE ideal-int
pkg syscall (darwin-arm64), const IPV6_PORTRANGE_DEFAULT = 0
pkg syscall (darwin-arm64), const IPV6_PORTRANGE_DEFAULT ideal-int
pkg syscall (darwin-arm64), const IPV6_PORTRANGE_HIGH = 1
pkg syscall (darwin-arm64), const IPV6_PORTRANGE_HIGH ideal-int
pkg syscall (darwin-arm64), const IPV6_PORTRANGE_LOW = 2
pkg syscall (darwin-arm64), const IPV6_PORTRANGE_LOW ideal-int
pkg syscall (darwin-arm64), const IPV6_RECVTCLASS = 35
pkg syscall (darwin-arm64), const IPV6_RECVTCLASS ideal-int
pkg syscall (darwin-arm64), const IPV6_RTHDR_LOOSE = 0
pkg syscall (darwin-arm64), const IPV6_RTHDR_LOOSE ideal-int
pkg syscall (darwin-arm64), const IPV6_RTHDR_STRICT = 1
pkg syscall (darwin-arm64), const IPV6_RTHDR_STRICT ideal-int
pkg syscall (darwin-arm64), const IPV6_RTHDR_TYPE_0 = 0
pkg syscall (darwin-arm64), const IPV6_RTHDR_TYPE_0 ideal-int
pkg syscall (darwin-arm64), const IPV6_SOCKOPT_RESERVED1 = 3
pkg syscall (darwin-arm64), const IPV6_SOCKOPT_RESERVED1 ideal-int
pkg syscall (darwin-arm64), const IPV6_TCLASS = 36
pkg syscall (darwin-arm64), const IPV6_TCLASS ideal-int
pkg syscall (darwin-arm64), const IPV6_UNICAST_HOPS = 4
pkg syscall (darwin-arm64), const IPV6_V6ONLY = 27
pkg syscall (darwin-arm64), const IPV6_VERSION = 96
pkg syscall (darwin-arm64), const IPV6_VERSION ideal-int
pkg syscall (darwin-arm64), const IPV6_VERSION_MASK = 240
pkg syscall (darwin-arm64), const IPV6_VERSION_MASK ideal-int
pkg syscall (darwin-arm64), const IP_ADD_MEMBERSHIP = 12
pkg syscall (darwin-arm64), const IP_ADD_SOURCE_MEMBERSHIP = 70
pkg syscall (darwin-arm64), const IP_ADD_SOURCE_MEMBERSHIP ideal-int
pkg syscall (darwin-arm64), const IP_BLOCK_SOURCE = 72
pkg syscall (darwin-arm64), const IP_BLOCK_SOURCE ideal-int
pkg syscall (darwin-arm64), const IP_BOUND_IF = 25
pkg syscall (darwin-arm64), const IP_BOUND_IF ideal-int
pkg syscall (darwin-arm64), const IP_DEFAULT_MULTICAST_LOOP = 1
pkg syscall (darwin-arm64), const IP_DEFAULT_MULTICAST_LOOP ideal-int
pkg syscall (darwin-arm64), const IP_DEFAULT_MULTICAST_TTL = 1
pkg syscall (darwin-arm64), const IP_DEFAULT_MULTICAST_TTL ideal-int
pkg syscall (darwin-arm64), const IP_DF = 16384
pkg syscall (darwin-arm64), const IP_DF ideal-int
pkg syscall (darwin-arm64), const IP_DROP_MEMBERSHIP = 13
pkg syscall (darwin-arm64), const IP_DROP_SOURCE_MEMBERSHIP = 71
pkg syscall (darwin-arm64), const IP_DROP_SOURCE_MEMBERSHIP ideal-int
pkg syscall (darwin-arm64), const IP_DUMMYNET_CONFIGURE = 60
pkg syscall (darwin-arm64), const IP_DUMMYNET_CONFIGURE ideal-int
pkg syscall (darwin-arm64), const IP_DUMMYNET_DEL = 61
pkg syscall (darwin-arm64), const IP_DUMMYNET_DEL ideal-int
pkg syscall (darwin-arm64), const IP_DUMMYNET_FLUSH = 62
pkg syscall (darwin-arm64), const IP_DUMMYNET_FLUSH ideal-int
pkg syscall (darwin-arm64), const IP_DUMMYNET_GET = 64
pkg syscall (darwin-arm64), const IP_DUMMYNET_GET ideal-int
pkg syscall (darwin-arm64), const IP_FAITH = 22
pkg syscall (darwin-arm64), const IP_FAITH ideal-int
pkg syscall (darwin-arm64), const IP_FW_ADD = 40
pkg syscall (darwin-arm64), const IP_FW_ADD ideal-int
pkg syscall (darwin-arm64), const IP_FW_DEL = 41
pkg syscall (darwin-arm64), const IP_FW_DEL ideal-int
pkg syscall (darwin-arm64), const IP_FW_FLUSH = 42
pkg syscall (darwin-arm64), const IP_FW_FLUSH ideal-int
pkg syscall (darwin-arm64), const IP_FW_GET = 44
pkg syscall (darwin-arm64), const IP_FW_GET ideal-int
pkg syscall (darwin-arm64), const IP_FW_RESETLOG = 45
pkg syscall (darwin-arm64), const IP_FW_RESETLOG ideal-int
pkg syscall (darwin-arm64), const IP_FW_ZERO = 43
pkg syscall (darwin-arm64), const IP_FW_ZERO ideal-int
pkg syscall (darwin-arm64), const IP_HDRINCL = 2
pkg syscall (darwin-arm64), const IP_HDRINCL ideal-int
pkg syscall (darwin-arm64), const IP_IPSEC_POLICY = 21
pkg syscall (darwin-arm64), const IP_IPSEC_POLICY ideal-int
pkg syscall (darwin-arm64), const IP_MAXPACKET = 65535
pkg syscall (darwin-arm64), const IP_MAXPACKET ideal-int
pkg syscall (darwin-arm64), const IP_MAX_GROUP_SRC_FILTER = 512
pkg syscall (darwin-arm64), const IP_MAX_GROUP_SRC_FILTER ideal-int
pkg syscall (darwin-arm64), const IP_MAX_MEMBERSHIPS = 4095
pkg syscall (darwin-arm64), const IP_MAX_MEMBERSHIPS ideal-int
pkg syscall (darwin-arm64), const IP_MAX_SOCK_MUTE_FILTER = 128
pkg syscall (darwin-arm64), const IP_MAX_SOCK_MUTE_FILTER ideal-int
pkg syscall (darwin-arm64), const IP_MAX_SOCK_SRC_FILTER = 128
pkg syscall (darwin-arm64), const IP_MAX_SOCK_SRC_FILTER ideal-int
pkg syscall (darwin-arm64), const IP_MF = 8192
pkg syscall (darwin-arm64), const IP_MF ideal-int
pkg syscall (darwin-arm64), const IP_MIN_MEMBERSHIPS = 31
pkg syscall (darwin-arm64), const IP_MIN_MEMBERSHIPS ideal-int
pkg syscall (darwin-arm64), const IP_MSFILTER = 74
pkg syscall (darwin-arm64), const IP_MSFILTER ideal-int
pkg syscall (darwin-arm64), const IP_MSS = 576
pkg syscall (darwin-arm64), const IP_MSS ideal-int
pkg syscall (darwin-arm64), const IP_MULTICAST_IF = 9
pkg syscall (darwin-arm64), const IP_MULTICAST_IFINDEX = 66
pkg syscall (darwin-arm64), const IP_MULTICAST_IFINDEX ideal-int
pkg syscall (darwin-arm64), const IP_MULTICAST_LOOP = 11
pkg syscall (darwin-arm64), const IP_MULTICAST_TTL = 10
pkg syscall (darwin-arm64), const IP_MULTICAST_VIF = 14
pkg syscall (darwin-arm64), const IP_MULTICAST_VIF ideal-int
pkg syscall (darwin-arm64), const IP_NAT__XXX = 55
pkg syscall (darwin-arm64), const IP_NAT__XXX ideal-int
pkg syscall (darwin-arm64), const IP_OFFMASK = 8191
pkg syscall (darwin-arm64), const IP_OFFMASK ideal-int
pkg syscall (darwin-arm64), const IP_OLD_FW_ADD = 50
pkg syscall (darwin-arm64), const IP_OLD_FW_ADD ideal-int
pkg syscall (darwin-arm64), const IP_OLD_FW_DEL = 51
pkg syscall (darwin-arm64), const IP_OLD_FW_DEL ideal-int
pkg syscall (darwin-arm64), const IP_OLD_FW_FLUSH = 52
pkg syscall (darwin-arm64), const IP_OLD_FW_FLUSH ideal-int
pkg syscall (darwin-arm64), const IP_OLD_FW_GET = 54
pkg syscall (darwin-arm64), const IP_OLD_FW_GET ideal-int
pkg syscall (darwin-arm64), const IP_OLD_FW_RESETLOG = 56
pkg syscall (darwin-arm64), const IP_OLD_FW_RESETLOG ideal-int
pkg syscall (darwin-arm64), const IP_OLD_FW_ZERO = 53
pkg syscall (darwin-arm64), const IP_OLD_FW_ZERO ideal-int
pkg syscall (darwin-arm64), const IP_OPTIONS = 1
pkg syscall (darwin-arm64), const IP_OPTIONS ideal-int
pkg syscall (darwin-arm64), const IP_PKTINFO = 26
pkg syscall (darwin-arm64), const IP_PKTINFO ideal-int
pkg syscall (darwin-arm64), const IP_PORTRANGE = 19
pkg syscall (darwin-arm64), const IP_PORTRANGE ideal-int
pkg syscall (darwin-arm64), const IP_PORTRANGE_DEFAULT = 0
pkg syscall (darwin-arm64), const IP_PORTRANGE_DEFAULT ideal-int
pkg syscall (darwin-arm64), const IP_PORTRANGE_HIGH = 1
pkg syscall (darwin-arm64), const IP_PORTRANGE_HIGH ideal-int
pkg syscall (darwin-arm64), const IP_PORTRANGE_LOW = 2
pkg syscall (darwin-arm64), const IP_PORTRANGE_LOW ideal-int
pkg syscall (darwin-arm64), const IP_RECVDSTADDR = 7
pkg syscall (darwin-arm64), const IP_RECVDSTADDR ideal-int
pkg syscall (darwin-arm64), const IP_RECVIF = 20
pkg syscall (darwin-arm64), const IP_RECVIF ideal-int
pkg syscall (darwin-arm64), const IP_RECVOPTS = 5
pkg syscall (darwin-arm64), const IP_RECVOPTS ideal-int
pkg syscall (darwin-arm64), const IP_RECVPKTINFO = 26
pkg syscall (darwin-arm64), const IP_RECVPKTINFO ideal-int
pkg syscall (darwin-arm64), const IP_RECVRETOPTS = 6
pkg syscall (darwin-arm64), const IP_RECVRETOPTS ideal-int
pkg syscall (darwin-arm64), const IP_RECVTTL = 24
pkg syscall (darwin-arm64), const IP_RECVTTL ideal-int
pkg syscall (darwin-arm64), const IP_RETOPTS = 8
pkg syscall (darwin-arm64), const IP_RETOPTS ideal-int
pkg syscall (darwin-arm64), const IP_RF = 32768
pkg syscall (darwin-arm64), const IP_RF ideal-int
pkg syscall (darwin-arm64), const IP_RSVP_OFF = 16
pkg syscall (darwin-arm64), const IP_RSVP_OFF ideal-int
pkg syscall (darwin-arm64), const IP_RSVP_ON = 15
pkg syscall (darwin-arm64), const IP_RSVP_ON ideal-int
pkg syscall (darwin-arm64), const IP_RSVP_VIF_OFF = 18
pkg syscall (darwin-arm64), const IP_RSVP_VIF_OFF ideal-int
pkg syscall (darwin-arm64), const IP_RSVP_VIF_ON = 17
pkg syscall (darwin-arm64), const IP_RSVP_VIF_ON ideal-int
pkg syscall (darwin-arm64), const IP_STRIPHDR = 23
pkg syscall (darwin-arm64), const IP_STRIPHDR ideal-int
pkg syscall (darwin-arm64), const IP_TOS = 3
pkg syscall (darwin-arm64), const IP_TRAFFIC_MGT_BACKGROUND = 65
pkg syscall (darwin-arm64), const IP_TRAFFIC_MGT_BACKGROUND ideal-int
pkg syscall (darwin-arm64), const IP_TTL = 4
pkg syscall (darwin-arm64), const IP_UNBLOCK_SOURCE = 73
pkg syscall (darwin-arm64), const IP_UNBLOCK_SOURCE ideal-int
pkg syscall (darwin-arm64), const ISIG = 128
pkg syscall (darwin-arm64), const ISIG ideal-int
pkg syscall (darwin-arm64), const ISTRIP = 32
pkg syscall (darwin-arm64), const ISTRIP ideal-int
pkg syscall (darwin-arm64), const IUTF8 = 16384
pkg syscall (darwin-arm64), const IUTF8 ideal-int
pkg syscall (darwin-arm64), const IXANY = 2048
pkg syscall (darwin-arm64), const IXANY ideal-int
pkg syscall (darwin-arm64), const IXOFF = 1024
pkg syscall (darwin-arm64), const IXOFF ideal-int
pkg syscall (darwin-arm64), const IXON = 512
pkg syscall (darwin-arm64), const IXON ideal-int
pkg syscall (darwin-arm64), const LOCK_EX = 2
pkg syscall (darwin-arm64), const LOCK_EX ideal-int
pkg syscall (darwin-arm64), const LOCK_NB = 4
pkg syscall (darwin-arm64), const LOCK_NB ideal-int
pkg syscall (darwin-arm64), const LOCK_SH = 1
pkg syscall (darwin-arm64), const LOCK_SH ideal-int
pkg syscall (darwin-arm64), const LOCK_UN = 8
pkg syscall (darwin-arm64), const LOCK_UN ideal-int
pkg syscall (darwin-arm64), const MADV_CAN_REUSE = 9
pkg syscall (darwin-arm64), const MADV_CAN_REUSE ideal-int
pkg syscall (darwin-arm64), const MADV_DONTNEED = 4
pkg syscall (darwin-arm64), const MADV_DONTNEED ideal-int
pkg syscall (darwin-arm64), const MADV_FREE = 5
pkg syscall (darwin-arm64), const MADV_FREE ideal-int
pkg syscall (darwin-arm64), const MADV_FREE_REUSABLE = 7
pkg syscall (darwin-arm64), const MADV_FREE_REUSABLE ideal-int
pkg syscall (darwin-arm64), const MADV_FREE_REUSE = 8
pkg syscall (darwin-arm64), const MADV_FREE_REUSE ideal-int
pkg syscall (darwin-arm64), const MADV_NORMAL = 0
pkg syscall (darwin-arm64), const MADV_NORMAL ideal-int
pkg syscall (darwin-arm64), const MADV_RANDOM = 1
pkg syscall (darwin-arm64), const MADV_RANDOM ideal-int
pkg syscall (darwin-arm64), const MADV_SEQUENTIAL = 2
pkg syscall (darwin-arm64), const MADV_SEQUENTIAL ideal-int
pkg syscall (darwin-arm64), const MADV_WILLNEED = 3
pkg syscall (darwin-arm64), const MADV_WILLNEED ideal-int
pkg syscall (darwin-arm64), const MADV_ZERO_WIRED_PAGES = 6
pkg syscall (darwin-arm64), const MADV_ZERO_WIRED_PAGES ideal-int
pkg syscall (darwin-arm64), const MAP_ANON = 4096
pkg syscall (darwin-arm64), const MAP_ANON ideal-int
pkg syscall (darwin-arm64), const MAP_COPY = 2
pkg syscall (darwin-arm64), const MAP_COPY ideal-int
pkg syscall (darwin-arm64), const MAP_FILE = 0
pkg syscall (darwin-arm64), const MAP_FILE ideal-int
pkg syscall (darwin-arm64), const MAP_FIXED = 16
pkg syscall (darwin-arm64), const MAP_FIXED ideal-int
pkg syscall (darwin-arm64), const MAP_HASSEMAPHORE = 512
pkg syscall (darwin-arm64), const MAP_HASSEMAPHORE ideal-int
pkg syscall (darwin-arm64), const MAP_JIT = 2048
pkg syscall (darwin-arm64), const MAP_JIT ideal-int
pkg syscall (darwin-arm64), const MAP_NOCACHE = 1024
pkg syscall (darwin-arm64), const MAP_NOCACHE ideal-int
pkg syscall (darwin-arm64), const MAP_NOEXTEND = 256
pkg syscall (darwin-arm64), const MAP_NOEXTEND ideal-int
pkg syscall (darwin-arm64), const MAP_NORESERVE = 64
pkg syscall (darwin-arm64), const MAP_NORESERVE ideal-int
pkg syscall (darwin-arm64), const MAP_PRIVATE = 2
pkg syscall (darwin-arm64), const MAP_PRIVATE ideal-int
pkg syscall (darwin-arm64), const MAP_RENAME = 32
pkg syscall (darwin-arm64), const MAP_RENAME ideal-int
pkg syscall (darwin-arm64), const MAP_RESERVED0080 = 128
pkg syscall (darwin-arm64), const MAP_RESERVED0080 ideal-int
pkg syscall (darwin-arm64), const MAP_SHARED = 1
pkg syscall (darwin-arm64), const MAP_SHARED ideal-int
pkg syscall (darwin-arm64), const MCL_CURRENT = 1
pkg syscall (darwin-arm64), const MCL_CURRENT ideal-int
pkg syscall (darwin-arm64), const MCL_FUTURE = 2
pkg syscall (darwin-arm64), const MCL_FUTURE ideal-int
pkg syscall (darwin-arm64), const MSG_CTRUNC = 32
pkg syscall (darwin-arm64), const MSG_CTRUNC ideal-int
pkg syscall (darwin-arm64), const MSG_DONTROUTE = 4
pkg syscall (darwin-arm64), const MSG_DONTROUTE ideal-int
pkg syscall (darwin-arm64), const MSG_DONTWAIT = 128
pkg syscall (darwin-arm64), const MSG_DONTWAIT ideal-int
pkg syscall (darwin-arm64), const MSG_EOF = 256
pkg syscall (darwin-arm64), const MSG_EOF ideal-int
pkg syscall (darwin-arm64), const MSG_EOR = 8
pkg syscall (darwin-arm64), const MSG_EOR ideal-int
pkg syscall (darwin-arm64), const MSG_FLUSH = 1024
pkg syscall (darwin-arm64), const MSG_FLUSH ideal-int
pkg syscall (darwin-arm64), const MSG_HAVEMORE = 8192
pkg syscall (darwin-arm64), const MSG_HAVEMORE ideal-int
pkg syscall (darwin-arm64), const MSG_HOLD = 2048
pkg syscall (darwin-arm64), const MSG_HOLD ideal-int
pkg syscall (darwin-arm64), const MSG_NEEDSA = 65536
pkg syscall (darwin-arm64), const MSG_NEEDSA ideal-int
pkg syscall (darwin-arm64), const MSG_OOB = 1
pkg syscall (darwin-arm64), const MSG_OOB ideal-int
pkg syscall (darwin-arm64), const MSG_PEEK = 2
pkg syscall (darwin-arm64), const MSG_PEEK ideal-int
pkg syscall (darwin-arm64), const MSG_RCVMORE = 16384
pkg syscall (darwin-arm64), const MSG_RCVMORE ideal-int
pkg syscall (darwin-arm64), const MSG_SEND = 4096
pkg syscall (darwin-arm64), const MSG_SEND ideal-int
pkg syscall (darwin-arm64), const MSG_TRUNC = 16
pkg syscall (darwin-arm64), const MSG_TRUNC ideal-int
pkg syscall (darwin-arm64), const MSG_WAITALL = 64
pkg syscall (darwin-arm64), const MSG_WAITALL ideal-int
pkg syscall (darwin-arm64), const MSG_WAITSTREAM = 512
pkg syscall (darwin-arm64), const MSG_WAITSTREAM ideal-int
pkg syscall (darwin-arm64), const MS_ASYNC = 1
pkg syscall (darwin-arm64), const MS_ASYNC ideal-int
pkg syscall (darwin-arm64), const MS_DEACTIVATE = 8
pkg syscall (darwin-arm64), const MS_DEACTIVATE ideal-int
pkg syscall (darwin-arm64), const MS_INVALIDATE = 2
pkg syscall (darwin-arm64), const MS_INVALIDATE ideal-int
pkg syscall (darwin-arm64), const MS_KILLPAGES = 4
pkg syscall (darwin-arm64), const MS_KILLPAGES ideal-int
pkg syscall (darwin-arm64), const MS_SYNC = 16
pkg syscall (darwin-arm64), const MS_SYNC ideal-int
pkg syscall (darwin-arm64), const NAME_MAX = 255
pkg syscall (darwin-arm64), const NAME_MAX ideal-int
pkg syscall (darwin-arm64), const NET_RT_DUMP = 1
pkg syscall (darwin-arm64), const NET_RT_DUMP ideal-int
pkg syscall (darwin-arm64), const NET_RT_DUMP2 = 7
pkg syscall (darwin-arm64), const NET_RT_DUMP2 ideal-int
pkg syscall (darwin-arm64), const NET_RT_FLAGS = 2
pkg syscall (darwin-arm64), const NET_RT_FLAGS ideal-int
pkg syscall (darwin-arm64), const NET_RT_IFLIST = 3
pkg syscall (darwin-arm64), const NET_RT_IFLIST ideal-int
pkg syscall (darwin-arm64), const NET_RT_IFLIST2 = 6
pkg syscall (darwin-arm64), const NET_RT_IFLIST2 ideal-int
pkg syscall (darwin-arm64), const NET_RT_MAXID = 10
pkg syscall (darwin-arm64), const NET_RT_MAXID ideal-int
pkg syscall (darwin-arm64), const NET_RT_STAT = 4
pkg syscall (darwin-arm64), const NET_RT_STAT ideal-int
pkg syscall (darwin-arm64), const NET_RT_TRASH = 5
pkg syscall (darwin-arm64), const NET_RT_TRASH ideal-int
pkg syscall (darwin-arm64), const NOFLSH = 2147483648
pkg syscall (darwin-arm64), const NOFLSH ideal-int
pkg syscall (darwin-arm64), const NOTE_ABSOLUTE = 8
pkg syscall (darwin-arm64), const NOTE_ABSOLUTE ideal-int
pkg syscall (darwin-arm64), const NOTE_ATTRIB = 8
pkg syscall (darwin-arm64), const NOTE_ATTRIB ideal-int
pkg syscall (darwin-arm64), const NOTE_BACKGROUND = 64
pkg syscall (darwin-arm64), const NOTE_BACKGROUND ideal-int
pkg syscall (darwin-arm64), const NOTE_CHILD = 4
pkg syscall (darwin-arm64), const NOTE_CHILD ideal-int
pkg syscall (darwin-arm64), const NOTE_CRITICAL = 32
pkg syscall (darwin-arm64), const NOTE_CRITICAL ideal-int
pkg syscall (darwin-arm64), const NOTE_DELETE = 1
pkg syscall (darwin-arm64), const NOTE_DELETE ideal-int
pkg syscall (darwin-arm64), const NOTE_EXEC = *********
pkg syscall (darwin-arm64), const NOTE_EXEC ideal-int
pkg syscall (darwin-arm64), const NOTE_EXIT = 2147483648
pkg syscall (darwin-arm64), const NOTE_EXIT ideal-int
pkg syscall (darwin-arm64), const NOTE_EXITSTATUS = 67108864
pkg syscall (darwin-arm64), const NOTE_EXITSTATUS ideal-int
pkg syscall (darwin-arm64), const NOTE_EXIT_CSERROR = 262144
pkg syscall (darwin-arm64), const NOTE_EXIT_CSERROR ideal-int
pkg syscall (darwin-arm64), const NOTE_EXIT_DECRYPTFAIL = 65536
pkg syscall (darwin-arm64), const NOTE_EXIT_DECRYPTFAIL ideal-int
pkg syscall (darwin-arm64), const NOTE_EXIT_DETAIL = 33554432
pkg syscall (darwin-arm64), const NOTE_EXIT_DETAIL ideal-int
pkg syscall (darwin-arm64), const NOTE_EXIT_DETAIL_MASK = 458752
pkg syscall (darwin-arm64), const NOTE_EXIT_DETAIL_MASK ideal-int
pkg syscall (darwin-arm64), const NOTE_EXIT_MEMORY = 131072
pkg syscall (darwin-arm64), const NOTE_EXIT_MEMORY ideal-int
pkg syscall (darwin-arm64), const NOTE_EXIT_REPARENTED = 524288
pkg syscall (darwin-arm64), const NOTE_EXIT_REPARENTED ideal-int
pkg syscall (darwin-arm64), const NOTE_EXTEND = 4
pkg syscall (darwin-arm64), const NOTE_EXTEND ideal-int
pkg syscall (darwin-arm64), const NOTE_FFAND = 1073741824
pkg syscall (darwin-arm64), const NOTE_FFAND ideal-int
pkg syscall (darwin-arm64), const NOTE_FFCOPY = 3221225472
pkg syscall (darwin-arm64), const NOTE_FFCOPY ideal-int
pkg syscall (darwin-arm64), const NOTE_FFCTRLMASK = 3221225472
pkg syscall (darwin-arm64), const NOTE_FFCTRLMASK ideal-int
pkg syscall (darwin-arm64), const NOTE_FFLAGSMASK = 16777215
pkg syscall (darwin-arm64), const NOTE_FFLAGSMASK ideal-int
pkg syscall (darwin-arm64), const NOTE_FFNOP = 0
pkg syscall (darwin-arm64), const NOTE_FFNOP ideal-int
pkg syscall (darwin-arm64), const NOTE_FFOR = 2147483648
pkg syscall (darwin-arm64), const NOTE_FFOR ideal-int
pkg syscall (darwin-arm64), const NOTE_FORK = 1073741824
pkg syscall (darwin-arm64), const NOTE_FORK ideal-int
pkg syscall (darwin-arm64), const NOTE_LEEWAY = 16
pkg syscall (darwin-arm64), const NOTE_LEEWAY ideal-int
pkg syscall (darwin-arm64), const NOTE_LINK = 16
pkg syscall (darwin-arm64), const NOTE_LINK ideal-int
pkg syscall (darwin-arm64), const NOTE_LOWAT = 1
pkg syscall (darwin-arm64), const NOTE_LOWAT ideal-int
pkg syscall (darwin-arm64), const NOTE_NONE = 128
pkg syscall (darwin-arm64), const NOTE_NONE ideal-int
pkg syscall (darwin-arm64), const NOTE_NSECONDS = 4
pkg syscall (darwin-arm64), const NOTE_NSECONDS ideal-int
pkg syscall (darwin-arm64), const NOTE_PCTRLMASK = -1048576
pkg syscall (darwin-arm64), const NOTE_PCTRLMASK ideal-int
pkg syscall (darwin-arm64), const NOTE_PDATAMASK = 1048575
pkg syscall (darwin-arm64), const NOTE_PDATAMASK ideal-int
pkg syscall (darwin-arm64), const NOTE_REAP = 268435456
pkg syscall (darwin-arm64), const NOTE_REAP ideal-int
pkg syscall (darwin-arm64), const NOTE_RENAME = 32
pkg syscall (darwin-arm64), const NOTE_RENAME ideal-int
pkg syscall (darwin-arm64), const NOTE_REVOKE = 64
pkg syscall (darwin-arm64), const NOTE_REVOKE ideal-int
pkg syscall (darwin-arm64), const NOTE_SECONDS = 1
pkg syscall (darwin-arm64), const NOTE_SECONDS ideal-int
pkg syscall (darwin-arm64), const NOTE_SIGNAL = 134217728
pkg syscall (darwin-arm64), const NOTE_SIGNAL ideal-int
pkg syscall (darwin-arm64), const NOTE_TRACK = 1
pkg syscall (darwin-arm64), const NOTE_TRACK ideal-int
pkg syscall (darwin-arm64), const NOTE_TRACKERR = 2
pkg syscall (darwin-arm64), const NOTE_TRACKERR ideal-int
pkg syscall (darwin-arm64), const NOTE_TRIGGER = 16777216
pkg syscall (darwin-arm64), const NOTE_TRIGGER ideal-int
pkg syscall (darwin-arm64), const NOTE_USECONDS = 2
pkg syscall (darwin-arm64), const NOTE_USECONDS ideal-int
pkg syscall (darwin-arm64), const NOTE_VM_ERROR = 268435456
pkg syscall (darwin-arm64), const NOTE_VM_ERROR ideal-int
pkg syscall (darwin-arm64), const NOTE_VM_PRESSURE = 2147483648
pkg syscall (darwin-arm64), const NOTE_VM_PRESSURE ideal-int
pkg syscall (darwin-arm64), const NOTE_VM_PRESSURE_SUDDEN_TERMINATE = *********
pkg syscall (darwin-arm64), const NOTE_VM_PRESSURE_SUDDEN_TERMINATE ideal-int
pkg syscall (darwin-arm64), const NOTE_VM_PRESSURE_TERMINATE = 1073741824
pkg syscall (darwin-arm64), const NOTE_VM_PRESSURE_TERMINATE ideal-int
pkg syscall (darwin-arm64), const NOTE_WRITE = 2
pkg syscall (darwin-arm64), const NOTE_WRITE ideal-int
pkg syscall (darwin-arm64), const OCRNL = 16
pkg syscall (darwin-arm64), const OCRNL ideal-int
pkg syscall (darwin-arm64), const OFDEL = 131072
pkg syscall (darwin-arm64), const OFDEL ideal-int
pkg syscall (darwin-arm64), const OFILL = 128
pkg syscall (darwin-arm64), const OFILL ideal-int
pkg syscall (darwin-arm64), const ONLCR = 2
pkg syscall (darwin-arm64), const ONLCR ideal-int
pkg syscall (darwin-arm64), const ONLRET = 64
pkg syscall (darwin-arm64), const ONLRET ideal-int
pkg syscall (darwin-arm64), const ONOCR = 32
pkg syscall (darwin-arm64), const ONOCR ideal-int
pkg syscall (darwin-arm64), const ONOEOT = 8
pkg syscall (darwin-arm64), const ONOEOT ideal-int
pkg syscall (darwin-arm64), const OPOST = 1
pkg syscall (darwin-arm64), const OPOST ideal-int
pkg syscall (darwin-arm64), const O_ACCMODE = 3
pkg syscall (darwin-arm64), const O_ACCMODE ideal-int
pkg syscall (darwin-arm64), const O_ALERT = *********
pkg syscall (darwin-arm64), const O_ALERT ideal-int
pkg syscall (darwin-arm64), const O_APPEND = 8
pkg syscall (darwin-arm64), const O_ASYNC = 64
pkg syscall (darwin-arm64), const O_CLOEXEC = 16777216
pkg syscall (darwin-arm64), const O_CREAT = 512
pkg syscall (darwin-arm64), const O_DIRECTORY = 1048576
pkg syscall (darwin-arm64), const O_DIRECTORY ideal-int
pkg syscall (darwin-arm64), const O_DP_GETRAWENCRYPTED = 1
pkg syscall (darwin-arm64), const O_DP_GETRAWENCRYPTED ideal-int
pkg syscall (darwin-arm64), const O_DSYNC = 4194304
pkg syscall (darwin-arm64), const O_DSYNC ideal-int
pkg syscall (darwin-arm64), const O_EVTONLY = 32768
pkg syscall (darwin-arm64), const O_EVTONLY ideal-int
pkg syscall (darwin-arm64), const O_EXCL = 2048
pkg syscall (darwin-arm64), const O_EXLOCK = 32
pkg syscall (darwin-arm64), const O_EXLOCK ideal-int
pkg syscall (darwin-arm64), const O_FSYNC = 128
pkg syscall (darwin-arm64), const O_FSYNC ideal-int
pkg syscall (darwin-arm64), const O_NDELAY = 4
pkg syscall (darwin-arm64), const O_NDELAY ideal-int
pkg syscall (darwin-arm64), const O_NOCTTY = 131072
pkg syscall (darwin-arm64), const O_NOFOLLOW = 256
pkg syscall (darwin-arm64), const O_NOFOLLOW ideal-int
pkg syscall (darwin-arm64), const O_NONBLOCK = 4
pkg syscall (darwin-arm64), const O_POPUP = 2147483648
pkg syscall (darwin-arm64), const O_POPUP ideal-int
pkg syscall (darwin-arm64), const O_SHLOCK = 16
pkg syscall (darwin-arm64), const O_SHLOCK ideal-int
pkg syscall (darwin-arm64), const O_SYMLINK = 2097152
pkg syscall (darwin-arm64), const O_SYMLINK ideal-int
pkg syscall (darwin-arm64), const O_SYNC = 128
pkg syscall (darwin-arm64), const O_TRUNC = 1024
pkg syscall (darwin-arm64), const PARENB = 4096
pkg syscall (darwin-arm64), const PARENB ideal-int
pkg syscall (darwin-arm64), const PARMRK = 8
pkg syscall (darwin-arm64), const PARMRK ideal-int
pkg syscall (darwin-arm64), const PARODD = 8192
pkg syscall (darwin-arm64), const PARODD ideal-int
pkg syscall (darwin-arm64), const PENDIN = *********
pkg syscall (darwin-arm64), const PENDIN ideal-int
pkg syscall (darwin-arm64), const PRIO_PGRP = 1
pkg syscall (darwin-arm64), const PRIO_PGRP ideal-int
pkg syscall (darwin-arm64), const PRIO_PROCESS = 0
pkg syscall (darwin-arm64), const PRIO_PROCESS ideal-int
pkg syscall (darwin-arm64), const PRIO_USER = 2
pkg syscall (darwin-arm64), const PRIO_USER ideal-int
pkg syscall (darwin-arm64), const PROT_EXEC = 4
pkg syscall (darwin-arm64), const PROT_EXEC ideal-int
pkg syscall (darwin-arm64), const PROT_NONE = 0
pkg syscall (darwin-arm64), const PROT_NONE ideal-int
pkg syscall (darwin-arm64), const PROT_READ = 1
pkg syscall (darwin-arm64), const PROT_READ ideal-int
pkg syscall (darwin-arm64), const PROT_WRITE = 2
pkg syscall (darwin-arm64), const PROT_WRITE ideal-int
pkg syscall (darwin-arm64), const PTRACE_CONT = 7
pkg syscall (darwin-arm64), const PTRACE_CONT ideal-int
pkg syscall (darwin-arm64), const PTRACE_KILL = 8
pkg syscall (darwin-arm64), const PTRACE_KILL ideal-int
pkg syscall (darwin-arm64), const PTRACE_TRACEME = 0
pkg syscall (darwin-arm64), const PTRACE_TRACEME ideal-int
pkg syscall (darwin-arm64), const PT_ATTACH = 10
pkg syscall (darwin-arm64), const PT_ATTACH ideal-int
pkg syscall (darwin-arm64), const PT_ATTACHEXC = 14
pkg syscall (darwin-arm64), const PT_ATTACHEXC ideal-int
pkg syscall (darwin-arm64), const PT_CONTINUE = 7
pkg syscall (darwin-arm64), const PT_CONTINUE ideal-int
pkg syscall (darwin-arm64), const PT_DENY_ATTACH = 31
pkg syscall (darwin-arm64), const PT_DENY_ATTACH ideal-int
pkg syscall (darwin-arm64), const PT_DETACH = 11
pkg syscall (darwin-arm64), const PT_DETACH ideal-int
pkg syscall (darwin-arm64), const PT_FIRSTMACH = 32
pkg syscall (darwin-arm64), const PT_FIRSTMACH ideal-int
pkg syscall (darwin-arm64), const PT_FORCEQUOTA = 30
pkg syscall (darwin-arm64), const PT_FORCEQUOTA ideal-int
pkg syscall (darwin-arm64), const PT_KILL = 8
pkg syscall (darwin-arm64), const PT_KILL ideal-int
pkg syscall (darwin-arm64), const PT_READ_D = 2
pkg syscall (darwin-arm64), const PT_READ_D ideal-int
pkg syscall (darwin-arm64), const PT_READ_I = 1
pkg syscall (darwin-arm64), const PT_READ_I ideal-int
pkg syscall (darwin-arm64), const PT_READ_U = 3
pkg syscall (darwin-arm64), const PT_READ_U ideal-int
pkg syscall (darwin-arm64), const PT_SIGEXC = 12
pkg syscall (darwin-arm64), const PT_SIGEXC ideal-int
pkg syscall (darwin-arm64), const PT_STEP = 9
pkg syscall (darwin-arm64), const PT_STEP ideal-int
pkg syscall (darwin-arm64), const PT_THUPDATE = 13
pkg syscall (darwin-arm64), const PT_THUPDATE ideal-int
pkg syscall (darwin-arm64), const PT_TRACE_ME = 0
pkg syscall (darwin-arm64), const PT_TRACE_ME ideal-int
pkg syscall (darwin-arm64), const PT_WRITE_D = 5
pkg syscall (darwin-arm64), const PT_WRITE_D ideal-int
pkg syscall (darwin-arm64), const PT_WRITE_I = 4
pkg syscall (darwin-arm64), const PT_WRITE_I ideal-int
pkg syscall (darwin-arm64), const PT_WRITE_U = 6
pkg syscall (darwin-arm64), const PT_WRITE_U ideal-int
pkg syscall (darwin-arm64), const RLIMIT_AS = 5
pkg syscall (darwin-arm64), const RLIMIT_AS ideal-int
pkg syscall (darwin-arm64), const RLIMIT_CORE = 4
pkg syscall (darwin-arm64), const RLIMIT_CORE ideal-int
pkg syscall (darwin-arm64), const RLIMIT_CPU = 0
pkg syscall (darwin-arm64), const RLIMIT_CPU ideal-int
pkg syscall (darwin-arm64), const RLIMIT_CPU_USAGE_MONITOR = 2
pkg syscall (darwin-arm64), const RLIMIT_CPU_USAGE_MONITOR ideal-int
pkg syscall (darwin-arm64), const RLIMIT_DATA = 2
pkg syscall (darwin-arm64), const RLIMIT_DATA ideal-int
pkg syscall (darwin-arm64), const RLIMIT_FSIZE = 1
pkg syscall (darwin-arm64), const RLIMIT_FSIZE ideal-int
pkg syscall (darwin-arm64), const RLIMIT_NOFILE = 8
pkg syscall (darwin-arm64), const RLIMIT_NOFILE ideal-int
pkg syscall (darwin-arm64), const RLIMIT_STACK = 3
pkg syscall (darwin-arm64), const RLIMIT_STACK ideal-int
pkg syscall (darwin-arm64), const RLIM_INFINITY = 9223372036854775807
pkg syscall (darwin-arm64), const RLIM_INFINITY ideal-int
pkg syscall (darwin-arm64), const RTAX_AUTHOR = 6
pkg syscall (darwin-arm64), const RTAX_AUTHOR ideal-int
pkg syscall (darwin-arm64), const RTAX_BRD = 7
pkg syscall (darwin-arm64), const RTAX_BRD ideal-int
pkg syscall (darwin-arm64), const RTAX_DST = 0
pkg syscall (darwin-arm64), const RTAX_DST ideal-int
pkg syscall (darwin-arm64), const RTAX_GATEWAY = 1
pkg syscall (darwin-arm64), const RTAX_GATEWAY ideal-int
pkg syscall (darwin-arm64), const RTAX_GENMASK = 3
pkg syscall (darwin-arm64), const RTAX_GENMASK ideal-int
pkg syscall (darwin-arm64), const RTAX_IFA = 5
pkg syscall (darwin-arm64), const RTAX_IFA ideal-int
pkg syscall (darwin-arm64), const RTAX_IFP = 4
pkg syscall (darwin-arm64), const RTAX_IFP ideal-int
pkg syscall (darwin-arm64), const RTAX_MAX = 8
pkg syscall (darwin-arm64), const RTAX_MAX ideal-int
pkg syscall (darwin-arm64), const RTAX_NETMASK = 2
pkg syscall (darwin-arm64), const RTAX_NETMASK ideal-int
pkg syscall (darwin-arm64), const RTA_AUTHOR = 64
pkg syscall (darwin-arm64), const RTA_AUTHOR ideal-int
pkg syscall (darwin-arm64), const RTA_BRD = 128
pkg syscall (darwin-arm64), const RTA_BRD ideal-int
pkg syscall (darwin-arm64), const RTA_DST = 1
pkg syscall (darwin-arm64), const RTA_DST ideal-int
pkg syscall (darwin-arm64), const RTA_GATEWAY = 2
pkg syscall (darwin-arm64), const RTA_GATEWAY ideal-int
pkg syscall (darwin-arm64), const RTA_GENMASK = 8
pkg syscall (darwin-arm64), const RTA_GENMASK ideal-int
pkg syscall (darwin-arm64), const RTA_IFA = 32
pkg syscall (darwin-arm64), const RTA_IFA ideal-int
pkg syscall (darwin-arm64), const RTA_IFP = 16
pkg syscall (darwin-arm64), const RTA_IFP ideal-int
pkg syscall (darwin-arm64), const RTA_NETMASK = 4
pkg syscall (darwin-arm64), const RTA_NETMASK ideal-int
pkg syscall (darwin-arm64), const RTF_BLACKHOLE = 4096
pkg syscall (darwin-arm64), const RTF_BLACKHOLE ideal-int
pkg syscall (darwin-arm64), const RTF_BROADCAST = 4194304
pkg syscall (darwin-arm64), const RTF_BROADCAST ideal-int
pkg syscall (darwin-arm64), const RTF_CLONING = 256
pkg syscall (darwin-arm64), const RTF_CLONING ideal-int
pkg syscall (darwin-arm64), const RTF_CONDEMNED = 33554432
pkg syscall (darwin-arm64), const RTF_CONDEMNED ideal-int
pkg syscall (darwin-arm64), const RTF_DELCLONE = 128
pkg syscall (darwin-arm64), const RTF_DELCLONE ideal-int
pkg syscall (darwin-arm64), const RTF_DONE = 64
pkg syscall (darwin-arm64), const RTF_DONE ideal-int
pkg syscall (darwin-arm64), const RTF_DYNAMIC = 16
pkg syscall (darwin-arm64), const RTF_DYNAMIC ideal-int
pkg syscall (darwin-arm64), const RTF_GATEWAY = 2
pkg syscall (darwin-arm64), const RTF_GATEWAY ideal-int
pkg syscall (darwin-arm64), const RTF_HOST = 4
pkg syscall (darwin-arm64), const RTF_HOST ideal-int
pkg syscall (darwin-arm64), const RTF_IFREF = 67108864
pkg syscall (darwin-arm64), const RTF_IFREF ideal-int
pkg syscall (darwin-arm64), const RTF_IFSCOPE = 16777216
pkg syscall (darwin-arm64), const RTF_IFSCOPE ideal-int
pkg syscall (darwin-arm64), const RTF_LLINFO = 1024
pkg syscall (darwin-arm64), const RTF_LLINFO ideal-int
pkg syscall (darwin-arm64), const RTF_LOCAL = 2097152
pkg syscall (darwin-arm64), const RTF_LOCAL ideal-int
pkg syscall (darwin-arm64), const RTF_MODIFIED = 32
pkg syscall (darwin-arm64), const RTF_MODIFIED ideal-int
pkg syscall (darwin-arm64), const RTF_MULTICAST = 8388608
pkg syscall (darwin-arm64), const RTF_MULTICAST ideal-int
pkg syscall (darwin-arm64), const RTF_PINNED = 1048576
pkg syscall (darwin-arm64), const RTF_PINNED ideal-int
pkg syscall (darwin-arm64), const RTF_PRCLONING = 65536
pkg syscall (darwin-arm64), const RTF_PRCLONING ideal-int
pkg syscall (darwin-arm64), const RTF_PROTO1 = 32768
pkg syscall (darwin-arm64), const RTF_PROTO1 ideal-int
pkg syscall (darwin-arm64), const RTF_PROTO2 = 16384
pkg syscall (darwin-arm64), const RTF_PROTO2 ideal-int
pkg syscall (darwin-arm64), const RTF_PROTO3 = 262144
pkg syscall (darwin-arm64), const RTF_PROTO3 ideal-int
pkg syscall (darwin-arm64), const RTF_PROXY = 134217728
pkg syscall (darwin-arm64), const RTF_PROXY ideal-int
pkg syscall (darwin-arm64), const RTF_REJECT = 8
pkg syscall (darwin-arm64), const RTF_REJECT ideal-int
pkg syscall (darwin-arm64), const RTF_ROUTER = 268435456
pkg syscall (darwin-arm64), const RTF_ROUTER ideal-int
pkg syscall (darwin-arm64), const RTF_STATIC = 2048
pkg syscall (darwin-arm64), const RTF_STATIC ideal-int
pkg syscall (darwin-arm64), const RTF_UP = 1
pkg syscall (darwin-arm64), const RTF_UP ideal-int
pkg syscall (darwin-arm64), const RTF_WASCLONED = 131072
pkg syscall (darwin-arm64), const RTF_WASCLONED ideal-int
pkg syscall (darwin-arm64), const RTF_XRESOLVE = 512
pkg syscall (darwin-arm64), const RTF_XRESOLVE ideal-int
pkg syscall (darwin-arm64), const RTM_ADD = 1
pkg syscall (darwin-arm64), const RTM_ADD ideal-int
pkg syscall (darwin-arm64), const RTM_CHANGE = 3
pkg syscall (darwin-arm64), const RTM_CHANGE ideal-int
pkg syscall (darwin-arm64), const RTM_DELADDR = 13
pkg syscall (darwin-arm64), const RTM_DELADDR ideal-int
pkg syscall (darwin-arm64), const RTM_DELETE = 2
pkg syscall (darwin-arm64), const RTM_DELETE ideal-int
pkg syscall (darwin-arm64), const RTM_DELMADDR = 16
pkg syscall (darwin-arm64), const RTM_DELMADDR ideal-int
pkg syscall (darwin-arm64), const RTM_GET = 4
pkg syscall (darwin-arm64), const RTM_GET ideal-int
pkg syscall (darwin-arm64), const RTM_GET2 = 20
pkg syscall (darwin-arm64), const RTM_GET2 ideal-int
pkg syscall (darwin-arm64), const RTM_IFINFO = 14
pkg syscall (darwin-arm64), const RTM_IFINFO ideal-int
pkg syscall (darwin-arm64), const RTM_IFINFO2 = 18
pkg syscall (darwin-arm64), const RTM_IFINFO2 ideal-int
pkg syscall (darwin-arm64), const RTM_LOCK = 8
pkg syscall (darwin-arm64), const RTM_LOCK ideal-int
pkg syscall (darwin-arm64), const RTM_LOSING = 5
pkg syscall (darwin-arm64), const RTM_LOSING ideal-int
pkg syscall (darwin-arm64), const RTM_MISS = 7
pkg syscall (darwin-arm64), const RTM_MISS ideal-int
pkg syscall (darwin-arm64), const RTM_NEWADDR = 12
pkg syscall (darwin-arm64), const RTM_NEWADDR ideal-int
pkg syscall (darwin-arm64), const RTM_NEWMADDR = 15
pkg syscall (darwin-arm64), const RTM_NEWMADDR ideal-int
pkg syscall (darwin-arm64), const RTM_NEWMADDR2 = 19
pkg syscall (darwin-arm64), const RTM_NEWMADDR2 ideal-int
pkg syscall (darwin-arm64), const RTM_OLDADD = 9
pkg syscall (darwin-arm64), const RTM_OLDADD ideal-int
pkg syscall (darwin-arm64), const RTM_OLDDEL = 10
pkg syscall (darwin-arm64), const RTM_OLDDEL ideal-int
pkg syscall (darwin-arm64), const RTM_REDIRECT = 6
pkg syscall (darwin-arm64), const RTM_REDIRECT ideal-int
pkg syscall (darwin-arm64), const RTM_RESOLVE = 11
pkg syscall (darwin-arm64), const RTM_RESOLVE ideal-int
pkg syscall (darwin-arm64), const RTM_RTTUNIT = 1000000
pkg syscall (darwin-arm64), const RTM_RTTUNIT ideal-int
pkg syscall (darwin-arm64), const RTM_VERSION = 5
pkg syscall (darwin-arm64), const RTM_VERSION ideal-int
pkg syscall (darwin-arm64), const RTV_EXPIRE = 4
pkg syscall (darwin-arm64), const RTV_EXPIRE ideal-int
pkg syscall (darwin-arm64), const RTV_HOPCOUNT = 2
pkg syscall (darwin-arm64), const RTV_HOPCOUNT ideal-int
pkg syscall (darwin-arm64), const RTV_MTU = 1
pkg syscall (darwin-arm64), const RTV_MTU ideal-int
pkg syscall (darwin-arm64), const RTV_RPIPE = 8
pkg syscall (darwin-arm64), const RTV_RPIPE ideal-int
pkg syscall (darwin-arm64), const RTV_RTT = 64
pkg syscall (darwin-arm64), const RTV_RTT ideal-int
pkg syscall (darwin-arm64), const RTV_RTTVAR = 128
pkg syscall (darwin-arm64), const RTV_RTTVAR ideal-int
pkg syscall (darwin-arm64), const RTV_SPIPE = 16
pkg syscall (darwin-arm64), const RTV_SPIPE ideal-int
pkg syscall (darwin-arm64), const RTV_SSTHRESH = 32
pkg syscall (darwin-arm64), const RTV_SSTHRESH ideal-int
pkg syscall (darwin-arm64), const RUSAGE_CHILDREN = -1
pkg syscall (darwin-arm64), const RUSAGE_CHILDREN ideal-int
pkg syscall (darwin-arm64), const RUSAGE_SELF = 0
pkg syscall (darwin-arm64), const RUSAGE_SELF ideal-int
pkg syscall (darwin-arm64), const SCM_CREDS = 3
pkg syscall (darwin-arm64), const SCM_CREDS ideal-int
pkg syscall (darwin-arm64), const SCM_RIGHTS = 1
pkg syscall (darwin-arm64), const SCM_RIGHTS ideal-int
pkg syscall (darwin-arm64), const SCM_TIMESTAMP = 2
pkg syscall (darwin-arm64), const SCM_TIMESTAMP ideal-int
pkg syscall (darwin-arm64), const SCM_TIMESTAMP_MONOTONIC = 4
pkg syscall (darwin-arm64), const SCM_TIMESTAMP_MONOTONIC ideal-int
pkg syscall (darwin-arm64), const SIGBUS = 10
pkg syscall (darwin-arm64), const SIGCHLD = 20
pkg syscall (darwin-arm64), const SIGCHLD Signal
pkg syscall (darwin-arm64), const SIGCONT = 19
pkg syscall (darwin-arm64), const SIGCONT Signal
pkg syscall (darwin-arm64), const SIGEMT = 7
pkg syscall (darwin-arm64), const SIGEMT Signal
pkg syscall (darwin-arm64), const SIGINFO = 29
pkg syscall (darwin-arm64), const SIGINFO Signal
pkg syscall (darwin-arm64), const SIGIO = 23
pkg syscall (darwin-arm64), const SIGIO Signal
pkg syscall (darwin-arm64), const SIGIOT = 6
pkg syscall (darwin-arm64), const SIGIOT Signal
pkg syscall (darwin-arm64), const SIGPROF = 27
pkg syscall (darwin-arm64), const SIGPROF Signal
pkg syscall (darwin-arm64), const SIGSTOP = 17
pkg syscall (darwin-arm64), const SIGSTOP Signal
pkg syscall (darwin-arm64), const SIGSYS = 12
pkg syscall (darwin-arm64), const SIGSYS Signal
pkg syscall (darwin-arm64), const SIGTSTP = 18
pkg syscall (darwin-arm64), const SIGTSTP Signal
pkg syscall (darwin-arm64), const SIGTTIN = 21
pkg syscall (darwin-arm64), const SIGTTIN Signal
pkg syscall (darwin-arm64), const SIGTTOU = 22
pkg syscall (darwin-arm64), const SIGTTOU Signal
pkg syscall (darwin-arm64), const SIGURG = 16
pkg syscall (darwin-arm64), const SIGURG Signal
pkg syscall (darwin-arm64), const SIGUSR1 = 30
pkg syscall (darwin-arm64), const SIGUSR1 Signal
pkg syscall (darwin-arm64), const SIGUSR2 = 31
pkg syscall (darwin-arm64), const SIGUSR2 Signal
pkg syscall (darwin-arm64), const SIGVTALRM = 26
pkg syscall (darwin-arm64), const SIGVTALRM Signal
pkg syscall (darwin-arm64), const SIGWINCH = 28
pkg syscall (darwin-arm64), const SIGWINCH Signal
pkg syscall (darwin-arm64), const SIGXCPU = 24
pkg syscall (darwin-arm64), const SIGXCPU Signal
pkg syscall (darwin-arm64), const SIGXFSZ = 25
pkg syscall (darwin-arm64), const SIGXFSZ Signal
pkg syscall (darwin-arm64), const SIOCADDMULTI = 2149607729
pkg syscall (darwin-arm64), const SIOCADDMULTI ideal-int
pkg syscall (darwin-arm64), const SIOCAIFADDR = 2151704858
pkg syscall (darwin-arm64), const SIOCAIFADDR ideal-int
pkg syscall (darwin-arm64), const SIOCARPIPLL = 3223349544
pkg syscall (darwin-arm64), const SIOCARPIPLL ideal-int
pkg syscall (darwin-arm64), const SIOCATMARK = 1074033415
pkg syscall (darwin-arm64), const SIOCATMARK ideal-int
pkg syscall (darwin-arm64), const SIOCAUTOADDR = 3223349542
pkg syscall (darwin-arm64), const SIOCAUTOADDR ideal-int
pkg syscall (darwin-arm64), const SIOCAUTONETMASK = 2149607719
pkg syscall (darwin-arm64), const SIOCAUTONETMASK ideal-int
pkg syscall (darwin-arm64), const SIOCDELMULTI = 2149607730
pkg syscall (darwin-arm64), const SIOCDELMULTI ideal-int
pkg syscall (darwin-arm64), const SIOCDIFADDR = 2149607705
pkg syscall (darwin-arm64), const SIOCDIFADDR ideal-int
pkg syscall (darwin-arm64), const SIOCDIFPHYADDR = 2149607745
pkg syscall (darwin-arm64), const SIOCDIFPHYADDR ideal-int
pkg syscall (darwin-arm64), const SIOCGDRVSPEC = 3223873915
pkg syscall (darwin-arm64), const SIOCGDRVSPEC ideal-int
pkg syscall (darwin-arm64), const SIOCGETVLAN = 3223349631
pkg syscall (darwin-arm64), const SIOCGETVLAN ideal-int
pkg syscall (darwin-arm64), const SIOCGHIWAT = 1074033409
pkg syscall (darwin-arm64), const SIOCGHIWAT ideal-int
pkg syscall (darwin-arm64), const SIOCGIFADDR = 3223349537
pkg syscall (darwin-arm64), const SIOCGIFADDR ideal-int
pkg syscall (darwin-arm64), const SIOCGIFALTMTU = 3223349576
pkg syscall (darwin-arm64), const SIOCGIFALTMTU ideal-int
pkg syscall (darwin-arm64), const SIOCGIFASYNCMAP = 3223349628
pkg syscall (darwin-arm64), const SIOCGIFASYNCMAP ideal-int
pkg syscall (darwin-arm64), const SIOCGIFBOND = 3223349575
pkg syscall (darwin-arm64), const SIOCGIFBOND ideal-int
pkg syscall (darwin-arm64), const SIOCGIFBRDADDR = 3223349539
pkg syscall (darwin-arm64), const SIOCGIFBRDADDR ideal-int
pkg syscall (darwin-arm64), const SIOCGIFCAP = 3223349595
pkg syscall (darwin-arm64), const SIOCGIFCAP ideal-int
pkg syscall (darwin-arm64), const SIOCGIFCONF = 3222038820
pkg syscall (darwin-arm64), const SIOCGIFCONF ideal-int
pkg syscall (darwin-arm64), const SIOCGIFDEVMTU = 3223349572
pkg syscall (darwin-arm64), const SIOCGIFDEVMTU ideal-int
pkg syscall (darwin-arm64), const SIOCGIFDSTADDR = 3223349538
pkg syscall (darwin-arm64), const SIOCGIFDSTADDR ideal-int
pkg syscall (darwin-arm64), const SIOCGIFFLAGS = 3223349521
pkg syscall (darwin-arm64), const SIOCGIFFLAGS ideal-int
pkg syscall (darwin-arm64), const SIOCGIFGENERIC = 3223349562
pkg syscall (darwin-arm64), const SIOCGIFGENERIC ideal-int
pkg syscall (darwin-arm64), const SIOCGIFKPI = 3223349639
pkg syscall (darwin-arm64), const SIOCGIFKPI ideal-int
pkg syscall (darwin-arm64), const SIOCGIFMAC = 3223349634
pkg syscall (darwin-arm64), const SIOCGIFMAC ideal-int
pkg syscall (darwin-arm64), const SIOCGIFMEDIA = 3224135992
pkg syscall (darwin-arm64), const SIOCGIFMEDIA ideal-int
pkg syscall (darwin-arm64), const SIOCGIFMETRIC = 3223349527
pkg syscall (darwin-arm64), const SIOCGIFMETRIC ideal-int
pkg syscall (darwin-arm64), const SIOCGIFMTU = 3223349555
pkg syscall (darwin-arm64), const SIOCGIFMTU ideal-int
pkg syscall (darwin-arm64), const SIOCGIFNETMASK = 3223349541
pkg syscall (darwin-arm64), const SIOCGIFNETMASK ideal-int
pkg syscall (darwin-arm64), const SIOCGIFPDSTADDR = 3223349568
pkg syscall (darwin-arm64), const SIOCGIFPDSTADDR ideal-int
pkg syscall (darwin-arm64), const SIOCGIFPHYS = 3223349557
pkg syscall (darwin-arm64), const SIOCGIFPHYS ideal-int
pkg syscall (darwin-arm64), const SIOCGIFPSRCADDR = 3223349567
pkg syscall (darwin-arm64), const SIOCGIFPSRCADDR ideal-int
pkg syscall (darwin-arm64), const SIOCGIFSTATUS = 3274795325
pkg syscall (darwin-arm64), const SIOCGIFSTATUS ideal-int
pkg syscall (darwin-arm64), const SIOCGIFVLAN = 3223349631
pkg syscall (darwin-arm64), const SIOCGIFVLAN ideal-int
pkg syscall (darwin-arm64), const SIOCGIFWAKEFLAGS = 3223349640
pkg syscall (darwin-arm64), const SIOCGIFWAKEFLAGS ideal-int
pkg syscall (darwin-arm64), const SIOCGLOWAT = 1074033411
pkg syscall (darwin-arm64), const SIOCGLOWAT ideal-int
pkg syscall (darwin-arm64), const SIOCGPGRP = 1074033417
pkg syscall (darwin-arm64), const SIOCGPGRP ideal-int
pkg syscall (darwin-arm64), const SIOCIFCREATE = 3223349624
pkg syscall (darwin-arm64), const SIOCIFCREATE ideal-int
pkg syscall (darwin-arm64), const SIOCIFCREATE2 = 3223349626
pkg syscall (darwin-arm64), const SIOCIFCREATE2 ideal-int
pkg syscall (darwin-arm64), const SIOCIFDESTROY = 2149607801
pkg syscall (darwin-arm64), const SIOCIFDESTROY ideal-int
pkg syscall (darwin-arm64), const SIOCIFGCLONERS = 3222301057
pkg syscall (darwin-arm64), const SIOCIFGCLONERS ideal-int
pkg syscall (darwin-arm64), const SIOCRSLVMULTI = 3222300987
pkg syscall (darwin-arm64), const SIOCRSLVMULTI ideal-int
pkg syscall (darwin-arm64), const SIOCSDRVSPEC = 2150132091
pkg syscall (darwin-arm64), const SIOCSDRVSPEC ideal-int
pkg syscall (darwin-arm64), const SIOCSETVLAN = 2149607806
pkg syscall (darwin-arm64), const SIOCSETVLAN ideal-int
pkg syscall (darwin-arm64), const SIOCSHIWAT = 2147775232
pkg syscall (darwin-arm64), const SIOCSHIWAT ideal-int
pkg syscall (darwin-arm64), const SIOCSIFADDR = 2149607692
pkg syscall (darwin-arm64), const SIOCSIFADDR ideal-int
pkg syscall (darwin-arm64), const SIOCSIFALTMTU = 2149607749
pkg syscall (darwin-arm64), const SIOCSIFALTMTU ideal-int
pkg syscall (darwin-arm64), const SIOCSIFASYNCMAP = 2149607805
pkg syscall (darwin-arm64), const SIOCSIFASYNCMAP ideal-int
pkg syscall (darwin-arm64), const SIOCSIFBOND = 2149607750
pkg syscall (darwin-arm64), const SIOCSIFBOND ideal-int
pkg syscall (darwin-arm64), const SIOCSIFBRDADDR = 2149607699
pkg syscall (darwin-arm64), const SIOCSIFBRDADDR ideal-int
pkg syscall (darwin-arm64), const SIOCSIFCAP = 2149607770
pkg syscall (darwin-arm64), const SIOCSIFCAP ideal-int
pkg syscall (darwin-arm64), const SIOCSIFDSTADDR = 2149607694
pkg syscall (darwin-arm64), const SIOCSIFDSTADDR ideal-int
pkg syscall (darwin-arm64), const SIOCSIFFLAGS = 2149607696
pkg syscall (darwin-arm64), const SIOCSIFFLAGS ideal-int
pkg syscall (darwin-arm64), const SIOCSIFGENERIC = 2149607737
pkg syscall (darwin-arm64), const SIOCSIFGENERIC ideal-int
pkg syscall (darwin-arm64), const SIOCSIFKPI = 2149607814
pkg syscall (darwin-arm64), const SIOCSIFKPI ideal-int
pkg syscall (darwin-arm64), const SIOCSIFLLADDR = 2149607740
pkg syscall (darwin-arm64), const SIOCSIFLLADDR ideal-int
pkg syscall (darwin-arm64), const SIOCSIFMAC = 2149607811
pkg syscall (darwin-arm64), const SIOCSIFMAC ideal-int
pkg syscall (darwin-arm64), const SIOCSIFMEDIA = 3223349559
pkg syscall (darwin-arm64), const SIOCSIFMEDIA ideal-int
pkg syscall (darwin-arm64), const SIOCSIFMETRIC = 2149607704
pkg syscall (darwin-arm64), const SIOCSIFMETRIC ideal-int
pkg syscall (darwin-arm64), const SIOCSIFMTU = 2149607732
pkg syscall (darwin-arm64), const SIOCSIFMTU ideal-int
pkg syscall (darwin-arm64), const SIOCSIFNETMASK = 2149607702
pkg syscall (darwin-arm64), const SIOCSIFNETMASK ideal-int
pkg syscall (darwin-arm64), const SIOCSIFPHYADDR = 2151704894
pkg syscall (darwin-arm64), const SIOCSIFPHYADDR ideal-int
pkg syscall (darwin-arm64), const SIOCSIFPHYS = 2149607734
pkg syscall (darwin-arm64), const SIOCSIFPHYS ideal-int
pkg syscall (darwin-arm64), const SIOCSIFVLAN = 2149607806
pkg syscall (darwin-arm64), const SIOCSIFVLAN ideal-int
pkg syscall (darwin-arm64), const SIOCSLOWAT = 2147775234
pkg syscall (darwin-arm64), const SIOCSLOWAT ideal-int
pkg syscall (darwin-arm64), const SIOCSPGRP = 2147775240
pkg syscall (darwin-arm64), const SIOCSPGRP ideal-int
pkg syscall (darwin-arm64), const SOCK_MAXADDRLEN = 255
pkg syscall (darwin-arm64), const SOCK_MAXADDRLEN ideal-int
pkg syscall (darwin-arm64), const SOCK_RDM = 4
pkg syscall (darwin-arm64), const SOCK_RDM ideal-int
pkg syscall (darwin-arm64), const SOL_SOCKET = 65535
pkg syscall (darwin-arm64), const SOMAXCONN = 128
pkg syscall (darwin-arm64), const SO_ACCEPTCONN = 2
pkg syscall (darwin-arm64), const SO_ACCEPTCONN ideal-int
pkg syscall (darwin-arm64), const SO_BROADCAST = 32
pkg syscall (darwin-arm64), const SO_DEBUG = 1
pkg syscall (darwin-arm64), const SO_DEBUG ideal-int
pkg syscall (darwin-arm64), const SO_DONTROUTE = 16
pkg syscall (darwin-arm64), const SO_DONTTRUNC = 8192
pkg syscall (darwin-arm64), const SO_DONTTRUNC ideal-int
pkg syscall (darwin-arm64), const SO_ERROR = 4103
pkg syscall (darwin-arm64), const SO_ERROR ideal-int
pkg syscall (darwin-arm64), const SO_KEEPALIVE = 8
pkg syscall (darwin-arm64), const SO_LABEL = 4112
pkg syscall (darwin-arm64), const SO_LABEL ideal-int
pkg syscall (darwin-arm64), const SO_LINGER = 128
pkg syscall (darwin-arm64), const SO_LINGER_SEC = 4224
pkg syscall (darwin-arm64), const SO_LINGER_SEC ideal-int
pkg syscall (darwin-arm64), const SO_NKE = 4129
pkg syscall (darwin-arm64), const SO_NKE ideal-int
pkg syscall (darwin-arm64), const SO_NOADDRERR = 4131
pkg syscall (darwin-arm64), const SO_NOADDRERR ideal-int
pkg syscall (darwin-arm64), const SO_NOSIGPIPE = 4130
pkg syscall (darwin-arm64), const SO_NOSIGPIPE ideal-int
pkg syscall (darwin-arm64), const SO_NOTIFYCONFLICT = 4134
pkg syscall (darwin-arm64), const SO_NOTIFYCONFLICT ideal-int
pkg syscall (darwin-arm64), const SO_NP_EXTENSIONS = 4227
pkg syscall (darwin-arm64), const SO_NP_EXTENSIONS ideal-int
pkg syscall (darwin-arm64), const SO_NREAD = 4128
pkg syscall (darwin-arm64), const SO_NREAD ideal-int
pkg syscall (darwin-arm64), const SO_NUMRCVPKT = 4370
pkg syscall (darwin-arm64), const SO_NUMRCVPKT ideal-int
pkg syscall (darwin-arm64), const SO_NWRITE = 4132
pkg syscall (darwin-arm64), const SO_NWRITE ideal-int
pkg syscall (darwin-arm64), const SO_OOBINLINE = 256
pkg syscall (darwin-arm64), const SO_OOBINLINE ideal-int
pkg syscall (darwin-arm64), const SO_PEERLABEL = 4113
pkg syscall (darwin-arm64), const SO_PEERLABEL ideal-int
pkg syscall (darwin-arm64), const SO_RANDOMPORT = 4226
pkg syscall (darwin-arm64), const SO_RANDOMPORT ideal-int
pkg syscall (darwin-arm64), const SO_RCVBUF = 4098
pkg syscall (darwin-arm64), const SO_RCVLOWAT = 4100
pkg syscall (darwin-arm64), const SO_RCVLOWAT ideal-int
pkg syscall (darwin-arm64), const SO_RCVTIMEO = 4102
pkg syscall (darwin-arm64), const SO_RCVTIMEO ideal-int
pkg syscall (darwin-arm64), const SO_REUSEADDR = 4
pkg syscall (darwin-arm64), const SO_REUSEPORT = 512
pkg syscall (darwin-arm64), const SO_REUSEPORT ideal-int
pkg syscall (darwin-arm64), const SO_REUSESHAREUID = 4133
pkg syscall (darwin-arm64), const SO_REUSESHAREUID ideal-int
pkg syscall (darwin-arm64), const SO_SNDBUF = 4097
pkg syscall (darwin-arm64), const SO_SNDLOWAT = 4099
pkg syscall (darwin-arm64), const SO_SNDLOWAT ideal-int
pkg syscall (darwin-arm64), const SO_SNDTIMEO = 4101
pkg syscall (darwin-arm64), const SO_SNDTIMEO ideal-int
pkg syscall (darwin-arm64), const SO_TIMESTAMP = 1024
pkg syscall (darwin-arm64), const SO_TIMESTAMP ideal-int
pkg syscall (darwin-arm64), const SO_TIMESTAMP_MONOTONIC = 2048
pkg syscall (darwin-arm64), const SO_TIMESTAMP_MONOTONIC ideal-int
pkg syscall (darwin-arm64), const SO_TYPE = 4104
pkg syscall (darwin-arm64), const SO_TYPE ideal-int
pkg syscall (darwin-arm64), const SO_UPCALLCLOSEWAIT = 4135
pkg syscall (darwin-arm64), const SO_UPCALLCLOSEWAIT ideal-int
pkg syscall (darwin-arm64), const SO_USELOOPBACK = 64
pkg syscall (darwin-arm64), const SO_USELOOPBACK ideal-int
pkg syscall (darwin-arm64), const SO_WANTMORE = 16384
pkg syscall (darwin-arm64), const SO_WANTMORE ideal-int
pkg syscall (darwin-arm64), const SO_WANTOOBFLAG = 32768
pkg syscall (darwin-arm64), const SO_WANTOOBFLAG ideal-int
pkg syscall (darwin-arm64), const SYS_ACCEPT = 30
pkg syscall (darwin-arm64), const SYS_ACCEPT ideal-int
pkg syscall (darwin-arm64), const SYS_ACCEPT_NOCANCEL = 404
pkg syscall (darwin-arm64), const SYS_ACCEPT_NOCANCEL ideal-int
pkg syscall (darwin-arm64), const SYS_ACCESS = 33
pkg syscall (darwin-arm64), const SYS_ACCESS ideal-int
pkg syscall (darwin-arm64), const SYS_ACCESS_EXTENDED = 284
pkg syscall (darwin-arm64), const SYS_ACCESS_EXTENDED ideal-int
pkg syscall (darwin-arm64), const SYS_ACCT = 51
pkg syscall (darwin-arm64), const SYS_ACCT ideal-int
pkg syscall (darwin-arm64), const SYS_ADJTIME = 140
pkg syscall (darwin-arm64), const SYS_ADJTIME ideal-int
pkg syscall (darwin-arm64), const SYS_AIO_CANCEL = 316
pkg syscall (darwin-arm64), const SYS_AIO_CANCEL ideal-int
pkg syscall (darwin-arm64), const SYS_AIO_ERROR = 317
pkg syscall (darwin-arm64), const SYS_AIO_ERROR ideal-int
pkg syscall (darwin-arm64), const SYS_AIO_FSYNC = 313
pkg syscall (darwin-arm64), const SYS_AIO_FSYNC ideal-int
pkg syscall (darwin-arm64), const SYS_AIO_READ = 318
pkg syscall (darwin-arm64), const SYS_AIO_READ ideal-int
pkg syscall (darwin-arm64), const SYS_AIO_RETURN = 314
pkg syscall (darwin-arm64), const SYS_AIO_RETURN ideal-int
pkg syscall (darwin-arm64), const SYS_AIO_SUSPEND = 315
pkg syscall (darwin-arm64), const SYS_AIO_SUSPEND ideal-int
pkg syscall (darwin-arm64), const SYS_AIO_SUSPEND_NOCANCEL = 421
pkg syscall (darwin-arm64), const SYS_AIO_SUSPEND_NOCANCEL ideal-int
pkg syscall (darwin-arm64), const SYS_AIO_WRITE = 319
pkg syscall (darwin-arm64), const SYS_AIO_WRITE ideal-int
pkg syscall (darwin-arm64), const SYS_ATGETMSG = 207
pkg syscall (darwin-arm64), const SYS_ATGETMSG ideal-int
pkg syscall (darwin-arm64), const SYS_ATPGETREQ = 211
pkg syscall (darwin-arm64), const SYS_ATPGETREQ ideal-int
pkg syscall (darwin-arm64), const SYS_ATPGETRSP = 212
pkg syscall (darwin-arm64), const SYS_ATPGETRSP ideal-int
pkg syscall (darwin-arm64), const SYS_ATPSNDREQ = 209
pkg syscall (darwin-arm64), const SYS_ATPSNDREQ ideal-int
pkg syscall (darwin-arm64), const SYS_ATPSNDRSP = 210
pkg syscall (darwin-arm64), const SYS_ATPSNDRSP ideal-int
pkg syscall (darwin-arm64), const SYS_ATPUTMSG = 208
pkg syscall (darwin-arm64), const SYS_ATPUTMSG ideal-int
pkg syscall (darwin-arm64), const SYS_ATSOCKET = 206
pkg syscall (darwin-arm64), const SYS_ATSOCKET ideal-int
pkg syscall (darwin-arm64), const SYS_AUDIT = 350
pkg syscall (darwin-arm64), const SYS_AUDIT ideal-int
pkg syscall (darwin-arm64), const SYS_AUDITCTL = 359
pkg syscall (darwin-arm64), const SYS_AUDITCTL ideal-int
pkg syscall (darwin-arm64), const SYS_AUDITON = 351
pkg syscall (darwin-arm64), const SYS_AUDITON ideal-int
pkg syscall (darwin-arm64), const SYS_AUDIT_SESSION_JOIN = 429
pkg syscall (darwin-arm64), const SYS_AUDIT_SESSION_JOIN ideal-int
pkg syscall (darwin-arm64), const SYS_AUDIT_SESSION_PORT = 432
pkg syscall (darwin-arm64), const SYS_AUDIT_SESSION_PORT ideal-int
pkg syscall (darwin-arm64), const SYS_AUDIT_SESSION_SELF = 428
pkg syscall (darwin-arm64), const SYS_AUDIT_SESSION_SELF ideal-int
pkg syscall (darwin-arm64), const SYS_BIND = 104
pkg syscall (darwin-arm64), const SYS_BIND ideal-int
pkg syscall (darwin-arm64), const SYS_BSDTHREAD_CREATE = 360
pkg syscall (darwin-arm64), const SYS_BSDTHREAD_CREATE ideal-int
pkg syscall (darwin-arm64), const SYS_BSDTHREAD_REGISTER = 366
pkg syscall (darwin-arm64), const SYS_BSDTHREAD_REGISTER ideal-int
pkg syscall (darwin-arm64), const SYS_BSDTHREAD_TERMINATE = 361
pkg syscall (darwin-arm64), const SYS_BSDTHREAD_TERMINATE ideal-int
pkg syscall (darwin-arm64), const SYS_CHDIR = 12
pkg syscall (darwin-arm64), const SYS_CHDIR ideal-int
pkg syscall (darwin-arm64), const SYS_CHFLAGS = 34
pkg syscall (darwin-arm64), const SYS_CHFLAGS ideal-int
pkg syscall (darwin-arm64), const SYS_CHMOD = 15
pkg syscall (darwin-arm64), const SYS_CHMOD ideal-int
pkg syscall (darwin-arm64), const SYS_CHMOD_EXTENDED = 282
pkg syscall (darwin-arm64), const SYS_CHMOD_EXTENDED ideal-int
pkg syscall (darwin-arm64), const SYS_CHOWN = 16
pkg syscall (darwin-arm64), const SYS_CHOWN ideal-int
pkg syscall (darwin-arm64), const SYS_CHROOT = 61
pkg syscall (darwin-arm64), const SYS_CHROOT ideal-int
pkg syscall (darwin-arm64), const SYS_CHUD = 185
pkg syscall (darwin-arm64), const SYS_CHUD ideal-int
pkg syscall (darwin-arm64), const SYS_CLOSE = 6
pkg syscall (darwin-arm64), const SYS_CLOSE ideal-int
pkg syscall (darwin-arm64), const SYS_CLOSE_NOCANCEL = 399
pkg syscall (darwin-arm64), const SYS_CLOSE_NOCANCEL ideal-int
pkg syscall (darwin-arm64), const SYS_CONNECT = 98
pkg syscall (darwin-arm64), const SYS_CONNECT ideal-int
pkg syscall (darwin-arm64), const SYS_CONNECT_NOCANCEL = 409
pkg syscall (darwin-arm64), const SYS_CONNECT_NOCANCEL ideal-int
pkg syscall (darwin-arm64), const SYS_COPYFILE = 227
pkg syscall (darwin-arm64), const SYS_COPYFILE ideal-int
pkg syscall (darwin-arm64), const SYS_CSOPS = 169
pkg syscall (darwin-arm64), const SYS_CSOPS ideal-int
pkg syscall (darwin-arm64), const SYS_CSOPS_AUDITTOKEN = 170
pkg syscall (darwin-arm64), const SYS_CSOPS_AUDITTOKEN ideal-int
pkg syscall (darwin-arm64), const SYS_DELETE = 226
pkg syscall (darwin-arm64), const SYS_DELETE ideal-int
pkg syscall (darwin-arm64), const SYS_DUP = 41
pkg syscall (darwin-arm64), const SYS_DUP ideal-int
pkg syscall (darwin-arm64), const SYS_DUP2 = 90
pkg syscall (darwin-arm64), const SYS_DUP2 ideal-int
pkg syscall (darwin-arm64), const SYS_EXCHANGEDATA = 223
pkg syscall (darwin-arm64), const SYS_EXCHANGEDATA ideal-int
pkg syscall (darwin-arm64), const SYS_EXECVE = 59
pkg syscall (darwin-arm64), const SYS_EXECVE ideal-int
pkg syscall (darwin-arm64), const SYS_EXIT = 1
pkg syscall (darwin-arm64), const SYS_EXIT ideal-int
pkg syscall (darwin-arm64), const SYS_FCHDIR = 13
pkg syscall (darwin-arm64), const SYS_FCHDIR ideal-int
pkg syscall (darwin-arm64), const SYS_FCHFLAGS = 35
pkg syscall (darwin-arm64), const SYS_FCHFLAGS ideal-int
pkg syscall (darwin-arm64), const SYS_FCHMOD = 124
pkg syscall (darwin-arm64), const SYS_FCHMOD ideal-int
pkg syscall (darwin-arm64), const SYS_FCHMOD_EXTENDED = 283
pkg syscall (darwin-arm64), const SYS_FCHMOD_EXTENDED ideal-int
pkg syscall (darwin-arm64), const SYS_FCHOWN = 123
pkg syscall (darwin-arm64), const SYS_FCHOWN ideal-int
pkg syscall (darwin-arm64), const SYS_FCNTL = 92
pkg syscall (darwin-arm64), const SYS_FCNTL ideal-int
pkg syscall (darwin-arm64), const SYS_FCNTL_NOCANCEL = 406
pkg syscall (darwin-arm64), const SYS_FCNTL_NOCANCEL ideal-int
pkg syscall (darwin-arm64), const SYS_FDATASYNC = 187
pkg syscall (darwin-arm64), const SYS_FDATASYNC ideal-int
pkg syscall (darwin-arm64), const SYS_FFSCTL = 245
pkg syscall (darwin-arm64), const SYS_FFSCTL ideal-int
pkg syscall (darwin-arm64), const SYS_FGETATTRLIST = 228
pkg syscall (darwin-arm64), const SYS_FGETATTRLIST ideal-int
pkg syscall (darwin-arm64), const SYS_FGETXATTR = 235
pkg syscall (darwin-arm64), const SYS_FGETXATTR ideal-int
pkg syscall (darwin-arm64), const SYS_FHOPEN = 248
pkg syscall (darwin-arm64), const SYS_FHOPEN ideal-int
pkg syscall (darwin-arm64), const SYS_FILEPORT_MAKEFD = 431
pkg syscall (darwin-arm64), const SYS_FILEPORT_MAKEFD ideal-int
pkg syscall (darwin-arm64), const SYS_FILEPORT_MAKEPORT = 430
pkg syscall (darwin-arm64), const SYS_FILEPORT_MAKEPORT ideal-int
pkg syscall (darwin-arm64), const SYS_FLISTXATTR = 241
pkg syscall (darwin-arm64), const SYS_FLISTXATTR ideal-int
pkg syscall (darwin-arm64), const SYS_FLOCK = 131
pkg syscall (darwin-arm64), const SYS_FLOCK ideal-int
pkg syscall (darwin-arm64), const SYS_FORK = 2
pkg syscall (darwin-arm64), const SYS_FORK ideal-int
pkg syscall (darwin-arm64), const SYS_FPATHCONF = 192
pkg syscall (darwin-arm64), const SYS_FPATHCONF ideal-int
pkg syscall (darwin-arm64), const SYS_FREMOVEXATTR = 239
pkg syscall (darwin-arm64), const SYS_FREMOVEXATTR ideal-int
pkg syscall (darwin-arm64), const SYS_FSCTL = 242
pkg syscall (darwin-arm64), const SYS_FSCTL ideal-int
pkg syscall (darwin-arm64), const SYS_FSETATTRLIST = 229
pkg syscall (darwin-arm64), const SYS_FSETATTRLIST ideal-int
pkg syscall (darwin-arm64), const SYS_FSETXATTR = 237
pkg syscall (darwin-arm64), const SYS_FSETXATTR ideal-int
pkg syscall (darwin-arm64), const SYS_FSGETPATH = 427
pkg syscall (darwin-arm64), const SYS_FSGETPATH ideal-int
pkg syscall (darwin-arm64), const SYS_FSTAT = 189
pkg syscall (darwin-arm64), const SYS_FSTAT ideal-int
pkg syscall (darwin-arm64), const SYS_FSTAT64 = 339
pkg syscall (darwin-arm64), const SYS_FSTAT64 ideal-int
pkg syscall (darwin-arm64), const SYS_FSTAT64_EXTENDED = 343
pkg syscall (darwin-arm64), const SYS_FSTAT64_EXTENDED ideal-int
pkg syscall (darwin-arm64), const SYS_FSTATFS = 158
pkg syscall (darwin-arm64), const SYS_FSTATFS ideal-int
pkg syscall (darwin-arm64), const SYS_FSTATFS64 = 346
pkg syscall (darwin-arm64), const SYS_FSTATFS64 ideal-int
pkg syscall (darwin-arm64), const SYS_FSTAT_EXTENDED = 281
pkg syscall (darwin-arm64), const SYS_FSTAT_EXTENDED ideal-int
pkg syscall (darwin-arm64), const SYS_FSYNC = 95
pkg syscall (darwin-arm64), const SYS_FSYNC ideal-int
pkg syscall (darwin-arm64), const SYS_FSYNC_NOCANCEL = 408
pkg syscall (darwin-arm64), const SYS_FSYNC_NOCANCEL ideal-int
pkg syscall (darwin-arm64), const SYS_FTRUNCATE = 201
pkg syscall (darwin-arm64), const SYS_FTRUNCATE ideal-int
pkg syscall (darwin-arm64), const SYS_FUTIMES = 139
pkg syscall (darwin-arm64), const SYS_FUTIMES ideal-int
pkg syscall (darwin-arm64), const SYS_GETATTRLIST = 220
pkg syscall (darwin-arm64), const SYS_GETATTRLIST ideal-int
pkg syscall (darwin-arm64), const SYS_GETAUDIT_ADDR = 357
pkg syscall (darwin-arm64), const SYS_GETAUDIT_ADDR ideal-int
pkg syscall (darwin-arm64), const SYS_GETAUID = 353
pkg syscall (darwin-arm64), const SYS_GETAUID ideal-int
pkg syscall (darwin-arm64), const SYS_GETDIRENTRIES = 196
pkg syscall (darwin-arm64), const SYS_GETDIRENTRIES ideal-int
pkg syscall (darwin-arm64), const SYS_GETDIRENTRIES64 = 344
pkg syscall (darwin-arm64), const SYS_GETDIRENTRIES64 ideal-int
pkg syscall (darwin-arm64), const SYS_GETDIRENTRIESATTR = 222
pkg syscall (darwin-arm64), const SYS_GETDIRENTRIESATTR ideal-int
pkg syscall (darwin-arm64), const SYS_GETDTABLESIZE = 89
pkg syscall (darwin-arm64), const SYS_GETDTABLESIZE ideal-int
pkg syscall (darwin-arm64), const SYS_GETEGID = 43
pkg syscall (darwin-arm64), const SYS_GETEGID ideal-int
pkg syscall (darwin-arm64), const SYS_GETEUID = 25
pkg syscall (darwin-arm64), const SYS_GETEUID ideal-int
pkg syscall (darwin-arm64), const SYS_GETFH = 161
pkg syscall (darwin-arm64), const SYS_GETFH ideal-int
pkg syscall (darwin-arm64), const SYS_GETFSSTAT = 18
pkg syscall (darwin-arm64), const SYS_GETFSSTAT ideal-int
pkg syscall (darwin-arm64), const SYS_GETFSSTAT64 = 347
pkg syscall (darwin-arm64), const SYS_GETFSSTAT64 ideal-int
pkg syscall (darwin-arm64), const SYS_GETGID = 47
pkg syscall (darwin-arm64), const SYS_GETGID ideal-int
pkg syscall (darwin-arm64), const SYS_GETGROUPS = 79
pkg syscall (darwin-arm64), const SYS_GETGROUPS ideal-int
pkg syscall (darwin-arm64), const SYS_GETHOSTUUID = 142
pkg syscall (darwin-arm64), const SYS_GETHOSTUUID ideal-int
pkg syscall (darwin-arm64), const SYS_GETITIMER = 86
pkg syscall (darwin-arm64), const SYS_GETITIMER ideal-int
pkg syscall (darwin-arm64), const SYS_GETLCID = 395
pkg syscall (darwin-arm64), const SYS_GETLCID ideal-int
pkg syscall (darwin-arm64), const SYS_GETLOGIN = 49
pkg syscall (darwin-arm64), const SYS_GETLOGIN ideal-int
pkg syscall (darwin-arm64), const SYS_GETPEERNAME = 31
pkg syscall (darwin-arm64), const SYS_GETPEERNAME ideal-int
pkg syscall (darwin-arm64), const SYS_GETPGID = 151
pkg syscall (darwin-arm64), const SYS_GETPGID ideal-int
pkg syscall (darwin-arm64), const SYS_GETPGRP = 81
pkg syscall (darwin-arm64), const SYS_GETPGRP ideal-int
pkg syscall (darwin-arm64), const SYS_GETPID = 20
pkg syscall (darwin-arm64), const SYS_GETPID ideal-int
pkg syscall (darwin-arm64), const SYS_GETPPID = 39
pkg syscall (darwin-arm64), const SYS_GETPPID ideal-int
pkg syscall (darwin-arm64), const SYS_GETPRIORITY = 100
pkg syscall (darwin-arm64), const SYS_GETPRIORITY ideal-int
pkg syscall (darwin-arm64), const SYS_GETRLIMIT = 194
pkg syscall (darwin-arm64), const SYS_GETRLIMIT ideal-int
pkg syscall (darwin-arm64), const SYS_GETRUSAGE = 117
pkg syscall (darwin-arm64), const SYS_GETRUSAGE ideal-int
pkg syscall (darwin-arm64), const SYS_GETSGROUPS = 288
pkg syscall (darwin-arm64), const SYS_GETSGROUPS ideal-int
pkg syscall (darwin-arm64), const SYS_GETSID = 310
pkg syscall (darwin-arm64), const SYS_GETSID ideal-int
pkg syscall (darwin-arm64), const SYS_GETSOCKNAME = 32
pkg syscall (darwin-arm64), const SYS_GETSOCKNAME ideal-int
pkg syscall (darwin-arm64), const SYS_GETSOCKOPT = 118
pkg syscall (darwin-arm64), const SYS_GETSOCKOPT ideal-int
pkg syscall (darwin-arm64), const SYS_GETTID = 286
pkg syscall (darwin-arm64), const SYS_GETTID ideal-int
pkg syscall (darwin-arm64), const SYS_GETTIMEOFDAY = 116
pkg syscall (darwin-arm64), const SYS_GETTIMEOFDAY ideal-int
pkg syscall (darwin-arm64), const SYS_GETUID = 24
pkg syscall (darwin-arm64), const SYS_GETUID ideal-int
pkg syscall (darwin-arm64), const SYS_GETWGROUPS = 290
pkg syscall (darwin-arm64), const SYS_GETWGROUPS ideal-int
pkg syscall (darwin-arm64), const SYS_GETXATTR = 234
pkg syscall (darwin-arm64), const SYS_GETXATTR ideal-int
pkg syscall (darwin-arm64), const SYS_IDENTITYSVC = 293
pkg syscall (darwin-arm64), const SYS_IDENTITYSVC ideal-int
pkg syscall (darwin-arm64), const SYS_INITGROUPS = 243
pkg syscall (darwin-arm64), const SYS_INITGROUPS ideal-int
pkg syscall (darwin-arm64), const SYS_IOCTL = 54
pkg syscall (darwin-arm64), const SYS_IOCTL ideal-int
pkg syscall (darwin-arm64), const SYS_IOPOLICYSYS = 322
pkg syscall (darwin-arm64), const SYS_IOPOLICYSYS ideal-int
pkg syscall (darwin-arm64), const SYS_ISSETUGID = 327
pkg syscall (darwin-arm64), const SYS_ISSETUGID ideal-int
pkg syscall (darwin-arm64), const SYS_KAS_INFO = 439
pkg syscall (darwin-arm64), const SYS_KAS_INFO ideal-int
pkg syscall (darwin-arm64), const SYS_KDEBUG_TRACE = 180
pkg syscall (darwin-arm64), const SYS_KDEBUG_TRACE ideal-int
pkg syscall (darwin-arm64), const SYS_KEVENT = 363
pkg syscall (darwin-arm64), const SYS_KEVENT ideal-int
pkg syscall (darwin-arm64), const SYS_KEVENT64 = 369
pkg syscall (darwin-arm64), const SYS_KEVENT64 ideal-int
pkg syscall (darwin-arm64), const SYS_KILL = 37
pkg syscall (darwin-arm64), const SYS_KILL ideal-int
pkg syscall (darwin-arm64), const SYS_KQUEUE = 362
pkg syscall (darwin-arm64), const SYS_KQUEUE ideal-int
pkg syscall (darwin-arm64), const SYS_LCHOWN = 364
pkg syscall (darwin-arm64), const SYS_LCHOWN ideal-int
pkg syscall (darwin-arm64), const SYS_LEDGER = 373
pkg syscall (darwin-arm64), const SYS_LEDGER ideal-int
pkg syscall (darwin-arm64), const SYS_LINK = 9
pkg syscall (darwin-arm64), const SYS_LINK ideal-int
pkg syscall (darwin-arm64), const SYS_LIO_LISTIO = 320
pkg syscall (darwin-arm64), const SYS_LIO_LISTIO ideal-int
pkg syscall (darwin-arm64), const SYS_LISTEN = 106
pkg syscall (darwin-arm64), const SYS_LISTEN ideal-int
pkg syscall (darwin-arm64), const SYS_LISTXATTR = 240
pkg syscall (darwin-arm64), const SYS_LISTXATTR ideal-int
pkg syscall (darwin-arm64), const SYS_LSEEK = 199
pkg syscall (darwin-arm64), const SYS_LSEEK ideal-int
pkg syscall (darwin-arm64), const SYS_LSTAT = 190
pkg syscall (darwin-arm64), const SYS_LSTAT ideal-int
pkg syscall (darwin-arm64), const SYS_LSTAT64 = 340
pkg syscall (darwin-arm64), const SYS_LSTAT64 ideal-int
pkg syscall (darwin-arm64), const SYS_LSTAT64_EXTENDED = 342
pkg syscall (darwin-arm64), const SYS_LSTAT64_EXTENDED ideal-int
pkg syscall (darwin-arm64), const SYS_LSTAT_EXTENDED = 280
pkg syscall (darwin-arm64), const SYS_LSTAT_EXTENDED ideal-int
pkg syscall (darwin-arm64), const SYS_MADVISE = 75
pkg syscall (darwin-arm64), const SYS_MADVISE ideal-int
pkg syscall (darwin-arm64), const SYS_MAXSYSCALL = 440
pkg syscall (darwin-arm64), const SYS_MAXSYSCALL ideal-int
pkg syscall (darwin-arm64), const SYS_MINCORE = 78
pkg syscall (darwin-arm64), const SYS_MINCORE ideal-int
pkg syscall (darwin-arm64), const SYS_MINHERIT = 250
pkg syscall (darwin-arm64), const SYS_MINHERIT ideal-int
pkg syscall (darwin-arm64), const SYS_MKDIR = 136
pkg syscall (darwin-arm64), const SYS_MKDIR ideal-int
pkg syscall (darwin-arm64), const SYS_MKDIR_EXTENDED = 292
pkg syscall (darwin-arm64), const SYS_MKDIR_EXTENDED ideal-int
pkg syscall (darwin-arm64), const SYS_MKFIFO = 132
pkg syscall (darwin-arm64), const SYS_MKFIFO ideal-int
pkg syscall (darwin-arm64), const SYS_MKFIFO_EXTENDED = 291
pkg syscall (darwin-arm64), const SYS_MKFIFO_EXTENDED ideal-int
pkg syscall (darwin-arm64), const SYS_MKNOD = 14
pkg syscall (darwin-arm64), const SYS_MKNOD ideal-int
pkg syscall (darwin-arm64), const SYS_MLOCK = 203
pkg syscall (darwin-arm64), const SYS_MLOCK ideal-int
pkg syscall (darwin-arm64), const SYS_MLOCKALL = 324
pkg syscall (darwin-arm64), const SYS_MLOCKALL ideal-int
pkg syscall (darwin-arm64), const SYS_MMAP = 197
pkg syscall (darwin-arm64), const SYS_MMAP ideal-int
pkg syscall (darwin-arm64), const SYS_MODWATCH = 233
pkg syscall (darwin-arm64), const SYS_MODWATCH ideal-int
pkg syscall (darwin-arm64), const SYS_MOUNT = 167
pkg syscall (darwin-arm64), const SYS_MOUNT ideal-int
pkg syscall (darwin-arm64), const SYS_MPROTECT = 74
pkg syscall (darwin-arm64), const SYS_MPROTECT ideal-int
pkg syscall (darwin-arm64), const SYS_MSGCTL = 258
pkg syscall (darwin-arm64), const SYS_MSGCTL ideal-int
pkg syscall (darwin-arm64), const SYS_MSGGET = 259
pkg syscall (darwin-arm64), const SYS_MSGGET ideal-int
pkg syscall (darwin-arm64), const SYS_MSGRCV = 261
pkg syscall (darwin-arm64), const SYS_MSGRCV ideal-int
pkg syscall (darwin-arm64), const SYS_MSGRCV_NOCANCEL = 419
pkg syscall (darwin-arm64), const SYS_MSGRCV_NOCANCEL ideal-int
pkg syscall (darwin-arm64), const SYS_MSGSND = 260
pkg syscall (darwin-arm64), const SYS_MSGSND ideal-int
pkg syscall (darwin-arm64), const SYS_MSGSND_NOCANCEL = 418
pkg syscall (darwin-arm64), const SYS_MSGSND_NOCANCEL ideal-int
pkg syscall (darwin-arm64), const SYS_MSGSYS = 252
pkg syscall (darwin-arm64), const SYS_MSGSYS ideal-int
pkg syscall (darwin-arm64), const SYS_MSYNC = 65
pkg syscall (darwin-arm64), const SYS_MSYNC ideal-int
pkg syscall (darwin-arm64), const SYS_MSYNC_NOCANCEL = 405
pkg syscall (darwin-arm64), const SYS_MSYNC_NOCANCEL ideal-int
pkg syscall (darwin-arm64), const SYS_MUNLOCK = 204
pkg syscall (darwin-arm64), const SYS_MUNLOCK ideal-int
pkg syscall (darwin-arm64), const SYS_MUNLOCKALL = 325
pkg syscall (darwin-arm64), const SYS_MUNLOCKALL ideal-int
pkg syscall (darwin-arm64), const SYS_MUNMAP = 73
pkg syscall (darwin-arm64), const SYS_MUNMAP ideal-int
pkg syscall (darwin-arm64), const SYS_NFSCLNT = 247
pkg syscall (darwin-arm64), const SYS_NFSCLNT ideal-int
pkg syscall (darwin-arm64), const SYS_NFSSVC = 155
pkg syscall (darwin-arm64), const SYS_NFSSVC ideal-int
pkg syscall (darwin-arm64), const SYS_OPEN = 5
pkg syscall (darwin-arm64), const SYS_OPEN ideal-int
pkg syscall (darwin-arm64), const SYS_OPEN_DPROTECTED_NP = 216
pkg syscall (darwin-arm64), const SYS_OPEN_DPROTECTED_NP ideal-int
pkg syscall (darwin-arm64), const SYS_OPEN_EXTENDED = 277
pkg syscall (darwin-arm64), const SYS_OPEN_EXTENDED ideal-int
pkg syscall (darwin-arm64), const SYS_OPEN_NOCANCEL = 398
pkg syscall (darwin-arm64), const SYS_OPEN_NOCANCEL ideal-int
pkg syscall (darwin-arm64), const SYS_PATHCONF = 191
pkg syscall (darwin-arm64), const SYS_PATHCONF ideal-int
pkg syscall (darwin-arm64), const SYS_PID_HIBERNATE = 435
pkg syscall (darwin-arm64), const SYS_PID_HIBERNATE ideal-int
pkg syscall (darwin-arm64), const SYS_PID_RESUME = 434
pkg syscall (darwin-arm64), const SYS_PID_RESUME ideal-int
pkg syscall (darwin-arm64), const SYS_PID_SHUTDOWN_SOCKETS = 436
pkg syscall (darwin-arm64), const SYS_PID_SHUTDOWN_SOCKETS ideal-int
pkg syscall (darwin-arm64), const SYS_PID_SUSPEND = 433
pkg syscall (darwin-arm64), const SYS_PID_SUSPEND ideal-int
pkg syscall (darwin-arm64), const SYS_PIPE = 42
pkg syscall (darwin-arm64), const SYS_PIPE ideal-int
pkg syscall (darwin-arm64), const SYS_POLL = 230
pkg syscall (darwin-arm64), const SYS_POLL ideal-int
pkg syscall (darwin-arm64), const SYS_POLL_NOCANCEL = 417
pkg syscall (darwin-arm64), const SYS_POLL_NOCANCEL ideal-int
pkg syscall (darwin-arm64), const SYS_POSIX_SPAWN = 244
pkg syscall (darwin-arm64), const SYS_POSIX_SPAWN ideal-int
pkg syscall (darwin-arm64), const SYS_PREAD = 153
pkg syscall (darwin-arm64), const SYS_PREAD ideal-int
pkg syscall (darwin-arm64), const SYS_PREAD_NOCANCEL = 414
pkg syscall (darwin-arm64), const SYS_PREAD_NOCANCEL ideal-int
pkg syscall (darwin-arm64), const SYS_PROCESS_POLICY = 323
pkg syscall (darwin-arm64), const SYS_PROCESS_POLICY ideal-int
pkg syscall (darwin-arm64), const SYS_PROC_INFO = 336
pkg syscall (darwin-arm64), const SYS_PROC_INFO ideal-int
pkg syscall (darwin-arm64), const SYS_PSYNCH_CVBROAD = 303
pkg syscall (darwin-arm64), const SYS_PSYNCH_CVBROAD ideal-int
pkg syscall (darwin-arm64), const SYS_PSYNCH_CVCLRPREPOST = 312
pkg syscall (darwin-arm64), const SYS_PSYNCH_CVCLRPREPOST ideal-int
pkg syscall (darwin-arm64), const SYS_PSYNCH_CVSIGNAL = 304
pkg syscall (darwin-arm64), const SYS_PSYNCH_CVSIGNAL ideal-int
pkg syscall (darwin-arm64), const SYS_PSYNCH_CVWAIT = 305
pkg syscall (darwin-arm64), const SYS_PSYNCH_CVWAIT ideal-int
pkg syscall (darwin-arm64), const SYS_PSYNCH_MUTEXDROP = 302
pkg syscall (darwin-arm64), const SYS_PSYNCH_MUTEXDROP ideal-int
pkg syscall (darwin-arm64), const SYS_PSYNCH_MUTEXWAIT = 301
pkg syscall (darwin-arm64), const SYS_PSYNCH_MUTEXWAIT ideal-int
pkg syscall (darwin-arm64), const SYS_PSYNCH_RW_DOWNGRADE = 299
pkg syscall (darwin-arm64), const SYS_PSYNCH_RW_DOWNGRADE ideal-int
pkg syscall (darwin-arm64), const SYS_PSYNCH_RW_LONGRDLOCK = 297
pkg syscall (darwin-arm64), const SYS_PSYNCH_RW_LONGRDLOCK ideal-int
pkg syscall (darwin-arm64), const SYS_PSYNCH_RW_RDLOCK = 306
pkg syscall (darwin-arm64), const SYS_PSYNCH_RW_RDLOCK ideal-int
pkg syscall (darwin-arm64), const SYS_PSYNCH_RW_UNLOCK = 308
pkg syscall (darwin-arm64), const SYS_PSYNCH_RW_UNLOCK ideal-int
pkg syscall (darwin-arm64), const SYS_PSYNCH_RW_UNLOCK2 = 309
pkg syscall (darwin-arm64), const SYS_PSYNCH_RW_UNLOCK2 ideal-int
pkg syscall (darwin-arm64), const SYS_PSYNCH_RW_UPGRADE = 300
pkg syscall (darwin-arm64), const SYS_PSYNCH_RW_UPGRADE ideal-int
pkg syscall (darwin-arm64), const SYS_PSYNCH_RW_WRLOCK = 307
pkg syscall (darwin-arm64), const SYS_PSYNCH_RW_WRLOCK ideal-int
pkg syscall (darwin-arm64), const SYS_PSYNCH_RW_YIELDWRLOCK = 298
pkg syscall (darwin-arm64), const SYS_PSYNCH_RW_YIELDWRLOCK ideal-int
pkg syscall (darwin-arm64), const SYS_PTRACE = 26
pkg syscall (darwin-arm64), const SYS_PTRACE ideal-int
pkg syscall (darwin-arm64), const SYS_PWRITE = 154
pkg syscall (darwin-arm64), const SYS_PWRITE ideal-int
pkg syscall (darwin-arm64), const SYS_PWRITE_NOCANCEL = 415
pkg syscall (darwin-arm64), const SYS_PWRITE_NOCANCEL ideal-int
pkg syscall (darwin-arm64), const SYS_QUOTACTL = 165
pkg syscall (darwin-arm64), const SYS_QUOTACTL ideal-int
pkg syscall (darwin-arm64), const SYS_READ = 3
pkg syscall (darwin-arm64), const SYS_READ ideal-int
pkg syscall (darwin-arm64), const SYS_READLINK = 58
pkg syscall (darwin-arm64), const SYS_READLINK ideal-int
pkg syscall (darwin-arm64), const SYS_READV = 120
pkg syscall (darwin-arm64), const SYS_READV ideal-int
pkg syscall (darwin-arm64), const SYS_READV_NOCANCEL = 411
pkg syscall (darwin-arm64), const SYS_READV_NOCANCEL ideal-int
pkg syscall (darwin-arm64), const SYS_READ_NOCANCEL = 396
pkg syscall (darwin-arm64), const SYS_READ_NOCANCEL ideal-int
pkg syscall (darwin-arm64), const SYS_REBOOT = 55
pkg syscall (darwin-arm64), const SYS_REBOOT ideal-int
pkg syscall (darwin-arm64), const SYS_RECVFROM = 29
pkg syscall (darwin-arm64), const SYS_RECVFROM ideal-int
pkg syscall (darwin-arm64), const SYS_RECVFROM_NOCANCEL = 403
pkg syscall (darwin-arm64), const SYS_RECVFROM_NOCANCEL ideal-int
pkg syscall (darwin-arm64), const SYS_RECVMSG = 27
pkg syscall (darwin-arm64), const SYS_RECVMSG ideal-int
pkg syscall (darwin-arm64), const SYS_RECVMSG_NOCANCEL = 401
pkg syscall (darwin-arm64), const SYS_RECVMSG_NOCANCEL ideal-int
pkg syscall (darwin-arm64), const SYS_REMOVEXATTR = 238
pkg syscall (darwin-arm64), const SYS_REMOVEXATTR ideal-int
pkg syscall (darwin-arm64), const SYS_RENAME = 128
pkg syscall (darwin-arm64), const SYS_RENAME ideal-int
pkg syscall (darwin-arm64), const SYS_REVOKE = 56
pkg syscall (darwin-arm64), const SYS_REVOKE ideal-int
pkg syscall (darwin-arm64), const SYS_RMDIR = 137
pkg syscall (darwin-arm64), const SYS_RMDIR ideal-int
pkg syscall (darwin-arm64), const SYS_SEARCHFS = 225
pkg syscall (darwin-arm64), const SYS_SEARCHFS ideal-int
pkg syscall (darwin-arm64), const SYS_SELECT = 93
pkg syscall (darwin-arm64), const SYS_SELECT ideal-int
pkg syscall (darwin-arm64), const SYS_SELECT_NOCANCEL = 407
pkg syscall (darwin-arm64), const SYS_SELECT_NOCANCEL ideal-int
pkg syscall (darwin-arm64), const SYS_SEMCTL = 254
pkg syscall (darwin-arm64), const SYS_SEMCTL ideal-int
pkg syscall (darwin-arm64), const SYS_SEMGET = 255
pkg syscall (darwin-arm64), const SYS_SEMGET ideal-int
pkg syscall (darwin-arm64), const SYS_SEMOP = 256
pkg syscall (darwin-arm64), const SYS_SEMOP ideal-int
pkg syscall (darwin-arm64), const SYS_SEMSYS = 251
pkg syscall (darwin-arm64), const SYS_SEMSYS ideal-int
pkg syscall (darwin-arm64), const SYS_SEM_CLOSE = 269
pkg syscall (darwin-arm64), const SYS_SEM_CLOSE ideal-int
pkg syscall (darwin-arm64), const SYS_SEM_DESTROY = 276
pkg syscall (darwin-arm64), const SYS_SEM_DESTROY ideal-int
pkg syscall (darwin-arm64), const SYS_SEM_GETVALUE = 274
pkg syscall (darwin-arm64), const SYS_SEM_GETVALUE ideal-int
pkg syscall (darwin-arm64), const SYS_SEM_INIT = 275
pkg syscall (darwin-arm64), const SYS_SEM_INIT ideal-int
pkg syscall (darwin-arm64), const SYS_SEM_OPEN = 268
pkg syscall (darwin-arm64), const SYS_SEM_OPEN ideal-int
pkg syscall (darwin-arm64), const SYS_SEM_POST = 273
pkg syscall (darwin-arm64), const SYS_SEM_POST ideal-int
pkg syscall (darwin-arm64), const SYS_SEM_TRYWAIT = 272
pkg syscall (darwin-arm64), const SYS_SEM_TRYWAIT ideal-int
pkg syscall (darwin-arm64), const SYS_SEM_UNLINK = 270
pkg syscall (darwin-arm64), const SYS_SEM_UNLINK ideal-int
pkg syscall (darwin-arm64), const SYS_SEM_WAIT = 271
pkg syscall (darwin-arm64), const SYS_SEM_WAIT ideal-int
pkg syscall (darwin-arm64), const SYS_SEM_WAIT_NOCANCEL = 420
pkg syscall (darwin-arm64), const SYS_SEM_WAIT_NOCANCEL ideal-int
pkg syscall (darwin-arm64), const SYS_SENDFILE = 337
pkg syscall (darwin-arm64), const SYS_SENDFILE ideal-int
pkg syscall (darwin-arm64), const SYS_SENDMSG = 28
pkg syscall (darwin-arm64), const SYS_SENDMSG ideal-int
pkg syscall (darwin-arm64), const SYS_SENDMSG_NOCANCEL = 402
pkg syscall (darwin-arm64), const SYS_SENDMSG_NOCANCEL ideal-int
pkg syscall (darwin-arm64), const SYS_SENDTO = 133
pkg syscall (darwin-arm64), const SYS_SENDTO ideal-int
pkg syscall (darwin-arm64), const SYS_SENDTO_NOCANCEL = 413
pkg syscall (darwin-arm64), const SYS_SENDTO_NOCANCEL ideal-int
pkg syscall (darwin-arm64), const SYS_SETATTRLIST = 221
pkg syscall (darwin-arm64), const SYS_SETATTRLIST ideal-int
pkg syscall (darwin-arm64), const SYS_SETAUDIT_ADDR = 358
pkg syscall (darwin-arm64), const SYS_SETAUDIT_ADDR ideal-int
pkg syscall (darwin-arm64), const SYS_SETAUID = 354
pkg syscall (darwin-arm64), const SYS_SETAUID ideal-int
pkg syscall (darwin-arm64), const SYS_SETEGID = 182
pkg syscall (darwin-arm64), const SYS_SETEGID ideal-int
pkg syscall (darwin-arm64), const SYS_SETEUID = 183
pkg syscall (darwin-arm64), const SYS_SETEUID ideal-int
pkg syscall (darwin-arm64), const SYS_SETGID = 181
pkg syscall (darwin-arm64), const SYS_SETGID ideal-int
pkg syscall (darwin-arm64), const SYS_SETGROUPS = 80
pkg syscall (darwin-arm64), const SYS_SETGROUPS ideal-int
pkg syscall (darwin-arm64), const SYS_SETITIMER = 83
pkg syscall (darwin-arm64), const SYS_SETITIMER ideal-int
pkg syscall (darwin-arm64), const SYS_SETLCID = 394
pkg syscall (darwin-arm64), const SYS_SETLCID ideal-int
pkg syscall (darwin-arm64), const SYS_SETLOGIN = 50
pkg syscall (darwin-arm64), const SYS_SETLOGIN ideal-int
pkg syscall (darwin-arm64), const SYS_SETPGID = 82
pkg syscall (darwin-arm64), const SYS_SETPGID ideal-int
pkg syscall (darwin-arm64), const SYS_SETPRIORITY = 96
pkg syscall (darwin-arm64), const SYS_SETPRIORITY ideal-int
pkg syscall (darwin-arm64), const SYS_SETPRIVEXEC = 152
pkg syscall (darwin-arm64), const SYS_SETPRIVEXEC ideal-int
pkg syscall (darwin-arm64), const SYS_SETREGID = 127
pkg syscall (darwin-arm64), const SYS_SETREGID ideal-int
pkg syscall (darwin-arm64), const SYS_SETREUID = 126
pkg syscall (darwin-arm64), const SYS_SETREUID ideal-int
pkg syscall (darwin-arm64), const SYS_SETRLIMIT = 195
pkg syscall (darwin-arm64), const SYS_SETRLIMIT ideal-int
pkg syscall (darwin-arm64), const SYS_SETSGROUPS = 287
pkg syscall (darwin-arm64), const SYS_SETSGROUPS ideal-int
pkg syscall (darwin-arm64), const SYS_SETSID = 147
pkg syscall (darwin-arm64), const SYS_SETSID ideal-int
pkg syscall (darwin-arm64), const SYS_SETSOCKOPT = 105
pkg syscall (darwin-arm64), const SYS_SETSOCKOPT ideal-int
pkg syscall (darwin-arm64), const SYS_SETTID = 285
pkg syscall (darwin-arm64), const SYS_SETTID ideal-int
pkg syscall (darwin-arm64), const SYS_SETTID_WITH_PID = 311
pkg syscall (darwin-arm64), const SYS_SETTID_WITH_PID ideal-int
pkg syscall (darwin-arm64), const SYS_SETTIMEOFDAY = 122
pkg syscall (darwin-arm64), const SYS_SETTIMEOFDAY ideal-int
pkg syscall (darwin-arm64), const SYS_SETUID = 23
pkg syscall (darwin-arm64), const SYS_SETUID ideal-int
pkg syscall (darwin-arm64), const SYS_SETWGROUPS = 289
pkg syscall (darwin-arm64), const SYS_SETWGROUPS ideal-int
pkg syscall (darwin-arm64), const SYS_SETXATTR = 236
pkg syscall (darwin-arm64), const SYS_SETXATTR ideal-int
pkg syscall (darwin-arm64), const SYS_SHARED_REGION_CHECK_NP = 294
pkg syscall (darwin-arm64), const SYS_SHARED_REGION_CHECK_NP ideal-int
pkg syscall (darwin-arm64), const SYS_SHARED_REGION_MAP_AND_SLIDE_NP = 438
pkg syscall (darwin-arm64), const SYS_SHARED_REGION_MAP_AND_SLIDE_NP ideal-int
pkg syscall (darwin-arm64), const SYS_SHMAT = 262
pkg syscall (darwin-arm64), const SYS_SHMAT ideal-int
pkg syscall (darwin-arm64), const SYS_SHMCTL = 263
pkg syscall (darwin-arm64), const SYS_SHMCTL ideal-int
pkg syscall (darwin-arm64), const SYS_SHMDT = 264
pkg syscall (darwin-arm64), const SYS_SHMDT ideal-int
pkg syscall (darwin-arm64), const SYS_SHMGET = 265
pkg syscall (darwin-arm64), const SYS_SHMGET ideal-int
pkg syscall (darwin-arm64), const SYS_SHMSYS = 253
pkg syscall (darwin-arm64), const SYS_SHMSYS ideal-int
pkg syscall (darwin-arm64), const SYS_SHM_OPEN = 266
pkg syscall (darwin-arm64), const SYS_SHM_OPEN ideal-int
pkg syscall (darwin-arm64), const SYS_SHM_UNLINK = 267
pkg syscall (darwin-arm64), const SYS_SHM_UNLINK ideal-int
pkg syscall (darwin-arm64), const SYS_SHUTDOWN = 134
pkg syscall (darwin-arm64), const SYS_SHUTDOWN ideal-int
pkg syscall (darwin-arm64), const SYS_SIGACTION = 46
pkg syscall (darwin-arm64), const SYS_SIGACTION ideal-int
pkg syscall (darwin-arm64), const SYS_SIGALTSTACK = 53
pkg syscall (darwin-arm64), const SYS_SIGALTSTACK ideal-int
pkg syscall (darwin-arm64), const SYS_SIGPENDING = 52
pkg syscall (darwin-arm64), const SYS_SIGPENDING ideal-int
pkg syscall (darwin-arm64), const SYS_SIGPROCMASK = 48
pkg syscall (darwin-arm64), const SYS_SIGPROCMASK ideal-int
pkg syscall (darwin-arm64), const SYS_SIGRETURN = 184
pkg syscall (darwin-arm64), const SYS_SIGRETURN ideal-int
pkg syscall (darwin-arm64), const SYS_SIGSUSPEND = 111
pkg syscall (darwin-arm64), const SYS_SIGSUSPEND ideal-int
pkg syscall (darwin-arm64), const SYS_SIGSUSPEND_NOCANCEL = 410
pkg syscall (darwin-arm64), const SYS_SIGSUSPEND_NOCANCEL ideal-int
pkg syscall (darwin-arm64), const SYS_SOCKET = 97
pkg syscall (darwin-arm64), const SYS_SOCKET ideal-int
pkg syscall (darwin-arm64), const SYS_SOCKETPAIR = 135
pkg syscall (darwin-arm64), const SYS_SOCKETPAIR ideal-int
pkg syscall (darwin-arm64), const SYS_STACK_SNAPSHOT = 365
pkg syscall (darwin-arm64), const SYS_STACK_SNAPSHOT ideal-int
pkg syscall (darwin-arm64), const SYS_STAT = 188
pkg syscall (darwin-arm64), const SYS_STAT ideal-int
pkg syscall (darwin-arm64), const SYS_STAT64 = 338
pkg syscall (darwin-arm64), const SYS_STAT64 ideal-int
pkg syscall (darwin-arm64), const SYS_STAT64_EXTENDED = 341
pkg syscall (darwin-arm64), const SYS_STAT64_EXTENDED ideal-int
pkg syscall (darwin-arm64), const SYS_STATFS = 157
pkg syscall (darwin-arm64), const SYS_STATFS ideal-int
pkg syscall (darwin-arm64), const SYS_STATFS64 = 345
pkg syscall (darwin-arm64), const SYS_STATFS64 ideal-int
pkg syscall (darwin-arm64), const SYS_STAT_EXTENDED = 279
pkg syscall (darwin-arm64), const SYS_STAT_EXTENDED ideal-int
pkg syscall (darwin-arm64), const SYS_SWAPON = 85
pkg syscall (darwin-arm64), const SYS_SWAPON ideal-int
pkg syscall (darwin-arm64), const SYS_SYMLINK = 57
pkg syscall (darwin-arm64), const SYS_SYMLINK ideal-int
pkg syscall (darwin-arm64), const SYS_SYNC = 36
pkg syscall (darwin-arm64), const SYS_SYNC ideal-int
pkg syscall (darwin-arm64), const SYS_SYSCALL = 0
pkg syscall (darwin-arm64), const SYS_SYSCALL ideal-int
pkg syscall (darwin-arm64), const SYS_THREAD_SELFID = 372
pkg syscall (darwin-arm64), const SYS_THREAD_SELFID ideal-int
pkg syscall (darwin-arm64), const SYS_TRUNCATE = 200
pkg syscall (darwin-arm64), const SYS_TRUNCATE ideal-int
pkg syscall (darwin-arm64), const SYS_UMASK = 60
pkg syscall (darwin-arm64), const SYS_UMASK ideal-int
pkg syscall (darwin-arm64), const SYS_UMASK_EXTENDED = 278
pkg syscall (darwin-arm64), const SYS_UMASK_EXTENDED ideal-int
pkg syscall (darwin-arm64), const SYS_UNDELETE = 205
pkg syscall (darwin-arm64), const SYS_UNDELETE ideal-int
pkg syscall (darwin-arm64), const SYS_UNLINK = 10
pkg syscall (darwin-arm64), const SYS_UNLINK ideal-int
pkg syscall (darwin-arm64), const SYS_UNMOUNT = 159
pkg syscall (darwin-arm64), const SYS_UNMOUNT ideal-int
pkg syscall (darwin-arm64), const SYS_UTIMES = 138
pkg syscall (darwin-arm64), const SYS_UTIMES ideal-int
pkg syscall (darwin-arm64), const SYS_VFORK = 66
pkg syscall (darwin-arm64), const SYS_VFORK ideal-int
pkg syscall (darwin-arm64), const SYS_VM_PRESSURE_MONITOR = 296
pkg syscall (darwin-arm64), const SYS_VM_PRESSURE_MONITOR ideal-int
pkg syscall (darwin-arm64), const SYS_WAIT4 = 7
pkg syscall (darwin-arm64), const SYS_WAIT4 ideal-int
pkg syscall (darwin-arm64), const SYS_WAIT4_NOCANCEL = 400
pkg syscall (darwin-arm64), const SYS_WAIT4_NOCANCEL ideal-int
pkg syscall (darwin-arm64), const SYS_WAITEVENT = 232
pkg syscall (darwin-arm64), const SYS_WAITEVENT ideal-int
pkg syscall (darwin-arm64), const SYS_WAITID = 173
pkg syscall (darwin-arm64), const SYS_WAITID ideal-int
pkg syscall (darwin-arm64), const SYS_WAITID_NOCANCEL = 416
pkg syscall (darwin-arm64), const SYS_WAITID_NOCANCEL ideal-int
pkg syscall (darwin-arm64), const SYS_WATCHEVENT = 231
pkg syscall (darwin-arm64), const SYS_WATCHEVENT ideal-int
pkg syscall (darwin-arm64), const SYS_WORKQ_KERNRETURN = 368
pkg syscall (darwin-arm64), const SYS_WORKQ_KERNRETURN ideal-int
pkg syscall (darwin-arm64), const SYS_WORKQ_OPEN = 367
pkg syscall (darwin-arm64), const SYS_WORKQ_OPEN ideal-int
pkg syscall (darwin-arm64), const SYS_WRITE = 4
pkg syscall (darwin-arm64), const SYS_WRITE ideal-int
pkg syscall (darwin-arm64), const SYS_WRITEV = 121
pkg syscall (darwin-arm64), const SYS_WRITEV ideal-int
pkg syscall (darwin-arm64), const SYS_WRITEV_NOCANCEL = 412
pkg syscall (darwin-arm64), const SYS_WRITEV_NOCANCEL ideal-int
pkg syscall (darwin-arm64), const SYS_WRITE_NOCANCEL = 397
pkg syscall (darwin-arm64), const SYS_WRITE_NOCANCEL ideal-int
pkg syscall (darwin-arm64), const SYS___DISABLE_THREADSIGNAL = 331
pkg syscall (darwin-arm64), const SYS___DISABLE_THREADSIGNAL ideal-int
pkg syscall (darwin-arm64), const SYS___MAC_EXECVE = 380
pkg syscall (darwin-arm64), const SYS___MAC_EXECVE ideal-int
pkg syscall (darwin-arm64), const SYS___MAC_GETFSSTAT = 426
pkg syscall (darwin-arm64), const SYS___MAC_GETFSSTAT ideal-int
pkg syscall (darwin-arm64), const SYS___MAC_GET_FD = 388
pkg syscall (darwin-arm64), const SYS___MAC_GET_FD ideal-int
pkg syscall (darwin-arm64), const SYS___MAC_GET_FILE = 382
pkg syscall (darwin-arm64), const SYS___MAC_GET_FILE ideal-int
pkg syscall (darwin-arm64), const SYS___MAC_GET_LCID = 391
pkg syscall (darwin-arm64), const SYS___MAC_GET_LCID ideal-int
pkg syscall (darwin-arm64), const SYS___MAC_GET_LCTX = 392
pkg syscall (darwin-arm64), const SYS___MAC_GET_LCTX ideal-int
pkg syscall (darwin-arm64), const SYS___MAC_GET_LINK = 384
pkg syscall (darwin-arm64), const SYS___MAC_GET_LINK ideal-int
pkg syscall (darwin-arm64), const SYS___MAC_GET_MOUNT = 425
pkg syscall (darwin-arm64), const SYS___MAC_GET_MOUNT ideal-int
pkg syscall (darwin-arm64), const SYS___MAC_GET_PID = 390
pkg syscall (darwin-arm64), const SYS___MAC_GET_PID ideal-int
pkg syscall (darwin-arm64), const SYS___MAC_GET_PROC = 386
pkg syscall (darwin-arm64), const SYS___MAC_GET_PROC ideal-int
pkg syscall (darwin-arm64), const SYS___MAC_MOUNT = 424
pkg syscall (darwin-arm64), const SYS___MAC_MOUNT ideal-int
pkg syscall (darwin-arm64), const SYS___MAC_SET_FD = 389
pkg syscall (darwin-arm64), const SYS___MAC_SET_FD ideal-int
pkg syscall (darwin-arm64), const SYS___MAC_SET_FILE = 383
pkg syscall (darwin-arm64), const SYS___MAC_SET_FILE ideal-int
pkg syscall (darwin-arm64), const SYS___MAC_SET_LCTX = 393
pkg syscall (darwin-arm64), const SYS___MAC_SET_LCTX ideal-int
pkg syscall (darwin-arm64), const SYS___MAC_SET_LINK = 385
pkg syscall (darwin-arm64), const SYS___MAC_SET_LINK ideal-int
pkg syscall (darwin-arm64), const SYS___MAC_SET_PROC = 387
pkg syscall (darwin-arm64), const SYS___MAC_SET_PROC ideal-int
pkg syscall (darwin-arm64), const SYS___MAC_SYSCALL = 381
pkg syscall (darwin-arm64), const SYS___MAC_SYSCALL ideal-int
pkg syscall (darwin-arm64), const SYS___OLD_SEMWAIT_SIGNAL = 370
pkg syscall (darwin-arm64), const SYS___OLD_SEMWAIT_SIGNAL ideal-int
pkg syscall (darwin-arm64), const SYS___OLD_SEMWAIT_SIGNAL_NOCANCEL = 371
pkg syscall (darwin-arm64), const SYS___OLD_SEMWAIT_SIGNAL_NOCANCEL ideal-int
pkg syscall (darwin-arm64), const SYS___PTHREAD_CANCELED = 333
pkg syscall (darwin-arm64), const SYS___PTHREAD_CANCELED ideal-int
pkg syscall (darwin-arm64), const SYS___PTHREAD_CHDIR = 348
pkg syscall (darwin-arm64), const SYS___PTHREAD_CHDIR ideal-int
pkg syscall (darwin-arm64), const SYS___PTHREAD_FCHDIR = 349
pkg syscall (darwin-arm64), const SYS___PTHREAD_FCHDIR ideal-int
pkg syscall (darwin-arm64), const SYS___PTHREAD_KILL = 328
pkg syscall (darwin-arm64), const SYS___PTHREAD_KILL ideal-int
pkg syscall (darwin-arm64), const SYS___PTHREAD_MARKCANCEL = 332
pkg syscall (darwin-arm64), const SYS___PTHREAD_MARKCANCEL ideal-int
pkg syscall (darwin-arm64), const SYS___PTHREAD_SIGMASK = 329
pkg syscall (darwin-arm64), const SYS___PTHREAD_SIGMASK ideal-int
pkg syscall (darwin-arm64), const SYS___SEMWAIT_SIGNAL = 334
pkg syscall (darwin-arm64), const SYS___SEMWAIT_SIGNAL ideal-int
pkg syscall (darwin-arm64), const SYS___SEMWAIT_SIGNAL_NOCANCEL = 423
pkg syscall (darwin-arm64), const SYS___SEMWAIT_SIGNAL_NOCANCEL ideal-int
pkg syscall (darwin-arm64), const SYS___SIGWAIT = 330
pkg syscall (darwin-arm64), const SYS___SIGWAIT ideal-int
pkg syscall (darwin-arm64), const SYS___SIGWAIT_NOCANCEL = 422
pkg syscall (darwin-arm64), const SYS___SIGWAIT_NOCANCEL ideal-int
pkg syscall (darwin-arm64), const SYS___SYSCTL = 202
pkg syscall (darwin-arm64), const SYS___SYSCTL ideal-int
pkg syscall (darwin-arm64), const S_IEXEC = 64
pkg syscall (darwin-arm64), const S_IEXEC ideal-int
pkg syscall (darwin-arm64), const S_IFMT = 61440
pkg syscall (darwin-arm64), const S_IFWHT = 57344
pkg syscall (darwin-arm64), const S_IFWHT ideal-int
pkg syscall (darwin-arm64), const S_IREAD = 256
pkg syscall (darwin-arm64), const S_IREAD ideal-int
pkg syscall (darwin-arm64), const S_IRGRP = 32
pkg syscall (darwin-arm64), const S_IRGRP ideal-int
pkg syscall (darwin-arm64), const S_IROTH = 4
pkg syscall (darwin-arm64), const S_IROTH ideal-int
pkg syscall (darwin-arm64), const S_IRWXG = 56
pkg syscall (darwin-arm64), const S_IRWXG ideal-int
pkg syscall (darwin-arm64), const S_IRWXO = 7
pkg syscall (darwin-arm64), const S_IRWXO ideal-int
pkg syscall (darwin-arm64), const S_IRWXU = 448
pkg syscall (darwin-arm64), const S_IRWXU ideal-int
pkg syscall (darwin-arm64), const S_ISTXT = 512
pkg syscall (darwin-arm64), const S_ISTXT ideal-int
pkg syscall (darwin-arm64), const S_IWGRP = 16
pkg syscall (darwin-arm64), const S_IWGRP ideal-int
pkg syscall (darwin-arm64), const S_IWOTH = 2
pkg syscall (darwin-arm64), const S_IWOTH ideal-int
pkg syscall (darwin-arm64), const S_IWRITE = 128
pkg syscall (darwin-arm64), const S_IWRITE ideal-int
pkg syscall (darwin-arm64), const S_IXGRP = 8
pkg syscall (darwin-arm64), const S_IXGRP ideal-int
pkg syscall (darwin-arm64), const S_IXOTH = 1
pkg syscall (darwin-arm64), const S_IXOTH ideal-int
pkg syscall (darwin-arm64), const SizeofBpfHdr = 20
pkg syscall (darwin-arm64), const SizeofBpfHdr ideal-int
pkg syscall (darwin-arm64), const SizeofBpfInsn = 8
pkg syscall (darwin-arm64), const SizeofBpfInsn ideal-int
pkg syscall (darwin-arm64), const SizeofBpfProgram = 16
pkg syscall (darwin-arm64), const SizeofBpfProgram ideal-int
pkg syscall (darwin-arm64), const SizeofBpfStat = 8
pkg syscall (darwin-arm64), const SizeofBpfStat ideal-int
pkg syscall (darwin-arm64), const SizeofBpfVersion = 4
pkg syscall (darwin-arm64), const SizeofBpfVersion ideal-int
pkg syscall (darwin-arm64), const SizeofCmsghdr = 12
pkg syscall (darwin-arm64), const SizeofCmsghdr ideal-int
pkg syscall (darwin-arm64), const SizeofICMPv6Filter = 32
pkg syscall (darwin-arm64), const SizeofICMPv6Filter ideal-int
pkg syscall (darwin-arm64), const SizeofIPMreq = 8
pkg syscall (darwin-arm64), const SizeofIPMreq ideal-int
pkg syscall (darwin-arm64), const SizeofIPv6MTUInfo = 32
pkg syscall (darwin-arm64), const SizeofIPv6MTUInfo ideal-int
pkg syscall (darwin-arm64), const SizeofIPv6Mreq = 20
pkg syscall (darwin-arm64), const SizeofIPv6Mreq ideal-int
pkg syscall (darwin-arm64), const SizeofIfData = 96
pkg syscall (darwin-arm64), const SizeofIfData ideal-int
pkg syscall (darwin-arm64), const SizeofIfMsghdr = 112
pkg syscall (darwin-arm64), const SizeofIfMsghdr ideal-int
pkg syscall (darwin-arm64), const SizeofIfaMsghdr = 20
pkg syscall (darwin-arm64), const SizeofIfaMsghdr ideal-int
pkg syscall (darwin-arm64), const SizeofIfmaMsghdr = 16
pkg syscall (darwin-arm64), const SizeofIfmaMsghdr ideal-int
pkg syscall (darwin-arm64), const SizeofIfmaMsghdr2 = 20
pkg syscall (darwin-arm64), const SizeofIfmaMsghdr2 ideal-int
pkg syscall (darwin-arm64), const SizeofInet4Pktinfo = 12
pkg syscall (darwin-arm64), const SizeofInet4Pktinfo ideal-int
pkg syscall (darwin-arm64), const SizeofInet6Pktinfo = 20
pkg syscall (darwin-arm64), const SizeofInet6Pktinfo ideal-int
pkg syscall (darwin-arm64), const SizeofLinger = 8
pkg syscall (darwin-arm64), const SizeofLinger ideal-int
pkg syscall (darwin-arm64), const SizeofMsghdr = 48
pkg syscall (darwin-arm64), const SizeofMsghdr ideal-int
pkg syscall (darwin-arm64), const SizeofRtMetrics = 56
pkg syscall (darwin-arm64), const SizeofRtMetrics ideal-int
pkg syscall (darwin-arm64), const SizeofRtMsghdr = 92
pkg syscall (darwin-arm64), const SizeofRtMsghdr ideal-int
pkg syscall (darwin-arm64), const SizeofSockaddrAny = 108
pkg syscall (darwin-arm64), const SizeofSockaddrAny ideal-int
pkg syscall (darwin-arm64), const SizeofSockaddrDatalink = 20
pkg syscall (darwin-arm64), const SizeofSockaddrDatalink ideal-int
pkg syscall (darwin-arm64), const SizeofSockaddrInet4 = 16
pkg syscall (darwin-arm64), const SizeofSockaddrInet4 ideal-int
pkg syscall (darwin-arm64), const SizeofSockaddrInet6 = 28
pkg syscall (darwin-arm64), const SizeofSockaddrInet6 ideal-int
pkg syscall (darwin-arm64), const SizeofSockaddrUnix = 106
pkg syscall (darwin-arm64), const SizeofSockaddrUnix ideal-int
pkg syscall (darwin-arm64), const TCIFLUSH = 1
pkg syscall (darwin-arm64), const TCIFLUSH ideal-int
pkg syscall (darwin-arm64), const TCIOFLUSH = 3
pkg syscall (darwin-arm64), const TCIOFLUSH ideal-int
pkg syscall (darwin-arm64), const TCOFLUSH = 2
pkg syscall (darwin-arm64), const TCOFLUSH ideal-int
pkg syscall (darwin-arm64), const TCP_CONNECTIONTIMEOUT = 32
pkg syscall (darwin-arm64), const TCP_CONNECTIONTIMEOUT ideal-int
pkg syscall (darwin-arm64), const TCP_ENABLE_ECN = 260
pkg syscall (darwin-arm64), const TCP_ENABLE_ECN ideal-int
pkg syscall (darwin-arm64), const TCP_KEEPALIVE = 16
pkg syscall (darwin-arm64), const TCP_KEEPALIVE ideal-int
pkg syscall (darwin-arm64), const TCP_KEEPCNT = 258
pkg syscall (darwin-arm64), const TCP_KEEPCNT ideal-int
pkg syscall (darwin-arm64), const TCP_KEEPINTVL = 257
pkg syscall (darwin-arm64), const TCP_KEEPINTVL ideal-int
pkg syscall (darwin-arm64), const TCP_MAXHLEN = 60
pkg syscall (darwin-arm64), const TCP_MAXHLEN ideal-int
pkg syscall (darwin-arm64), const TCP_MAXOLEN = 40
pkg syscall (darwin-arm64), const TCP_MAXOLEN ideal-int
pkg syscall (darwin-arm64), const TCP_MAXSEG = 2
pkg syscall (darwin-arm64), const TCP_MAXSEG ideal-int
pkg syscall (darwin-arm64), const TCP_MAXWIN = 65535
pkg syscall (darwin-arm64), const TCP_MAXWIN ideal-int
pkg syscall (darwin-arm64), const TCP_MAX_SACK = 4
pkg syscall (darwin-arm64), const TCP_MAX_SACK ideal-int
pkg syscall (darwin-arm64), const TCP_MAX_WINSHIFT = 14
pkg syscall (darwin-arm64), const TCP_MAX_WINSHIFT ideal-int
pkg syscall (darwin-arm64), const TCP_MINMSS = 216
pkg syscall (darwin-arm64), const TCP_MINMSS ideal-int
pkg syscall (darwin-arm64), const TCP_MSS = 512
pkg syscall (darwin-arm64), const TCP_MSS ideal-int
pkg syscall (darwin-arm64), const TCP_NOOPT = 8
pkg syscall (darwin-arm64), const TCP_NOOPT ideal-int
pkg syscall (darwin-arm64), const TCP_NOPUSH = 4
pkg syscall (darwin-arm64), const TCP_NOPUSH ideal-int
pkg syscall (darwin-arm64), const TCP_NOTSENT_LOWAT = 513
pkg syscall (darwin-arm64), const TCP_NOTSENT_LOWAT ideal-int
pkg syscall (darwin-arm64), const TCP_RXT_CONNDROPTIME = 128
pkg syscall (darwin-arm64), const TCP_RXT_CONNDROPTIME ideal-int
pkg syscall (darwin-arm64), const TCP_RXT_FINDROP = 256
pkg syscall (darwin-arm64), const TCP_RXT_FINDROP ideal-int
pkg syscall (darwin-arm64), const TCP_SENDMOREACKS = 259
pkg syscall (darwin-arm64), const TCP_SENDMOREACKS ideal-int
pkg syscall (darwin-arm64), const TCSAFLUSH = 2
pkg syscall (darwin-arm64), const TCSAFLUSH ideal-int
pkg syscall (darwin-arm64), const TIOCCBRK = 536900730
pkg syscall (darwin-arm64), const TIOCCBRK ideal-int
pkg syscall (darwin-arm64), const TIOCCDTR = 536900728
pkg syscall (darwin-arm64), const TIOCCDTR ideal-int
pkg syscall (darwin-arm64), const TIOCCONS = 2147775586
pkg syscall (darwin-arm64), const TIOCCONS ideal-int
pkg syscall (darwin-arm64), const TIOCDCDTIMESTAMP = 1074820184
pkg syscall (darwin-arm64), const TIOCDCDTIMESTAMP ideal-int
pkg syscall (darwin-arm64), const TIOCDRAIN = 536900702
pkg syscall (darwin-arm64), const TIOCDRAIN ideal-int
pkg syscall (darwin-arm64), const TIOCDSIMICROCODE = 536900693
pkg syscall (darwin-arm64), const TIOCDSIMICROCODE ideal-int
pkg syscall (darwin-arm64), const TIOCEXCL = 536900621
pkg syscall (darwin-arm64), const TIOCEXCL ideal-int
pkg syscall (darwin-arm64), const TIOCEXT = 2147775584
pkg syscall (darwin-arm64), const TIOCEXT ideal-int
pkg syscall (darwin-arm64), const TIOCFLUSH = 2147775504
pkg syscall (darwin-arm64), const TIOCFLUSH ideal-int
pkg syscall (darwin-arm64), const TIOCGDRAINWAIT = 1074033750
pkg syscall (darwin-arm64), const TIOCGDRAINWAIT ideal-int
pkg syscall (darwin-arm64), const TIOCGETA = 1078490131
pkg syscall (darwin-arm64), const TIOCGETA ideal-int
pkg syscall (darwin-arm64), const TIOCGETD = 1074033690
pkg syscall (darwin-arm64), const TIOCGETD ideal-int
pkg syscall (darwin-arm64), const TIOCGPGRP = 1074033783
pkg syscall (darwin-arm64), const TIOCGPGRP ideal-int
pkg syscall (darwin-arm64), const TIOCGWINSZ = 1074295912
pkg syscall (darwin-arm64), const TIOCGWINSZ ideal-int
pkg syscall (darwin-arm64), const TIOCIXOFF = 536900736
pkg syscall (darwin-arm64), const TIOCIXOFF ideal-int
pkg syscall (darwin-arm64), const TIOCIXON = 536900737
pkg syscall (darwin-arm64), const TIOCIXON ideal-int
pkg syscall (darwin-arm64), const TIOCMBIC = 2147775595
pkg syscall (darwin-arm64), const TIOCMBIC ideal-int
pkg syscall (darwin-arm64), const TIOCMBIS = 2147775596
pkg syscall (darwin-arm64), const TIOCMBIS ideal-int
pkg syscall (darwin-arm64), const TIOCMGDTRWAIT = 1074033754
pkg syscall (darwin-arm64), const TIOCMGDTRWAIT ideal-int
pkg syscall (darwin-arm64), const TIOCMGET = 1074033770
pkg syscall (darwin-arm64), const TIOCMGET ideal-int
pkg syscall (darwin-arm64), const TIOCMODG = 1074033667
pkg syscall (darwin-arm64), const TIOCMODG ideal-int
pkg syscall (darwin-arm64), const TIOCMODS = 2147775492
pkg syscall (darwin-arm64), const TIOCMODS ideal-int
pkg syscall (darwin-arm64), const TIOCMSDTRWAIT = 2147775579
pkg syscall (darwin-arm64), const TIOCMSDTRWAIT ideal-int
pkg syscall (darwin-arm64), const TIOCMSET = 2147775597
pkg syscall (darwin-arm64), const TIOCMSET ideal-int
pkg syscall (darwin-arm64), const TIOCM_CAR = 64
pkg syscall (darwin-arm64), const TIOCM_CAR ideal-int
pkg syscall (darwin-arm64), const TIOCM_CD = 64
pkg syscall (darwin-arm64), const TIOCM_CD ideal-int
pkg syscall (darwin-arm64), const TIOCM_CTS = 32
pkg syscall (darwin-arm64), const TIOCM_CTS ideal-int
pkg syscall (darwin-arm64), const TIOCM_DSR = 256
pkg syscall (darwin-arm64), const TIOCM_DSR ideal-int
pkg syscall (darwin-arm64), const TIOCM_DTR = 2
pkg syscall (darwin-arm64), const TIOCM_DTR ideal-int
pkg syscall (darwin-arm64), const TIOCM_LE = 1
pkg syscall (darwin-arm64), const TIOCM_LE ideal-int
pkg syscall (darwin-arm64), const TIOCM_RI = 128
pkg syscall (darwin-arm64), const TIOCM_RI ideal-int
pkg syscall (darwin-arm64), const TIOCM_RNG = 128
pkg syscall (darwin-arm64), const TIOCM_RNG ideal-int
pkg syscall (darwin-arm64), const TIOCM_RTS = 4
pkg syscall (darwin-arm64), const TIOCM_RTS ideal-int
pkg syscall (darwin-arm64), const TIOCM_SR = 16
pkg syscall (darwin-arm64), const TIOCM_SR ideal-int
pkg syscall (darwin-arm64), const TIOCM_ST = 8
pkg syscall (darwin-arm64), const TIOCM_ST ideal-int
pkg syscall (darwin-arm64), const TIOCNOTTY = 536900721
pkg syscall (darwin-arm64), const TIOCNOTTY ideal-int
pkg syscall (darwin-arm64), const TIOCNXCL = 536900622
pkg syscall (darwin-arm64), const TIOCNXCL ideal-int
pkg syscall (darwin-arm64), const TIOCOUTQ = 1074033779
pkg syscall (darwin-arm64), const TIOCOUTQ ideal-int
pkg syscall (darwin-arm64), const TIOCPKT = 2147775600
pkg syscall (darwin-arm64), const TIOCPKT ideal-int
pkg syscall (darwin-arm64), const TIOCPKT_DATA = 0
pkg syscall (darwin-arm64), const TIOCPKT_DATA ideal-int
pkg syscall (darwin-arm64), const TIOCPKT_DOSTOP = 32
pkg syscall (darwin-arm64), const TIOCPKT_DOSTOP ideal-int
pkg syscall (darwin-arm64), const TIOCPKT_FLUSHREAD = 1
pkg syscall (darwin-arm64), const TIOCPKT_FLUSHREAD ideal-int
pkg syscall (darwin-arm64), const TIOCPKT_FLUSHWRITE = 2
pkg syscall (darwin-arm64), const TIOCPKT_FLUSHWRITE ideal-int
pkg syscall (darwin-arm64), const TIOCPKT_IOCTL = 64
pkg syscall (darwin-arm64), const TIOCPKT_IOCTL ideal-int
pkg syscall (darwin-arm64), const TIOCPKT_NOSTOP = 16
pkg syscall (darwin-arm64), const TIOCPKT_NOSTOP ideal-int
pkg syscall (darwin-arm64), const TIOCPKT_START = 8
pkg syscall (darwin-arm64), const TIOCPKT_START ideal-int
pkg syscall (darwin-arm64), const TIOCPKT_STOP = 4
pkg syscall (darwin-arm64), const TIOCPKT_STOP ideal-int
pkg syscall (darwin-arm64), const TIOCPTYGNAME = 1082160211
pkg syscall (darwin-arm64), const TIOCPTYGNAME ideal-int
pkg syscall (darwin-arm64), const TIOCPTYGRANT = 536900692
pkg syscall (darwin-arm64), const TIOCPTYGRANT ideal-int
pkg syscall (darwin-arm64), const TIOCPTYUNLK = 536900690
pkg syscall (darwin-arm64), const TIOCPTYUNLK ideal-int
pkg syscall (darwin-arm64), const TIOCREMOTE = 2147775593
pkg syscall (darwin-arm64), const TIOCREMOTE ideal-int
pkg syscall (darwin-arm64), const TIOCSBRK = 536900731
pkg syscall (darwin-arm64), const TIOCSBRK ideal-int
pkg syscall (darwin-arm64), const TIOCSCONS = 536900707
pkg syscall (darwin-arm64), const TIOCSCONS ideal-int
pkg syscall (darwin-arm64), const TIOCSCTTY = 536900705
pkg syscall (darwin-arm64), const TIOCSCTTY ideal-int
pkg syscall (darwin-arm64), const TIOCSDRAINWAIT = 2147775575
pkg syscall (darwin-arm64), const TIOCSDRAINWAIT ideal-int
pkg syscall (darwin-arm64), const TIOCSDTR = 536900729
pkg syscall (darwin-arm64), const TIOCSDTR ideal-int
pkg syscall (darwin-arm64), const TIOCSETA = 2152231956
pkg syscall (darwin-arm64), const TIOCSETA ideal-int
pkg syscall (darwin-arm64), const TIOCSETAF = 2152231958
pkg syscall (darwin-arm64), const TIOCSETAF ideal-int
pkg syscall (darwin-arm64), const TIOCSETAW = 2152231957
pkg syscall (darwin-arm64), const TIOCSETAW ideal-int
pkg syscall (darwin-arm64), const TIOCSETD = 2147775515
pkg syscall (darwin-arm64), const TIOCSETD ideal-int
pkg syscall (darwin-arm64), const TIOCSIG = 536900703
pkg syscall (darwin-arm64), const TIOCSIG ideal-int
pkg syscall (darwin-arm64), const TIOCSPGRP = 2147775606
pkg syscall (darwin-arm64), const TIOCSPGRP ideal-int
pkg syscall (darwin-arm64), const TIOCSTART = 536900718
pkg syscall (darwin-arm64), const TIOCSTART ideal-int
pkg syscall (darwin-arm64), const TIOCSTAT = 536900709
pkg syscall (darwin-arm64), const TIOCSTAT ideal-int
pkg syscall (darwin-arm64), const TIOCSTI = 2147578994
pkg syscall (darwin-arm64), const TIOCSTI ideal-int
pkg syscall (darwin-arm64), const TIOCSTOP = 536900719
pkg syscall (darwin-arm64), const TIOCSTOP ideal-int
pkg syscall (darwin-arm64), const TIOCSWINSZ = 2148037735
pkg syscall (darwin-arm64), const TIOCSWINSZ ideal-int
pkg syscall (darwin-arm64), const TIOCTIMESTAMP = 1074820185
pkg syscall (darwin-arm64), const TIOCTIMESTAMP ideal-int
pkg syscall (darwin-arm64), const TIOCUCNTL = 2147775590
pkg syscall (darwin-arm64), const TIOCUCNTL ideal-int
pkg syscall (darwin-arm64), const TOSTOP = 4194304
pkg syscall (darwin-arm64), const TOSTOP ideal-int
pkg syscall (darwin-arm64), const VDISCARD = 15
pkg syscall (darwin-arm64), const VDISCARD ideal-int
pkg syscall (darwin-arm64), const VDSUSP = 11
pkg syscall (darwin-arm64), const VDSUSP ideal-int
pkg syscall (darwin-arm64), const VEOF = 0
pkg syscall (darwin-arm64), const VEOF ideal-int
pkg syscall (darwin-arm64), const VEOL = 1
pkg syscall (darwin-arm64), const VEOL ideal-int
pkg syscall (darwin-arm64), const VEOL2 = 2
pkg syscall (darwin-arm64), const VEOL2 ideal-int
pkg syscall (darwin-arm64), const VERASE = 3
pkg syscall (darwin-arm64), const VERASE ideal-int
pkg syscall (darwin-arm64), const VINTR = 8
pkg syscall (darwin-arm64), const VINTR ideal-int
pkg syscall (darwin-arm64), const VKILL = 5
pkg syscall (darwin-arm64), const VKILL ideal-int
pkg syscall (darwin-arm64), const VLNEXT = 14
pkg syscall (darwin-arm64), const VLNEXT ideal-int
pkg syscall (darwin-arm64), const VMIN = 16
pkg syscall (darwin-arm64), const VMIN ideal-int
pkg syscall (darwin-arm64), const VQUIT = 9
pkg syscall (darwin-arm64), const VQUIT ideal-int
pkg syscall (darwin-arm64), const VREPRINT = 6
pkg syscall (darwin-arm64), const VREPRINT ideal-int
pkg syscall (darwin-arm64), const VSTART = 12
pkg syscall (darwin-arm64), const VSTART ideal-int
pkg syscall (darwin-arm64), const VSTATUS = 18
pkg syscall (darwin-arm64), const VSTATUS ideal-int
pkg syscall (darwin-arm64), const VSTOP = 13
pkg syscall (darwin-arm64), const VSTOP ideal-int
pkg syscall (darwin-arm64), const VSUSP = 10
pkg syscall (darwin-arm64), const VSUSP ideal-int
pkg syscall (darwin-arm64), const VT0 = 0
pkg syscall (darwin-arm64), const VT0 ideal-int
pkg syscall (darwin-arm64), const VT1 = 65536
pkg syscall (darwin-arm64), const VT1 ideal-int
pkg syscall (darwin-arm64), const VTDLY = 65536
pkg syscall (darwin-arm64), const VTDLY ideal-int
pkg syscall (darwin-arm64), const VTIME = 17
pkg syscall (darwin-arm64), const VTIME ideal-int
pkg syscall (darwin-arm64), const VWERASE = 4
pkg syscall (darwin-arm64), const VWERASE ideal-int
pkg syscall (darwin-arm64), const WCONTINUED = 16
pkg syscall (darwin-arm64), const WCONTINUED ideal-int
pkg syscall (darwin-arm64), const WCOREFLAG = 128
pkg syscall (darwin-arm64), const WCOREFLAG ideal-int
pkg syscall (darwin-arm64), const WEXITED = 4
pkg syscall (darwin-arm64), const WEXITED ideal-int
pkg syscall (darwin-arm64), const WNOHANG = 1
pkg syscall (darwin-arm64), const WNOHANG ideal-int
pkg syscall (darwin-arm64), const WNOWAIT = 32
pkg syscall (darwin-arm64), const WNOWAIT ideal-int
pkg syscall (darwin-arm64), const WORDSIZE = 64
pkg syscall (darwin-arm64), const WORDSIZE ideal-int
pkg syscall (darwin-arm64), const WSTOPPED = 8
pkg syscall (darwin-arm64), const WSTOPPED ideal-int
pkg syscall (darwin-arm64), const WUNTRACED = 2
pkg syscall (darwin-arm64), const WUNTRACED ideal-int
pkg syscall (darwin-arm64), func Accept(int) (int, Sockaddr, error)
pkg syscall (darwin-arm64), func Access(string, uint32) error
pkg syscall (darwin-arm64), func Adjtime(*Timeval, *Timeval) error
pkg syscall (darwin-arm64), func Bind(int, Sockaddr) error
pkg syscall (darwin-arm64), func BpfBuflen //deprecated
pkg syscall (darwin-arm64), func BpfBuflen(int) (int, error)
pkg syscall (darwin-arm64), func BpfDatalink //deprecated
pkg syscall (darwin-arm64), func BpfDatalink(int) (int, error)
pkg syscall (darwin-arm64), func BpfHeadercmpl //deprecated
pkg syscall (darwin-arm64), func BpfHeadercmpl(int) (int, error)
pkg syscall (darwin-arm64), func BpfInterface //deprecated
pkg syscall (darwin-arm64), func BpfInterface(int, string) (string, error)
pkg syscall (darwin-arm64), func BpfJump //deprecated
pkg syscall (darwin-arm64), func BpfJump(int, int, int, int) *BpfInsn
pkg syscall (darwin-arm64), func BpfStats //deprecated
pkg syscall (darwin-arm64), func BpfStats(int) (*BpfStat, error)
pkg syscall (darwin-arm64), func BpfStmt //deprecated
pkg syscall (darwin-arm64), func BpfStmt(int, int) *BpfInsn
pkg syscall (darwin-arm64), func BpfTimeout //deprecated
pkg syscall (darwin-arm64), func BpfTimeout(int) (*Timeval, error)
pkg syscall (darwin-arm64), func CheckBpfVersion //deprecated
pkg syscall (darwin-arm64), func CheckBpfVersion(int) error
pkg syscall (darwin-arm64), func Chflags(string, int) error
pkg syscall (darwin-arm64), func Chroot(string) error
pkg syscall (darwin-arm64), func Close(int) error
pkg syscall (darwin-arm64), func CloseOnExec(int)
pkg syscall (darwin-arm64), func CmsgLen(int) int
pkg syscall (darwin-arm64), func CmsgSpace(int) int
pkg syscall (darwin-arm64), func Connect(int, Sockaddr) error
pkg syscall (darwin-arm64), func Dup(int) (int, error)
pkg syscall (darwin-arm64), func Dup2(int, int) error
pkg syscall (darwin-arm64), func Exchangedata(string, string, int) error
pkg syscall (darwin-arm64), func Fchdir(int) error
pkg syscall (darwin-arm64), func Fchflags(int, int) error
pkg syscall (darwin-arm64), func Fchmod(int, uint32) error
pkg syscall (darwin-arm64), func Fchown(int, int, int) error
pkg syscall (darwin-arm64), func FcntlFlock(uintptr, int, *Flock_t) error
pkg syscall (darwin-arm64), func Flock(int, int) error
pkg syscall (darwin-arm64), func FlushBpf //deprecated
pkg syscall (darwin-arm64), func FlushBpf(int) error
pkg syscall (darwin-arm64), func ForkExec(string, []string, *ProcAttr) (int, error)
pkg syscall (darwin-arm64), func Fpathconf(int, int) (int, error)
pkg syscall (darwin-arm64), func Fstat(int, *Stat_t) error
pkg syscall (darwin-arm64), func Fstatfs(int, *Statfs_t) error
pkg syscall (darwin-arm64), func Fsync(int) error
pkg syscall (darwin-arm64), func Ftruncate(int, int64) error
pkg syscall (darwin-arm64), func Futimes(int, []Timeval) error
pkg syscall (darwin-arm64), func Getdirentries(int, []uint8, *uintptr) (int, error)
pkg syscall (darwin-arm64), func Getdtablesize() int
pkg syscall (darwin-arm64), func Getfsstat([]Statfs_t, int) (int, error)
pkg syscall (darwin-arm64), func Getpeername(int) (Sockaddr, error)
pkg syscall (darwin-arm64), func Getpgid(int) (int, error)
pkg syscall (darwin-arm64), func Getpgrp() int
pkg syscall (darwin-arm64), func Getpriority(int, int) (int, error)
pkg syscall (darwin-arm64), func Getrlimit(int, *Rlimit) error
pkg syscall (darwin-arm64), func Getrusage(int, *Rusage) error
pkg syscall (darwin-arm64), func Getsid(int) (int, error)
pkg syscall (darwin-arm64), func Getsockname(int) (Sockaddr, error)
pkg syscall (darwin-arm64), func GetsockoptByte(int, int, int) (uint8, error)
pkg syscall (darwin-arm64), func GetsockoptICMPv6Filter(int, int, int) (*ICMPv6Filter, error)
pkg syscall (darwin-arm64), func GetsockoptIPMreq(int, int, int) (*IPMreq, error)
pkg syscall (darwin-arm64), func GetsockoptIPv6MTUInfo(int, int, int) (*IPv6MTUInfo, error)
pkg syscall (darwin-arm64), func GetsockoptIPv6Mreq(int, int, int) (*IPv6Mreq, error)
pkg syscall (darwin-arm64), func GetsockoptInet4Addr(int, int, int) ([4]uint8, error)
pkg syscall (darwin-arm64), func GetsockoptInt(int, int, int) (int, error)
pkg syscall (darwin-arm64), func Issetugid() bool
pkg syscall (darwin-arm64), func Kevent(int, []Kevent_t, []Kevent_t, *Timespec) (int, error)
pkg syscall (darwin-arm64), func Kill(int, Signal) error
pkg syscall (darwin-arm64), func Kqueue() (int, error)
pkg syscall (darwin-arm64), func Listen(int, int) error
pkg syscall (darwin-arm64), func Lstat(string, *Stat_t) error
pkg syscall (darwin-arm64), func Mkfifo(string, uint32) error
pkg syscall (darwin-arm64), func Mknod(string, uint32, int) error
pkg syscall (darwin-arm64), func Mlock([]uint8) error
pkg syscall (darwin-arm64), func Mlockall(int) error
pkg syscall (darwin-arm64), func Mmap(int, int64, int, int, int) ([]uint8, error)
pkg syscall (darwin-arm64), func Mprotect([]uint8, int) error
pkg syscall (darwin-arm64), func Munlock([]uint8) error
pkg syscall (darwin-arm64), func Munlockall() error
pkg syscall (darwin-arm64), func Munmap([]uint8) error
pkg syscall (darwin-arm64), func Open(string, int, uint32) (int, error)
pkg syscall (darwin-arm64), func ParseDirent([]uint8, int, []string) (int, int, []string)
pkg syscall (darwin-arm64), func ParseRoutingMessage //deprecated
pkg syscall (darwin-arm64), func ParseRoutingMessage([]uint8) ([]RoutingMessage, error)
pkg syscall (darwin-arm64), func ParseRoutingSockaddr //deprecated
pkg syscall (darwin-arm64), func ParseRoutingSockaddr(RoutingMessage) ([]Sockaddr, error)
pkg syscall (darwin-arm64), func ParseSocketControlMessage([]uint8) ([]SocketControlMessage, error)
pkg syscall (darwin-arm64), func ParseUnixRights(*SocketControlMessage) ([]int, error)
pkg syscall (darwin-arm64), func Pathconf(string, int) (int, error)
pkg syscall (darwin-arm64), func Pipe([]int) error
pkg syscall (darwin-arm64), func Pread(int, []uint8, int64) (int, error)
pkg syscall (darwin-arm64), func PtraceAttach(int) error
pkg syscall (darwin-arm64), func PtraceDetach(int) error
pkg syscall (darwin-arm64), func Pwrite(int, []uint8, int64) (int, error)
pkg syscall (darwin-arm64), func RawSyscall(uintptr, uintptr, uintptr, uintptr) (uintptr, uintptr, Errno)
pkg syscall (darwin-arm64), func RawSyscall6(uintptr, uintptr, uintptr, uintptr, uintptr, uintptr, uintptr) (uintptr, uintptr, Errno)
pkg syscall (darwin-arm64), func Read(int, []uint8) (int, error)
pkg syscall (darwin-arm64), func ReadDirent(int, []uint8) (int, error)
pkg syscall (darwin-arm64), func Recvfrom(int, []uint8, int) (int, Sockaddr, error)
pkg syscall (darwin-arm64), func Recvmsg(int, []uint8, []uint8, int) (int, int, int, Sockaddr, error)
pkg syscall (darwin-arm64), func Revoke(string) error
pkg syscall (darwin-arm64), func RouteRIB //deprecated
pkg syscall (darwin-arm64), func RouteRIB(int, int) ([]uint8, error)
pkg syscall (darwin-arm64), func Seek(int, int64, int) (int64, error)
pkg syscall (darwin-arm64), func Select(int, *FdSet, *FdSet, *FdSet, *Timeval) error
pkg syscall (darwin-arm64), func Sendfile(int, int, *int64, int) (int, error)
pkg syscall (darwin-arm64), func Sendmsg(int, []uint8, []uint8, Sockaddr, int) error
pkg syscall (darwin-arm64), func SendmsgN(int, []uint8, []uint8, Sockaddr, int) (int, error)
pkg syscall (darwin-arm64), func Sendto(int, []uint8, int, Sockaddr) error
pkg syscall (darwin-arm64), func SetBpf //deprecated
pkg syscall (darwin-arm64), func SetBpf(int, []BpfInsn) error
pkg syscall (darwin-arm64), func SetBpfBuflen //deprecated
pkg syscall (darwin-arm64), func SetBpfBuflen(int, int) (int, error)
pkg syscall (darwin-arm64), func SetBpfDatalink //deprecated
pkg syscall (darwin-arm64), func SetBpfDatalink(int, int) (int, error)
pkg syscall (darwin-arm64), func SetBpfHeadercmpl //deprecated
pkg syscall (darwin-arm64), func SetBpfHeadercmpl(int, int) error
pkg syscall (darwin-arm64), func SetBpfImmediate //deprecated
pkg syscall (darwin-arm64), func SetBpfImmediate(int, int) error
pkg syscall (darwin-arm64), func SetBpfInterface //deprecated
pkg syscall (darwin-arm64), func SetBpfInterface(int, string) error
pkg syscall (darwin-arm64), func SetBpfPromisc //deprecated
pkg syscall (darwin-arm64), func SetBpfPromisc(int, int) error
pkg syscall (darwin-arm64), func SetBpfTimeout //deprecated
pkg syscall (darwin-arm64), func SetBpfTimeout(int, *Timeval) error
pkg syscall (darwin-arm64), func SetKevent(*Kevent_t, int, int, int)
pkg syscall (darwin-arm64), func SetNonblock(int, bool) error
pkg syscall (darwin-arm64), func Setegid(int) error
pkg syscall (darwin-arm64), func Seteuid(int) error
pkg syscall (darwin-arm64), func Setgid(int) error
pkg syscall (darwin-arm64), func Setgroups([]int) error
pkg syscall (darwin-arm64), func Setlogin(string) error
pkg syscall (darwin-arm64), func Setpgid(int, int) error
pkg syscall (darwin-arm64), func Setpriority(int, int, int) error
pkg syscall (darwin-arm64), func Setprivexec(int) error
pkg syscall (darwin-arm64), func Setregid(int, int) error
pkg syscall (darwin-arm64), func Setreuid(int, int) error
pkg syscall (darwin-arm64), func Setrlimit(int, *Rlimit) error
pkg syscall (darwin-arm64), func Setsid() (int, error)
pkg syscall (darwin-arm64), func SetsockoptByte(int, int, int, uint8) error
pkg syscall (darwin-arm64), func SetsockoptICMPv6Filter(int, int, int, *ICMPv6Filter) error
pkg syscall (darwin-arm64), func SetsockoptIPMreq(int, int, int, *IPMreq) error
pkg syscall (darwin-arm64), func SetsockoptIPv6Mreq(int, int, int, *IPv6Mreq) error
pkg syscall (darwin-arm64), func SetsockoptInet4Addr(int, int, int, [4]uint8) error
pkg syscall (darwin-arm64), func SetsockoptInt(int, int, int, int) error
pkg syscall (darwin-arm64), func SetsockoptLinger(int, int, int, *Linger) error
pkg syscall (darwin-arm64), func SetsockoptString(int, int, int, string) error
pkg syscall (darwin-arm64), func SetsockoptTimeval(int, int, int, *Timeval) error
pkg syscall (darwin-arm64), func Settimeofday(*Timeval) error
pkg syscall (darwin-arm64), func Setuid(int) error
pkg syscall (darwin-arm64), func Shutdown(int, int) error
pkg syscall (darwin-arm64), func SlicePtrFromStrings([]string) ([]*uint8, error)
pkg syscall (darwin-arm64), func Socket(int, int, int) (int, error)
pkg syscall (darwin-arm64), func Socketpair(int, int, int) ([2]int, error)
pkg syscall (darwin-arm64), func Stat(string, *Stat_t) error
pkg syscall (darwin-arm64), func Statfs(string, *Statfs_t) error
pkg syscall (darwin-arm64), func StringSlicePtr //deprecated
pkg syscall (darwin-arm64), func StringSlicePtr([]string) []*uint8
pkg syscall (darwin-arm64), func Sync() error
pkg syscall (darwin-arm64), func Syscall(uintptr, uintptr, uintptr, uintptr) (uintptr, uintptr, Errno)
pkg syscall (darwin-arm64), func Syscall6(uintptr, uintptr, uintptr, uintptr, uintptr, uintptr, uintptr) (uintptr, uintptr, Errno)
pkg syscall (darwin-arm64), func Syscall9(uintptr, uintptr, uintptr, uintptr, uintptr, uintptr, uintptr, uintptr, uintptr, uintptr) (uintptr, uintptr, Errno)
pkg syscall (darwin-arm64), func Sysctl(string) (string, error)
pkg syscall (darwin-arm64), func SysctlUint32(string) (uint32, error)
pkg syscall (darwin-arm64), func TimevalToNsec(Timeval) int64
pkg syscall (darwin-arm64), func Truncate(string, int64) error
pkg syscall (darwin-arm64), func Umask(int) int
pkg syscall (darwin-arm64), func Undelete(string) error
pkg syscall (darwin-arm64), func UnixRights(...int) []uint8
pkg syscall (darwin-arm64), func Unmount(string, int) error
pkg syscall (darwin-arm64), func Wait4(int, *WaitStatus, int, *Rusage) (int, error)
pkg syscall (darwin-arm64), func Write(int, []uint8) (int, error)
pkg syscall (darwin-arm64), method (*Cmsghdr) SetLen(int)
pkg syscall (darwin-arm64), method (*Iovec) SetLen(int)
pkg syscall (darwin-arm64), method (*Msghdr) SetControllen(int)
pkg syscall (darwin-arm64), type BpfHdr struct
pkg syscall (darwin-arm64), type BpfHdr struct, Caplen uint32
pkg syscall (darwin-arm64), type BpfHdr struct, Datalen uint32
pkg syscall (darwin-arm64), type BpfHdr struct, Hdrlen uint16
pkg syscall (darwin-arm64), type BpfHdr struct, Pad_cgo_0 [2]uint8
pkg syscall (darwin-arm64), type BpfHdr struct, Tstamp Timeval32
pkg syscall (darwin-arm64), type BpfInsn struct
pkg syscall (darwin-arm64), type BpfInsn struct, Code uint16
pkg syscall (darwin-arm64), type BpfInsn struct, Jf uint8
pkg syscall (darwin-arm64), type BpfInsn struct, Jt uint8
pkg syscall (darwin-arm64), type BpfInsn struct, K uint32
pkg syscall (darwin-arm64), type BpfProgram struct
pkg syscall (darwin-arm64), type BpfProgram struct, Insns *BpfInsn
pkg syscall (darwin-arm64), type BpfProgram struct, Len uint32
pkg syscall (darwin-arm64), type BpfProgram struct, Pad_cgo_0 [4]uint8
pkg syscall (darwin-arm64), type BpfStat struct
pkg syscall (darwin-arm64), type BpfStat struct, Drop uint32
pkg syscall (darwin-arm64), type BpfStat struct, Recv uint32
pkg syscall (darwin-arm64), type BpfVersion struct
pkg syscall (darwin-arm64), type BpfVersion struct, Major uint16
pkg syscall (darwin-arm64), type BpfVersion struct, Minor uint16
pkg syscall (darwin-arm64), type Cmsghdr struct
pkg syscall (darwin-arm64), type Cmsghdr struct, Len uint32
pkg syscall (darwin-arm64), type Cmsghdr struct, Level int32
pkg syscall (darwin-arm64), type Cmsghdr struct, Type int32
pkg syscall (darwin-arm64), type Credential struct
pkg syscall (darwin-arm64), type Credential struct, Gid uint32
pkg syscall (darwin-arm64), type Credential struct, Groups []uint32
pkg syscall (darwin-arm64), type Credential struct, NoSetGroups bool
pkg syscall (darwin-arm64), type Credential struct, Uid uint32
pkg syscall (darwin-arm64), type Dirent struct
pkg syscall (darwin-arm64), type Dirent struct, Ino uint64
pkg syscall (darwin-arm64), type Dirent struct, Name [1024]int8
pkg syscall (darwin-arm64), type Dirent struct, Namlen uint16
pkg syscall (darwin-arm64), type Dirent struct, Pad_cgo_0 [3]uint8
pkg syscall (darwin-arm64), type Dirent struct, Reclen uint16
pkg syscall (darwin-arm64), type Dirent struct, Seekoff uint64
pkg syscall (darwin-arm64), type Dirent struct, Type uint8
pkg syscall (darwin-arm64), type Fbootstraptransfer_t struct
pkg syscall (darwin-arm64), type Fbootstraptransfer_t struct, Buffer *uint8
pkg syscall (darwin-arm64), type Fbootstraptransfer_t struct, Length uint64
pkg syscall (darwin-arm64), type Fbootstraptransfer_t struct, Offset int64
pkg syscall (darwin-arm64), type FdSet struct
pkg syscall (darwin-arm64), type FdSet struct, Bits [32]int32
pkg syscall (darwin-arm64), type Flock_t struct
pkg syscall (darwin-arm64), type Flock_t struct, Len int64
pkg syscall (darwin-arm64), type Flock_t struct, Pid int32
pkg syscall (darwin-arm64), type Flock_t struct, Start int64
pkg syscall (darwin-arm64), type Flock_t struct, Type int16
pkg syscall (darwin-arm64), type Flock_t struct, Whence int16
pkg syscall (darwin-arm64), type Fsid struct
pkg syscall (darwin-arm64), type Fsid struct, Val [2]int32
pkg syscall (darwin-arm64), type Fstore_t struct
pkg syscall (darwin-arm64), type Fstore_t struct, Bytesalloc int64
pkg syscall (darwin-arm64), type Fstore_t struct, Flags uint32
pkg syscall (darwin-arm64), type Fstore_t struct, Length int64
pkg syscall (darwin-arm64), type Fstore_t struct, Offset int64
pkg syscall (darwin-arm64), type Fstore_t struct, Posmode int32
pkg syscall (darwin-arm64), type ICMPv6Filter struct
pkg syscall (darwin-arm64), type ICMPv6Filter struct, Filt [8]uint32
pkg syscall (darwin-arm64), type IPv6MTUInfo struct
pkg syscall (darwin-arm64), type IPv6MTUInfo struct, Addr RawSockaddrInet6
pkg syscall (darwin-arm64), type IPv6MTUInfo struct, Mtu uint32
pkg syscall (darwin-arm64), type IfData struct
pkg syscall (darwin-arm64), type IfData struct, Addrlen uint8
pkg syscall (darwin-arm64), type IfData struct, Baudrate uint32
pkg syscall (darwin-arm64), type IfData struct, Collisions uint32
pkg syscall (darwin-arm64), type IfData struct, Hdrlen uint8
pkg syscall (darwin-arm64), type IfData struct, Hwassist uint32
pkg syscall (darwin-arm64), type IfData struct, Ibytes uint32
pkg syscall (darwin-arm64), type IfData struct, Ierrors uint32
pkg syscall (darwin-arm64), type IfData struct, Imcasts uint32
pkg syscall (darwin-arm64), type IfData struct, Ipackets uint32
pkg syscall (darwin-arm64), type IfData struct, Iqdrops uint32
pkg syscall (darwin-arm64), type IfData struct, Lastchange Timeval32
pkg syscall (darwin-arm64), type IfData struct, Metric uint32
pkg syscall (darwin-arm64), type IfData struct, Mtu uint32
pkg syscall (darwin-arm64), type IfData struct, Noproto uint32
pkg syscall (darwin-arm64), type IfData struct, Obytes uint32
pkg syscall (darwin-arm64), type IfData struct, Oerrors uint32
pkg syscall (darwin-arm64), type IfData struct, Omcasts uint32
pkg syscall (darwin-arm64), type IfData struct, Opackets uint32
pkg syscall (darwin-arm64), type IfData struct, Physical uint8
pkg syscall (darwin-arm64), type IfData struct, Recvquota uint8
pkg syscall (darwin-arm64), type IfData struct, Recvtiming uint32
pkg syscall (darwin-arm64), type IfData struct, Reserved1 uint32
pkg syscall (darwin-arm64), type IfData struct, Reserved2 uint32
pkg syscall (darwin-arm64), type IfData struct, Type uint8
pkg syscall (darwin-arm64), type IfData struct, Typelen uint8
pkg syscall (darwin-arm64), type IfData struct, Unused1 uint8
pkg syscall (darwin-arm64), type IfData struct, Unused2 uint32
pkg syscall (darwin-arm64), type IfData struct, Xmitquota uint8
pkg syscall (darwin-arm64), type IfData struct, Xmittiming uint32
pkg syscall (darwin-arm64), type IfMsghdr struct
pkg syscall (darwin-arm64), type IfMsghdr struct, Addrs int32
pkg syscall (darwin-arm64), type IfMsghdr struct, Data IfData
pkg syscall (darwin-arm64), type IfMsghdr struct, Flags int32
pkg syscall (darwin-arm64), type IfMsghdr struct, Index uint16
pkg syscall (darwin-arm64), type IfMsghdr struct, Msglen uint16
pkg syscall (darwin-arm64), type IfMsghdr struct, Pad_cgo_0 [2]uint8
pkg syscall (darwin-arm64), type IfMsghdr struct, Type uint8
pkg syscall (darwin-arm64), type IfMsghdr struct, Version uint8
pkg syscall (darwin-arm64), type IfaMsghdr struct
pkg syscall (darwin-arm64), type IfaMsghdr struct, Addrs int32
pkg syscall (darwin-arm64), type IfaMsghdr struct, Flags int32
pkg syscall (darwin-arm64), type IfaMsghdr struct, Index uint16
pkg syscall (darwin-arm64), type IfaMsghdr struct, Metric int32
pkg syscall (darwin-arm64), type IfaMsghdr struct, Msglen uint16
pkg syscall (darwin-arm64), type IfaMsghdr struct, Pad_cgo_0 [2]uint8
pkg syscall (darwin-arm64), type IfaMsghdr struct, Type uint8
pkg syscall (darwin-arm64), type IfaMsghdr struct, Version uint8
pkg syscall (darwin-arm64), type IfmaMsghdr struct
pkg syscall (darwin-arm64), type IfmaMsghdr struct, Addrs int32
pkg syscall (darwin-arm64), type IfmaMsghdr struct, Flags int32
pkg syscall (darwin-arm64), type IfmaMsghdr struct, Index uint16
pkg syscall (darwin-arm64), type IfmaMsghdr struct, Msglen uint16
pkg syscall (darwin-arm64), type IfmaMsghdr struct, Pad_cgo_0 [2]uint8
pkg syscall (darwin-arm64), type IfmaMsghdr struct, Type uint8
pkg syscall (darwin-arm64), type IfmaMsghdr struct, Version uint8
pkg syscall (darwin-arm64), type IfmaMsghdr2 struct
pkg syscall (darwin-arm64), type IfmaMsghdr2 struct, Addrs int32
pkg syscall (darwin-arm64), type IfmaMsghdr2 struct, Flags int32
pkg syscall (darwin-arm64), type IfmaMsghdr2 struct, Index uint16
pkg syscall (darwin-arm64), type IfmaMsghdr2 struct, Msglen uint16
pkg syscall (darwin-arm64), type IfmaMsghdr2 struct, Pad_cgo_0 [2]uint8
pkg syscall (darwin-arm64), type IfmaMsghdr2 struct, Refcount int32
pkg syscall (darwin-arm64), type IfmaMsghdr2 struct, Type uint8
pkg syscall (darwin-arm64), type IfmaMsghdr2 struct, Version uint8
pkg syscall (darwin-arm64), type Inet4Pktinfo struct
pkg syscall (darwin-arm64), type Inet4Pktinfo struct, Addr [4]uint8
pkg syscall (darwin-arm64), type Inet4Pktinfo struct, Ifindex uint32
pkg syscall (darwin-arm64), type Inet4Pktinfo struct, Spec_dst [4]uint8
pkg syscall (darwin-arm64), type Inet6Pktinfo struct
pkg syscall (darwin-arm64), type Inet6Pktinfo struct, Addr [16]uint8
pkg syscall (darwin-arm64), type Inet6Pktinfo struct, Ifindex uint32
pkg syscall (darwin-arm64), type InterfaceAddrMessage //deprecated
pkg syscall (darwin-arm64), type InterfaceAddrMessage struct
pkg syscall (darwin-arm64), type InterfaceAddrMessage struct, Data []uint8
pkg syscall (darwin-arm64), type InterfaceAddrMessage struct, Header IfaMsghdr
pkg syscall (darwin-arm64), type InterfaceMessage //deprecated
pkg syscall (darwin-arm64), type InterfaceMessage struct
pkg syscall (darwin-arm64), type InterfaceMessage struct, Data []uint8
pkg syscall (darwin-arm64), type InterfaceMessage struct, Header IfMsghdr
pkg syscall (darwin-arm64), type InterfaceMulticastAddrMessage //deprecated
pkg syscall (darwin-arm64), type InterfaceMulticastAddrMessage struct
pkg syscall (darwin-arm64), type InterfaceMulticastAddrMessage struct, Data []uint8
pkg syscall (darwin-arm64), type InterfaceMulticastAddrMessage struct, Header IfmaMsghdr2
pkg syscall (darwin-arm64), type Iovec struct
pkg syscall (darwin-arm64), type Iovec struct, Base *uint8
pkg syscall (darwin-arm64), type Iovec struct, Len uint64
pkg syscall (darwin-arm64), type Kevent_t struct
pkg syscall (darwin-arm64), type Kevent_t struct, Data int64
pkg syscall (darwin-arm64), type Kevent_t struct, Fflags uint32
pkg syscall (darwin-arm64), type Kevent_t struct, Filter int16
pkg syscall (darwin-arm64), type Kevent_t struct, Flags uint16
pkg syscall (darwin-arm64), type Kevent_t struct, Ident uint64
pkg syscall (darwin-arm64), type Kevent_t struct, Udata *uint8
pkg syscall (darwin-arm64), type Log2phys_t struct
pkg syscall (darwin-arm64), type Log2phys_t struct, Contigbytes int64
pkg syscall (darwin-arm64), type Log2phys_t struct, Devoffset int64
pkg syscall (darwin-arm64), type Log2phys_t struct, Flags uint32
pkg syscall (darwin-arm64), type Msghdr struct
pkg syscall (darwin-arm64), type Msghdr struct, Control *uint8
pkg syscall (darwin-arm64), type Msghdr struct, Controllen uint32
pkg syscall (darwin-arm64), type Msghdr struct, Flags int32
pkg syscall (darwin-arm64), type Msghdr struct, Iov *Iovec
pkg syscall (darwin-arm64), type Msghdr struct, Iovlen int32
pkg syscall (darwin-arm64), type Msghdr struct, Name *uint8
pkg syscall (darwin-arm64), type Msghdr struct, Namelen uint32
pkg syscall (darwin-arm64), type Msghdr struct, Pad_cgo_0 [4]uint8
pkg syscall (darwin-arm64), type Msghdr struct, Pad_cgo_1 [4]uint8
pkg syscall (darwin-arm64), type Radvisory_t struct
pkg syscall (darwin-arm64), type Radvisory_t struct, Count int32
pkg syscall (darwin-arm64), type Radvisory_t struct, Offset int64
pkg syscall (darwin-arm64), type Radvisory_t struct, Pad_cgo_0 [4]uint8
pkg syscall (darwin-arm64), type RawSockaddr struct, Data [14]int8
pkg syscall (darwin-arm64), type RawSockaddr struct, Family uint8
pkg syscall (darwin-arm64), type RawSockaddr struct, Len uint8
pkg syscall (darwin-arm64), type RawSockaddrAny struct, Pad [92]int8
pkg syscall (darwin-arm64), type RawSockaddrDatalink struct
pkg syscall (darwin-arm64), type RawSockaddrDatalink struct, Alen uint8
pkg syscall (darwin-arm64), type RawSockaddrDatalink struct, Data [12]int8
pkg syscall (darwin-arm64), type RawSockaddrDatalink struct, Family uint8
pkg syscall (darwin-arm64), type RawSockaddrDatalink struct, Index uint16
pkg syscall (darwin-arm64), type RawSockaddrDatalink struct, Len uint8
pkg syscall (darwin-arm64), type RawSockaddrDatalink struct, Nlen uint8
pkg syscall (darwin-arm64), type RawSockaddrDatalink struct, Slen uint8
pkg syscall (darwin-arm64), type RawSockaddrDatalink struct, Type uint8
pkg syscall (darwin-arm64), type RawSockaddrInet4 struct, Family uint8
pkg syscall (darwin-arm64), type RawSockaddrInet4 struct, Len uint8
pkg syscall (darwin-arm64), type RawSockaddrInet4 struct, Zero [8]int8
pkg syscall (darwin-arm64), type RawSockaddrInet6 struct, Family uint8
pkg syscall (darwin-arm64), type RawSockaddrInet6 struct, Len uint8
pkg syscall (darwin-arm64), type RawSockaddrUnix struct, Family uint8
pkg syscall (darwin-arm64), type RawSockaddrUnix struct, Len uint8
pkg syscall (darwin-arm64), type RawSockaddrUnix struct, Path [104]int8
pkg syscall (darwin-arm64), type Rlimit struct
pkg syscall (darwin-arm64), type Rlimit struct, Cur uint64
pkg syscall (darwin-arm64), type Rlimit struct, Max uint64
pkg syscall (darwin-arm64), type RouteMessage //deprecated
pkg syscall (darwin-arm64), type RouteMessage struct
pkg syscall (darwin-arm64), type RouteMessage struct, Data []uint8
pkg syscall (darwin-arm64), type RouteMessage struct, Header RtMsghdr
pkg syscall (darwin-arm64), type RoutingMessage //deprecated
pkg syscall (darwin-arm64), type RoutingMessage interface, unexported methods
pkg syscall (darwin-arm64), type RtMetrics struct
pkg syscall (darwin-arm64), type RtMetrics struct, Expire int32
pkg syscall (darwin-arm64), type RtMetrics struct, Filler [4]uint32
pkg syscall (darwin-arm64), type RtMetrics struct, Hopcount uint32
pkg syscall (darwin-arm64), type RtMetrics struct, Locks uint32
pkg syscall (darwin-arm64), type RtMetrics struct, Mtu uint32
pkg syscall (darwin-arm64), type RtMetrics struct, Pksent uint32
pkg syscall (darwin-arm64), type RtMetrics struct, Recvpipe uint32
pkg syscall (darwin-arm64), type RtMetrics struct, Rtt uint32
pkg syscall (darwin-arm64), type RtMetrics struct, Rttvar uint32
pkg syscall (darwin-arm64), type RtMetrics struct, Sendpipe uint32
pkg syscall (darwin-arm64), type RtMetrics struct, Ssthresh uint32
pkg syscall (darwin-arm64), type RtMsghdr struct
pkg syscall (darwin-arm64), type RtMsghdr struct, Addrs int32
pkg syscall (darwin-arm64), type RtMsghdr struct, Errno int32
pkg syscall (darwin-arm64), type RtMsghdr struct, Flags int32
pkg syscall (darwin-arm64), type RtMsghdr struct, Index uint16
pkg syscall (darwin-arm64), type RtMsghdr struct, Inits uint32
pkg syscall (darwin-arm64), type RtMsghdr struct, Msglen uint16
pkg syscall (darwin-arm64), type RtMsghdr struct, Pad_cgo_0 [2]uint8
pkg syscall (darwin-arm64), type RtMsghdr struct, Pid int32
pkg syscall (darwin-arm64), type RtMsghdr struct, Rmx RtMetrics
pkg syscall (darwin-arm64), type RtMsghdr struct, Seq int32
pkg syscall (darwin-arm64), type RtMsghdr struct, Type uint8
pkg syscall (darwin-arm64), type RtMsghdr struct, Use int32
pkg syscall (darwin-arm64), type RtMsghdr struct, Version uint8
pkg syscall (darwin-arm64), type Rusage struct, Idrss int64
pkg syscall (darwin-arm64), type Rusage struct, Inblock int64
pkg syscall (darwin-arm64), type Rusage struct, Isrss int64
pkg syscall (darwin-arm64), type Rusage struct, Ixrss int64
pkg syscall (darwin-arm64), type Rusage struct, Majflt int64
pkg syscall (darwin-arm64), type Rusage struct, Maxrss int64
pkg syscall (darwin-arm64), type Rusage struct, Minflt int64
pkg syscall (darwin-arm64), type Rusage struct, Msgrcv int64
pkg syscall (darwin-arm64), type Rusage struct, Msgsnd int64
pkg syscall (darwin-arm64), type Rusage struct, Nivcsw int64
pkg syscall (darwin-arm64), type Rusage struct, Nsignals int64
pkg syscall (darwin-arm64), type Rusage struct, Nswap int64
pkg syscall (darwin-arm64), type Rusage struct, Nvcsw int64
pkg syscall (darwin-arm64), type Rusage struct, Oublock int64
pkg syscall (darwin-arm64), type Rusage struct, Stime Timeval
pkg syscall (darwin-arm64), type Rusage struct, Utime Timeval
pkg syscall (darwin-arm64), type SockaddrDatalink struct
pkg syscall (darwin-arm64), type SockaddrDatalink struct, Alen uint8
pkg syscall (darwin-arm64), type SockaddrDatalink struct, Data [12]int8
pkg syscall (darwin-arm64), type SockaddrDatalink struct, Family uint8
pkg syscall (darwin-arm64), type SockaddrDatalink struct, Index uint16
pkg syscall (darwin-arm64), type SockaddrDatalink struct, Len uint8
pkg syscall (darwin-arm64), type SockaddrDatalink struct, Nlen uint8
pkg syscall (darwin-arm64), type SockaddrDatalink struct, Slen uint8
pkg syscall (darwin-arm64), type SockaddrDatalink struct, Type uint8
pkg syscall (darwin-arm64), type SocketControlMessage struct
pkg syscall (darwin-arm64), type SocketControlMessage struct, Data []uint8
pkg syscall (darwin-arm64), type SocketControlMessage struct, Header Cmsghdr
pkg syscall (darwin-arm64), type Stat_t struct
pkg syscall (darwin-arm64), type Stat_t struct, Atimespec Timespec
pkg syscall (darwin-arm64), type Stat_t struct, Birthtimespec Timespec
pkg syscall (darwin-arm64), type Stat_t struct, Blksize int32
pkg syscall (darwin-arm64), type Stat_t struct, Blocks int64
pkg syscall (darwin-arm64), type Stat_t struct, Ctimespec Timespec
pkg syscall (darwin-arm64), type Stat_t struct, Dev int32
pkg syscall (darwin-arm64), type Stat_t struct, Flags uint32
pkg syscall (darwin-arm64), type Stat_t struct, Gen uint32
pkg syscall (darwin-arm64), type Stat_t struct, Gid uint32
pkg syscall (darwin-arm64), type Stat_t struct, Ino uint64
pkg syscall (darwin-arm64), type Stat_t struct, Lspare int32
pkg syscall (darwin-arm64), type Stat_t struct, Mode uint16
pkg syscall (darwin-arm64), type Stat_t struct, Mtimespec Timespec
pkg syscall (darwin-arm64), type Stat_t struct, Nlink uint16
pkg syscall (darwin-arm64), type Stat_t struct, Pad_cgo_0 [4]uint8
pkg syscall (darwin-arm64), type Stat_t struct, Qspare [2]int64
pkg syscall (darwin-arm64), type Stat_t struct, Rdev int32
pkg syscall (darwin-arm64), type Stat_t struct, Size int64
pkg syscall (darwin-arm64), type Stat_t struct, Uid uint32
pkg syscall (darwin-arm64), type Statfs_t struct
pkg syscall (darwin-arm64), type Statfs_t struct, Bavail uint64
pkg syscall (darwin-arm64), type Statfs_t struct, Bfree uint64
pkg syscall (darwin-arm64), type Statfs_t struct, Blocks uint64
pkg syscall (darwin-arm64), type Statfs_t struct, Bsize uint32
pkg syscall (darwin-arm64), type Statfs_t struct, Ffree uint64
pkg syscall (darwin-arm64), type Statfs_t struct, Files uint64
pkg syscall (darwin-arm64), type Statfs_t struct, Flags uint32
pkg syscall (darwin-arm64), type Statfs_t struct, Fsid Fsid
pkg syscall (darwin-arm64), type Statfs_t struct, Fssubtype uint32
pkg syscall (darwin-arm64), type Statfs_t struct, Fstypename [16]int8
pkg syscall (darwin-arm64), type Statfs_t struct, Iosize int32
pkg syscall (darwin-arm64), type Statfs_t struct, Mntfromname [1024]int8
pkg syscall (darwin-arm64), type Statfs_t struct, Mntonname [1024]int8
pkg syscall (darwin-arm64), type Statfs_t struct, Owner uint32
pkg syscall (darwin-arm64), type Statfs_t struct, Reserved [8]uint32
pkg syscall (darwin-arm64), type Statfs_t struct, Type uint32
pkg syscall (darwin-arm64), type SysProcAttr struct, Chroot string
pkg syscall (darwin-arm64), type SysProcAttr struct, Credential *Credential
pkg syscall (darwin-arm64), type SysProcAttr struct, Ctty int
pkg syscall (darwin-arm64), type SysProcAttr struct, Foreground bool
pkg syscall (darwin-arm64), type SysProcAttr struct, Noctty bool
pkg syscall (darwin-arm64), type SysProcAttr struct, Pgid int
pkg syscall (darwin-arm64), type SysProcAttr struct, Ptrace bool
pkg syscall (darwin-arm64), type SysProcAttr struct, Setctty bool
pkg syscall (darwin-arm64), type SysProcAttr struct, Setpgid bool
pkg syscall (darwin-arm64), type SysProcAttr struct, Setsid bool
pkg syscall (darwin-arm64), type Termios struct
pkg syscall (darwin-arm64), type Termios struct, Cc [20]uint8
pkg syscall (darwin-arm64), type Termios struct, Cflag uint64
pkg syscall (darwin-arm64), type Termios struct, Iflag uint64
pkg syscall (darwin-arm64), type Termios struct, Ispeed uint64
pkg syscall (darwin-arm64), type Termios struct, Lflag uint64
pkg syscall (darwin-arm64), type Termios struct, Oflag uint64
pkg syscall (darwin-arm64), type Termios struct, Ospeed uint64
pkg syscall (darwin-arm64), type Termios struct, Pad_cgo_0 [4]uint8
pkg syscall (darwin-arm64), type Timespec struct, Nsec int64
pkg syscall (darwin-arm64), type Timespec struct, Sec int64
pkg syscall (darwin-arm64), type Timeval struct, Pad_cgo_0 [4]uint8
pkg syscall (darwin-arm64), type Timeval struct, Sec int64
pkg syscall (darwin-arm64), type Timeval struct, Usec int32
pkg syscall (darwin-arm64), type Timeval32 struct
pkg syscall (darwin-arm64), type Timeval32 struct, Sec int32
pkg syscall (darwin-arm64), type Timeval32 struct, Usec int32
pkg syscall (darwin-arm64), type WaitStatus uint32
pkg syscall (darwin-arm64), var Stderr int
pkg syscall (darwin-arm64), var Stdin int
pkg syscall (darwin-arm64), var Stdout int
pkg syscall (darwin-arm64-cgo), const AF_APPLETALK = 16
pkg syscall (darwin-arm64-cgo), const AF_APPLETALK ideal-int
pkg syscall (darwin-arm64-cgo), const AF_CCITT = 10
pkg syscall (darwin-arm64-cgo), const AF_CCITT ideal-int
pkg syscall (darwin-arm64-cgo), const AF_CHAOS = 5
pkg syscall (darwin-arm64-cgo), const AF_CHAOS ideal-int
pkg syscall (darwin-arm64-cgo), const AF_CNT = 21
pkg syscall (darwin-arm64-cgo), const AF_CNT ideal-int
pkg syscall (darwin-arm64-cgo), const AF_COIP = 20
pkg syscall (darwin-arm64-cgo), const AF_COIP ideal-int
pkg syscall (darwin-arm64-cgo), const AF_DATAKIT = 9
pkg syscall (darwin-arm64-cgo), const AF_DATAKIT ideal-int
pkg syscall (darwin-arm64-cgo), const AF_DECnet = 12
pkg syscall (darwin-arm64-cgo), const AF_DECnet ideal-int
pkg syscall (darwin-arm64-cgo), const AF_DLI = 13
pkg syscall (darwin-arm64-cgo), const AF_DLI ideal-int
pkg syscall (darwin-arm64-cgo), const AF_E164 = 28
pkg syscall (darwin-arm64-cgo), const AF_E164 ideal-int
pkg syscall (darwin-arm64-cgo), const AF_ECMA = 8
pkg syscall (darwin-arm64-cgo), const AF_ECMA ideal-int
pkg syscall (darwin-arm64-cgo), const AF_HYLINK = 15
pkg syscall (darwin-arm64-cgo), const AF_HYLINK ideal-int
pkg syscall (darwin-arm64-cgo), const AF_IEEE80211 = 37
pkg syscall (darwin-arm64-cgo), const AF_IEEE80211 ideal-int
pkg syscall (darwin-arm64-cgo), const AF_IMPLINK = 3
pkg syscall (darwin-arm64-cgo), const AF_IMPLINK ideal-int
pkg syscall (darwin-arm64-cgo), const AF_INET6 = 30
pkg syscall (darwin-arm64-cgo), const AF_IPX = 23
pkg syscall (darwin-arm64-cgo), const AF_IPX ideal-int
pkg syscall (darwin-arm64-cgo), const AF_ISDN = 28
pkg syscall (darwin-arm64-cgo), const AF_ISDN ideal-int
pkg syscall (darwin-arm64-cgo), const AF_ISO = 7
pkg syscall (darwin-arm64-cgo), const AF_ISO ideal-int
pkg syscall (darwin-arm64-cgo), const AF_LAT = 14
pkg syscall (darwin-arm64-cgo), const AF_LAT ideal-int
pkg syscall (darwin-arm64-cgo), const AF_LINK = 18
pkg syscall (darwin-arm64-cgo), const AF_LINK ideal-int
pkg syscall (darwin-arm64-cgo), const AF_LOCAL = 1
pkg syscall (darwin-arm64-cgo), const AF_LOCAL ideal-int
pkg syscall (darwin-arm64-cgo), const AF_MAX = 40
pkg syscall (darwin-arm64-cgo), const AF_MAX ideal-int
pkg syscall (darwin-arm64-cgo), const AF_NATM = 31
pkg syscall (darwin-arm64-cgo), const AF_NATM ideal-int
pkg syscall (darwin-arm64-cgo), const AF_NDRV = 27
pkg syscall (darwin-arm64-cgo), const AF_NDRV ideal-int
pkg syscall (darwin-arm64-cgo), const AF_NETBIOS = 33
pkg syscall (darwin-arm64-cgo), const AF_NETBIOS ideal-int
pkg syscall (darwin-arm64-cgo), const AF_NS = 6
pkg syscall (darwin-arm64-cgo), const AF_NS ideal-int
pkg syscall (darwin-arm64-cgo), const AF_OSI = 7
pkg syscall (darwin-arm64-cgo), const AF_OSI ideal-int
pkg syscall (darwin-arm64-cgo), const AF_PPP = 34
pkg syscall (darwin-arm64-cgo), const AF_PPP ideal-int
pkg syscall (darwin-arm64-cgo), const AF_PUP = 4
pkg syscall (darwin-arm64-cgo), const AF_PUP ideal-int
pkg syscall (darwin-arm64-cgo), const AF_RESERVED_36 = 36
pkg syscall (darwin-arm64-cgo), const AF_RESERVED_36 ideal-int
pkg syscall (darwin-arm64-cgo), const AF_ROUTE = 17
pkg syscall (darwin-arm64-cgo), const AF_ROUTE ideal-int
pkg syscall (darwin-arm64-cgo), const AF_SIP = 24
pkg syscall (darwin-arm64-cgo), const AF_SIP ideal-int
pkg syscall (darwin-arm64-cgo), const AF_SNA = 11
pkg syscall (darwin-arm64-cgo), const AF_SNA ideal-int
pkg syscall (darwin-arm64-cgo), const AF_SYSTEM = 32
pkg syscall (darwin-arm64-cgo), const AF_SYSTEM ideal-int
pkg syscall (darwin-arm64-cgo), const AF_UTUN = 38
pkg syscall (darwin-arm64-cgo), const AF_UTUN ideal-int
pkg syscall (darwin-arm64-cgo), const B0 = 0
pkg syscall (darwin-arm64-cgo), const B0 ideal-int
pkg syscall (darwin-arm64-cgo), const B110 = 110
pkg syscall (darwin-arm64-cgo), const B110 ideal-int
pkg syscall (darwin-arm64-cgo), const B115200 = 115200
pkg syscall (darwin-arm64-cgo), const B115200 ideal-int
pkg syscall (darwin-arm64-cgo), const B1200 = 1200
pkg syscall (darwin-arm64-cgo), const B1200 ideal-int
pkg syscall (darwin-arm64-cgo), const B134 = 134
pkg syscall (darwin-arm64-cgo), const B134 ideal-int
pkg syscall (darwin-arm64-cgo), const B14400 = 14400
pkg syscall (darwin-arm64-cgo), const B14400 ideal-int
pkg syscall (darwin-arm64-cgo), const B150 = 150
pkg syscall (darwin-arm64-cgo), const B150 ideal-int
pkg syscall (darwin-arm64-cgo), const B1800 = 1800
pkg syscall (darwin-arm64-cgo), const B1800 ideal-int
pkg syscall (darwin-arm64-cgo), const B19200 = 19200
pkg syscall (darwin-arm64-cgo), const B19200 ideal-int
pkg syscall (darwin-arm64-cgo), const B200 = 200
pkg syscall (darwin-arm64-cgo), const B200 ideal-int
pkg syscall (darwin-arm64-cgo), const B230400 = 230400
pkg syscall (darwin-arm64-cgo), const B230400 ideal-int
pkg syscall (darwin-arm64-cgo), const B2400 = 2400
pkg syscall (darwin-arm64-cgo), const B2400 ideal-int
pkg syscall (darwin-arm64-cgo), const B28800 = 28800
pkg syscall (darwin-arm64-cgo), const B28800 ideal-int
pkg syscall (darwin-arm64-cgo), const B300 = 300
pkg syscall (darwin-arm64-cgo), const B300 ideal-int
pkg syscall (darwin-arm64-cgo), const B38400 = 38400
pkg syscall (darwin-arm64-cgo), const B38400 ideal-int
pkg syscall (darwin-arm64-cgo), const B4800 = 4800
pkg syscall (darwin-arm64-cgo), const B4800 ideal-int
pkg syscall (darwin-arm64-cgo), const B50 = 50
pkg syscall (darwin-arm64-cgo), const B50 ideal-int
pkg syscall (darwin-arm64-cgo), const B57600 = 57600
pkg syscall (darwin-arm64-cgo), const B57600 ideal-int
pkg syscall (darwin-arm64-cgo), const B600 = 600
pkg syscall (darwin-arm64-cgo), const B600 ideal-int
pkg syscall (darwin-arm64-cgo), const B7200 = 7200
pkg syscall (darwin-arm64-cgo), const B7200 ideal-int
pkg syscall (darwin-arm64-cgo), const B75 = 75
pkg syscall (darwin-arm64-cgo), const B75 ideal-int
pkg syscall (darwin-arm64-cgo), const B76800 = 76800
pkg syscall (darwin-arm64-cgo), const B76800 ideal-int
pkg syscall (darwin-arm64-cgo), const B9600 = 9600
pkg syscall (darwin-arm64-cgo), const B9600 ideal-int
pkg syscall (darwin-arm64-cgo), const BIOCFLUSH = 536887912
pkg syscall (darwin-arm64-cgo), const BIOCFLUSH ideal-int
pkg syscall (darwin-arm64-cgo), const BIOCGBLEN = 1074020966
pkg syscall (darwin-arm64-cgo), const BIOCGBLEN ideal-int
pkg syscall (darwin-arm64-cgo), const BIOCGDLT = 1074020970
pkg syscall (darwin-arm64-cgo), const BIOCGDLT ideal-int
pkg syscall (darwin-arm64-cgo), const BIOCGDLTLIST = 3222028921
pkg syscall (darwin-arm64-cgo), const BIOCGDLTLIST ideal-int
pkg syscall (darwin-arm64-cgo), const BIOCGETIF = 1075855979
pkg syscall (darwin-arm64-cgo), const BIOCGETIF ideal-int
pkg syscall (darwin-arm64-cgo), const BIOCGHDRCMPLT = 1074020980
pkg syscall (darwin-arm64-cgo), const BIOCGHDRCMPLT ideal-int
pkg syscall (darwin-arm64-cgo), const BIOCGRSIG = 1074020978
pkg syscall (darwin-arm64-cgo), const BIOCGRSIG ideal-int
pkg syscall (darwin-arm64-cgo), const BIOCGRTIMEOUT = 1074807406
pkg syscall (darwin-arm64-cgo), const BIOCGRTIMEOUT ideal-int
pkg syscall (darwin-arm64-cgo), const BIOCGSEESENT = 1074020982
pkg syscall (darwin-arm64-cgo), const BIOCGSEESENT ideal-int
pkg syscall (darwin-arm64-cgo), const BIOCGSTATS = 1074283119
pkg syscall (darwin-arm64-cgo), const BIOCGSTATS ideal-int
pkg syscall (darwin-arm64-cgo), const BIOCIMMEDIATE = 2147762800
pkg syscall (darwin-arm64-cgo), const BIOCIMMEDIATE ideal-int
pkg syscall (darwin-arm64-cgo), const BIOCPROMISC = 536887913
pkg syscall (darwin-arm64-cgo), const BIOCPROMISC ideal-int
pkg syscall (darwin-arm64-cgo), const BIOCSBLEN = 3221504614
pkg syscall (darwin-arm64-cgo), const BIOCSBLEN ideal-int
pkg syscall (darwin-arm64-cgo), const BIOCSDLT = 2147762808
pkg syscall (darwin-arm64-cgo), const BIOCSDLT ideal-int
pkg syscall (darwin-arm64-cgo), const BIOCSETF = 2148549223
pkg syscall (darwin-arm64-cgo), const BIOCSETF ideal-int
pkg syscall (darwin-arm64-cgo), const BIOCSETIF = 2149597804
pkg syscall (darwin-arm64-cgo), const BIOCSETIF ideal-int
pkg syscall (darwin-arm64-cgo), const BIOCSHDRCMPLT = 2147762805
pkg syscall (darwin-arm64-cgo), const BIOCSHDRCMPLT ideal-int
pkg syscall (darwin-arm64-cgo), const BIOCSRSIG = 2147762803
pkg syscall (darwin-arm64-cgo), const BIOCSRSIG ideal-int
pkg syscall (darwin-arm64-cgo), const BIOCSRTIMEOUT = 2148549229
pkg syscall (darwin-arm64-cgo), const BIOCSRTIMEOUT ideal-int
pkg syscall (darwin-arm64-cgo), const BIOCSSEESENT = 2147762807
pkg syscall (darwin-arm64-cgo), const BIOCSSEESENT ideal-int
pkg syscall (darwin-arm64-cgo), const BIOCVERSION = 1074020977
pkg syscall (darwin-arm64-cgo), const BIOCVERSION ideal-int
pkg syscall (darwin-arm64-cgo), const BPF_A = 16
pkg syscall (darwin-arm64-cgo), const BPF_A ideal-int
pkg syscall (darwin-arm64-cgo), const BPF_ABS = 32
pkg syscall (darwin-arm64-cgo), const BPF_ABS ideal-int
pkg syscall (darwin-arm64-cgo), const BPF_ADD = 0
pkg syscall (darwin-arm64-cgo), const BPF_ADD ideal-int
pkg syscall (darwin-arm64-cgo), const BPF_ALIGNMENT = 4
pkg syscall (darwin-arm64-cgo), const BPF_ALIGNMENT ideal-int
pkg syscall (darwin-arm64-cgo), const BPF_ALU = 4
pkg syscall (darwin-arm64-cgo), const BPF_ALU ideal-int
pkg syscall (darwin-arm64-cgo), const BPF_AND = 80
pkg syscall (darwin-arm64-cgo), const BPF_AND ideal-int
pkg syscall (darwin-arm64-cgo), const BPF_B = 16
pkg syscall (darwin-arm64-cgo), const BPF_B ideal-int
pkg syscall (darwin-arm64-cgo), const BPF_DIV = 48
pkg syscall (darwin-arm64-cgo), const BPF_DIV ideal-int
pkg syscall (darwin-arm64-cgo), const BPF_H = 8
pkg syscall (darwin-arm64-cgo), const BPF_H ideal-int
pkg syscall (darwin-arm64-cgo), const BPF_IMM = 0
pkg syscall (darwin-arm64-cgo), const BPF_IMM ideal-int
pkg syscall (darwin-arm64-cgo), const BPF_IND = 64
pkg syscall (darwin-arm64-cgo), const BPF_IND ideal-int
pkg syscall (darwin-arm64-cgo), const BPF_JA = 0
pkg syscall (darwin-arm64-cgo), const BPF_JA ideal-int
pkg syscall (darwin-arm64-cgo), const BPF_JEQ = 16
pkg syscall (darwin-arm64-cgo), const BPF_JEQ ideal-int
pkg syscall (darwin-arm64-cgo), const BPF_JGE = 48
pkg syscall (darwin-arm64-cgo), const BPF_JGE ideal-int
pkg syscall (darwin-arm64-cgo), const BPF_JGT = 32
pkg syscall (darwin-arm64-cgo), const BPF_JGT ideal-int
pkg syscall (darwin-arm64-cgo), const BPF_JMP = 5
pkg syscall (darwin-arm64-cgo), const BPF_JMP ideal-int
pkg syscall (darwin-arm64-cgo), const BPF_JSET = 64
pkg syscall (darwin-arm64-cgo), const BPF_JSET ideal-int
pkg syscall (darwin-arm64-cgo), const BPF_K = 0
pkg syscall (darwin-arm64-cgo), const BPF_K ideal-int
pkg syscall (darwin-arm64-cgo), const BPF_LD = 0
pkg syscall (darwin-arm64-cgo), const BPF_LD ideal-int
pkg syscall (darwin-arm64-cgo), const BPF_LDX = 1
pkg syscall (darwin-arm64-cgo), const BPF_LDX ideal-int
pkg syscall (darwin-arm64-cgo), const BPF_LEN = 128
pkg syscall (darwin-arm64-cgo), const BPF_LEN ideal-int
pkg syscall (darwin-arm64-cgo), const BPF_LSH = 96
pkg syscall (darwin-arm64-cgo), const BPF_LSH ideal-int
pkg syscall (darwin-arm64-cgo), const BPF_MAJOR_VERSION = 1
pkg syscall (darwin-arm64-cgo), const BPF_MAJOR_VERSION ideal-int
pkg syscall (darwin-arm64-cgo), const BPF_MAXBUFSIZE = 524288
pkg syscall (darwin-arm64-cgo), const BPF_MAXBUFSIZE ideal-int
pkg syscall (darwin-arm64-cgo), const BPF_MAXINSNS = 512
pkg syscall (darwin-arm64-cgo), const BPF_MAXINSNS ideal-int
pkg syscall (darwin-arm64-cgo), const BPF_MEM = 96
pkg syscall (darwin-arm64-cgo), const BPF_MEM ideal-int
pkg syscall (darwin-arm64-cgo), const BPF_MEMWORDS = 16
pkg syscall (darwin-arm64-cgo), const BPF_MEMWORDS ideal-int
pkg syscall (darwin-arm64-cgo), const BPF_MINBUFSIZE = 32
pkg syscall (darwin-arm64-cgo), const BPF_MINBUFSIZE ideal-int
pkg syscall (darwin-arm64-cgo), const BPF_MINOR_VERSION = 1
pkg syscall (darwin-arm64-cgo), const BPF_MINOR_VERSION ideal-int
pkg syscall (darwin-arm64-cgo), const BPF_MISC = 7
pkg syscall (darwin-arm64-cgo), const BPF_MISC ideal-int
pkg syscall (darwin-arm64-cgo), const BPF_MSH = 160
pkg syscall (darwin-arm64-cgo), const BPF_MSH ideal-int
pkg syscall (darwin-arm64-cgo), const BPF_MUL = 32
pkg syscall (darwin-arm64-cgo), const BPF_MUL ideal-int
pkg syscall (darwin-arm64-cgo), const BPF_NEG = 128
pkg syscall (darwin-arm64-cgo), const BPF_NEG ideal-int
pkg syscall (darwin-arm64-cgo), const BPF_OR = 64
pkg syscall (darwin-arm64-cgo), const BPF_OR ideal-int
pkg syscall (darwin-arm64-cgo), const BPF_RELEASE = 199606
pkg syscall (darwin-arm64-cgo), const BPF_RELEASE ideal-int
pkg syscall (darwin-arm64-cgo), const BPF_RET = 6
pkg syscall (darwin-arm64-cgo), const BPF_RET ideal-int
pkg syscall (darwin-arm64-cgo), const BPF_RSH = 112
pkg syscall (darwin-arm64-cgo), const BPF_RSH ideal-int
pkg syscall (darwin-arm64-cgo), const BPF_ST = 2
pkg syscall (darwin-arm64-cgo), const BPF_ST ideal-int
pkg syscall (darwin-arm64-cgo), const BPF_STX = 3
pkg syscall (darwin-arm64-cgo), const BPF_STX ideal-int
pkg syscall (darwin-arm64-cgo), const BPF_SUB = 16
pkg syscall (darwin-arm64-cgo), const BPF_SUB ideal-int
pkg syscall (darwin-arm64-cgo), const BPF_TAX = 0
pkg syscall (darwin-arm64-cgo), const BPF_TAX ideal-int
pkg syscall (darwin-arm64-cgo), const BPF_TXA = 128
pkg syscall (darwin-arm64-cgo), const BPF_TXA ideal-int
pkg syscall (darwin-arm64-cgo), const BPF_W = 0
pkg syscall (darwin-arm64-cgo), const BPF_W ideal-int
pkg syscall (darwin-arm64-cgo), const BPF_X = 8
pkg syscall (darwin-arm64-cgo), const BPF_X ideal-int
pkg syscall (darwin-arm64-cgo), const BRKINT = 2
pkg syscall (darwin-arm64-cgo), const BRKINT ideal-int
pkg syscall (darwin-arm64-cgo), const CFLUSH = 15
pkg syscall (darwin-arm64-cgo), const CFLUSH ideal-int
pkg syscall (darwin-arm64-cgo), const CLOCAL = 32768
pkg syscall (darwin-arm64-cgo), const CLOCAL ideal-int
pkg syscall (darwin-arm64-cgo), const CREAD = 2048
pkg syscall (darwin-arm64-cgo), const CREAD ideal-int
pkg syscall (darwin-arm64-cgo), const CS5 = 0
pkg syscall (darwin-arm64-cgo), const CS5 ideal-int
pkg syscall (darwin-arm64-cgo), const CS6 = 256
pkg syscall (darwin-arm64-cgo), const CS6 ideal-int
pkg syscall (darwin-arm64-cgo), const CS7 = 512
pkg syscall (darwin-arm64-cgo), const CS7 ideal-int
pkg syscall (darwin-arm64-cgo), const CS8 = 768
pkg syscall (darwin-arm64-cgo), const CS8 ideal-int
pkg syscall (darwin-arm64-cgo), const CSIZE = 768
pkg syscall (darwin-arm64-cgo), const CSIZE ideal-int
pkg syscall (darwin-arm64-cgo), const CSTART = 17
pkg syscall (darwin-arm64-cgo), const CSTART ideal-int
pkg syscall (darwin-arm64-cgo), const CSTATUS = 20
pkg syscall (darwin-arm64-cgo), const CSTATUS ideal-int
pkg syscall (darwin-arm64-cgo), const CSTOP = 19
pkg syscall (darwin-arm64-cgo), const CSTOP ideal-int
pkg syscall (darwin-arm64-cgo), const CSTOPB = 1024
pkg syscall (darwin-arm64-cgo), const CSTOPB ideal-int
pkg syscall (darwin-arm64-cgo), const CSUSP = 26
pkg syscall (darwin-arm64-cgo), const CSUSP ideal-int
pkg syscall (darwin-arm64-cgo), const CTL_MAXNAME = 12
pkg syscall (darwin-arm64-cgo), const CTL_MAXNAME ideal-int
pkg syscall (darwin-arm64-cgo), const CTL_NET = 4
pkg syscall (darwin-arm64-cgo), const CTL_NET ideal-int
pkg syscall (darwin-arm64-cgo), const DLT_APPLE_IP_OVER_IEEE1394 = 138
pkg syscall (darwin-arm64-cgo), const DLT_APPLE_IP_OVER_IEEE1394 ideal-int
pkg syscall (darwin-arm64-cgo), const DLT_ARCNET = 7
pkg syscall (darwin-arm64-cgo), const DLT_ARCNET ideal-int
pkg syscall (darwin-arm64-cgo), const DLT_ATM_CLIP = 19
pkg syscall (darwin-arm64-cgo), const DLT_ATM_CLIP ideal-int
pkg syscall (darwin-arm64-cgo), const DLT_ATM_RFC1483 = 11
pkg syscall (darwin-arm64-cgo), const DLT_ATM_RFC1483 ideal-int
pkg syscall (darwin-arm64-cgo), const DLT_AX25 = 3
pkg syscall (darwin-arm64-cgo), const DLT_AX25 ideal-int
pkg syscall (darwin-arm64-cgo), const DLT_CHAOS = 5
pkg syscall (darwin-arm64-cgo), const DLT_CHAOS ideal-int
pkg syscall (darwin-arm64-cgo), const DLT_CHDLC = 104
pkg syscall (darwin-arm64-cgo), const DLT_CHDLC ideal-int
pkg syscall (darwin-arm64-cgo), const DLT_C_HDLC = 104
pkg syscall (darwin-arm64-cgo), const DLT_C_HDLC ideal-int
pkg syscall (darwin-arm64-cgo), const DLT_EN10MB = 1
pkg syscall (darwin-arm64-cgo), const DLT_EN10MB ideal-int
pkg syscall (darwin-arm64-cgo), const DLT_EN3MB = 2
pkg syscall (darwin-arm64-cgo), const DLT_EN3MB ideal-int
pkg syscall (darwin-arm64-cgo), const DLT_FDDI = 10
pkg syscall (darwin-arm64-cgo), const DLT_FDDI ideal-int
pkg syscall (darwin-arm64-cgo), const DLT_IEEE802 = 6
pkg syscall (darwin-arm64-cgo), const DLT_IEEE802 ideal-int
pkg syscall (darwin-arm64-cgo), const DLT_IEEE802_11 = 105
pkg syscall (darwin-arm64-cgo), const DLT_IEEE802_11 ideal-int
pkg syscall (darwin-arm64-cgo), const DLT_IEEE802_11_RADIO = 127
pkg syscall (darwin-arm64-cgo), const DLT_IEEE802_11_RADIO ideal-int
pkg syscall (darwin-arm64-cgo), const DLT_IEEE802_11_RADIO_AVS = 163
pkg syscall (darwin-arm64-cgo), const DLT_IEEE802_11_RADIO_AVS ideal-int
pkg syscall (darwin-arm64-cgo), const DLT_LINUX_SLL = 113
pkg syscall (darwin-arm64-cgo), const DLT_LINUX_SLL ideal-int
pkg syscall (darwin-arm64-cgo), const DLT_LOOP = 108
pkg syscall (darwin-arm64-cgo), const DLT_LOOP ideal-int
pkg syscall (darwin-arm64-cgo), const DLT_NULL = 0
pkg syscall (darwin-arm64-cgo), const DLT_NULL ideal-int
pkg syscall (darwin-arm64-cgo), const DLT_PFLOG = 117
pkg syscall (darwin-arm64-cgo), const DLT_PFLOG ideal-int
pkg syscall (darwin-arm64-cgo), const DLT_PFSYNC = 18
pkg syscall (darwin-arm64-cgo), const DLT_PFSYNC ideal-int
pkg syscall (darwin-arm64-cgo), const DLT_PPP = 9
pkg syscall (darwin-arm64-cgo), const DLT_PPP ideal-int
pkg syscall (darwin-arm64-cgo), const DLT_PPP_BSDOS = 16
pkg syscall (darwin-arm64-cgo), const DLT_PPP_BSDOS ideal-int
pkg syscall (darwin-arm64-cgo), const DLT_PPP_SERIAL = 50
pkg syscall (darwin-arm64-cgo), const DLT_PPP_SERIAL ideal-int
pkg syscall (darwin-arm64-cgo), const DLT_PRONET = 4
pkg syscall (darwin-arm64-cgo), const DLT_PRONET ideal-int
pkg syscall (darwin-arm64-cgo), const DLT_RAW = 12
pkg syscall (darwin-arm64-cgo), const DLT_RAW ideal-int
pkg syscall (darwin-arm64-cgo), const DLT_SLIP = 8
pkg syscall (darwin-arm64-cgo), const DLT_SLIP ideal-int
pkg syscall (darwin-arm64-cgo), const DLT_SLIP_BSDOS = 15
pkg syscall (darwin-arm64-cgo), const DLT_SLIP_BSDOS ideal-int
pkg syscall (darwin-arm64-cgo), const DT_BLK = 6
pkg syscall (darwin-arm64-cgo), const DT_BLK ideal-int
pkg syscall (darwin-arm64-cgo), const DT_CHR = 2
pkg syscall (darwin-arm64-cgo), const DT_CHR ideal-int
pkg syscall (darwin-arm64-cgo), const DT_DIR = 4
pkg syscall (darwin-arm64-cgo), const DT_DIR ideal-int
pkg syscall (darwin-arm64-cgo), const DT_FIFO = 1
pkg syscall (darwin-arm64-cgo), const DT_FIFO ideal-int
pkg syscall (darwin-arm64-cgo), const DT_LNK = 10
pkg syscall (darwin-arm64-cgo), const DT_LNK ideal-int
pkg syscall (darwin-arm64-cgo), const DT_REG = 8
pkg syscall (darwin-arm64-cgo), const DT_REG ideal-int
pkg syscall (darwin-arm64-cgo), const DT_SOCK = 12
pkg syscall (darwin-arm64-cgo), const DT_SOCK ideal-int
pkg syscall (darwin-arm64-cgo), const DT_UNKNOWN = 0
pkg syscall (darwin-arm64-cgo), const DT_UNKNOWN ideal-int
pkg syscall (darwin-arm64-cgo), const DT_WHT = 14
pkg syscall (darwin-arm64-cgo), const DT_WHT ideal-int
pkg syscall (darwin-arm64-cgo), const E2BIG = 7
pkg syscall (darwin-arm64-cgo), const EACCES = 13
pkg syscall (darwin-arm64-cgo), const EADDRINUSE = 48
pkg syscall (darwin-arm64-cgo), const EADDRNOTAVAIL = 49
pkg syscall (darwin-arm64-cgo), const EAFNOSUPPORT = 47
pkg syscall (darwin-arm64-cgo), const EAGAIN = 35
pkg syscall (darwin-arm64-cgo), const EALREADY = 37
pkg syscall (darwin-arm64-cgo), const EAUTH = 80
pkg syscall (darwin-arm64-cgo), const EAUTH Errno
pkg syscall (darwin-arm64-cgo), const EBADARCH = 86
pkg syscall (darwin-arm64-cgo), const EBADARCH Errno
pkg syscall (darwin-arm64-cgo), const EBADEXEC = 85
pkg syscall (darwin-arm64-cgo), const EBADEXEC Errno
pkg syscall (darwin-arm64-cgo), const EBADF = 9
pkg syscall (darwin-arm64-cgo), const EBADMACHO = 88
pkg syscall (darwin-arm64-cgo), const EBADMACHO Errno
pkg syscall (darwin-arm64-cgo), const EBADMSG = 94
pkg syscall (darwin-arm64-cgo), const EBADMSG Errno
pkg syscall (darwin-arm64-cgo), const EBADRPC = 72
pkg syscall (darwin-arm64-cgo), const EBADRPC Errno
pkg syscall (darwin-arm64-cgo), const EBUSY = 16
pkg syscall (darwin-arm64-cgo), const ECANCELED = 89
pkg syscall (darwin-arm64-cgo), const ECHILD = 10
pkg syscall (darwin-arm64-cgo), const ECHO = 8
pkg syscall (darwin-arm64-cgo), const ECHO ideal-int
pkg syscall (darwin-arm64-cgo), const ECHOCTL = 64
pkg syscall (darwin-arm64-cgo), const ECHOCTL ideal-int
pkg syscall (darwin-arm64-cgo), const ECHOE = 2
pkg syscall (darwin-arm64-cgo), const ECHOE ideal-int
pkg syscall (darwin-arm64-cgo), const ECHOK = 4
pkg syscall (darwin-arm64-cgo), const ECHOK ideal-int
pkg syscall (darwin-arm64-cgo), const ECHOKE = 1
pkg syscall (darwin-arm64-cgo), const ECHOKE ideal-int
pkg syscall (darwin-arm64-cgo), const ECHONL = 16
pkg syscall (darwin-arm64-cgo), const ECHONL ideal-int
pkg syscall (darwin-arm64-cgo), const ECHOPRT = 32
pkg syscall (darwin-arm64-cgo), const ECHOPRT ideal-int
pkg syscall (darwin-arm64-cgo), const ECONNABORTED = 53
pkg syscall (darwin-arm64-cgo), const ECONNREFUSED = 61
pkg syscall (darwin-arm64-cgo), const ECONNRESET = 54
pkg syscall (darwin-arm64-cgo), const EDEADLK = 11
pkg syscall (darwin-arm64-cgo), const EDESTADDRREQ = 39
pkg syscall (darwin-arm64-cgo), const EDEVERR = 83
pkg syscall (darwin-arm64-cgo), const EDEVERR Errno
pkg syscall (darwin-arm64-cgo), const EDOM = 33
pkg syscall (darwin-arm64-cgo), const EDQUOT = 69
pkg syscall (darwin-arm64-cgo), const EEXIST = 17
pkg syscall (darwin-arm64-cgo), const EFAULT = 14
pkg syscall (darwin-arm64-cgo), const EFBIG = 27
pkg syscall (darwin-arm64-cgo), const EFTYPE = 79
pkg syscall (darwin-arm64-cgo), const EFTYPE Errno
pkg syscall (darwin-arm64-cgo), const EHOSTDOWN = 64
pkg syscall (darwin-arm64-cgo), const EHOSTUNREACH = 65
pkg syscall (darwin-arm64-cgo), const EIDRM = 90
pkg syscall (darwin-arm64-cgo), const EILSEQ = 92
pkg syscall (darwin-arm64-cgo), const EINPROGRESS = 36
pkg syscall (darwin-arm64-cgo), const EINTR = 4
pkg syscall (darwin-arm64-cgo), const EINVAL = 22
pkg syscall (darwin-arm64-cgo), const EIO = 5
pkg syscall (darwin-arm64-cgo), const EISCONN = 56
pkg syscall (darwin-arm64-cgo), const EISDIR = 21
pkg syscall (darwin-arm64-cgo), const ELAST = 106
pkg syscall (darwin-arm64-cgo), const ELAST Errno
pkg syscall (darwin-arm64-cgo), const ELOOP = 62
pkg syscall (darwin-arm64-cgo), const EMFILE = 24
pkg syscall (darwin-arm64-cgo), const EMLINK = 31
pkg syscall (darwin-arm64-cgo), const EMSGSIZE = 40
pkg syscall (darwin-arm64-cgo), const EMULTIHOP = 95
pkg syscall (darwin-arm64-cgo), const EMULTIHOP Errno
pkg syscall (darwin-arm64-cgo), const ENAMETOOLONG = 63
pkg syscall (darwin-arm64-cgo), const ENEEDAUTH = 81
pkg syscall (darwin-arm64-cgo), const ENEEDAUTH Errno
pkg syscall (darwin-arm64-cgo), const ENETDOWN = 50
pkg syscall (darwin-arm64-cgo), const ENETRESET = 52
pkg syscall (darwin-arm64-cgo), const ENETUNREACH = 51
pkg syscall (darwin-arm64-cgo), const ENFILE = 23
pkg syscall (darwin-arm64-cgo), const ENOATTR = 93
pkg syscall (darwin-arm64-cgo), const ENOATTR Errno
pkg syscall (darwin-arm64-cgo), const ENOBUFS = 55
pkg syscall (darwin-arm64-cgo), const ENODATA = 96
pkg syscall (darwin-arm64-cgo), const ENODATA Errno
pkg syscall (darwin-arm64-cgo), const ENODEV = 19
pkg syscall (darwin-arm64-cgo), const ENOEXEC = 8
pkg syscall (darwin-arm64-cgo), const ENOLCK = 77
pkg syscall (darwin-arm64-cgo), const ENOLINK = 97
pkg syscall (darwin-arm64-cgo), const ENOLINK Errno
pkg syscall (darwin-arm64-cgo), const ENOMEM = 12
pkg syscall (darwin-arm64-cgo), const ENOMSG = 91
pkg syscall (darwin-arm64-cgo), const ENOPOLICY = 103
pkg syscall (darwin-arm64-cgo), const ENOPOLICY Errno
pkg syscall (darwin-arm64-cgo), const ENOPROTOOPT = 42
pkg syscall (darwin-arm64-cgo), const ENOSPC = 28
pkg syscall (darwin-arm64-cgo), const ENOSR = 98
pkg syscall (darwin-arm64-cgo), const ENOSR Errno
pkg syscall (darwin-arm64-cgo), const ENOSTR = 99
pkg syscall (darwin-arm64-cgo), const ENOSTR Errno
pkg syscall (darwin-arm64-cgo), const ENOSYS = 78
pkg syscall (darwin-arm64-cgo), const ENOTBLK = 15
pkg syscall (darwin-arm64-cgo), const ENOTCONN = 57
pkg syscall (darwin-arm64-cgo), const ENOTDIR = 20
pkg syscall (darwin-arm64-cgo), const ENOTEMPTY = 66
pkg syscall (darwin-arm64-cgo), const ENOTRECOVERABLE = 104
pkg syscall (darwin-arm64-cgo), const ENOTRECOVERABLE Errno
pkg syscall (darwin-arm64-cgo), const ENOTSOCK = 38
pkg syscall (darwin-arm64-cgo), const ENOTSUP = 45
pkg syscall (darwin-arm64-cgo), const ENOTTY = 25
pkg syscall (darwin-arm64-cgo), const ENXIO = 6
pkg syscall (darwin-arm64-cgo), const EOPNOTSUPP = 102
pkg syscall (darwin-arm64-cgo), const EOVERFLOW = 84
pkg syscall (darwin-arm64-cgo), const EOWNERDEAD = 105
pkg syscall (darwin-arm64-cgo), const EOWNERDEAD Errno
pkg syscall (darwin-arm64-cgo), const EPERM = 1
pkg syscall (darwin-arm64-cgo), const EPFNOSUPPORT = 46
pkg syscall (darwin-arm64-cgo), const EPIPE = 32
pkg syscall (darwin-arm64-cgo), const EPROCLIM = 67
pkg syscall (darwin-arm64-cgo), const EPROCLIM Errno
pkg syscall (darwin-arm64-cgo), const EPROCUNAVAIL = 76
pkg syscall (darwin-arm64-cgo), const EPROCUNAVAIL Errno
pkg syscall (darwin-arm64-cgo), const EPROGMISMATCH = 75
pkg syscall (darwin-arm64-cgo), const EPROGMISMATCH Errno
pkg syscall (darwin-arm64-cgo), const EPROGUNAVAIL = 74
pkg syscall (darwin-arm64-cgo), const EPROGUNAVAIL Errno
pkg syscall (darwin-arm64-cgo), const EPROTO = 100
pkg syscall (darwin-arm64-cgo), const EPROTO Errno
pkg syscall (darwin-arm64-cgo), const EPROTONOSUPPORT = 43
pkg syscall (darwin-arm64-cgo), const EPROTOTYPE = 41
pkg syscall (darwin-arm64-cgo), const EPWROFF = 82
pkg syscall (darwin-arm64-cgo), const EPWROFF Errno
pkg syscall (darwin-arm64-cgo), const EQFULL = 106
pkg syscall (darwin-arm64-cgo), const EQFULL Errno
pkg syscall (darwin-arm64-cgo), const ERANGE = 34
pkg syscall (darwin-arm64-cgo), const EREMOTE = 71
pkg syscall (darwin-arm64-cgo), const EROFS = 30
pkg syscall (darwin-arm64-cgo), const ERPCMISMATCH = 73
pkg syscall (darwin-arm64-cgo), const ERPCMISMATCH Errno
pkg syscall (darwin-arm64-cgo), const ESHLIBVERS = 87
pkg syscall (darwin-arm64-cgo), const ESHLIBVERS Errno
pkg syscall (darwin-arm64-cgo), const ESHUTDOWN = 58
pkg syscall (darwin-arm64-cgo), const ESOCKTNOSUPPORT = 44
pkg syscall (darwin-arm64-cgo), const ESPIPE = 29
pkg syscall (darwin-arm64-cgo), const ESRCH = 3
pkg syscall (darwin-arm64-cgo), const ESTALE = 70
pkg syscall (darwin-arm64-cgo), const ETIME = 101
pkg syscall (darwin-arm64-cgo), const ETIME Errno
pkg syscall (darwin-arm64-cgo), const ETIMEDOUT = 60
pkg syscall (darwin-arm64-cgo), const ETOOMANYREFS = 59
pkg syscall (darwin-arm64-cgo), const ETXTBSY = 26
pkg syscall (darwin-arm64-cgo), const EUSERS = 68
pkg syscall (darwin-arm64-cgo), const EVFILT_AIO = -3
pkg syscall (darwin-arm64-cgo), const EVFILT_AIO ideal-int
pkg syscall (darwin-arm64-cgo), const EVFILT_FS = -9
pkg syscall (darwin-arm64-cgo), const EVFILT_FS ideal-int
pkg syscall (darwin-arm64-cgo), const EVFILT_MACHPORT = -8
pkg syscall (darwin-arm64-cgo), const EVFILT_MACHPORT ideal-int
pkg syscall (darwin-arm64-cgo), const EVFILT_PROC = -5
pkg syscall (darwin-arm64-cgo), const EVFILT_PROC ideal-int
pkg syscall (darwin-arm64-cgo), const EVFILT_READ = -1
pkg syscall (darwin-arm64-cgo), const EVFILT_READ ideal-int
pkg syscall (darwin-arm64-cgo), const EVFILT_SIGNAL = -6
pkg syscall (darwin-arm64-cgo), const EVFILT_SIGNAL ideal-int
pkg syscall (darwin-arm64-cgo), const EVFILT_SYSCOUNT = 14
pkg syscall (darwin-arm64-cgo), const EVFILT_SYSCOUNT ideal-int
pkg syscall (darwin-arm64-cgo), const EVFILT_THREADMARKER = 14
pkg syscall (darwin-arm64-cgo), const EVFILT_THREADMARKER ideal-int
pkg syscall (darwin-arm64-cgo), const EVFILT_TIMER = -7
pkg syscall (darwin-arm64-cgo), const EVFILT_TIMER ideal-int
pkg syscall (darwin-arm64-cgo), const EVFILT_USER = -10
pkg syscall (darwin-arm64-cgo), const EVFILT_USER ideal-int
pkg syscall (darwin-arm64-cgo), const EVFILT_VM = -12
pkg syscall (darwin-arm64-cgo), const EVFILT_VM ideal-int
pkg syscall (darwin-arm64-cgo), const EVFILT_VNODE = -4
pkg syscall (darwin-arm64-cgo), const EVFILT_VNODE ideal-int
pkg syscall (darwin-arm64-cgo), const EVFILT_WRITE = -2
pkg syscall (darwin-arm64-cgo), const EVFILT_WRITE ideal-int
pkg syscall (darwin-arm64-cgo), const EV_ADD = 1
pkg syscall (darwin-arm64-cgo), const EV_ADD ideal-int
pkg syscall (darwin-arm64-cgo), const EV_CLEAR = 32
pkg syscall (darwin-arm64-cgo), const EV_CLEAR ideal-int
pkg syscall (darwin-arm64-cgo), const EV_DELETE = 2
pkg syscall (darwin-arm64-cgo), const EV_DELETE ideal-int
pkg syscall (darwin-arm64-cgo), const EV_DISABLE = 8
pkg syscall (darwin-arm64-cgo), const EV_DISABLE ideal-int
pkg syscall (darwin-arm64-cgo), const EV_DISPATCH = 128
pkg syscall (darwin-arm64-cgo), const EV_DISPATCH ideal-int
pkg syscall (darwin-arm64-cgo), const EV_ENABLE = 4
pkg syscall (darwin-arm64-cgo), const EV_ENABLE ideal-int
pkg syscall (darwin-arm64-cgo), const EV_EOF = 32768
pkg syscall (darwin-arm64-cgo), const EV_EOF ideal-int
pkg syscall (darwin-arm64-cgo), const EV_ERROR = 16384
pkg syscall (darwin-arm64-cgo), const EV_ERROR ideal-int
pkg syscall (darwin-arm64-cgo), const EV_FLAG0 = 4096
pkg syscall (darwin-arm64-cgo), const EV_FLAG0 ideal-int
pkg syscall (darwin-arm64-cgo), const EV_FLAG1 = 8192
pkg syscall (darwin-arm64-cgo), const EV_FLAG1 ideal-int
pkg syscall (darwin-arm64-cgo), const EV_ONESHOT = 16
pkg syscall (darwin-arm64-cgo), const EV_ONESHOT ideal-int
pkg syscall (darwin-arm64-cgo), const EV_OOBAND = 8192
pkg syscall (darwin-arm64-cgo), const EV_OOBAND ideal-int
pkg syscall (darwin-arm64-cgo), const EV_POLL = 4096
pkg syscall (darwin-arm64-cgo), const EV_POLL ideal-int
pkg syscall (darwin-arm64-cgo), const EV_RECEIPT = 64
pkg syscall (darwin-arm64-cgo), const EV_RECEIPT ideal-int
pkg syscall (darwin-arm64-cgo), const EV_SYSFLAGS = 61440
pkg syscall (darwin-arm64-cgo), const EV_SYSFLAGS ideal-int
pkg syscall (darwin-arm64-cgo), const EWOULDBLOCK = 35
pkg syscall (darwin-arm64-cgo), const EXDEV = 18
pkg syscall (darwin-arm64-cgo), const EXTA = 19200
pkg syscall (darwin-arm64-cgo), const EXTA ideal-int
pkg syscall (darwin-arm64-cgo), const EXTB = 38400
pkg syscall (darwin-arm64-cgo), const EXTB ideal-int
pkg syscall (darwin-arm64-cgo), const EXTPROC = 2048
pkg syscall (darwin-arm64-cgo), const EXTPROC ideal-int
pkg syscall (darwin-arm64-cgo), const FD_CLOEXEC = 1
pkg syscall (darwin-arm64-cgo), const FD_CLOEXEC ideal-int
pkg syscall (darwin-arm64-cgo), const FD_SETSIZE = 1024
pkg syscall (darwin-arm64-cgo), const FD_SETSIZE ideal-int
pkg syscall (darwin-arm64-cgo), const FLUSHO = 8388608
pkg syscall (darwin-arm64-cgo), const FLUSHO ideal-int
pkg syscall (darwin-arm64-cgo), const F_ADDFILESIGS = 61
pkg syscall (darwin-arm64-cgo), const F_ADDFILESIGS ideal-int
pkg syscall (darwin-arm64-cgo), const F_ADDSIGS = 59
pkg syscall (darwin-arm64-cgo), const F_ADDSIGS ideal-int
pkg syscall (darwin-arm64-cgo), const F_ALLOCATEALL = 4
pkg syscall (darwin-arm64-cgo), const F_ALLOCATEALL ideal-int
pkg syscall (darwin-arm64-cgo), const F_ALLOCATECONTIG = 2
pkg syscall (darwin-arm64-cgo), const F_ALLOCATECONTIG ideal-int
pkg syscall (darwin-arm64-cgo), const F_CHKCLEAN = 41
pkg syscall (darwin-arm64-cgo), const F_CHKCLEAN ideal-int
pkg syscall (darwin-arm64-cgo), const F_DUPFD = 0
pkg syscall (darwin-arm64-cgo), const F_DUPFD ideal-int
pkg syscall (darwin-arm64-cgo), const F_DUPFD_CLOEXEC = 67
pkg syscall (darwin-arm64-cgo), const F_DUPFD_CLOEXEC ideal-int
pkg syscall (darwin-arm64-cgo), const F_FINDSIGS = 78
pkg syscall (darwin-arm64-cgo), const F_FINDSIGS ideal-int
pkg syscall (darwin-arm64-cgo), const F_FLUSH_DATA = 40
pkg syscall (darwin-arm64-cgo), const F_FLUSH_DATA ideal-int
pkg syscall (darwin-arm64-cgo), const F_FREEZE_FS = 53
pkg syscall (darwin-arm64-cgo), const F_FREEZE_FS ideal-int
pkg syscall (darwin-arm64-cgo), const F_FULLFSYNC = 51
pkg syscall (darwin-arm64-cgo), const F_FULLFSYNC ideal-int
pkg syscall (darwin-arm64-cgo), const F_GETCODEDIR = 72
pkg syscall (darwin-arm64-cgo), const F_GETCODEDIR ideal-int
pkg syscall (darwin-arm64-cgo), const F_GETFD = 1
pkg syscall (darwin-arm64-cgo), const F_GETFD ideal-int
pkg syscall (darwin-arm64-cgo), const F_GETFL = 3
pkg syscall (darwin-arm64-cgo), const F_GETFL ideal-int
pkg syscall (darwin-arm64-cgo), const F_GETLK = 7
pkg syscall (darwin-arm64-cgo), const F_GETLK ideal-int
pkg syscall (darwin-arm64-cgo), const F_GETLKPID = 66
pkg syscall (darwin-arm64-cgo), const F_GETLKPID ideal-int
pkg syscall (darwin-arm64-cgo), const F_GETNOSIGPIPE = 74
pkg syscall (darwin-arm64-cgo), const F_GETNOSIGPIPE ideal-int
pkg syscall (darwin-arm64-cgo), const F_GETOWN = 5
pkg syscall (darwin-arm64-cgo), const F_GETOWN ideal-int
pkg syscall (darwin-arm64-cgo), const F_GETPATH = 50
pkg syscall (darwin-arm64-cgo), const F_GETPATH ideal-int
pkg syscall (darwin-arm64-cgo), const F_GETPATH_MTMINFO = 71
pkg syscall (darwin-arm64-cgo), const F_GETPATH_MTMINFO ideal-int
pkg syscall (darwin-arm64-cgo), const F_GETPROTECTIONCLASS = 63
pkg syscall (darwin-arm64-cgo), const F_GETPROTECTIONCLASS ideal-int
pkg syscall (darwin-arm64-cgo), const F_GETPROTECTIONLEVEL = 77
pkg syscall (darwin-arm64-cgo), const F_GETPROTECTIONLEVEL ideal-int
pkg syscall (darwin-arm64-cgo), const F_GLOBAL_NOCACHE = 55
pkg syscall (darwin-arm64-cgo), const F_GLOBAL_NOCACHE ideal-int
pkg syscall (darwin-arm64-cgo), const F_LOG2PHYS = 49
pkg syscall (darwin-arm64-cgo), const F_LOG2PHYS ideal-int
pkg syscall (darwin-arm64-cgo), const F_LOG2PHYS_EXT = 65
pkg syscall (darwin-arm64-cgo), const F_LOG2PHYS_EXT ideal-int
pkg syscall (darwin-arm64-cgo), const F_NOCACHE = 48
pkg syscall (darwin-arm64-cgo), const F_NOCACHE ideal-int
pkg syscall (darwin-arm64-cgo), const F_NODIRECT = 62
pkg syscall (darwin-arm64-cgo), const F_NODIRECT ideal-int
pkg syscall (darwin-arm64-cgo), const F_OK = 0
pkg syscall (darwin-arm64-cgo), const F_OK ideal-int
pkg syscall (darwin-arm64-cgo), const F_PATHPKG_CHECK = 52
pkg syscall (darwin-arm64-cgo), const F_PATHPKG_CHECK ideal-int
pkg syscall (darwin-arm64-cgo), const F_PEOFPOSMODE = 3
pkg syscall (darwin-arm64-cgo), const F_PEOFPOSMODE ideal-int
pkg syscall (darwin-arm64-cgo), const F_PREALLOCATE = 42
pkg syscall (darwin-arm64-cgo), const F_PREALLOCATE ideal-int
pkg syscall (darwin-arm64-cgo), const F_RDADVISE = 44
pkg syscall (darwin-arm64-cgo), const F_RDADVISE ideal-int
pkg syscall (darwin-arm64-cgo), const F_RDAHEAD = 45
pkg syscall (darwin-arm64-cgo), const F_RDAHEAD ideal-int
pkg syscall (darwin-arm64-cgo), const F_RDLCK = 1
pkg syscall (darwin-arm64-cgo), const F_RDLCK ideal-int
pkg syscall (darwin-arm64-cgo), const F_SETBACKINGSTORE = 70
pkg syscall (darwin-arm64-cgo), const F_SETBACKINGSTORE ideal-int
pkg syscall (darwin-arm64-cgo), const F_SETFD = 2
pkg syscall (darwin-arm64-cgo), const F_SETFD ideal-int
pkg syscall (darwin-arm64-cgo), const F_SETFL = 4
pkg syscall (darwin-arm64-cgo), const F_SETFL ideal-int
pkg syscall (darwin-arm64-cgo), const F_SETLK = 8
pkg syscall (darwin-arm64-cgo), const F_SETLK ideal-int
pkg syscall (darwin-arm64-cgo), const F_SETLKW = 9
pkg syscall (darwin-arm64-cgo), const F_SETLKW ideal-int
pkg syscall (darwin-arm64-cgo), const F_SETLKWTIMEOUT = 10
pkg syscall (darwin-arm64-cgo), const F_SETLKWTIMEOUT ideal-int
pkg syscall (darwin-arm64-cgo), const F_SETNOSIGPIPE = 73
pkg syscall (darwin-arm64-cgo), const F_SETNOSIGPIPE ideal-int
pkg syscall (darwin-arm64-cgo), const F_SETOWN = 6
pkg syscall (darwin-arm64-cgo), const F_SETOWN ideal-int
pkg syscall (darwin-arm64-cgo), const F_SETPROTECTIONCLASS = 64
pkg syscall (darwin-arm64-cgo), const F_SETPROTECTIONCLASS ideal-int
pkg syscall (darwin-arm64-cgo), const F_SETSIZE = 43
pkg syscall (darwin-arm64-cgo), const F_SETSIZE ideal-int
pkg syscall (darwin-arm64-cgo), const F_SINGLE_WRITER = 76
pkg syscall (darwin-arm64-cgo), const F_SINGLE_WRITER ideal-int
pkg syscall (darwin-arm64-cgo), const F_THAW_FS = 54
pkg syscall (darwin-arm64-cgo), const F_THAW_FS ideal-int
pkg syscall (darwin-arm64-cgo), const F_TRANSCODEKEY = 75
pkg syscall (darwin-arm64-cgo), const F_TRANSCODEKEY ideal-int
pkg syscall (darwin-arm64-cgo), const F_UNLCK = 2
pkg syscall (darwin-arm64-cgo), const F_UNLCK ideal-int
pkg syscall (darwin-arm64-cgo), const F_VOLPOSMODE = 4
pkg syscall (darwin-arm64-cgo), const F_VOLPOSMODE ideal-int
pkg syscall (darwin-arm64-cgo), const F_WRLCK = 3
pkg syscall (darwin-arm64-cgo), const F_WRLCK ideal-int
pkg syscall (darwin-arm64-cgo), const HUPCL = 16384
pkg syscall (darwin-arm64-cgo), const HUPCL ideal-int
pkg syscall (darwin-arm64-cgo), const ICANON = 256
pkg syscall (darwin-arm64-cgo), const ICANON ideal-int
pkg syscall (darwin-arm64-cgo), const ICMP6_FILTER = 18
pkg syscall (darwin-arm64-cgo), const ICMP6_FILTER ideal-int
pkg syscall (darwin-arm64-cgo), const ICRNL = 256
pkg syscall (darwin-arm64-cgo), const ICRNL ideal-int
pkg syscall (darwin-arm64-cgo), const IEXTEN = 1024
pkg syscall (darwin-arm64-cgo), const IEXTEN ideal-int
pkg syscall (darwin-arm64-cgo), const IFF_ALLMULTI = 512
pkg syscall (darwin-arm64-cgo), const IFF_ALLMULTI ideal-int
pkg syscall (darwin-arm64-cgo), const IFF_ALTPHYS = 16384
pkg syscall (darwin-arm64-cgo), const IFF_ALTPHYS ideal-int
pkg syscall (darwin-arm64-cgo), const IFF_DEBUG = 4
pkg syscall (darwin-arm64-cgo), const IFF_DEBUG ideal-int
pkg syscall (darwin-arm64-cgo), const IFF_LINK0 = 4096
pkg syscall (darwin-arm64-cgo), const IFF_LINK0 ideal-int
pkg syscall (darwin-arm64-cgo), const IFF_LINK1 = 8192
pkg syscall (darwin-arm64-cgo), const IFF_LINK1 ideal-int
pkg syscall (darwin-arm64-cgo), const IFF_LINK2 = 16384
pkg syscall (darwin-arm64-cgo), const IFF_LINK2 ideal-int
pkg syscall (darwin-arm64-cgo), const IFF_LOOPBACK = 8
pkg syscall (darwin-arm64-cgo), const IFF_MULTICAST = 32768
pkg syscall (darwin-arm64-cgo), const IFF_NOARP = 128
pkg syscall (darwin-arm64-cgo), const IFF_NOARP ideal-int
pkg syscall (darwin-arm64-cgo), const IFF_NOTRAILERS = 32
pkg syscall (darwin-arm64-cgo), const IFF_NOTRAILERS ideal-int
pkg syscall (darwin-arm64-cgo), const IFF_OACTIVE = 1024
pkg syscall (darwin-arm64-cgo), const IFF_OACTIVE ideal-int
pkg syscall (darwin-arm64-cgo), const IFF_POINTOPOINT = 16
pkg syscall (darwin-arm64-cgo), const IFF_POINTOPOINT ideal-int
pkg syscall (darwin-arm64-cgo), const IFF_PROMISC = 256
pkg syscall (darwin-arm64-cgo), const IFF_PROMISC ideal-int
pkg syscall (darwin-arm64-cgo), const IFF_RUNNING = 64
pkg syscall (darwin-arm64-cgo), const IFF_RUNNING ideal-int
pkg syscall (darwin-arm64-cgo), const IFF_SIMPLEX = 2048
pkg syscall (darwin-arm64-cgo), const IFF_SIMPLEX ideal-int
pkg syscall (darwin-arm64-cgo), const IFNAMSIZ = 16
pkg syscall (darwin-arm64-cgo), const IFNAMSIZ ideal-int
pkg syscall (darwin-arm64-cgo), const IFT_1822 = 2
pkg syscall (darwin-arm64-cgo), const IFT_1822 ideal-int
pkg syscall (darwin-arm64-cgo), const IFT_AAL5 = 49
pkg syscall (darwin-arm64-cgo), const IFT_AAL5 ideal-int
pkg syscall (darwin-arm64-cgo), const IFT_ARCNET = 35
pkg syscall (darwin-arm64-cgo), const IFT_ARCNET ideal-int
pkg syscall (darwin-arm64-cgo), const IFT_ARCNETPLUS = 36
pkg syscall (darwin-arm64-cgo), const IFT_ARCNETPLUS ideal-int
pkg syscall (darwin-arm64-cgo), const IFT_ATM = 37
pkg syscall (darwin-arm64-cgo), const IFT_ATM ideal-int
pkg syscall (darwin-arm64-cgo), const IFT_BRIDGE = 209
pkg syscall (darwin-arm64-cgo), const IFT_BRIDGE ideal-int
pkg syscall (darwin-arm64-cgo), const IFT_CARP = 248
pkg syscall (darwin-arm64-cgo), const IFT_CARP ideal-int
pkg syscall (darwin-arm64-cgo), const IFT_CELLULAR = 255
pkg syscall (darwin-arm64-cgo), const IFT_CELLULAR ideal-int
pkg syscall (darwin-arm64-cgo), const IFT_CEPT = 19
pkg syscall (darwin-arm64-cgo), const IFT_CEPT ideal-int
pkg syscall (darwin-arm64-cgo), const IFT_DS3 = 30
pkg syscall (darwin-arm64-cgo), const IFT_DS3 ideal-int
pkg syscall (darwin-arm64-cgo), const IFT_ENC = 244
pkg syscall (darwin-arm64-cgo), const IFT_ENC ideal-int
pkg syscall (darwin-arm64-cgo), const IFT_EON = 25
pkg syscall (darwin-arm64-cgo), const IFT_EON ideal-int
pkg syscall (darwin-arm64-cgo), const IFT_ETHER = 6
pkg syscall (darwin-arm64-cgo), const IFT_ETHER ideal-int
pkg syscall (darwin-arm64-cgo), const IFT_FAITH = 56
pkg syscall (darwin-arm64-cgo), const IFT_FAITH ideal-int
pkg syscall (darwin-arm64-cgo), const IFT_FDDI = 15
pkg syscall (darwin-arm64-cgo), const IFT_FDDI ideal-int
pkg syscall (darwin-arm64-cgo), const IFT_FRELAY = 32
pkg syscall (darwin-arm64-cgo), const IFT_FRELAY ideal-int
pkg syscall (darwin-arm64-cgo), const IFT_FRELAYDCE = 44
pkg syscall (darwin-arm64-cgo), const IFT_FRELAYDCE ideal-int
pkg syscall (darwin-arm64-cgo), const IFT_GIF = 55
pkg syscall (darwin-arm64-cgo), const IFT_GIF ideal-int
pkg syscall (darwin-arm64-cgo), const IFT_HDH1822 = 3
pkg syscall (darwin-arm64-cgo), const IFT_HDH1822 ideal-int
pkg syscall (darwin-arm64-cgo), const IFT_HIPPI = 47
pkg syscall (darwin-arm64-cgo), const IFT_HIPPI ideal-int
pkg syscall (darwin-arm64-cgo), const IFT_HSSI = 46
pkg syscall (darwin-arm64-cgo), const IFT_HSSI ideal-int
pkg syscall (darwin-arm64-cgo), const IFT_HY = 14
pkg syscall (darwin-arm64-cgo), const IFT_HY ideal-int
pkg syscall (darwin-arm64-cgo), const IFT_IEEE1394 = 144
pkg syscall (darwin-arm64-cgo), const IFT_IEEE1394 ideal-int
pkg syscall (darwin-arm64-cgo), const IFT_IEEE8023ADLAG = 136
pkg syscall (darwin-arm64-cgo), const IFT_IEEE8023ADLAG ideal-int
pkg syscall (darwin-arm64-cgo), const IFT_ISDNBASIC = 20
pkg syscall (darwin-arm64-cgo), const IFT_ISDNBASIC ideal-int
pkg syscall (darwin-arm64-cgo), const IFT_ISDNPRIMARY = 21
pkg syscall (darwin-arm64-cgo), const IFT_ISDNPRIMARY ideal-int
pkg syscall (darwin-arm64-cgo), const IFT_ISO88022LLC = 41
pkg syscall (darwin-arm64-cgo), const IFT_ISO88022LLC ideal-int
pkg syscall (darwin-arm64-cgo), const IFT_ISO88023 = 7
pkg syscall (darwin-arm64-cgo), const IFT_ISO88023 ideal-int
pkg syscall (darwin-arm64-cgo), const IFT_ISO88024 = 8
pkg syscall (darwin-arm64-cgo), const IFT_ISO88024 ideal-int
pkg syscall (darwin-arm64-cgo), const IFT_ISO88025 = 9
pkg syscall (darwin-arm64-cgo), const IFT_ISO88025 ideal-int
pkg syscall (darwin-arm64-cgo), const IFT_ISO88026 = 10
pkg syscall (darwin-arm64-cgo), const IFT_ISO88026 ideal-int
pkg syscall (darwin-arm64-cgo), const IFT_L2VLAN = 135
pkg syscall (darwin-arm64-cgo), const IFT_L2VLAN ideal-int
pkg syscall (darwin-arm64-cgo), const IFT_LAPB = 16
pkg syscall (darwin-arm64-cgo), const IFT_LAPB ideal-int
pkg syscall (darwin-arm64-cgo), const IFT_LOCALTALK = 42
pkg syscall (darwin-arm64-cgo), const IFT_LOCALTALK ideal-int
pkg syscall (darwin-arm64-cgo), const IFT_LOOP = 24
pkg syscall (darwin-arm64-cgo), const IFT_LOOP ideal-int
pkg syscall (darwin-arm64-cgo), const IFT_MIOX25 = 38
pkg syscall (darwin-arm64-cgo), const IFT_MIOX25 ideal-int
pkg syscall (darwin-arm64-cgo), const IFT_MODEM = 48
pkg syscall (darwin-arm64-cgo), const IFT_MODEM ideal-int
pkg syscall (darwin-arm64-cgo), const IFT_NSIP = 27
pkg syscall (darwin-arm64-cgo), const IFT_NSIP ideal-int
pkg syscall (darwin-arm64-cgo), const IFT_OTHER = 1
pkg syscall (darwin-arm64-cgo), const IFT_OTHER ideal-int
pkg syscall (darwin-arm64-cgo), const IFT_P10 = 12
pkg syscall (darwin-arm64-cgo), const IFT_P10 ideal-int
pkg syscall (darwin-arm64-cgo), const IFT_P80 = 13
pkg syscall (darwin-arm64-cgo), const IFT_P80 ideal-int
pkg syscall (darwin-arm64-cgo), const IFT_PARA = 34
pkg syscall (darwin-arm64-cgo), const IFT_PARA ideal-int
pkg syscall (darwin-arm64-cgo), const IFT_PDP = 255
pkg syscall (darwin-arm64-cgo), const IFT_PDP ideal-int
pkg syscall (darwin-arm64-cgo), const IFT_PFLOG = 245
pkg syscall (darwin-arm64-cgo), const IFT_PFLOG ideal-int
pkg syscall (darwin-arm64-cgo), const IFT_PFSYNC = 246
pkg syscall (darwin-arm64-cgo), const IFT_PFSYNC ideal-int
pkg syscall (darwin-arm64-cgo), const IFT_PPP = 23
pkg syscall (darwin-arm64-cgo), const IFT_PPP ideal-int
pkg syscall (darwin-arm64-cgo), const IFT_PROPMUX = 54
pkg syscall (darwin-arm64-cgo), const IFT_PROPMUX ideal-int
pkg syscall (darwin-arm64-cgo), const IFT_PROPVIRTUAL = 53
pkg syscall (darwin-arm64-cgo), const IFT_PROPVIRTUAL ideal-int
pkg syscall (darwin-arm64-cgo), const IFT_PTPSERIAL = 22
pkg syscall (darwin-arm64-cgo), const IFT_PTPSERIAL ideal-int
pkg syscall (darwin-arm64-cgo), const IFT_RS232 = 33
pkg syscall (darwin-arm64-cgo), const IFT_RS232 ideal-int
pkg syscall (darwin-arm64-cgo), const IFT_SDLC = 17
pkg syscall (darwin-arm64-cgo), const IFT_SDLC ideal-int
pkg syscall (darwin-arm64-cgo), const IFT_SIP = 31
pkg syscall (darwin-arm64-cgo), const IFT_SIP ideal-int
pkg syscall (darwin-arm64-cgo), const IFT_SLIP = 28
pkg syscall (darwin-arm64-cgo), const IFT_SLIP ideal-int
pkg syscall (darwin-arm64-cgo), const IFT_SMDSDXI = 43
pkg syscall (darwin-arm64-cgo), const IFT_SMDSDXI ideal-int
pkg syscall (darwin-arm64-cgo), const IFT_SMDSICIP = 52
pkg syscall (darwin-arm64-cgo), const IFT_SMDSICIP ideal-int
pkg syscall (darwin-arm64-cgo), const IFT_SONET = 39
pkg syscall (darwin-arm64-cgo), const IFT_SONET ideal-int
pkg syscall (darwin-arm64-cgo), const IFT_SONETPATH = 50
pkg syscall (darwin-arm64-cgo), const IFT_SONETPATH ideal-int
pkg syscall (darwin-arm64-cgo), const IFT_SONETVT = 51
pkg syscall (darwin-arm64-cgo), const IFT_SONETVT ideal-int
pkg syscall (darwin-arm64-cgo), const IFT_STARLAN = 11
pkg syscall (darwin-arm64-cgo), const IFT_STARLAN ideal-int
pkg syscall (darwin-arm64-cgo), const IFT_STF = 57
pkg syscall (darwin-arm64-cgo), const IFT_STF ideal-int
pkg syscall (darwin-arm64-cgo), const IFT_T1 = 18
pkg syscall (darwin-arm64-cgo), const IFT_T1 ideal-int
pkg syscall (darwin-arm64-cgo), const IFT_ULTRA = 29
pkg syscall (darwin-arm64-cgo), const IFT_ULTRA ideal-int
pkg syscall (darwin-arm64-cgo), const IFT_V35 = 45
pkg syscall (darwin-arm64-cgo), const IFT_V35 ideal-int
pkg syscall (darwin-arm64-cgo), const IFT_X25 = 5
pkg syscall (darwin-arm64-cgo), const IFT_X25 ideal-int
pkg syscall (darwin-arm64-cgo), const IFT_X25DDN = 4
pkg syscall (darwin-arm64-cgo), const IFT_X25DDN ideal-int
pkg syscall (darwin-arm64-cgo), const IFT_X25PLE = 40
pkg syscall (darwin-arm64-cgo), const IFT_X25PLE ideal-int
pkg syscall (darwin-arm64-cgo), const IFT_XETHER = 26
pkg syscall (darwin-arm64-cgo), const IFT_XETHER ideal-int
pkg syscall (darwin-arm64-cgo), const IGNBRK = 1
pkg syscall (darwin-arm64-cgo), const IGNBRK ideal-int
pkg syscall (darwin-arm64-cgo), const IGNCR = 128
pkg syscall (darwin-arm64-cgo), const IGNCR ideal-int
pkg syscall (darwin-arm64-cgo), const IGNPAR = 4
pkg syscall (darwin-arm64-cgo), const IGNPAR ideal-int
pkg syscall (darwin-arm64-cgo), const IMAXBEL = 8192
pkg syscall (darwin-arm64-cgo), const IMAXBEL ideal-int
pkg syscall (darwin-arm64-cgo), const INLCR = 64
pkg syscall (darwin-arm64-cgo), const INLCR ideal-int
pkg syscall (darwin-arm64-cgo), const INPCK = 16
pkg syscall (darwin-arm64-cgo), const INPCK ideal-int
pkg syscall (darwin-arm64-cgo), const IN_CLASSA_HOST = 16777215
pkg syscall (darwin-arm64-cgo), const IN_CLASSA_HOST ideal-int
pkg syscall (darwin-arm64-cgo), const IN_CLASSA_MAX = 128
pkg syscall (darwin-arm64-cgo), const IN_CLASSA_MAX ideal-int
pkg syscall (darwin-arm64-cgo), const IN_CLASSA_NET = 4278190080
pkg syscall (darwin-arm64-cgo), const IN_CLASSA_NET ideal-int
pkg syscall (darwin-arm64-cgo), const IN_CLASSA_NSHIFT = 24
pkg syscall (darwin-arm64-cgo), const IN_CLASSA_NSHIFT ideal-int
pkg syscall (darwin-arm64-cgo), const IN_CLASSB_HOST = 65535
pkg syscall (darwin-arm64-cgo), const IN_CLASSB_HOST ideal-int
pkg syscall (darwin-arm64-cgo), const IN_CLASSB_MAX = 65536
pkg syscall (darwin-arm64-cgo), const IN_CLASSB_MAX ideal-int
pkg syscall (darwin-arm64-cgo), const IN_CLASSB_NET = 4294901760
pkg syscall (darwin-arm64-cgo), const IN_CLASSB_NET ideal-int
pkg syscall (darwin-arm64-cgo), const IN_CLASSB_NSHIFT = 16
pkg syscall (darwin-arm64-cgo), const IN_CLASSB_NSHIFT ideal-int
pkg syscall (darwin-arm64-cgo), const IN_CLASSC_HOST = 255
pkg syscall (darwin-arm64-cgo), const IN_CLASSC_HOST ideal-int
pkg syscall (darwin-arm64-cgo), const IN_CLASSC_NET = 4294967040
pkg syscall (darwin-arm64-cgo), const IN_CLASSC_NET ideal-int
pkg syscall (darwin-arm64-cgo), const IN_CLASSC_NSHIFT = 8
pkg syscall (darwin-arm64-cgo), const IN_CLASSC_NSHIFT ideal-int
pkg syscall (darwin-arm64-cgo), const IN_CLASSD_HOST = 268435455
pkg syscall (darwin-arm64-cgo), const IN_CLASSD_HOST ideal-int
pkg syscall (darwin-arm64-cgo), const IN_CLASSD_NET = 4026531840
pkg syscall (darwin-arm64-cgo), const IN_CLASSD_NET ideal-int
pkg syscall (darwin-arm64-cgo), const IN_CLASSD_NSHIFT = 28
pkg syscall (darwin-arm64-cgo), const IN_CLASSD_NSHIFT ideal-int
pkg syscall (darwin-arm64-cgo), const IN_LINKLOCALNETNUM = 2851995648
pkg syscall (darwin-arm64-cgo), const IN_LINKLOCALNETNUM ideal-int
pkg syscall (darwin-arm64-cgo), const IN_LOOPBACKNET = 127
pkg syscall (darwin-arm64-cgo), const IN_LOOPBACKNET ideal-int
pkg syscall (darwin-arm64-cgo), const IPPROTO_3PC = 34
pkg syscall (darwin-arm64-cgo), const IPPROTO_3PC ideal-int
pkg syscall (darwin-arm64-cgo), const IPPROTO_ADFS = 68
pkg syscall (darwin-arm64-cgo), const IPPROTO_ADFS ideal-int
pkg syscall (darwin-arm64-cgo), const IPPROTO_AH = 51
pkg syscall (darwin-arm64-cgo), const IPPROTO_AH ideal-int
pkg syscall (darwin-arm64-cgo), const IPPROTO_AHIP = 61
pkg syscall (darwin-arm64-cgo), const IPPROTO_AHIP ideal-int
pkg syscall (darwin-arm64-cgo), const IPPROTO_APES = 99
pkg syscall (darwin-arm64-cgo), const IPPROTO_APES ideal-int
pkg syscall (darwin-arm64-cgo), const IPPROTO_ARGUS = 13
pkg syscall (darwin-arm64-cgo), const IPPROTO_ARGUS ideal-int
pkg syscall (darwin-arm64-cgo), const IPPROTO_AX25 = 93
pkg syscall (darwin-arm64-cgo), const IPPROTO_AX25 ideal-int
pkg syscall (darwin-arm64-cgo), const IPPROTO_BHA = 49
pkg syscall (darwin-arm64-cgo), const IPPROTO_BHA ideal-int
pkg syscall (darwin-arm64-cgo), const IPPROTO_BLT = 30
pkg syscall (darwin-arm64-cgo), const IPPROTO_BLT ideal-int
pkg syscall (darwin-arm64-cgo), const IPPROTO_BRSATMON = 76
pkg syscall (darwin-arm64-cgo), const IPPROTO_BRSATMON ideal-int
pkg syscall (darwin-arm64-cgo), const IPPROTO_CFTP = 62
pkg syscall (darwin-arm64-cgo), const IPPROTO_CFTP ideal-int
pkg syscall (darwin-arm64-cgo), const IPPROTO_CHAOS = 16
pkg syscall (darwin-arm64-cgo), const IPPROTO_CHAOS ideal-int
pkg syscall (darwin-arm64-cgo), const IPPROTO_CMTP = 38
pkg syscall (darwin-arm64-cgo), const IPPROTO_CMTP ideal-int
pkg syscall (darwin-arm64-cgo), const IPPROTO_CPHB = 73
pkg syscall (darwin-arm64-cgo), const IPPROTO_CPHB ideal-int
pkg syscall (darwin-arm64-cgo), const IPPROTO_CPNX = 72
pkg syscall (darwin-arm64-cgo), const IPPROTO_CPNX ideal-int
pkg syscall (darwin-arm64-cgo), const IPPROTO_DDP = 37
pkg syscall (darwin-arm64-cgo), const IPPROTO_DDP ideal-int
pkg syscall (darwin-arm64-cgo), const IPPROTO_DGP = 86
pkg syscall (darwin-arm64-cgo), const IPPROTO_DGP ideal-int
pkg syscall (darwin-arm64-cgo), const IPPROTO_DIVERT = 254
pkg syscall (darwin-arm64-cgo), const IPPROTO_DIVERT ideal-int
pkg syscall (darwin-arm64-cgo), const IPPROTO_DONE = 257
pkg syscall (darwin-arm64-cgo), const IPPROTO_DONE ideal-int
pkg syscall (darwin-arm64-cgo), const IPPROTO_DSTOPTS = 60
pkg syscall (darwin-arm64-cgo), const IPPROTO_DSTOPTS ideal-int
pkg syscall (darwin-arm64-cgo), const IPPROTO_EGP = 8
pkg syscall (darwin-arm64-cgo), const IPPROTO_EGP ideal-int
pkg syscall (darwin-arm64-cgo), const IPPROTO_EMCON = 14
pkg syscall (darwin-arm64-cgo), const IPPROTO_EMCON ideal-int
pkg syscall (darwin-arm64-cgo), const IPPROTO_ENCAP = 98
pkg syscall (darwin-arm64-cgo), const IPPROTO_ENCAP ideal-int
pkg syscall (darwin-arm64-cgo), const IPPROTO_EON = 80
pkg syscall (darwin-arm64-cgo), const IPPROTO_EON ideal-int
pkg syscall (darwin-arm64-cgo), const IPPROTO_ESP = 50
pkg syscall (darwin-arm64-cgo), const IPPROTO_ESP ideal-int
pkg syscall (darwin-arm64-cgo), const IPPROTO_ETHERIP = 97
pkg syscall (darwin-arm64-cgo), const IPPROTO_ETHERIP ideal-int
pkg syscall (darwin-arm64-cgo), const IPPROTO_FRAGMENT = 44
pkg syscall (darwin-arm64-cgo), const IPPROTO_FRAGMENT ideal-int
pkg syscall (darwin-arm64-cgo), const IPPROTO_GGP = 3
pkg syscall (darwin-arm64-cgo), const IPPROTO_GGP ideal-int
pkg syscall (darwin-arm64-cgo), const IPPROTO_GMTP = 100
pkg syscall (darwin-arm64-cgo), const IPPROTO_GMTP ideal-int
pkg syscall (darwin-arm64-cgo), const IPPROTO_GRE = 47
pkg syscall (darwin-arm64-cgo), const IPPROTO_GRE ideal-int
pkg syscall (darwin-arm64-cgo), const IPPROTO_HELLO = 63
pkg syscall (darwin-arm64-cgo), const IPPROTO_HELLO ideal-int
pkg syscall (darwin-arm64-cgo), const IPPROTO_HMP = 20
pkg syscall (darwin-arm64-cgo), const IPPROTO_HMP ideal-int
pkg syscall (darwin-arm64-cgo), const IPPROTO_HOPOPTS = 0
pkg syscall (darwin-arm64-cgo), const IPPROTO_HOPOPTS ideal-int
pkg syscall (darwin-arm64-cgo), const IPPROTO_ICMP = 1
pkg syscall (darwin-arm64-cgo), const IPPROTO_ICMP ideal-int
pkg syscall (darwin-arm64-cgo), const IPPROTO_ICMPV6 = 58
pkg syscall (darwin-arm64-cgo), const IPPROTO_ICMPV6 ideal-int
pkg syscall (darwin-arm64-cgo), const IPPROTO_IDP = 22
pkg syscall (darwin-arm64-cgo), const IPPROTO_IDP ideal-int
pkg syscall (darwin-arm64-cgo), const IPPROTO_IDPR = 35
pkg syscall (darwin-arm64-cgo), const IPPROTO_IDPR ideal-int
pkg syscall (darwin-arm64-cgo), const IPPROTO_IDRP = 45
pkg syscall (darwin-arm64-cgo), const IPPROTO_IDRP ideal-int
pkg syscall (darwin-arm64-cgo), const IPPROTO_IGMP = 2
pkg syscall (darwin-arm64-cgo), const IPPROTO_IGMP ideal-int
pkg syscall (darwin-arm64-cgo), const IPPROTO_IGP = 85
pkg syscall (darwin-arm64-cgo), const IPPROTO_IGP ideal-int
pkg syscall (darwin-arm64-cgo), const IPPROTO_IGRP = 88
pkg syscall (darwin-arm64-cgo), const IPPROTO_IGRP ideal-int
pkg syscall (darwin-arm64-cgo), const IPPROTO_IL = 40
pkg syscall (darwin-arm64-cgo), const IPPROTO_IL ideal-int
pkg syscall (darwin-arm64-cgo), const IPPROTO_INLSP = 52
pkg syscall (darwin-arm64-cgo), const IPPROTO_INLSP ideal-int
pkg syscall (darwin-arm64-cgo), const IPPROTO_INP = 32
pkg syscall (darwin-arm64-cgo), const IPPROTO_INP ideal-int
pkg syscall (darwin-arm64-cgo), const IPPROTO_IPCOMP = 108
pkg syscall (darwin-arm64-cgo), const IPPROTO_IPCOMP ideal-int
pkg syscall (darwin-arm64-cgo), const IPPROTO_IPCV = 71
pkg syscall (darwin-arm64-cgo), const IPPROTO_IPCV ideal-int
pkg syscall (darwin-arm64-cgo), const IPPROTO_IPEIP = 94
pkg syscall (darwin-arm64-cgo), const IPPROTO_IPEIP ideal-int
pkg syscall (darwin-arm64-cgo), const IPPROTO_IPIP = 4
pkg syscall (darwin-arm64-cgo), const IPPROTO_IPIP ideal-int
pkg syscall (darwin-arm64-cgo), const IPPROTO_IPPC = 67
pkg syscall (darwin-arm64-cgo), const IPPROTO_IPPC ideal-int
pkg syscall (darwin-arm64-cgo), const IPPROTO_IPV4 = 4
pkg syscall (darwin-arm64-cgo), const IPPROTO_IPV4 ideal-int
pkg syscall (darwin-arm64-cgo), const IPPROTO_IRTP = 28
pkg syscall (darwin-arm64-cgo), const IPPROTO_IRTP ideal-int
pkg syscall (darwin-arm64-cgo), const IPPROTO_KRYPTOLAN = 65
pkg syscall (darwin-arm64-cgo), const IPPROTO_KRYPTOLAN ideal-int
pkg syscall (darwin-arm64-cgo), const IPPROTO_LARP = 91
pkg syscall (darwin-arm64-cgo), const IPPROTO_LARP ideal-int
pkg syscall (darwin-arm64-cgo), const IPPROTO_LEAF1 = 25
pkg syscall (darwin-arm64-cgo), const IPPROTO_LEAF1 ideal-int
pkg syscall (darwin-arm64-cgo), const IPPROTO_LEAF2 = 26
pkg syscall (darwin-arm64-cgo), const IPPROTO_LEAF2 ideal-int
pkg syscall (darwin-arm64-cgo), const IPPROTO_MAX = 256
pkg syscall (darwin-arm64-cgo), const IPPROTO_MAX ideal-int
pkg syscall (darwin-arm64-cgo), const IPPROTO_MAXID = 52
pkg syscall (darwin-arm64-cgo), const IPPROTO_MAXID ideal-int
pkg syscall (darwin-arm64-cgo), const IPPROTO_MEAS = 19
pkg syscall (darwin-arm64-cgo), const IPPROTO_MEAS ideal-int
pkg syscall (darwin-arm64-cgo), const IPPROTO_MHRP = 48
pkg syscall (darwin-arm64-cgo), const IPPROTO_MHRP ideal-int
pkg syscall (darwin-arm64-cgo), const IPPROTO_MICP = 95
pkg syscall (darwin-arm64-cgo), const IPPROTO_MICP ideal-int
pkg syscall (darwin-arm64-cgo), const IPPROTO_MTP = 92
pkg syscall (darwin-arm64-cgo), const IPPROTO_MTP ideal-int
pkg syscall (darwin-arm64-cgo), const IPPROTO_MUX = 18
pkg syscall (darwin-arm64-cgo), const IPPROTO_MUX ideal-int
pkg syscall (darwin-arm64-cgo), const IPPROTO_ND = 77
pkg syscall (darwin-arm64-cgo), const IPPROTO_ND ideal-int
pkg syscall (darwin-arm64-cgo), const IPPROTO_NHRP = 54
pkg syscall (darwin-arm64-cgo), const IPPROTO_NHRP ideal-int
pkg syscall (darwin-arm64-cgo), const IPPROTO_NONE = 59
pkg syscall (darwin-arm64-cgo), const IPPROTO_NONE ideal-int
pkg syscall (darwin-arm64-cgo), const IPPROTO_NSP = 31
pkg syscall (darwin-arm64-cgo), const IPPROTO_NSP ideal-int
pkg syscall (darwin-arm64-cgo), const IPPROTO_NVPII = 11
pkg syscall (darwin-arm64-cgo), const IPPROTO_NVPII ideal-int
pkg syscall (darwin-arm64-cgo), const IPPROTO_OSPFIGP = 89
pkg syscall (darwin-arm64-cgo), const IPPROTO_OSPFIGP ideal-int
pkg syscall (darwin-arm64-cgo), const IPPROTO_PGM = 113
pkg syscall (darwin-arm64-cgo), const IPPROTO_PGM ideal-int
pkg syscall (darwin-arm64-cgo), const IPPROTO_PIGP = 9
pkg syscall (darwin-arm64-cgo), const IPPROTO_PIGP ideal-int
pkg syscall (darwin-arm64-cgo), const IPPROTO_PIM = 103
pkg syscall (darwin-arm64-cgo), const IPPROTO_PIM ideal-int
pkg syscall (darwin-arm64-cgo), const IPPROTO_PRM = 21
pkg syscall (darwin-arm64-cgo), const IPPROTO_PRM ideal-int
pkg syscall (darwin-arm64-cgo), const IPPROTO_PUP = 12
pkg syscall (darwin-arm64-cgo), const IPPROTO_PUP ideal-int
pkg syscall (darwin-arm64-cgo), const IPPROTO_PVP = 75
pkg syscall (darwin-arm64-cgo), const IPPROTO_PVP ideal-int
pkg syscall (darwin-arm64-cgo), const IPPROTO_RAW = 255
pkg syscall (darwin-arm64-cgo), const IPPROTO_RAW ideal-int
pkg syscall (darwin-arm64-cgo), const IPPROTO_RCCMON = 10
pkg syscall (darwin-arm64-cgo), const IPPROTO_RCCMON ideal-int
pkg syscall (darwin-arm64-cgo), const IPPROTO_RDP = 27
pkg syscall (darwin-arm64-cgo), const IPPROTO_RDP ideal-int
pkg syscall (darwin-arm64-cgo), const IPPROTO_ROUTING = 43
pkg syscall (darwin-arm64-cgo), const IPPROTO_ROUTING ideal-int
pkg syscall (darwin-arm64-cgo), const IPPROTO_RSVP = 46
pkg syscall (darwin-arm64-cgo), const IPPROTO_RSVP ideal-int
pkg syscall (darwin-arm64-cgo), const IPPROTO_RVD = 66
pkg syscall (darwin-arm64-cgo), const IPPROTO_RVD ideal-int
pkg syscall (darwin-arm64-cgo), const IPPROTO_SATEXPAK = 64
pkg syscall (darwin-arm64-cgo), const IPPROTO_SATEXPAK ideal-int
pkg syscall (darwin-arm64-cgo), const IPPROTO_SATMON = 69
pkg syscall (darwin-arm64-cgo), const IPPROTO_SATMON ideal-int
pkg syscall (darwin-arm64-cgo), const IPPROTO_SCCSP = 96
pkg syscall (darwin-arm64-cgo), const IPPROTO_SCCSP ideal-int
pkg syscall (darwin-arm64-cgo), const IPPROTO_SCTP = 132
pkg syscall (darwin-arm64-cgo), const IPPROTO_SCTP ideal-int
pkg syscall (darwin-arm64-cgo), const IPPROTO_SDRP = 42
pkg syscall (darwin-arm64-cgo), const IPPROTO_SDRP ideal-int
pkg syscall (darwin-arm64-cgo), const IPPROTO_SEP = 33
pkg syscall (darwin-arm64-cgo), const IPPROTO_SEP ideal-int
pkg syscall (darwin-arm64-cgo), const IPPROTO_SRPC = 90
pkg syscall (darwin-arm64-cgo), const IPPROTO_SRPC ideal-int
pkg syscall (darwin-arm64-cgo), const IPPROTO_ST = 7
pkg syscall (darwin-arm64-cgo), const IPPROTO_ST ideal-int
pkg syscall (darwin-arm64-cgo), const IPPROTO_SVMTP = 82
pkg syscall (darwin-arm64-cgo), const IPPROTO_SVMTP ideal-int
pkg syscall (darwin-arm64-cgo), const IPPROTO_SWIPE = 53
pkg syscall (darwin-arm64-cgo), const IPPROTO_SWIPE ideal-int
pkg syscall (darwin-arm64-cgo), const IPPROTO_TCF = 87
pkg syscall (darwin-arm64-cgo), const IPPROTO_TCF ideal-int
pkg syscall (darwin-arm64-cgo), const IPPROTO_TP = 29
pkg syscall (darwin-arm64-cgo), const IPPROTO_TP ideal-int
pkg syscall (darwin-arm64-cgo), const IPPROTO_TPXX = 39
pkg syscall (darwin-arm64-cgo), const IPPROTO_TPXX ideal-int
pkg syscall (darwin-arm64-cgo), const IPPROTO_TRUNK1 = 23
pkg syscall (darwin-arm64-cgo), const IPPROTO_TRUNK1 ideal-int
pkg syscall (darwin-arm64-cgo), const IPPROTO_TRUNK2 = 24
pkg syscall (darwin-arm64-cgo), const IPPROTO_TRUNK2 ideal-int
pkg syscall (darwin-arm64-cgo), const IPPROTO_TTP = 84
pkg syscall (darwin-arm64-cgo), const IPPROTO_TTP ideal-int
pkg syscall (darwin-arm64-cgo), const IPPROTO_VINES = 83
pkg syscall (darwin-arm64-cgo), const IPPROTO_VINES ideal-int
pkg syscall (darwin-arm64-cgo), const IPPROTO_VISA = 70
pkg syscall (darwin-arm64-cgo), const IPPROTO_VISA ideal-int
pkg syscall (darwin-arm64-cgo), const IPPROTO_VMTP = 81
pkg syscall (darwin-arm64-cgo), const IPPROTO_VMTP ideal-int
pkg syscall (darwin-arm64-cgo), const IPPROTO_WBEXPAK = 79
pkg syscall (darwin-arm64-cgo), const IPPROTO_WBEXPAK ideal-int
pkg syscall (darwin-arm64-cgo), const IPPROTO_WBMON = 78
pkg syscall (darwin-arm64-cgo), const IPPROTO_WBMON ideal-int
pkg syscall (darwin-arm64-cgo), const IPPROTO_WSN = 74
pkg syscall (darwin-arm64-cgo), const IPPROTO_WSN ideal-int
pkg syscall (darwin-arm64-cgo), const IPPROTO_XNET = 15
pkg syscall (darwin-arm64-cgo), const IPPROTO_XNET ideal-int
pkg syscall (darwin-arm64-cgo), const IPPROTO_XTP = 36
pkg syscall (darwin-arm64-cgo), const IPPROTO_XTP ideal-int
pkg syscall (darwin-arm64-cgo), const IPV6_2292DSTOPTS = 23
pkg syscall (darwin-arm64-cgo), const IPV6_2292DSTOPTS ideal-int
pkg syscall (darwin-arm64-cgo), const IPV6_2292HOPLIMIT = 20
pkg syscall (darwin-arm64-cgo), const IPV6_2292HOPLIMIT ideal-int
pkg syscall (darwin-arm64-cgo), const IPV6_2292HOPOPTS = 22
pkg syscall (darwin-arm64-cgo), const IPV6_2292HOPOPTS ideal-int
pkg syscall (darwin-arm64-cgo), const IPV6_2292NEXTHOP = 21
pkg syscall (darwin-arm64-cgo), const IPV6_2292NEXTHOP ideal-int
pkg syscall (darwin-arm64-cgo), const IPV6_2292PKTINFO = 19
pkg syscall (darwin-arm64-cgo), const IPV6_2292PKTINFO ideal-int
pkg syscall (darwin-arm64-cgo), const IPV6_2292PKTOPTIONS = 25
pkg syscall (darwin-arm64-cgo), const IPV6_2292PKTOPTIONS ideal-int
pkg syscall (darwin-arm64-cgo), const IPV6_2292RTHDR = 24
pkg syscall (darwin-arm64-cgo), const IPV6_2292RTHDR ideal-int
pkg syscall (darwin-arm64-cgo), const IPV6_BINDV6ONLY = 27
pkg syscall (darwin-arm64-cgo), const IPV6_BINDV6ONLY ideal-int
pkg syscall (darwin-arm64-cgo), const IPV6_BOUND_IF = 125
pkg syscall (darwin-arm64-cgo), const IPV6_BOUND_IF ideal-int
pkg syscall (darwin-arm64-cgo), const IPV6_CHECKSUM = 26
pkg syscall (darwin-arm64-cgo), const IPV6_CHECKSUM ideal-int
pkg syscall (darwin-arm64-cgo), const IPV6_DEFAULT_MULTICAST_HOPS = 1
pkg syscall (darwin-arm64-cgo), const IPV6_DEFAULT_MULTICAST_HOPS ideal-int
pkg syscall (darwin-arm64-cgo), const IPV6_DEFAULT_MULTICAST_LOOP = 1
pkg syscall (darwin-arm64-cgo), const IPV6_DEFAULT_MULTICAST_LOOP ideal-int
pkg syscall (darwin-arm64-cgo), const IPV6_DEFHLIM = 64
pkg syscall (darwin-arm64-cgo), const IPV6_DEFHLIM ideal-int
pkg syscall (darwin-arm64-cgo), const IPV6_FAITH = 29
pkg syscall (darwin-arm64-cgo), const IPV6_FAITH ideal-int
pkg syscall (darwin-arm64-cgo), const IPV6_FLOWINFO_MASK = 4294967055
pkg syscall (darwin-arm64-cgo), const IPV6_FLOWINFO_MASK ideal-int
pkg syscall (darwin-arm64-cgo), const IPV6_FLOWLABEL_MASK = 4294905600
pkg syscall (darwin-arm64-cgo), const IPV6_FLOWLABEL_MASK ideal-int
pkg syscall (darwin-arm64-cgo), const IPV6_FRAGTTL = 120
pkg syscall (darwin-arm64-cgo), const IPV6_FRAGTTL ideal-int
pkg syscall (darwin-arm64-cgo), const IPV6_FW_ADD = 30
pkg syscall (darwin-arm64-cgo), const IPV6_FW_ADD ideal-int
pkg syscall (darwin-arm64-cgo), const IPV6_FW_DEL = 31
pkg syscall (darwin-arm64-cgo), const IPV6_FW_DEL ideal-int
pkg syscall (darwin-arm64-cgo), const IPV6_FW_FLUSH = 32
pkg syscall (darwin-arm64-cgo), const IPV6_FW_FLUSH ideal-int
pkg syscall (darwin-arm64-cgo), const IPV6_FW_GET = 34
pkg syscall (darwin-arm64-cgo), const IPV6_FW_GET ideal-int
pkg syscall (darwin-arm64-cgo), const IPV6_FW_ZERO = 33
pkg syscall (darwin-arm64-cgo), const IPV6_FW_ZERO ideal-int
pkg syscall (darwin-arm64-cgo), const IPV6_HLIMDEC = 1
pkg syscall (darwin-arm64-cgo), const IPV6_HLIMDEC ideal-int
pkg syscall (darwin-arm64-cgo), const IPV6_IPSEC_POLICY = 28
pkg syscall (darwin-arm64-cgo), const IPV6_IPSEC_POLICY ideal-int
pkg syscall (darwin-arm64-cgo), const IPV6_JOIN_GROUP = 12
pkg syscall (darwin-arm64-cgo), const IPV6_LEAVE_GROUP = 13
pkg syscall (darwin-arm64-cgo), const IPV6_MAXHLIM = 255
pkg syscall (darwin-arm64-cgo), const IPV6_MAXHLIM ideal-int
pkg syscall (darwin-arm64-cgo), const IPV6_MAXOPTHDR = 2048
pkg syscall (darwin-arm64-cgo), const IPV6_MAXOPTHDR ideal-int
pkg syscall (darwin-arm64-cgo), const IPV6_MAXPACKET = 65535
pkg syscall (darwin-arm64-cgo), const IPV6_MAXPACKET ideal-int
pkg syscall (darwin-arm64-cgo), const IPV6_MAX_GROUP_SRC_FILTER = 512
pkg syscall (darwin-arm64-cgo), const IPV6_MAX_GROUP_SRC_FILTER ideal-int
pkg syscall (darwin-arm64-cgo), const IPV6_MAX_MEMBERSHIPS = 4095
pkg syscall (darwin-arm64-cgo), const IPV6_MAX_MEMBERSHIPS ideal-int
pkg syscall (darwin-arm64-cgo), const IPV6_MAX_SOCK_SRC_FILTER = 128
pkg syscall (darwin-arm64-cgo), const IPV6_MAX_SOCK_SRC_FILTER ideal-int
pkg syscall (darwin-arm64-cgo), const IPV6_MIN_MEMBERSHIPS = 31
pkg syscall (darwin-arm64-cgo), const IPV6_MIN_MEMBERSHIPS ideal-int
pkg syscall (darwin-arm64-cgo), const IPV6_MMTU = 1280
pkg syscall (darwin-arm64-cgo), const IPV6_MMTU ideal-int
pkg syscall (darwin-arm64-cgo), const IPV6_MULTICAST_HOPS = 10
pkg syscall (darwin-arm64-cgo), const IPV6_MULTICAST_IF = 9
pkg syscall (darwin-arm64-cgo), const IPV6_MULTICAST_LOOP = 11
pkg syscall (darwin-arm64-cgo), const IPV6_PORTRANGE = 14
pkg syscall (darwin-arm64-cgo), const IPV6_PORTRANGE ideal-int
pkg syscall (darwin-arm64-cgo), const IPV6_PORTRANGE_DEFAULT = 0
pkg syscall (darwin-arm64-cgo), const IPV6_PORTRANGE_DEFAULT ideal-int
pkg syscall (darwin-arm64-cgo), const IPV6_PORTRANGE_HIGH = 1
pkg syscall (darwin-arm64-cgo), const IPV6_PORTRANGE_HIGH ideal-int
pkg syscall (darwin-arm64-cgo), const IPV6_PORTRANGE_LOW = 2
pkg syscall (darwin-arm64-cgo), const IPV6_PORTRANGE_LOW ideal-int
pkg syscall (darwin-arm64-cgo), const IPV6_RECVTCLASS = 35
pkg syscall (darwin-arm64-cgo), const IPV6_RECVTCLASS ideal-int
pkg syscall (darwin-arm64-cgo), const IPV6_RTHDR_LOOSE = 0
pkg syscall (darwin-arm64-cgo), const IPV6_RTHDR_LOOSE ideal-int
pkg syscall (darwin-arm64-cgo), const IPV6_RTHDR_STRICT = 1
pkg syscall (darwin-arm64-cgo), const IPV6_RTHDR_STRICT ideal-int
pkg syscall (darwin-arm64-cgo), const IPV6_RTHDR_TYPE_0 = 0
pkg syscall (darwin-arm64-cgo), const IPV6_RTHDR_TYPE_0 ideal-int
pkg syscall (darwin-arm64-cgo), const IPV6_SOCKOPT_RESERVED1 = 3
pkg syscall (darwin-arm64-cgo), const IPV6_SOCKOPT_RESERVED1 ideal-int
pkg syscall (darwin-arm64-cgo), const IPV6_TCLASS = 36
pkg syscall (darwin-arm64-cgo), const IPV6_TCLASS ideal-int
pkg syscall (darwin-arm64-cgo), const IPV6_UNICAST_HOPS = 4
pkg syscall (darwin-arm64-cgo), const IPV6_V6ONLY = 27
pkg syscall (darwin-arm64-cgo), const IPV6_VERSION = 96
pkg syscall (darwin-arm64-cgo), const IPV6_VERSION ideal-int
pkg syscall (darwin-arm64-cgo), const IPV6_VERSION_MASK = 240
pkg syscall (darwin-arm64-cgo), const IPV6_VERSION_MASK ideal-int
pkg syscall (darwin-arm64-cgo), const IP_ADD_MEMBERSHIP = 12
pkg syscall (darwin-arm64-cgo), const IP_ADD_SOURCE_MEMBERSHIP = 70
pkg syscall (darwin-arm64-cgo), const IP_ADD_SOURCE_MEMBERSHIP ideal-int
pkg syscall (darwin-arm64-cgo), const IP_BLOCK_SOURCE = 72
pkg syscall (darwin-arm64-cgo), const IP_BLOCK_SOURCE ideal-int
pkg syscall (darwin-arm64-cgo), const IP_BOUND_IF = 25
pkg syscall (darwin-arm64-cgo), const IP_BOUND_IF ideal-int
pkg syscall (darwin-arm64-cgo), const IP_DEFAULT_MULTICAST_LOOP = 1
pkg syscall (darwin-arm64-cgo), const IP_DEFAULT_MULTICAST_LOOP ideal-int
pkg syscall (darwin-arm64-cgo), const IP_DEFAULT_MULTICAST_TTL = 1
pkg syscall (darwin-arm64-cgo), const IP_DEFAULT_MULTICAST_TTL ideal-int
pkg syscall (darwin-arm64-cgo), const IP_DF = 16384
pkg syscall (darwin-arm64-cgo), const IP_DF ideal-int
pkg syscall (darwin-arm64-cgo), const IP_DROP_MEMBERSHIP = 13
pkg syscall (darwin-arm64-cgo), const IP_DROP_SOURCE_MEMBERSHIP = 71
pkg syscall (darwin-arm64-cgo), const IP_DROP_SOURCE_MEMBERSHIP ideal-int
pkg syscall (darwin-arm64-cgo), const IP_DUMMYNET_CONFIGURE = 60
pkg syscall (darwin-arm64-cgo), const IP_DUMMYNET_CONFIGURE ideal-int
pkg syscall (darwin-arm64-cgo), const IP_DUMMYNET_DEL = 61
pkg syscall (darwin-arm64-cgo), const IP_DUMMYNET_DEL ideal-int
pkg syscall (darwin-arm64-cgo), const IP_DUMMYNET_FLUSH = 62
pkg syscall (darwin-arm64-cgo), const IP_DUMMYNET_FLUSH ideal-int
pkg syscall (darwin-arm64-cgo), const IP_DUMMYNET_GET = 64
pkg syscall (darwin-arm64-cgo), const IP_DUMMYNET_GET ideal-int
pkg syscall (darwin-arm64-cgo), const IP_FAITH = 22
pkg syscall (darwin-arm64-cgo), const IP_FAITH ideal-int
pkg syscall (darwin-arm64-cgo), const IP_FW_ADD = 40
pkg syscall (darwin-arm64-cgo), const IP_FW_ADD ideal-int
pkg syscall (darwin-arm64-cgo), const IP_FW_DEL = 41
pkg syscall (darwin-arm64-cgo), const IP_FW_DEL ideal-int
pkg syscall (darwin-arm64-cgo), const IP_FW_FLUSH = 42
pkg syscall (darwin-arm64-cgo), const IP_FW_FLUSH ideal-int
pkg syscall (darwin-arm64-cgo), const IP_FW_GET = 44
pkg syscall (darwin-arm64-cgo), const IP_FW_GET ideal-int
pkg syscall (darwin-arm64-cgo), const IP_FW_RESETLOG = 45
pkg syscall (darwin-arm64-cgo), const IP_FW_RESETLOG ideal-int
pkg syscall (darwin-arm64-cgo), const IP_FW_ZERO = 43
pkg syscall (darwin-arm64-cgo), const IP_FW_ZERO ideal-int
pkg syscall (darwin-arm64-cgo), const IP_HDRINCL = 2
pkg syscall (darwin-arm64-cgo), const IP_HDRINCL ideal-int
pkg syscall (darwin-arm64-cgo), const IP_IPSEC_POLICY = 21
pkg syscall (darwin-arm64-cgo), const IP_IPSEC_POLICY ideal-int
pkg syscall (darwin-arm64-cgo), const IP_MAXPACKET = 65535
pkg syscall (darwin-arm64-cgo), const IP_MAXPACKET ideal-int
pkg syscall (darwin-arm64-cgo), const IP_MAX_GROUP_SRC_FILTER = 512
pkg syscall (darwin-arm64-cgo), const IP_MAX_GROUP_SRC_FILTER ideal-int
pkg syscall (darwin-arm64-cgo), const IP_MAX_MEMBERSHIPS = 4095
pkg syscall (darwin-arm64-cgo), const IP_MAX_MEMBERSHIPS ideal-int
pkg syscall (darwin-arm64-cgo), const IP_MAX_SOCK_MUTE_FILTER = 128
pkg syscall (darwin-arm64-cgo), const IP_MAX_SOCK_MUTE_FILTER ideal-int
pkg syscall (darwin-arm64-cgo), const IP_MAX_SOCK_SRC_FILTER = 128
pkg syscall (darwin-arm64-cgo), const IP_MAX_SOCK_SRC_FILTER ideal-int
pkg syscall (darwin-arm64-cgo), const IP_MF = 8192
pkg syscall (darwin-arm64-cgo), const IP_MF ideal-int
pkg syscall (darwin-arm64-cgo), const IP_MIN_MEMBERSHIPS = 31
pkg syscall (darwin-arm64-cgo), const IP_MIN_MEMBERSHIPS ideal-int
pkg syscall (darwin-arm64-cgo), const IP_MSFILTER = 74
pkg syscall (darwin-arm64-cgo), const IP_MSFILTER ideal-int
pkg syscall (darwin-arm64-cgo), const IP_MSS = 576
pkg syscall (darwin-arm64-cgo), const IP_MSS ideal-int
pkg syscall (darwin-arm64-cgo), const IP_MULTICAST_IF = 9
pkg syscall (darwin-arm64-cgo), const IP_MULTICAST_IFINDEX = 66
pkg syscall (darwin-arm64-cgo), const IP_MULTICAST_IFINDEX ideal-int
pkg syscall (darwin-arm64-cgo), const IP_MULTICAST_LOOP = 11
pkg syscall (darwin-arm64-cgo), const IP_MULTICAST_TTL = 10
pkg syscall (darwin-arm64-cgo), const IP_MULTICAST_VIF = 14
pkg syscall (darwin-arm64-cgo), const IP_MULTICAST_VIF ideal-int
pkg syscall (darwin-arm64-cgo), const IP_NAT__XXX = 55
pkg syscall (darwin-arm64-cgo), const IP_NAT__XXX ideal-int
pkg syscall (darwin-arm64-cgo), const IP_OFFMASK = 8191
pkg syscall (darwin-arm64-cgo), const IP_OFFMASK ideal-int
pkg syscall (darwin-arm64-cgo), const IP_OLD_FW_ADD = 50
pkg syscall (darwin-arm64-cgo), const IP_OLD_FW_ADD ideal-int
pkg syscall (darwin-arm64-cgo), const IP_OLD_FW_DEL = 51
pkg syscall (darwin-arm64-cgo), const IP_OLD_FW_DEL ideal-int
pkg syscall (darwin-arm64-cgo), const IP_OLD_FW_FLUSH = 52
pkg syscall (darwin-arm64-cgo), const IP_OLD_FW_FLUSH ideal-int
pkg syscall (darwin-arm64-cgo), const IP_OLD_FW_GET = 54
pkg syscall (darwin-arm64-cgo), const IP_OLD_FW_GET ideal-int
pkg syscall (darwin-arm64-cgo), const IP_OLD_FW_RESETLOG = 56
pkg syscall (darwin-arm64-cgo), const IP_OLD_FW_RESETLOG ideal-int
pkg syscall (darwin-arm64-cgo), const IP_OLD_FW_ZERO = 53
pkg syscall (darwin-arm64-cgo), const IP_OLD_FW_ZERO ideal-int
pkg syscall (darwin-arm64-cgo), const IP_OPTIONS = 1
pkg syscall (darwin-arm64-cgo), const IP_OPTIONS ideal-int
pkg syscall (darwin-arm64-cgo), const IP_PKTINFO = 26
pkg syscall (darwin-arm64-cgo), const IP_PKTINFO ideal-int
pkg syscall (darwin-arm64-cgo), const IP_PORTRANGE = 19
pkg syscall (darwin-arm64-cgo), const IP_PORTRANGE ideal-int
pkg syscall (darwin-arm64-cgo), const IP_PORTRANGE_DEFAULT = 0
pkg syscall (darwin-arm64-cgo), const IP_PORTRANGE_DEFAULT ideal-int
pkg syscall (darwin-arm64-cgo), const IP_PORTRANGE_HIGH = 1
pkg syscall (darwin-arm64-cgo), const IP_PORTRANGE_HIGH ideal-int
pkg syscall (darwin-arm64-cgo), const IP_PORTRANGE_LOW = 2
pkg syscall (darwin-arm64-cgo), const IP_PORTRANGE_LOW ideal-int
pkg syscall (darwin-arm64-cgo), const IP_RECVDSTADDR = 7
pkg syscall (darwin-arm64-cgo), const IP_RECVDSTADDR ideal-int
pkg syscall (darwin-arm64-cgo), const IP_RECVIF = 20
pkg syscall (darwin-arm64-cgo), const IP_RECVIF ideal-int
pkg syscall (darwin-arm64-cgo), const IP_RECVOPTS = 5
pkg syscall (darwin-arm64-cgo), const IP_RECVOPTS ideal-int
pkg syscall (darwin-arm64-cgo), const IP_RECVPKTINFO = 26
pkg syscall (darwin-arm64-cgo), const IP_RECVPKTINFO ideal-int
pkg syscall (darwin-arm64-cgo), const IP_RECVRETOPTS = 6
pkg syscall (darwin-arm64-cgo), const IP_RECVRETOPTS ideal-int
pkg syscall (darwin-arm64-cgo), const IP_RECVTTL = 24
pkg syscall (darwin-arm64-cgo), const IP_RECVTTL ideal-int
pkg syscall (darwin-arm64-cgo), const IP_RETOPTS = 8
pkg syscall (darwin-arm64-cgo), const IP_RETOPTS ideal-int
pkg syscall (darwin-arm64-cgo), const IP_RF = 32768
pkg syscall (darwin-arm64-cgo), const IP_RF ideal-int
pkg syscall (darwin-arm64-cgo), const IP_RSVP_OFF = 16
pkg syscall (darwin-arm64-cgo), const IP_RSVP_OFF ideal-int
pkg syscall (darwin-arm64-cgo), const IP_RSVP_ON = 15
pkg syscall (darwin-arm64-cgo), const IP_RSVP_ON ideal-int
pkg syscall (darwin-arm64-cgo), const IP_RSVP_VIF_OFF = 18
pkg syscall (darwin-arm64-cgo), const IP_RSVP_VIF_OFF ideal-int
pkg syscall (darwin-arm64-cgo), const IP_RSVP_VIF_ON = 17
pkg syscall (darwin-arm64-cgo), const IP_RSVP_VIF_ON ideal-int
pkg syscall (darwin-arm64-cgo), const IP_STRIPHDR = 23
pkg syscall (darwin-arm64-cgo), const IP_STRIPHDR ideal-int
pkg syscall (darwin-arm64-cgo), const IP_TOS = 3
pkg syscall (darwin-arm64-cgo), const IP_TRAFFIC_MGT_BACKGROUND = 65
pkg syscall (darwin-arm64-cgo), const IP_TRAFFIC_MGT_BACKGROUND ideal-int
pkg syscall (darwin-arm64-cgo), const IP_TTL = 4
pkg syscall (darwin-arm64-cgo), const IP_UNBLOCK_SOURCE = 73
pkg syscall (darwin-arm64-cgo), const IP_UNBLOCK_SOURCE ideal-int
pkg syscall (darwin-arm64-cgo), const ISIG = 128
pkg syscall (darwin-arm64-cgo), const ISIG ideal-int
pkg syscall (darwin-arm64-cgo), const ISTRIP = 32
pkg syscall (darwin-arm64-cgo), const ISTRIP ideal-int
pkg syscall (darwin-arm64-cgo), const IUTF8 = 16384
pkg syscall (darwin-arm64-cgo), const IUTF8 ideal-int
pkg syscall (darwin-arm64-cgo), const IXANY = 2048
pkg syscall (darwin-arm64-cgo), const IXANY ideal-int
pkg syscall (darwin-arm64-cgo), const IXOFF = 1024
pkg syscall (darwin-arm64-cgo), const IXOFF ideal-int
pkg syscall (darwin-arm64-cgo), const IXON = 512
pkg syscall (darwin-arm64-cgo), const IXON ideal-int
pkg syscall (darwin-arm64-cgo), const LOCK_EX = 2
pkg syscall (darwin-arm64-cgo), const LOCK_EX ideal-int
pkg syscall (darwin-arm64-cgo), const LOCK_NB = 4
pkg syscall (darwin-arm64-cgo), const LOCK_NB ideal-int
pkg syscall (darwin-arm64-cgo), const LOCK_SH = 1
pkg syscall (darwin-arm64-cgo), const LOCK_SH ideal-int
pkg syscall (darwin-arm64-cgo), const LOCK_UN = 8
pkg syscall (darwin-arm64-cgo), const LOCK_UN ideal-int
pkg syscall (darwin-arm64-cgo), const MADV_CAN_REUSE = 9
pkg syscall (darwin-arm64-cgo), const MADV_CAN_REUSE ideal-int
pkg syscall (darwin-arm64-cgo), const MADV_DONTNEED = 4
pkg syscall (darwin-arm64-cgo), const MADV_DONTNEED ideal-int
pkg syscall (darwin-arm64-cgo), const MADV_FREE = 5
pkg syscall (darwin-arm64-cgo), const MADV_FREE ideal-int
pkg syscall (darwin-arm64-cgo), const MADV_FREE_REUSABLE = 7
pkg syscall (darwin-arm64-cgo), const MADV_FREE_REUSABLE ideal-int
pkg syscall (darwin-arm64-cgo), const MADV_FREE_REUSE = 8
pkg syscall (darwin-arm64-cgo), const MADV_FREE_REUSE ideal-int
pkg syscall (darwin-arm64-cgo), const MADV_NORMAL = 0
pkg syscall (darwin-arm64-cgo), const MADV_NORMAL ideal-int
pkg syscall (darwin-arm64-cgo), const MADV_RANDOM = 1
pkg syscall (darwin-arm64-cgo), const MADV_RANDOM ideal-int
pkg syscall (darwin-arm64-cgo), const MADV_SEQUENTIAL = 2
pkg syscall (darwin-arm64-cgo), const MADV_SEQUENTIAL ideal-int
pkg syscall (darwin-arm64-cgo), const MADV_WILLNEED = 3
pkg syscall (darwin-arm64-cgo), const MADV_WILLNEED ideal-int
pkg syscall (darwin-arm64-cgo), const MADV_ZERO_WIRED_PAGES = 6
pkg syscall (darwin-arm64-cgo), const MADV_ZERO_WIRED_PAGES ideal-int
pkg syscall (darwin-arm64-cgo), const MAP_ANON = 4096
pkg syscall (darwin-arm64-cgo), const MAP_ANON ideal-int
pkg syscall (darwin-arm64-cgo), const MAP_COPY = 2
pkg syscall (darwin-arm64-cgo), const MAP_COPY ideal-int
pkg syscall (darwin-arm64-cgo), const MAP_FILE = 0
pkg syscall (darwin-arm64-cgo), const MAP_FILE ideal-int
pkg syscall (darwin-arm64-cgo), const MAP_FIXED = 16
pkg syscall (darwin-arm64-cgo), const MAP_FIXED ideal-int
pkg syscall (darwin-arm64-cgo), const MAP_HASSEMAPHORE = 512
pkg syscall (darwin-arm64-cgo), const MAP_HASSEMAPHORE ideal-int
pkg syscall (darwin-arm64-cgo), const MAP_JIT = 2048
pkg syscall (darwin-arm64-cgo), const MAP_JIT ideal-int
pkg syscall (darwin-arm64-cgo), const MAP_NOCACHE = 1024
pkg syscall (darwin-arm64-cgo), const MAP_NOCACHE ideal-int
pkg syscall (darwin-arm64-cgo), const MAP_NOEXTEND = 256
pkg syscall (darwin-arm64-cgo), const MAP_NOEXTEND ideal-int
pkg syscall (darwin-arm64-cgo), const MAP_NORESERVE = 64
pkg syscall (darwin-arm64-cgo), const MAP_NORESERVE ideal-int
pkg syscall (darwin-arm64-cgo), const MAP_PRIVATE = 2
pkg syscall (darwin-arm64-cgo), const MAP_PRIVATE ideal-int
pkg syscall (darwin-arm64-cgo), const MAP_RENAME = 32
pkg syscall (darwin-arm64-cgo), const MAP_RENAME ideal-int
pkg syscall (darwin-arm64-cgo), const MAP_RESERVED0080 = 128
pkg syscall (darwin-arm64-cgo), const MAP_RESERVED0080 ideal-int
pkg syscall (darwin-arm64-cgo), const MAP_SHARED = 1
pkg syscall (darwin-arm64-cgo), const MAP_SHARED ideal-int
pkg syscall (darwin-arm64-cgo), const MCL_CURRENT = 1
pkg syscall (darwin-arm64-cgo), const MCL_CURRENT ideal-int
pkg syscall (darwin-arm64-cgo), const MCL_FUTURE = 2
pkg syscall (darwin-arm64-cgo), const MCL_FUTURE ideal-int
pkg syscall (darwin-arm64-cgo), const MSG_CTRUNC = 32
pkg syscall (darwin-arm64-cgo), const MSG_CTRUNC ideal-int
pkg syscall (darwin-arm64-cgo), const MSG_DONTROUTE = 4
pkg syscall (darwin-arm64-cgo), const MSG_DONTROUTE ideal-int
pkg syscall (darwin-arm64-cgo), const MSG_DONTWAIT = 128
pkg syscall (darwin-arm64-cgo), const MSG_DONTWAIT ideal-int
pkg syscall (darwin-arm64-cgo), const MSG_EOF = 256
pkg syscall (darwin-arm64-cgo), const MSG_EOF ideal-int
pkg syscall (darwin-arm64-cgo), const MSG_EOR = 8
pkg syscall (darwin-arm64-cgo), const MSG_EOR ideal-int
pkg syscall (darwin-arm64-cgo), const MSG_FLUSH = 1024
pkg syscall (darwin-arm64-cgo), const MSG_FLUSH ideal-int
pkg syscall (darwin-arm64-cgo), const MSG_HAVEMORE = 8192
pkg syscall (darwin-arm64-cgo), const MSG_HAVEMORE ideal-int
pkg syscall (darwin-arm64-cgo), const MSG_HOLD = 2048
pkg syscall (darwin-arm64-cgo), const MSG_HOLD ideal-int
pkg syscall (darwin-arm64-cgo), const MSG_NEEDSA = 65536
pkg syscall (darwin-arm64-cgo), const MSG_NEEDSA ideal-int
pkg syscall (darwin-arm64-cgo), const MSG_OOB = 1
pkg syscall (darwin-arm64-cgo), const MSG_OOB ideal-int
pkg syscall (darwin-arm64-cgo), const MSG_PEEK = 2
pkg syscall (darwin-arm64-cgo), const MSG_PEEK ideal-int
pkg syscall (darwin-arm64-cgo), const MSG_RCVMORE = 16384
pkg syscall (darwin-arm64-cgo), const MSG_RCVMORE ideal-int
pkg syscall (darwin-arm64-cgo), const MSG_SEND = 4096
pkg syscall (darwin-arm64-cgo), const MSG_SEND ideal-int
pkg syscall (darwin-arm64-cgo), const MSG_TRUNC = 16
pkg syscall (darwin-arm64-cgo), const MSG_TRUNC ideal-int
pkg syscall (darwin-arm64-cgo), const MSG_WAITALL = 64
pkg syscall (darwin-arm64-cgo), const MSG_WAITALL ideal-int
pkg syscall (darwin-arm64-cgo), const MSG_WAITSTREAM = 512
pkg syscall (darwin-arm64-cgo), const MSG_WAITSTREAM ideal-int
pkg syscall (darwin-arm64-cgo), const MS_ASYNC = 1
pkg syscall (darwin-arm64-cgo), const MS_ASYNC ideal-int
pkg syscall (darwin-arm64-cgo), const MS_DEACTIVATE = 8
pkg syscall (darwin-arm64-cgo), const MS_DEACTIVATE ideal-int
pkg syscall (darwin-arm64-cgo), const MS_INVALIDATE = 2
pkg syscall (darwin-arm64-cgo), const MS_INVALIDATE ideal-int
pkg syscall (darwin-arm64-cgo), const MS_KILLPAGES = 4
pkg syscall (darwin-arm64-cgo), const MS_KILLPAGES ideal-int
pkg syscall (darwin-arm64-cgo), const MS_SYNC = 16
pkg syscall (darwin-arm64-cgo), const MS_SYNC ideal-int
pkg syscall (darwin-arm64-cgo), const NAME_MAX = 255
pkg syscall (darwin-arm64-cgo), const NAME_MAX ideal-int
pkg syscall (darwin-arm64-cgo), const NET_RT_DUMP = 1
pkg syscall (darwin-arm64-cgo), const NET_RT_DUMP ideal-int
pkg syscall (darwin-arm64-cgo), const NET_RT_DUMP2 = 7
pkg syscall (darwin-arm64-cgo), const NET_RT_DUMP2 ideal-int
pkg syscall (darwin-arm64-cgo), const NET_RT_FLAGS = 2
pkg syscall (darwin-arm64-cgo), const NET_RT_FLAGS ideal-int
pkg syscall (darwin-arm64-cgo), const NET_RT_IFLIST = 3
pkg syscall (darwin-arm64-cgo), const NET_RT_IFLIST ideal-int
pkg syscall (darwin-arm64-cgo), const NET_RT_IFLIST2 = 6
pkg syscall (darwin-arm64-cgo), const NET_RT_IFLIST2 ideal-int
pkg syscall (darwin-arm64-cgo), const NET_RT_MAXID = 10
pkg syscall (darwin-arm64-cgo), const NET_RT_MAXID ideal-int
pkg syscall (darwin-arm64-cgo), const NET_RT_STAT = 4
pkg syscall (darwin-arm64-cgo), const NET_RT_STAT ideal-int
pkg syscall (darwin-arm64-cgo), const NET_RT_TRASH = 5
pkg syscall (darwin-arm64-cgo), const NET_RT_TRASH ideal-int
pkg syscall (darwin-arm64-cgo), const NOFLSH = 2147483648
pkg syscall (darwin-arm64-cgo), const NOFLSH ideal-int
pkg syscall (darwin-arm64-cgo), const NOTE_ABSOLUTE = 8
pkg syscall (darwin-arm64-cgo), const NOTE_ABSOLUTE ideal-int
pkg syscall (darwin-arm64-cgo), const NOTE_ATTRIB = 8
pkg syscall (darwin-arm64-cgo), const NOTE_ATTRIB ideal-int
pkg syscall (darwin-arm64-cgo), const NOTE_BACKGROUND = 64
pkg syscall (darwin-arm64-cgo), const NOTE_BACKGROUND ideal-int
pkg syscall (darwin-arm64-cgo), const NOTE_CHILD = 4
pkg syscall (darwin-arm64-cgo), const NOTE_CHILD ideal-int
pkg syscall (darwin-arm64-cgo), const NOTE_CRITICAL = 32
pkg syscall (darwin-arm64-cgo), const NOTE_CRITICAL ideal-int
pkg syscall (darwin-arm64-cgo), const NOTE_DELETE = 1
pkg syscall (darwin-arm64-cgo), const NOTE_DELETE ideal-int
pkg syscall (darwin-arm64-cgo), const NOTE_EXEC = *********
pkg syscall (darwin-arm64-cgo), const NOTE_EXEC ideal-int
pkg syscall (darwin-arm64-cgo), const NOTE_EXIT = 2147483648
pkg syscall (darwin-arm64-cgo), const NOTE_EXIT ideal-int
pkg syscall (darwin-arm64-cgo), const NOTE_EXITSTATUS = 67108864
pkg syscall (darwin-arm64-cgo), const NOTE_EXITSTATUS ideal-int
pkg syscall (darwin-arm64-cgo), const NOTE_EXIT_CSERROR = 262144
pkg syscall (darwin-arm64-cgo), const NOTE_EXIT_CSERROR ideal-int
pkg syscall (darwin-arm64-cgo), const NOTE_EXIT_DECRYPTFAIL = 65536
pkg syscall (darwin-arm64-cgo), const NOTE_EXIT_DECRYPTFAIL ideal-int
pkg syscall (darwin-arm64-cgo), const NOTE_EXIT_DETAIL = 33554432
pkg syscall (darwin-arm64-cgo), const NOTE_EXIT_DETAIL ideal-int
pkg syscall (darwin-arm64-cgo), const NOTE_EXIT_DETAIL_MASK = 458752
pkg syscall (darwin-arm64-cgo), const NOTE_EXIT_DETAIL_MASK ideal-int
pkg syscall (darwin-arm64-cgo), const NOTE_EXIT_MEMORY = 131072
pkg syscall (darwin-arm64-cgo), const NOTE_EXIT_MEMORY ideal-int
pkg syscall (darwin-arm64-cgo), const NOTE_EXIT_REPARENTED = 524288
pkg syscall (darwin-arm64-cgo), const NOTE_EXIT_REPARENTED ideal-int
pkg syscall (darwin-arm64-cgo), const NOTE_EXTEND = 4
pkg syscall (darwin-arm64-cgo), const NOTE_EXTEND ideal-int
pkg syscall (darwin-arm64-cgo), const NOTE_FFAND = 1073741824
pkg syscall (darwin-arm64-cgo), const NOTE_FFAND ideal-int
pkg syscall (darwin-arm64-cgo), const NOTE_FFCOPY = 3221225472
pkg syscall (darwin-arm64-cgo), const NOTE_FFCOPY ideal-int
pkg syscall (darwin-arm64-cgo), const NOTE_FFCTRLMASK = 3221225472
pkg syscall (darwin-arm64-cgo), const NOTE_FFCTRLMASK ideal-int
pkg syscall (darwin-arm64-cgo), const NOTE_FFLAGSMASK = 16777215
pkg syscall (darwin-arm64-cgo), const NOTE_FFLAGSMASK ideal-int
pkg syscall (darwin-arm64-cgo), const NOTE_FFNOP = 0
pkg syscall (darwin-arm64-cgo), const NOTE_FFNOP ideal-int
pkg syscall (darwin-arm64-cgo), const NOTE_FFOR = 2147483648
pkg syscall (darwin-arm64-cgo), const NOTE_FFOR ideal-int
pkg syscall (darwin-arm64-cgo), const NOTE_FORK = 1073741824
pkg syscall (darwin-arm64-cgo), const NOTE_FORK ideal-int
pkg syscall (darwin-arm64-cgo), const NOTE_LEEWAY = 16
pkg syscall (darwin-arm64-cgo), const NOTE_LEEWAY ideal-int
pkg syscall (darwin-arm64-cgo), const NOTE_LINK = 16
pkg syscall (darwin-arm64-cgo), const NOTE_LINK ideal-int
pkg syscall (darwin-arm64-cgo), const NOTE_LOWAT = 1
pkg syscall (darwin-arm64-cgo), const NOTE_LOWAT ideal-int
pkg syscall (darwin-arm64-cgo), const NOTE_NONE = 128
pkg syscall (darwin-arm64-cgo), const NOTE_NONE ideal-int
pkg syscall (darwin-arm64-cgo), const NOTE_NSECONDS = 4
pkg syscall (darwin-arm64-cgo), const NOTE_NSECONDS ideal-int
pkg syscall (darwin-arm64-cgo), const NOTE_PCTRLMASK = -1048576
pkg syscall (darwin-arm64-cgo), const NOTE_PCTRLMASK ideal-int
pkg syscall (darwin-arm64-cgo), const NOTE_PDATAMASK = 1048575
pkg syscall (darwin-arm64-cgo), const NOTE_PDATAMASK ideal-int
pkg syscall (darwin-arm64-cgo), const NOTE_REAP = 268435456
pkg syscall (darwin-arm64-cgo), const NOTE_REAP ideal-int
pkg syscall (darwin-arm64-cgo), const NOTE_RENAME = 32
pkg syscall (darwin-arm64-cgo), const NOTE_RENAME ideal-int
pkg syscall (darwin-arm64-cgo), const NOTE_REVOKE = 64
pkg syscall (darwin-arm64-cgo), const NOTE_REVOKE ideal-int
pkg syscall (darwin-arm64-cgo), const NOTE_SECONDS = 1
pkg syscall (darwin-arm64-cgo), const NOTE_SECONDS ideal-int
pkg syscall (darwin-arm64-cgo), const NOTE_SIGNAL = 134217728
pkg syscall (darwin-arm64-cgo), const NOTE_SIGNAL ideal-int
pkg syscall (darwin-arm64-cgo), const NOTE_TRACK = 1
pkg syscall (darwin-arm64-cgo), const NOTE_TRACK ideal-int
pkg syscall (darwin-arm64-cgo), const NOTE_TRACKERR = 2
pkg syscall (darwin-arm64-cgo), const NOTE_TRACKERR ideal-int
pkg syscall (darwin-arm64-cgo), const NOTE_TRIGGER = 16777216
pkg syscall (darwin-arm64-cgo), const NOTE_TRIGGER ideal-int
pkg syscall (darwin-arm64-cgo), const NOTE_USECONDS = 2
pkg syscall (darwin-arm64-cgo), const NOTE_USECONDS ideal-int
pkg syscall (darwin-arm64-cgo), const NOTE_VM_ERROR = 268435456
pkg syscall (darwin-arm64-cgo), const NOTE_VM_ERROR ideal-int
pkg syscall (darwin-arm64-cgo), const NOTE_VM_PRESSURE = 2147483648
pkg syscall (darwin-arm64-cgo), const NOTE_VM_PRESSURE ideal-int
pkg syscall (darwin-arm64-cgo), const NOTE_VM_PRESSURE_SUDDEN_TERMINATE = *********
pkg syscall (darwin-arm64-cgo), const NOTE_VM_PRESSURE_SUDDEN_TERMINATE ideal-int
pkg syscall (darwin-arm64-cgo), const NOTE_VM_PRESSURE_TERMINATE = 1073741824
pkg syscall (darwin-arm64-cgo), const NOTE_VM_PRESSURE_TERMINATE ideal-int
pkg syscall (darwin-arm64-cgo), const NOTE_WRITE = 2
pkg syscall (darwin-arm64-cgo), const NOTE_WRITE ideal-int
pkg syscall (darwin-arm64-cgo), const OCRNL = 16
pkg syscall (darwin-arm64-cgo), const OCRNL ideal-int
pkg syscall (darwin-arm64-cgo), const OFDEL = 131072
pkg syscall (darwin-arm64-cgo), const OFDEL ideal-int
pkg syscall (darwin-arm64-cgo), const OFILL = 128
pkg syscall (darwin-arm64-cgo), const OFILL ideal-int
pkg syscall (darwin-arm64-cgo), const ONLCR = 2
pkg syscall (darwin-arm64-cgo), const ONLCR ideal-int
pkg syscall (darwin-arm64-cgo), const ONLRET = 64
pkg syscall (darwin-arm64-cgo), const ONLRET ideal-int
pkg syscall (darwin-arm64-cgo), const ONOCR = 32
pkg syscall (darwin-arm64-cgo), const ONOCR ideal-int
pkg syscall (darwin-arm64-cgo), const ONOEOT = 8
pkg syscall (darwin-arm64-cgo), const ONOEOT ideal-int
pkg syscall (darwin-arm64-cgo), const OPOST = 1
pkg syscall (darwin-arm64-cgo), const OPOST ideal-int
pkg syscall (darwin-arm64-cgo), const O_ACCMODE = 3
pkg syscall (darwin-arm64-cgo), const O_ACCMODE ideal-int
pkg syscall (darwin-arm64-cgo), const O_ALERT = *********
pkg syscall (darwin-arm64-cgo), const O_ALERT ideal-int
pkg syscall (darwin-arm64-cgo), const O_APPEND = 8
pkg syscall (darwin-arm64-cgo), const O_ASYNC = 64
pkg syscall (darwin-arm64-cgo), const O_CLOEXEC = 16777216
pkg syscall (darwin-arm64-cgo), const O_CREAT = 512
pkg syscall (darwin-arm64-cgo), const O_DIRECTORY = 1048576
pkg syscall (darwin-arm64-cgo), const O_DIRECTORY ideal-int
pkg syscall (darwin-arm64-cgo), const O_DP_GETRAWENCRYPTED = 1
pkg syscall (darwin-arm64-cgo), const O_DP_GETRAWENCRYPTED ideal-int
pkg syscall (darwin-arm64-cgo), const O_DSYNC = 4194304
pkg syscall (darwin-arm64-cgo), const O_DSYNC ideal-int
pkg syscall (darwin-arm64-cgo), const O_EVTONLY = 32768
pkg syscall (darwin-arm64-cgo), const O_EVTONLY ideal-int
pkg syscall (darwin-arm64-cgo), const O_EXCL = 2048
pkg syscall (darwin-arm64-cgo), const O_EXLOCK = 32
pkg syscall (darwin-arm64-cgo), const O_EXLOCK ideal-int
pkg syscall (darwin-arm64-cgo), const O_FSYNC = 128
pkg syscall (darwin-arm64-cgo), const O_FSYNC ideal-int
pkg syscall (darwin-arm64-cgo), const O_NDELAY = 4
pkg syscall (darwin-arm64-cgo), const O_NDELAY ideal-int
pkg syscall (darwin-arm64-cgo), const O_NOCTTY = 131072
pkg syscall (darwin-arm64-cgo), const O_NOFOLLOW = 256
pkg syscall (darwin-arm64-cgo), const O_NOFOLLOW ideal-int
pkg syscall (darwin-arm64-cgo), const O_NONBLOCK = 4
pkg syscall (darwin-arm64-cgo), const O_POPUP = 2147483648
pkg syscall (darwin-arm64-cgo), const O_POPUP ideal-int
pkg syscall (darwin-arm64-cgo), const O_SHLOCK = 16
pkg syscall (darwin-arm64-cgo), const O_SHLOCK ideal-int
pkg syscall (darwin-arm64-cgo), const O_SYMLINK = 2097152
pkg syscall (darwin-arm64-cgo), const O_SYMLINK ideal-int
pkg syscall (darwin-arm64-cgo), const O_SYNC = 128
pkg syscall (darwin-arm64-cgo), const O_TRUNC = 1024
pkg syscall (darwin-arm64-cgo), const PARENB = 4096
pkg syscall (darwin-arm64-cgo), const PARENB ideal-int
pkg syscall (darwin-arm64-cgo), const PARMRK = 8
pkg syscall (darwin-arm64-cgo), const PARMRK ideal-int
pkg syscall (darwin-arm64-cgo), const PARODD = 8192
pkg syscall (darwin-arm64-cgo), const PARODD ideal-int
pkg syscall (darwin-arm64-cgo), const PENDIN = *********
pkg syscall (darwin-arm64-cgo), const PENDIN ideal-int
pkg syscall (darwin-arm64-cgo), const PRIO_PGRP = 1
pkg syscall (darwin-arm64-cgo), const PRIO_PGRP ideal-int
pkg syscall (darwin-arm64-cgo), const PRIO_PROCESS = 0
pkg syscall (darwin-arm64-cgo), const PRIO_PROCESS ideal-int
pkg syscall (darwin-arm64-cgo), const PRIO_USER = 2
pkg syscall (darwin-arm64-cgo), const PRIO_USER ideal-int
pkg syscall (darwin-arm64-cgo), const PROT_EXEC = 4
pkg syscall (darwin-arm64-cgo), const PROT_EXEC ideal-int
pkg syscall (darwin-arm64-cgo), const PROT_NONE = 0
pkg syscall (darwin-arm64-cgo), const PROT_NONE ideal-int
pkg syscall (darwin-arm64-cgo), const PROT_READ = 1
pkg syscall (darwin-arm64-cgo), const PROT_READ ideal-int
pkg syscall (darwin-arm64-cgo), const PROT_WRITE = 2
pkg syscall (darwin-arm64-cgo), const PROT_WRITE ideal-int
pkg syscall (darwin-arm64-cgo), const PTRACE_CONT = 7
pkg syscall (darwin-arm64-cgo), const PTRACE_CONT ideal-int
pkg syscall (darwin-arm64-cgo), const PTRACE_KILL = 8
pkg syscall (darwin-arm64-cgo), const PTRACE_KILL ideal-int
pkg syscall (darwin-arm64-cgo), const PTRACE_TRACEME = 0
pkg syscall (darwin-arm64-cgo), const PTRACE_TRACEME ideal-int
pkg syscall (darwin-arm64-cgo), const PT_ATTACH = 10
pkg syscall (darwin-arm64-cgo), const PT_ATTACH ideal-int
pkg syscall (darwin-arm64-cgo), const PT_ATTACHEXC = 14
pkg syscall (darwin-arm64-cgo), const PT_ATTACHEXC ideal-int
pkg syscall (darwin-arm64-cgo), const PT_CONTINUE = 7
pkg syscall (darwin-arm64-cgo), const PT_CONTINUE ideal-int
pkg syscall (darwin-arm64-cgo), const PT_DENY_ATTACH = 31
pkg syscall (darwin-arm64-cgo), const PT_DENY_ATTACH ideal-int
pkg syscall (darwin-arm64-cgo), const PT_DETACH = 11
pkg syscall (darwin-arm64-cgo), const PT_DETACH ideal-int
pkg syscall (darwin-arm64-cgo), const PT_FIRSTMACH = 32
pkg syscall (darwin-arm64-cgo), const PT_FIRSTMACH ideal-int
pkg syscall (darwin-arm64-cgo), const PT_FORCEQUOTA = 30
pkg syscall (darwin-arm64-cgo), const PT_FORCEQUOTA ideal-int
pkg syscall (darwin-arm64-cgo), const PT_KILL = 8
pkg syscall (darwin-arm64-cgo), const PT_KILL ideal-int
pkg syscall (darwin-arm64-cgo), const PT_READ_D = 2
pkg syscall (darwin-arm64-cgo), const PT_READ_D ideal-int
pkg syscall (darwin-arm64-cgo), const PT_READ_I = 1
pkg syscall (darwin-arm64-cgo), const PT_READ_I ideal-int
pkg syscall (darwin-arm64-cgo), const PT_READ_U = 3
pkg syscall (darwin-arm64-cgo), const PT_READ_U ideal-int
pkg syscall (darwin-arm64-cgo), const PT_SIGEXC = 12
pkg syscall (darwin-arm64-cgo), const PT_SIGEXC ideal-int
pkg syscall (darwin-arm64-cgo), const PT_STEP = 9
pkg syscall (darwin-arm64-cgo), const PT_STEP ideal-int
pkg syscall (darwin-arm64-cgo), const PT_THUPDATE = 13
pkg syscall (darwin-arm64-cgo), const PT_THUPDATE ideal-int
pkg syscall (darwin-arm64-cgo), const PT_TRACE_ME = 0
pkg syscall (darwin-arm64-cgo), const PT_TRACE_ME ideal-int
pkg syscall (darwin-arm64-cgo), const PT_WRITE_D = 5
pkg syscall (darwin-arm64-cgo), const PT_WRITE_D ideal-int
pkg syscall (darwin-arm64-cgo), const PT_WRITE_I = 4
pkg syscall (darwin-arm64-cgo), const PT_WRITE_I ideal-int
pkg syscall (darwin-arm64-cgo), const PT_WRITE_U = 6
pkg syscall (darwin-arm64-cgo), const PT_WRITE_U ideal-int
pkg syscall (darwin-arm64-cgo), const RLIMIT_AS = 5
pkg syscall (darwin-arm64-cgo), const RLIMIT_AS ideal-int
pkg syscall (darwin-arm64-cgo), const RLIMIT_CORE = 4
pkg syscall (darwin-arm64-cgo), const RLIMIT_CORE ideal-int
pkg syscall (darwin-arm64-cgo), const RLIMIT_CPU = 0
pkg syscall (darwin-arm64-cgo), const RLIMIT_CPU ideal-int
pkg syscall (darwin-arm64-cgo), const RLIMIT_CPU_USAGE_MONITOR = 2
pkg syscall (darwin-arm64-cgo), const RLIMIT_CPU_USAGE_MONITOR ideal-int
pkg syscall (darwin-arm64-cgo), const RLIMIT_DATA = 2
pkg syscall (darwin-arm64-cgo), const RLIMIT_DATA ideal-int
pkg syscall (darwin-arm64-cgo), const RLIMIT_FSIZE = 1
pkg syscall (darwin-arm64-cgo), const RLIMIT_FSIZE ideal-int
pkg syscall (darwin-arm64-cgo), const RLIMIT_NOFILE = 8
pkg syscall (darwin-arm64-cgo), const RLIMIT_NOFILE ideal-int
pkg syscall (darwin-arm64-cgo), const RLIMIT_STACK = 3
pkg syscall (darwin-arm64-cgo), const RLIMIT_STACK ideal-int
pkg syscall (darwin-arm64-cgo), const RLIM_INFINITY = 9223372036854775807
pkg syscall (darwin-arm64-cgo), const RLIM_INFINITY ideal-int
pkg syscall (darwin-arm64-cgo), const RTAX_AUTHOR = 6
pkg syscall (darwin-arm64-cgo), const RTAX_AUTHOR ideal-int
pkg syscall (darwin-arm64-cgo), const RTAX_BRD = 7
pkg syscall (darwin-arm64-cgo), const RTAX_BRD ideal-int
pkg syscall (darwin-arm64-cgo), const RTAX_DST = 0
pkg syscall (darwin-arm64-cgo), const RTAX_DST ideal-int
pkg syscall (darwin-arm64-cgo), const RTAX_GATEWAY = 1
pkg syscall (darwin-arm64-cgo), const RTAX_GATEWAY ideal-int
pkg syscall (darwin-arm64-cgo), const RTAX_GENMASK = 3
pkg syscall (darwin-arm64-cgo), const RTAX_GENMASK ideal-int
pkg syscall (darwin-arm64-cgo), const RTAX_IFA = 5
pkg syscall (darwin-arm64-cgo), const RTAX_IFA ideal-int
pkg syscall (darwin-arm64-cgo), const RTAX_IFP = 4
pkg syscall (darwin-arm64-cgo), const RTAX_IFP ideal-int
pkg syscall (darwin-arm64-cgo), const RTAX_MAX = 8
pkg syscall (darwin-arm64-cgo), const RTAX_MAX ideal-int
pkg syscall (darwin-arm64-cgo), const RTAX_NETMASK = 2
pkg syscall (darwin-arm64-cgo), const RTAX_NETMASK ideal-int
pkg syscall (darwin-arm64-cgo), const RTA_AUTHOR = 64
pkg syscall (darwin-arm64-cgo), const RTA_AUTHOR ideal-int
pkg syscall (darwin-arm64-cgo), const RTA_BRD = 128
pkg syscall (darwin-arm64-cgo), const RTA_BRD ideal-int
pkg syscall (darwin-arm64-cgo), const RTA_DST = 1
pkg syscall (darwin-arm64-cgo), const RTA_DST ideal-int
pkg syscall (darwin-arm64-cgo), const RTA_GATEWAY = 2
pkg syscall (darwin-arm64-cgo), const RTA_GATEWAY ideal-int
pkg syscall (darwin-arm64-cgo), const RTA_GENMASK = 8
pkg syscall (darwin-arm64-cgo), const RTA_GENMASK ideal-int
pkg syscall (darwin-arm64-cgo), const RTA_IFA = 32
pkg syscall (darwin-arm64-cgo), const RTA_IFA ideal-int
pkg syscall (darwin-arm64-cgo), const RTA_IFP = 16
pkg syscall (darwin-arm64-cgo), const RTA_IFP ideal-int
pkg syscall (darwin-arm64-cgo), const RTA_NETMASK = 4
pkg syscall (darwin-arm64-cgo), const RTA_NETMASK ideal-int
pkg syscall (darwin-arm64-cgo), const RTF_BLACKHOLE = 4096
pkg syscall (darwin-arm64-cgo), const RTF_BLACKHOLE ideal-int
pkg syscall (darwin-arm64-cgo), const RTF_BROADCAST = 4194304
pkg syscall (darwin-arm64-cgo), const RTF_BROADCAST ideal-int
pkg syscall (darwin-arm64-cgo), const RTF_CLONING = 256
pkg syscall (darwin-arm64-cgo), const RTF_CLONING ideal-int
pkg syscall (darwin-arm64-cgo), const RTF_CONDEMNED = 33554432
pkg syscall (darwin-arm64-cgo), const RTF_CONDEMNED ideal-int
pkg syscall (darwin-arm64-cgo), const RTF_DELCLONE = 128
pkg syscall (darwin-arm64-cgo), const RTF_DELCLONE ideal-int
pkg syscall (darwin-arm64-cgo), const RTF_DONE = 64
pkg syscall (darwin-arm64-cgo), const RTF_DONE ideal-int
pkg syscall (darwin-arm64-cgo), const RTF_DYNAMIC = 16
pkg syscall (darwin-arm64-cgo), const RTF_DYNAMIC ideal-int
pkg syscall (darwin-arm64-cgo), const RTF_GATEWAY = 2
pkg syscall (darwin-arm64-cgo), const RTF_GATEWAY ideal-int
pkg syscall (darwin-arm64-cgo), const RTF_HOST = 4
pkg syscall (darwin-arm64-cgo), const RTF_HOST ideal-int
pkg syscall (darwin-arm64-cgo), const RTF_IFREF = 67108864
pkg syscall (darwin-arm64-cgo), const RTF_IFREF ideal-int
pkg syscall (darwin-arm64-cgo), const RTF_IFSCOPE = 16777216
pkg syscall (darwin-arm64-cgo), const RTF_IFSCOPE ideal-int
pkg syscall (darwin-arm64-cgo), const RTF_LLINFO = 1024
pkg syscall (darwin-arm64-cgo), const RTF_LLINFO ideal-int
pkg syscall (darwin-arm64-cgo), const RTF_LOCAL = 2097152
pkg syscall (darwin-arm64-cgo), const RTF_LOCAL ideal-int
pkg syscall (darwin-arm64-cgo), const RTF_MODIFIED = 32
pkg syscall (darwin-arm64-cgo), const RTF_MODIFIED ideal-int
pkg syscall (darwin-arm64-cgo), const RTF_MULTICAST = 8388608
pkg syscall (darwin-arm64-cgo), const RTF_MULTICAST ideal-int
pkg syscall (darwin-arm64-cgo), const RTF_PINNED = 1048576
pkg syscall (darwin-arm64-cgo), const RTF_PINNED ideal-int
pkg syscall (darwin-arm64-cgo), const RTF_PRCLONING = 65536
pkg syscall (darwin-arm64-cgo), const RTF_PRCLONING ideal-int
pkg syscall (darwin-arm64-cgo), const RTF_PROTO1 = 32768
pkg syscall (darwin-arm64-cgo), const RTF_PROTO1 ideal-int
pkg syscall (darwin-arm64-cgo), const RTF_PROTO2 = 16384
pkg syscall (darwin-arm64-cgo), const RTF_PROTO2 ideal-int
pkg syscall (darwin-arm64-cgo), const RTF_PROTO3 = 262144
pkg syscall (darwin-arm64-cgo), const RTF_PROTO3 ideal-int
pkg syscall (darwin-arm64-cgo), const RTF_PROXY = 134217728
pkg syscall (darwin-arm64-cgo), const RTF_PROXY ideal-int
pkg syscall (darwin-arm64-cgo), const RTF_REJECT = 8
pkg syscall (darwin-arm64-cgo), const RTF_REJECT ideal-int
pkg syscall (darwin-arm64-cgo), const RTF_ROUTER = 268435456
pkg syscall (darwin-arm64-cgo), const RTF_ROUTER ideal-int
pkg syscall (darwin-arm64-cgo), const RTF_STATIC = 2048
pkg syscall (darwin-arm64-cgo), const RTF_STATIC ideal-int
pkg syscall (darwin-arm64-cgo), const RTF_UP = 1
pkg syscall (darwin-arm64-cgo), const RTF_UP ideal-int
pkg syscall (darwin-arm64-cgo), const RTF_WASCLONED = 131072
pkg syscall (darwin-arm64-cgo), const RTF_WASCLONED ideal-int
pkg syscall (darwin-arm64-cgo), const RTF_XRESOLVE = 512
pkg syscall (darwin-arm64-cgo), const RTF_XRESOLVE ideal-int
pkg syscall (darwin-arm64-cgo), const RTM_ADD = 1
pkg syscall (darwin-arm64-cgo), const RTM_ADD ideal-int
pkg syscall (darwin-arm64-cgo), const RTM_CHANGE = 3
pkg syscall (darwin-arm64-cgo), const RTM_CHANGE ideal-int
pkg syscall (darwin-arm64-cgo), const RTM_DELADDR = 13
pkg syscall (darwin-arm64-cgo), const RTM_DELADDR ideal-int
pkg syscall (darwin-arm64-cgo), const RTM_DELETE = 2
pkg syscall (darwin-arm64-cgo), const RTM_DELETE ideal-int
pkg syscall (darwin-arm64-cgo), const RTM_DELMADDR = 16
pkg syscall (darwin-arm64-cgo), const RTM_DELMADDR ideal-int
pkg syscall (darwin-arm64-cgo), const RTM_GET = 4
pkg syscall (darwin-arm64-cgo), const RTM_GET ideal-int
pkg syscall (darwin-arm64-cgo), const RTM_GET2 = 20
pkg syscall (darwin-arm64-cgo), const RTM_GET2 ideal-int
pkg syscall (darwin-arm64-cgo), const RTM_IFINFO = 14
pkg syscall (darwin-arm64-cgo), const RTM_IFINFO ideal-int
pkg syscall (darwin-arm64-cgo), const RTM_IFINFO2 = 18
pkg syscall (darwin-arm64-cgo), const RTM_IFINFO2 ideal-int
pkg syscall (darwin-arm64-cgo), const RTM_LOCK = 8
pkg syscall (darwin-arm64-cgo), const RTM_LOCK ideal-int
pkg syscall (darwin-arm64-cgo), const RTM_LOSING = 5
pkg syscall (darwin-arm64-cgo), const RTM_LOSING ideal-int
pkg syscall (darwin-arm64-cgo), const RTM_MISS = 7
pkg syscall (darwin-arm64-cgo), const RTM_MISS ideal-int
pkg syscall (darwin-arm64-cgo), const RTM_NEWADDR = 12
pkg syscall (darwin-arm64-cgo), const RTM_NEWADDR ideal-int
pkg syscall (darwin-arm64-cgo), const RTM_NEWMADDR = 15
pkg syscall (darwin-arm64-cgo), const RTM_NEWMADDR ideal-int
pkg syscall (darwin-arm64-cgo), const RTM_NEWMADDR2 = 19
pkg syscall (darwin-arm64-cgo), const RTM_NEWMADDR2 ideal-int
pkg syscall (darwin-arm64-cgo), const RTM_OLDADD = 9
pkg syscall (darwin-arm64-cgo), const RTM_OLDADD ideal-int
pkg syscall (darwin-arm64-cgo), const RTM_OLDDEL = 10
pkg syscall (darwin-arm64-cgo), const RTM_OLDDEL ideal-int
pkg syscall (darwin-arm64-cgo), const RTM_REDIRECT = 6
pkg syscall (darwin-arm64-cgo), const RTM_REDIRECT ideal-int
pkg syscall (darwin-arm64-cgo), const RTM_RESOLVE = 11
pkg syscall (darwin-arm64-cgo), const RTM_RESOLVE ideal-int
pkg syscall (darwin-arm64-cgo), const RTM_RTTUNIT = 1000000
pkg syscall (darwin-arm64-cgo), const RTM_RTTUNIT ideal-int
pkg syscall (darwin-arm64-cgo), const RTM_VERSION = 5
pkg syscall (darwin-arm64-cgo), const RTM_VERSION ideal-int
pkg syscall (darwin-arm64-cgo), const RTV_EXPIRE = 4
pkg syscall (darwin-arm64-cgo), const RTV_EXPIRE ideal-int
pkg syscall (darwin-arm64-cgo), const RTV_HOPCOUNT = 2
pkg syscall (darwin-arm64-cgo), const RTV_HOPCOUNT ideal-int
pkg syscall (darwin-arm64-cgo), const RTV_MTU = 1
pkg syscall (darwin-arm64-cgo), const RTV_MTU ideal-int
pkg syscall (darwin-arm64-cgo), const RTV_RPIPE = 8
pkg syscall (darwin-arm64-cgo), const RTV_RPIPE ideal-int
pkg syscall (darwin-arm64-cgo), const RTV_RTT = 64
pkg syscall (darwin-arm64-cgo), const RTV_RTT ideal-int
pkg syscall (darwin-arm64-cgo), const RTV_RTTVAR = 128
pkg syscall (darwin-arm64-cgo), const RTV_RTTVAR ideal-int
pkg syscall (darwin-arm64-cgo), const RTV_SPIPE = 16
pkg syscall (darwin-arm64-cgo), const RTV_SPIPE ideal-int
pkg syscall (darwin-arm64-cgo), const RTV_SSTHRESH = 32
pkg syscall (darwin-arm64-cgo), const RTV_SSTHRESH ideal-int
pkg syscall (darwin-arm64-cgo), const RUSAGE_CHILDREN = -1
pkg syscall (darwin-arm64-cgo), const RUSAGE_CHILDREN ideal-int
pkg syscall (darwin-arm64-cgo), const RUSAGE_SELF = 0
pkg syscall (darwin-arm64-cgo), const RUSAGE_SELF ideal-int
pkg syscall (darwin-arm64-cgo), const SCM_CREDS = 3
pkg syscall (darwin-arm64-cgo), const SCM_CREDS ideal-int
pkg syscall (darwin-arm64-cgo), const SCM_RIGHTS = 1
pkg syscall (darwin-arm64-cgo), const SCM_RIGHTS ideal-int
pkg syscall (darwin-arm64-cgo), const SCM_TIMESTAMP = 2
pkg syscall (darwin-arm64-cgo), const SCM_TIMESTAMP ideal-int
pkg syscall (darwin-arm64-cgo), const SCM_TIMESTAMP_MONOTONIC = 4
pkg syscall (darwin-arm64-cgo), const SCM_TIMESTAMP_MONOTONIC ideal-int
pkg syscall (darwin-arm64-cgo), const SIGBUS = 10
pkg syscall (darwin-arm64-cgo), const SIGCHLD = 20
pkg syscall (darwin-arm64-cgo), const SIGCHLD Signal
pkg syscall (darwin-arm64-cgo), const SIGCONT = 19
pkg syscall (darwin-arm64-cgo), const SIGCONT Signal
pkg syscall (darwin-arm64-cgo), const SIGEMT = 7
pkg syscall (darwin-arm64-cgo), const SIGEMT Signal
pkg syscall (darwin-arm64-cgo), const SIGINFO = 29
pkg syscall (darwin-arm64-cgo), const SIGINFO Signal
pkg syscall (darwin-arm64-cgo), const SIGIO = 23
pkg syscall (darwin-arm64-cgo), const SIGIO Signal
pkg syscall (darwin-arm64-cgo), const SIGIOT = 6
pkg syscall (darwin-arm64-cgo), const SIGIOT Signal
pkg syscall (darwin-arm64-cgo), const SIGPROF = 27
pkg syscall (darwin-arm64-cgo), const SIGPROF Signal
pkg syscall (darwin-arm64-cgo), const SIGSTOP = 17
pkg syscall (darwin-arm64-cgo), const SIGSTOP Signal
pkg syscall (darwin-arm64-cgo), const SIGSYS = 12
pkg syscall (darwin-arm64-cgo), const SIGSYS Signal
pkg syscall (darwin-arm64-cgo), const SIGTSTP = 18
pkg syscall (darwin-arm64-cgo), const SIGTSTP Signal
pkg syscall (darwin-arm64-cgo), const SIGTTIN = 21
pkg syscall (darwin-arm64-cgo), const SIGTTIN Signal
pkg syscall (darwin-arm64-cgo), const SIGTTOU = 22
pkg syscall (darwin-arm64-cgo), const SIGTTOU Signal
pkg syscall (darwin-arm64-cgo), const SIGURG = 16
pkg syscall (darwin-arm64-cgo), const SIGURG Signal
pkg syscall (darwin-arm64-cgo), const SIGUSR1 = 30
pkg syscall (darwin-arm64-cgo), const SIGUSR1 Signal
pkg syscall (darwin-arm64-cgo), const SIGUSR2 = 31
pkg syscall (darwin-arm64-cgo), const SIGUSR2 Signal
pkg syscall (darwin-arm64-cgo), const SIGVTALRM = 26
pkg syscall (darwin-arm64-cgo), const SIGVTALRM Signal
pkg syscall (darwin-arm64-cgo), const SIGWINCH = 28
pkg syscall (darwin-arm64-cgo), const SIGWINCH Signal
pkg syscall (darwin-arm64-cgo), const SIGXCPU = 24
pkg syscall (darwin-arm64-cgo), const SIGXCPU Signal
pkg syscall (darwin-arm64-cgo), const SIGXFSZ = 25
pkg syscall (darwin-arm64-cgo), const SIGXFSZ Signal
pkg syscall (darwin-arm64-cgo), const SIOCADDMULTI = 2149607729
pkg syscall (darwin-arm64-cgo), const SIOCADDMULTI ideal-int
pkg syscall (darwin-arm64-cgo), const SIOCAIFADDR = 2151704858
pkg syscall (darwin-arm64-cgo), const SIOCAIFADDR ideal-int
pkg syscall (darwin-arm64-cgo), const SIOCARPIPLL = 3223349544
pkg syscall (darwin-arm64-cgo), const SIOCARPIPLL ideal-int
pkg syscall (darwin-arm64-cgo), const SIOCATMARK = 1074033415
pkg syscall (darwin-arm64-cgo), const SIOCATMARK ideal-int
pkg syscall (darwin-arm64-cgo), const SIOCAUTOADDR = 3223349542
pkg syscall (darwin-arm64-cgo), const SIOCAUTOADDR ideal-int
pkg syscall (darwin-arm64-cgo), const SIOCAUTONETMASK = 2149607719
pkg syscall (darwin-arm64-cgo), const SIOCAUTONETMASK ideal-int
pkg syscall (darwin-arm64-cgo), const SIOCDELMULTI = 2149607730
pkg syscall (darwin-arm64-cgo), const SIOCDELMULTI ideal-int
pkg syscall (darwin-arm64-cgo), const SIOCDIFADDR = 2149607705
pkg syscall (darwin-arm64-cgo), const SIOCDIFADDR ideal-int
pkg syscall (darwin-arm64-cgo), const SIOCDIFPHYADDR = 2149607745
pkg syscall (darwin-arm64-cgo), const SIOCDIFPHYADDR ideal-int
pkg syscall (darwin-arm64-cgo), const SIOCGDRVSPEC = 3223873915
pkg syscall (darwin-arm64-cgo), const SIOCGDRVSPEC ideal-int
pkg syscall (darwin-arm64-cgo), const SIOCGETVLAN = 3223349631
pkg syscall (darwin-arm64-cgo), const SIOCGETVLAN ideal-int
pkg syscall (darwin-arm64-cgo), const SIOCGHIWAT = 1074033409
pkg syscall (darwin-arm64-cgo), const SIOCGHIWAT ideal-int
pkg syscall (darwin-arm64-cgo), const SIOCGIFADDR = 3223349537
pkg syscall (darwin-arm64-cgo), const SIOCGIFADDR ideal-int
pkg syscall (darwin-arm64-cgo), const SIOCGIFALTMTU = 3223349576
pkg syscall (darwin-arm64-cgo), const SIOCGIFALTMTU ideal-int
pkg syscall (darwin-arm64-cgo), const SIOCGIFASYNCMAP = 3223349628
pkg syscall (darwin-arm64-cgo), const SIOCGIFASYNCMAP ideal-int
pkg syscall (darwin-arm64-cgo), const SIOCGIFBOND = 3223349575
pkg syscall (darwin-arm64-cgo), const SIOCGIFBOND ideal-int
pkg syscall (darwin-arm64-cgo), const SIOCGIFBRDADDR = 3223349539
pkg syscall (darwin-arm64-cgo), const SIOCGIFBRDADDR ideal-int
pkg syscall (darwin-arm64-cgo), const SIOCGIFCAP = 3223349595
pkg syscall (darwin-arm64-cgo), const SIOCGIFCAP ideal-int
pkg syscall (darwin-arm64-cgo), const SIOCGIFCONF = 3222038820
pkg syscall (darwin-arm64-cgo), const SIOCGIFCONF ideal-int
pkg syscall (darwin-arm64-cgo), const SIOCGIFDEVMTU = 3223349572
pkg syscall (darwin-arm64-cgo), const SIOCGIFDEVMTU ideal-int
pkg syscall (darwin-arm64-cgo), const SIOCGIFDSTADDR = 3223349538
pkg syscall (darwin-arm64-cgo), const SIOCGIFDSTADDR ideal-int
pkg syscall (darwin-arm64-cgo), const SIOCGIFFLAGS = 3223349521
pkg syscall (darwin-arm64-cgo), const SIOCGIFFLAGS ideal-int
pkg syscall (darwin-arm64-cgo), const SIOCGIFGENERIC = 3223349562
pkg syscall (darwin-arm64-cgo), const SIOCGIFGENERIC ideal-int
pkg syscall (darwin-arm64-cgo), const SIOCGIFKPI = 3223349639
pkg syscall (darwin-arm64-cgo), const SIOCGIFKPI ideal-int
pkg syscall (darwin-arm64-cgo), const SIOCGIFMAC = 3223349634
pkg syscall (darwin-arm64-cgo), const SIOCGIFMAC ideal-int
pkg syscall (darwin-arm64-cgo), const SIOCGIFMEDIA = 3224135992
pkg syscall (darwin-arm64-cgo), const SIOCGIFMEDIA ideal-int
pkg syscall (darwin-arm64-cgo), const SIOCGIFMETRIC = 3223349527
pkg syscall (darwin-arm64-cgo), const SIOCGIFMETRIC ideal-int
pkg syscall (darwin-arm64-cgo), const SIOCGIFMTU = 3223349555
pkg syscall (darwin-arm64-cgo), const SIOCGIFMTU ideal-int
pkg syscall (darwin-arm64-cgo), const SIOCGIFNETMASK = 3223349541
pkg syscall (darwin-arm64-cgo), const SIOCGIFNETMASK ideal-int
pkg syscall (darwin-arm64-cgo), const SIOCGIFPDSTADDR = 3223349568
pkg syscall (darwin-arm64-cgo), const SIOCGIFPDSTADDR ideal-int
pkg syscall (darwin-arm64-cgo), const SIOCGIFPHYS = 3223349557
pkg syscall (darwin-arm64-cgo), const SIOCGIFPHYS ideal-int
pkg syscall (darwin-arm64-cgo), const SIOCGIFPSRCADDR = 3223349567
pkg syscall (darwin-arm64-cgo), const SIOCGIFPSRCADDR ideal-int
pkg syscall (darwin-arm64-cgo), const SIOCGIFSTATUS = 3274795325
pkg syscall (darwin-arm64-cgo), const SIOCGIFSTATUS ideal-int
pkg syscall (darwin-arm64-cgo), const SIOCGIFVLAN = 3223349631
pkg syscall (darwin-arm64-cgo), const SIOCGIFVLAN ideal-int
pkg syscall (darwin-arm64-cgo), const SIOCGIFWAKEFLAGS = 3223349640
pkg syscall (darwin-arm64-cgo), const SIOCGIFWAKEFLAGS ideal-int
pkg syscall (darwin-arm64-cgo), const SIOCGLOWAT = 1074033411
pkg syscall (darwin-arm64-cgo), const SIOCGLOWAT ideal-int
pkg syscall (darwin-arm64-cgo), const SIOCGPGRP = 1074033417
pkg syscall (darwin-arm64-cgo), const SIOCGPGRP ideal-int
pkg syscall (darwin-arm64-cgo), const SIOCIFCREATE = 3223349624
pkg syscall (darwin-arm64-cgo), const SIOCIFCREATE ideal-int
pkg syscall (darwin-arm64-cgo), const SIOCIFCREATE2 = 3223349626
pkg syscall (darwin-arm64-cgo), const SIOCIFCREATE2 ideal-int
pkg syscall (darwin-arm64-cgo), const SIOCIFDESTROY = 2149607801
pkg syscall (darwin-arm64-cgo), const SIOCIFDESTROY ideal-int
pkg syscall (darwin-arm64-cgo), const SIOCIFGCLONERS = 3222301057
pkg syscall (darwin-arm64-cgo), const SIOCIFGCLONERS ideal-int
pkg syscall (darwin-arm64-cgo), const SIOCRSLVMULTI = 3222300987
pkg syscall (darwin-arm64-cgo), const SIOCRSLVMULTI ideal-int
pkg syscall (darwin-arm64-cgo), const SIOCSDRVSPEC = 2150132091
pkg syscall (darwin-arm64-cgo), const SIOCSDRVSPEC ideal-int
pkg syscall (darwin-arm64-cgo), const SIOCSETVLAN = 2149607806
pkg syscall (darwin-arm64-cgo), const SIOCSETVLAN ideal-int
pkg syscall (darwin-arm64-cgo), const SIOCSHIWAT = 2147775232
pkg syscall (darwin-arm64-cgo), const SIOCSHIWAT ideal-int
pkg syscall (darwin-arm64-cgo), const SIOCSIFADDR = 2149607692
pkg syscall (darwin-arm64-cgo), const SIOCSIFADDR ideal-int
pkg syscall (darwin-arm64-cgo), const SIOCSIFALTMTU = 2149607749
pkg syscall (darwin-arm64-cgo), const SIOCSIFALTMTU ideal-int
pkg syscall (darwin-arm64-cgo), const SIOCSIFASYNCMAP = 2149607805
pkg syscall (darwin-arm64-cgo), const SIOCSIFASYNCMAP ideal-int
pkg syscall (darwin-arm64-cgo), const SIOCSIFBOND = 2149607750
pkg syscall (darwin-arm64-cgo), const SIOCSIFBOND ideal-int
pkg syscall (darwin-arm64-cgo), const SIOCSIFBRDADDR = 2149607699
pkg syscall (darwin-arm64-cgo), const SIOCSIFBRDADDR ideal-int
pkg syscall (darwin-arm64-cgo), const SIOCSIFCAP = 2149607770
pkg syscall (darwin-arm64-cgo), const SIOCSIFCAP ideal-int
pkg syscall (darwin-arm64-cgo), const SIOCSIFDSTADDR = 2149607694
pkg syscall (darwin-arm64-cgo), const SIOCSIFDSTADDR ideal-int
pkg syscall (darwin-arm64-cgo), const SIOCSIFFLAGS = 2149607696
pkg syscall (darwin-arm64-cgo), const SIOCSIFFLAGS ideal-int
pkg syscall (darwin-arm64-cgo), const SIOCSIFGENERIC = 2149607737
pkg syscall (darwin-arm64-cgo), const SIOCSIFGENERIC ideal-int
pkg syscall (darwin-arm64-cgo), const SIOCSIFKPI = 2149607814
pkg syscall (darwin-arm64-cgo), const SIOCSIFKPI ideal-int
pkg syscall (darwin-arm64-cgo), const SIOCSIFLLADDR = 2149607740
pkg syscall (darwin-arm64-cgo), const SIOCSIFLLADDR ideal-int
pkg syscall (darwin-arm64-cgo), const SIOCSIFMAC = 2149607811
pkg syscall (darwin-arm64-cgo), const SIOCSIFMAC ideal-int
pkg syscall (darwin-arm64-cgo), const SIOCSIFMEDIA = 3223349559
pkg syscall (darwin-arm64-cgo), const SIOCSIFMEDIA ideal-int
pkg syscall (darwin-arm64-cgo), const SIOCSIFMETRIC = 2149607704
pkg syscall (darwin-arm64-cgo), const SIOCSIFMETRIC ideal-int
pkg syscall (darwin-arm64-cgo), const SIOCSIFMTU = 2149607732
pkg syscall (darwin-arm64-cgo), const SIOCSIFMTU ideal-int
pkg syscall (darwin-arm64-cgo), const SIOCSIFNETMASK = 2149607702
pkg syscall (darwin-arm64-cgo), const SIOCSIFNETMASK ideal-int
pkg syscall (darwin-arm64-cgo), const SIOCSIFPHYADDR = 2151704894
pkg syscall (darwin-arm64-cgo), const SIOCSIFPHYADDR ideal-int
pkg syscall (darwin-arm64-cgo), const SIOCSIFPHYS = 2149607734
pkg syscall (darwin-arm64-cgo), const SIOCSIFPHYS ideal-int
pkg syscall (darwin-arm64-cgo), const SIOCSIFVLAN = 2149607806
pkg syscall (darwin-arm64-cgo), const SIOCSIFVLAN ideal-int
pkg syscall (darwin-arm64-cgo), const SIOCSLOWAT = 2147775234
pkg syscall (darwin-arm64-cgo), const SIOCSLOWAT ideal-int
pkg syscall (darwin-arm64-cgo), const SIOCSPGRP = 2147775240
pkg syscall (darwin-arm64-cgo), const SIOCSPGRP ideal-int
pkg syscall (darwin-arm64-cgo), const SOCK_MAXADDRLEN = 255
pkg syscall (darwin-arm64-cgo), const SOCK_MAXADDRLEN ideal-int
pkg syscall (darwin-arm64-cgo), const SOCK_RDM = 4
pkg syscall (darwin-arm64-cgo), const SOCK_RDM ideal-int
pkg syscall (darwin-arm64-cgo), const SOL_SOCKET = 65535
pkg syscall (darwin-arm64-cgo), const SOMAXCONN = 128
pkg syscall (darwin-arm64-cgo), const SO_ACCEPTCONN = 2
pkg syscall (darwin-arm64-cgo), const SO_ACCEPTCONN ideal-int
pkg syscall (darwin-arm64-cgo), const SO_BROADCAST = 32
pkg syscall (darwin-arm64-cgo), const SO_DEBUG = 1
pkg syscall (darwin-arm64-cgo), const SO_DEBUG ideal-int
pkg syscall (darwin-arm64-cgo), const SO_DONTROUTE = 16
pkg syscall (darwin-arm64-cgo), const SO_DONTTRUNC = 8192
pkg syscall (darwin-arm64-cgo), const SO_DONTTRUNC ideal-int
pkg syscall (darwin-arm64-cgo), const SO_ERROR = 4103
pkg syscall (darwin-arm64-cgo), const SO_ERROR ideal-int
pkg syscall (darwin-arm64-cgo), const SO_KEEPALIVE = 8
pkg syscall (darwin-arm64-cgo), const SO_LABEL = 4112
pkg syscall (darwin-arm64-cgo), const SO_LABEL ideal-int
pkg syscall (darwin-arm64-cgo), const SO_LINGER = 128
pkg syscall (darwin-arm64-cgo), const SO_LINGER_SEC = 4224
pkg syscall (darwin-arm64-cgo), const SO_LINGER_SEC ideal-int
pkg syscall (darwin-arm64-cgo), const SO_NKE = 4129
pkg syscall (darwin-arm64-cgo), const SO_NKE ideal-int
pkg syscall (darwin-arm64-cgo), const SO_NOADDRERR = 4131
pkg syscall (darwin-arm64-cgo), const SO_NOADDRERR ideal-int
pkg syscall (darwin-arm64-cgo), const SO_NOSIGPIPE = 4130
pkg syscall (darwin-arm64-cgo), const SO_NOSIGPIPE ideal-int
pkg syscall (darwin-arm64-cgo), const SO_NOTIFYCONFLICT = 4134
pkg syscall (darwin-arm64-cgo), const SO_NOTIFYCONFLICT ideal-int
pkg syscall (darwin-arm64-cgo), const SO_NP_EXTENSIONS = 4227
pkg syscall (darwin-arm64-cgo), const SO_NP_EXTENSIONS ideal-int
pkg syscall (darwin-arm64-cgo), const SO_NREAD = 4128
pkg syscall (darwin-arm64-cgo), const SO_NREAD ideal-int
pkg syscall (darwin-arm64-cgo), const SO_NUMRCVPKT = 4370
pkg syscall (darwin-arm64-cgo), const SO_NUMRCVPKT ideal-int
pkg syscall (darwin-arm64-cgo), const SO_NWRITE = 4132
pkg syscall (darwin-arm64-cgo), const SO_NWRITE ideal-int
pkg syscall (darwin-arm64-cgo), const SO_OOBINLINE = 256
pkg syscall (darwin-arm64-cgo), const SO_OOBINLINE ideal-int
pkg syscall (darwin-arm64-cgo), const SO_PEERLABEL = 4113
pkg syscall (darwin-arm64-cgo), const SO_PEERLABEL ideal-int
pkg syscall (darwin-arm64-cgo), const SO_RANDOMPORT = 4226
pkg syscall (darwin-arm64-cgo), const SO_RANDOMPORT ideal-int
pkg syscall (darwin-arm64-cgo), const SO_RCVBUF = 4098
pkg syscall (darwin-arm64-cgo), const SO_RCVLOWAT = 4100
pkg syscall (darwin-arm64-cgo), const SO_RCVLOWAT ideal-int
pkg syscall (darwin-arm64-cgo), const SO_RCVTIMEO = 4102
pkg syscall (darwin-arm64-cgo), const SO_RCVTIMEO ideal-int
pkg syscall (darwin-arm64-cgo), const SO_REUSEADDR = 4
pkg syscall (darwin-arm64-cgo), const SO_REUSEPORT = 512
pkg syscall (darwin-arm64-cgo), const SO_REUSEPORT ideal-int
pkg syscall (darwin-arm64-cgo), const SO_REUSESHAREUID = 4133
pkg syscall (darwin-arm64-cgo), const SO_REUSESHAREUID ideal-int
pkg syscall (darwin-arm64-cgo), const SO_SNDBUF = 4097
pkg syscall (darwin-arm64-cgo), const SO_SNDLOWAT = 4099
pkg syscall (darwin-arm64-cgo), const SO_SNDLOWAT ideal-int
pkg syscall (darwin-arm64-cgo), const SO_SNDTIMEO = 4101
pkg syscall (darwin-arm64-cgo), const SO_SNDTIMEO ideal-int
pkg syscall (darwin-arm64-cgo), const SO_TIMESTAMP = 1024
pkg syscall (darwin-arm64-cgo), const SO_TIMESTAMP ideal-int
pkg syscall (darwin-arm64-cgo), const SO_TIMESTAMP_MONOTONIC = 2048
pkg syscall (darwin-arm64-cgo), const SO_TIMESTAMP_MONOTONIC ideal-int
pkg syscall (darwin-arm64-cgo), const SO_TYPE = 4104
pkg syscall (darwin-arm64-cgo), const SO_TYPE ideal-int
pkg syscall (darwin-arm64-cgo), const SO_UPCALLCLOSEWAIT = 4135
pkg syscall (darwin-arm64-cgo), const SO_UPCALLCLOSEWAIT ideal-int
pkg syscall (darwin-arm64-cgo), const SO_USELOOPBACK = 64
pkg syscall (darwin-arm64-cgo), const SO_USELOOPBACK ideal-int
pkg syscall (darwin-arm64-cgo), const SO_WANTMORE = 16384
pkg syscall (darwin-arm64-cgo), const SO_WANTMORE ideal-int
pkg syscall (darwin-arm64-cgo), const SO_WANTOOBFLAG = 32768
pkg syscall (darwin-arm64-cgo), const SO_WANTOOBFLAG ideal-int
pkg syscall (darwin-arm64-cgo), const SYS_ACCEPT = 30
pkg syscall (darwin-arm64-cgo), const SYS_ACCEPT ideal-int
pkg syscall (darwin-arm64-cgo), const SYS_ACCEPT_NOCANCEL = 404
pkg syscall (darwin-arm64-cgo), const SYS_ACCEPT_NOCANCEL ideal-int
pkg syscall (darwin-arm64-cgo), const SYS_ACCESS = 33
pkg syscall (darwin-arm64-cgo), const SYS_ACCESS ideal-int
pkg syscall (darwin-arm64-cgo), const SYS_ACCESS_EXTENDED = 284
pkg syscall (darwin-arm64-cgo), const SYS_ACCESS_EXTENDED ideal-int
pkg syscall (darwin-arm64-cgo), const SYS_ACCT = 51
pkg syscall (darwin-arm64-cgo), const SYS_ACCT ideal-int
pkg syscall (darwin-arm64-cgo), const SYS_ADJTIME = 140
pkg syscall (darwin-arm64-cgo), const SYS_ADJTIME ideal-int
pkg syscall (darwin-arm64-cgo), const SYS_AIO_CANCEL = 316
pkg syscall (darwin-arm64-cgo), const SYS_AIO_CANCEL ideal-int
pkg syscall (darwin-arm64-cgo), const SYS_AIO_ERROR = 317
pkg syscall (darwin-arm64-cgo), const SYS_AIO_ERROR ideal-int
pkg syscall (darwin-arm64-cgo), const SYS_AIO_FSYNC = 313
pkg syscall (darwin-arm64-cgo), const SYS_AIO_FSYNC ideal-int
pkg syscall (darwin-arm64-cgo), const SYS_AIO_READ = 318
pkg syscall (darwin-arm64-cgo), const SYS_AIO_READ ideal-int
pkg syscall (darwin-arm64-cgo), const SYS_AIO_RETURN = 314
pkg syscall (darwin-arm64-cgo), const SYS_AIO_RETURN ideal-int
pkg syscall (darwin-arm64-cgo), const SYS_AIO_SUSPEND = 315
pkg syscall (darwin-arm64-cgo), const SYS_AIO_SUSPEND ideal-int
pkg syscall (darwin-arm64-cgo), const SYS_AIO_SUSPEND_NOCANCEL = 421
pkg syscall (darwin-arm64-cgo), const SYS_AIO_SUSPEND_NOCANCEL ideal-int
pkg syscall (darwin-arm64-cgo), const SYS_AIO_WRITE = 319
pkg syscall (darwin-arm64-cgo), const SYS_AIO_WRITE ideal-int
pkg syscall (darwin-arm64-cgo), const SYS_ATGETMSG = 207
pkg syscall (darwin-arm64-cgo), const SYS_ATGETMSG ideal-int
pkg syscall (darwin-arm64-cgo), const SYS_ATPGETREQ = 211
pkg syscall (darwin-arm64-cgo), const SYS_ATPGETREQ ideal-int
pkg syscall (darwin-arm64-cgo), const SYS_ATPGETRSP = 212
pkg syscall (darwin-arm64-cgo), const SYS_ATPGETRSP ideal-int
pkg syscall (darwin-arm64-cgo), const SYS_ATPSNDREQ = 209
pkg syscall (darwin-arm64-cgo), const SYS_ATPSNDREQ ideal-int
pkg syscall (darwin-arm64-cgo), const SYS_ATPSNDRSP = 210
pkg syscall (darwin-arm64-cgo), const SYS_ATPSNDRSP ideal-int
pkg syscall (darwin-arm64-cgo), const SYS_ATPUTMSG = 208
pkg syscall (darwin-arm64-cgo), const SYS_ATPUTMSG ideal-int
pkg syscall (darwin-arm64-cgo), const SYS_ATSOCKET = 206
pkg syscall (darwin-arm64-cgo), const SYS_ATSOCKET ideal-int
pkg syscall (darwin-arm64-cgo), const SYS_AUDIT = 350
pkg syscall (darwin-arm64-cgo), const SYS_AUDIT ideal-int
pkg syscall (darwin-arm64-cgo), const SYS_AUDITCTL = 359
pkg syscall (darwin-arm64-cgo), const SYS_AUDITCTL ideal-int
pkg syscall (darwin-arm64-cgo), const SYS_AUDITON = 351
pkg syscall (darwin-arm64-cgo), const SYS_AUDITON ideal-int
pkg syscall (darwin-arm64-cgo), const SYS_AUDIT_SESSION_JOIN = 429
pkg syscall (darwin-arm64-cgo), const SYS_AUDIT_SESSION_JOIN ideal-int
pkg syscall (darwin-arm64-cgo), const SYS_AUDIT_SESSION_PORT = 432
pkg syscall (darwin-arm64-cgo), const SYS_AUDIT_SESSION_PORT ideal-int
pkg syscall (darwin-arm64-cgo), const SYS_AUDIT_SESSION_SELF = 428
pkg syscall (darwin-arm64-cgo), const SYS_AUDIT_SESSION_SELF ideal-int
pkg syscall (darwin-arm64-cgo), const SYS_BIND = 104
pkg syscall (darwin-arm64-cgo), const SYS_BIND ideal-int
pkg syscall (darwin-arm64-cgo), const SYS_BSDTHREAD_CREATE = 360
pkg syscall (darwin-arm64-cgo), const SYS_BSDTHREAD_CREATE ideal-int
pkg syscall (darwin-arm64-cgo), const SYS_BSDTHREAD_REGISTER = 366
pkg syscall (darwin-arm64-cgo), const SYS_BSDTHREAD_REGISTER ideal-int
pkg syscall (darwin-arm64-cgo), const SYS_BSDTHREAD_TERMINATE = 361
pkg syscall (darwin-arm64-cgo), const SYS_BSDTHREAD_TERMINATE ideal-int
pkg syscall (darwin-arm64-cgo), const SYS_CHDIR = 12
pkg syscall (darwin-arm64-cgo), const SYS_CHDIR ideal-int
pkg syscall (darwin-arm64-cgo), const SYS_CHFLAGS = 34
pkg syscall (darwin-arm64-cgo), const SYS_CHFLAGS ideal-int
pkg syscall (darwin-arm64-cgo), const SYS_CHMOD = 15
pkg syscall (darwin-arm64-cgo), const SYS_CHMOD ideal-int
pkg syscall (darwin-arm64-cgo), const SYS_CHMOD_EXTENDED = 282
pkg syscall (darwin-arm64-cgo), const SYS_CHMOD_EXTENDED ideal-int
pkg syscall (darwin-arm64-cgo), const SYS_CHOWN = 16
pkg syscall (darwin-arm64-cgo), const SYS_CHOWN ideal-int
pkg syscall (darwin-arm64-cgo), const SYS_CHROOT = 61
pkg syscall (darwin-arm64-cgo), const SYS_CHROOT ideal-int
pkg syscall (darwin-arm64-cgo), const SYS_CHUD = 185
pkg syscall (darwin-arm64-cgo), const SYS_CHUD ideal-int
pkg syscall (darwin-arm64-cgo), const SYS_CLOSE = 6
pkg syscall (darwin-arm64-cgo), const SYS_CLOSE ideal-int
pkg syscall (darwin-arm64-cgo), const SYS_CLOSE_NOCANCEL = 399
pkg syscall (darwin-arm64-cgo), const SYS_CLOSE_NOCANCEL ideal-int
pkg syscall (darwin-arm64-cgo), const SYS_CONNECT = 98
pkg syscall (darwin-arm64-cgo), const SYS_CONNECT ideal-int
pkg syscall (darwin-arm64-cgo), const SYS_CONNECT_NOCANCEL = 409
pkg syscall (darwin-arm64-cgo), const SYS_CONNECT_NOCANCEL ideal-int
pkg syscall (darwin-arm64-cgo), const SYS_COPYFILE = 227
pkg syscall (darwin-arm64-cgo), const SYS_COPYFILE ideal-int
pkg syscall (darwin-arm64-cgo), const SYS_CSOPS = 169
pkg syscall (darwin-arm64-cgo), const SYS_CSOPS ideal-int
pkg syscall (darwin-arm64-cgo), const SYS_CSOPS_AUDITTOKEN = 170
pkg syscall (darwin-arm64-cgo), const SYS_CSOPS_AUDITTOKEN ideal-int
pkg syscall (darwin-arm64-cgo), const SYS_DELETE = 226
pkg syscall (darwin-arm64-cgo), const SYS_DELETE ideal-int
pkg syscall (darwin-arm64-cgo), const SYS_DUP = 41
pkg syscall (darwin-arm64-cgo), const SYS_DUP ideal-int
pkg syscall (darwin-arm64-cgo), const SYS_DUP2 = 90
pkg syscall (darwin-arm64-cgo), const SYS_DUP2 ideal-int
pkg syscall (darwin-arm64-cgo), const SYS_EXCHANGEDATA = 223
pkg syscall (darwin-arm64-cgo), const SYS_EXCHANGEDATA ideal-int
pkg syscall (darwin-arm64-cgo), const SYS_EXECVE = 59
pkg syscall (darwin-arm64-cgo), const SYS_EXECVE ideal-int
pkg syscall (darwin-arm64-cgo), const SYS_EXIT = 1
pkg syscall (darwin-arm64-cgo), const SYS_EXIT ideal-int
pkg syscall (darwin-arm64-cgo), const SYS_FCHDIR = 13
pkg syscall (darwin-arm64-cgo), const SYS_FCHDIR ideal-int
pkg syscall (darwin-arm64-cgo), const SYS_FCHFLAGS = 35
pkg syscall (darwin-arm64-cgo), const SYS_FCHFLAGS ideal-int
pkg syscall (darwin-arm64-cgo), const SYS_FCHMOD = 124
pkg syscall (darwin-arm64-cgo), const SYS_FCHMOD ideal-int
pkg syscall (darwin-arm64-cgo), const SYS_FCHMOD_EXTENDED = 283
pkg syscall (darwin-arm64-cgo), const SYS_FCHMOD_EXTENDED ideal-int
pkg syscall (darwin-arm64-cgo), const SYS_FCHOWN = 123
pkg syscall (darwin-arm64-cgo), const SYS_FCHOWN ideal-int
pkg syscall (darwin-arm64-cgo), const SYS_FCNTL = 92
pkg syscall (darwin-arm64-cgo), const SYS_FCNTL ideal-int
pkg syscall (darwin-arm64-cgo), const SYS_FCNTL_NOCANCEL = 406
pkg syscall (darwin-arm64-cgo), const SYS_FCNTL_NOCANCEL ideal-int
pkg syscall (darwin-arm64-cgo), const SYS_FDATASYNC = 187
pkg syscall (darwin-arm64-cgo), const SYS_FDATASYNC ideal-int
pkg syscall (darwin-arm64-cgo), const SYS_FFSCTL = 245
pkg syscall (darwin-arm64-cgo), const SYS_FFSCTL ideal-int
pkg syscall (darwin-arm64-cgo), const SYS_FGETATTRLIST = 228
pkg syscall (darwin-arm64-cgo), const SYS_FGETATTRLIST ideal-int
pkg syscall (darwin-arm64-cgo), const SYS_FGETXATTR = 235
pkg syscall (darwin-arm64-cgo), const SYS_FGETXATTR ideal-int
pkg syscall (darwin-arm64-cgo), const SYS_FHOPEN = 248
pkg syscall (darwin-arm64-cgo), const SYS_FHOPEN ideal-int
pkg syscall (darwin-arm64-cgo), const SYS_FILEPORT_MAKEFD = 431
pkg syscall (darwin-arm64-cgo), const SYS_FILEPORT_MAKEFD ideal-int
pkg syscall (darwin-arm64-cgo), const SYS_FILEPORT_MAKEPORT = 430
pkg syscall (darwin-arm64-cgo), const SYS_FILEPORT_MAKEPORT ideal-int
pkg syscall (darwin-arm64-cgo), const SYS_FLISTXATTR = 241
pkg syscall (darwin-arm64-cgo), const SYS_FLISTXATTR ideal-int
pkg syscall (darwin-arm64-cgo), const SYS_FLOCK = 131
pkg syscall (darwin-arm64-cgo), const SYS_FLOCK ideal-int
pkg syscall (darwin-arm64-cgo), const SYS_FORK = 2
pkg syscall (darwin-arm64-cgo), const SYS_FORK ideal-int
pkg syscall (darwin-arm64-cgo), const SYS_FPATHCONF = 192
pkg syscall (darwin-arm64-cgo), const SYS_FPATHCONF ideal-int
pkg syscall (darwin-arm64-cgo), const SYS_FREMOVEXATTR = 239
pkg syscall (darwin-arm64-cgo), const SYS_FREMOVEXATTR ideal-int
pkg syscall (darwin-arm64-cgo), const SYS_FSCTL = 242
pkg syscall (darwin-arm64-cgo), const SYS_FSCTL ideal-int
pkg syscall (darwin-arm64-cgo), const SYS_FSETATTRLIST = 229
pkg syscall (darwin-arm64-cgo), const SYS_FSETATTRLIST ideal-int
pkg syscall (darwin-arm64-cgo), const SYS_FSETXATTR = 237
pkg syscall (darwin-arm64-cgo), const SYS_FSETXATTR ideal-int
pkg syscall (darwin-arm64-cgo), const SYS_FSGETPATH = 427
pkg syscall (darwin-arm64-cgo), const SYS_FSGETPATH ideal-int
pkg syscall (darwin-arm64-cgo), const SYS_FSTAT = 189
pkg syscall (darwin-arm64-cgo), const SYS_FSTAT ideal-int
pkg syscall (darwin-arm64-cgo), const SYS_FSTAT64 = 339
pkg syscall (darwin-arm64-cgo), const SYS_FSTAT64 ideal-int
pkg syscall (darwin-arm64-cgo), const SYS_FSTAT64_EXTENDED = 343
pkg syscall (darwin-arm64-cgo), const SYS_FSTAT64_EXTENDED ideal-int
pkg syscall (darwin-arm64-cgo), const SYS_FSTATFS = 158
pkg syscall (darwin-arm64-cgo), const SYS_FSTATFS ideal-int
pkg syscall (darwin-arm64-cgo), const SYS_FSTATFS64 = 346
pkg syscall (darwin-arm64-cgo), const SYS_FSTATFS64 ideal-int
pkg syscall (darwin-arm64-cgo), const SYS_FSTAT_EXTENDED = 281
pkg syscall (darwin-arm64-cgo), const SYS_FSTAT_EXTENDED ideal-int
pkg syscall (darwin-arm64-cgo), const SYS_FSYNC = 95
pkg syscall (darwin-arm64-cgo), const SYS_FSYNC ideal-int
pkg syscall (darwin-arm64-cgo), const SYS_FSYNC_NOCANCEL = 408
pkg syscall (darwin-arm64-cgo), const SYS_FSYNC_NOCANCEL ideal-int
pkg syscall (darwin-arm64-cgo), const SYS_FTRUNCATE = 201
pkg syscall (darwin-arm64-cgo), const SYS_FTRUNCATE ideal-int
pkg syscall (darwin-arm64-cgo), const SYS_FUTIMES = 139
pkg syscall (darwin-arm64-cgo), const SYS_FUTIMES ideal-int
pkg syscall (darwin-arm64-cgo), const SYS_GETATTRLIST = 220
pkg syscall (darwin-arm64-cgo), const SYS_GETATTRLIST ideal-int
pkg syscall (darwin-arm64-cgo), const SYS_GETAUDIT_ADDR = 357
pkg syscall (darwin-arm64-cgo), const SYS_GETAUDIT_ADDR ideal-int
pkg syscall (darwin-arm64-cgo), const SYS_GETAUID = 353
pkg syscall (darwin-arm64-cgo), const SYS_GETAUID ideal-int
pkg syscall (darwin-arm64-cgo), const SYS_GETDIRENTRIES = 196
pkg syscall (darwin-arm64-cgo), const SYS_GETDIRENTRIES ideal-int
pkg syscall (darwin-arm64-cgo), const SYS_GETDIRENTRIES64 = 344
pkg syscall (darwin-arm64-cgo), const SYS_GETDIRENTRIES64 ideal-int
pkg syscall (darwin-arm64-cgo), const SYS_GETDIRENTRIESATTR = 222
pkg syscall (darwin-arm64-cgo), const SYS_GETDIRENTRIESATTR ideal-int
pkg syscall (darwin-arm64-cgo), const SYS_GETDTABLESIZE = 89
pkg syscall (darwin-arm64-cgo), const SYS_GETDTABLESIZE ideal-int
pkg syscall (darwin-arm64-cgo), const SYS_GETEGID = 43
pkg syscall (darwin-arm64-cgo), const SYS_GETEGID ideal-int
pkg syscall (darwin-arm64-cgo), const SYS_GETEUID = 25
pkg syscall (darwin-arm64-cgo), const SYS_GETEUID ideal-int
pkg syscall (darwin-arm64-cgo), const SYS_GETFH = 161
pkg syscall (darwin-arm64-cgo), const SYS_GETFH ideal-int
pkg syscall (darwin-arm64-cgo), const SYS_GETFSSTAT = 18
pkg syscall (darwin-arm64-cgo), const SYS_GETFSSTAT ideal-int
pkg syscall (darwin-arm64-cgo), const SYS_GETFSSTAT64 = 347
pkg syscall (darwin-arm64-cgo), const SYS_GETFSSTAT64 ideal-int
pkg syscall (darwin-arm64-cgo), const SYS_GETGID = 47
pkg syscall (darwin-arm64-cgo), const SYS_GETGID ideal-int
pkg syscall (darwin-arm64-cgo), const SYS_GETGROUPS = 79
pkg syscall (darwin-arm64-cgo), const SYS_GETGROUPS ideal-int
pkg syscall (darwin-arm64-cgo), const SYS_GETHOSTUUID = 142
pkg syscall (darwin-arm64-cgo), const SYS_GETHOSTUUID ideal-int
pkg syscall (darwin-arm64-cgo), const SYS_GETITIMER = 86
pkg syscall (darwin-arm64-cgo), const SYS_GETITIMER ideal-int
pkg syscall (darwin-arm64-cgo), const SYS_GETLCID = 395
pkg syscall (darwin-arm64-cgo), const SYS_GETLCID ideal-int
pkg syscall (darwin-arm64-cgo), const SYS_GETLOGIN = 49
pkg syscall (darwin-arm64-cgo), const SYS_GETLOGIN ideal-int
pkg syscall (darwin-arm64-cgo), const SYS_GETPEERNAME = 31
pkg syscall (darwin-arm64-cgo), const SYS_GETPEERNAME ideal-int
pkg syscall (darwin-arm64-cgo), const SYS_GETPGID = 151
pkg syscall (darwin-arm64-cgo), const SYS_GETPGID ideal-int
pkg syscall (darwin-arm64-cgo), const SYS_GETPGRP = 81
pkg syscall (darwin-arm64-cgo), const SYS_GETPGRP ideal-int
pkg syscall (darwin-arm64-cgo), const SYS_GETPID = 20
pkg syscall (darwin-arm64-cgo), const SYS_GETPID ideal-int
pkg syscall (darwin-arm64-cgo), const SYS_GETPPID = 39
pkg syscall (darwin-arm64-cgo), const SYS_GETPPID ideal-int
pkg syscall (darwin-arm64-cgo), const SYS_GETPRIORITY = 100
pkg syscall (darwin-arm64-cgo), const SYS_GETPRIORITY ideal-int
pkg syscall (darwin-arm64-cgo), const SYS_GETRLIMIT = 194
pkg syscall (darwin-arm64-cgo), const SYS_GETRLIMIT ideal-int
pkg syscall (darwin-arm64-cgo), const SYS_GETRUSAGE = 117
pkg syscall (darwin-arm64-cgo), const SYS_GETRUSAGE ideal-int
pkg syscall (darwin-arm64-cgo), const SYS_GETSGROUPS = 288
pkg syscall (darwin-arm64-cgo), const SYS_GETSGROUPS ideal-int
pkg syscall (darwin-arm64-cgo), const SYS_GETSID = 310
pkg syscall (darwin-arm64-cgo), const SYS_GETSID ideal-int
pkg syscall (darwin-arm64-cgo), const SYS_GETSOCKNAME = 32
pkg syscall (darwin-arm64-cgo), const SYS_GETSOCKNAME ideal-int
pkg syscall (darwin-arm64-cgo), const SYS_GETSOCKOPT = 118
pkg syscall (darwin-arm64-cgo), const SYS_GETSOCKOPT ideal-int
pkg syscall (darwin-arm64-cgo), const SYS_GETTID = 286
pkg syscall (darwin-arm64-cgo), const SYS_GETTID ideal-int
pkg syscall (darwin-arm64-cgo), const SYS_GETTIMEOFDAY = 116
pkg syscall (darwin-arm64-cgo), const SYS_GETTIMEOFDAY ideal-int
pkg syscall (darwin-arm64-cgo), const SYS_GETUID = 24
pkg syscall (darwin-arm64-cgo), const SYS_GETUID ideal-int
pkg syscall (darwin-arm64-cgo), const SYS_GETWGROUPS = 290
pkg syscall (darwin-arm64-cgo), const SYS_GETWGROUPS ideal-int
pkg syscall (darwin-arm64-cgo), const SYS_GETXATTR = 234
pkg syscall (darwin-arm64-cgo), const SYS_GETXATTR ideal-int
pkg syscall (darwin-arm64-cgo), const SYS_IDENTITYSVC = 293
pkg syscall (darwin-arm64-cgo), const SYS_IDENTITYSVC ideal-int
pkg syscall (darwin-arm64-cgo), const SYS_INITGROUPS = 243
pkg syscall (darwin-arm64-cgo), const SYS_INITGROUPS ideal-int
pkg syscall (darwin-arm64-cgo), const SYS_IOCTL = 54
pkg syscall (darwin-arm64-cgo), const SYS_IOCTL ideal-int
pkg syscall (darwin-arm64-cgo), const SYS_IOPOLICYSYS = 322
pkg syscall (darwin-arm64-cgo), const SYS_IOPOLICYSYS ideal-int
pkg syscall (darwin-arm64-cgo), const SYS_ISSETUGID = 327
pkg syscall (darwin-arm64-cgo), const SYS_ISSETUGID ideal-int
pkg syscall (darwin-arm64-cgo), const SYS_KAS_INFO = 439
pkg syscall (darwin-arm64-cgo), const SYS_KAS_INFO ideal-int
pkg syscall (darwin-arm64-cgo), const SYS_KDEBUG_TRACE = 180
pkg syscall (darwin-arm64-cgo), const SYS_KDEBUG_TRACE ideal-int
pkg syscall (darwin-arm64-cgo), const SYS_KEVENT = 363
pkg syscall (darwin-arm64-cgo), const SYS_KEVENT ideal-int
pkg syscall (darwin-arm64-cgo), const SYS_KEVENT64 = 369
pkg syscall (darwin-arm64-cgo), const SYS_KEVENT64 ideal-int
pkg syscall (darwin-arm64-cgo), const SYS_KILL = 37
pkg syscall (darwin-arm64-cgo), const SYS_KILL ideal-int
pkg syscall (darwin-arm64-cgo), const SYS_KQUEUE = 362
pkg syscall (darwin-arm64-cgo), const SYS_KQUEUE ideal-int
pkg syscall (darwin-arm64-cgo), const SYS_LCHOWN = 364
pkg syscall (darwin-arm64-cgo), const SYS_LCHOWN ideal-int
pkg syscall (darwin-arm64-cgo), const SYS_LEDGER = 373
pkg syscall (darwin-arm64-cgo), const SYS_LEDGER ideal-int
pkg syscall (darwin-arm64-cgo), const SYS_LINK = 9
pkg syscall (darwin-arm64-cgo), const SYS_LINK ideal-int
pkg syscall (darwin-arm64-cgo), const SYS_LIO_LISTIO = 320
pkg syscall (darwin-arm64-cgo), const SYS_LIO_LISTIO ideal-int
pkg syscall (darwin-arm64-cgo), const SYS_LISTEN = 106
pkg syscall (darwin-arm64-cgo), const SYS_LISTEN ideal-int
pkg syscall (darwin-arm64-cgo), const SYS_LISTXATTR = 240
pkg syscall (darwin-arm64-cgo), const SYS_LISTXATTR ideal-int
pkg syscall (darwin-arm64-cgo), const SYS_LSEEK = 199
pkg syscall (darwin-arm64-cgo), const SYS_LSEEK ideal-int
pkg syscall (darwin-arm64-cgo), const SYS_LSTAT = 190
pkg syscall (darwin-arm64-cgo), const SYS_LSTAT ideal-int
pkg syscall (darwin-arm64-cgo), const SYS_LSTAT64 = 340
pkg syscall (darwin-arm64-cgo), const SYS_LSTAT64 ideal-int
pkg syscall (darwin-arm64-cgo), const SYS_LSTAT64_EXTENDED = 342
pkg syscall (darwin-arm64-cgo), const SYS_LSTAT64_EXTENDED ideal-int
pkg syscall (darwin-arm64-cgo), const SYS_LSTAT_EXTENDED = 280
pkg syscall (darwin-arm64-cgo), const SYS_LSTAT_EXTENDED ideal-int
pkg syscall (darwin-arm64-cgo), const SYS_MADVISE = 75
pkg syscall (darwin-arm64-cgo), const SYS_MADVISE ideal-int
pkg syscall (darwin-arm64-cgo), const SYS_MAXSYSCALL = 440
pkg syscall (darwin-arm64-cgo), const SYS_MAXSYSCALL ideal-int
pkg syscall (darwin-arm64-cgo), const SYS_MINCORE = 78
pkg syscall (darwin-arm64-cgo), const SYS_MINCORE ideal-int
pkg syscall (darwin-arm64-cgo), const SYS_MINHERIT = 250
pkg syscall (darwin-arm64-cgo), const SYS_MINHERIT ideal-int
pkg syscall (darwin-arm64-cgo), const SYS_MKDIR = 136
pkg syscall (darwin-arm64-cgo), const SYS_MKDIR ideal-int
pkg syscall (darwin-arm64-cgo), const SYS_MKDIR_EXTENDED = 292
pkg syscall (darwin-arm64-cgo), const SYS_MKDIR_EXTENDED ideal-int
pkg syscall (darwin-arm64-cgo), const SYS_MKFIFO = 132
pkg syscall (darwin-arm64-cgo), const SYS_MKFIFO ideal-int
pkg syscall (darwin-arm64-cgo), const SYS_MKFIFO_EXTENDED = 291
pkg syscall (darwin-arm64-cgo), const SYS_MKFIFO_EXTENDED ideal-int
pkg syscall (darwin-arm64-cgo), const SYS_MKNOD = 14
pkg syscall (darwin-arm64-cgo), const SYS_MKNOD ideal-int
pkg syscall (darwin-arm64-cgo), const SYS_MLOCK = 203
pkg syscall (darwin-arm64-cgo), const SYS_MLOCK ideal-int
pkg syscall (darwin-arm64-cgo), const SYS_MLOCKALL = 324
pkg syscall (darwin-arm64-cgo), const SYS_MLOCKALL ideal-int
pkg syscall (darwin-arm64-cgo), const SYS_MMAP = 197
pkg syscall (darwin-arm64-cgo), const SYS_MMAP ideal-int
pkg syscall (darwin-arm64-cgo), const SYS_MODWATCH = 233
pkg syscall (darwin-arm64-cgo), const SYS_MODWATCH ideal-int
pkg syscall (darwin-arm64-cgo), const SYS_MOUNT = 167
pkg syscall (darwin-arm64-cgo), const SYS_MOUNT ideal-int
pkg syscall (darwin-arm64-cgo), const SYS_MPROTECT = 74
pkg syscall (darwin-arm64-cgo), const SYS_MPROTECT ideal-int
pkg syscall (darwin-arm64-cgo), const SYS_MSGCTL = 258
pkg syscall (darwin-arm64-cgo), const SYS_MSGCTL ideal-int
pkg syscall (darwin-arm64-cgo), const SYS_MSGGET = 259
pkg syscall (darwin-arm64-cgo), const SYS_MSGGET ideal-int
pkg syscall (darwin-arm64-cgo), const SYS_MSGRCV = 261
pkg syscall (darwin-arm64-cgo), const SYS_MSGRCV ideal-int
pkg syscall (darwin-arm64-cgo), const SYS_MSGRCV_NOCANCEL = 419
pkg syscall (darwin-arm64-cgo), const SYS_MSGRCV_NOCANCEL ideal-int
pkg syscall (darwin-arm64-cgo), const SYS_MSGSND = 260
pkg syscall (darwin-arm64-cgo), const SYS_MSGSND ideal-int
pkg syscall (darwin-arm64-cgo), const SYS_MSGSND_NOCANCEL = 418
pkg syscall (darwin-arm64-cgo), const SYS_MSGSND_NOCANCEL ideal-int
pkg syscall (darwin-arm64-cgo), const SYS_MSGSYS = 252
pkg syscall (darwin-arm64-cgo), const SYS_MSGSYS ideal-int
pkg syscall (darwin-arm64-cgo), const SYS_MSYNC = 65
pkg syscall (darwin-arm64-cgo), const SYS_MSYNC ideal-int
pkg syscall (darwin-arm64-cgo), const SYS_MSYNC_NOCANCEL = 405
pkg syscall (darwin-arm64-cgo), const SYS_MSYNC_NOCANCEL ideal-int
pkg syscall (darwin-arm64-cgo), const SYS_MUNLOCK = 204
pkg syscall (darwin-arm64-cgo), const SYS_MUNLOCK ideal-int
pkg syscall (darwin-arm64-cgo), const SYS_MUNLOCKALL = 325
pkg syscall (darwin-arm64-cgo), const SYS_MUNLOCKALL ideal-int
pkg syscall (darwin-arm64-cgo), const SYS_MUNMAP = 73
pkg syscall (darwin-arm64-cgo), const SYS_MUNMAP ideal-int
pkg syscall (darwin-arm64-cgo), const SYS_NFSCLNT = 247
pkg syscall (darwin-arm64-cgo), const SYS_NFSCLNT ideal-int
pkg syscall (darwin-arm64-cgo), const SYS_NFSSVC = 155
pkg syscall (darwin-arm64-cgo), const SYS_NFSSVC ideal-int
pkg syscall (darwin-arm64-cgo), const SYS_OPEN = 5
pkg syscall (darwin-arm64-cgo), const SYS_OPEN ideal-int
pkg syscall (darwin-arm64-cgo), const SYS_OPEN_DPROTECTED_NP = 216
pkg syscall (darwin-arm64-cgo), const SYS_OPEN_DPROTECTED_NP ideal-int
pkg syscall (darwin-arm64-cgo), const SYS_OPEN_EXTENDED = 277
pkg syscall (darwin-arm64-cgo), const SYS_OPEN_EXTENDED ideal-int
pkg syscall (darwin-arm64-cgo), const SYS_OPEN_NOCANCEL = 398
pkg syscall (darwin-arm64-cgo), const SYS_OPEN_NOCANCEL ideal-int
pkg syscall (darwin-arm64-cgo), const SYS_PATHCONF = 191
pkg syscall (darwin-arm64-cgo), const SYS_PATHCONF ideal-int
pkg syscall (darwin-arm64-cgo), const SYS_PID_HIBERNATE = 435
pkg syscall (darwin-arm64-cgo), const SYS_PID_HIBERNATE ideal-int
pkg syscall (darwin-arm64-cgo), const SYS_PID_RESUME = 434
pkg syscall (darwin-arm64-cgo), const SYS_PID_RESUME ideal-int
pkg syscall (darwin-arm64-cgo), const SYS_PID_SHUTDOWN_SOCKETS = 436
pkg syscall (darwin-arm64-cgo), const SYS_PID_SHUTDOWN_SOCKETS ideal-int
pkg syscall (darwin-arm64-cgo), const SYS_PID_SUSPEND = 433
pkg syscall (darwin-arm64-cgo), const SYS_PID_SUSPEND ideal-int
pkg syscall (darwin-arm64-cgo), const SYS_PIPE = 42
pkg syscall (darwin-arm64-cgo), const SYS_PIPE ideal-int
pkg syscall (darwin-arm64-cgo), const SYS_POLL = 230
pkg syscall (darwin-arm64-cgo), const SYS_POLL ideal-int
pkg syscall (darwin-arm64-cgo), const SYS_POLL_NOCANCEL = 417
pkg syscall (darwin-arm64-cgo), const SYS_POLL_NOCANCEL ideal-int
pkg syscall (darwin-arm64-cgo), const SYS_POSIX_SPAWN = 244
pkg syscall (darwin-arm64-cgo), const SYS_POSIX_SPAWN ideal-int
pkg syscall (darwin-arm64-cgo), const SYS_PREAD = 153
pkg syscall (darwin-arm64-cgo), const SYS_PREAD ideal-int
pkg syscall (darwin-arm64-cgo), const SYS_PREAD_NOCANCEL = 414
pkg syscall (darwin-arm64-cgo), const SYS_PREAD_NOCANCEL ideal-int
pkg syscall (darwin-arm64-cgo), const SYS_PROCESS_POLICY = 323
pkg syscall (darwin-arm64-cgo), const SYS_PROCESS_POLICY ideal-int
pkg syscall (darwin-arm64-cgo), const SYS_PROC_INFO = 336
pkg syscall (darwin-arm64-cgo), const SYS_PROC_INFO ideal-int
pkg syscall (darwin-arm64-cgo), const SYS_PSYNCH_CVBROAD = 303
pkg syscall (darwin-arm64-cgo), const SYS_PSYNCH_CVBROAD ideal-int
pkg syscall (darwin-arm64-cgo), const SYS_PSYNCH_CVCLRPREPOST = 312
pkg syscall (darwin-arm64-cgo), const SYS_PSYNCH_CVCLRPREPOST ideal-int
pkg syscall (darwin-arm64-cgo), const SYS_PSYNCH_CVSIGNAL = 304
pkg syscall (darwin-arm64-cgo), const SYS_PSYNCH_CVSIGNAL ideal-int
pkg syscall (darwin-arm64-cgo), const SYS_PSYNCH_CVWAIT = 305
pkg syscall (darwin-arm64-cgo), const SYS_PSYNCH_CVWAIT ideal-int
pkg syscall (darwin-arm64-cgo), const SYS_PSYNCH_MUTEXDROP = 302
pkg syscall (darwin-arm64-cgo), const SYS_PSYNCH_MUTEXDROP ideal-int
pkg syscall (darwin-arm64-cgo), const SYS_PSYNCH_MUTEXWAIT = 301
pkg syscall (darwin-arm64-cgo), const SYS_PSYNCH_MUTEXWAIT ideal-int
pkg syscall (darwin-arm64-cgo), const SYS_PSYNCH_RW_DOWNGRADE = 299
pkg syscall (darwin-arm64-cgo), const SYS_PSYNCH_RW_DOWNGRADE ideal-int
pkg syscall (darwin-arm64-cgo), const SYS_PSYNCH_RW_LONGRDLOCK = 297
pkg syscall (darwin-arm64-cgo), const SYS_PSYNCH_RW_LONGRDLOCK ideal-int
pkg syscall (darwin-arm64-cgo), const SYS_PSYNCH_RW_RDLOCK = 306
pkg syscall (darwin-arm64-cgo), const SYS_PSYNCH_RW_RDLOCK ideal-int
pkg syscall (darwin-arm64-cgo), const SYS_PSYNCH_RW_UNLOCK = 308
pkg syscall (darwin-arm64-cgo), const SYS_PSYNCH_RW_UNLOCK ideal-int
pkg syscall (darwin-arm64-cgo), const SYS_PSYNCH_RW_UNLOCK2 = 309
pkg syscall (darwin-arm64-cgo), const SYS_PSYNCH_RW_UNLOCK2 ideal-int
pkg syscall (darwin-arm64-cgo), const SYS_PSYNCH_RW_UPGRADE = 300
pkg syscall (darwin-arm64-cgo), const SYS_PSYNCH_RW_UPGRADE ideal-int
pkg syscall (darwin-arm64-cgo), const SYS_PSYNCH_RW_WRLOCK = 307
pkg syscall (darwin-arm64-cgo), const SYS_PSYNCH_RW_WRLOCK ideal-int
pkg syscall (darwin-arm64-cgo), const SYS_PSYNCH_RW_YIELDWRLOCK = 298
pkg syscall (darwin-arm64-cgo), const SYS_PSYNCH_RW_YIELDWRLOCK ideal-int
pkg syscall (darwin-arm64-cgo), const SYS_PTRACE = 26
pkg syscall (darwin-arm64-cgo), const SYS_PTRACE ideal-int
pkg syscall (darwin-arm64-cgo), const SYS_PWRITE = 154
pkg syscall (darwin-arm64-cgo), const SYS_PWRITE ideal-int
pkg syscall (darwin-arm64-cgo), const SYS_PWRITE_NOCANCEL = 415
pkg syscall (darwin-arm64-cgo), const SYS_PWRITE_NOCANCEL ideal-int
pkg syscall (darwin-arm64-cgo), const SYS_QUOTACTL = 165
pkg syscall (darwin-arm64-cgo), const SYS_QUOTACTL ideal-int
pkg syscall (darwin-arm64-cgo), const SYS_READ = 3
pkg syscall (darwin-arm64-cgo), const SYS_READ ideal-int
pkg syscall (darwin-arm64-cgo), const SYS_READLINK = 58
pkg syscall (darwin-arm64-cgo), const SYS_READLINK ideal-int
pkg syscall (darwin-arm64-cgo), const SYS_READV = 120
pkg syscall (darwin-arm64-cgo), const SYS_READV ideal-int
pkg syscall (darwin-arm64-cgo), const SYS_READV_NOCANCEL = 411
pkg syscall (darwin-arm64-cgo), const SYS_READV_NOCANCEL ideal-int
pkg syscall (darwin-arm64-cgo), const SYS_READ_NOCANCEL = 396
pkg syscall (darwin-arm64-cgo), const SYS_READ_NOCANCEL ideal-int
pkg syscall (darwin-arm64-cgo), const SYS_REBOOT = 55
pkg syscall (darwin-arm64-cgo), const SYS_REBOOT ideal-int
pkg syscall (darwin-arm64-cgo), const SYS_RECVFROM = 29
pkg syscall (darwin-arm64-cgo), const SYS_RECVFROM ideal-int
pkg syscall (darwin-arm64-cgo), const SYS_RECVFROM_NOCANCEL = 403
pkg syscall (darwin-arm64-cgo), const SYS_RECVFROM_NOCANCEL ideal-int
pkg syscall (darwin-arm64-cgo), const SYS_RECVMSG = 27
pkg syscall (darwin-arm64-cgo), const SYS_RECVMSG ideal-int
pkg syscall (darwin-arm64-cgo), const SYS_RECVMSG_NOCANCEL = 401
pkg syscall (darwin-arm64-cgo), const SYS_RECVMSG_NOCANCEL ideal-int
pkg syscall (darwin-arm64-cgo), const SYS_REMOVEXATTR = 238
pkg syscall (darwin-arm64-cgo), const SYS_REMOVEXATTR ideal-int
pkg syscall (darwin-arm64-cgo), const SYS_RENAME = 128
pkg syscall (darwin-arm64-cgo), const SYS_RENAME ideal-int
pkg syscall (darwin-arm64-cgo), const SYS_REVOKE = 56
pkg syscall (darwin-arm64-cgo), const SYS_REVOKE ideal-int
pkg syscall (darwin-arm64-cgo), const SYS_RMDIR = 137
pkg syscall (darwin-arm64-cgo), const SYS_RMDIR ideal-int
pkg syscall (darwin-arm64-cgo), const SYS_SEARCHFS = 225
pkg syscall (darwin-arm64-cgo), const SYS_SEARCHFS ideal-int
pkg syscall (darwin-arm64-cgo), const SYS_SELECT = 93
pkg syscall (darwin-arm64-cgo), const SYS_SELECT ideal-int
pkg syscall (darwin-arm64-cgo), const SYS_SELECT_NOCANCEL = 407
pkg syscall (darwin-arm64-cgo), const SYS_SELECT_NOCANCEL ideal-int
pkg syscall (darwin-arm64-cgo), const SYS_SEMCTL = 254
pkg syscall (darwin-arm64-cgo), const SYS_SEMCTL ideal-int
pkg syscall (darwin-arm64-cgo), const SYS_SEMGET = 255
pkg syscall (darwin-arm64-cgo), const SYS_SEMGET ideal-int
pkg syscall (darwin-arm64-cgo), const SYS_SEMOP = 256
pkg syscall (darwin-arm64-cgo), const SYS_SEMOP ideal-int
pkg syscall (darwin-arm64-cgo), const SYS_SEMSYS = 251
pkg syscall (darwin-arm64-cgo), const SYS_SEMSYS ideal-int
pkg syscall (darwin-arm64-cgo), const SYS_SEM_CLOSE = 269
pkg syscall (darwin-arm64-cgo), const SYS_SEM_CLOSE ideal-int
pkg syscall (darwin-arm64-cgo), const SYS_SEM_DESTROY = 276
pkg syscall (darwin-arm64-cgo), const SYS_SEM_DESTROY ideal-int
pkg syscall (darwin-arm64-cgo), const SYS_SEM_GETVALUE = 274
pkg syscall (darwin-arm64-cgo), const SYS_SEM_GETVALUE ideal-int
pkg syscall (darwin-arm64-cgo), const SYS_SEM_INIT = 275
pkg syscall (darwin-arm64-cgo), const SYS_SEM_INIT ideal-int
pkg syscall (darwin-arm64-cgo), const SYS_SEM_OPEN = 268
pkg syscall (darwin-arm64-cgo), const SYS_SEM_OPEN ideal-int
pkg syscall (darwin-arm64-cgo), const SYS_SEM_POST = 273
pkg syscall (darwin-arm64-cgo), const SYS_SEM_POST ideal-int
pkg syscall (darwin-arm64-cgo), const SYS_SEM_TRYWAIT = 272
pkg syscall (darwin-arm64-cgo), const SYS_SEM_TRYWAIT ideal-int
pkg syscall (darwin-arm64-cgo), const SYS_SEM_UNLINK = 270
pkg syscall (darwin-arm64-cgo), const SYS_SEM_UNLINK ideal-int
pkg syscall (darwin-arm64-cgo), const SYS_SEM_WAIT = 271
pkg syscall (darwin-arm64-cgo), const SYS_SEM_WAIT ideal-int
pkg syscall (darwin-arm64-cgo), const SYS_SEM_WAIT_NOCANCEL = 420
pkg syscall (darwin-arm64-cgo), const SYS_SEM_WAIT_NOCANCEL ideal-int
pkg syscall (darwin-arm64-cgo), const SYS_SENDFILE = 337
pkg syscall (darwin-arm64-cgo), const SYS_SENDFILE ideal-int
pkg syscall (darwin-arm64-cgo), const SYS_SENDMSG = 28
pkg syscall (darwin-arm64-cgo), const SYS_SENDMSG ideal-int
pkg syscall (darwin-arm64-cgo), const SYS_SENDMSG_NOCANCEL = 402
pkg syscall (darwin-arm64-cgo), const SYS_SENDMSG_NOCANCEL ideal-int
pkg syscall (darwin-arm64-cgo), const SYS_SENDTO = 133
pkg syscall (darwin-arm64-cgo), const SYS_SENDTO ideal-int
pkg syscall (darwin-arm64-cgo), const SYS_SENDTO_NOCANCEL = 413
pkg syscall (darwin-arm64-cgo), const SYS_SENDTO_NOCANCEL ideal-int
pkg syscall (darwin-arm64-cgo), const SYS_SETATTRLIST = 221
pkg syscall (darwin-arm64-cgo), const SYS_SETATTRLIST ideal-int
pkg syscall (darwin-arm64-cgo), const SYS_SETAUDIT_ADDR = 358
pkg syscall (darwin-arm64-cgo), const SYS_SETAUDIT_ADDR ideal-int
pkg syscall (darwin-arm64-cgo), const SYS_SETAUID = 354
pkg syscall (darwin-arm64-cgo), const SYS_SETAUID ideal-int
pkg syscall (darwin-arm64-cgo), const SYS_SETEGID = 182
pkg syscall (darwin-arm64-cgo), const SYS_SETEGID ideal-int
pkg syscall (darwin-arm64-cgo), const SYS_SETEUID = 183
pkg syscall (darwin-arm64-cgo), const SYS_SETEUID ideal-int
pkg syscall (darwin-arm64-cgo), const SYS_SETGID = 181
pkg syscall (darwin-arm64-cgo), const SYS_SETGID ideal-int
pkg syscall (darwin-arm64-cgo), const SYS_SETGROUPS = 80
pkg syscall (darwin-arm64-cgo), const SYS_SETGROUPS ideal-int
pkg syscall (darwin-arm64-cgo), const SYS_SETITIMER = 83
pkg syscall (darwin-arm64-cgo), const SYS_SETITIMER ideal-int
pkg syscall (darwin-arm64-cgo), const SYS_SETLCID = 394
pkg syscall (darwin-arm64-cgo), const SYS_SETLCID ideal-int
pkg syscall (darwin-arm64-cgo), const SYS_SETLOGIN = 50
pkg syscall (darwin-arm64-cgo), const SYS_SETLOGIN ideal-int
pkg syscall (darwin-arm64-cgo), const SYS_SETPGID = 82
pkg syscall (darwin-arm64-cgo), const SYS_SETPGID ideal-int
pkg syscall (darwin-arm64-cgo), const SYS_SETPRIORITY = 96
pkg syscall (darwin-arm64-cgo), const SYS_SETPRIORITY ideal-int
pkg syscall (darwin-arm64-cgo), const SYS_SETPRIVEXEC = 152
pkg syscall (darwin-arm64-cgo), const SYS_SETPRIVEXEC ideal-int
pkg syscall (darwin-arm64-cgo), const SYS_SETREGID = 127
pkg syscall (darwin-arm64-cgo), const SYS_SETREGID ideal-int
pkg syscall (darwin-arm64-cgo), const SYS_SETREUID = 126
pkg syscall (darwin-arm64-cgo), const SYS_SETREUID ideal-int
pkg syscall (darwin-arm64-cgo), const SYS_SETRLIMIT = 195
pkg syscall (darwin-arm64-cgo), const SYS_SETRLIMIT ideal-int
pkg syscall (darwin-arm64-cgo), const SYS_SETSGROUPS = 287
pkg syscall (darwin-arm64-cgo), const SYS_SETSGROUPS ideal-int
pkg syscall (darwin-arm64-cgo), const SYS_SETSID = 147
pkg syscall (darwin-arm64-cgo), const SYS_SETSID ideal-int
pkg syscall (darwin-arm64-cgo), const SYS_SETSOCKOPT = 105
pkg syscall (darwin-arm64-cgo), const SYS_SETSOCKOPT ideal-int
pkg syscall (darwin-arm64-cgo), const SYS_SETTID = 285
pkg syscall (darwin-arm64-cgo), const SYS_SETTID ideal-int
pkg syscall (darwin-arm64-cgo), const SYS_SETTID_WITH_PID = 311
pkg syscall (darwin-arm64-cgo), const SYS_SETTID_WITH_PID ideal-int
pkg syscall (darwin-arm64-cgo), const SYS_SETTIMEOFDAY = 122
pkg syscall (darwin-arm64-cgo), const SYS_SETTIMEOFDAY ideal-int
pkg syscall (darwin-arm64-cgo), const SYS_SETUID = 23
pkg syscall (darwin-arm64-cgo), const SYS_SETUID ideal-int
pkg syscall (darwin-arm64-cgo), const SYS_SETWGROUPS = 289
pkg syscall (darwin-arm64-cgo), const SYS_SETWGROUPS ideal-int
pkg syscall (darwin-arm64-cgo), const SYS_SETXATTR = 236
pkg syscall (darwin-arm64-cgo), const SYS_SETXATTR ideal-int
pkg syscall (darwin-arm64-cgo), const SYS_SHARED_REGION_CHECK_NP = 294
pkg syscall (darwin-arm64-cgo), const SYS_SHARED_REGION_CHECK_NP ideal-int
pkg syscall (darwin-arm64-cgo), const SYS_SHARED_REGION_MAP_AND_SLIDE_NP = 438
pkg syscall (darwin-arm64-cgo), const SYS_SHARED_REGION_MAP_AND_SLIDE_NP ideal-int
pkg syscall (darwin-arm64-cgo), const SYS_SHMAT = 262
pkg syscall (darwin-arm64-cgo), const SYS_SHMAT ideal-int
pkg syscall (darwin-arm64-cgo), const SYS_SHMCTL = 263
pkg syscall (darwin-arm64-cgo), const SYS_SHMCTL ideal-int
pkg syscall (darwin-arm64-cgo), const SYS_SHMDT = 264
pkg syscall (darwin-arm64-cgo), const SYS_SHMDT ideal-int
pkg syscall (darwin-arm64-cgo), const SYS_SHMGET = 265
pkg syscall (darwin-arm64-cgo), const SYS_SHMGET ideal-int
pkg syscall (darwin-arm64-cgo), const SYS_SHMSYS = 253
pkg syscall (darwin-arm64-cgo), const SYS_SHMSYS ideal-int
pkg syscall (darwin-arm64-cgo), const SYS_SHM_OPEN = 266
pkg syscall (darwin-arm64-cgo), const SYS_SHM_OPEN ideal-int
pkg syscall (darwin-arm64-cgo), const SYS_SHM_UNLINK = 267
pkg syscall (darwin-arm64-cgo), const SYS_SHM_UNLINK ideal-int
pkg syscall (darwin-arm64-cgo), const SYS_SHUTDOWN = 134
pkg syscall (darwin-arm64-cgo), const SYS_SHUTDOWN ideal-int
pkg syscall (darwin-arm64-cgo), const SYS_SIGACTION = 46
pkg syscall (darwin-arm64-cgo), const SYS_SIGACTION ideal-int
pkg syscall (darwin-arm64-cgo), const SYS_SIGALTSTACK = 53
pkg syscall (darwin-arm64-cgo), const SYS_SIGALTSTACK ideal-int
pkg syscall (darwin-arm64-cgo), const SYS_SIGPENDING = 52
pkg syscall (darwin-arm64-cgo), const SYS_SIGPENDING ideal-int
pkg syscall (darwin-arm64-cgo), const SYS_SIGPROCMASK = 48
pkg syscall (darwin-arm64-cgo), const SYS_SIGPROCMASK ideal-int
pkg syscall (darwin-arm64-cgo), const SYS_SIGRETURN = 184
pkg syscall (darwin-arm64-cgo), const SYS_SIGRETURN ideal-int
pkg syscall (darwin-arm64-cgo), const SYS_SIGSUSPEND = 111
pkg syscall (darwin-arm64-cgo), const SYS_SIGSUSPEND ideal-int
pkg syscall (darwin-arm64-cgo), const SYS_SIGSUSPEND_NOCANCEL = 410
pkg syscall (darwin-arm64-cgo), const SYS_SIGSUSPEND_NOCANCEL ideal-int
pkg syscall (darwin-arm64-cgo), const SYS_SOCKET = 97
pkg syscall (darwin-arm64-cgo), const SYS_SOCKET ideal-int
pkg syscall (darwin-arm64-cgo), const SYS_SOCKETPAIR = 135
pkg syscall (darwin-arm64-cgo), const SYS_SOCKETPAIR ideal-int
pkg syscall (darwin-arm64-cgo), const SYS_STACK_SNAPSHOT = 365
pkg syscall (darwin-arm64-cgo), const SYS_STACK_SNAPSHOT ideal-int
pkg syscall (darwin-arm64-cgo), const SYS_STAT = 188
pkg syscall (darwin-arm64-cgo), const SYS_STAT ideal-int
pkg syscall (darwin-arm64-cgo), const SYS_STAT64 = 338
pkg syscall (darwin-arm64-cgo), const SYS_STAT64 ideal-int
pkg syscall (darwin-arm64-cgo), const SYS_STAT64_EXTENDED = 341
pkg syscall (darwin-arm64-cgo), const SYS_STAT64_EXTENDED ideal-int
pkg syscall (darwin-arm64-cgo), const SYS_STATFS = 157
pkg syscall (darwin-arm64-cgo), const SYS_STATFS ideal-int
pkg syscall (darwin-arm64-cgo), const SYS_STATFS64 = 345
pkg syscall (darwin-arm64-cgo), const SYS_STATFS64 ideal-int
pkg syscall (darwin-arm64-cgo), const SYS_STAT_EXTENDED = 279
pkg syscall (darwin-arm64-cgo), const SYS_STAT_EXTENDED ideal-int
pkg syscall (darwin-arm64-cgo), const SYS_SWAPON = 85
pkg syscall (darwin-arm64-cgo), const SYS_SWAPON ideal-int
pkg syscall (darwin-arm64-cgo), const SYS_SYMLINK = 57
pkg syscall (darwin-arm64-cgo), const SYS_SYMLINK ideal-int
pkg syscall (darwin-arm64-cgo), const SYS_SYNC = 36
pkg syscall (darwin-arm64-cgo), const SYS_SYNC ideal-int
pkg syscall (darwin-arm64-cgo), const SYS_SYSCALL = 0
pkg syscall (darwin-arm64-cgo), const SYS_SYSCALL ideal-int
pkg syscall (darwin-arm64-cgo), const SYS_THREAD_SELFID = 372
pkg syscall (darwin-arm64-cgo), const SYS_THREAD_SELFID ideal-int
pkg syscall (darwin-arm64-cgo), const SYS_TRUNCATE = 200
pkg syscall (darwin-arm64-cgo), const SYS_TRUNCATE ideal-int
pkg syscall (darwin-arm64-cgo), const SYS_UMASK = 60
pkg syscall (darwin-arm64-cgo), const SYS_UMASK ideal-int
pkg syscall (darwin-arm64-cgo), const SYS_UMASK_EXTENDED = 278
pkg syscall (darwin-arm64-cgo), const SYS_UMASK_EXTENDED ideal-int
pkg syscall (darwin-arm64-cgo), const SYS_UNDELETE = 205
pkg syscall (darwin-arm64-cgo), const SYS_UNDELETE ideal-int
pkg syscall (darwin-arm64-cgo), const SYS_UNLINK = 10
pkg syscall (darwin-arm64-cgo), const SYS_UNLINK ideal-int
pkg syscall (darwin-arm64-cgo), const SYS_UNMOUNT = 159
pkg syscall (darwin-arm64-cgo), const SYS_UNMOUNT ideal-int
pkg syscall (darwin-arm64-cgo), const SYS_UTIMES = 138
pkg syscall (darwin-arm64-cgo), const SYS_UTIMES ideal-int
pkg syscall (darwin-arm64-cgo), const SYS_VFORK = 66
pkg syscall (darwin-arm64-cgo), const SYS_VFORK ideal-int
pkg syscall (darwin-arm64-cgo), const SYS_VM_PRESSURE_MONITOR = 296
pkg syscall (darwin-arm64-cgo), const SYS_VM_PRESSURE_MONITOR ideal-int
pkg syscall (darwin-arm64-cgo), const SYS_WAIT4 = 7
pkg syscall (darwin-arm64-cgo), const SYS_WAIT4 ideal-int
pkg syscall (darwin-arm64-cgo), const SYS_WAIT4_NOCANCEL = 400
pkg syscall (darwin-arm64-cgo), const SYS_WAIT4_NOCANCEL ideal-int
pkg syscall (darwin-arm64-cgo), const SYS_WAITEVENT = 232
pkg syscall (darwin-arm64-cgo), const SYS_WAITEVENT ideal-int
pkg syscall (darwin-arm64-cgo), const SYS_WAITID = 173
pkg syscall (darwin-arm64-cgo), const SYS_WAITID ideal-int
pkg syscall (darwin-arm64-cgo), const SYS_WAITID_NOCANCEL = 416
pkg syscall (darwin-arm64-cgo), const SYS_WAITID_NOCANCEL ideal-int
pkg syscall (darwin-arm64-cgo), const SYS_WATCHEVENT = 231
pkg syscall (darwin-arm64-cgo), const SYS_WATCHEVENT ideal-int
pkg syscall (darwin-arm64-cgo), const SYS_WORKQ_KERNRETURN = 368
pkg syscall (darwin-arm64-cgo), const SYS_WORKQ_KERNRETURN ideal-int
pkg syscall (darwin-arm64-cgo), const SYS_WORKQ_OPEN = 367
pkg syscall (darwin-arm64-cgo), const SYS_WORKQ_OPEN ideal-int
pkg syscall (darwin-arm64-cgo), const SYS_WRITE = 4
pkg syscall (darwin-arm64-cgo), const SYS_WRITE ideal-int
pkg syscall (darwin-arm64-cgo), const SYS_WRITEV = 121
pkg syscall (darwin-arm64-cgo), const SYS_WRITEV ideal-int
pkg syscall (darwin-arm64-cgo), const SYS_WRITEV_NOCANCEL = 412
pkg syscall (darwin-arm64-cgo), const SYS_WRITEV_NOCANCEL ideal-int
pkg syscall (darwin-arm64-cgo), const SYS_WRITE_NOCANCEL = 397
pkg syscall (darwin-arm64-cgo), const SYS_WRITE_NOCANCEL ideal-int
pkg syscall (darwin-arm64-cgo), const SYS___DISABLE_THREADSIGNAL = 331
pkg syscall (darwin-arm64-cgo), const SYS___DISABLE_THREADSIGNAL ideal-int
pkg syscall (darwin-arm64-cgo), const SYS___MAC_EXECVE = 380
pkg syscall (darwin-arm64-cgo), const SYS___MAC_EXECVE ideal-int
pkg syscall (darwin-arm64-cgo), const SYS___MAC_GETFSSTAT = 426
pkg syscall (darwin-arm64-cgo), const SYS___MAC_GETFSSTAT ideal-int
pkg syscall (darwin-arm64-cgo), const SYS___MAC_GET_FD = 388
pkg syscall (darwin-arm64-cgo), const SYS___MAC_GET_FD ideal-int
pkg syscall (darwin-arm64-cgo), const SYS___MAC_GET_FILE = 382
pkg syscall (darwin-arm64-cgo), const SYS___MAC_GET_FILE ideal-int
pkg syscall (darwin-arm64-cgo), const SYS___MAC_GET_LCID = 391
pkg syscall (darwin-arm64-cgo), const SYS___MAC_GET_LCID ideal-int
pkg syscall (darwin-arm64-cgo), const SYS___MAC_GET_LCTX = 392
pkg syscall (darwin-arm64-cgo), const SYS___MAC_GET_LCTX ideal-int
pkg syscall (darwin-arm64-cgo), const SYS___MAC_GET_LINK = 384
pkg syscall (darwin-arm64-cgo), const SYS___MAC_GET_LINK ideal-int
pkg syscall (darwin-arm64-cgo), const SYS___MAC_GET_MOUNT = 425
pkg syscall (darwin-arm64-cgo), const SYS___MAC_GET_MOUNT ideal-int
pkg syscall (darwin-arm64-cgo), const SYS___MAC_GET_PID = 390
pkg syscall (darwin-arm64-cgo), const SYS___MAC_GET_PID ideal-int
pkg syscall (darwin-arm64-cgo), const SYS___MAC_GET_PROC = 386
pkg syscall (darwin-arm64-cgo), const SYS___MAC_GET_PROC ideal-int
pkg syscall (darwin-arm64-cgo), const SYS___MAC_MOUNT = 424
pkg syscall (darwin-arm64-cgo), const SYS___MAC_MOUNT ideal-int
pkg syscall (darwin-arm64-cgo), const SYS___MAC_SET_FD = 389
pkg syscall (darwin-arm64-cgo), const SYS___MAC_SET_FD ideal-int
pkg syscall (darwin-arm64-cgo), const SYS___MAC_SET_FILE = 383
pkg syscall (darwin-arm64-cgo), const SYS___MAC_SET_FILE ideal-int
pkg syscall (darwin-arm64-cgo), const SYS___MAC_SET_LCTX = 393
pkg syscall (darwin-arm64-cgo), const SYS___MAC_SET_LCTX ideal-int
pkg syscall (darwin-arm64-cgo), const SYS___MAC_SET_LINK = 385
pkg syscall (darwin-arm64-cgo), const SYS___MAC_SET_LINK ideal-int
pkg syscall (darwin-arm64-cgo), const SYS___MAC_SET_PROC = 387
pkg syscall (darwin-arm64-cgo), const SYS___MAC_SET_PROC ideal-int
pkg syscall (darwin-arm64-cgo), const SYS___MAC_SYSCALL = 381
pkg syscall (darwin-arm64-cgo), const SYS___MAC_SYSCALL ideal-int
pkg syscall (darwin-arm64-cgo), const SYS___OLD_SEMWAIT_SIGNAL = 370
pkg syscall (darwin-arm64-cgo), const SYS___OLD_SEMWAIT_SIGNAL ideal-int
pkg syscall (darwin-arm64-cgo), const SYS___OLD_SEMWAIT_SIGNAL_NOCANCEL = 371
pkg syscall (darwin-arm64-cgo), const SYS___OLD_SEMWAIT_SIGNAL_NOCANCEL ideal-int
pkg syscall (darwin-arm64-cgo), const SYS___PTHREAD_CANCELED = 333
pkg syscall (darwin-arm64-cgo), const SYS___PTHREAD_CANCELED ideal-int
pkg syscall (darwin-arm64-cgo), const SYS___PTHREAD_CHDIR = 348
pkg syscall (darwin-arm64-cgo), const SYS___PTHREAD_CHDIR ideal-int
pkg syscall (darwin-arm64-cgo), const SYS___PTHREAD_FCHDIR = 349
pkg syscall (darwin-arm64-cgo), const SYS___PTHREAD_FCHDIR ideal-int
pkg syscall (darwin-arm64-cgo), const SYS___PTHREAD_KILL = 328
pkg syscall (darwin-arm64-cgo), const SYS___PTHREAD_KILL ideal-int
pkg syscall (darwin-arm64-cgo), const SYS___PTHREAD_MARKCANCEL = 332
pkg syscall (darwin-arm64-cgo), const SYS___PTHREAD_MARKCANCEL ideal-int
pkg syscall (darwin-arm64-cgo), const SYS___PTHREAD_SIGMASK = 329
pkg syscall (darwin-arm64-cgo), const SYS___PTHREAD_SIGMASK ideal-int
pkg syscall (darwin-arm64-cgo), const SYS___SEMWAIT_SIGNAL = 334
pkg syscall (darwin-arm64-cgo), const SYS___SEMWAIT_SIGNAL ideal-int
pkg syscall (darwin-arm64-cgo), const SYS___SEMWAIT_SIGNAL_NOCANCEL = 423
pkg syscall (darwin-arm64-cgo), const SYS___SEMWAIT_SIGNAL_NOCANCEL ideal-int
pkg syscall (darwin-arm64-cgo), const SYS___SIGWAIT = 330
pkg syscall (darwin-arm64-cgo), const SYS___SIGWAIT ideal-int
pkg syscall (darwin-arm64-cgo), const SYS___SIGWAIT_NOCANCEL = 422
pkg syscall (darwin-arm64-cgo), const SYS___SIGWAIT_NOCANCEL ideal-int
pkg syscall (darwin-arm64-cgo), const SYS___SYSCTL = 202
pkg syscall (darwin-arm64-cgo), const SYS___SYSCTL ideal-int
pkg syscall (darwin-arm64-cgo), const S_IEXEC = 64
pkg syscall (darwin-arm64-cgo), const S_IEXEC ideal-int
pkg syscall (darwin-arm64-cgo), const S_IFMT = 61440
pkg syscall (darwin-arm64-cgo), const S_IFWHT = 57344
pkg syscall (darwin-arm64-cgo), const S_IFWHT ideal-int
pkg syscall (darwin-arm64-cgo), const S_IREAD = 256
pkg syscall (darwin-arm64-cgo), const S_IREAD ideal-int
pkg syscall (darwin-arm64-cgo), const S_IRGRP = 32
pkg syscall (darwin-arm64-cgo), const S_IRGRP ideal-int
pkg syscall (darwin-arm64-cgo), const S_IROTH = 4
pkg syscall (darwin-arm64-cgo), const S_IROTH ideal-int
pkg syscall (darwin-arm64-cgo), const S_IRWXG = 56
pkg syscall (darwin-arm64-cgo), const S_IRWXG ideal-int
pkg syscall (darwin-arm64-cgo), const S_IRWXO = 7
pkg syscall (darwin-arm64-cgo), const S_IRWXO ideal-int
pkg syscall (darwin-arm64-cgo), const S_IRWXU = 448
pkg syscall (darwin-arm64-cgo), const S_IRWXU ideal-int
pkg syscall (darwin-arm64-cgo), const S_ISTXT = 512
pkg syscall (darwin-arm64-cgo), const S_ISTXT ideal-int
pkg syscall (darwin-arm64-cgo), const S_IWGRP = 16
pkg syscall (darwin-arm64-cgo), const S_IWGRP ideal-int
pkg syscall (darwin-arm64-cgo), const S_IWOTH = 2
pkg syscall (darwin-arm64-cgo), const S_IWOTH ideal-int
pkg syscall (darwin-arm64-cgo), const S_IWRITE = 128
pkg syscall (darwin-arm64-cgo), const S_IWRITE ideal-int
pkg syscall (darwin-arm64-cgo), const S_IXGRP = 8
pkg syscall (darwin-arm64-cgo), const S_IXGRP ideal-int
pkg syscall (darwin-arm64-cgo), const S_IXOTH = 1
pkg syscall (darwin-arm64-cgo), const S_IXOTH ideal-int
pkg syscall (darwin-arm64-cgo), const SizeofBpfHdr = 20
pkg syscall (darwin-arm64-cgo), const SizeofBpfHdr ideal-int
pkg syscall (darwin-arm64-cgo), const SizeofBpfInsn = 8
pkg syscall (darwin-arm64-cgo), const SizeofBpfInsn ideal-int
pkg syscall (darwin-arm64-cgo), const SizeofBpfProgram = 16
pkg syscall (darwin-arm64-cgo), const SizeofBpfProgram ideal-int
pkg syscall (darwin-arm64-cgo), const SizeofBpfStat = 8
pkg syscall (darwin-arm64-cgo), const SizeofBpfStat ideal-int
pkg syscall (darwin-arm64-cgo), const SizeofBpfVersion = 4
pkg syscall (darwin-arm64-cgo), const SizeofBpfVersion ideal-int
pkg syscall (darwin-arm64-cgo), const SizeofCmsghdr = 12
pkg syscall (darwin-arm64-cgo), const SizeofCmsghdr ideal-int
pkg syscall (darwin-arm64-cgo), const SizeofICMPv6Filter = 32
pkg syscall (darwin-arm64-cgo), const SizeofICMPv6Filter ideal-int
pkg syscall (darwin-arm64-cgo), const SizeofIPMreq = 8
pkg syscall (darwin-arm64-cgo), const SizeofIPMreq ideal-int
pkg syscall (darwin-arm64-cgo), const SizeofIPv6MTUInfo = 32
pkg syscall (darwin-arm64-cgo), const SizeofIPv6MTUInfo ideal-int
pkg syscall (darwin-arm64-cgo), const SizeofIPv6Mreq = 20
pkg syscall (darwin-arm64-cgo), const SizeofIPv6Mreq ideal-int
pkg syscall (darwin-arm64-cgo), const SizeofIfData = 96
pkg syscall (darwin-arm64-cgo), const SizeofIfData ideal-int
pkg syscall (darwin-arm64-cgo), const SizeofIfMsghdr = 112
pkg syscall (darwin-arm64-cgo), const SizeofIfMsghdr ideal-int
pkg syscall (darwin-arm64-cgo), const SizeofIfaMsghdr = 20
pkg syscall (darwin-arm64-cgo), const SizeofIfaMsghdr ideal-int
pkg syscall (darwin-arm64-cgo), const SizeofIfmaMsghdr = 16
pkg syscall (darwin-arm64-cgo), const SizeofIfmaMsghdr ideal-int
pkg syscall (darwin-arm64-cgo), const SizeofIfmaMsghdr2 = 20
pkg syscall (darwin-arm64-cgo), const SizeofIfmaMsghdr2 ideal-int
pkg syscall (darwin-arm64-cgo), const SizeofInet4Pktinfo = 12
pkg syscall (darwin-arm64-cgo), const SizeofInet4Pktinfo ideal-int
pkg syscall (darwin-arm64-cgo), const SizeofInet6Pktinfo = 20
pkg syscall (darwin-arm64-cgo), const SizeofInet6Pktinfo ideal-int
pkg syscall (darwin-arm64-cgo), const SizeofLinger = 8
pkg syscall (darwin-arm64-cgo), const SizeofLinger ideal-int
pkg syscall (darwin-arm64-cgo), const SizeofMsghdr = 48
pkg syscall (darwin-arm64-cgo), const SizeofMsghdr ideal-int
pkg syscall (darwin-arm64-cgo), const SizeofRtMetrics = 56
pkg syscall (darwin-arm64-cgo), const SizeofRtMetrics ideal-int
pkg syscall (darwin-arm64-cgo), const SizeofRtMsghdr = 92
pkg syscall (darwin-arm64-cgo), const SizeofRtMsghdr ideal-int
pkg syscall (darwin-arm64-cgo), const SizeofSockaddrAny = 108
pkg syscall (darwin-arm64-cgo), const SizeofSockaddrAny ideal-int
pkg syscall (darwin-arm64-cgo), const SizeofSockaddrDatalink = 20
pkg syscall (darwin-arm64-cgo), const SizeofSockaddrDatalink ideal-int
pkg syscall (darwin-arm64-cgo), const SizeofSockaddrInet4 = 16
pkg syscall (darwin-arm64-cgo), const SizeofSockaddrInet4 ideal-int
pkg syscall (darwin-arm64-cgo), const SizeofSockaddrInet6 = 28
pkg syscall (darwin-arm64-cgo), const SizeofSockaddrInet6 ideal-int
pkg syscall (darwin-arm64-cgo), const SizeofSockaddrUnix = 106
pkg syscall (darwin-arm64-cgo), const SizeofSockaddrUnix ideal-int
pkg syscall (darwin-arm64-cgo), const TCIFLUSH = 1
pkg syscall (darwin-arm64-cgo), const TCIFLUSH ideal-int
pkg syscall (darwin-arm64-cgo), const TCIOFLUSH = 3
pkg syscall (darwin-arm64-cgo), const TCIOFLUSH ideal-int
pkg syscall (darwin-arm64-cgo), const TCOFLUSH = 2
pkg syscall (darwin-arm64-cgo), const TCOFLUSH ideal-int
pkg syscall (darwin-arm64-cgo), const TCP_CONNECTIONTIMEOUT = 32
pkg syscall (darwin-arm64-cgo), const TCP_CONNECTIONTIMEOUT ideal-int
pkg syscall (darwin-arm64-cgo), const TCP_ENABLE_ECN = 260
pkg syscall (darwin-arm64-cgo), const TCP_ENABLE_ECN ideal-int
pkg syscall (darwin-arm64-cgo), const TCP_KEEPALIVE = 16
pkg syscall (darwin-arm64-cgo), const TCP_KEEPALIVE ideal-int
pkg syscall (darwin-arm64-cgo), const TCP_KEEPCNT = 258
pkg syscall (darwin-arm64-cgo), const TCP_KEEPCNT ideal-int
pkg syscall (darwin-arm64-cgo), const TCP_KEEPINTVL = 257
pkg syscall (darwin-arm64-cgo), const TCP_KEEPINTVL ideal-int
pkg syscall (darwin-arm64-cgo), const TCP_MAXHLEN = 60
pkg syscall (darwin-arm64-cgo), const TCP_MAXHLEN ideal-int
pkg syscall (darwin-arm64-cgo), const TCP_MAXOLEN = 40
pkg syscall (darwin-arm64-cgo), const TCP_MAXOLEN ideal-int
pkg syscall (darwin-arm64-cgo), const TCP_MAXSEG = 2
pkg syscall (darwin-arm64-cgo), const TCP_MAXSEG ideal-int
pkg syscall (darwin-arm64-cgo), const TCP_MAXWIN = 65535
pkg syscall (darwin-arm64-cgo), const TCP_MAXWIN ideal-int
pkg syscall (darwin-arm64-cgo), const TCP_MAX_SACK = 4
pkg syscall (darwin-arm64-cgo), const TCP_MAX_SACK ideal-int
pkg syscall (darwin-arm64-cgo), const TCP_MAX_WINSHIFT = 14
pkg syscall (darwin-arm64-cgo), const TCP_MAX_WINSHIFT ideal-int
pkg syscall (darwin-arm64-cgo), const TCP_MINMSS = 216
pkg syscall (darwin-arm64-cgo), const TCP_MINMSS ideal-int
pkg syscall (darwin-arm64-cgo), const TCP_MSS = 512
pkg syscall (darwin-arm64-cgo), const TCP_MSS ideal-int
pkg syscall (darwin-arm64-cgo), const TCP_NOOPT = 8
pkg syscall (darwin-arm64-cgo), const TCP_NOOPT ideal-int
pkg syscall (darwin-arm64-cgo), const TCP_NOPUSH = 4
pkg syscall (darwin-arm64-cgo), const TCP_NOPUSH ideal-int
pkg syscall (darwin-arm64-cgo), const TCP_NOTSENT_LOWAT = 513
pkg syscall (darwin-arm64-cgo), const TCP_NOTSENT_LOWAT ideal-int
pkg syscall (darwin-arm64-cgo), const TCP_RXT_CONNDROPTIME = 128
pkg syscall (darwin-arm64-cgo), const TCP_RXT_CONNDROPTIME ideal-int
pkg syscall (darwin-arm64-cgo), const TCP_RXT_FINDROP = 256
pkg syscall (darwin-arm64-cgo), const TCP_RXT_FINDROP ideal-int
pkg syscall (darwin-arm64-cgo), const TCP_SENDMOREACKS = 259
pkg syscall (darwin-arm64-cgo), const TCP_SENDMOREACKS ideal-int
pkg syscall (darwin-arm64-cgo), const TCSAFLUSH = 2
pkg syscall (darwin-arm64-cgo), const TCSAFLUSH ideal-int
pkg syscall (darwin-arm64-cgo), const TIOCCBRK = 536900730
pkg syscall (darwin-arm64-cgo), const TIOCCBRK ideal-int
pkg syscall (darwin-arm64-cgo), const TIOCCDTR = 536900728
pkg syscall (darwin-arm64-cgo), const TIOCCDTR ideal-int
pkg syscall (darwin-arm64-cgo), const TIOCCONS = 2147775586
pkg syscall (darwin-arm64-cgo), const TIOCCONS ideal-int
pkg syscall (darwin-arm64-cgo), const TIOCDCDTIMESTAMP = 1074820184
pkg syscall (darwin-arm64-cgo), const TIOCDCDTIMESTAMP ideal-int
pkg syscall (darwin-arm64-cgo), const TIOCDRAIN = 536900702
pkg syscall (darwin-arm64-cgo), const TIOCDRAIN ideal-int
pkg syscall (darwin-arm64-cgo), const TIOCDSIMICROCODE = 536900693
pkg syscall (darwin-arm64-cgo), const TIOCDSIMICROCODE ideal-int
pkg syscall (darwin-arm64-cgo), const TIOCEXCL = 536900621
pkg syscall (darwin-arm64-cgo), const TIOCEXCL ideal-int
pkg syscall (darwin-arm64-cgo), const TIOCEXT = 2147775584
pkg syscall (darwin-arm64-cgo), const TIOCEXT ideal-int
pkg syscall (darwin-arm64-cgo), const TIOCFLUSH = 2147775504
pkg syscall (darwin-arm64-cgo), const TIOCFLUSH ideal-int
pkg syscall (darwin-arm64-cgo), const TIOCGDRAINWAIT = 1074033750
pkg syscall (darwin-arm64-cgo), const TIOCGDRAINWAIT ideal-int
pkg syscall (darwin-arm64-cgo), const TIOCGETA = 1078490131
pkg syscall (darwin-arm64-cgo), const TIOCGETA ideal-int
pkg syscall (darwin-arm64-cgo), const TIOCGETD = 1074033690
pkg syscall (darwin-arm64-cgo), const TIOCGETD ideal-int
pkg syscall (darwin-arm64-cgo), const TIOCGPGRP = 1074033783
pkg syscall (darwin-arm64-cgo), const TIOCGPGRP ideal-int
pkg syscall (darwin-arm64-cgo), const TIOCGWINSZ = 1074295912
pkg syscall (darwin-arm64-cgo), const TIOCGWINSZ ideal-int
pkg syscall (darwin-arm64-cgo), const TIOCIXOFF = 536900736
pkg syscall (darwin-arm64-cgo), const TIOCIXOFF ideal-int
pkg syscall (darwin-arm64-cgo), const TIOCIXON = 536900737
pkg syscall (darwin-arm64-cgo), const TIOCIXON ideal-int
pkg syscall (darwin-arm64-cgo), const TIOCMBIC = 2147775595
pkg syscall (darwin-arm64-cgo), const TIOCMBIC ideal-int
pkg syscall (darwin-arm64-cgo), const TIOCMBIS = 2147775596
pkg syscall (darwin-arm64-cgo), const TIOCMBIS ideal-int
pkg syscall (darwin-arm64-cgo), const TIOCMGDTRWAIT = 1074033754
pkg syscall (darwin-arm64-cgo), const TIOCMGDTRWAIT ideal-int
pkg syscall (darwin-arm64-cgo), const TIOCMGET = 1074033770
pkg syscall (darwin-arm64-cgo), const TIOCMGET ideal-int
pkg syscall (darwin-arm64-cgo), const TIOCMODG = 1074033667
pkg syscall (darwin-arm64-cgo), const TIOCMODG ideal-int
pkg syscall (darwin-arm64-cgo), const TIOCMODS = 2147775492
pkg syscall (darwin-arm64-cgo), const TIOCMODS ideal-int
pkg syscall (darwin-arm64-cgo), const TIOCMSDTRWAIT = 2147775579
pkg syscall (darwin-arm64-cgo), const TIOCMSDTRWAIT ideal-int
pkg syscall (darwin-arm64-cgo), const TIOCMSET = 2147775597
pkg syscall (darwin-arm64-cgo), const TIOCMSET ideal-int
pkg syscall (darwin-arm64-cgo), const TIOCM_CAR = 64
pkg syscall (darwin-arm64-cgo), const TIOCM_CAR ideal-int
pkg syscall (darwin-arm64-cgo), const TIOCM_CD = 64
pkg syscall (darwin-arm64-cgo), const TIOCM_CD ideal-int
pkg syscall (darwin-arm64-cgo), const TIOCM_CTS = 32
pkg syscall (darwin-arm64-cgo), const TIOCM_CTS ideal-int
pkg syscall (darwin-arm64-cgo), const TIOCM_DSR = 256
pkg syscall (darwin-arm64-cgo), const TIOCM_DSR ideal-int
pkg syscall (darwin-arm64-cgo), const TIOCM_DTR = 2
pkg syscall (darwin-arm64-cgo), const TIOCM_DTR ideal-int
pkg syscall (darwin-arm64-cgo), const TIOCM_LE = 1
pkg syscall (darwin-arm64-cgo), const TIOCM_LE ideal-int
pkg syscall (darwin-arm64-cgo), const TIOCM_RI = 128
pkg syscall (darwin-arm64-cgo), const TIOCM_RI ideal-int
pkg syscall (darwin-arm64-cgo), const TIOCM_RNG = 128
pkg syscall (darwin-arm64-cgo), const TIOCM_RNG ideal-int
pkg syscall (darwin-arm64-cgo), const TIOCM_RTS = 4
pkg syscall (darwin-arm64-cgo), const TIOCM_RTS ideal-int
pkg syscall (darwin-arm64-cgo), const TIOCM_SR = 16
pkg syscall (darwin-arm64-cgo), const TIOCM_SR ideal-int
pkg syscall (darwin-arm64-cgo), const TIOCM_ST = 8
pkg syscall (darwin-arm64-cgo), const TIOCM_ST ideal-int
pkg syscall (darwin-arm64-cgo), const TIOCNOTTY = 536900721
pkg syscall (darwin-arm64-cgo), const TIOCNOTTY ideal-int
pkg syscall (darwin-arm64-cgo), const TIOCNXCL = 536900622
pkg syscall (darwin-arm64-cgo), const TIOCNXCL ideal-int
pkg syscall (darwin-arm64-cgo), const TIOCOUTQ = 1074033779
pkg syscall (darwin-arm64-cgo), const TIOCOUTQ ideal-int
pkg syscall (darwin-arm64-cgo), const TIOCPKT = 2147775600
pkg syscall (darwin-arm64-cgo), const TIOCPKT ideal-int
pkg syscall (darwin-arm64-cgo), const TIOCPKT_DATA = 0
pkg syscall (darwin-arm64-cgo), const TIOCPKT_DATA ideal-int
pkg syscall (darwin-arm64-cgo), const TIOCPKT_DOSTOP = 32
pkg syscall (darwin-arm64-cgo), const TIOCPKT_DOSTOP ideal-int
pkg syscall (darwin-arm64-cgo), const TIOCPKT_FLUSHREAD = 1
pkg syscall (darwin-arm64-cgo), const TIOCPKT_FLUSHREAD ideal-int
pkg syscall (darwin-arm64-cgo), const TIOCPKT_FLUSHWRITE = 2
pkg syscall (darwin-arm64-cgo), const TIOCPKT_FLUSHWRITE ideal-int
pkg syscall (darwin-arm64-cgo), const TIOCPKT_IOCTL = 64
pkg syscall (darwin-arm64-cgo), const TIOCPKT_IOCTL ideal-int
pkg syscall (darwin-arm64-cgo), const TIOCPKT_NOSTOP = 16
pkg syscall (darwin-arm64-cgo), const TIOCPKT_NOSTOP ideal-int
pkg syscall (darwin-arm64-cgo), const TIOCPKT_START = 8
pkg syscall (darwin-arm64-cgo), const TIOCPKT_START ideal-int
pkg syscall (darwin-arm64-cgo), const TIOCPKT_STOP = 4
pkg syscall (darwin-arm64-cgo), const TIOCPKT_STOP ideal-int
pkg syscall (darwin-arm64-cgo), const TIOCPTYGNAME = 1082160211
pkg syscall (darwin-arm64-cgo), const TIOCPTYGNAME ideal-int
pkg syscall (darwin-arm64-cgo), const TIOCPTYGRANT = 536900692
pkg syscall (darwin-arm64-cgo), const TIOCPTYGRANT ideal-int
pkg syscall (darwin-arm64-cgo), const TIOCPTYUNLK = 536900690
pkg syscall (darwin-arm64-cgo), const TIOCPTYUNLK ideal-int
pkg syscall (darwin-arm64-cgo), const TIOCREMOTE = 2147775593
pkg syscall (darwin-arm64-cgo), const TIOCREMOTE ideal-int
pkg syscall (darwin-arm64-cgo), const TIOCSBRK = 536900731
pkg syscall (darwin-arm64-cgo), const TIOCSBRK ideal-int
pkg syscall (darwin-arm64-cgo), const TIOCSCONS = 536900707
pkg syscall (darwin-arm64-cgo), const TIOCSCONS ideal-int
pkg syscall (darwin-arm64-cgo), const TIOCSCTTY = 536900705
pkg syscall (darwin-arm64-cgo), const TIOCSCTTY ideal-int
pkg syscall (darwin-arm64-cgo), const TIOCSDRAINWAIT = 2147775575
pkg syscall (darwin-arm64-cgo), const TIOCSDRAINWAIT ideal-int
pkg syscall (darwin-arm64-cgo), const TIOCSDTR = 536900729
pkg syscall (darwin-arm64-cgo), const TIOCSDTR ideal-int
pkg syscall (darwin-arm64-cgo), const TIOCSETA = 2152231956
pkg syscall (darwin-arm64-cgo), const TIOCSETA ideal-int
pkg syscall (darwin-arm64-cgo), const TIOCSETAF = 2152231958
pkg syscall (darwin-arm64-cgo), const TIOCSETAF ideal-int
pkg syscall (darwin-arm64-cgo), const TIOCSETAW = 2152231957
pkg syscall (darwin-arm64-cgo), const TIOCSETAW ideal-int
pkg syscall (darwin-arm64-cgo), const TIOCSETD = 2147775515
pkg syscall (darwin-arm64-cgo), const TIOCSETD ideal-int
pkg syscall (darwin-arm64-cgo), const TIOCSIG = 536900703
pkg syscall (darwin-arm64-cgo), const TIOCSIG ideal-int
pkg syscall (darwin-arm64-cgo), const TIOCSPGRP = 2147775606
pkg syscall (darwin-arm64-cgo), const TIOCSPGRP ideal-int
pkg syscall (darwin-arm64-cgo), const TIOCSTART = 536900718
pkg syscall (darwin-arm64-cgo), const TIOCSTART ideal-int
pkg syscall (darwin-arm64-cgo), const TIOCSTAT = 536900709
pkg syscall (darwin-arm64-cgo), const TIOCSTAT ideal-int
pkg syscall (darwin-arm64-cgo), const TIOCSTI = 2147578994
pkg syscall (darwin-arm64-cgo), const TIOCSTI ideal-int
pkg syscall (darwin-arm64-cgo), const TIOCSTOP = 536900719
pkg syscall (darwin-arm64-cgo), const TIOCSTOP ideal-int
pkg syscall (darwin-arm64-cgo), const TIOCSWINSZ = 2148037735
pkg syscall (darwin-arm64-cgo), const TIOCSWINSZ ideal-int
pkg syscall (darwin-arm64-cgo), const TIOCTIMESTAMP = 1074820185
pkg syscall (darwin-arm64-cgo), const TIOCTIMESTAMP ideal-int
pkg syscall (darwin-arm64-cgo), const TIOCUCNTL = 2147775590
pkg syscall (darwin-arm64-cgo), const TIOCUCNTL ideal-int
pkg syscall (darwin-arm64-cgo), const TOSTOP = 4194304
pkg syscall (darwin-arm64-cgo), const TOSTOP ideal-int
pkg syscall (darwin-arm64-cgo), const VDISCARD = 15
pkg syscall (darwin-arm64-cgo), const VDISCARD ideal-int
pkg syscall (darwin-arm64-cgo), const VDSUSP = 11
pkg syscall (darwin-arm64-cgo), const VDSUSP ideal-int
pkg syscall (darwin-arm64-cgo), const VEOF = 0
pkg syscall (darwin-arm64-cgo), const VEOF ideal-int
pkg syscall (darwin-arm64-cgo), const VEOL = 1
pkg syscall (darwin-arm64-cgo), const VEOL ideal-int
pkg syscall (darwin-arm64-cgo), const VEOL2 = 2
pkg syscall (darwin-arm64-cgo), const VEOL2 ideal-int
pkg syscall (darwin-arm64-cgo), const VERASE = 3
pkg syscall (darwin-arm64-cgo), const VERASE ideal-int
pkg syscall (darwin-arm64-cgo), const VINTR = 8
pkg syscall (darwin-arm64-cgo), const VINTR ideal-int
pkg syscall (darwin-arm64-cgo), const VKILL = 5
pkg syscall (darwin-arm64-cgo), const VKILL ideal-int
pkg syscall (darwin-arm64-cgo), const VLNEXT = 14
pkg syscall (darwin-arm64-cgo), const VLNEXT ideal-int
pkg syscall (darwin-arm64-cgo), const VMIN = 16
pkg syscall (darwin-arm64-cgo), const VMIN ideal-int
pkg syscall (darwin-arm64-cgo), const VQUIT = 9
pkg syscall (darwin-arm64-cgo), const VQUIT ideal-int
pkg syscall (darwin-arm64-cgo), const VREPRINT = 6
pkg syscall (darwin-arm64-cgo), const VREPRINT ideal-int
pkg syscall (darwin-arm64-cgo), const VSTART = 12
pkg syscall (darwin-arm64-cgo), const VSTART ideal-int
pkg syscall (darwin-arm64-cgo), const VSTATUS = 18
pkg syscall (darwin-arm64-cgo), const VSTATUS ideal-int
pkg syscall (darwin-arm64-cgo), const VSTOP = 13
pkg syscall (darwin-arm64-cgo), const VSTOP ideal-int
pkg syscall (darwin-arm64-cgo), const VSUSP = 10
pkg syscall (darwin-arm64-cgo), const VSUSP ideal-int
pkg syscall (darwin-arm64-cgo), const VT0 = 0
pkg syscall (darwin-arm64-cgo), const VT0 ideal-int
pkg syscall (darwin-arm64-cgo), const VT1 = 65536
pkg syscall (darwin-arm64-cgo), const VT1 ideal-int
pkg syscall (darwin-arm64-cgo), const VTDLY = 65536
pkg syscall (darwin-arm64-cgo), const VTDLY ideal-int
pkg syscall (darwin-arm64-cgo), const VTIME = 17
pkg syscall (darwin-arm64-cgo), const VTIME ideal-int
pkg syscall (darwin-arm64-cgo), const VWERASE = 4
pkg syscall (darwin-arm64-cgo), const VWERASE ideal-int
pkg syscall (darwin-arm64-cgo), const WCONTINUED = 16
pkg syscall (darwin-arm64-cgo), const WCONTINUED ideal-int
pkg syscall (darwin-arm64-cgo), const WCOREFLAG = 128
pkg syscall (darwin-arm64-cgo), const WCOREFLAG ideal-int
pkg syscall (darwin-arm64-cgo), const WEXITED = 4
pkg syscall (darwin-arm64-cgo), const WEXITED ideal-int
pkg syscall (darwin-arm64-cgo), const WNOHANG = 1
pkg syscall (darwin-arm64-cgo), const WNOHANG ideal-int
pkg syscall (darwin-arm64-cgo), const WNOWAIT = 32
pkg syscall (darwin-arm64-cgo), const WNOWAIT ideal-int
pkg syscall (darwin-arm64-cgo), const WORDSIZE = 64
pkg syscall (darwin-arm64-cgo), const WORDSIZE ideal-int
pkg syscall (darwin-arm64-cgo), const WSTOPPED = 8
pkg syscall (darwin-arm64-cgo), const WSTOPPED ideal-int
pkg syscall (darwin-arm64-cgo), const WUNTRACED = 2
pkg syscall (darwin-arm64-cgo), const WUNTRACED ideal-int
pkg syscall (darwin-arm64-cgo), func Accept(int) (int, Sockaddr, error)
pkg syscall (darwin-arm64-cgo), func Access(string, uint32) error
pkg syscall (darwin-arm64-cgo), func Adjtime(*Timeval, *Timeval) error
pkg syscall (darwin-arm64-cgo), func Bind(int, Sockaddr) error
pkg syscall (darwin-arm64-cgo), func BpfBuflen //deprecated
pkg syscall (darwin-arm64-cgo), func BpfBuflen(int) (int, error)
pkg syscall (darwin-arm64-cgo), func BpfDatalink //deprecated
pkg syscall (darwin-arm64-cgo), func BpfDatalink(int) (int, error)
pkg syscall (darwin-arm64-cgo), func BpfHeadercmpl //deprecated
pkg syscall (darwin-arm64-cgo), func BpfHeadercmpl(int) (int, error)
pkg syscall (darwin-arm64-cgo), func BpfInterface //deprecated
pkg syscall (darwin-arm64-cgo), func BpfInterface(int, string) (string, error)
pkg syscall (darwin-arm64-cgo), func BpfJump //deprecated
pkg syscall (darwin-arm64-cgo), func BpfJump(int, int, int, int) *BpfInsn
pkg syscall (darwin-arm64-cgo), func BpfStats //deprecated
pkg syscall (darwin-arm64-cgo), func BpfStats(int) (*BpfStat, error)
pkg syscall (darwin-arm64-cgo), func BpfStmt //deprecated
pkg syscall (darwin-arm64-cgo), func BpfStmt(int, int) *BpfInsn
pkg syscall (darwin-arm64-cgo), func BpfTimeout //deprecated
pkg syscall (darwin-arm64-cgo), func BpfTimeout(int) (*Timeval, error)
pkg syscall (darwin-arm64-cgo), func CheckBpfVersion //deprecated
pkg syscall (darwin-arm64-cgo), func CheckBpfVersion(int) error
pkg syscall (darwin-arm64-cgo), func Chflags(string, int) error
pkg syscall (darwin-arm64-cgo), func Chroot(string) error
pkg syscall (darwin-arm64-cgo), func Close(int) error
pkg syscall (darwin-arm64-cgo), func CloseOnExec(int)
pkg syscall (darwin-arm64-cgo), func CmsgLen(int) int
pkg syscall (darwin-arm64-cgo), func CmsgSpace(int) int
pkg syscall (darwin-arm64-cgo), func Connect(int, Sockaddr) error
pkg syscall (darwin-arm64-cgo), func Dup(int) (int, error)
pkg syscall (darwin-arm64-cgo), func Dup2(int, int) error
pkg syscall (darwin-arm64-cgo), func Exchangedata(string, string, int) error
pkg syscall (darwin-arm64-cgo), func Fchdir(int) error
pkg syscall (darwin-arm64-cgo), func Fchflags(int, int) error
pkg syscall (darwin-arm64-cgo), func Fchmod(int, uint32) error
pkg syscall (darwin-arm64-cgo), func Fchown(int, int, int) error
pkg syscall (darwin-arm64-cgo), func FcntlFlock(uintptr, int, *Flock_t) error
pkg syscall (darwin-arm64-cgo), func Flock(int, int) error
pkg syscall (darwin-arm64-cgo), func FlushBpf //deprecated
pkg syscall (darwin-arm64-cgo), func FlushBpf(int) error
pkg syscall (darwin-arm64-cgo), func ForkExec(string, []string, *ProcAttr) (int, error)
pkg syscall (darwin-arm64-cgo), func Fpathconf(int, int) (int, error)
pkg syscall (darwin-arm64-cgo), func Fstat(int, *Stat_t) error
pkg syscall (darwin-arm64-cgo), func Fstatfs(int, *Statfs_t) error
pkg syscall (darwin-arm64-cgo), func Fsync(int) error
pkg syscall (darwin-arm64-cgo), func Ftruncate(int, int64) error
pkg syscall (darwin-arm64-cgo), func Futimes(int, []Timeval) error
pkg syscall (darwin-arm64-cgo), func Getdirentries(int, []uint8, *uintptr) (int, error)
pkg syscall (darwin-arm64-cgo), func Getdtablesize() int
pkg syscall (darwin-arm64-cgo), func Getfsstat([]Statfs_t, int) (int, error)
pkg syscall (darwin-arm64-cgo), func Getpeername(int) (Sockaddr, error)
pkg syscall (darwin-arm64-cgo), func Getpgid(int) (int, error)
pkg syscall (darwin-arm64-cgo), func Getpgrp() int
pkg syscall (darwin-arm64-cgo), func Getpriority(int, int) (int, error)
pkg syscall (darwin-arm64-cgo), func Getrlimit(int, *Rlimit) error
pkg syscall (darwin-arm64-cgo), func Getrusage(int, *Rusage) error
pkg syscall (darwin-arm64-cgo), func Getsid(int) (int, error)
pkg syscall (darwin-arm64-cgo), func Getsockname(int) (Sockaddr, error)
pkg syscall (darwin-arm64-cgo), func GetsockoptByte(int, int, int) (uint8, error)
pkg syscall (darwin-arm64-cgo), func GetsockoptICMPv6Filter(int, int, int) (*ICMPv6Filter, error)
pkg syscall (darwin-arm64-cgo), func GetsockoptIPMreq(int, int, int) (*IPMreq, error)
pkg syscall (darwin-arm64-cgo), func GetsockoptIPv6MTUInfo(int, int, int) (*IPv6MTUInfo, error)
pkg syscall (darwin-arm64-cgo), func GetsockoptIPv6Mreq(int, int, int) (*IPv6Mreq, error)
pkg syscall (darwin-arm64-cgo), func GetsockoptInet4Addr(int, int, int) ([4]uint8, error)
pkg syscall (darwin-arm64-cgo), func GetsockoptInt(int, int, int) (int, error)
pkg syscall (darwin-arm64-cgo), func Issetugid() bool
pkg syscall (darwin-arm64-cgo), func Kevent(int, []Kevent_t, []Kevent_t, *Timespec) (int, error)
pkg syscall (darwin-arm64-cgo), func Kill(int, Signal) error
pkg syscall (darwin-arm64-cgo), func Kqueue() (int, error)
pkg syscall (darwin-arm64-cgo), func Listen(int, int) error
pkg syscall (darwin-arm64-cgo), func Lstat(string, *Stat_t) error
pkg syscall (darwin-arm64-cgo), func Mkfifo(string, uint32) error
pkg syscall (darwin-arm64-cgo), func Mknod(string, uint32, int) error
pkg syscall (darwin-arm64-cgo), func Mlock([]uint8) error
pkg syscall (darwin-arm64-cgo), func Mlockall(int) error
pkg syscall (darwin-arm64-cgo), func Mmap(int, int64, int, int, int) ([]uint8, error)
pkg syscall (darwin-arm64-cgo), func Mprotect([]uint8, int) error
pkg syscall (darwin-arm64-cgo), func Munlock([]uint8) error
pkg syscall (darwin-arm64-cgo), func Munlockall() error
pkg syscall (darwin-arm64-cgo), func Munmap([]uint8) error
pkg syscall (darwin-arm64-cgo), func Open(string, int, uint32) (int, error)
pkg syscall (darwin-arm64-cgo), func ParseDirent([]uint8, int, []string) (int, int, []string)
pkg syscall (darwin-arm64-cgo), func ParseRoutingMessage //deprecated
pkg syscall (darwin-arm64-cgo), func ParseRoutingMessage([]uint8) ([]RoutingMessage, error)
pkg syscall (darwin-arm64-cgo), func ParseRoutingSockaddr //deprecated
pkg syscall (darwin-arm64-cgo), func ParseRoutingSockaddr(RoutingMessage) ([]Sockaddr, error)
pkg syscall (darwin-arm64-cgo), func ParseSocketControlMessage([]uint8) ([]SocketControlMessage, error)
pkg syscall (darwin-arm64-cgo), func ParseUnixRights(*SocketControlMessage) ([]int, error)
pkg syscall (darwin-arm64-cgo), func Pathconf(string, int) (int, error)
pkg syscall (darwin-arm64-cgo), func Pipe([]int) error
pkg syscall (darwin-arm64-cgo), func Pread(int, []uint8, int64) (int, error)
pkg syscall (darwin-arm64-cgo), func PtraceAttach(int) error
pkg syscall (darwin-arm64-cgo), func PtraceDetach(int) error
pkg syscall (darwin-arm64-cgo), func Pwrite(int, []uint8, int64) (int, error)
pkg syscall (darwin-arm64-cgo), func RawSyscall(uintptr, uintptr, uintptr, uintptr) (uintptr, uintptr, Errno)
pkg syscall (darwin-arm64-cgo), func RawSyscall6(uintptr, uintptr, uintptr, uintptr, uintptr, uintptr, uintptr) (uintptr, uintptr, Errno)
pkg syscall (darwin-arm64-cgo), func Read(int, []uint8) (int, error)
pkg syscall (darwin-arm64-cgo), func ReadDirent(int, []uint8) (int, error)
pkg syscall (darwin-arm64-cgo), func Recvfrom(int, []uint8, int) (int, Sockaddr, error)
pkg syscall (darwin-arm64-cgo), func Recvmsg(int, []uint8, []uint8, int) (int, int, int, Sockaddr, error)
pkg syscall (darwin-arm64-cgo), func Revoke(string) error
pkg syscall (darwin-arm64-cgo), func RouteRIB //deprecated
pkg syscall (darwin-arm64-cgo), func RouteRIB(int, int) ([]uint8, error)
pkg syscall (darwin-arm64-cgo), func Seek(int, int64, int) (int64, error)
pkg syscall (darwin-arm64-cgo), func Select(int, *FdSet, *FdSet, *FdSet, *Timeval) error
pkg syscall (darwin-arm64-cgo), func Sendfile(int, int, *int64, int) (int, error)
pkg syscall (darwin-arm64-cgo), func Sendmsg(int, []uint8, []uint8, Sockaddr, int) error
pkg syscall (darwin-arm64-cgo), func SendmsgN(int, []uint8, []uint8, Sockaddr, int) (int, error)
pkg syscall (darwin-arm64-cgo), func Sendto(int, []uint8, int, Sockaddr) error
pkg syscall (darwin-arm64-cgo), func SetBpf //deprecated
pkg syscall (darwin-arm64-cgo), func SetBpf(int, []BpfInsn) error
pkg syscall (darwin-arm64-cgo), func SetBpfBuflen //deprecated
pkg syscall (darwin-arm64-cgo), func SetBpfBuflen(int, int) (int, error)
pkg syscall (darwin-arm64-cgo), func SetBpfDatalink //deprecated
pkg syscall (darwin-arm64-cgo), func SetBpfDatalink(int, int) (int, error)
pkg syscall (darwin-arm64-cgo), func SetBpfHeadercmpl //deprecated
pkg syscall (darwin-arm64-cgo), func SetBpfHeadercmpl(int, int) error
pkg syscall (darwin-arm64-cgo), func SetBpfImmediate //deprecated
pkg syscall (darwin-arm64-cgo), func SetBpfImmediate(int, int) error
pkg syscall (darwin-arm64-cgo), func SetBpfInterface //deprecated
pkg syscall (darwin-arm64-cgo), func SetBpfInterface(int, string) error
pkg syscall (darwin-arm64-cgo), func SetBpfPromisc //deprecated
pkg syscall (darwin-arm64-cgo), func SetBpfPromisc(int, int) error
pkg syscall (darwin-arm64-cgo), func SetBpfTimeout //deprecated
pkg syscall (darwin-arm64-cgo), func SetBpfTimeout(int, *Timeval) error
pkg syscall (darwin-arm64-cgo), func SetKevent(*Kevent_t, int, int, int)
pkg syscall (darwin-arm64-cgo), func SetNonblock(int, bool) error
pkg syscall (darwin-arm64-cgo), func Setegid(int) error
pkg syscall (darwin-arm64-cgo), func Seteuid(int) error
pkg syscall (darwin-arm64-cgo), func Setgid(int) error
pkg syscall (darwin-arm64-cgo), func Setgroups([]int) error
pkg syscall (darwin-arm64-cgo), func Setlogin(string) error
pkg syscall (darwin-arm64-cgo), func Setpgid(int, int) error
pkg syscall (darwin-arm64-cgo), func Setpriority(int, int, int) error
pkg syscall (darwin-arm64-cgo), func Setprivexec(int) error
pkg syscall (darwin-arm64-cgo), func Setregid(int, int) error
pkg syscall (darwin-arm64-cgo), func Setreuid(int, int) error
pkg syscall (darwin-arm64-cgo), func Setrlimit(int, *Rlimit) error
pkg syscall (darwin-arm64-cgo), func Setsid() (int, error)
pkg syscall (darwin-arm64-cgo), func SetsockoptByte(int, int, int, uint8) error
pkg syscall (darwin-arm64-cgo), func SetsockoptICMPv6Filter(int, int, int, *ICMPv6Filter) error
pkg syscall (darwin-arm64-cgo), func SetsockoptIPMreq(int, int, int, *IPMreq) error
pkg syscall (darwin-arm64-cgo), func SetsockoptIPv6Mreq(int, int, int, *IPv6Mreq) error
pkg syscall (darwin-arm64-cgo), func SetsockoptInet4Addr(int, int, int, [4]uint8) error
pkg syscall (darwin-arm64-cgo), func SetsockoptInt(int, int, int, int) error
pkg syscall (darwin-arm64-cgo), func SetsockoptLinger(int, int, int, *Linger) error
pkg syscall (darwin-arm64-cgo), func SetsockoptString(int, int, int, string) error
pkg syscall (darwin-arm64-cgo), func SetsockoptTimeval(int, int, int, *Timeval) error
pkg syscall (darwin-arm64-cgo), func Settimeofday(*Timeval) error
pkg syscall (darwin-arm64-cgo), func Setuid(int) error
pkg syscall (darwin-arm64-cgo), func Shutdown(int, int) error
pkg syscall (darwin-arm64-cgo), func SlicePtrFromStrings([]string) ([]*uint8, error)
pkg syscall (darwin-arm64-cgo), func Socket(int, int, int) (int, error)
pkg syscall (darwin-arm64-cgo), func Socketpair(int, int, int) ([2]int, error)
pkg syscall (darwin-arm64-cgo), func Stat(string, *Stat_t) error
pkg syscall (darwin-arm64-cgo), func Statfs(string, *Statfs_t) error
pkg syscall (darwin-arm64-cgo), func StringSlicePtr //deprecated
pkg syscall (darwin-arm64-cgo), func StringSlicePtr([]string) []*uint8
pkg syscall (darwin-arm64-cgo), func Sync() error
pkg syscall (darwin-arm64-cgo), func Syscall(uintptr, uintptr, uintptr, uintptr) (uintptr, uintptr, Errno)
pkg syscall (darwin-arm64-cgo), func Syscall6(uintptr, uintptr, uintptr, uintptr, uintptr, uintptr, uintptr) (uintptr, uintptr, Errno)
pkg syscall (darwin-arm64-cgo), func Syscall9(uintptr, uintptr, uintptr, uintptr, uintptr, uintptr, uintptr, uintptr, uintptr, uintptr) (uintptr, uintptr, Errno)
pkg syscall (darwin-arm64-cgo), func Sysctl(string) (string, error)
pkg syscall (darwin-arm64-cgo), func SysctlUint32(string) (uint32, error)
pkg syscall (darwin-arm64-cgo), func TimevalToNsec(Timeval) int64
pkg syscall (darwin-arm64-cgo), func Truncate(string, int64) error
pkg syscall (darwin-arm64-cgo), func Umask(int) int
pkg syscall (darwin-arm64-cgo), func Undelete(string) error
pkg syscall (darwin-arm64-cgo), func UnixRights(...int) []uint8
pkg syscall (darwin-arm64-cgo), func Unmount(string, int) error
pkg syscall (darwin-arm64-cgo), func Wait4(int, *WaitStatus, int, *Rusage) (int, error)
pkg syscall (darwin-arm64-cgo), func Write(int, []uint8) (int, error)
pkg syscall (darwin-arm64-cgo), method (*Cmsghdr) SetLen(int)
pkg syscall (darwin-arm64-cgo), method (*Iovec) SetLen(int)
pkg syscall (darwin-arm64-cgo), method (*Msghdr) SetControllen(int)
pkg syscall (darwin-arm64-cgo), type BpfHdr struct
pkg syscall (darwin-arm64-cgo), type BpfHdr struct, Caplen uint32
pkg syscall (darwin-arm64-cgo), type BpfHdr struct, Datalen uint32
pkg syscall (darwin-arm64-cgo), type BpfHdr struct, Hdrlen uint16
pkg syscall (darwin-arm64-cgo), type BpfHdr struct, Pad_cgo_0 [2]uint8
pkg syscall (darwin-arm64-cgo), type BpfHdr struct, Tstamp Timeval32
pkg syscall (darwin-arm64-cgo), type BpfInsn struct
pkg syscall (darwin-arm64-cgo), type BpfInsn struct, Code uint16
pkg syscall (darwin-arm64-cgo), type BpfInsn struct, Jf uint8
pkg syscall (darwin-arm64-cgo), type BpfInsn struct, Jt uint8
pkg syscall (darwin-arm64-cgo), type BpfInsn struct, K uint32
pkg syscall (darwin-arm64-cgo), type BpfProgram struct
pkg syscall (darwin-arm64-cgo), type BpfProgram struct, Insns *BpfInsn
pkg syscall (darwin-arm64-cgo), type BpfProgram struct, Len uint32
pkg syscall (darwin-arm64-cgo), type BpfProgram struct, Pad_cgo_0 [4]uint8
pkg syscall (darwin-arm64-cgo), type BpfStat struct
pkg syscall (darwin-arm64-cgo), type BpfStat struct, Drop uint32
pkg syscall (darwin-arm64-cgo), type BpfStat struct, Recv uint32
pkg syscall (darwin-arm64-cgo), type BpfVersion struct
pkg syscall (darwin-arm64-cgo), type BpfVersion struct, Major uint16
pkg syscall (darwin-arm64-cgo), type BpfVersion struct, Minor uint16
pkg syscall (darwin-arm64-cgo), type Cmsghdr struct
pkg syscall (darwin-arm64-cgo), type Cmsghdr struct, Len uint32
pkg syscall (darwin-arm64-cgo), type Cmsghdr struct, Level int32
pkg syscall (darwin-arm64-cgo), type Cmsghdr struct, Type int32
pkg syscall (darwin-arm64-cgo), type Credential struct
pkg syscall (darwin-arm64-cgo), type Credential struct, Gid uint32
pkg syscall (darwin-arm64-cgo), type Credential struct, Groups []uint32
pkg syscall (darwin-arm64-cgo), type Credential struct, NoSetGroups bool
pkg syscall (darwin-arm64-cgo), type Credential struct, Uid uint32
pkg syscall (darwin-arm64-cgo), type Dirent struct
pkg syscall (darwin-arm64-cgo), type Dirent struct, Ino uint64
pkg syscall (darwin-arm64-cgo), type Dirent struct, Name [1024]int8
pkg syscall (darwin-arm64-cgo), type Dirent struct, Namlen uint16
pkg syscall (darwin-arm64-cgo), type Dirent struct, Pad_cgo_0 [3]uint8
pkg syscall (darwin-arm64-cgo), type Dirent struct, Reclen uint16
pkg syscall (darwin-arm64-cgo), type Dirent struct, Seekoff uint64
pkg syscall (darwin-arm64-cgo), type Dirent struct, Type uint8
pkg syscall (darwin-arm64-cgo), type Fbootstraptransfer_t struct
pkg syscall (darwin-arm64-cgo), type Fbootstraptransfer_t struct, Buffer *uint8
pkg syscall (darwin-arm64-cgo), type Fbootstraptransfer_t struct, Length uint64
pkg syscall (darwin-arm64-cgo), type Fbootstraptransfer_t struct, Offset int64
pkg syscall (darwin-arm64-cgo), type FdSet struct
pkg syscall (darwin-arm64-cgo), type FdSet struct, Bits [32]int32
pkg syscall (darwin-arm64-cgo), type Flock_t struct
pkg syscall (darwin-arm64-cgo), type Flock_t struct, Len int64
pkg syscall (darwin-arm64-cgo), type Flock_t struct, Pid int32
pkg syscall (darwin-arm64-cgo), type Flock_t struct, Start int64
pkg syscall (darwin-arm64-cgo), type Flock_t struct, Type int16
pkg syscall (darwin-arm64-cgo), type Flock_t struct, Whence int16
pkg syscall (darwin-arm64-cgo), type Fsid struct
pkg syscall (darwin-arm64-cgo), type Fsid struct, Val [2]int32
pkg syscall (darwin-arm64-cgo), type Fstore_t struct
pkg syscall (darwin-arm64-cgo), type Fstore_t struct, Bytesalloc int64
pkg syscall (darwin-arm64-cgo), type Fstore_t struct, Flags uint32
pkg syscall (darwin-arm64-cgo), type Fstore_t struct, Length int64
pkg syscall (darwin-arm64-cgo), type Fstore_t struct, Offset int64
pkg syscall (darwin-arm64-cgo), type Fstore_t struct, Posmode int32
pkg syscall (darwin-arm64-cgo), type ICMPv6Filter struct
pkg syscall (darwin-arm64-cgo), type ICMPv6Filter struct, Filt [8]uint32
pkg syscall (darwin-arm64-cgo), type IPv6MTUInfo struct
pkg syscall (darwin-arm64-cgo), type IPv6MTUInfo struct, Addr RawSockaddrInet6
pkg syscall (darwin-arm64-cgo), type IPv6MTUInfo struct, Mtu uint32
pkg syscall (darwin-arm64-cgo), type IfData struct
pkg syscall (darwin-arm64-cgo), type IfData struct, Addrlen uint8
pkg syscall (darwin-arm64-cgo), type IfData struct, Baudrate uint32
pkg syscall (darwin-arm64-cgo), type IfData struct, Collisions uint32
pkg syscall (darwin-arm64-cgo), type IfData struct, Hdrlen uint8
pkg syscall (darwin-arm64-cgo), type IfData struct, Hwassist uint32
pkg syscall (darwin-arm64-cgo), type IfData struct, Ibytes uint32
pkg syscall (darwin-arm64-cgo), type IfData struct, Ierrors uint32
pkg syscall (darwin-arm64-cgo), type IfData struct, Imcasts uint32
pkg syscall (darwin-arm64-cgo), type IfData struct, Ipackets uint32
pkg syscall (darwin-arm64-cgo), type IfData struct, Iqdrops uint32
pkg syscall (darwin-arm64-cgo), type IfData struct, Lastchange Timeval32
pkg syscall (darwin-arm64-cgo), type IfData struct, Metric uint32
pkg syscall (darwin-arm64-cgo), type IfData struct, Mtu uint32
pkg syscall (darwin-arm64-cgo), type IfData struct, Noproto uint32
pkg syscall (darwin-arm64-cgo), type IfData struct, Obytes uint32
pkg syscall (darwin-arm64-cgo), type IfData struct, Oerrors uint32
pkg syscall (darwin-arm64-cgo), type IfData struct, Omcasts uint32
pkg syscall (darwin-arm64-cgo), type IfData struct, Opackets uint32
pkg syscall (darwin-arm64-cgo), type IfData struct, Physical uint8
pkg syscall (darwin-arm64-cgo), type IfData struct, Recvquota uint8
pkg syscall (darwin-arm64-cgo), type IfData struct, Recvtiming uint32
pkg syscall (darwin-arm64-cgo), type IfData struct, Reserved1 uint32
pkg syscall (darwin-arm64-cgo), type IfData struct, Reserved2 uint32
pkg syscall (darwin-arm64-cgo), type IfData struct, Type uint8
pkg syscall (darwin-arm64-cgo), type IfData struct, Typelen uint8
pkg syscall (darwin-arm64-cgo), type IfData struct, Unused1 uint8
pkg syscall (darwin-arm64-cgo), type IfData struct, Unused2 uint32
pkg syscall (darwin-arm64-cgo), type IfData struct, Xmitquota uint8
pkg syscall (darwin-arm64-cgo), type IfData struct, Xmittiming uint32
pkg syscall (darwin-arm64-cgo), type IfMsghdr struct
pkg syscall (darwin-arm64-cgo), type IfMsghdr struct, Addrs int32
pkg syscall (darwin-arm64-cgo), type IfMsghdr struct, Data IfData
pkg syscall (darwin-arm64-cgo), type IfMsghdr struct, Flags int32
pkg syscall (darwin-arm64-cgo), type IfMsghdr struct, Index uint16
pkg syscall (darwin-arm64-cgo), type IfMsghdr struct, Msglen uint16
pkg syscall (darwin-arm64-cgo), type IfMsghdr struct, Pad_cgo_0 [2]uint8
pkg syscall (darwin-arm64-cgo), type IfMsghdr struct, Type uint8
pkg syscall (darwin-arm64-cgo), type IfMsghdr struct, Version uint8
pkg syscall (darwin-arm64-cgo), type IfaMsghdr struct
pkg syscall (darwin-arm64-cgo), type IfaMsghdr struct, Addrs int32
pkg syscall (darwin-arm64-cgo), type IfaMsghdr struct, Flags int32
pkg syscall (darwin-arm64-cgo), type IfaMsghdr struct, Index uint16
pkg syscall (darwin-arm64-cgo), type IfaMsghdr struct, Metric int32
pkg syscall (darwin-arm64-cgo), type IfaMsghdr struct, Msglen uint16
pkg syscall (darwin-arm64-cgo), type IfaMsghdr struct, Pad_cgo_0 [2]uint8
pkg syscall (darwin-arm64-cgo), type IfaMsghdr struct, Type uint8
pkg syscall (darwin-arm64-cgo), type IfaMsghdr struct, Version uint8
pkg syscall (darwin-arm64-cgo), type IfmaMsghdr struct
pkg syscall (darwin-arm64-cgo), type IfmaMsghdr struct, Addrs int32
pkg syscall (darwin-arm64-cgo), type IfmaMsghdr struct, Flags int32
pkg syscall (darwin-arm64-cgo), type IfmaMsghdr struct, Index uint16
pkg syscall (darwin-arm64-cgo), type IfmaMsghdr struct, Msglen uint16
pkg syscall (darwin-arm64-cgo), type IfmaMsghdr struct, Pad_cgo_0 [2]uint8
pkg syscall (darwin-arm64-cgo), type IfmaMsghdr struct, Type uint8
pkg syscall (darwin-arm64-cgo), type IfmaMsghdr struct, Version uint8
pkg syscall (darwin-arm64-cgo), type IfmaMsghdr2 struct
pkg syscall (darwin-arm64-cgo), type IfmaMsghdr2 struct, Addrs int32
pkg syscall (darwin-arm64-cgo), type IfmaMsghdr2 struct, Flags int32
pkg syscall (darwin-arm64-cgo), type IfmaMsghdr2 struct, Index uint16
pkg syscall (darwin-arm64-cgo), type IfmaMsghdr2 struct, Msglen uint16
pkg syscall (darwin-arm64-cgo), type IfmaMsghdr2 struct, Pad_cgo_0 [2]uint8
pkg syscall (darwin-arm64-cgo), type IfmaMsghdr2 struct, Refcount int32
pkg syscall (darwin-arm64-cgo), type IfmaMsghdr2 struct, Type uint8
pkg syscall (darwin-arm64-cgo), type IfmaMsghdr2 struct, Version uint8
pkg syscall (darwin-arm64-cgo), type Inet4Pktinfo struct
pkg syscall (darwin-arm64-cgo), type Inet4Pktinfo struct, Addr [4]uint8
pkg syscall (darwin-arm64-cgo), type Inet4Pktinfo struct, Ifindex uint32
pkg syscall (darwin-arm64-cgo), type Inet4Pktinfo struct, Spec_dst [4]uint8
pkg syscall (darwin-arm64-cgo), type Inet6Pktinfo struct
pkg syscall (darwin-arm64-cgo), type Inet6Pktinfo struct, Addr [16]uint8
pkg syscall (darwin-arm64-cgo), type Inet6Pktinfo struct, Ifindex uint32
pkg syscall (darwin-arm64-cgo), type InterfaceAddrMessage //deprecated
pkg syscall (darwin-arm64-cgo), type InterfaceAddrMessage struct
pkg syscall (darwin-arm64-cgo), type InterfaceAddrMessage struct, Data []uint8
pkg syscall (darwin-arm64-cgo), type InterfaceAddrMessage struct, Header IfaMsghdr
pkg syscall (darwin-arm64-cgo), type InterfaceMessage //deprecated
pkg syscall (darwin-arm64-cgo), type InterfaceMessage struct
pkg syscall (darwin-arm64-cgo), type InterfaceMessage struct, Data []uint8
pkg syscall (darwin-arm64-cgo), type InterfaceMessage struct, Header IfMsghdr
pkg syscall (darwin-arm64-cgo), type InterfaceMulticastAddrMessage //deprecated
pkg syscall (darwin-arm64-cgo), type InterfaceMulticastAddrMessage struct
pkg syscall (darwin-arm64-cgo), type InterfaceMulticastAddrMessage struct, Data []uint8
pkg syscall (darwin-arm64-cgo), type InterfaceMulticastAddrMessage struct, Header IfmaMsghdr2
pkg syscall (darwin-arm64-cgo), type Iovec struct
pkg syscall (darwin-arm64-cgo), type Iovec struct, Base *uint8
pkg syscall (darwin-arm64-cgo), type Iovec struct, Len uint64
pkg syscall (darwin-arm64-cgo), type Kevent_t struct
pkg syscall (darwin-arm64-cgo), type Kevent_t struct, Data int64
pkg syscall (darwin-arm64-cgo), type Kevent_t struct, Fflags uint32
pkg syscall (darwin-arm64-cgo), type Kevent_t struct, Filter int16
pkg syscall (darwin-arm64-cgo), type Kevent_t struct, Flags uint16
pkg syscall (darwin-arm64-cgo), type Kevent_t struct, Ident uint64
pkg syscall (darwin-arm64-cgo), type Kevent_t struct, Udata *uint8
pkg syscall (darwin-arm64-cgo), type Log2phys_t struct
pkg syscall (darwin-arm64-cgo), type Log2phys_t struct, Contigbytes int64
pkg syscall (darwin-arm64-cgo), type Log2phys_t struct, Devoffset int64
pkg syscall (darwin-arm64-cgo), type Log2phys_t struct, Flags uint32
pkg syscall (darwin-arm64-cgo), type Msghdr struct
pkg syscall (darwin-arm64-cgo), type Msghdr struct, Control *uint8
pkg syscall (darwin-arm64-cgo), type Msghdr struct, Controllen uint32
pkg syscall (darwin-arm64-cgo), type Msghdr struct, Flags int32
pkg syscall (darwin-arm64-cgo), type Msghdr struct, Iov *Iovec
pkg syscall (darwin-arm64-cgo), type Msghdr struct, Iovlen int32
pkg syscall (darwin-arm64-cgo), type Msghdr struct, Name *uint8
pkg syscall (darwin-arm64-cgo), type Msghdr struct, Namelen uint32
pkg syscall (darwin-arm64-cgo), type Msghdr struct, Pad_cgo_0 [4]uint8
pkg syscall (darwin-arm64-cgo), type Msghdr struct, Pad_cgo_1 [4]uint8
pkg syscall (darwin-arm64-cgo), type Radvisory_t struct
pkg syscall (darwin-arm64-cgo), type Radvisory_t struct, Count int32
pkg syscall (darwin-arm64-cgo), type Radvisory_t struct, Offset int64
pkg syscall (darwin-arm64-cgo), type Radvisory_t struct, Pad_cgo_0 [4]uint8
pkg syscall (darwin-arm64-cgo), type RawSockaddr struct, Data [14]int8
pkg syscall (darwin-arm64-cgo), type RawSockaddr struct, Family uint8
pkg syscall (darwin-arm64-cgo), type RawSockaddr struct, Len uint8
pkg syscall (darwin-arm64-cgo), type RawSockaddrAny struct, Pad [92]int8
pkg syscall (darwin-arm64-cgo), type RawSockaddrDatalink struct
pkg syscall (darwin-arm64-cgo), type RawSockaddrDatalink struct, Alen uint8
pkg syscall (darwin-arm64-cgo), type RawSockaddrDatalink struct, Data [12]int8
pkg syscall (darwin-arm64-cgo), type RawSockaddrDatalink struct, Family uint8
pkg syscall (darwin-arm64-cgo), type RawSockaddrDatalink struct, Index uint16
pkg syscall (darwin-arm64-cgo), type RawSockaddrDatalink struct, Len uint8
pkg syscall (darwin-arm64-cgo), type RawSockaddrDatalink struct, Nlen uint8
pkg syscall (darwin-arm64-cgo), type RawSockaddrDatalink struct, Slen uint8
pkg syscall (darwin-arm64-cgo), type RawSockaddrDatalink struct, Type uint8
pkg syscall (darwin-arm64-cgo), type RawSockaddrInet4 struct, Family uint8
pkg syscall (darwin-arm64-cgo), type RawSockaddrInet4 struct, Len uint8
pkg syscall (darwin-arm64-cgo), type RawSockaddrInet4 struct, Zero [8]int8
pkg syscall (darwin-arm64-cgo), type RawSockaddrInet6 struct, Family uint8
pkg syscall (darwin-arm64-cgo), type RawSockaddrInet6 struct, Len uint8
pkg syscall (darwin-arm64-cgo), type RawSockaddrUnix struct, Family uint8
pkg syscall (darwin-arm64-cgo), type RawSockaddrUnix struct, Len uint8
pkg syscall (darwin-arm64-cgo), type RawSockaddrUnix struct, Path [104]int8
pkg syscall (darwin-arm64-cgo), type Rlimit struct
pkg syscall (darwin-arm64-cgo), type Rlimit struct, Cur uint64
pkg syscall (darwin-arm64-cgo), type Rlimit struct, Max uint64
pkg syscall (darwin-arm64-cgo), type RouteMessage //deprecated
pkg syscall (darwin-arm64-cgo), type RouteMessage struct
pkg syscall (darwin-arm64-cgo), type RouteMessage struct, Data []uint8
pkg syscall (darwin-arm64-cgo), type RouteMessage struct, Header RtMsghdr
pkg syscall (darwin-arm64-cgo), type RoutingMessage //deprecated
pkg syscall (darwin-arm64-cgo), type RoutingMessage interface, unexported methods
pkg syscall (darwin-arm64-cgo), type RtMetrics struct
pkg syscall (darwin-arm64-cgo), type RtMetrics struct, Expire int32
pkg syscall (darwin-arm64-cgo), type RtMetrics struct, Filler [4]uint32
pkg syscall (darwin-arm64-cgo), type RtMetrics struct, Hopcount uint32
pkg syscall (darwin-arm64-cgo), type RtMetrics struct, Locks uint32
pkg syscall (darwin-arm64-cgo), type RtMetrics struct, Mtu uint32
pkg syscall (darwin-arm64-cgo), type RtMetrics struct, Pksent uint32
pkg syscall (darwin-arm64-cgo), type RtMetrics struct, Recvpipe uint32
pkg syscall (darwin-arm64-cgo), type RtMetrics struct, Rtt uint32
pkg syscall (darwin-arm64-cgo), type RtMetrics struct, Rttvar uint32
pkg syscall (darwin-arm64-cgo), type RtMetrics struct, Sendpipe uint32
pkg syscall (darwin-arm64-cgo), type RtMetrics struct, Ssthresh uint32
pkg syscall (darwin-arm64-cgo), type RtMsghdr struct
pkg syscall (darwin-arm64-cgo), type RtMsghdr struct, Addrs int32
pkg syscall (darwin-arm64-cgo), type RtMsghdr struct, Errno int32
pkg syscall (darwin-arm64-cgo), type RtMsghdr struct, Flags int32
pkg syscall (darwin-arm64-cgo), type RtMsghdr struct, Index uint16
pkg syscall (darwin-arm64-cgo), type RtMsghdr struct, Inits uint32
pkg syscall (darwin-arm64-cgo), type RtMsghdr struct, Msglen uint16
pkg syscall (darwin-arm64-cgo), type RtMsghdr struct, Pad_cgo_0 [2]uint8
pkg syscall (darwin-arm64-cgo), type RtMsghdr struct, Pid int32
pkg syscall (darwin-arm64-cgo), type RtMsghdr struct, Rmx RtMetrics
pkg syscall (darwin-arm64-cgo), type RtMsghdr struct, Seq int32
pkg syscall (darwin-arm64-cgo), type RtMsghdr struct, Type uint8
pkg syscall (darwin-arm64-cgo), type RtMsghdr struct, Use int32
pkg syscall (darwin-arm64-cgo), type RtMsghdr struct, Version uint8
pkg syscall (darwin-arm64-cgo), type Rusage struct, Idrss int64
pkg syscall (darwin-arm64-cgo), type Rusage struct, Inblock int64
pkg syscall (darwin-arm64-cgo), type Rusage struct, Isrss int64
pkg syscall (darwin-arm64-cgo), type Rusage struct, Ixrss int64
pkg syscall (darwin-arm64-cgo), type Rusage struct, Majflt int64
pkg syscall (darwin-arm64-cgo), type Rusage struct, Maxrss int64
pkg syscall (darwin-arm64-cgo), type Rusage struct, Minflt int64
pkg syscall (darwin-arm64-cgo), type Rusage struct, Msgrcv int64
pkg syscall (darwin-arm64-cgo), type Rusage struct, Msgsnd int64
pkg syscall (darwin-arm64-cgo), type Rusage struct, Nivcsw int64
pkg syscall (darwin-arm64-cgo), type Rusage struct, Nsignals int64
pkg syscall (darwin-arm64-cgo), type Rusage struct, Nswap int64
pkg syscall (darwin-arm64-cgo), type Rusage struct, Nvcsw int64
pkg syscall (darwin-arm64-cgo), type Rusage struct, Oublock int64
pkg syscall (darwin-arm64-cgo), type Rusage struct, Stime Timeval
pkg syscall (darwin-arm64-cgo), type Rusage struct, Utime Timeval
pkg syscall (darwin-arm64-cgo), type SockaddrDatalink struct
pkg syscall (darwin-arm64-cgo), type SockaddrDatalink struct, Alen uint8
pkg syscall (darwin-arm64-cgo), type SockaddrDatalink struct, Data [12]int8
pkg syscall (darwin-arm64-cgo), type SockaddrDatalink struct, Family uint8
pkg syscall (darwin-arm64-cgo), type SockaddrDatalink struct, Index uint16
pkg syscall (darwin-arm64-cgo), type SockaddrDatalink struct, Len uint8
pkg syscall (darwin-arm64-cgo), type SockaddrDatalink struct, Nlen uint8
pkg syscall (darwin-arm64-cgo), type SockaddrDatalink struct, Slen uint8
pkg syscall (darwin-arm64-cgo), type SockaddrDatalink struct, Type uint8
pkg syscall (darwin-arm64-cgo), type SocketControlMessage struct
pkg syscall (darwin-arm64-cgo), type SocketControlMessage struct, Data []uint8
pkg syscall (darwin-arm64-cgo), type SocketControlMessage struct, Header Cmsghdr
pkg syscall (darwin-arm64-cgo), type Stat_t struct
pkg syscall (darwin-arm64-cgo), type Stat_t struct, Atimespec Timespec
pkg syscall (darwin-arm64-cgo), type Stat_t struct, Birthtimespec Timespec
pkg syscall (darwin-arm64-cgo), type Stat_t struct, Blksize int32
pkg syscall (darwin-arm64-cgo), type Stat_t struct, Blocks int64
pkg syscall (darwin-arm64-cgo), type Stat_t struct, Ctimespec Timespec
pkg syscall (darwin-arm64-cgo), type Stat_t struct, Dev int32
pkg syscall (darwin-arm64-cgo), type Stat_t struct, Flags uint32
pkg syscall (darwin-arm64-cgo), type Stat_t struct, Gen uint32
pkg syscall (darwin-arm64-cgo), type Stat_t struct, Gid uint32
pkg syscall (darwin-arm64-cgo), type Stat_t struct, Ino uint64
pkg syscall (darwin-arm64-cgo), type Stat_t struct, Lspare int32
pkg syscall (darwin-arm64-cgo), type Stat_t struct, Mode uint16
pkg syscall (darwin-arm64-cgo), type Stat_t struct, Mtimespec Timespec
pkg syscall (darwin-arm64-cgo), type Stat_t struct, Nlink uint16
pkg syscall (darwin-arm64-cgo), type Stat_t struct, Pad_cgo_0 [4]uint8
pkg syscall (darwin-arm64-cgo), type Stat_t struct, Qspare [2]int64
pkg syscall (darwin-arm64-cgo), type Stat_t struct, Rdev int32
pkg syscall (darwin-arm64-cgo), type Stat_t struct, Size int64
pkg syscall (darwin-arm64-cgo), type Stat_t struct, Uid uint32
pkg syscall (darwin-arm64-cgo), type Statfs_t struct
pkg syscall (darwin-arm64-cgo), type Statfs_t struct, Bavail uint64
pkg syscall (darwin-arm64-cgo), type Statfs_t struct, Bfree uint64
pkg syscall (darwin-arm64-cgo), type Statfs_t struct, Blocks uint64
pkg syscall (darwin-arm64-cgo), type Statfs_t struct, Bsize uint32
pkg syscall (darwin-arm64-cgo), type Statfs_t struct, Ffree uint64
pkg syscall (darwin-arm64-cgo), type Statfs_t struct, Files uint64
pkg syscall (darwin-arm64-cgo), type Statfs_t struct, Flags uint32
pkg syscall (darwin-arm64-cgo), type Statfs_t struct, Fsid Fsid
pkg syscall (darwin-arm64-cgo), type Statfs_t struct, Fssubtype uint32
pkg syscall (darwin-arm64-cgo), type Statfs_t struct, Fstypename [16]int8
pkg syscall (darwin-arm64-cgo), type Statfs_t struct, Iosize int32
pkg syscall (darwin-arm64-cgo), type Statfs_t struct, Mntfromname [1024]int8
pkg syscall (darwin-arm64-cgo), type Statfs_t struct, Mntonname [1024]int8
pkg syscall (darwin-arm64-cgo), type Statfs_t struct, Owner uint32
pkg syscall (darwin-arm64-cgo), type Statfs_t struct, Reserved [8]uint32
pkg syscall (darwin-arm64-cgo), type Statfs_t struct, Type uint32
pkg syscall (darwin-arm64-cgo), type SysProcAttr struct, Chroot string
pkg syscall (darwin-arm64-cgo), type SysProcAttr struct, Credential *Credential
pkg syscall (darwin-arm64-cgo), type SysProcAttr struct, Ctty int
pkg syscall (darwin-arm64-cgo), type SysProcAttr struct, Foreground bool
pkg syscall (darwin-arm64-cgo), type SysProcAttr struct, Noctty bool
pkg syscall (darwin-arm64-cgo), type SysProcAttr struct, Pgid int
pkg syscall (darwin-arm64-cgo), type SysProcAttr struct, Ptrace bool
pkg syscall (darwin-arm64-cgo), type SysProcAttr struct, Setctty bool
pkg syscall (darwin-arm64-cgo), type SysProcAttr struct, Setpgid bool
pkg syscall (darwin-arm64-cgo), type SysProcAttr struct, Setsid bool
pkg syscall (darwin-arm64-cgo), type Termios struct
pkg syscall (darwin-arm64-cgo), type Termios struct, Cc [20]uint8
pkg syscall (darwin-arm64-cgo), type Termios struct, Cflag uint64
pkg syscall (darwin-arm64-cgo), type Termios struct, Iflag uint64
pkg syscall (darwin-arm64-cgo), type Termios struct, Ispeed uint64
pkg syscall (darwin-arm64-cgo), type Termios struct, Lflag uint64
pkg syscall (darwin-arm64-cgo), type Termios struct, Oflag uint64
pkg syscall (darwin-arm64-cgo), type Termios struct, Ospeed uint64
pkg syscall (darwin-arm64-cgo), type Termios struct, Pad_cgo_0 [4]uint8
pkg syscall (darwin-arm64-cgo), type Timespec struct, Nsec int64
pkg syscall (darwin-arm64-cgo), type Timespec struct, Sec int64
pkg syscall (darwin-arm64-cgo), type Timeval struct, Pad_cgo_0 [4]uint8
pkg syscall (darwin-arm64-cgo), type Timeval struct, Sec int64
pkg syscall (darwin-arm64-cgo), type Timeval struct, Usec int32
pkg syscall (darwin-arm64-cgo), type Timeval32 struct
pkg syscall (darwin-arm64-cgo), type Timeval32 struct, Sec int32
pkg syscall (darwin-arm64-cgo), type Timeval32 struct, Usec int32
pkg syscall (darwin-arm64-cgo), type WaitStatus uint32
pkg syscall (darwin-arm64-cgo), var Stderr int
pkg syscall (darwin-arm64-cgo), var Stdin int
pkg syscall (darwin-arm64-cgo), var Stdout int
