rsc.io/quote@v1.0.0

-- .mod --
module "rsc.io/quote"
-- .info --
{"Version":"v1.0.0","Name":"f488df80bcdbd3e5bafdc24ad7d1e79e83edd7e6","Short":"f488df80bcdb","Time":"2018-02-14T00:45:20Z"}
-- go.mod --
module "rsc.io/quote"
-- quote.go --
// Copyright 2018 The Go Authors. All rights reserved.
// Use of this source code is governed by a BSD-style
// license that can be found in the LICENSE file.

// Package quote collects pithy sayings.
package quote // import "rsc.io/quote"

// Hello returns a greeting.
func Hello() string {
	return "Hello, world."
}
-- quote_test.go --
// Copyright 2018 The Go Authors. All rights reserved.
// Use of this source code is governed by a BSD-style
// license that can be found in the LICENSE file.

package quote

import "testing"

func TestHello(t *testing.T) {
	hello := "Hello, world."
	if out := Hello(); out != hello {
		t.<PERSON><PERSON><PERSON>("Hello() = %q, want %q", out, hello)
	}
}
