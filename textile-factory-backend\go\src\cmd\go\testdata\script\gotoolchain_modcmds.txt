env TESTGO_VERSION=go1.21.0
env TESTGO_VERSION_SWITCH=switch

# If the main module's go.mod file lists a version lower than the version
# required by its dependencies, the commands that fetch and diagnose the module
# graph (such as 'go mod download' and 'go mod graph') should fail explicitly:
# they can't interpret the graph themselves, and they aren't allowed to update
# the go.mod file to record a specific, stable toolchain version that can.

! go mod download rsc.io/future@v1.0.0
stderr '^go: rsc.io/future@v1.0.0 requires go >= 1.999 \(running go 1.21.0\)'

! go mod download rsc.io/future
stderr '^go: rsc.io/future@v1.0.0 requires go >= 1.999 \(running go 1.21.0\)'

! go mod download
stderr '^go: rsc.io/future@v1.0.0: module rsc.io/future@v1.0.0 requires go >= 1.999 \(running go 1.21.0\)'

! go mod verify
stderr '^go: rsc.io/future@v1.0.0: module rsc.io/future@v1.0.0 requires go >= 1.999 \(running go 1.21.0\)'

! go mod graph
stderr '^go: rsc.io/future@v1.0.0: module rsc.io/future@v1.0.0 requires go >= 1.999 \(running go 1.21.0\)'


# 'go get' should update the main module's go.mod file to a version compatible with the
# go version required for rsc.io/future, not fail.
go get .
stderr '^go: module rsc.io/future@v1.0.0 requires go >= 1.999; switching to go1.999testmod$'
stderr '^go: upgraded go 1.21 => 1.999$'
stderr '^go: added toolchain go1.999testmod$'


# Now, the various 'go mod' subcommands should succeed.

go mod download rsc.io/future@v1.0.0
go mod download rsc.io/future
go mod download

go mod verify

go mod graph


-- go.mod --
module example

go 1.21

require rsc.io/future v1.0.0
-- example.go --
package example

import _ "rsc.io/future"
