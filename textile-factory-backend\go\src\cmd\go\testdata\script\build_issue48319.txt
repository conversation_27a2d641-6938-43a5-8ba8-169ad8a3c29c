# Regression test for https://go.dev/issue/48319:
# cgo builds should not include debug information from a stale GOROOT_FINAL.

[short] skip
[!cgo] skip

# This test is sensitive to cache invalidation,
# so use a separate build cache that we can control.
env GOCACHE=$WORK/gocache
mkdir $GOCACHE

# Build a binary using a specific value of GOROOT_FINAL.
env GOROOT_FINAL=$WORK${/}goroot1
go build -o main.exe
mv main.exe main1.exe

# Now clean the cache and build using a different GOROOT_FINAL.
# The resulting binaries should differ in their debug metadata.
go clean -cache
env GOROOT_FINAL=$WORK${/}goroot2
go build -o main.exe
mv main.exe main2.exe
! cmp -q main2.exe main1.exe

# Set GOROOT_FINAL back to the first value.
# If the build is properly reproducible, the two binaries should match.
env GOROOT_FINAL=$WORK${/}goroot1
go build -o main.exe
cmp -q main.exe main1.exe

-- go.mod --
module main

go 1.18
-- main.go --
package main

import "C"

import "runtime"

var _ C.int

func main() {
	println(runtime.GOROOT())
}
