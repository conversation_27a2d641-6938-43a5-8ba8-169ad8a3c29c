# Build something to create the executable, including several cases
[short] skip

# --------------------- clean executables -------------------------

# case1: test file-named executable 'main'
env GO111MODULE=on

! exists main$GOEXE
go build main.go
exists -exec main$GOEXE
go clean
! exists main$GOEXE

# case2: test module-named executable 'a.b.c'
! exists a.b.c$GOEXE
go build
exists -exec a.b.c$GOEXE
go clean
! exists a.b.c$GOEXE

# case3: directory-named executable 'src'
env GO111MODULE=off

! exists src$GOEXE
go build
exists -exec src$GOEXE
go clean
! exists src$GOEXE

# --------------------- clean test files -------------------------

# case1: test file-named test file
env GO111MODULE=on

! exists main.test$GOEXE
go test -c main_test.go
exists -exec main.test$GOEXE
go clean
! exists main.test$GOEXE

# case2: test module-named test file
! exists a.b.c.test$GOEXE
go test -c
exists -exec a.b.c.test$GOEXE
go clean
! exists a.b.c.test$GOEXE

# case3: test directory-based test file
env GO111MODULE=off

! exists src.test$GOEXE
go test -c
exists -exec src.test$GOEXE
go clean
! exists src.test$GOEXE

-- main.go --
package main

import "fmt"

func main() {
	fmt.Println("hello!")
}

-- main_test.go --
package main

import "testing"

func TestSomething(t *testing.T) {
}

-- go.mod --
module example.com/a.b.c/v2

go 1.12