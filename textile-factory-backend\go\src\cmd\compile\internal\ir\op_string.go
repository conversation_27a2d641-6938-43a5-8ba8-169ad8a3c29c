// Code generated by "stringer -type=Op -trimprefix=O node.go"; DO NOT EDIT.

package ir

import "strconv"

func _() {
	// An "invalid array index" compiler error signifies that the constant values have changed.
	// Re-run the stringer command to generate them again.
	var x [1]struct{}
	_ = x[OXXX-0]
	_ = x[ONAME-1]
	_ = x[ONONAME-2]
	_ = x[OTYPE-3]
	_ = x[OLITERAL-4]
	_ = x[ONIL-5]
	_ = x[OADD-6]
	_ = x[OSUB-7]
	_ = x[OOR-8]
	_ = x[OXOR-9]
	_ = x[OADDSTR-10]
	_ = x[OADDR-11]
	_ = x[OANDAND-12]
	_ = x[OAPPEND-13]
	_ = x[OBYTES2STR-14]
	_ = x[OBYTES2STRTMP-15]
	_ = x[ORUNES2STR-16]
	_ = x[OSTR2BYTES-17]
	_ = x[OSTR2BYTESTMP-18]
	_ = x[OSTR2RUNES-19]
	_ = x[OSLICE2ARR-20]
	_ = x[OSLICE2ARRPTR-21]
	_ = x[OAS-22]
	_ = x[OAS2-23]
	_ = x[OAS2DOTTYPE-24]
	_ = x[OAS2FUNC-25]
	_ = x[OAS2MAPR-26]
	_ = x[OAS2RECV-27]
	_ = x[OASOP-28]
	_ = x[OCALL-29]
	_ = x[OCALLFUNC-30]
	_ = x[OCALLMETH-31]
	_ = x[OCALLINTER-32]
	_ = x[OCAP-33]
	_ = x[OCLEAR-34]
	_ = x[OCLOSE-35]
	_ = x[OCLOSURE-36]
	_ = x[OCOMPLIT-37]
	_ = x[OMAPLIT-38]
	_ = x[OSTRUCTLIT-39]
	_ = x[OARRAYLIT-40]
	_ = x[OSLICELIT-41]
	_ = x[OPTRLIT-42]
	_ = x[OCONV-43]
	_ = x[OCONVIFACE-44]
	_ = x[OCONVIDATA-45]
	_ = x[OCONVNOP-46]
	_ = x[OCOPY-47]
	_ = x[ODCL-48]
	_ = x[ODCLFUNC-49]
	_ = x[ODCLCONST-50]
	_ = x[ODCLTYPE-51]
	_ = x[ODELETE-52]
	_ = x[ODOT-53]
	_ = x[ODOTPTR-54]
	_ = x[ODOTMETH-55]
	_ = x[ODOTINTER-56]
	_ = x[OXDOT-57]
	_ = x[ODOTTYPE-58]
	_ = x[ODOTTYPE2-59]
	_ = x[OEQ-60]
	_ = x[ONE-61]
	_ = x[OLT-62]
	_ = x[OLE-63]
	_ = x[OGE-64]
	_ = x[OGT-65]
	_ = x[ODEREF-66]
	_ = x[OINDEX-67]
	_ = x[OINDEXMAP-68]
	_ = x[OKEY-69]
	_ = x[OSTRUCTKEY-70]
	_ = x[OLEN-71]
	_ = x[OMAKE-72]
	_ = x[OMAKECHAN-73]
	_ = x[OMAKEMAP-74]
	_ = x[OMAKESLICE-75]
	_ = x[OMAKESLICECOPY-76]
	_ = x[OMUL-77]
	_ = x[ODIV-78]
	_ = x[OMOD-79]
	_ = x[OLSH-80]
	_ = x[ORSH-81]
	_ = x[OAND-82]
	_ = x[OANDNOT-83]
	_ = x[ONEW-84]
	_ = x[ONOT-85]
	_ = x[OBITNOT-86]
	_ = x[OPLUS-87]
	_ = x[ONEG-88]
	_ = x[OOROR-89]
	_ = x[OPANIC-90]
	_ = x[OPRINT-91]
	_ = x[OPRINTN-92]
	_ = x[OPAREN-93]
	_ = x[OSEND-94]
	_ = x[OSLICE-95]
	_ = x[OSLICEARR-96]
	_ = x[OSLICESTR-97]
	_ = x[OSLICE3-98]
	_ = x[OSLICE3ARR-99]
	_ = x[OSLICEHEADER-100]
	_ = x[OSTRINGHEADER-101]
	_ = x[ORECOVER-102]
	_ = x[ORECOVERFP-103]
	_ = x[ORECV-104]
	_ = x[ORUNESTR-105]
	_ = x[OSELRECV2-106]
	_ = x[OMIN-107]
	_ = x[OMAX-108]
	_ = x[OREAL-109]
	_ = x[OIMAG-110]
	_ = x[OCOMPLEX-111]
	_ = x[OALIGNOF-112]
	_ = x[OOFFSETOF-113]
	_ = x[OSIZEOF-114]
	_ = x[OUNSAFEADD-115]
	_ = x[OUNSAFESLICE-116]
	_ = x[OUNSAFESLICEDATA-117]
	_ = x[OUNSAFESTRING-118]
	_ = x[OUNSAFESTRINGDATA-119]
	_ = x[OMETHEXPR-120]
	_ = x[OMETHVALUE-121]
	_ = x[OBLOCK-122]
	_ = x[OBREAK-123]
	_ = x[OCASE-124]
	_ = x[OCONTINUE-125]
	_ = x[ODEFER-126]
	_ = x[OFALL-127]
	_ = x[OFOR-128]
	_ = x[OGOTO-129]
	_ = x[OIF-130]
	_ = x[OLABEL-131]
	_ = x[OGO-132]
	_ = x[ORANGE-133]
	_ = x[ORETURN-134]
	_ = x[OSELECT-135]
	_ = x[OSWITCH-136]
	_ = x[OTYPESW-137]
	_ = x[OFUNCINST-138]
	_ = x[OINLCALL-139]
	_ = x[OEFACE-140]
	_ = x[OITAB-141]
	_ = x[OIDATA-142]
	_ = x[OSPTR-143]
	_ = x[OCFUNC-144]
	_ = x[OCHECKNIL-145]
	_ = x[ORESULT-146]
	_ = x[OINLMARK-147]
	_ = x[OLINKSYMOFFSET-148]
	_ = x[OJUMPTABLE-149]
	_ = x[ODYNAMICDOTTYPE-150]
	_ = x[ODYNAMICDOTTYPE2-151]
	_ = x[ODYNAMICTYPE-152]
	_ = x[OTAILCALL-153]
	_ = x[OGETG-154]
	_ = x[OGETCALLERPC-155]
	_ = x[OGETCALLERSP-156]
	_ = x[OEND-157]
}

const _Op_name = "XXXNAMENONAMETYPELITERALNILADDSUBORXORADDSTRADDRANDANDAPPENDBYTES2STRBYTES2STRTMPRUNES2STRSTR2BYTESSTR2BYTESTMPSTR2RUNESSLICE2ARRSLICE2ARRPTRASAS2AS2DOTTYPEAS2FUNCAS2MAPRAS2RECVASOPCALLCALLFUNCCALLMETHCALLINTERCAPCLEARCLOSECLOSURECOMPLITMAPLITSTRUCTLITARRAYLITSLICELITPTRLITCONVCONVIFACECONVIDATACONVNOPCOPYDCLDCLFUNCDCLCONSTDCLTYPEDELETEDOTDOTPTRDOTMETHDOTINTERXDOTDOTTYPEDOTTYPE2EQNELTLEGEGTDEREFINDEXINDEXMAPKEYSTRUCTKEYLENMAKEMAKECHANMAKEMAPMAKESLICEMAKESLICECOPYMULDIVMODLSHRSHANDANDNOTNEWNOTBITNOTPLUSNEGORORPANICPRINTPRINTNPARENSENDSLICESLICEARRSLICESTRSLICE3SLICE3ARRSLICEHEADERSTRINGHEADERRECOVERRECOVERFPRECVRUNESTRSELRECV2MINMAXREALIMAGCOMPLEXALIGNOFOFFSETOFSIZEOFUNSAFEADDUNSAFESLICEUNSAFESLICEDATAUNSAFESTRINGUNSAFESTRINGDATAMETHEXPRMETHVALUEBLOCKBREAKCASECONTINUEDEFERFALLFORGOTOIFLABELGORANGERETURNSELECTSWITCHTYPESWFUNCINSTINLCALLEFACEITABIDATASPTRCFUNCCHECKNILRESULTINLMARKLINKSYMOFFSETJUMPTABLEDYNAMICDOTTYPEDYNAMICDOTTYPE2DYNAMICTYPETAILCALLGETGGETCALLERPCGETCALLERSPEND"

var _Op_index = [...]uint16{0, 3, 7, 13, 17, 24, 27, 30, 33, 35, 38, 44, 48, 54, 60, 69, 81, 90, 99, 111, 120, 129, 141, 143, 146, 156, 163, 170, 177, 181, 185, 193, 201, 210, 213, 218, 223, 230, 237, 243, 252, 260, 268, 274, 278, 287, 296, 303, 307, 310, 317, 325, 332, 338, 341, 347, 354, 362, 366, 373, 381, 383, 385, 387, 389, 391, 393, 398, 403, 411, 414, 423, 426, 430, 438, 445, 454, 467, 470, 473, 476, 479, 482, 485, 491, 494, 497, 503, 507, 510, 514, 519, 524, 530, 535, 539, 544, 552, 560, 566, 575, 586, 598, 605, 614, 618, 625, 633, 636, 639, 643, 647, 654, 661, 669, 675, 684, 695, 710, 722, 738, 746, 755, 760, 765, 769, 777, 782, 786, 789, 793, 795, 800, 802, 807, 813, 819, 825, 831, 839, 846, 851, 855, 860, 864, 869, 877, 883, 890, 903, 912, 926, 941, 952, 960, 964, 975, 986, 989}

func (i Op) String() string {
	if i >= Op(len(_Op_index)-1) {
		return "Op(" + strconv.FormatInt(int64(i), 10) + ")"
	}
	return _Op_name[_Op_index[i]:_Op_index[i+1]]
}
