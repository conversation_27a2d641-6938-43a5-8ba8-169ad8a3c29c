# This test was converted from a test in vendor_test.go (which no longer exists).
# That seems to imply that it's about vendoring semantics, but the test doesn't
# use 'go -mod=vendor' (and none of the fetched repos have vendor folders).
# The test still seems to be useful as a test of direct-mode go get.

[short] skip
[!git] skip
env GO111MODULE=off

env GOPATH=$WORK/tmp/d1
go get vcs-test.golang.org/git/modlegacy1-old.git/p1
go list -f '{{.Deps}}' vcs-test.golang.org/git/modlegacy1-old.git/p1
stdout 'new.git/p2' # old/p1 should depend on new/p2
! stdout new.git/v2/p2 # old/p1 should NOT depend on new/v2/p2
go build vcs-test.golang.org/git/modlegacy1-old.git/p1 vcs-test.golang.org/git/modlegacy1-new.git/p1
! stdout .

env GOPATH=$WORK/tmp/d2

rm $GOPATH
go get github.com/rsc/vgotest5
go get github.com/rsc/vgotest4
go get github.com/myitcv/vgo_example_compat

rm $GOPATH
go get github.com/rsc/vgotest4
go get github.com/rsc/vgotest5
go get github.com/myitcv/vgo_example_compat

rm $GOPATH
go get github.com/rsc/vgotest4 github.com/rsc/vgotest5
go get github.com/myitcv/vgo_example_compat

rm $GOPATH
go get github.com/rsc/vgotest5 github.com/rsc/vgotest4
go get github.com/myitcv/vgo_example_compat

rm $GOPATH
go get github.com/myitcv/vgo_example_compat
go get github.com/rsc/vgotest5 github.com/rsc/vgotest4

rm $GOPATH
go get github.com/myitcv/vgo_example_compat github.com/rsc/vgotest4 github.com/rsc/vgotest5

rm $GOPATH
go get github.com/myitcv/vgo_example_compat github.com/rsc/vgotest5 github.com/rsc/vgotest4

rm $GOPATH
go get github.com/rsc/vgotest4 github.com/myitcv/vgo_example_compat github.com/rsc/vgotest5

rm $GOPATH
go get github.com/rsc/vgotest4 github.com/rsc/vgotest5 github.com/myitcv/vgo_example_compat

rm $GOPATH
go get github.com/rsc/vgotest5 github.com/myitcv/vgo_example_compat github.com/rsc/vgotest4

rm $GOPATH
go get github.com/rsc/vgotest5 github.com/rsc/vgotest4 github.com/myitcv/vgo_example_compat
