/**
 * 扫码功能页面生命周期混入
 * 用于管理页面显示/隐藏时的扫码状态
 */
import scannerUtils from './scannerUtils.js'

export function useScannerLifecycle() {
  // 页面显示时激活扫码
  const onShow = () => {
    console.log('页面显示，激活扫码功能')
    scannerUtils.setPageActive(true)
  }

  // 页面隐藏时停用扫码
  const onHide = () => {
    console.log('页面隐藏，停用扫码功能')
    scannerUtils.setPageActive(false)
  }

  // 页面卸载时清理资源
  const onUnload = () => {
    console.log('页面卸载，清理扫码资源')
    scannerUtils.destroy()
  }

  return {
    onShow,
    onHide,
    onUnload
  }
}
