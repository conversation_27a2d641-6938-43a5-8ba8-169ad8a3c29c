// Copyright 2016 The Go Authors. All rights reserved.
// Use of this source code is governed by a BSD-style
// license that can be found in the LICENSE file.

package ssa

import (
	"cmd/compile/internal/types"
	"testing"
)

func TestShortCircuit(t *testing.T) {
	c := testConfig(t)

	fun := c.<PERSON>("entry",
		<PERSON>("entry",
			<PERSON><PERSON>("mem", OpInitMem, types.TypeMem, 0, nil),
			<PERSON><PERSON>("arg1", OpArg, c.config.Types.Int64, 0, nil),
			<PERSON><PERSON>("arg2", OpArg, c.config.Types.Int64, 0, nil),
			<PERSON><PERSON>("arg3", OpArg, c.config.Types.Int64, 0, nil),
			<PERSON><PERSON>("b1")),
		<PERSON>("b1",
			<PERSON><PERSON>("cmp1", OpLess64, c.config.Types.Bool, 0, nil, "arg1", "arg2"),
			If("cmp1", "b2", "b3")),
		<PERSON>("b2",
			<PERSON><PERSON>("cmp2", <PERSON><PERSON><PERSON><PERSON>, c.config.Types.Bool, 0, nil, "arg2", "arg3"),
			<PERSON><PERSON>("b3")),
		<PERSON>("b3",
			<PERSON><PERSON>("phi2", Op<PERSON>hi, c.config.Types.Bool, 0, nil, "cmp1", "cmp2"),
			If("phi2", "b4", "b5")),
		Bloc("b4",
			Valu("cmp3", OpLess64, c.config.Types.Bool, 0, nil, "arg3", "arg1"),
			Goto("b5")),
		Bloc("b5",
			Valu("phi3", OpPhi, c.config.Types.Bool, 0, nil, "phi2", "cmp3"),
			If("phi3", "b6", "b7")),
		Bloc("b6",
			Exit("mem")),
		Bloc("b7",
			Exit("mem")))

	CheckFunc(fun.f)
	shortcircuit(fun.f)
	CheckFunc(fun.f)

	for _, b := range fun.f.Blocks {
		for _, v := range b.Values {
			if v.Op == OpPhi {
				t.Errorf("phi %s remains", v)
			}
		}
	}
}
