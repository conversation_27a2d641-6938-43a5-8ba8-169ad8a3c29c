[short] skip
env GO111MODULE=on

# Set up fresh GOCACHE.
env GOCACHE=$WORK/gocache
mkdir $GOCACHE

cd $WORK
go build -o a.out

# Varying -trimpath should cause a rebuild.
go build -x -o a.out -trimpath
stderr '(compile|gccgo)( |\.exe)'
stderr 'link( |\.exe)'

# Two distinct versions of the same module with identical content should
# still be cached separately.
# Verifies golang.org/issue/35412.
go get example.com/stack@v1.0.0
go run -trimpath printstack.go
stdout '^example.com/stack@v1.0.0/stack.go$'
go get example.com/stack@v1.0.1
go run -trimpath printstack.go
stdout '^example.com/stack@v1.0.1/stack.go$'

-- $WORK/hello.go --
package main
func main() { println("hello") }

-- $WORK/printstack.go --
// +build ignore

package main

import (
	"fmt"

	"example.com/stack"
)

func main() {
	fmt.Println(stack.TopFile())
}
-- $WORK/go.mod --
module m

go 1.14
