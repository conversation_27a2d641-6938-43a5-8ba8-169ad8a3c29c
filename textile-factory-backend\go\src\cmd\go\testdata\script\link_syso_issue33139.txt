# Test that we can use the external linker with a host syso file that is
# embedded in a package, that is referenced by a Go assembly function.
# See issue 33139.

[!compiler:gc] skip
[!cgo] skip
[short] skip 'invokes system C compiler'

# External linking is not supported on linux/ppc64.
# See: https://github.com/golang/go/issues/8912
[GOOS:linux] [GOARCH:ppc64] skip

cc -c -o syso/objTestImpl.syso syso/src/objTestImpl.c
go build -ldflags='-linkmode=external' ./cmd/main.go

-- go.mod --
module m

go 1.16
-- syso/objTest.s --
#include "textflag.h"

TEXT ·ObjTest(SB), NOSPLIT, $0
	// We do not actually execute this function in the test above, thus
	// there is no stack frame setup here.
	// We only care about Go build and/or link errors when referencing
	// the objTestImpl symbol in the syso file.
	JMP objTestImpl(SB)

-- syso/pkg.go --
package syso

func ObjTest()

-- syso/src/objTestImpl.c --
void objTestImpl() { /* Empty */ }

-- cmd/main.go --
package main

import "m/syso"

func main() {
	syso.ObjTest()
}
