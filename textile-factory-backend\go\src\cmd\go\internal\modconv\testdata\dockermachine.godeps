{"ImportPath": "github.com/docker/machine", "GoVersion": "go1.4.2", "Deps": [{"ImportPath": "code.google.com/p/goauth2/oauth", "Comment": "weekly-56", "Rev": "afe77d958c701557ec5dc56f6936fcc194d15520"}, {"ImportPath": "github.com/MSOpenTech/azure-sdk-for-go", "Comment": "v1.1-17-g515f3ec", "Rev": "515f3ec74ce6a5b31e934cefae997c97bd0a1b1e"}, {"ImportPath": "github.com/cenkalti/backoff", "Rev": "9831e1e25c874e0a0601b6dc43641071414eec7a"}, {"ImportPath": "github.com/codegangsta/cli", "Comment": "1.2.0-64-ge1712f3", "Rev": "e1712f381785e32046927f64a7c86fe569203196"}, {"ImportPath": "github.com/digitalocean/godo", "Comment": "v0.5.0", "Rev": "5478aae80694de1d2d0e02c386bbedd201266234"}, {"ImportPath": "github.com/docker/docker/dockerversion", "Comment": "v1.5.0", "Rev": "a8a31eff10544860d2188dddabdee4d727545796"}, {"ImportPath": "github.com/docker/docker/engine", "Comment": "v1.5.0", "Rev": "a8a31eff10544860d2188dddabdee4d727545796"}, {"ImportPath": "github.com/docker/docker/pkg/archive", "Comment": "v1.5.0", "Rev": "a8a31eff10544860d2188dddabdee4d727545796"}, {"ImportPath": "github.com/docker/docker/pkg/fileutils", "Comment": "v1.5.0", "Rev": "a8a31eff10544860d2188dddabdee4d727545796"}, {"ImportPath": "github.com/docker/docker/pkg/ioutils", "Comment": "v1.5.0", "Rev": "a8a31eff10544860d2188dddabdee4d727545796"}, {"ImportPath": "github.com/docker/docker/pkg/mflag", "Comment": "v1.5.0", "Rev": "a8a31eff10544860d2188dddabdee4d727545796"}, {"ImportPath": "github.com/docker/docker/pkg/parsers", "Comment": "v1.5.0", "Rev": "a8a31eff10544860d2188dddabdee4d727545796"}, {"ImportPath": "github.com/docker/docker/pkg/pools", "Comment": "v1.5.0", "Rev": "a8a31eff10544860d2188dddabdee4d727545796"}, {"ImportPath": "github.com/docker/docker/pkg/promise", "Comment": "v1.5.0", "Rev": "a8a31eff10544860d2188dddabdee4d727545796"}, {"ImportPath": "github.com/docker/docker/pkg/system", "Comment": "v1.5.0", "Rev": "a8a31eff10544860d2188dddabdee4d727545796"}, {"ImportPath": "github.com/docker/docker/pkg/term", "Comment": "v1.5.0", "Rev": "a8a31eff10544860d2188dddabdee4d727545796"}, {"ImportPath": "github.com/docker/docker/pkg/timeutils", "Comment": "v1.5.0", "Rev": "a8a31eff10544860d2188dddabdee4d727545796"}, {"ImportPath": "github.com/docker/docker/pkg/units", "Comment": "v1.5.0", "Rev": "a8a31eff10544860d2188dddabdee4d727545796"}, {"ImportPath": "github.com/docker/docker/pkg/version", "Comment": "v1.5.0", "Rev": "a8a31eff10544860d2188dddabdee4d727545796"}, {"ImportPath": "github.com/docker/docker/vendor/src/code.google.com/p/go/src/pkg/archive/tar", "Comment": "v1.5.0", "Rev": "a8a31eff10544860d2188dddabdee4d727545796"}, {"ImportPath": "github.com/docker/libtrust", "Rev": "c54fbb67c1f1e68d7d6f8d2ad7c9360404616a41"}, {"ImportPath": "github.com/google/go-querystring/query", "Rev": "30f7a39f4a218feb5325f3aebc60c32a572a8274"}, {"ImportPath": "github.com/mitchellh/mapstructure", "Rev": "740c764bc6149d3f1806231418adb9f52c11bcbf"}, {"ImportPath": "github.com/rackspace/gophercloud", "Comment": "v1.0.0-558-ce0f487", "Rev": "ce0f487f6747ab43c4e4404722df25349385bebd"}, {"ImportPath": "github.com/skarademir/naturalsort", "Rev": "983d4d86054d80f91fd04dd62ec52c1d078ce403"}, {"ImportPath": "github.com/smartystreets/go-aws-auth", "Rev": "1f0db8c0ee6362470abe06a94e3385927ed72a4b"}, {"ImportPath": "github.com/stretchr/testify/assert", "Rev": "e4ec8152c15fc46bd5056ce65997a07c7d415325"}, {"ImportPath": "github.com/pyr/egoscale/src/egoscale", "Rev": "bbaa67324aeeacc90430c1fe0a9c620d3929512e"}, {"ImportPath": "github.com/tent/http-link-go", "Rev": "ac974c61c2f990f4115b119354b5e0b47550e888"}, {"ImportPath": "github.com/vmware/govcloudair", "Comment": "v0.0.2", "Rev": "66a23eaabc61518f91769939ff541886fe1dceef"}, {"ImportPath": "golang.org/x/crypto/ssh", "Rev": "1fbbd62cfec66bd39d91e97749579579d4d3037e"}, {"ImportPath": "google.golang.org/api/compute/v1", "Rev": "aa91ac681e18e52b1a0dfe29b9d8354e88c0dcf5"}, {"ImportPath": "google.golang.org/api/googleapi", "Rev": "aa91ac681e18e52b1a0dfe29b9d8354e88c0dcf5"}]}