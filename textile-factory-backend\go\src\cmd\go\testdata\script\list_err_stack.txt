
# golang.org/issue/40544: regression in error stacks for parse errors

env GO111MODULE=off
cd sandbox/foo
go list -e -json .
stdout '"sandbox/foo"'
stdout '"sandbox/bar"'
stdout '"Pos": "..(/|\\\\)bar(/|\\\\)bar.go:1:1"'
stdout '"Err": "expected ''package'', found ackage"'

env GO111MODULE=on
go list -e -json .
stdout '"sandbox/foo"'
stdout '"sandbox/bar"'
stdout '"Pos": "..(/|\\\\)bar(/|\\\\)bar.go:1:1"'
stdout '"Err": "expected ''package'', found ackage"'

-- sandbox/go.mod --
module sandbox

-- sandbox/foo/foo.go --
package pkg

import "sandbox/bar"
-- sandbox/bar/bar.go --
ackage bar