[short] skip
[!cgo] skip
[compiler:gccgo] skip # gccgo has no cover tool

# Test coverage on cgo code.

go test -short -cover cgocover
stdout  'coverage:.*[1-9][0-9.]+%'
! stderr '[^0-9]0\.0%'

-- go.mod --
module cgocover

go 1.16
-- p.go --
package p

/*
void
f(void)
{
}
*/
import "C"

var b bool

func F() {
	if b {
		for {
		}
	}
	C.f()
}
-- p_test.go --
package p

import "testing"

func TestF(t *testing.T) {
	F()
}
