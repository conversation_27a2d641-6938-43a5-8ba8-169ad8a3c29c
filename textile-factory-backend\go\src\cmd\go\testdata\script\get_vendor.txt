[short] skip
env GO111MODULE=off

cd $GOPATH/src/v
go run m.go
go test
go list -f '{{.Imports}}'
stdout 'v/vendor/vendor.org/p'
go list -f '{{.TestImports}}'
stdout 'v/vendor/vendor.org/p'
go get -d
go get -t -d

[!net:github.com] stop
[!git] stop

cd $GOPATH/src

# Update
go get 'github.com/rsc/go-get-issue-11864'
go get -u 'github.com/rsc/go-get-issue-11864'
exists github.com/rsc/go-get-issue-11864/vendor

# get -u
rm $GOPATH
mkdir $GOPATH/src
go get -u 'github.com/rsc/go-get-issue-11864'
exists github.com/rsc/go-get-issue-11864/vendor

# get -t -u
rm $GOPATH
mkdir $GOPATH/src
go get -t -u 'github.com/rsc/go-get-issue-11864/...'
exists github.com/rsc/go-get-issue-11864/vendor

# Submodules
rm $GOPATH
mkdir $GOPATH/src
go get -d 'github.com/rsc/go-get-issue-12612'
go get -u -d 'github.com/rsc/go-get-issue-12612'
exists github.com/rsc/go-get-issue-12612/vendor/golang.org/x/crypto/.git

# Bad vendor (bad/imp)
rm $GOPATH
mkdir $GOPATH/src
! go get -t -u 'github.com/rsc/go-get-issue-18219/bad/imp'
stderr 'must be imported as'
! exists github.com/rsc/go-get-issue-11864/vendor

# Bad vendor (bad/imp2)
rm $GOPATH
mkdir $GOPATH/src
! go get -t -u 'github.com/rsc/go-get-issue-18219/bad/imp2'
stderr 'must be imported as'
! exists github.com/rsc/go-get-issue-11864/vendor

# Bad vendor (bad/imp3)
rm $GOPATH
mkdir $GOPATH/src
! go get -t -u 'github.com/rsc/go-get-issue-18219/bad/imp3'
stderr 'must be imported as'
! exists github.com/rsc/go-get-issue-11864/vendor

# Bad vendor (bad/...)
rm $GOPATH
mkdir $GOPATH/src
! go get -t -u 'github.com/rsc/go-get-issue-18219/bad/...'
stderr 'must be imported as'
! exists github.com/rsc/go-get-issue-11864/vendor

-- v/m.go --
package main

import (
	"fmt"
	"vendor.org/p"
)

func main() {
	fmt.Println(p.C)
}
-- v/m_test.go --
package main
import (
	"fmt"
	"testing"
	"vendor.org/p"
)

func TestNothing(t *testing.T) {
	fmt.Println(p.C)
}
-- v/vendor/vendor.org/p/p.go --
package p
const C = 1
