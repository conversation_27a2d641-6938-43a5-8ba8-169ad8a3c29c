# go doc --help
! go doc --help
stderr 'go doc'
stderr 'go doc <pkg>'
stderr 'go doc <sym>\[\.<methodOrField>\]'
stderr 'go doc \[<pkg>\.\]<sym>\[\.<methodOrField>\]'
stderr 'go doc \[<pkg>\.\]\[<sym>\.\]<methodOrField>'
stderr 'go doc <pkg> <sym>\[\.<methodOrField>\]'

# go help doc
go help doc
stdout 'go doc'
stdout 'go doc <pkg>'
stdout 'go doc <sym>\[\.<methodOrField>\]'
stdout 'go doc \[<pkg>\.\]<sym>\[\.<methodOrField>\]'
stdout 'go doc \[<pkg>\.\]\[<sym>\.\]<methodOrField>'
stdout 'go doc <pkg> <sym>\[\.<methodOrField>\]'

# go doc <pkg>
go doc p/v2
stdout .

# go doc <pkg> <sym>
go doc p/v2 Symbol
stdout .

# go doc <pkg> <sym> <method>
! go doc p/v2 Symbol Method
stderr .

# go doc <pkg>.<sym>
go doc p/v2.Symbol
stdout .

# go doc <pkg>.<sym>.<method>
go doc p/v2.Symbol.Method
stdout .

# go doc <sym>
go doc Symbol
stdout .

# go doc <sym> <method>
! go doc Symbol Method
stderr .

# go doc <sym>.<method>
go doc Symbol.Method
stdout .

# go doc <pkg>.<method>
go doc p/v2.Method
stdout .

# go doc <pkg> <method>
go doc p/v2 Method
stdout .

# go doc <method>
go doc Method
stdout .

-- go.mod --
module p/v2

go 1.13

-- p.go --
package p

type Symbol struct{}

func (Symbol) Method() error {
	return nil
}
