Module example.com/retract/self/prerelease is a module that retracts its own
latest version and all other release version.

A pre-release version higher than the highest release version is still
available, and that should be matched by @latest.

-- .mod --
module example.com/retract/self/prerelease

go 1.15

retract v1.0.0 // bad
retract v1.9.0 // self

-- .info --
{"Version":"v1.9.0"}

-- p.go --
package p
