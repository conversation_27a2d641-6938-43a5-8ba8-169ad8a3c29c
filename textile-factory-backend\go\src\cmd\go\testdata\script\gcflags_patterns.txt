env GO111MODULE=off

[!compiler:gc] skip 'using -gcflags and -ldflags'
[short] skip

# -gcflags=-e applies to named packages, not dependencies
go build -a -n -v -gcflags=-e z1 z2
stderr 'compile.* -p z1.* -e '
stderr 'compile.* -p z2.* -e '
stderr 'compile.* -p y'
! stderr 'compile.* -p [^z].* -e '

# -gcflags can specify package=flags, and can be repeated; last match wins
go build -a -n -v -gcflags=-e -gcflags=z1=-N z1 z2
stderr 'compile.* -p z1.* -N '
! stderr 'compile.* -p z1.* -e '
! stderr 'compile.* -p z2.* -N '
stderr 'compile.* -p z2.* -e '
stderr 'compile.* -p y'
! stderr 'compile.* -p [^z].* -e '
! stderr 'compile.* -p [^z].* -N '

# -gcflags can have arbitrary spaces around the flags
go build -a -n -v -gcflags='  z1 =  	-e 	' z1
stderr 'compile.* -p z1.* -e '

# -gcflags='all=-e' should apply to all packages, even with go test
go test -a -c -n -gcflags='all=-e' z1
stderr 'compile.* -p z3.* -e '

# this particular -gcflags argument made the compiler crash
! go build -gcflags=-d=ssa/ z1
stderr 'PhaseOptions usage'

# check for valid -ldflags parameter
! go build '-ldflags="-X main.X=Hello"'
stderr 'invalid value'

# -ldflags for implicit test package applies to test binary
go test -a -c -n -gcflags=-N -ldflags=-X=x.y=z z1
stderr 'compile.* -N .*z_test.go'
stderr 'link.* -X=x.y=z'

# -ldflags for explicit test package applies to test binary
go test -a -c -n -gcflags=z1=-N -ldflags=z1=-X=x.y=z z1
stderr 'compile.* -N .*z_test.go'
stderr 'link.* -X=x.y=z'

# -ldflags applies to link of command
go build -a -n -ldflags=-X=math.pi=3 my/cmd/prog
stderr 'link.* -X=math.pi=3'

# -ldflags applies to link of command even with strange directory name
go build -a -n -ldflags=-X=math.pi=3 my/cmd/prog/
stderr 'link.* -X=math.pi=3'

# -ldflags applies to current directory
cd my/cmd/prog
go build -a -n -ldflags=-X=math.pi=3
stderr 'link.* -X=math.pi=3'

# -ldflags applies to current directory even if GOPATH is funny
[!case-sensitive] cd $WORK/GoPath/src/my/cmd/prog
go build -a -n -ldflags=-X=math.pi=3
stderr 'link.* -X=math.pi=3'

# cgo.a should not be a dependency of internally-linked go package
go build -ldflags='-linkmode=external -linkmode=internal' -n prog.go
! stderr 'packagefile .*runtime/cgo.a'

-- z1/z.go --
package z1
import _ "y"
import _ "z2"

-- z1/z_test.go --
package z1_test
import "testing"
import _ "z3"
func Test(t *testing.T) {}

-- z2/z.go --
package z2

-- z3/z.go --
package z3

-- y/y.go --
package y

-- my/cmd/prog/prog.go --
package main
func main() {}
