/**
 * 商米扫码头配置工具
 * 用于配置和管理商米PDA设备的扫码功能
 */

export class SunmiConfig {
  
  /**
   * 扫码头类型常量
   */
  static SCANNER_MODELS = {
    NONE: 100,
    P2LITE_V2PRO_P2PRO: 101,
    L2_NEWLAND_EM2096: 102,
    L2_ZEBRA_SE4710: 103,
    L2_HONEYWELL_N3601: 104,
    L2_HONEYWELL_N6603: 105,
    L2_ZEBRA_SE4750: 106,
    L2_ZEBRA_EM1350: 107
  }
  
  /**
   * 广播动作常量
   */
  static BROADCAST_ACTION = "com.sunmi.scanner.ACTION_DATA_CODE_RECEIVED"
  
  /**
   * 扫码服务相关常量
   */
  static SERVICE_PACKAGE = "com.sunmi.scanner"
  static SERVICE_ACTION = "com.sunmi.scanner.IScanInterface"
  
  /**
   * 数据字段常量
   */
  static DATA_FIELD = "data"
  static SOURCE_BYTE_FIELD = "source_byte"
  
  /**
   * 检查是否为商米设备
   */
  static isSunmiDevice() {
    if (uni.getSystemInfoSync().platform !== 'android') {
      return false
    }
    
    try {
      const systemInfo = uni.getSystemInfoSync()
      // 简单的品牌检查
      if (systemInfo.brand && systemInfo.brand.toLowerCase().includes('sunmi')) {
        return true
      }
      
      // 尝试检查商米扫码服务包是否存在
      const main = plus.android.runtimeMainActivity()
      const packageManager = main.getPackageManager()
      const PackageManager = plus.android.importClass("android.content.pm.PackageManager")
      
      try {
        // 尝试获取商米扫码服务包信息
        const packageInfo = packageManager.getPackageInfo(this.SERVICE_PACKAGE, 0)
        return packageInfo != null
      } catch (packageError) {
        console.log('商米扫码服务包不存在:', packageError)
        return false
      }
    } catch (error) {
      console.log('检查商米设备失败:', error)
      return false
    }
  }
  
  /**
   * 获取设备信息
   */
  static getDeviceInfo() {
    try {
      const systemInfo = uni.getSystemInfoSync()
      return {
        platform: systemInfo.platform,
        brand: systemInfo.brand,
        model: systemInfo.model,
        system: systemInfo.system
      }
    } catch (error) {
      console.error('获取设备信息失败:', error)
      return null
    }
  }
  
  /**
   * 配置扫码输出方式
   * 根据商米文档，设置为广播输出模式
   */
  static async configureOutputMode() {
    try {
      // 这里可以通过Intent广播来配置扫码头的输出方式
      // 但通常需要用户在设备设置中手动配置
      console.log('商米扫码头输出配置提示：')
      console.log('1. 打开设备设置 -> 扫码头设置')
      console.log('2. 确保"广播输出"已开启')
      console.log('3. 确保监听广播为: ' + this.BROADCAST_ACTION)
      
      return {
        success: true,
        message: '请确认设备扫码配置已正确设置'
      }
    } catch (error) {
      console.error('配置扫码输出模式失败:', error)
      return {
        success: false,
        error: error.message
      }
    }
  }
  
  /**
   * 检查扫码头配置
   */
  static async checkScannerConfig() {
    if (!this.isSunmiDevice()) {
      return {
        success: false,
        error: '非商米设备或扫码服务不可用'
      }
    }
    
    try {
      const deviceInfo = this.getDeviceInfo()
      console.log('设备信息:', deviceInfo)
      
      return {
        success: true,
        deviceInfo: deviceInfo,
        message: '商米扫码头检查通过'
      }
    } catch (error) {
      console.error('检查扫码头配置失败:', error)
      return {
        success: false,
        error: error.message
      }
    }
  }
}

export default SunmiConfig
