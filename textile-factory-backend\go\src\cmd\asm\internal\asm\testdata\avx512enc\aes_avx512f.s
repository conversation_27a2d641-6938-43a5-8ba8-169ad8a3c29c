// Code generated by avx512test. DO NOT EDIT.

#include "../../../../../../runtime/textflag.h"

TEXT asmtest_aes_avx512f(SB), NOSPLIT, $0
	VAESDEC X24, X7, X11                               // 62124508ded8 or 6212c508ded8
	VAESDEC X20, X7, X11                               // 62324508dedc or 6232c508dedc
	VAESDEC X24, X0, X11                               // 62127d08ded8 or 6212fd08ded8
	VAESDEC X20, X0, X11                               // 62327d08dedc or 6232fd08dedc
	VAESDEC X24, X7, X31                               // 62024508def8 or 6202c508def8
	VAESDEC X20, X7, X31                               // 62224508defc or 6222c508defc
	VAESDEC X7, X7, X31                                // 62624508deff or 6262c508deff
	VAESDEC -7(DI)(R8*1), X7, X31                      // 62224508debc07f9ffffff or 6222c508debc07f9ffffff
	VAESDEC (SP), X7, X31                              // 62624508de3c24 or 6262c508de3c24
	VAESDEC X24, X0, X31                               // 62027d08def8 or 6202fd08def8
	VAESDEC X20, X0, X31                               // 62227d08defc or 6222fd08defc
	VAESDEC X7, X0, X31                                // 62627d08deff or 6262fd08deff
	VAESDEC -7(DI)(R8*1), X0, X31                      // 62227d08debc07f9ffffff or 6222fd08debc07f9ffffff
	VAESDEC (SP), X0, X31                              // 62627d08de3c24 or 6262fd08de3c24
	VAESDEC X24, X7, X3                                // 62924508ded8 or 6292c508ded8
	VAESDEC X20, X7, X3                                // 62b24508dedc or 62b2c508dedc
	VAESDEC X24, X0, X3                                // 62927d08ded8 or 6292fd08ded8
	VAESDEC X20, X0, X3                                // 62b27d08dedc or 62b2fd08dedc
	VAESDEC Y5, Y31, Y22                               // 62e20520def5 or 62e28520def5
	VAESDEC Y19, Y31, Y22                              // 62a20520def3 or 62a28520def3
	VAESDEC Y31, Y31, Y22                              // 62820520def7 or 62828520def7
	VAESDEC 99(R15)(R15*1), Y31, Y22                   // 62820520deb43f63000000 or 62828520deb43f63000000
	VAESDEC (DX), Y31, Y22                             // 62e20520de32 or 62e28520de32
	VAESDEC Y5, Y5, Y22                                // 62e25528def5 or 62e2d528def5
	VAESDEC Y19, Y5, Y22                               // 62a25528def3 or 62a2d528def3
	VAESDEC Y31, Y5, Y22                               // 62825528def7 or 6282d528def7
	VAESDEC 99(R15)(R15*1), Y5, Y22                    // 62825528deb43f63000000 or 6282d528deb43f63000000
	VAESDEC (DX), Y5, Y22                              // 62e25528de32 or 62e2d528de32
	VAESDEC Y5, Y0, Y22                                // 62e27d28def5 or 62e2fd28def5
	VAESDEC Y19, Y0, Y22                               // 62a27d28def3 or 62a2fd28def3
	VAESDEC Y31, Y0, Y22                               // 62827d28def7 or 6282fd28def7
	VAESDEC 99(R15)(R15*1), Y0, Y22                    // 62827d28deb43f63000000 or 6282fd28deb43f63000000
	VAESDEC (DX), Y0, Y22                              // 62e27d28de32 or 62e2fd28de32
	VAESDEC Y5, Y31, Y9                                // 62720520decd or 62728520decd
	VAESDEC Y19, Y31, Y9                               // 62320520decb or 62328520decb
	VAESDEC Y31, Y31, Y9                               // 62120520decf or 62128520decf
	VAESDEC 99(R15)(R15*1), Y31, Y9                    // 62120520de8c3f63000000 or 62128520de8c3f63000000
	VAESDEC (DX), Y31, Y9                              // 62720520de0a or 62728520de0a
	VAESDEC Y19, Y5, Y9                                // 62325528decb or 6232d528decb
	VAESDEC Y31, Y5, Y9                                // 62125528decf or 6212d528decf
	VAESDEC Y19, Y0, Y9                                // 62327d28decb or 6232fd28decb
	VAESDEC Y31, Y0, Y9                                // 62127d28decf or 6212fd28decf
	VAESDEC Y5, Y31, Y23                               // 62e20520defd or 62e28520defd
	VAESDEC Y19, Y31, Y23                              // 62a20520defb or 62a28520defb
	VAESDEC Y31, Y31, Y23                              // 62820520deff or 62828520deff
	VAESDEC 99(R15)(R15*1), Y31, Y23                   // 62820520debc3f63000000 or 62828520debc3f63000000
	VAESDEC (DX), Y31, Y23                             // 62e20520de3a or 62e28520de3a
	VAESDEC Y5, Y5, Y23                                // 62e25528defd or 62e2d528defd
	VAESDEC Y19, Y5, Y23                               // 62a25528defb or 62a2d528defb
	VAESDEC Y31, Y5, Y23                               // 62825528deff or 6282d528deff
	VAESDEC 99(R15)(R15*1), Y5, Y23                    // 62825528debc3f63000000 or 6282d528debc3f63000000
	VAESDEC (DX), Y5, Y23                              // 62e25528de3a or 62e2d528de3a
	VAESDEC Y5, Y0, Y23                                // 62e27d28defd or 62e2fd28defd
	VAESDEC Y19, Y0, Y23                               // 62a27d28defb or 62a2fd28defb
	VAESDEC Y31, Y0, Y23                               // 62827d28deff or 6282fd28deff
	VAESDEC 99(R15)(R15*1), Y0, Y23                    // 62827d28debc3f63000000 or 6282fd28debc3f63000000
	VAESDEC (DX), Y0, Y23                              // 62e27d28de3a or 62e2fd28de3a
	VAESDEC Z27, Z3, Z11                               // 62126548dedb or 6212e548dedb
	VAESDEC Z15, Z3, Z11                               // 62526548dedf or 6252e548dedf
	VAESDEC 99(R15)(R15*1), Z3, Z11                    // 62126548de9c3f63000000 or 6212e548de9c3f63000000
	VAESDEC (DX), Z3, Z11                              // 62726548de1a or 6272e548de1a
	VAESDEC Z27, Z12, Z11                              // 62121d48dedb or 62129d48dedb
	VAESDEC Z15, Z12, Z11                              // 62521d48dedf or 62529d48dedf
	VAESDEC 99(R15)(R15*1), Z12, Z11                   // 62121d48de9c3f63000000 or 62129d48de9c3f63000000
	VAESDEC (DX), Z12, Z11                             // 62721d48de1a or 62729d48de1a
	VAESDEC Z27, Z3, Z25                               // 62026548decb or 6202e548decb
	VAESDEC Z15, Z3, Z25                               // 62426548decf or 6242e548decf
	VAESDEC 99(R15)(R15*1), Z3, Z25                    // 62026548de8c3f63000000 or 6202e548de8c3f63000000
	VAESDEC (DX), Z3, Z25                              // 62626548de0a or 6262e548de0a
	VAESDEC Z27, Z12, Z25                              // 62021d48decb or 62029d48decb
	VAESDEC Z15, Z12, Z25                              // 62421d48decf or 62429d48decf
	VAESDEC 99(R15)(R15*1), Z12, Z25                   // 62021d48de8c3f63000000 or 62029d48de8c3f63000000
	VAESDEC (DX), Z12, Z25                             // 62621d48de0a or 62629d48de0a
	VAESDECLAST X21, X5, X9                            // 62325508dfcd or 6232d508dfcd
	VAESDECLAST X21, X31, X9                           // 62320500dfcd or 62328500dfcd
	VAESDECLAST X1, X31, X9                            // 62720500dfc9 or 62728500dfc9
	VAESDECLAST X11, X31, X9                           // 62520500dfcb or 62528500dfcb
	VAESDECLAST -7(CX), X31, X9                        // 62720500df89f9ffffff or 62728500df89f9ffffff
	VAESDECLAST 15(DX)(BX*4), X31, X9                  // 62720500df8c9a0f000000 or 62728500df8c9a0f000000
	VAESDECLAST X21, X3, X9                            // 62326508dfcd or 6232e508dfcd
	VAESDECLAST X21, X5, X7                            // 62b25508dffd or 62b2d508dffd
	VAESDECLAST X21, X31, X7                           // 62b20500dffd or 62b28500dffd
	VAESDECLAST X1, X31, X7                            // 62f20500dff9 or 62f28500dff9
	VAESDECLAST X11, X31, X7                           // 62d20500dffb or 62d28500dffb
	VAESDECLAST -7(CX), X31, X7                        // 62f20500dfb9f9ffffff or 62f28500dfb9f9ffffff
	VAESDECLAST 15(DX)(BX*4), X31, X7                  // 62f20500dfbc9a0f000000 or 62f28500dfbc9a0f000000
	VAESDECLAST X21, X3, X7                            // 62b26508dffd or 62b2e508dffd
	VAESDECLAST X21, X5, X14                           // 62325508dff5 or 6232d508dff5
	VAESDECLAST X21, X31, X14                          // 62320500dff5 or 62328500dff5
	VAESDECLAST X1, X31, X14                           // 62720500dff1 or 62728500dff1
	VAESDECLAST X11, X31, X14                          // 62520500dff3 or 62528500dff3
	VAESDECLAST -7(CX), X31, X14                       // 62720500dfb1f9ffffff or 62728500dfb1f9ffffff
	VAESDECLAST 15(DX)(BX*4), X31, X14                 // 62720500dfb49a0f000000 or 62728500dfb49a0f000000
	VAESDECLAST X21, X3, X14                           // 62326508dff5 or 6232e508dff5
	VAESDECLAST Y31, Y27, Y28                          // 62022520dfe7 or 6202a520dfe7
	VAESDECLAST Y3, Y27, Y28                           // 62622520dfe3 or 6262a520dfe3
	VAESDECLAST Y14, Y27, Y28                          // 62422520dfe6 or 6242a520dfe6
	VAESDECLAST -17(BP)(SI*8), Y27, Y28                // 62622520dfa4f5efffffff or 6262a520dfa4f5efffffff
	VAESDECLAST (R15), Y27, Y28                        // 62422520df27 or 6242a520df27
	VAESDECLAST Y31, Y0, Y28                           // 62027d28dfe7 or 6202fd28dfe7
	VAESDECLAST Y3, Y0, Y28                            // 62627d28dfe3 or 6262fd28dfe3
	VAESDECLAST Y14, Y0, Y28                           // 62427d28dfe6 or 6242fd28dfe6
	VAESDECLAST -17(BP)(SI*8), Y0, Y28                 // 62627d28dfa4f5efffffff or 6262fd28dfa4f5efffffff
	VAESDECLAST (R15), Y0, Y28                         // 62427d28df27 or 6242fd28df27
	VAESDECLAST Y31, Y11, Y28                          // 62022528dfe7 or 6202a528dfe7
	VAESDECLAST Y3, Y11, Y28                           // 62622528dfe3 or 6262a528dfe3
	VAESDECLAST Y14, Y11, Y28                          // 62422528dfe6 or 6242a528dfe6
	VAESDECLAST -17(BP)(SI*8), Y11, Y28                // 62622528dfa4f5efffffff or 6262a528dfa4f5efffffff
	VAESDECLAST (R15), Y11, Y28                        // 62422528df27 or 6242a528df27
	VAESDECLAST Y31, Y27, Y2                           // 62922520dfd7 or 6292a520dfd7
	VAESDECLAST Y3, Y27, Y2                            // 62f22520dfd3 or 62f2a520dfd3
	VAESDECLAST Y14, Y27, Y2                           // 62d22520dfd6 or 62d2a520dfd6
	VAESDECLAST -17(BP)(SI*8), Y27, Y2                 // 62f22520df94f5efffffff or 62f2a520df94f5efffffff
	VAESDECLAST (R15), Y27, Y2                         // 62d22520df17 or 62d2a520df17
	VAESDECLAST Y31, Y0, Y2                            // 62927d28dfd7 or 6292fd28dfd7
	VAESDECLAST Y31, Y11, Y2                           // 62922528dfd7 or 6292a528dfd7
	VAESDECLAST Y31, Y27, Y24                          // 62022520dfc7 or 6202a520dfc7
	VAESDECLAST Y3, Y27, Y24                           // 62622520dfc3 or 6262a520dfc3
	VAESDECLAST Y14, Y27, Y24                          // 62422520dfc6 or 6242a520dfc6
	VAESDECLAST -17(BP)(SI*8), Y27, Y24                // 62622520df84f5efffffff or 6262a520df84f5efffffff
	VAESDECLAST (R15), Y27, Y24                        // 62422520df07 or 6242a520df07
	VAESDECLAST Y31, Y0, Y24                           // 62027d28dfc7 or 6202fd28dfc7
	VAESDECLAST Y3, Y0, Y24                            // 62627d28dfc3 or 6262fd28dfc3
	VAESDECLAST Y14, Y0, Y24                           // 62427d28dfc6 or 6242fd28dfc6
	VAESDECLAST -17(BP)(SI*8), Y0, Y24                 // 62627d28df84f5efffffff or 6262fd28df84f5efffffff
	VAESDECLAST (R15), Y0, Y24                         // 62427d28df07 or 6242fd28df07
	VAESDECLAST Y31, Y11, Y24                          // 62022528dfc7 or 6202a528dfc7
	VAESDECLAST Y3, Y11, Y24                           // 62622528dfc3 or 6262a528dfc3
	VAESDECLAST Y14, Y11, Y24                          // 62422528dfc6 or 6242a528dfc6
	VAESDECLAST -17(BP)(SI*8), Y11, Y24                // 62622528df84f5efffffff or 6262a528df84f5efffffff
	VAESDECLAST (R15), Y11, Y24                        // 62422528df07 or 6242a528df07
	VAESDECLAST Z8, Z23, Z23                           // 62c24540dff8 or 62c2c540dff8
	VAESDECLAST Z28, Z23, Z23                          // 62824540dffc or 6282c540dffc
	VAESDECLAST -17(BP)(SI*8), Z23, Z23                // 62e24540dfbcf5efffffff or 62e2c540dfbcf5efffffff
	VAESDECLAST (R15), Z23, Z23                        // 62c24540df3f or 62c2c540df3f
	VAESDECLAST Z8, Z6, Z23                            // 62c24d48dff8 or 62c2cd48dff8
	VAESDECLAST Z28, Z6, Z23                           // 62824d48dffc or 6282cd48dffc
	VAESDECLAST -17(BP)(SI*8), Z6, Z23                 // 62e24d48dfbcf5efffffff or 62e2cd48dfbcf5efffffff
	VAESDECLAST (R15), Z6, Z23                         // 62c24d48df3f or 62c2cd48df3f
	VAESDECLAST Z8, Z23, Z5                            // 62d24540dfe8 or 62d2c540dfe8
	VAESDECLAST Z28, Z23, Z5                           // 62924540dfec or 6292c540dfec
	VAESDECLAST -17(BP)(SI*8), Z23, Z5                 // 62f24540dfacf5efffffff or 62f2c540dfacf5efffffff
	VAESDECLAST (R15), Z23, Z5                         // 62d24540df2f or 62d2c540df2f
	VAESDECLAST Z8, Z6, Z5                             // 62d24d48dfe8 or 62d2cd48dfe8
	VAESDECLAST Z28, Z6, Z5                            // 62924d48dfec or 6292cd48dfec
	VAESDECLAST -17(BP)(SI*8), Z6, Z5                  // 62f24d48dfacf5efffffff or 62f2cd48dfacf5efffffff
	VAESDECLAST (R15), Z6, Z5                          // 62d24d48df2f or 62d2cd48df2f
	VAESENC X14, X16, X13                              // 62527d00dcee or 6252fd00dcee
	VAESENC X19, X16, X13                              // 62327d00dceb or 6232fd00dceb
	VAESENC X8, X16, X13                               // 62527d00dce8 or 6252fd00dce8
	VAESENC 99(R15)(R15*8), X16, X13                   // 62127d00dcacff63000000 or 6212fd00dcacff63000000
	VAESENC 7(AX)(CX*8), X16, X13                      // 62727d00dcacc807000000 or 6272fd00dcacc807000000
	VAESENC X19, X14, X13                              // 62320d08dceb or 62328d08dceb
	VAESENC X19, X11, X13                              // 62322508dceb or 6232a508dceb
	VAESENC X14, X16, X0                               // 62d27d00dcc6 or 62d2fd00dcc6
	VAESENC X19, X16, X0                               // 62b27d00dcc3 or 62b2fd00dcc3
	VAESENC X8, X16, X0                                // 62d27d00dcc0 or 62d2fd00dcc0
	VAESENC 99(R15)(R15*8), X16, X0                    // 62927d00dc84ff63000000 or 6292fd00dc84ff63000000
	VAESENC 7(AX)(CX*8), X16, X0                       // 62f27d00dc84c807000000 or 62f2fd00dc84c807000000
	VAESENC X19, X14, X0                               // 62b20d08dcc3 or 62b28d08dcc3
	VAESENC X19, X11, X0                               // 62b22508dcc3 or 62b2a508dcc3
	VAESENC X14, X16, X30                              // 62427d00dcf6 or 6242fd00dcf6
	VAESENC X19, X16, X30                              // 62227d00dcf3 or 6222fd00dcf3
	VAESENC X8, X16, X30                               // 62427d00dcf0 or 6242fd00dcf0
	VAESENC 99(R15)(R15*8), X16, X30                   // 62027d00dcb4ff63000000 or 6202fd00dcb4ff63000000
	VAESENC 7(AX)(CX*8), X16, X30                      // 62627d00dcb4c807000000 or 6262fd00dcb4c807000000
	VAESENC X14, X14, X30                              // 62420d08dcf6 or 62428d08dcf6
	VAESENC X19, X14, X30                              // 62220d08dcf3 or 62228d08dcf3
	VAESENC X8, X14, X30                               // 62420d08dcf0 or 62428d08dcf0
	VAESENC 99(R15)(R15*8), X14, X30                   // 62020d08dcb4ff63000000 or 62028d08dcb4ff63000000
	VAESENC 7(AX)(CX*8), X14, X30                      // 62620d08dcb4c807000000 or 62628d08dcb4c807000000
	VAESENC X14, X11, X30                              // 62422508dcf6 or 6242a508dcf6
	VAESENC X19, X11, X30                              // 62222508dcf3 or 6222a508dcf3
	VAESENC X8, X11, X30                               // 62422508dcf0 or 6242a508dcf0
	VAESENC 99(R15)(R15*8), X11, X30                   // 62022508dcb4ff63000000 or 6202a508dcb4ff63000000
	VAESENC 7(AX)(CX*8), X11, X30                      // 62622508dcb4c807000000 or 6262a508dcb4c807000000
	VAESENC Y18, Y15, Y2                               // 62b20528dcd2 or 62b28528dcd2
	VAESENC Y24, Y15, Y2                               // 62920528dcd0 or 62928528dcd0
	VAESENC Y18, Y22, Y2                               // 62b24d20dcd2 or 62b2cd20dcd2
	VAESENC Y24, Y22, Y2                               // 62924d20dcd0 or 6292cd20dcd0
	VAESENC Y9, Y22, Y2                                // 62d24d20dcd1 or 62d2cd20dcd1
	VAESENC 7(SI)(DI*8), Y22, Y2                       // 62f24d20dc94fe07000000 or 62f2cd20dc94fe07000000
	VAESENC -15(R14), Y22, Y2                          // 62d24d20dc96f1ffffff or 62d2cd20dc96f1ffffff
	VAESENC Y18, Y20, Y2                               // 62b25d20dcd2 or 62b2dd20dcd2
	VAESENC Y24, Y20, Y2                               // 62925d20dcd0 or 6292dd20dcd0
	VAESENC Y9, Y20, Y2                                // 62d25d20dcd1 or 62d2dd20dcd1
	VAESENC 7(SI)(DI*8), Y20, Y2                       // 62f25d20dc94fe07000000 or 62f2dd20dc94fe07000000
	VAESENC -15(R14), Y20, Y2                          // 62d25d20dc96f1ffffff or 62d2dd20dc96f1ffffff
	VAESENC Y18, Y15, Y13                              // 62320528dcea or 62328528dcea
	VAESENC Y24, Y15, Y13                              // 62120528dce8 or 62128528dce8
	VAESENC Y18, Y22, Y13                              // 62324d20dcea or 6232cd20dcea
	VAESENC Y24, Y22, Y13                              // 62124d20dce8 or 6212cd20dce8
	VAESENC Y9, Y22, Y13                               // 62524d20dce9 or 6252cd20dce9
	VAESENC 7(SI)(DI*8), Y22, Y13                      // 62724d20dcacfe07000000 or 6272cd20dcacfe07000000
	VAESENC -15(R14), Y22, Y13                         // 62524d20dcaef1ffffff or 6252cd20dcaef1ffffff
	VAESENC Y18, Y20, Y13                              // 62325d20dcea or 6232dd20dcea
	VAESENC Y24, Y20, Y13                              // 62125d20dce8 or 6212dd20dce8
	VAESENC Y9, Y20, Y13                               // 62525d20dce9 or 6252dd20dce9
	VAESENC 7(SI)(DI*8), Y20, Y13                      // 62725d20dcacfe07000000 or 6272dd20dcacfe07000000
	VAESENC -15(R14), Y20, Y13                         // 62525d20dcaef1ffffff or 6252dd20dcaef1ffffff
	VAESENC Y18, Y15, Y27                              // 62220528dcda or 62228528dcda
	VAESENC Y24, Y15, Y27                              // 62020528dcd8 or 62028528dcd8
	VAESENC Y9, Y15, Y27                               // 62420528dcd9 or 62428528dcd9
	VAESENC 7(SI)(DI*8), Y15, Y27                      // 62620528dc9cfe07000000 or 62628528dc9cfe07000000
	VAESENC -15(R14), Y15, Y27                         // 62420528dc9ef1ffffff or 62428528dc9ef1ffffff
	VAESENC Y18, Y22, Y27                              // 62224d20dcda or 6222cd20dcda
	VAESENC Y24, Y22, Y27                              // 62024d20dcd8 or 6202cd20dcd8
	VAESENC Y9, Y22, Y27                               // 62424d20dcd9 or 6242cd20dcd9
	VAESENC 7(SI)(DI*8), Y22, Y27                      // 62624d20dc9cfe07000000 or 6262cd20dc9cfe07000000
	VAESENC -15(R14), Y22, Y27                         // 62424d20dc9ef1ffffff or 6242cd20dc9ef1ffffff
	VAESENC Y18, Y20, Y27                              // 62225d20dcda or 6222dd20dcda
	VAESENC Y24, Y20, Y27                              // 62025d20dcd8 or 6202dd20dcd8
	VAESENC Y9, Y20, Y27                               // 62425d20dcd9 or 6242dd20dcd9
	VAESENC 7(SI)(DI*8), Y20, Y27                      // 62625d20dc9cfe07000000 or 6262dd20dc9cfe07000000
	VAESENC -15(R14), Y20, Y27                         // 62425d20dc9ef1ffffff or 6242dd20dc9ef1ffffff
	VAESENC Z12, Z16, Z21                              // 62c27d40dcec or 62c2fd40dcec
	VAESENC Z27, Z16, Z21                              // 62827d40dceb or 6282fd40dceb
	VAESENC 7(SI)(DI*8), Z16, Z21                      // 62e27d40dcacfe07000000 or 62e2fd40dcacfe07000000
	VAESENC -15(R14), Z16, Z21                         // 62c27d40dcaef1ffffff or 62c2fd40dcaef1ffffff
	VAESENC Z12, Z13, Z21                              // 62c21548dcec or 62c29548dcec
	VAESENC Z27, Z13, Z21                              // 62821548dceb or 62829548dceb
	VAESENC 7(SI)(DI*8), Z13, Z21                      // 62e21548dcacfe07000000 or 62e29548dcacfe07000000
	VAESENC -15(R14), Z13, Z21                         // 62c21548dcaef1ffffff or 62c29548dcaef1ffffff
	VAESENC Z12, Z16, Z5                               // 62d27d40dcec or 62d2fd40dcec
	VAESENC Z27, Z16, Z5                               // 62927d40dceb or 6292fd40dceb
	VAESENC 7(SI)(DI*8), Z16, Z5                       // 62f27d40dcacfe07000000 or 62f2fd40dcacfe07000000
	VAESENC -15(R14), Z16, Z5                          // 62d27d40dcaef1ffffff or 62d2fd40dcaef1ffffff
	VAESENC Z12, Z13, Z5                               // 62d21548dcec or 62d29548dcec
	VAESENC Z27, Z13, Z5                               // 62921548dceb or 62929548dceb
	VAESENC 7(SI)(DI*8), Z13, Z5                       // 62f21548dcacfe07000000 or 62f29548dcacfe07000000
	VAESENC -15(R14), Z13, Z5                          // 62d21548dcaef1ffffff or 62d29548dcaef1ffffff
	VAESENCLAST X23, X12, X8                           // 62321d08ddc7 or 62329d08ddc7
	VAESENCLAST X31, X12, X8                           // 62121d08ddc7 or 62129d08ddc7
	VAESENCLAST X23, X16, X8                           // 62327d00ddc7 or 6232fd00ddc7
	VAESENCLAST X11, X16, X8                           // 62527d00ddc3 or 6252fd00ddc3
	VAESENCLAST X31, X16, X8                           // 62127d00ddc7 or 6212fd00ddc7
	VAESENCLAST (AX), X16, X8                          // 62727d00dd00 or 6272fd00dd00
	VAESENCLAST 7(SI), X16, X8                         // 62727d00dd8607000000 or 6272fd00dd8607000000
	VAESENCLAST X23, X23, X8                           // 62324500ddc7 or 6232c500ddc7
	VAESENCLAST X11, X23, X8                           // 62524500ddc3 or 6252c500ddc3
	VAESENCLAST X31, X23, X8                           // 62124500ddc7 or 6212c500ddc7
	VAESENCLAST (AX), X23, X8                          // 62724500dd00 or 6272c500dd00
	VAESENCLAST 7(SI), X23, X8                         // 62724500dd8607000000 or 6272c500dd8607000000
	VAESENCLAST X23, X12, X26                          // 62221d08ddd7 or 62229d08ddd7
	VAESENCLAST X11, X12, X26                          // 62421d08ddd3 or 62429d08ddd3
	VAESENCLAST X31, X12, X26                          // 62021d08ddd7 or 62029d08ddd7
	VAESENCLAST (AX), X12, X26                         // 62621d08dd10 or 62629d08dd10
	VAESENCLAST 7(SI), X12, X26                        // 62621d08dd9607000000 or 62629d08dd9607000000
	VAESENCLAST X23, X16, X26                          // 62227d00ddd7 or 6222fd00ddd7
	VAESENCLAST X11, X16, X26                          // 62427d00ddd3 or 6242fd00ddd3
	VAESENCLAST X31, X16, X26                          // 62027d00ddd7 or 6202fd00ddd7
	VAESENCLAST (AX), X16, X26                         // 62627d00dd10 or 6262fd00dd10
	VAESENCLAST 7(SI), X16, X26                        // 62627d00dd9607000000 or 6262fd00dd9607000000
	VAESENCLAST X23, X23, X26                          // 62224500ddd7 or 6222c500ddd7
	VAESENCLAST X11, X23, X26                          // 62424500ddd3 or 6242c500ddd3
	VAESENCLAST X31, X23, X26                          // 62024500ddd7 or 6202c500ddd7
	VAESENCLAST (AX), X23, X26                         // 62624500dd10 or 6262c500dd10
	VAESENCLAST 7(SI), X23, X26                        // 62624500dd9607000000 or 6262c500dd9607000000
	VAESENCLAST X23, X12, X23                          // 62a21d08ddff or 62a29d08ddff
	VAESENCLAST X11, X12, X23                          // 62c21d08ddfb or 62c29d08ddfb
	VAESENCLAST X31, X12, X23                          // 62821d08ddff or 62829d08ddff
	VAESENCLAST (AX), X12, X23                         // 62e21d08dd38 or 62e29d08dd38
	VAESENCLAST 7(SI), X12, X23                        // 62e21d08ddbe07000000 or 62e29d08ddbe07000000
	VAESENCLAST X23, X16, X23                          // 62a27d00ddff or 62a2fd00ddff
	VAESENCLAST X11, X16, X23                          // 62c27d00ddfb or 62c2fd00ddfb
	VAESENCLAST X31, X16, X23                          // 62827d00ddff or 6282fd00ddff
	VAESENCLAST (AX), X16, X23                         // 62e27d00dd38 or 62e2fd00dd38
	VAESENCLAST 7(SI), X16, X23                        // 62e27d00ddbe07000000 or 62e2fd00ddbe07000000
	VAESENCLAST X23, X23, X23                          // 62a24500ddff or 62a2c500ddff
	VAESENCLAST X11, X23, X23                          // 62c24500ddfb or 62c2c500ddfb
	VAESENCLAST X31, X23, X23                          // 62824500ddff or 6282c500ddff
	VAESENCLAST (AX), X23, X23                         // 62e24500dd38 or 62e2c500dd38
	VAESENCLAST 7(SI), X23, X23                        // 62e24500ddbe07000000 or 62e2c500ddbe07000000
	VAESENCLAST Y5, Y19, Y3                            // 62f26520dddd or 62f2e520dddd
	VAESENCLAST Y16, Y19, Y3                           // 62b26520ddd8 or 62b2e520ddd8
	VAESENCLAST Y2, Y19, Y3                            // 62f26520ddda or 62f2e520ddda
	VAESENCLAST 7(SI)(DI*1), Y19, Y3                   // 62f26520dd9c3e07000000 or 62f2e520dd9c3e07000000
	VAESENCLAST 15(DX)(BX*8), Y19, Y3                  // 62f26520dd9cda0f000000 or 62f2e520dd9cda0f000000
	VAESENCLAST Y16, Y14, Y3                           // 62b20d28ddd8 or 62b28d28ddd8
	VAESENCLAST Y5, Y21, Y3                            // 62f25520dddd or 62f2d520dddd
	VAESENCLAST Y16, Y21, Y3                           // 62b25520ddd8 or 62b2d520ddd8
	VAESENCLAST Y2, Y21, Y3                            // 62f25520ddda or 62f2d520ddda
	VAESENCLAST 7(SI)(DI*1), Y21, Y3                   // 62f25520dd9c3e07000000 or 62f2d520dd9c3e07000000
	VAESENCLAST 15(DX)(BX*8), Y21, Y3                  // 62f25520dd9cda0f000000 or 62f2d520dd9cda0f000000
	VAESENCLAST Y5, Y19, Y19                           // 62e26520dddd or 62e2e520dddd
	VAESENCLAST Y16, Y19, Y19                          // 62a26520ddd8 or 62a2e520ddd8
	VAESENCLAST Y2, Y19, Y19                           // 62e26520ddda or 62e2e520ddda
	VAESENCLAST 7(SI)(DI*1), Y19, Y19                  // 62e26520dd9c3e07000000 or 62e2e520dd9c3e07000000
	VAESENCLAST 15(DX)(BX*8), Y19, Y19                 // 62e26520dd9cda0f000000 or 62e2e520dd9cda0f000000
	VAESENCLAST Y5, Y14, Y19                           // 62e20d28dddd or 62e28d28dddd
	VAESENCLAST Y16, Y14, Y19                          // 62a20d28ddd8 or 62a28d28ddd8
	VAESENCLAST Y2, Y14, Y19                           // 62e20d28ddda or 62e28d28ddda
	VAESENCLAST 7(SI)(DI*1), Y14, Y19                  // 62e20d28dd9c3e07000000 or 62e28d28dd9c3e07000000
	VAESENCLAST 15(DX)(BX*8), Y14, Y19                 // 62e20d28dd9cda0f000000 or 62e28d28dd9cda0f000000
	VAESENCLAST Y5, Y21, Y19                           // 62e25520dddd or 62e2d520dddd
	VAESENCLAST Y16, Y21, Y19                          // 62a25520ddd8 or 62a2d520ddd8
	VAESENCLAST Y2, Y21, Y19                           // 62e25520ddda or 62e2d520ddda
	VAESENCLAST 7(SI)(DI*1), Y21, Y19                  // 62e25520dd9c3e07000000 or 62e2d520dd9c3e07000000
	VAESENCLAST 15(DX)(BX*8), Y21, Y19                 // 62e25520dd9cda0f000000 or 62e2d520dd9cda0f000000
	VAESENCLAST Y5, Y19, Y23                           // 62e26520ddfd or 62e2e520ddfd
	VAESENCLAST Y16, Y19, Y23                          // 62a26520ddf8 or 62a2e520ddf8
	VAESENCLAST Y2, Y19, Y23                           // 62e26520ddfa or 62e2e520ddfa
	VAESENCLAST 7(SI)(DI*1), Y19, Y23                  // 62e26520ddbc3e07000000 or 62e2e520ddbc3e07000000
	VAESENCLAST 15(DX)(BX*8), Y19, Y23                 // 62e26520ddbcda0f000000 or 62e2e520ddbcda0f000000
	VAESENCLAST Y5, Y14, Y23                           // 62e20d28ddfd or 62e28d28ddfd
	VAESENCLAST Y16, Y14, Y23                          // 62a20d28ddf8 or 62a28d28ddf8
	VAESENCLAST Y2, Y14, Y23                           // 62e20d28ddfa or 62e28d28ddfa
	VAESENCLAST 7(SI)(DI*1), Y14, Y23                  // 62e20d28ddbc3e07000000 or 62e28d28ddbc3e07000000
	VAESENCLAST 15(DX)(BX*8), Y14, Y23                 // 62e20d28ddbcda0f000000 or 62e28d28ddbcda0f000000
	VAESENCLAST Y5, Y21, Y23                           // 62e25520ddfd or 62e2d520ddfd
	VAESENCLAST Y16, Y21, Y23                          // 62a25520ddf8 or 62a2d520ddf8
	VAESENCLAST Y2, Y21, Y23                           // 62e25520ddfa or 62e2d520ddfa
	VAESENCLAST 7(SI)(DI*1), Y21, Y23                  // 62e25520ddbc3e07000000 or 62e2d520ddbc3e07000000
	VAESENCLAST 15(DX)(BX*8), Y21, Y23                 // 62e25520ddbcda0f000000 or 62e2d520ddbcda0f000000
	VAESENCLAST Z25, Z6, Z22                           // 62824d48ddf1 or 6282cd48ddf1
	VAESENCLAST Z12, Z6, Z22                           // 62c24d48ddf4 or 62c2cd48ddf4
	VAESENCLAST 7(SI)(DI*1), Z6, Z22                   // 62e24d48ddb43e07000000 or 62e2cd48ddb43e07000000
	VAESENCLAST 15(DX)(BX*8), Z6, Z22                  // 62e24d48ddb4da0f000000 or 62e2cd48ddb4da0f000000
	VAESENCLAST Z25, Z8, Z22                           // 62823d48ddf1 or 6282bd48ddf1
	VAESENCLAST Z12, Z8, Z22                           // 62c23d48ddf4 or 62c2bd48ddf4
	VAESENCLAST 7(SI)(DI*1), Z8, Z22                   // 62e23d48ddb43e07000000 or 62e2bd48ddb43e07000000
	VAESENCLAST 15(DX)(BX*8), Z8, Z22                  // 62e23d48ddb4da0f000000 or 62e2bd48ddb4da0f000000
	VAESENCLAST Z25, Z6, Z11                           // 62124d48ddd9 or 6212cd48ddd9
	VAESENCLAST Z12, Z6, Z11                           // 62524d48dddc or 6252cd48dddc
	VAESENCLAST 7(SI)(DI*1), Z6, Z11                   // 62724d48dd9c3e07000000 or 6272cd48dd9c3e07000000
	VAESENCLAST 15(DX)(BX*8), Z6, Z11                  // 62724d48dd9cda0f000000 or 6272cd48dd9cda0f000000
	VAESENCLAST Z25, Z8, Z11                           // 62123d48ddd9 or 6212bd48ddd9
	VAESENCLAST Z12, Z8, Z11                           // 62523d48dddc or 6252bd48dddc
	VAESENCLAST 7(SI)(DI*1), Z8, Z11                   // 62723d48dd9c3e07000000 or 6272bd48dd9c3e07000000
	VAESENCLAST 15(DX)(BX*8), Z8, Z11                  // 62723d48dd9cda0f000000 or 6272bd48dd9cda0f000000
	RET
