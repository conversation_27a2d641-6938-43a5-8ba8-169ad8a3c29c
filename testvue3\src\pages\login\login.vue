<script lang="ts" setup>
import { ref, reactive } from 'vue'
import { API, setAuthToken } from '@/utils/api'

// 登录表单数据
const loginForm = reactive({
  username: '',
  password: ''
})

// 登录状态
const isLoading = ref(false)

// 登录方法
const handleLogin = async () => {
  if (!loginForm.username || !loginForm.password) {
    uni.showToast({
      title: '请输入用户名和密码',
      icon: 'none'
    })
    return
  }

  isLoading.value = true
  
  try {
    // 调用后端登录API
    const response = await API.auth.login({
      username: loginForm.username,
      password: loginForm.password
    })
    
    // 保存token和用户信息
    setAuthToken(response.token)
    uni.setStorageSync('userInfo', response.user)
    
    uni.showToast({
      title: '登录成功',
      icon: 'success'
    })
    
    // 登录成功后跳转到工作台
    setTimeout(() => {
      uni.switchTab({
        url: '/pages/workspace/workspace'
      })
    }, 1500)
    
  } catch (error) {
    console.error('登录失败:', error)
    uni.showToast({
      title: error.message || '登录失败',
      icon: 'none'
    })
  } finally {
    isLoading.value = false
  }
}


</script>

<template>
  <view class="login-container">
    <!-- 状态栏占位 -->
    <view class="status-bar"></view>
    
    <!-- 头部logo区域 -->
    <view class="header">
      <image class="logo" src="/static/logo.png" mode="aspectFit"></image>
      <text class="title">织厂管理系统</text>
      <text class="subtitle">机器异常管理平台</text>
    </view>
    
    <!-- 登录表单 -->
    <view class="form-container">
      <view class="form-item">
        <view class="form-label">用户名</view>
        <input 
          class="form-input" 
          v-model="loginForm.username" 
          placeholder="请输入用户名"
          placeholder-class="placeholder"
        />
      </view>
      
      <view class="form-item">
        <view class="form-label">密码</view>
        <input 
          class="form-input" 
          v-model="loginForm.password" 
          placeholder="请输入密码"
          placeholder-class="placeholder"
          password
        />
      </view>
      

      
      <button 
        class="login-btn"
        :class="{ 'loading': isLoading }"
        @click="handleLogin"
        :disabled="isLoading"
      >
        {{ isLoading ? '登录中...' : '登录' }}
      </button>
    </view>
    
    <!-- 底部信息 -->
    <view class="footer">
      <text class="footer-text">织厂机器异常管理系统 v1.0.0</text>
    </view>
  </view>
</template>

<style lang="scss">
.login-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  flex-direction: column;
  padding: 0 40rpx;
}

.status-bar {
  height: var(--status-bar-height);
}

.header {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 80rpx 0;
  
  .logo {
    width: 120rpx;
    height: 120rpx;
    margin-bottom: 40rpx;
  }
  
  .title {
    font-size: 48rpx;
    font-weight: bold;
    color: #ffffff;
    margin-bottom: 20rpx;
  }
  
  .subtitle {
    font-size: 28rpx;
    color: rgba(255, 255, 255, 0.8);
  }
}

.form-container {
  background: #ffffff;
  border-radius: 20rpx;
  padding: 60rpx 40rpx;
  margin-bottom: 40rpx;
  box-shadow: 0 10rpx 30rpx rgba(0, 0, 0, 0.1);
}

.form-item {
  margin-bottom: 40rpx;
  
  &:last-child {
    margin-bottom: 0;
  }
}

.form-label {
  font-size: 28rpx;
  color: #333333;
  margin-bottom: 20rpx;
  font-weight: 500;
}

.form-input {
  width: 100%;
  height: 88rpx;
  border: 2rpx solid #e0e0e0;
  border-radius: 12rpx;
  padding: 0 24rpx;
  font-size: 28rpx;
  color: #333333;
  background: #fafafa;
  box-sizing: border-box;
  
  &:focus {
    border-color: #2196F3;
    background: #ffffff;
  }
}

.placeholder {
  color: #999999;
}



.login-btn {
  width: 100%;
  height: 88rpx;
  background: linear-gradient(135deg, #2196F3 0%, #1976D2 100%);
  color: #ffffff;
  border: none;
  border-radius: 12rpx;
  font-size: 32rpx;
  font-weight: bold;
  margin-top: 40rpx;
  box-shadow: 0 6rpx 20rpx rgba(33, 150, 243, 0.3);
  
  &:active {
    transform: translateY(2rpx);
  }
  
  &.loading {
    background: #cccccc;
    box-shadow: none;
  }
  
  &[disabled] {
    background: #cccccc;
    box-shadow: none;
  }
}

.footer {
  text-align: center;
  padding: 40rpx 0;
}

.footer-text {
  font-size: 24rpx;
  color: rgba(255, 255, 255, 0.6);
}
</style>
