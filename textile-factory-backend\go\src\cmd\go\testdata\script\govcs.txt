env GO111MODULE=on
env proxy=$GOPROXY
env GOPROXY=direct

# GOVCS stops go get
env GOVCS='*:none'
! go get github.com/google/go-cmp
stderr '^go: GOVCS disallows using git for public github.com/google/go-cmp; see ''go help vcs''$'
env GOPRIVATE='github.com/google'
! go get github.com/google/go-cmp
stderr '^go: GOVCS disallows using git for private github.com/google/go-cmp; see ''go help vcs''$'

# public pattern works
env GOPRIVATE='github.com/google'
env GOVCS='public:all,private:none'
! go get github.com/google/go-cmp
stderr '^go: GOVCS disallows using git for private github.com/google/go-cmp; see ''go help vcs''$'

# private pattern works
env GOPRIVATE='hubgit.com/google'
env GOVCS='private:all,public:none'
! go get github.com/google/go-cmp
stderr '^go: GOVCS disallows using git for public github.com/google/go-cmp; see ''go help vcs''$'

# other patterns work (for more patterns, see TestGOVCS)
env GOPRIVATE=
env GOVCS='github.com:svn|hg'
! go get github.com/google/go-cmp
stderr '^go: GOVCS disallows using git for public github.com/google/go-cmp; see ''go help vcs''$'
env GOVCS='github.com/google/go-cmp/inner:git,github.com:svn|hg'
! go get github.com/google/go-cmp
stderr '^go: GOVCS disallows using git for public github.com/google/go-cmp; see ''go help vcs''$'

# bad patterns are reported (for more bad patterns, see TestGOVCSErrors)
env GOVCS='git'
! go get github.com/google/go-cmp
stderr '^go: github.com/google/go-cmp: malformed entry in GOVCS \(missing colon\): "git"$'

env GOVCS=github.com:hg,github.com:git
! go get github.com/google/go-cmp
stderr '^go: github.com/google/go-cmp: unreachable pattern in GOVCS: "github.com:git" after "github.com:hg"$'

# bad GOVCS patterns do not stop commands that do not need to check VCS
go list
env GOPROXY=$proxy
go get rsc.io/quote # ok because used proxy
env GOPROXY=direct

# svn is disallowed by default
env GOPRIVATE=
env GOVCS=
! go get rsc.io/nonexist.svn/hello
stderr '^go: rsc.io/nonexist.svn/hello: GOVCS disallows using svn for public rsc.io/nonexist.svn; see ''go help vcs''$'

# fossil is disallowed by default
env GOPRIVATE=
env GOVCS=
! go get rsc.io/nonexist.fossil/hello
stderr '^go: rsc.io/nonexist.fossil/hello: GOVCS disallows using fossil for public rsc.io/nonexist.fossil; see ''go help vcs''$'

# bzr is disallowed by default
env GOPRIVATE=
env GOVCS=
! go get rsc.io/nonexist.bzr/hello
stderr '^go: rsc.io/nonexist.bzr/hello: GOVCS disallows using bzr for public rsc.io/nonexist.bzr; see ''go help vcs''$'

# git is OK by default
env GOVCS=
env GONOSUMDB='*'
[net:rsc.io] [git] [!short] go get rsc.io/sampler

# hg is OK by default
env GOVCS=
env GONOSUMDB='*'
[exec:hg] [!short] go get vcs-test.golang.org/go/custom-hg-hello

# git can be disallowed
env GOVCS=public:hg
! go get rsc.io/nonexist.git/hello
stderr '^go: rsc.io/nonexist.git/hello: GOVCS disallows using git for public rsc.io/nonexist.git; see ''go help vcs''$'

# hg can be disallowed
env GOVCS=public:git
! go get rsc.io/nonexist.hg/hello
stderr '^go: rsc.io/nonexist.hg/hello: GOVCS disallows using hg for public rsc.io/nonexist.hg; see ''go help vcs''$'

# Repeat in GOPATH mode. Error texts slightly different.

env GO111MODULE=off

# GOVCS stops go get
env GOVCS='*:none'
! go get github.com/google/go-cmp
stderr '^package github.com/google/go-cmp: GOVCS disallows using git for public github.com/google/go-cmp; see ''go help vcs''$'
env GOPRIVATE='github.com/google'
! go get github.com/google/go-cmp
stderr '^package github.com/google/go-cmp: GOVCS disallows using git for private github.com/google/go-cmp; see ''go help vcs''$'

# public pattern works
env GOPRIVATE='github.com/google'
env GOVCS='public:all,private:none'
! go get github.com/google/go-cmp
stderr '^package github.com/google/go-cmp: GOVCS disallows using git for private github.com/google/go-cmp; see ''go help vcs''$'

# private pattern works
env GOPRIVATE='hubgit.com/google'
env GOVCS='private:all,public:none'
! go get github.com/google/go-cmp
stderr '^package github.com/google/go-cmp: GOVCS disallows using git for public github.com/google/go-cmp; see ''go help vcs''$'

# other patterns work (for more patterns, see TestGOVCS)
env GOPRIVATE=
env GOVCS='github.com:svn|hg'
! go get github.com/google/go-cmp
stderr '^package github.com/google/go-cmp: GOVCS disallows using git for public github.com/google/go-cmp; see ''go help vcs''$'
env GOVCS='github.com/google/go-cmp/inner:git,github.com:svn|hg'
! go get github.com/google/go-cmp
stderr '^package github.com/google/go-cmp: GOVCS disallows using git for public github.com/google/go-cmp; see ''go help vcs''$'

# bad patterns are reported (for more bad patterns, see TestGOVCSErrors)
env GOVCS='git'
! go get github.com/google/go-cmp
stderr '^package github.com/google/go-cmp: malformed entry in GOVCS \(missing colon\): "git"$'

env GOVCS=github.com:hg,github.com:git
! go get github.com/google/go-cmp
stderr '^package github.com/google/go-cmp: unreachable pattern in GOVCS: "github.com:git" after "github.com:hg"$'

# bad GOVCS patterns do not stop commands that do not need to check VCS
go list

# svn is disallowed by default
env GOPRIVATE=
env GOVCS=
! go get rsc.io/nonexist.svn/hello
stderr '^package rsc.io/nonexist.svn/hello: GOVCS disallows using svn for public rsc.io/nonexist.svn; see ''go help vcs''$'

# fossil is disallowed by default
env GOPRIVATE=
env GOVCS=
! go get rsc.io/nonexist.fossil/hello
stderr '^package rsc.io/nonexist.fossil/hello: GOVCS disallows using fossil for public rsc.io/nonexist.fossil; see ''go help vcs''$'

# bzr is disallowed by default
env GOPRIVATE=
env GOVCS=
! go get rsc.io/nonexist.bzr/hello
stderr '^package rsc.io/nonexist.bzr/hello: GOVCS disallows using bzr for public rsc.io/nonexist.bzr; see ''go help vcs''$'

# git is OK by default
env GOVCS=
env GONOSUMDB='*'
[net:rsc.io] [git] [!short] go get rsc.io/sampler

# hg is OK by default
env GOVCS=
env GONOSUMDB='*'
[exec:hg] [!short] go get vcs-test.golang.org/go/custom-hg-hello

# git can be disallowed
env GOVCS=public:hg
! go get rsc.io/nonexist.git/hello
stderr '^package rsc.io/nonexist.git/hello: GOVCS disallows using git for public rsc.io/nonexist.git; see ''go help vcs''$'

# hg can be disallowed
env GOVCS=public:git
! go get rsc.io/nonexist.hg/hello
stderr '^package rsc.io/nonexist.hg/hello: GOVCS disallows using hg for public rsc.io/nonexist.hg; see ''go help vcs''$'

-- go.mod --
module m

-- p.go --
package p
