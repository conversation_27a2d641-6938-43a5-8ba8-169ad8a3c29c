package controllers

import (
	"net/http"
	"textile-factory-backend/config"
	"textile-factory-backend/models"
	"textile-factory-backend/utils"
	"github.com/gin-gonic/gin"
)

type LoginRequest struct {
	Username string `json:"username" binding:"required"`
	Password string `json:"password" binding:"required"`
}

type LoginResponse struct {
	Token string      `json:"token"`
	User  models.User `json:"user"`
}

func Login(c *gin.Context) {
	var req LoginRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}
	
	var user models.User
	result := config.DB.Where("username = ?", req.Username).First(&user)
	if result.Error != nil {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "用户名或密码错误"})
		return
	}
	
	if !utils.CheckPassword(req.Password, user.Password) {
		c.J<PERSON>(http.StatusUnauthorized, gin.H{"error": "用户名或密码错误"})
		return
	}
	
	token, err := utils.GenerateToken(user.ID, user.Username, user.Role)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "生成token失败"})
		return
	}
	
	user.Password = ""
	
	c.JSON(http.StatusOK, LoginResponse{
		Token: token,
		User:  user,
	})
}

func GetUserInfo(c *gin.Context) {
	userID, _ := c.Get("user_id")
	
	var user models.User
	result := config.DB.First(&user, userID)
	if result.Error != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "用户不存在"})
		return
	}
	
	user.Password = ""
	c.JSON(http.StatusOK, user)
}