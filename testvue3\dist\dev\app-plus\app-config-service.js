
  ;(function(){
  let u=void 0,isReady=false,onReadyCallbacks=[],isServiceReady=false,onServiceReadyCallbacks=[];
  const __uniConfig = {"pages":[],"globalStyle":{"backgroundColor":"#f5f5f5","navigationBar":{"backgroundColor":"#2196F3","titleText":"织厂管理系统","type":"default","titleColor":"#ffffff"},"isNVue":false},"nvue":{"compiler":"uni-app","styleCompiler":"uni-app","flex-direction":"column"},"renderer":"auto","appname":"testvue3","splashscreen":{"alwaysShowBeforeRender":true,"autoclose":true},"compilerVersion":"4.29","entryPagePath":"pages/login/login","entryPageQuery":"","realEntryPagePath":"","networkTimeout":{"request":60000,"connectSocket":60000,"uploadFile":60000,"downloadFile":60000},"tabBar":{"position":"bottom","color":"#7A7E83","selectedColor":"#2196F3","borderStyle":"black","blurEffect":"none","fontSize":"10px","iconWidth":"24px","spacing":"3px","height":"50px","list":[{"pagePath":"pages/workspace/workspace","text":"工作台","iconPath":"/static/tabbar/home_1.png","selectedIconPath":"/static/tabbar/home_2.png"},{"pagePath":"pages/profile/profile","text":"我的","iconPath":"/static/tabbar/my_1.png","selectedIconPath":"/static/tabbar/my_2.png"}],"backgroundColor":"#ffffff","selectedIndex":0,"shown":true},"locales":{},"darkmode":false,"themeConfig":{}};
  const __uniRoutes = [{"path":"pages/login/login","meta":{"isQuit":true,"isEntry":true,"navigationBar":{"titleText":"织厂管理系统","style":"custom","type":"default"},"isNVue":false}},{"path":"pages/workspace/workspace","meta":{"isQuit":true,"isTabBar":true,"tabBarIndex":0,"navigationBar":{"backgroundColor":"#2196F3","titleText":"工作台","type":"default","titleColor":"#ffffff"},"isNVue":false}},{"path":"pages/profile/profile","meta":{"isQuit":true,"isTabBar":true,"tabBarIndex":1,"navigationBar":{"backgroundColor":"#2196F3","titleText":"我的","type":"default","titleColor":"#ffffff"},"isNVue":false}},{"path":"pages/worker/home","meta":{"navigationBar":{"backgroundColor":"#2196F3","titleText":"织工工作台","type":"default","titleColor":"#ffffff"},"isNVue":false}},{"path":"pages/mechanic/home","meta":{"navigationBar":{"backgroundColor":"#2196F3","titleText":"机修工作台","type":"default","titleColor":"#ffffff"},"isNVue":false}},{"path":"pages/report/report","meta":{"navigationBar":{"backgroundColor":"#2196F3","titleText":"异常上报","type":"default","titleColor":"#ffffff"},"isNVue":false}},{"path":"pages/repair/repair","meta":{"navigationBar":{"backgroundColor":"#2196F3","titleText":"维修操作","type":"default","titleColor":"#ffffff"},"isNVue":false}},{"path":"pages/test/scanner","meta":{"navigationBar":{"backgroundColor":"#2196F3","titleText":"扫码测试","type":"default","titleColor":"#ffffff"},"isNVue":false}}].map(uniRoute=>(uniRoute.meta.route=uniRoute.path,__uniConfig.pages.push(uniRoute.path),uniRoute.path='/'+uniRoute.path,uniRoute));
  __uniConfig.styles=[];//styles
  __uniConfig.onReady=function(callback){if(__uniConfig.ready){callback()}else{onReadyCallbacks.push(callback)}};Object.defineProperty(__uniConfig,"ready",{get:function(){return isReady},set:function(val){isReady=val;if(!isReady){return}const callbacks=onReadyCallbacks.slice(0);onReadyCallbacks.length=0;callbacks.forEach(function(callback){callback()})}});
  __uniConfig.onServiceReady=function(callback){if(__uniConfig.serviceReady){callback()}else{onServiceReadyCallbacks.push(callback)}};Object.defineProperty(__uniConfig,"serviceReady",{get:function(){return isServiceReady},set:function(val){isServiceReady=val;if(!isServiceReady){return}const callbacks=onServiceReadyCallbacks.slice(0);onServiceReadyCallbacks.length=0;callbacks.forEach(function(callback){callback()})}});
  service.register("uni-app-config",{create(a,b,c){if(!__uniConfig.viewport){var d=b.weex.config.env.scale,e=b.weex.config.env.deviceWidth,f=Math.ceil(e/d);Object.assign(__uniConfig,{viewport:f,defaultFontSize:16})}return{instance:{__uniConfig:__uniConfig,__uniRoutes:__uniRoutes,global:u,window:u,document:u,frames:u,self:u,location:u,navigator:u,localStorage:u,history:u,Caches:u,screen:u,alert:u,confirm:u,prompt:u,fetch:u,XMLHttpRequest:u,WebSocket:u,webkit:u,print:u}}}}); 
  })();
  