# Security Policy

## Supported Versions

We support the past two Go releases (for example, Go 1.17.x and Go 1.18.x when Go 1.18.x is the latest stable release).

See https://go.dev/wiki/Go-Release-Cycle and in particular the
[Release Maintenance](https://go.dev/wiki/Go-Release-Cycle#release-maintenance)
part of that page.

## Reporting a Vulnerability

See https://go.dev/security for how to report a vulnerability.
