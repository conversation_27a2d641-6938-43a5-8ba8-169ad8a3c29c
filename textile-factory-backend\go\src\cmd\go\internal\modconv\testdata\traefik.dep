# This file is autogenerated, do not edit; changes may be undone by the next 'dep ensure'.

[[projects]]
  name = "github.com/Nvveen/Gotty"
  packages = ["."]
  revision = "a8b993ba6abdb0e0c12b0125c603323a71c7790c"
  source = "github.com/ijc25/Gotty"

[[projects]]
  branch = "master"
  name = "github.com/OpenDNS/vegadns2client"
  packages = ["."]
  revision = "a3fa4a771d87bda2514a90a157e1fed1b6897d2e"

[[projects]]
  name = "github.com/PuerkitoBio/purell"
  packages = ["."]
  revision = "8a290539e2e8629dbc4e6bad948158f790ec31f4"
  version = "v1.0.0"

[[projects]]
  name = "github.com/PuerkitoBio/urlesc"
  packages = ["."]
  revision = "5bd2802263f21d8788851d5305584c82a5c75d7e"

[[projects]]
  name = "github.com/Shopify/sarama"
  packages = ["."]
  revision = "70f6a705d4a17af059acbc6946fb2bd30762acd7"

[[projects]]
  name = "github.com/VividCortex/gohistogram"
  packages = ["."]
  revision = "51564d9861991fb0ad0f531c99ef602d0f9866e6"
  version = "v1.0.0"

[[projects]]
  branch = "containous-fork"
  name = "github.com/abbot/go-http-auth"
  packages = ["."]
  revision = "65b0cdae8d7fe5c05c7430e055938ef6d24a66c9"
  source = "github.com/containous/go-http-auth"

[[projects]]
  branch = "master"
  name = "github.com/abronan/valkeyrie"
  packages = [
    ".",
    "store",
    "store/boltdb",
    "store/consul",
    "store/etcd/v2",
    "store/etcd/v3",
    "store/zookeeper"
  ]
  revision = "063d875e3c5fd734fa2aa12fac83829f62acfc70"
  
[[projects]]
  branch = "master"
  name = "github.com/mesosphere/mesos-dns"
  packages = [
    "detect",
    "errorutil",
    "logging",
    "models",
    "records",
    "records/labels",
    "records/state",
    "util"
  ]
  revision = "b47dc4c19f215e98da687b15b4c64e70f629bea5"
  source = "**************:containous/mesos-dns.git"

  [[projects]]
  name = "gopkg.in/fsnotify.v1"
  packages = ["."]
  revision = "629574ca2a5df945712d3079857300b5e4da0236"
  source = "github.com/fsnotify/fsnotify"
  version = "v1.4.2"