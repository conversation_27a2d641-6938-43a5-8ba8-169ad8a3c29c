  src/cmd/compile/internal/ssa/testdata/scopes.go
22:	func test() {
23:		x := id(0)
24:		y := id(0)
25:		fmt.Println(x)
0:
27:			x := i * i
28:			y += id(x) //gdb-dbg=(x,y)//gdb-opt=(x,y)
x = 0
y = 0
26:		for i := x; i < 3; i++ {
27:			x := i * i
28:			y += id(x) //gdb-dbg=(x,y)//gdb-opt=(x,y)
x = 1
y = 0
26:		for i := x; i < 3; i++ {
27:			x := i * i
28:			y += id(x) //gdb-dbg=(x,y)//gdb-opt=(x,y)
x = 4
y = 1
26:		for i := x; i < 3; i++ {
30:		y = x + y //gdb-dbg=(x,y)//gdb-opt=(x,y)
x = 0
y = 5
31:		fmt.Println(x, y)
0: 5
34:			a := y
35:			f1(a)
37:				b := 0
38:				f2(b)
39:				if gretbool() {
40:					c := 0
41:					f3(c)
46:				f5(b)
48:			f6(a)
33:		for x := 0; x <= 1; x++ { // From delve scopetest.go
34:			a := y
35:			f1(a)
37:				b := 0
38:				f2(b)
39:				if gretbool() {
43:					c := 1.1
44:					f4(int(c))
46:				f5(b)
48:			f6(a)
33:		for x := 0; x <= 1; x++ { // From delve scopetest.go
53:				j = id(1)
54:				f = id(2)
56:			for i := 0; i <= 5; i++ {
57:				j += j * (j ^ 3) / 100
58:				if i == f {
62:				sleepytime()
56:			for i := 0; i <= 5; i++ {
57:				j += j * (j ^ 3) / 100
58:				if i == f {
62:				sleepytime()
56:			for i := 0; i <= 5; i++ {
57:				j += j * (j ^ 3) / 100
58:				if i == f {
59:					fmt.Println("foo")
60:					break
64:			helloworld()
66:	}
15:	}
