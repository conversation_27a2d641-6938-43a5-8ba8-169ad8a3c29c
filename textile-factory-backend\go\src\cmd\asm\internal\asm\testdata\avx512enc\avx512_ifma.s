// Code generated by avx512test. DO NOT EDIT.

#include "../../../../../../runtime/textflag.h"

TEXT asmtest_avx512_ifma(SB), NOSPLIT, $0
	VPMADD52HUQ X7, X11, K1, X18                       // 62e2a509b5d7
	VPMADD52HUQ X0, X11, K1, X18                       // 62e2a509b5d0
	VPMADD52HUQ 17(SP)(BP*2), X11, K1, X18             // 62e2a509b5946c11000000
	VPMADD52HUQ -7(DI)(R8*4), X11, K1, X18             // 62a2a509b59487f9ffffff
	VPMADD52HUQ X7, X31, K1, X18                       // 62e28501b5d7
	VPMADD52HUQ X0, X31, K1, X18                       // 62e28501b5d0
	VPMADD52HUQ 17(SP)(BP*2), X31, K1, X18             // 62e28501b5946c11000000
	VPMADD52HUQ -7(DI)(R8*4), X31, K1, X18             // 62a28501b59487f9ffffff
	VPMADD52HUQ X7, X3, K1, X18                        // 62e2e509b5d7
	VPMADD52HUQ X0, X3, K1, X18                        // 62e2e509b5d0
	VPMADD52HUQ 17(SP)(BP*2), X3, K1, X18              // 62e2e509b5946c11000000
	VPMADD52HUQ -7(DI)(R8*4), X3, K1, X18              // 62a2e509b59487f9ffffff
	VPMADD52HUQ X7, X11, K1, X21                       // 62e2a509b5ef
	VPMADD52HUQ X0, X11, K1, X21                       // 62e2a509b5e8
	VPMADD52HUQ 17(SP)(BP*2), X11, K1, X21             // 62e2a509b5ac6c11000000
	VPMADD52HUQ -7(DI)(R8*4), X11, K1, X21             // 62a2a509b5ac87f9ffffff
	VPMADD52HUQ X7, X31, K1, X21                       // 62e28501b5ef
	VPMADD52HUQ X0, X31, K1, X21                       // 62e28501b5e8
	VPMADD52HUQ 17(SP)(BP*2), X31, K1, X21             // 62e28501b5ac6c11000000
	VPMADD52HUQ -7(DI)(R8*4), X31, K1, X21             // 62a28501b5ac87f9ffffff
	VPMADD52HUQ X7, X3, K1, X21                        // 62e2e509b5ef
	VPMADD52HUQ X0, X3, K1, X21                        // 62e2e509b5e8
	VPMADD52HUQ 17(SP)(BP*2), X3, K1, X21              // 62e2e509b5ac6c11000000
	VPMADD52HUQ -7(DI)(R8*4), X3, K1, X21              // 62a2e509b5ac87f9ffffff
	VPMADD52HUQ X7, X11, K1, X1                        // 62f2a509b5cf
	VPMADD52HUQ X0, X11, K1, X1                        // 62f2a509b5c8
	VPMADD52HUQ 17(SP)(BP*2), X11, K1, X1              // 62f2a509b58c6c11000000
	VPMADD52HUQ -7(DI)(R8*4), X11, K1, X1              // 62b2a509b58c87f9ffffff
	VPMADD52HUQ X7, X31, K1, X1                        // 62f28501b5cf
	VPMADD52HUQ X0, X31, K1, X1                        // 62f28501b5c8
	VPMADD52HUQ 17(SP)(BP*2), X31, K1, X1              // 62f28501b58c6c11000000
	VPMADD52HUQ -7(DI)(R8*4), X31, K1, X1              // 62b28501b58c87f9ffffff
	VPMADD52HUQ X7, X3, K1, X1                         // 62f2e509b5cf
	VPMADD52HUQ X0, X3, K1, X1                         // 62f2e509b5c8
	VPMADD52HUQ 17(SP)(BP*2), X3, K1, X1               // 62f2e509b58c6c11000000
	VPMADD52HUQ -7(DI)(R8*4), X3, K1, X1               // 62b2e509b58c87f9ffffff
	VPMADD52HUQ Y28, Y31, K7, Y17                      // 62828527b5cc
	VPMADD52HUQ Y13, Y31, K7, Y17                      // 62c28527b5cd
	VPMADD52HUQ Y7, Y31, K7, Y17                       // 62e28527b5cf
	VPMADD52HUQ (R8), Y31, K7, Y17                     // 62c28527b508
	VPMADD52HUQ 15(DX)(BX*2), Y31, K7, Y17             // 62e28527b58c5a0f000000
	VPMADD52HUQ Y28, Y8, K7, Y17                       // 6282bd2fb5cc
	VPMADD52HUQ Y13, Y8, K7, Y17                       // 62c2bd2fb5cd
	VPMADD52HUQ Y7, Y8, K7, Y17                        // 62e2bd2fb5cf
	VPMADD52HUQ (R8), Y8, K7, Y17                      // 62c2bd2fb508
	VPMADD52HUQ 15(DX)(BX*2), Y8, K7, Y17              // 62e2bd2fb58c5a0f000000
	VPMADD52HUQ Y28, Y1, K7, Y17                       // 6282f52fb5cc
	VPMADD52HUQ Y13, Y1, K7, Y17                       // 62c2f52fb5cd
	VPMADD52HUQ Y7, Y1, K7, Y17                        // 62e2f52fb5cf
	VPMADD52HUQ (R8), Y1, K7, Y17                      // 62c2f52fb508
	VPMADD52HUQ 15(DX)(BX*2), Y1, K7, Y17              // 62e2f52fb58c5a0f000000
	VPMADD52HUQ Y28, Y31, K7, Y7                       // 62928527b5fc
	VPMADD52HUQ Y13, Y31, K7, Y7                       // 62d28527b5fd
	VPMADD52HUQ Y7, Y31, K7, Y7                        // 62f28527b5ff
	VPMADD52HUQ (R8), Y31, K7, Y7                      // 62d28527b538
	VPMADD52HUQ 15(DX)(BX*2), Y31, K7, Y7              // 62f28527b5bc5a0f000000
	VPMADD52HUQ Y28, Y8, K7, Y7                        // 6292bd2fb5fc
	VPMADD52HUQ Y13, Y8, K7, Y7                        // 62d2bd2fb5fd
	VPMADD52HUQ Y7, Y8, K7, Y7                         // 62f2bd2fb5ff
	VPMADD52HUQ (R8), Y8, K7, Y7                       // 62d2bd2fb538
	VPMADD52HUQ 15(DX)(BX*2), Y8, K7, Y7               // 62f2bd2fb5bc5a0f000000
	VPMADD52HUQ Y28, Y1, K7, Y7                        // 6292f52fb5fc
	VPMADD52HUQ Y13, Y1, K7, Y7                        // 62d2f52fb5fd
	VPMADD52HUQ Y7, Y1, K7, Y7                         // 62f2f52fb5ff
	VPMADD52HUQ (R8), Y1, K7, Y7                       // 62d2f52fb538
	VPMADD52HUQ 15(DX)(BX*2), Y1, K7, Y7               // 62f2f52fb5bc5a0f000000
	VPMADD52HUQ Y28, Y31, K7, Y9                       // 62128527b5cc
	VPMADD52HUQ Y13, Y31, K7, Y9                       // 62528527b5cd
	VPMADD52HUQ Y7, Y31, K7, Y9                        // 62728527b5cf
	VPMADD52HUQ (R8), Y31, K7, Y9                      // 62528527b508
	VPMADD52HUQ 15(DX)(BX*2), Y31, K7, Y9              // 62728527b58c5a0f000000
	VPMADD52HUQ Y28, Y8, K7, Y9                        // 6212bd2fb5cc
	VPMADD52HUQ Y13, Y8, K7, Y9                        // 6252bd2fb5cd
	VPMADD52HUQ Y7, Y8, K7, Y9                         // 6272bd2fb5cf
	VPMADD52HUQ (R8), Y8, K7, Y9                       // 6252bd2fb508
	VPMADD52HUQ 15(DX)(BX*2), Y8, K7, Y9               // 6272bd2fb58c5a0f000000
	VPMADD52HUQ Y28, Y1, K7, Y9                        // 6212f52fb5cc
	VPMADD52HUQ Y13, Y1, K7, Y9                        // 6252f52fb5cd
	VPMADD52HUQ Y7, Y1, K7, Y9                         // 6272f52fb5cf
	VPMADD52HUQ (R8), Y1, K7, Y9                       // 6252f52fb508
	VPMADD52HUQ 15(DX)(BX*2), Y1, K7, Y9               // 6272f52fb58c5a0f000000
	VPMADD52HUQ Z23, Z23, K1, Z27                      // 6222c541b5df
	VPMADD52HUQ Z6, Z23, K1, Z27                       // 6262c541b5de
	VPMADD52HUQ 17(SP), Z23, K1, Z27                   // 6262c541b59c2411000000
	VPMADD52HUQ -17(BP)(SI*4), Z23, K1, Z27            // 6262c541b59cb5efffffff
	VPMADD52HUQ Z23, Z5, K1, Z27                       // 6222d549b5df
	VPMADD52HUQ Z6, Z5, K1, Z27                        // 6262d549b5de
	VPMADD52HUQ 17(SP), Z5, K1, Z27                    // 6262d549b59c2411000000
	VPMADD52HUQ -17(BP)(SI*4), Z5, K1, Z27             // 6262d549b59cb5efffffff
	VPMADD52HUQ Z23, Z23, K1, Z15                      // 6232c541b5ff
	VPMADD52HUQ Z6, Z23, K1, Z15                       // 6272c541b5fe
	VPMADD52HUQ 17(SP), Z23, K1, Z15                   // 6272c541b5bc2411000000
	VPMADD52HUQ -17(BP)(SI*4), Z23, K1, Z15            // 6272c541b5bcb5efffffff
	VPMADD52HUQ Z23, Z5, K1, Z15                       // 6232d549b5ff
	VPMADD52HUQ Z6, Z5, K1, Z15                        // 6272d549b5fe
	VPMADD52HUQ 17(SP), Z5, K1, Z15                    // 6272d549b5bc2411000000
	VPMADD52HUQ -17(BP)(SI*4), Z5, K1, Z15             // 6272d549b5bcb5efffffff
	VPMADD52LUQ X5, X9, K1, X24                        // 6262b509b4c5
	VPMADD52LUQ X31, X9, K1, X24                       // 6202b509b4c7
	VPMADD52LUQ X3, X9, K1, X24                        // 6262b509b4c3
	VPMADD52LUQ 15(R8), X9, K1, X24                    // 6242b509b4800f000000
	VPMADD52LUQ (BP), X9, K1, X24                      // 6262b509b44500
	VPMADD52LUQ X5, X7, K1, X24                        // 6262c509b4c5
	VPMADD52LUQ X31, X7, K1, X24                       // 6202c509b4c7
	VPMADD52LUQ X3, X7, K1, X24                        // 6262c509b4c3
	VPMADD52LUQ 15(R8), X7, K1, X24                    // 6242c509b4800f000000
	VPMADD52LUQ (BP), X7, K1, X24                      // 6262c509b44500
	VPMADD52LUQ X5, X14, K1, X24                       // 62628d09b4c5
	VPMADD52LUQ X31, X14, K1, X24                      // 62028d09b4c7
	VPMADD52LUQ X3, X14, K1, X24                       // 62628d09b4c3
	VPMADD52LUQ 15(R8), X14, K1, X24                   // 62428d09b4800f000000
	VPMADD52LUQ (BP), X14, K1, X24                     // 62628d09b44500
	VPMADD52LUQ X5, X9, K1, X20                        // 62e2b509b4e5
	VPMADD52LUQ X31, X9, K1, X20                       // 6282b509b4e7
	VPMADD52LUQ X3, X9, K1, X20                        // 62e2b509b4e3
	VPMADD52LUQ 15(R8), X9, K1, X20                    // 62c2b509b4a00f000000
	VPMADD52LUQ (BP), X9, K1, X20                      // 62e2b509b46500
	VPMADD52LUQ X5, X7, K1, X20                        // 62e2c509b4e5
	VPMADD52LUQ X31, X7, K1, X20                       // 6282c509b4e7
	VPMADD52LUQ X3, X7, K1, X20                        // 62e2c509b4e3
	VPMADD52LUQ 15(R8), X7, K1, X20                    // 62c2c509b4a00f000000
	VPMADD52LUQ (BP), X7, K1, X20                      // 62e2c509b46500
	VPMADD52LUQ X5, X14, K1, X20                       // 62e28d09b4e5
	VPMADD52LUQ X31, X14, K1, X20                      // 62828d09b4e7
	VPMADD52LUQ X3, X14, K1, X20                       // 62e28d09b4e3
	VPMADD52LUQ 15(R8), X14, K1, X20                   // 62c28d09b4a00f000000
	VPMADD52LUQ (BP), X14, K1, X20                     // 62e28d09b46500
	VPMADD52LUQ X5, X9, K1, X7                         // 62f2b509b4fd
	VPMADD52LUQ X31, X9, K1, X7                        // 6292b509b4ff
	VPMADD52LUQ X3, X9, K1, X7                         // 62f2b509b4fb
	VPMADD52LUQ 15(R8), X9, K1, X7                     // 62d2b509b4b80f000000
	VPMADD52LUQ (BP), X9, K1, X7                       // 62f2b509b47d00
	VPMADD52LUQ X5, X7, K1, X7                         // 62f2c509b4fd
	VPMADD52LUQ X31, X7, K1, X7                        // 6292c509b4ff
	VPMADD52LUQ X3, X7, K1, X7                         // 62f2c509b4fb
	VPMADD52LUQ 15(R8), X7, K1, X7                     // 62d2c509b4b80f000000
	VPMADD52LUQ (BP), X7, K1, X7                       // 62f2c509b47d00
	VPMADD52LUQ X5, X14, K1, X7                        // 62f28d09b4fd
	VPMADD52LUQ X31, X14, K1, X7                       // 62928d09b4ff
	VPMADD52LUQ X3, X14, K1, X7                        // 62f28d09b4fb
	VPMADD52LUQ 15(R8), X14, K1, X7                    // 62d28d09b4b80f000000
	VPMADD52LUQ (BP), X14, K1, X7                      // 62f28d09b47d00
	VPMADD52LUQ Y3, Y9, K1, Y2                         // 62f2b529b4d3
	VPMADD52LUQ Y2, Y9, K1, Y2                         // 62f2b529b4d2
	VPMADD52LUQ Y9, Y9, K1, Y2                         // 62d2b529b4d1
	VPMADD52LUQ 17(SP)(BP*1), Y9, K1, Y2               // 62f2b529b4942c11000000
	VPMADD52LUQ -7(CX)(DX*8), Y9, K1, Y2               // 62f2b529b494d1f9ffffff
	VPMADD52LUQ Y3, Y1, K1, Y2                         // 62f2f529b4d3
	VPMADD52LUQ Y2, Y1, K1, Y2                         // 62f2f529b4d2
	VPMADD52LUQ Y9, Y1, K1, Y2                         // 62d2f529b4d1
	VPMADD52LUQ 17(SP)(BP*1), Y1, K1, Y2               // 62f2f529b4942c11000000
	VPMADD52LUQ -7(CX)(DX*8), Y1, K1, Y2               // 62f2f529b494d1f9ffffff
	VPMADD52LUQ Y3, Y9, K1, Y21                        // 62e2b529b4eb
	VPMADD52LUQ Y2, Y9, K1, Y21                        // 62e2b529b4ea
	VPMADD52LUQ Y9, Y9, K1, Y21                        // 62c2b529b4e9
	VPMADD52LUQ 17(SP)(BP*1), Y9, K1, Y21              // 62e2b529b4ac2c11000000
	VPMADD52LUQ -7(CX)(DX*8), Y9, K1, Y21              // 62e2b529b4acd1f9ffffff
	VPMADD52LUQ Y3, Y1, K1, Y21                        // 62e2f529b4eb
	VPMADD52LUQ Y2, Y1, K1, Y21                        // 62e2f529b4ea
	VPMADD52LUQ Y9, Y1, K1, Y21                        // 62c2f529b4e9
	VPMADD52LUQ 17(SP)(BP*1), Y1, K1, Y21              // 62e2f529b4ac2c11000000
	VPMADD52LUQ -7(CX)(DX*8), Y1, K1, Y21              // 62e2f529b4acd1f9ffffff
	VPMADD52LUQ Y3, Y9, K1, Y12                        // 6272b529b4e3
	VPMADD52LUQ Y2, Y9, K1, Y12                        // 6272b529b4e2
	VPMADD52LUQ Y9, Y9, K1, Y12                        // 6252b529b4e1
	VPMADD52LUQ 17(SP)(BP*1), Y9, K1, Y12              // 6272b529b4a42c11000000
	VPMADD52LUQ -7(CX)(DX*8), Y9, K1, Y12              // 6272b529b4a4d1f9ffffff
	VPMADD52LUQ Y3, Y1, K1, Y12                        // 6272f529b4e3
	VPMADD52LUQ Y2, Y1, K1, Y12                        // 6272f529b4e2
	VPMADD52LUQ Y9, Y1, K1, Y12                        // 6252f529b4e1
	VPMADD52LUQ 17(SP)(BP*1), Y1, K1, Y12              // 6272f529b4a42c11000000
	VPMADD52LUQ -7(CX)(DX*8), Y1, K1, Y12              // 6272f529b4a4d1f9ffffff
	VPMADD52LUQ Z16, Z21, K7, Z8                       // 6232d547b4c0
	VPMADD52LUQ Z13, Z21, K7, Z8                       // 6252d547b4c5
	VPMADD52LUQ 7(AX), Z21, K7, Z8                     // 6272d547b48007000000
	VPMADD52LUQ (DI), Z21, K7, Z8                      // 6272d547b407
	VPMADD52LUQ Z16, Z5, K7, Z8                        // 6232d54fb4c0
	VPMADD52LUQ Z13, Z5, K7, Z8                        // 6252d54fb4c5
	VPMADD52LUQ 7(AX), Z5, K7, Z8                      // 6272d54fb48007000000
	VPMADD52LUQ (DI), Z5, K7, Z8                       // 6272d54fb407
	VPMADD52LUQ Z16, Z21, K7, Z28                      // 6222d547b4e0
	VPMADD52LUQ Z13, Z21, K7, Z28                      // 6242d547b4e5
	VPMADD52LUQ 7(AX), Z21, K7, Z28                    // 6262d547b4a007000000
	VPMADD52LUQ (DI), Z21, K7, Z28                     // 6262d547b427
	VPMADD52LUQ Z16, Z5, K7, Z28                       // 6222d54fb4e0
	VPMADD52LUQ Z13, Z5, K7, Z28                       // 6242d54fb4e5
	VPMADD52LUQ 7(AX), Z5, K7, Z28                     // 6262d54fb4a007000000
	VPMADD52LUQ (DI), Z5, K7, Z28                      // 6262d54fb427
	RET
