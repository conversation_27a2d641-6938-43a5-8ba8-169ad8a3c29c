env GO111MODULE=off

# Integration test for cache directory calculation (cmd/go/internal/cache).

[GOOS:windows] skip 'windows does not use XDG_CACHE_HOME'
[GOOS:darwin]  skip 'dar<PERSON> does not use XDG_CACHE_HOME'
[GOOS:ios]     skip 'ios does not use XDG_CACHE_HOME'
[GOOS:plan9]   skip 'plan9 does not use XDG_CACHE_HOME'

mkdir $WORK/gocache
mkdir $WORK/xdg
mkdir $WORK/home

# Set GOCACHE, XDG_CACHE_HOME, and HOME.
env GOCACHE=$WORK/gocache
env XDG_CACHE_HOME=$WORK/xdg
env HOME=$WORK/home

# With all three set, we should prefer GOCACHE.
go env GOCACHE
stdout $WORK'/gocache$'

# Without GOCACHE, we should prefer XDG_CACHE_HOME over HOME.
env GOCACHE=
go env GOCACHE
stdout $WORK'/xdg/go-build$$'

# With only HOME set, we should use $HOME/.cache.
env XDG_CACHE_HOME=
go env GOCACHE
stdout $WORK'/home/<USER>/go-build$'

# With no guidance from the environment, we must disable the cache, but that
# should not cause commands that do not write to the cache to fail.
env HOME=
go env GOCACHE
stdout 'off'
