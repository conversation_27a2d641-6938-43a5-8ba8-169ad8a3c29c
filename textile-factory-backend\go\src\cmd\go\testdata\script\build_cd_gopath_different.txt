[compiler:gccgo] skip 'gccgo does not support -ldflags -X'
env GO111MODULE=off
go build run_go.go

# Apply identity function to GOPATH
exec ./run_go$GOEXE $GOPATH/src/my.pkg/main $GOPATH IDENTITY build -o $WORK/tmp/a.exe -ldflags -X=my.pkg.Text=linkXworked
exec $WORK/tmp/a.exe
stderr 'linkXworked'
rm $WORK/tmp/a.exe

[!GOOS:windows] stop 'rest of the tests only apply to Windows'

# Replace '\' with '/' in GOPATH
exec ./run_go$GOEXE $GOPATH/src/my.pkg/main $GOPATH REPLACE_SLASH build -o $WORK/tmp/a.exe -ldflags -X=my.pkg.Text=linkXworked
exec $WORK/tmp/a.exe
stderr 'linkXworked'
rm $WORK/tmp/a.exe

# Apply identity function to GOPATH
exec ./run_go$GOEXE $GOPATH/src/my.pkg/main $GOPATH UPPER build -o $WORK/tmp/a.exe -ldflags -X=my.pkg.Text=linkXworked
exec $WORK/tmp/a.exe
stderr 'linkXworked'
rm $WORK/tmp/a.exe

# Apply identity function to GOPATH
exec ./run_go$GOEXE $GOPATH/src/my.pkg/main $GOPATH LOWER build -o $WORK/tmp/a.exe -ldflags -X=my.pkg.Text=linkXworked
exec $WORK/tmp/a.exe
stderr 'linkXworked'
rm $WORK/tmp/a.exe

-- run_go.go --
package main

import (
	"fmt"
	"os"
	"os/exec"
	"strings"
)

func main() {
	dir := os.Args[1]
	gopath := os.Args[2]
	switch os.Args[3] {
		case "IDENTITY":
		case "REPLACE_SLASH": gopath = strings.ReplaceAll(gopath, `\`, `/`)
		case "UPPER": gopath = strings.ToUpper(gopath)
		case "LOWER": gopath = strings.ToLower(gopath)
		default: fmt.Fprintln(os.Stderr, "bad op"); os.Exit(1)
	}
	cmd := exec.Command("go", os.Args[4:]...)
	cmd.Dir = dir
	cmd.Env = append(os.Environ(), "GOPATH="+gopath)
	cmd.Stdout = os.Stdout
	cmd.Stderr = os.Stderr
	if err := cmd.Run(); err != nil {
		fmt.Fprintln(os.Stderr, err)
		os.Exit(1)
	}
}

-- my.pkg/main/main.go --
package main

import "my.pkg"

func main() {
	println(pkg.Text)
}
-- my.pkg/pkg.go --
package pkg

var Text = "unset"
