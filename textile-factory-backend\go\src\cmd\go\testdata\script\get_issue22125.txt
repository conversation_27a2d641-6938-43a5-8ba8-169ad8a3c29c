# This test verifies a fix for a security issue; see https://go.dev/issue/22125.

[short] skip
[!git] skip
[!exec:svn] skip

env GO111MODULE=off

cd $GOPATH
! go get -u vcs-test.golang.org/go/test1-svn-git
stderr 'src'${/}'vcs-test.* uses git, but parent .*src'${/}'vcs-test.* uses svn'

[!case-sensitive] ! go get -u vcs-test.golang.org/go/test2-svn-git/test2main
[!case-sensitive] stderr 'src'${/}'vcs-test.* uses git, but parent .*src'${/}'vcs-test.* uses svn'
