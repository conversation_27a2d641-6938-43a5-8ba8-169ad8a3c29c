// Code generated by avx512test. DO NOT EDIT.

#include "../../../../../../runtime/textflag.h"

TEXT asmtest_avx512bw(SB), NOSPLIT, $0
	KADDD K4, K7, K5                                   // c4e1c54aec
	KADDD K6, K7, K5                                   // c4e1c54aee
	KADDD K4, K6, K5                                   // c4e1cd4aec
	KADDD K6, K6, K5                                   // c4e1cd4aee
	KADDD K4, K7, K4                                   // c4e1c54ae4
	KADDD K6, K7, K4                                   // c4e1c54ae6
	KADDD K4, K6, K4                                   // c4e1cd4ae4
	KADDD K6, K6, K4                                   // c4e1cd4ae6
	KADDQ K4, K5, K0                                   // c4e1d44ac4
	KADDQ K6, K5, K0                                   // c4e1d44ac6
	KADDQ K4, K4, K0                                   // c4e1dc4ac4
	KADDQ K6, K4, K0                                   // c4e1dc4ac6
	KADDQ K4, K5, K7                                   // c4e1d44afc
	KADDQ K6, K5, K7                                   // c4e1d44afe
	KADDQ K4, K4, K7                                   // c4e1dc4afc
	KADDQ K6, K4, K7                                   // c4e1dc4afe
	KANDD K1, K6, K0                                   // c4e1cd41c1
	KANDD K5, K6, K0                                   // c4e1cd41c5
	KANDD K1, K5, K0                                   // c4e1d541c1
	KANDD K5, K5, K0                                   // c4e1d541c5
	KANDD K1, K6, K5                                   // c4e1cd41e9
	KANDD K5, K6, K5                                   // c4e1cd41ed
	KANDD K1, K5, K5                                   // c4e1d541e9
	KANDD K5, K5, K5                                   // c4e1d541ed
	KANDND K5, K0, K4                                  // c4e1fd42e5
	KANDND K4, K0, K4                                  // c4e1fd42e4
	KANDND K5, K7, K4                                  // c4e1c542e5
	KANDND K4, K7, K4                                  // c4e1c542e4
	KANDND K5, K0, K6                                  // c4e1fd42f5
	KANDND K4, K0, K6                                  // c4e1fd42f4
	KANDND K5, K7, K6                                  // c4e1c542f5
	KANDND K4, K7, K6                                  // c4e1c542f4
	KANDNQ K6, K1, K4                                  // c4e1f442e6
	KANDNQ K7, K1, K4                                  // c4e1f442e7
	KANDNQ K6, K3, K4                                  // c4e1e442e6
	KANDNQ K7, K3, K4                                  // c4e1e442e7
	KANDNQ K6, K1, K6                                  // c4e1f442f6
	KANDNQ K7, K1, K6                                  // c4e1f442f7
	KANDNQ K6, K3, K6                                  // c4e1e442f6
	KANDNQ K7, K3, K6                                  // c4e1e442f7
	KANDQ K6, K0, K2                                   // c4e1fc41d6
	KANDQ K5, K0, K2                                   // c4e1fc41d5
	KANDQ K6, K5, K2                                   // c4e1d441d6
	KANDQ K5, K5, K2                                   // c4e1d441d5
	KANDQ K6, K0, K7                                   // c4e1fc41fe
	KANDQ K5, K0, K7                                   // c4e1fc41fd
	KANDQ K6, K5, K7                                   // c4e1d441fe
	KANDQ K5, K5, K7                                   // c4e1d441fd
	KMOVD K1, 17(SP)                                   // c4e1f9914c2411
	KMOVD K3, 17(SP)                                   // c4e1f9915c2411
	KMOVD K1, -17(BP)(SI*4)                            // c4e1f9914cb5ef
	KMOVD K3, -17(BP)(SI*4)                            // c4e1f9915cb5ef
	KMOVD K6, R14                                      // c57b93f6
	KMOVD K7, R14                                      // c57b93f7
	KMOVD K6, AX                                       // c5fb93c6
	KMOVD K7, AX                                       // c5fb93c7
	KMOVD K4, K6                                       // c4e1f990f4
	KMOVD K6, K6                                       // c4e1f990f6
	KMOVD 7(AX), K6                                    // c4e1f9907007
	KMOVD (DI), K6                                     // c4e1f99037
	KMOVD K4, K4                                       // c4e1f990e4
	KMOVD K6, K4                                       // c4e1f990e6
	KMOVD 7(AX), K4                                    // c4e1f9906007
	KMOVD (DI), K4                                     // c4e1f99027
	KMOVD R9, K4                                       // c4c17b92e1
	KMOVD CX, K4                                       // c5fb92e1
	KMOVD R9, K5                                       // c4c17b92e9
	KMOVD CX, K5                                       // c5fb92e9
	KMOVQ K2, 17(SP)                                   // c4e1f891542411
	KMOVQ K7, 17(SP)                                   // c4e1f8917c2411
	KMOVQ K2, -17(BP)(SI*4)                            // c4e1f89154b5ef
	KMOVQ K7, -17(BP)(SI*4)                            // c4e1f8917cb5ef
	KMOVQ K0, DX                                       // c4e1fb93d0
	KMOVQ K5, DX                                       // c4e1fb93d5
	KMOVQ K0, BP                                       // c4e1fb93e8
	KMOVQ K5, BP                                       // c4e1fb93ed
	KMOVQ K1, K6                                       // c4e1f890f1
	KMOVQ K5, K6                                       // c4e1f890f5
	KMOVQ 7(AX), K6                                    // c4e1f8907007
	KMOVQ (DI), K6                                     // c4e1f89037
	KMOVQ K1, K5                                       // c4e1f890e9
	KMOVQ K5, K5                                       // c4e1f890ed
	KMOVQ 7(AX), K5                                    // c4e1f8906807
	KMOVQ (DI), K5                                     // c4e1f8902f
	KMOVQ R10, K3                                      // c4c1fb92da
	KMOVQ CX, K3                                       // c4e1fb92d9
	KMOVQ R10, K1                                      // c4c1fb92ca
	KMOVQ CX, K1                                       // c4e1fb92c9
	KNOTD K6, K6                                       // c4e1f944f6
	KNOTD K4, K6                                       // c4e1f944f4
	KNOTD K6, K7                                       // c4e1f944fe
	KNOTD K4, K7                                       // c4e1f944fc
	KNOTQ K4, K4                                       // c4e1f844e4
	KNOTQ K5, K4                                       // c4e1f844e5
	KNOTQ K4, K6                                       // c4e1f844f4
	KNOTQ K5, K6                                       // c4e1f844f5
	KORD K4, K7, K5                                    // c4e1c545ec
	KORD K6, K7, K5                                    // c4e1c545ee
	KORD K4, K6, K5                                    // c4e1cd45ec
	KORD K6, K6, K5                                    // c4e1cd45ee
	KORD K4, K7, K4                                    // c4e1c545e4
	KORD K6, K7, K4                                    // c4e1c545e6
	KORD K4, K6, K4                                    // c4e1cd45e4
	KORD K6, K6, K4                                    // c4e1cd45e6
	KORQ K4, K5, K0                                    // c4e1d445c4
	KORQ K6, K5, K0                                    // c4e1d445c6
	KORQ K4, K4, K0                                    // c4e1dc45c4
	KORQ K6, K4, K0                                    // c4e1dc45c6
	KORQ K4, K5, K7                                    // c4e1d445fc
	KORQ K6, K5, K7                                    // c4e1d445fe
	KORQ K4, K4, K7                                    // c4e1dc45fc
	KORQ K6, K4, K7                                    // c4e1dc45fe
	KORTESTD K4, K6                                    // c4e1f998f4
	KORTESTD K6, K6                                    // c4e1f998f6
	KORTESTD K4, K4                                    // c4e1f998e4
	KORTESTD K6, K4                                    // c4e1f998e6
	KORTESTQ K2, K4                                    // c4e1f898e2
	KORTESTQ K7, K4                                    // c4e1f898e7
	KORTESTQ K2, K5                                    // c4e1f898ea
	KORTESTQ K7, K5                                    // c4e1f898ef
	KSHIFTLD $0, K5, K0                                // c4e37933c500
	KSHIFTLD $0, K4, K0                                // c4e37933c400
	KSHIFTLD $0, K5, K7                                // c4e37933fd00
	KSHIFTLD $0, K4, K7                                // c4e37933fc00
	KSHIFTLQ $97, K1, K4                               // c4e3f933e161
	KSHIFTLQ $97, K3, K4                               // c4e3f933e361
	KSHIFTLQ $97, K1, K6                               // c4e3f933f161
	KSHIFTLQ $97, K3, K6                               // c4e3f933f361
	KSHIFTRD $79, K0, K2                               // c4e37931d04f
	KSHIFTRD $79, K5, K2                               // c4e37931d54f
	KSHIFTRD $79, K0, K7                               // c4e37931f84f
	KSHIFTRD $79, K5, K7                               // c4e37931fd4f
	KSHIFTRQ $64, K1, K6                               // c4e3f931f140
	KSHIFTRQ $64, K5, K6                               // c4e3f931f540
	KSHIFTRQ $64, K1, K5                               // c4e3f931e940
	KSHIFTRQ $64, K5, K5                               // c4e3f931ed40
	KTESTD K5, K0                                      // c4e1f999c5
	KTESTD K4, K0                                      // c4e1f999c4
	KTESTD K5, K7                                      // c4e1f999fd
	KTESTD K4, K7                                      // c4e1f999fc
	KTESTQ K1, K4                                      // c4e1f899e1
	KTESTQ K3, K4                                      // c4e1f899e3
	KTESTQ K1, K6                                      // c4e1f899f1
	KTESTQ K3, K6                                      // c4e1f899f3
	KUNPCKDQ K1, K6, K0                                // c4e1cc4bc1
	KUNPCKDQ K5, K6, K0                                // c4e1cc4bc5
	KUNPCKDQ K1, K5, K0                                // c4e1d44bc1
	KUNPCKDQ K5, K5, K0                                // c4e1d44bc5
	KUNPCKDQ K1, K6, K5                                // c4e1cc4be9
	KUNPCKDQ K5, K6, K5                                // c4e1cc4bed
	KUNPCKDQ K1, K5, K5                                // c4e1d44be9
	KUNPCKDQ K5, K5, K5                                // c4e1d44bed
	KUNPCKWD K7, K5, K3                                // c5d44bdf
	KUNPCKWD K6, K5, K3                                // c5d44bde
	KUNPCKWD K7, K4, K3                                // c5dc4bdf
	KUNPCKWD K6, K4, K3                                // c5dc4bde
	KUNPCKWD K7, K5, K1                                // c5d44bcf
	KUNPCKWD K6, K5, K1                                // c5d44bce
	KUNPCKWD K7, K4, K1                                // c5dc4bcf
	KUNPCKWD K6, K4, K1                                // c5dc4bce
	KXNORD K6, K1, K4                                  // c4e1f546e6
	KXNORD K7, K1, K4                                  // c4e1f546e7
	KXNORD K6, K3, K4                                  // c4e1e546e6
	KXNORD K7, K3, K4                                  // c4e1e546e7
	KXNORD K6, K1, K6                                  // c4e1f546f6
	KXNORD K7, K1, K6                                  // c4e1f546f7
	KXNORD K6, K3, K6                                  // c4e1e546f6
	KXNORD K7, K3, K6                                  // c4e1e546f7
	KXNORQ K4, K4, K6                                  // c4e1dc46f4
	KXNORQ K5, K4, K6                                  // c4e1dc46f5
	KXNORQ K4, K6, K6                                  // c4e1cc46f4
	KXNORQ K5, K6, K6                                  // c4e1cc46f5
	KXNORQ K4, K4, K4                                  // c4e1dc46e4
	KXNORQ K5, K4, K4                                  // c4e1dc46e5
	KXNORQ K4, K6, K4                                  // c4e1cc46e4
	KXNORQ K5, K6, K4                                  // c4e1cc46e5
	KXORD K0, K4, K7                                   // c4e1dd47f8
	KXORD K7, K4, K7                                   // c4e1dd47ff
	KXORD K0, K6, K7                                   // c4e1cd47f8
	KXORD K7, K6, K7                                   // c4e1cd47ff
	KXORD K0, K4, K6                                   // c4e1dd47f0
	KXORD K7, K4, K6                                   // c4e1dd47f7
	KXORD K0, K6, K6                                   // c4e1cd47f0
	KXORD K7, K6, K6                                   // c4e1cd47f7
	KXORQ K1, K4, K5                                   // c4e1dc47e9
	KXORQ K3, K4, K5                                   // c4e1dc47eb
	KXORQ K1, K6, K5                                   // c4e1cc47e9
	KXORQ K3, K6, K5                                   // c4e1cc47eb
	KXORQ K1, K4, K4                                   // c4e1dc47e1
	KXORQ K3, K4, K4                                   // c4e1dc47e3
	KXORQ K1, K6, K4                                   // c4e1cc47e1
	KXORQ K3, K6, K4                                   // c4e1cc47e3
	VDBPSADBW $65, X15, X17, K3, X5                    // 62d3750342ef41
	VDBPSADBW $65, 7(AX)(CX*4), X17, K3, X5            // 62f3750342ac880700000041
	VDBPSADBW $65, 7(AX)(CX*1), X17, K3, X5            // 62f3750342ac080700000041
	VDBPSADBW $67, Y17, Y5, K4, Y19                    // 62a3552c42d943
	VDBPSADBW $67, 99(R15)(R15*2), Y5, K4, Y19         // 6283552c429c7f6300000043
	VDBPSADBW $67, -7(DI), Y5, K4, Y19                 // 62e3552c429ff9ffffff43
	VDBPSADBW $127, Z3, Z5, K2, Z19                    // 62e3554a42db7f
	VDBPSADBW $127, Z5, Z5, K2, Z19                    // 62e3554a42dd7f
	VDBPSADBW $127, 17(SP)(BP*1), Z5, K2, Z19          // 62e3554a429c2c110000007f
	VDBPSADBW $127, -7(CX)(DX*8), Z5, K2, Z19          // 62e3554a429cd1f9ffffff7f
	VDBPSADBW $127, Z3, Z1, K2, Z19                    // 62e3754a42db7f
	VDBPSADBW $127, Z5, Z1, K2, Z19                    // 62e3754a42dd7f
	VDBPSADBW $127, 17(SP)(BP*1), Z1, K2, Z19          // 62e3754a429c2c110000007f
	VDBPSADBW $127, -7(CX)(DX*8), Z1, K2, Z19          // 62e3754a429cd1f9ffffff7f
	VDBPSADBW $127, Z3, Z5, K2, Z15                    // 6273554a42fb7f
	VDBPSADBW $127, Z5, Z5, K2, Z15                    // 6273554a42fd7f
	VDBPSADBW $127, 17(SP)(BP*1), Z5, K2, Z15          // 6273554a42bc2c110000007f
	VDBPSADBW $127, -7(CX)(DX*8), Z5, K2, Z15          // 6273554a42bcd1f9ffffff7f
	VDBPSADBW $127, Z3, Z1, K2, Z15                    // 6273754a42fb7f
	VDBPSADBW $127, Z5, Z1, K2, Z15                    // 6273754a42fd7f
	VDBPSADBW $127, 17(SP)(BP*1), Z1, K2, Z15          // 6273754a42bc2c110000007f
	VDBPSADBW $127, -7(CX)(DX*8), Z1, K2, Z15          // 6273754a42bcd1f9ffffff7f
	VMOVDQU16 X14, K1, X16                             // 6231ff097ff0
	VMOVDQU16 X14, K1, -17(BP)(SI*2)                   // 6271ff097fb475efffffff
	VMOVDQU16 X14, K1, 7(AX)(CX*2)                     // 6271ff097fb44807000000
	VMOVDQU16 X14, K1, X11                             // 6251ff097ff3
	VMOVDQU16 15(R8)(R14*1), K1, X11                   // 6211ff096f9c300f000000
	VMOVDQU16 15(R8)(R14*2), K1, X11                   // 6211ff096f9c700f000000
	VMOVDQU16 Y24, K7, Y18                             // 6221ff2f7fc2
	VMOVDQU16 Y24, K7, 7(SI)(DI*4)                     // 6261ff2f7f84be07000000
	VMOVDQU16 Y24, K7, -7(DI)(R8*2)                    // 6221ff2f7f8447f9ffffff
	VMOVDQU16 Y11, K2, Y8                              // 6251ff2a7fd8
	VMOVDQU16 17(SP), K2, Y8                           // 6271ff2a6f842411000000
	VMOVDQU16 -17(BP)(SI*4), K2, Y8                    // 6271ff2a6f84b5efffffff
	VMOVDQU16 Z6, K4, Z22                              // 62b1ff4c7ff6
	VMOVDQU16 Z8, K4, Z22                              // 6231ff4c7fc6
	VMOVDQU16 Z6, K4, Z11                              // 62d1ff4c7ff3
	VMOVDQU16 Z8, K4, Z11                              // 6251ff4c7fc3
	VMOVDQU16 Z6, K4, (CX)                             // 62f1ff4c7f31
	VMOVDQU16 Z8, K4, (CX)                             // 6271ff4c7f01
	VMOVDQU16 Z6, K4, 99(R15)                          // 62d1ff4c7fb763000000
	VMOVDQU16 Z8, K4, 99(R15)                          // 6251ff4c7f8763000000
	VMOVDQU16 Z12, K1, Z25                             // 6211ff497fe1
	VMOVDQU16 Z17, K1, Z25                             // 6281ff497fc9
	VMOVDQU16 99(R15)(R15*2), K1, Z25                  // 6201ff496f8c7f63000000
	VMOVDQU16 -7(DI), K1, Z25                          // 6261ff496f8ff9ffffff
	VMOVDQU16 Z12, K1, Z12                             // 6251ff497fe4
	VMOVDQU16 Z17, K1, Z12                             // 62c1ff497fcc
	VMOVDQU16 99(R15)(R15*2), K1, Z12                  // 6211ff496fa47f63000000
	VMOVDQU16 -7(DI), K1, Z12                          // 6271ff496fa7f9ffffff
	VMOVDQU8 X11, K5, X23                              // 62317f0d7fdf
	VMOVDQU8 X11, K5, -7(CX)(DX*1)                     // 62717f0d7f9c11f9ffffff
	VMOVDQU8 X11, K5, -15(R14)(R15*4)                  // 62117f0d7f9cbef1ffffff
	VMOVDQU8 X24, K3, X31                              // 62017f0b7fc7
	VMOVDQU8 15(DX)(BX*1), K3, X31                     // 62617f0b6fbc1a0f000000
	VMOVDQU8 -7(CX)(DX*2), K3, X31                     // 62617f0b6fbc51f9ffffff
	VMOVDQU8 Y3, K4, Y6                                // 62f17f2c7fde
	VMOVDQU8 Y3, K4, 7(SI)(DI*1)                       // 62f17f2c7f9c3e07000000
	VMOVDQU8 Y3, K4, 15(DX)(BX*8)                      // 62f17f2c7f9cda0f000000
	VMOVDQU8 Y6, K2, Y7                                // 62f17f2a7ff7
	VMOVDQU8 -7(DI)(R8*1), K2, Y7                      // 62b17f2a6fbc07f9ffffff
	VMOVDQU8 (SP), K2, Y7                              // 62f17f2a6f3c24
	VMOVDQU8 Z9, K2, Z3                                // 62717f4a7fcb
	VMOVDQU8 Z19, K2, Z3                               // 62e17f4a7fdb
	VMOVDQU8 Z9, K2, Z30                               // 62117f4a7fce
	VMOVDQU8 Z19, K2, Z30                              // 62817f4a7fde
	VMOVDQU8 Z9, K2, 15(R8)                            // 62517f4a7f880f000000
	VMOVDQU8 Z19, K2, 15(R8)                           // 62c17f4a7f980f000000
	VMOVDQU8 Z9, K2, (BP)                              // 62717f4a7f4d00
	VMOVDQU8 Z19, K2, (BP)                             // 62e17f4a7f5d00
	VMOVDQU8 Z11, K3, Z12                              // 62517f4b7fdc
	VMOVDQU8 Z5, K3, Z12                               // 62d17f4b7fec
	VMOVDQU8 15(R8)(R14*8), K3, Z12                    // 62117f4b6fa4f00f000000
	VMOVDQU8 -15(R14)(R15*2), K3, Z12                  // 62117f4b6fa47ef1ffffff
	VMOVDQU8 Z11, K3, Z22                              // 62317f4b7fde
	VMOVDQU8 Z5, K3, Z22                               // 62b17f4b7fee
	VMOVDQU8 15(R8)(R14*8), K3, Z22                    // 62817f4b6fb4f00f000000
	VMOVDQU8 -15(R14)(R15*2), K3, Z22                  // 62817f4b6fb47ef1ffffff
	VPABSB X22, K3, X6                                 // 62b27d0b1cf6 or 62b2fd0b1cf6
	VPABSB -7(CX), K3, X6                              // 62f27d0b1cb1f9ffffff or 62f2fd0b1cb1f9ffffff
	VPABSB 15(DX)(BX*4), K3, X6                        // 62f27d0b1cb49a0f000000 or 62f2fd0b1cb49a0f000000
	VPABSB Y27, K4, Y11                                // 62127d2c1cdb or 6212fd2c1cdb
	VPABSB 15(DX)(BX*1), K4, Y11                       // 62727d2c1c9c1a0f000000 or 6272fd2c1c9c1a0f000000
	VPABSB -7(CX)(DX*2), K4, Y11                       // 62727d2c1c9c51f9ffffff or 6272fd2c1c9c51f9ffffff
	VPABSB Z6, K5, Z21                                 // 62e27d4d1cee or 62e2fd4d1cee
	VPABSB Z9, K5, Z21                                 // 62c27d4d1ce9 or 62c2fd4d1ce9
	VPABSB (AX), K5, Z21                               // 62e27d4d1c28 or 62e2fd4d1c28
	VPABSB 7(SI), K5, Z21                              // 62e27d4d1cae07000000 or 62e2fd4d1cae07000000
	VPABSB Z6, K5, Z9                                  // 62727d4d1cce or 6272fd4d1cce
	VPABSB Z9, K5, Z9                                  // 62527d4d1cc9 or 6252fd4d1cc9
	VPABSB (AX), K5, Z9                                // 62727d4d1c08 or 6272fd4d1c08
	VPABSB 7(SI), K5, Z9                               // 62727d4d1c8e07000000 or 6272fd4d1c8e07000000
	VPABSW X11, K4, X15                                // 62527d0c1dfb or 6252fd0c1dfb
	VPABSW (BX), K4, X15                               // 62727d0c1d3b or 6272fd0c1d3b
	VPABSW -17(BP)(SI*1), K4, X15                      // 62727d0c1dbc35efffffff or 6272fd0c1dbc35efffffff
	VPABSW Y3, K7, Y26                                 // 62627d2f1dd3 or 6262fd2f1dd3
	VPABSW 15(R8), K7, Y26                             // 62427d2f1d900f000000 or 6242fd2f1d900f000000
	VPABSW (BP), K7, Y26                               // 62627d2f1d5500 or 6262fd2f1d5500
	VPABSW Z16, K2, Z7                                 // 62b27d4a1df8 or 62b2fd4a1df8
	VPABSW Z25, K2, Z7                                 // 62927d4a1df9 or 6292fd4a1df9
	VPABSW (R8), K2, Z7                                // 62d27d4a1d38 or 62d2fd4a1d38
	VPABSW 15(DX)(BX*2), K2, Z7                        // 62f27d4a1dbc5a0f000000 or 62f2fd4a1dbc5a0f000000
	VPABSW Z16, K2, Z21                                // 62a27d4a1de8 or 62a2fd4a1de8
	VPABSW Z25, K2, Z21                                // 62827d4a1de9 or 6282fd4a1de9
	VPABSW (R8), K2, Z21                               // 62c27d4a1d28 or 62c2fd4a1d28
	VPABSW 15(DX)(BX*2), K2, Z21                       // 62e27d4a1dac5a0f000000 or 62e2fd4a1dac5a0f000000
	VPACKSSDW X13, X19, K5, X1                         // 62d165056bcd
	VPACKSSDW 15(R8)(R14*4), X19, K5, X1               // 629165056b8cb00f000000
	VPACKSSDW -7(CX)(DX*4), X19, K5, X1                // 62f165056b8c91f9ffffff
	VPACKSSDW Y1, Y28, K3, Y8                          // 62711d236bc1
	VPACKSSDW 15(R8)(R14*8), Y28, K3, Y8               // 62111d236b84f00f000000
	VPACKSSDW -15(R14)(R15*2), Y28, K3, Y8             // 62111d236b847ef1ffffff
	VPACKSSDW Z21, Z12, K4, Z14                        // 62311d4c6bf5
	VPACKSSDW Z9, Z12, K4, Z14                         // 62511d4c6bf1
	VPACKSSDW 17(SP)(BP*1), Z12, K4, Z14               // 62711d4c6bb42c11000000
	VPACKSSDW -7(CX)(DX*8), Z12, K4, Z14               // 62711d4c6bb4d1f9ffffff
	VPACKSSDW Z21, Z13, K4, Z14                        // 6231154c6bf5
	VPACKSSDW Z9, Z13, K4, Z14                         // 6251154c6bf1
	VPACKSSDW 17(SP)(BP*1), Z13, K4, Z14               // 6271154c6bb42c11000000
	VPACKSSDW -7(CX)(DX*8), Z13, K4, Z14               // 6271154c6bb4d1f9ffffff
	VPACKSSDW Z21, Z12, K4, Z13                        // 62311d4c6bed
	VPACKSSDW Z9, Z12, K4, Z13                         // 62511d4c6be9
	VPACKSSDW 17(SP)(BP*1), Z12, K4, Z13               // 62711d4c6bac2c11000000
	VPACKSSDW -7(CX)(DX*8), Z12, K4, Z13               // 62711d4c6bacd1f9ffffff
	VPACKSSDW Z21, Z13, K4, Z13                        // 6231154c6bed
	VPACKSSDW Z9, Z13, K4, Z13                         // 6251154c6be9
	VPACKSSDW 17(SP)(BP*1), Z13, K4, Z13               // 6271154c6bac2c11000000
	VPACKSSDW -7(CX)(DX*8), Z13, K4, Z13               // 6271154c6bacd1f9ffffff
	VPACKSSWB X0, X14, K2, X2                          // 62f10d0a63d0 or 62f18d0a63d0
	VPACKSSWB (R8), X14, K2, X2                        // 62d10d0a6310 or 62d18d0a6310
	VPACKSSWB 15(DX)(BX*2), X14, K2, X2                // 62f10d0a63945a0f000000 or 62f18d0a63945a0f000000
	VPACKSSWB Y31, Y14, K2, Y23                        // 62810d2a63ff or 62818d2a63ff
	VPACKSSWB -15(R14)(R15*1), Y14, K2, Y23            // 62810d2a63bc3ef1ffffff or 62818d2a63bc3ef1ffffff
	VPACKSSWB -15(BX), Y14, K2, Y23                    // 62e10d2a63bbf1ffffff or 62e18d2a63bbf1ffffff
	VPACKSSWB Z23, Z27, K3, Z2                         // 62b1254363d7 or 62b1a54363d7
	VPACKSSWB Z9, Z27, K3, Z2                          // 62d1254363d1 or 62d1a54363d1
	VPACKSSWB -17(BP)(SI*2), Z27, K3, Z2               // 62f12543639475efffffff or 62f1a543639475efffffff
	VPACKSSWB 7(AX)(CX*2), Z27, K3, Z2                 // 62f1254363944807000000 or 62f1a54363944807000000
	VPACKSSWB Z23, Z25, K3, Z2                         // 62b1354363d7 or 62b1b54363d7
	VPACKSSWB Z9, Z25, K3, Z2                          // 62d1354363d1 or 62d1b54363d1
	VPACKSSWB -17(BP)(SI*2), Z25, K3, Z2               // 62f13543639475efffffff or 62f1b543639475efffffff
	VPACKSSWB 7(AX)(CX*2), Z25, K3, Z2                 // 62f1354363944807000000 or 62f1b54363944807000000
	VPACKSSWB Z23, Z27, K3, Z7                         // 62b1254363ff or 62b1a54363ff
	VPACKSSWB Z9, Z27, K3, Z7                          // 62d1254363f9 or 62d1a54363f9
	VPACKSSWB -17(BP)(SI*2), Z27, K3, Z7               // 62f1254363bc75efffffff or 62f1a54363bc75efffffff
	VPACKSSWB 7(AX)(CX*2), Z27, K3, Z7                 // 62f1254363bc4807000000 or 62f1a54363bc4807000000
	VPACKSSWB Z23, Z25, K3, Z7                         // 62b1354363ff or 62b1b54363ff
	VPACKSSWB Z9, Z25, K3, Z7                          // 62d1354363f9 or 62d1b54363f9
	VPACKSSWB -17(BP)(SI*2), Z25, K3, Z7               // 62f1354363bc75efffffff or 62f1b54363bc75efffffff
	VPACKSSWB 7(AX)(CX*2), Z25, K3, Z7                 // 62f1354363bc4807000000 or 62f1b54363bc4807000000
	VPACKUSDW X11, X25, K3, X0                         // 62d235032bc3
	VPACKUSDW 17(SP)(BP*1), X25, K3, X0                // 62f235032b842c11000000
	VPACKUSDW -7(CX)(DX*8), X25, K3, X0                // 62f235032b84d1f9ffffff
	VPACKUSDW Y22, Y2, K3, Y25                         // 62226d2b2bce
	VPACKUSDW 7(AX)(CX*4), Y2, K3, Y25                 // 62626d2b2b8c8807000000
	VPACKUSDW 7(AX)(CX*1), Y2, K3, Y25                 // 62626d2b2b8c0807000000
	VPACKUSDW Z14, Z3, K2, Z27                         // 6242654a2bde
	VPACKUSDW Z7, Z3, K2, Z27                          // 6262654a2bdf
	VPACKUSDW 15(R8)(R14*1), Z3, K2, Z27               // 6202654a2b9c300f000000
	VPACKUSDW 15(R8)(R14*2), Z3, K2, Z27               // 6202654a2b9c700f000000
	VPACKUSDW Z14, Z0, K2, Z27                         // 62427d4a2bde
	VPACKUSDW Z7, Z0, K2, Z27                          // 62627d4a2bdf
	VPACKUSDW 15(R8)(R14*1), Z0, K2, Z27               // 62027d4a2b9c300f000000
	VPACKUSDW 15(R8)(R14*2), Z0, K2, Z27               // 62027d4a2b9c700f000000
	VPACKUSDW Z14, Z3, K2, Z14                         // 6252654a2bf6
	VPACKUSDW Z7, Z3, K2, Z14                          // 6272654a2bf7
	VPACKUSDW 15(R8)(R14*1), Z3, K2, Z14               // 6212654a2bb4300f000000
	VPACKUSDW 15(R8)(R14*2), Z3, K2, Z14               // 6212654a2bb4700f000000
	VPACKUSDW Z14, Z0, K2, Z14                         // 62527d4a2bf6
	VPACKUSDW Z7, Z0, K2, Z14                          // 62727d4a2bf7
	VPACKUSDW 15(R8)(R14*1), Z0, K2, Z14               // 62127d4a2bb4300f000000
	VPACKUSDW 15(R8)(R14*2), Z0, K2, Z14               // 62127d4a2bb4700f000000
	VPACKUSWB X11, X18, K1, X17                        // 62c16d0167cb or 62c1ed0167cb
	VPACKUSWB -17(BP)(SI*2), X18, K1, X17              // 62e16d01678c75efffffff or 62e1ed01678c75efffffff
	VPACKUSWB 7(AX)(CX*2), X18, K1, X17                // 62e16d01678c4807000000 or 62e1ed01678c4807000000
	VPACKUSWB Y9, Y8, K2, Y27                          // 62413d2a67d9 or 6241bd2a67d9
	VPACKUSWB (SI), Y8, K2, Y27                        // 62613d2a671e or 6261bd2a671e
	VPACKUSWB 7(SI)(DI*2), Y8, K2, Y27                 // 62613d2a679c7e07000000 or 6261bd2a679c7e07000000
	VPACKUSWB Z1, Z22, K1, Z8                          // 62714d4167c1 or 6271cd4167c1
	VPACKUSWB Z16, Z22, K1, Z8                         // 62314d4167c0 or 6231cd4167c0
	VPACKUSWB (R14), Z22, K1, Z8                       // 62514d416706 or 6251cd416706
	VPACKUSWB -7(DI)(R8*8), Z22, K1, Z8                // 62314d416784c7f9ffffff or 6231cd416784c7f9ffffff
	VPACKUSWB Z1, Z25, K1, Z8                          // 6271354167c1 or 6271b54167c1
	VPACKUSWB Z16, Z25, K1, Z8                         // 6231354167c0 or 6231b54167c0
	VPACKUSWB (R14), Z25, K1, Z8                       // 625135416706 or 6251b5416706
	VPACKUSWB -7(DI)(R8*8), Z25, K1, Z8                // 623135416784c7f9ffffff or 6231b5416784c7f9ffffff
	VPACKUSWB Z1, Z22, K1, Z24                         // 62614d4167c1 or 6261cd4167c1
	VPACKUSWB Z16, Z22, K1, Z24                        // 62214d4167c0 or 6221cd4167c0
	VPACKUSWB (R14), Z22, K1, Z24                      // 62414d416706 or 6241cd416706
	VPACKUSWB -7(DI)(R8*8), Z22, K1, Z24               // 62214d416784c7f9ffffff or 6221cd416784c7f9ffffff
	VPACKUSWB Z1, Z25, K1, Z24                         // 6261354167c1 or 6261b54167c1
	VPACKUSWB Z16, Z25, K1, Z24                        // 6221354167c0 or 6221b54167c0
	VPACKUSWB (R14), Z25, K1, Z24                      // 624135416706 or 6241b5416706
	VPACKUSWB -7(DI)(R8*8), Z25, K1, Z24               // 622135416784c7f9ffffff or 6221b5416784c7f9ffffff
	VPADDB X24, X2, K7, X9                             // 62116d0ffcc8 or 6211ed0ffcc8
	VPADDB 15(R8)(R14*1), X2, K7, X9                   // 62116d0ffc8c300f000000 or 6211ed0ffc8c300f000000
	VPADDB 15(R8)(R14*2), X2, K7, X9                   // 62116d0ffc8c700f000000 or 6211ed0ffc8c700f000000
	VPADDB Y14, Y9, K1, Y22                            // 62c13529fcf6 or 62c1b529fcf6
	VPADDB 17(SP)(BP*8), Y9, K1, Y22                   // 62e13529fcb4ec11000000 or 62e1b529fcb4ec11000000
	VPADDB 17(SP)(BP*4), Y9, K1, Y22                   // 62e13529fcb4ac11000000 or 62e1b529fcb4ac11000000
	VPADDB Z15, Z0, K1, Z6                             // 62d17d49fcf7 or 62d1fd49fcf7
	VPADDB Z12, Z0, K1, Z6                             // 62d17d49fcf4 or 62d1fd49fcf4
	VPADDB 99(R15)(R15*4), Z0, K1, Z6                  // 62917d49fcb4bf63000000 or 6291fd49fcb4bf63000000
	VPADDB 15(DX), Z0, K1, Z6                          // 62f17d49fcb20f000000 or 62f1fd49fcb20f000000
	VPADDB Z15, Z8, K1, Z6                             // 62d13d49fcf7 or 62d1bd49fcf7
	VPADDB Z12, Z8, K1, Z6                             // 62d13d49fcf4 or 62d1bd49fcf4
	VPADDB 99(R15)(R15*4), Z8, K1, Z6                  // 62913d49fcb4bf63000000 or 6291bd49fcb4bf63000000
	VPADDB 15(DX), Z8, K1, Z6                          // 62f13d49fcb20f000000 or 62f1bd49fcb20f000000
	VPADDB Z15, Z0, K1, Z2                             // 62d17d49fcd7 or 62d1fd49fcd7
	VPADDB Z12, Z0, K1, Z2                             // 62d17d49fcd4 or 62d1fd49fcd4
	VPADDB 99(R15)(R15*4), Z0, K1, Z2                  // 62917d49fc94bf63000000 or 6291fd49fc94bf63000000
	VPADDB 15(DX), Z0, K1, Z2                          // 62f17d49fc920f000000 or 62f1fd49fc920f000000
	VPADDB Z15, Z8, K1, Z2                             // 62d13d49fcd7 or 62d1bd49fcd7
	VPADDB Z12, Z8, K1, Z2                             // 62d13d49fcd4 or 62d1bd49fcd4
	VPADDB 99(R15)(R15*4), Z8, K1, Z2                  // 62913d49fc94bf63000000 or 6291bd49fc94bf63000000
	VPADDB 15(DX), Z8, K1, Z2                          // 62f13d49fc920f000000 or 62f1bd49fc920f000000
	VPADDSB X15, X11, K4, X3                           // 62d1250cecdf or 62d1a50cecdf
	VPADDSB (CX), X11, K4, X3                          // 62f1250cec19 or 62f1a50cec19
	VPADDSB 99(R15), X11, K4, X3                       // 62d1250cec9f63000000 or 62d1a50cec9f63000000
	VPADDSB Y9, Y22, K5, Y31                           // 62414d25ecf9 or 6241cd25ecf9
	VPADDSB 7(AX), Y22, K5, Y31                        // 62614d25ecb807000000 or 6261cd25ecb807000000
	VPADDSB (DI), Y22, K5, Y31                         // 62614d25ec3f or 6261cd25ec3f
	VPADDSB Z13, Z28, K7, Z26                          // 62411d47ecd5 or 62419d47ecd5
	VPADDSB Z21, Z28, K7, Z26                          // 62211d47ecd5 or 62219d47ecd5
	VPADDSB -7(CX)(DX*1), Z28, K7, Z26                 // 62611d47ec9411f9ffffff or 62619d47ec9411f9ffffff
	VPADDSB -15(R14)(R15*4), Z28, K7, Z26              // 62011d47ec94bef1ffffff or 62019d47ec94bef1ffffff
	VPADDSB Z13, Z6, K7, Z26                           // 62414d4fecd5 or 6241cd4fecd5
	VPADDSB Z21, Z6, K7, Z26                           // 62214d4fecd5 or 6221cd4fecd5
	VPADDSB -7(CX)(DX*1), Z6, K7, Z26                  // 62614d4fec9411f9ffffff or 6261cd4fec9411f9ffffff
	VPADDSB -15(R14)(R15*4), Z6, K7, Z26               // 62014d4fec94bef1ffffff or 6201cd4fec94bef1ffffff
	VPADDSB Z13, Z28, K7, Z14                          // 62511d47ecf5 or 62519d47ecf5
	VPADDSB Z21, Z28, K7, Z14                          // 62311d47ecf5 or 62319d47ecf5
	VPADDSB -7(CX)(DX*1), Z28, K7, Z14                 // 62711d47ecb411f9ffffff or 62719d47ecb411f9ffffff
	VPADDSB -15(R14)(R15*4), Z28, K7, Z14              // 62111d47ecb4bef1ffffff or 62119d47ecb4bef1ffffff
	VPADDSB Z13, Z6, K7, Z14                           // 62514d4fecf5 or 6251cd4fecf5
	VPADDSB Z21, Z6, K7, Z14                           // 62314d4fecf5 or 6231cd4fecf5
	VPADDSB -7(CX)(DX*1), Z6, K7, Z14                  // 62714d4fecb411f9ffffff or 6271cd4fecb411f9ffffff
	VPADDSB -15(R14)(R15*4), Z6, K7, Z14               // 62114d4fecb4bef1ffffff or 6211cd4fecb4bef1ffffff
	VPADDSW X6, X13, K7, X30                           // 6261150fedf6 or 6261950fedf6
	VPADDSW 99(R15)(R15*2), X13, K7, X30               // 6201150fedb47f63000000 or 6201950fedb47f63000000
	VPADDSW -7(DI), X13, K7, X30                       // 6261150fedb7f9ffffff or 6261950fedb7f9ffffff
	VPADDSW Y5, Y31, K6, Y23                           // 62e10526edfd or 62e18526edfd
	VPADDSW 99(R15)(R15*1), Y31, K6, Y23               // 62810526edbc3f63000000 or 62818526edbc3f63000000
	VPADDSW (DX), Y31, K6, Y23                         // 62e10526ed3a or 62e18526ed3a
	VPADDSW Z21, Z3, K3, Z26                           // 6221654bedd5 or 6221e54bedd5
	VPADDSW Z13, Z3, K3, Z26                           // 6241654bedd5 or 6241e54bedd5
	VPADDSW 15(DX)(BX*1), Z3, K3, Z26                  // 6261654bed941a0f000000 or 6261e54bed941a0f000000
	VPADDSW -7(CX)(DX*2), Z3, K3, Z26                  // 6261654bed9451f9ffffff or 6261e54bed9451f9ffffff
	VPADDSW Z21, Z0, K3, Z26                           // 62217d4bedd5 or 6221fd4bedd5
	VPADDSW Z13, Z0, K3, Z26                           // 62417d4bedd5 or 6241fd4bedd5
	VPADDSW 15(DX)(BX*1), Z0, K3, Z26                  // 62617d4bed941a0f000000 or 6261fd4bed941a0f000000
	VPADDSW -7(CX)(DX*2), Z0, K3, Z26                  // 62617d4bed9451f9ffffff or 6261fd4bed9451f9ffffff
	VPADDSW Z21, Z3, K3, Z3                            // 62b1654beddd or 62b1e54beddd
	VPADDSW Z13, Z3, K3, Z3                            // 62d1654beddd or 62d1e54beddd
	VPADDSW 15(DX)(BX*1), Z3, K3, Z3                   // 62f1654bed9c1a0f000000 or 62f1e54bed9c1a0f000000
	VPADDSW -7(CX)(DX*2), Z3, K3, Z3                   // 62f1654bed9c51f9ffffff or 62f1e54bed9c51f9ffffff
	VPADDSW Z21, Z0, K3, Z3                            // 62b17d4beddd or 62b1fd4beddd
	VPADDSW Z13, Z0, K3, Z3                            // 62d17d4beddd or 62d1fd4beddd
	VPADDSW 15(DX)(BX*1), Z0, K3, Z3                   // 62f17d4bed9c1a0f000000 or 62f1fd4bed9c1a0f000000
	VPADDSW -7(CX)(DX*2), Z0, K3, Z3                   // 62f17d4bed9c51f9ffffff or 62f1fd4bed9c51f9ffffff
	VPADDUSB X30, X23, K7, X12                         // 62114507dce6 or 6211c507dce6
	VPADDUSB -7(CX)(DX*1), X23, K7, X12                // 62714507dca411f9ffffff or 6271c507dca411f9ffffff
	VPADDUSB -15(R14)(R15*4), X23, K7, X12             // 62114507dca4bef1ffffff or 6211c507dca4bef1ffffff
	VPADDUSB Y19, Y5, K4, Y0                           // 62b1552cdcc3 or 62b1d52cdcc3
	VPADDUSB -17(BP)(SI*8), Y5, K4, Y0                 // 62f1552cdc84f5efffffff or 62f1d52cdc84f5efffffff
	VPADDUSB (R15), Y5, K4, Y0                         // 62d1552cdc07 or 62d1d52cdc07
	VPADDUSB Z27, Z3, K4, Z11                          // 6211654cdcdb or 6211e54cdcdb
	VPADDUSB Z15, Z3, K4, Z11                          // 6251654cdcdf or 6251e54cdcdf
	VPADDUSB -17(BP), Z3, K4, Z11                      // 6271654cdc9defffffff or 6271e54cdc9defffffff
	VPADDUSB -15(R14)(R15*8), Z3, K4, Z11              // 6211654cdc9cfef1ffffff or 6211e54cdc9cfef1ffffff
	VPADDUSB Z27, Z12, K4, Z11                         // 62111d4cdcdb or 62119d4cdcdb
	VPADDUSB Z15, Z12, K4, Z11                         // 62511d4cdcdf or 62519d4cdcdf
	VPADDUSB -17(BP), Z12, K4, Z11                     // 62711d4cdc9defffffff or 62719d4cdc9defffffff
	VPADDUSB -15(R14)(R15*8), Z12, K4, Z11             // 62111d4cdc9cfef1ffffff or 62119d4cdc9cfef1ffffff
	VPADDUSB Z27, Z3, K4, Z25                          // 6201654cdccb or 6201e54cdccb
	VPADDUSB Z15, Z3, K4, Z25                          // 6241654cdccf or 6241e54cdccf
	VPADDUSB -17(BP), Z3, K4, Z25                      // 6261654cdc8defffffff or 6261e54cdc8defffffff
	VPADDUSB -15(R14)(R15*8), Z3, K4, Z25              // 6201654cdc8cfef1ffffff or 6201e54cdc8cfef1ffffff
	VPADDUSB Z27, Z12, K4, Z25                         // 62011d4cdccb or 62019d4cdccb
	VPADDUSB Z15, Z12, K4, Z25                         // 62411d4cdccf or 62419d4cdccf
	VPADDUSB -17(BP), Z12, K4, Z25                     // 62611d4cdc8defffffff or 62619d4cdc8defffffff
	VPADDUSB -15(R14)(R15*8), Z12, K4, Z25             // 62011d4cdc8cfef1ffffff or 62019d4cdc8cfef1ffffff
	VPADDUSW X2, X20, K7, X8                           // 62715d07ddc2 or 6271dd07ddc2
	VPADDUSW 15(DX)(BX*1), X20, K7, X8                 // 62715d07dd841a0f000000 or 6271dd07dd841a0f000000
	VPADDUSW -7(CX)(DX*2), X20, K7, X8                 // 62715d07dd8451f9ffffff or 6271dd07dd8451f9ffffff
	VPADDUSW Y2, Y28, K2, Y31                          // 62611d22ddfa or 62619d22ddfa
	VPADDUSW 7(SI)(DI*8), Y28, K2, Y31                 // 62611d22ddbcfe07000000 or 62619d22ddbcfe07000000
	VPADDUSW -15(R14), Y28, K2, Y31                    // 62411d22ddbef1ffffff or 62419d22ddbef1ffffff
	VPADDUSW Z8, Z23, K5, Z23                          // 62c14545ddf8 or 62c1c545ddf8
	VPADDUSW Z28, Z23, K5, Z23                         // 62814545ddfc or 6281c545ddfc
	VPADDUSW 17(SP)(BP*2), Z23, K5, Z23                // 62e14545ddbc6c11000000 or 62e1c545ddbc6c11000000
	VPADDUSW -7(DI)(R8*4), Z23, K5, Z23                // 62a14545ddbc87f9ffffff or 62a1c545ddbc87f9ffffff
	VPADDUSW Z8, Z6, K5, Z23                           // 62c14d4dddf8 or 62c1cd4dddf8
	VPADDUSW Z28, Z6, K5, Z23                          // 62814d4dddfc or 6281cd4dddfc
	VPADDUSW 17(SP)(BP*2), Z6, K5, Z23                 // 62e14d4dddbc6c11000000 or 62e1cd4dddbc6c11000000
	VPADDUSW -7(DI)(R8*4), Z6, K5, Z23                 // 62a14d4dddbc87f9ffffff or 62a1cd4dddbc87f9ffffff
	VPADDUSW Z8, Z23, K5, Z5                           // 62d14545dde8 or 62d1c545dde8
	VPADDUSW Z28, Z23, K5, Z5                          // 62914545ddec or 6291c545ddec
	VPADDUSW 17(SP)(BP*2), Z23, K5, Z5                 // 62f14545ddac6c11000000 or 62f1c545ddac6c11000000
	VPADDUSW -7(DI)(R8*4), Z23, K5, Z5                 // 62b14545ddac87f9ffffff or 62b1c545ddac87f9ffffff
	VPADDUSW Z8, Z6, K5, Z5                            // 62d14d4ddde8 or 62d1cd4ddde8
	VPADDUSW Z28, Z6, K5, Z5                           // 62914d4dddec or 6291cd4dddec
	VPADDUSW 17(SP)(BP*2), Z6, K5, Z5                  // 62f14d4dddac6c11000000 or 62f1cd4dddac6c11000000
	VPADDUSW -7(DI)(R8*4), Z6, K5, Z5                  // 62b14d4dddac87f9ffffff or 62b1cd4dddac87f9ffffff
	VPADDW X19, X26, K3, X9                            // 62312d03fdcb or 6231ad03fdcb
	VPADDW -17(BP), X26, K3, X9                        // 62712d03fd8defffffff or 6271ad03fd8defffffff
	VPADDW -15(R14)(R15*8), X26, K3, X9                // 62112d03fd8cfef1ffffff or 6211ad03fd8cfef1ffffff
	VPADDW Y0, Y27, K4, Y24                            // 62612524fdc0 or 6261a524fdc0
	VPADDW 7(SI)(DI*1), Y27, K4, Y24                   // 62612524fd843e07000000 or 6261a524fd843e07000000
	VPADDW 15(DX)(BX*8), Y27, K4, Y24                  // 62612524fd84da0f000000 or 6261a524fd84da0f000000
	VPADDW Z12, Z16, K2, Z21                           // 62c17d42fdec or 62c1fd42fdec
	VPADDW Z27, Z16, K2, Z21                           // 62817d42fdeb or 6281fd42fdeb
	VPADDW 15(R8), Z16, K2, Z21                        // 62c17d42fda80f000000 or 62c1fd42fda80f000000
	VPADDW (BP), Z16, K2, Z21                          // 62e17d42fd6d00 or 62e1fd42fd6d00
	VPADDW Z12, Z13, K2, Z21                           // 62c1154afdec or 62c1954afdec
	VPADDW Z27, Z13, K2, Z21                           // 6281154afdeb or 6281954afdeb
	VPADDW 15(R8), Z13, K2, Z21                        // 62c1154afda80f000000 or 62c1954afda80f000000
	VPADDW (BP), Z13, K2, Z21                          // 62e1154afd6d00 or 62e1954afd6d00
	VPADDW Z12, Z16, K2, Z5                            // 62d17d42fdec or 62d1fd42fdec
	VPADDW Z27, Z16, K2, Z5                            // 62917d42fdeb or 6291fd42fdeb
	VPADDW 15(R8), Z16, K2, Z5                         // 62d17d42fda80f000000 or 62d1fd42fda80f000000
	VPADDW (BP), Z16, K2, Z5                           // 62f17d42fd6d00 or 62f1fd42fd6d00
	VPADDW Z12, Z13, K2, Z5                            // 62d1154afdec or 62d1954afdec
	VPADDW Z27, Z13, K2, Z5                            // 6291154afdeb or 6291954afdeb
	VPADDW 15(R8), Z13, K2, Z5                         // 62d1154afda80f000000 or 62d1954afda80f000000
	VPADDW (BP), Z13, K2, Z5                           // 62f1154afd6d00 or 62f1954afd6d00
	VPALIGNR $13, X16, X31, K2, X0                     // 62b305020fc00d or 62b385020fc00d
	VPALIGNR $13, 17(SP)(BP*2), X31, K2, X0            // 62f305020f846c110000000d or 62f385020f846c110000000d
	VPALIGNR $13, -7(DI)(R8*4), X31, K2, X0            // 62b305020f8487f9ffffff0d or 62b385020f8487f9ffffff0d
	VPALIGNR $65, Y3, Y31, K3, Y11                     // 627305230fdb41 or 627385230fdb41
	VPALIGNR $65, -7(DI)(R8*1), Y31, K3, Y11           // 623305230f9c07f9ffffff41 or 623385230f9c07f9ffffff41
	VPALIGNR $65, (SP), Y31, K3, Y11                   // 627305230f1c2441 or 627385230f1c2441
	VPALIGNR $67, Z25, Z6, K3, Z22                     // 62834d4b0ff143 or 6283cd4b0ff143
	VPALIGNR $67, Z12, Z6, K3, Z22                     // 62c34d4b0ff443 or 62c3cd4b0ff443
	VPALIGNR $67, 15(R8)(R14*8), Z6, K3, Z22           // 62834d4b0fb4f00f00000043 or 6283cd4b0fb4f00f00000043
	VPALIGNR $67, -15(R14)(R15*2), Z6, K3, Z22         // 62834d4b0fb47ef1ffffff43 or 6283cd4b0fb47ef1ffffff43
	VPALIGNR $67, Z25, Z8, K3, Z22                     // 62833d4b0ff143 or 6283bd4b0ff143
	VPALIGNR $67, Z12, Z8, K3, Z22                     // 62c33d4b0ff443 or 62c3bd4b0ff443
	VPALIGNR $67, 15(R8)(R14*8), Z8, K3, Z22           // 62833d4b0fb4f00f00000043 or 6283bd4b0fb4f00f00000043
	VPALIGNR $67, -15(R14)(R15*2), Z8, K3, Z22         // 62833d4b0fb47ef1ffffff43 or 6283bd4b0fb47ef1ffffff43
	VPALIGNR $67, Z25, Z6, K3, Z11                     // 62134d4b0fd943 or 6213cd4b0fd943
	VPALIGNR $67, Z12, Z6, K3, Z11                     // 62534d4b0fdc43 or 6253cd4b0fdc43
	VPALIGNR $67, 15(R8)(R14*8), Z6, K3, Z11           // 62134d4b0f9cf00f00000043 or 6213cd4b0f9cf00f00000043
	VPALIGNR $67, -15(R14)(R15*2), Z6, K3, Z11         // 62134d4b0f9c7ef1ffffff43 or 6213cd4b0f9c7ef1ffffff43
	VPALIGNR $67, Z25, Z8, K3, Z11                     // 62133d4b0fd943 or 6213bd4b0fd943
	VPALIGNR $67, Z12, Z8, K3, Z11                     // 62533d4b0fdc43 or 6253bd4b0fdc43
	VPALIGNR $67, 15(R8)(R14*8), Z8, K3, Z11           // 62133d4b0f9cf00f00000043 or 6213bd4b0f9cf00f00000043
	VPALIGNR $67, -15(R14)(R15*2), Z8, K3, Z11         // 62133d4b0f9c7ef1ffffff43 or 6213bd4b0f9c7ef1ffffff43
	VPAVGB X16, X7, K1, X19                            // 62a14509e0d8 or 62a1c509e0d8
	VPAVGB (SI), X7, K1, X19                           // 62e14509e01e or 62e1c509e01e
	VPAVGB 7(SI)(DI*2), X7, K1, X19                    // 62e14509e09c7e07000000 or 62e1c509e09c7e07000000
	VPAVGB Y14, Y19, K3, Y23                           // 62c16523e0fe or 62c1e523e0fe
	VPAVGB 15(R8)(R14*4), Y19, K3, Y23                 // 62816523e0bcb00f000000 or 6281e523e0bcb00f000000
	VPAVGB -7(CX)(DX*4), Y19, K3, Y23                  // 62e16523e0bc91f9ffffff or 62e1e523e0bc91f9ffffff
	VPAVGB Z2, Z18, K4, Z11                            // 62716d44e0da or 6271ed44e0da
	VPAVGB Z21, Z18, K4, Z11                           // 62316d44e0dd or 6231ed44e0dd
	VPAVGB 7(SI)(DI*4), Z18, K4, Z11                   // 62716d44e09cbe07000000 or 6271ed44e09cbe07000000
	VPAVGB -7(DI)(R8*2), Z18, K4, Z11                  // 62316d44e09c47f9ffffff or 6231ed44e09c47f9ffffff
	VPAVGB Z2, Z24, K4, Z11                            // 62713d44e0da or 6271bd44e0da
	VPAVGB Z21, Z24, K4, Z11                           // 62313d44e0dd or 6231bd44e0dd
	VPAVGB 7(SI)(DI*4), Z24, K4, Z11                   // 62713d44e09cbe07000000 or 6271bd44e09cbe07000000
	VPAVGB -7(DI)(R8*2), Z24, K4, Z11                  // 62313d44e09c47f9ffffff or 6231bd44e09c47f9ffffff
	VPAVGB Z2, Z18, K4, Z5                             // 62f16d44e0ea or 62f1ed44e0ea
	VPAVGB Z21, Z18, K4, Z5                            // 62b16d44e0ed or 62b1ed44e0ed
	VPAVGB 7(SI)(DI*4), Z18, K4, Z5                    // 62f16d44e0acbe07000000 or 62f1ed44e0acbe07000000
	VPAVGB -7(DI)(R8*2), Z18, K4, Z5                   // 62b16d44e0ac47f9ffffff or 62b1ed44e0ac47f9ffffff
	VPAVGB Z2, Z24, K4, Z5                             // 62f13d44e0ea or 62f1bd44e0ea
	VPAVGB Z21, Z24, K4, Z5                            // 62b13d44e0ed or 62b1bd44e0ed
	VPAVGB 7(SI)(DI*4), Z24, K4, Z5                    // 62f13d44e0acbe07000000 or 62f1bd44e0acbe07000000
	VPAVGB -7(DI)(R8*2), Z24, K4, Z5                   // 62b13d44e0ac47f9ffffff or 62b1bd44e0ac47f9ffffff
	VPAVGW X7, X1, K5, X31                             // 6261750de3ff or 6261f50de3ff
	VPAVGW 17(SP)(BP*8), X1, K5, X31                   // 6261750de3bcec11000000 or 6261f50de3bcec11000000
	VPAVGW 17(SP)(BP*4), X1, K5, X31                   // 6261750de3bcac11000000 or 6261f50de3bcac11000000
	VPAVGW Y16, Y5, K7, Y21                            // 62a1552fe3e8 or 62a1d52fe3e8
	VPAVGW (R8), Y5, K7, Y21                           // 62c1552fe328 or 62c1d52fe328
	VPAVGW 15(DX)(BX*2), Y5, K7, Y21                   // 62e1552fe3ac5a0f000000 or 62e1d52fe3ac5a0f000000
	VPAVGW Z6, Z6, K7, Z7                              // 62f14d4fe3fe or 62f1cd4fe3fe
	VPAVGW Z22, Z6, K7, Z7                             // 62b14d4fe3fe or 62b1cd4fe3fe
	VPAVGW 17(SP), Z6, K7, Z7                          // 62f14d4fe3bc2411000000 or 62f1cd4fe3bc2411000000
	VPAVGW -17(BP)(SI*4), Z6, K7, Z7                   // 62f14d4fe3bcb5efffffff or 62f1cd4fe3bcb5efffffff
	VPAVGW Z6, Z16, K7, Z7                             // 62f17d47e3fe or 62f1fd47e3fe
	VPAVGW Z22, Z16, K7, Z7                            // 62b17d47e3fe or 62b1fd47e3fe
	VPAVGW 17(SP), Z16, K7, Z7                         // 62f17d47e3bc2411000000 or 62f1fd47e3bc2411000000
	VPAVGW -17(BP)(SI*4), Z16, K7, Z7                  // 62f17d47e3bcb5efffffff or 62f1fd47e3bcb5efffffff
	VPAVGW Z6, Z6, K7, Z13                             // 62714d4fe3ee or 6271cd4fe3ee
	VPAVGW Z22, Z6, K7, Z13                            // 62314d4fe3ee or 6231cd4fe3ee
	VPAVGW 17(SP), Z6, K7, Z13                         // 62714d4fe3ac2411000000 or 6271cd4fe3ac2411000000
	VPAVGW -17(BP)(SI*4), Z6, K7, Z13                  // 62714d4fe3acb5efffffff or 6271cd4fe3acb5efffffff
	VPAVGW Z6, Z16, K7, Z13                            // 62717d47e3ee or 6271fd47e3ee
	VPAVGW Z22, Z16, K7, Z13                           // 62317d47e3ee or 6231fd47e3ee
	VPAVGW 17(SP), Z16, K7, Z13                        // 62717d47e3ac2411000000 or 6271fd47e3ac2411000000
	VPAVGW -17(BP)(SI*4), Z16, K7, Z13                 // 62717d47e3acb5efffffff or 6271fd47e3acb5efffffff
	VPBLENDMB X12, X15, K6, X9                         // 6252050e66cc
	VPBLENDMB 7(SI)(DI*4), X15, K6, X9                 // 6272050e668cbe07000000
	VPBLENDMB -7(DI)(R8*2), X15, K6, X9                // 6232050e668c47f9ffffff
	VPBLENDMB Y20, Y21, K3, Y2                         // 62b2552366d4
	VPBLENDMB 17(SP)(BP*1), Y21, K3, Y2                // 62f2552366942c11000000
	VPBLENDMB -7(CX)(DX*8), Y21, K3, Y2                // 62f255236694d1f9ffffff
	VPBLENDMB Z18, Z13, K7, Z1                         // 62b2154f66ca
	VPBLENDMB Z8, Z13, K7, Z1                          // 62d2154f66c8
	VPBLENDMB 7(AX), Z13, K7, Z1                       // 62f2154f668807000000
	VPBLENDMB (DI), Z13, K7, Z1                        // 62f2154f660f
	VPBLENDMB Z18, Z13, K7, Z15                        // 6232154f66fa
	VPBLENDMB Z8, Z13, K7, Z15                         // 6252154f66f8
	VPBLENDMB 7(AX), Z13, K7, Z15                      // 6272154f66b807000000
	VPBLENDMB (DI), Z13, K7, Z15                       // 6272154f663f
	VPBLENDMW X26, X3, K4, X8                          // 6212e50c66c2
	VPBLENDMW 99(R15)(R15*1), X3, K4, X8               // 6212e50c66843f63000000
	VPBLENDMW (DX), X3, K4, X8                         // 6272e50c6602
	VPBLENDMW Y3, Y0, K2, Y6                           // 62f2fd2a66f3
	VPBLENDMW (R14), Y0, K2, Y6                        // 62d2fd2a6636
	VPBLENDMW -7(DI)(R8*8), Y0, K2, Y6                 // 62b2fd2a66b4c7f9ffffff
	VPBLENDMW Z15, Z3, K2, Z14                         // 6252e54a66f7
	VPBLENDMW Z30, Z3, K2, Z14                         // 6212e54a66f6
	VPBLENDMW 7(SI)(DI*8), Z3, K2, Z14                 // 6272e54a66b4fe07000000
	VPBLENDMW -15(R14), Z3, K2, Z14                    // 6252e54a66b6f1ffffff
	VPBLENDMW Z15, Z12, K2, Z14                        // 62529d4a66f7
	VPBLENDMW Z30, Z12, K2, Z14                        // 62129d4a66f6
	VPBLENDMW 7(SI)(DI*8), Z12, K2, Z14                // 62729d4a66b4fe07000000
	VPBLENDMW -15(R14), Z12, K2, Z14                   // 62529d4a66b6f1ffffff
	VPBLENDMW Z15, Z3, K2, Z28                         // 6242e54a66e7
	VPBLENDMW Z30, Z3, K2, Z28                         // 6202e54a66e6
	VPBLENDMW 7(SI)(DI*8), Z3, K2, Z28                 // 6262e54a66a4fe07000000
	VPBLENDMW -15(R14), Z3, K2, Z28                    // 6242e54a66a6f1ffffff
	VPBLENDMW Z15, Z12, K2, Z28                        // 62429d4a66e7
	VPBLENDMW Z30, Z12, K2, Z28                        // 62029d4a66e6
	VPBLENDMW 7(SI)(DI*8), Z12, K2, Z28                // 62629d4a66a4fe07000000
	VPBLENDMW -15(R14), Z12, K2, Z28                   // 62429d4a66a6f1ffffff
	VPBROADCASTB CX, K3, X23                           // 62e27d0b7af9
	VPBROADCASTB SP, K3, X23                           // 62e27d0b7afc
	VPBROADCASTB R14, K3, Y5                           // 62d27d2b7aee
	VPBROADCASTB AX, K3, Y5                            // 62f27d2b7ae8
	VPBROADCASTB R9, K3, Z19                           // 62c27d4b7ad9
	VPBROADCASTB CX, K3, Z19                           // 62e27d4b7ad9
	VPBROADCASTB R9, K3, Z15                           // 62527d4b7af9
	VPBROADCASTB CX, K3, Z15                           // 62727d4b7af9
	VPBROADCASTB X28, K2, X13                          // 62127d0a78ec
	VPBROADCASTB 99(R15)(R15*1), K2, X13               // 62127d0a786c3f63
	VPBROADCASTB (DX), K2, X13                         // 62727d0a782a
	VPBROADCASTB X24, K1, Y20                          // 62827d2978e0
	VPBROADCASTB -17(BP)(SI*8), K1, Y20                // 62e27d297864f5ef
	VPBROADCASTB (R15), K1, Y20                        // 62c27d297827
	VPBROADCASTB X9, K2, Z5                            // 62d27d4a78e9
	VPBROADCASTB 7(SI)(DI*8), K2, Z5                   // 62f27d4a786cfe07
	VPBROADCASTB -15(R14), K2, Z5                      // 62d27d4a786ef1
	VPBROADCASTB X9, K2, Z1                            // 62d27d4a78c9
	VPBROADCASTB 7(SI)(DI*8), K2, Z1                   // 62f27d4a784cfe07
	VPBROADCASTB -15(R14), K2, Z1                      // 62d27d4a784ef1
	VPBROADCASTW R14, K7, X20                          // 62c27d0f7be6
	VPBROADCASTW AX, K7, X20                           // 62e27d0f7be0
	VPBROADCASTW R9, K7, Y22                           // 62c27d2f7bf1
	VPBROADCASTW CX, K7, Y22                           // 62e27d2f7bf1
	VPBROADCASTW SP, K6, Z0                            // 62f27d4e7bc4
	VPBROADCASTW R14, K6, Z0                           // 62d27d4e7bc6
	VPBROADCASTW SP, K6, Z11                           // 62727d4e7bdc
	VPBROADCASTW R14, K6, Z11                          // 62527d4e7bde
	VPBROADCASTW X9, K3, X7                            // 62d27d0b79f9
	VPBROADCASTW 99(R15)(R15*1), K3, X7                // 62927d0b79bc3f63000000
	VPBROADCASTW (DX), K3, X7                          // 62f27d0b793a
	VPBROADCASTW X7, K7, Y13                           // 62727d2f79ef
	VPBROADCASTW -17(BP)(SI*8), K7, Y13                // 62727d2f79acf5efffffff
	VPBROADCASTW (R15), K7, Y13                        // 62527d2f792f
	VPBROADCASTW X14, K4, Z0                           // 62d27d4c79c6
	VPBROADCASTW 7(SI)(DI*8), K4, Z0                   // 62f27d4c7984fe07000000
	VPBROADCASTW -15(R14), K4, Z0                      // 62d27d4c7986f1ffffff
	VPBROADCASTW X14, K4, Z25                          // 62427d4c79ce
	VPBROADCASTW 7(SI)(DI*8), K4, Z25                  // 62627d4c798cfe07000000
	VPBROADCASTW -15(R14), K4, Z25                     // 62427d4c798ef1ffffff
	VPCMPB $81, X1, X21, K4, K5                        // 62f355043fe951
	VPCMPB $81, 7(SI)(DI*8), X21, K4, K5               // 62f355043facfe0700000051
	VPCMPB $81, -15(R14), X21, K4, K5                  // 62d355043faef1ffffff51
	VPCMPB $81, X1, X21, K4, K4                        // 62f355043fe151
	VPCMPB $81, 7(SI)(DI*8), X21, K4, K4               // 62f355043fa4fe0700000051
	VPCMPB $81, -15(R14), X21, K4, K4                  // 62d355043fa6f1ffffff51
	VPCMPB $42, Y7, Y17, K7, K4                        // 62f375273fe72a
	VPCMPB $42, (CX), Y17, K7, K4                      // 62f375273f212a
	VPCMPB $42, 99(R15), Y17, K7, K4                   // 62d375273fa7630000002a
	VPCMPB $42, Y7, Y17, K7, K6                        // 62f375273ff72a
	VPCMPB $42, (CX), Y17, K7, K6                      // 62f375273f312a
	VPCMPB $42, 99(R15), Y17, K7, K6                   // 62d375273fb7630000002a
	VPCMPB $79, Z9, Z9, K2, K1                         // 62d3354a3fc94f
	VPCMPB $79, Z28, Z9, K2, K1                        // 6293354a3fcc4f
	VPCMPB $79, -7(DI)(R8*1), Z9, K2, K1               // 62b3354a3f8c07f9ffffff4f
	VPCMPB $79, (SP), Z9, K2, K1                       // 62f3354a3f0c244f
	VPCMPB $79, Z9, Z25, K2, K1                        // 62d335423fc94f
	VPCMPB $79, Z28, Z25, K2, K1                       // 629335423fcc4f
	VPCMPB $79, -7(DI)(R8*1), Z25, K2, K1              // 62b335423f8c07f9ffffff4f
	VPCMPB $79, (SP), Z25, K2, K1                      // 62f335423f0c244f
	VPCMPB $79, Z9, Z9, K2, K3                         // 62d3354a3fd94f
	VPCMPB $79, Z28, Z9, K2, K3                        // 6293354a3fdc4f
	VPCMPB $79, -7(DI)(R8*1), Z9, K2, K3               // 62b3354a3f9c07f9ffffff4f
	VPCMPB $79, (SP), Z9, K2, K3                       // 62f3354a3f1c244f
	VPCMPB $79, Z9, Z25, K2, K3                        // 62d335423fd94f
	VPCMPB $79, Z28, Z25, K2, K3                       // 629335423fdc4f
	VPCMPB $79, -7(DI)(R8*1), Z25, K2, K3              // 62b335423f9c07f9ffffff4f
	VPCMPB $79, (SP), Z25, K2, K3                      // 62f335423f1c244f
	VPCMPEQB X30, X0, K2, K4                           // 62917d0a74e6 or 6291fd0a74e6
	VPCMPEQB -7(DI)(R8*1), X0, K2, K4                  // 62b17d0a74a407f9ffffff or 62b1fd0a74a407f9ffffff
	VPCMPEQB (SP), X0, K2, K4                          // 62f17d0a742424 or 62f1fd0a742424
	VPCMPEQB X30, X0, K2, K5                           // 62917d0a74ee or 6291fd0a74ee
	VPCMPEQB -7(DI)(R8*1), X0, K2, K5                  // 62b17d0a74ac07f9ffffff or 62b1fd0a74ac07f9ffffff
	VPCMPEQB (SP), X0, K2, K5                          // 62f17d0a742c24 or 62f1fd0a742c24
	VPCMPEQB Y1, Y8, K2, K2                            // 62f13d2a74d1 or 62f1bd2a74d1
	VPCMPEQB -7(CX)(DX*1), Y8, K2, K2                  // 62f13d2a749411f9ffffff or 62f1bd2a749411f9ffffff
	VPCMPEQB -15(R14)(R15*4), Y8, K2, K2               // 62913d2a7494bef1ffffff or 6291bd2a7494bef1ffffff
	VPCMPEQB Y1, Y8, K2, K7                            // 62f13d2a74f9 or 62f1bd2a74f9
	VPCMPEQB -7(CX)(DX*1), Y8, K2, K7                  // 62f13d2a74bc11f9ffffff or 62f1bd2a74bc11f9ffffff
	VPCMPEQB -15(R14)(R15*4), Y8, K2, K7               // 62913d2a74bcbef1ffffff or 6291bd2a74bcbef1ffffff
	VPCMPEQB Z31, Z17, K3, K0                          // 6291754374c7 or 6291f54374c7
	VPCMPEQB Z0, Z17, K3, K0                           // 62f1754374c0 or 62f1f54374c0
	VPCMPEQB 99(R15)(R15*8), Z17, K3, K0               // 629175437484ff63000000 or 6291f5437484ff63000000
	VPCMPEQB 7(AX)(CX*8), Z17, K3, K0                  // 62f175437484c807000000 or 62f1f5437484c807000000
	VPCMPEQB Z31, Z23, K3, K0                          // 6291454374c7 or 6291c54374c7
	VPCMPEQB Z0, Z23, K3, K0                           // 62f1454374c0 or 62f1c54374c0
	VPCMPEQB 99(R15)(R15*8), Z23, K3, K0               // 629145437484ff63000000 or 6291c5437484ff63000000
	VPCMPEQB 7(AX)(CX*8), Z23, K3, K0                  // 62f145437484c807000000 or 62f1c5437484c807000000
	VPCMPEQB Z31, Z17, K3, K5                          // 6291754374ef or 6291f54374ef
	VPCMPEQB Z0, Z17, K3, K5                           // 62f1754374e8 or 62f1f54374e8
	VPCMPEQB 99(R15)(R15*8), Z17, K3, K5               // 6291754374acff63000000 or 6291f54374acff63000000
	VPCMPEQB 7(AX)(CX*8), Z17, K3, K5                  // 62f1754374acc807000000 or 62f1f54374acc807000000
	VPCMPEQB Z31, Z23, K3, K5                          // 6291454374ef or 6291c54374ef
	VPCMPEQB Z0, Z23, K3, K5                           // 62f1454374e8 or 62f1c54374e8
	VPCMPEQB 99(R15)(R15*8), Z23, K3, K5               // 6291454374acff63000000 or 6291c54374acff63000000
	VPCMPEQB 7(AX)(CX*8), Z23, K3, K5                  // 62f1454374acc807000000 or 62f1c54374acc807000000
	VPCMPEQW X8, X19, K7, K0                           // 62d1650775c0 or 62d1e50775c0
	VPCMPEQW (AX), X19, K7, K0                         // 62f165077500 or 62f1e5077500
	VPCMPEQW 7(SI), X19, K7, K0                        // 62f16507758607000000 or 62f1e507758607000000
	VPCMPEQW X8, X19, K7, K7                           // 62d1650775f8 or 62d1e50775f8
	VPCMPEQW (AX), X19, K7, K7                         // 62f165077538 or 62f1e5077538
	VPCMPEQW 7(SI), X19, K7, K7                        // 62f1650775be07000000 or 62f1e50775be07000000
	VPCMPEQW Y12, Y21, K1, K5                          // 62d1552175ec or 62d1d52175ec
	VPCMPEQW 17(SP)(BP*2), Y21, K1, K5                 // 62f1552175ac6c11000000 or 62f1d52175ac6c11000000
	VPCMPEQW -7(DI)(R8*4), Y21, K1, K5                 // 62b1552175ac87f9ffffff or 62b1d52175ac87f9ffffff
	VPCMPEQW Y12, Y21, K1, K4                          // 62d1552175e4 or 62d1d52175e4
	VPCMPEQW 17(SP)(BP*2), Y21, K1, K4                 // 62f1552175a46c11000000 or 62f1d52175a46c11000000
	VPCMPEQW -7(DI)(R8*4), Y21, K1, K4                 // 62b1552175a487f9ffffff or 62b1d52175a487f9ffffff
	VPCMPEQW Z26, Z30, K1, K4                          // 62910d4175e2 or 62918d4175e2
	VPCMPEQW Z22, Z30, K1, K4                          // 62b10d4175e6 or 62b18d4175e6
	VPCMPEQW 15(R8)(R14*4), Z30, K1, K4                // 62910d4175a4b00f000000 or 62918d4175a4b00f000000
	VPCMPEQW -7(CX)(DX*4), Z30, K1, K4                 // 62f10d4175a491f9ffffff or 62f18d4175a491f9ffffff
	VPCMPEQW Z26, Z5, K1, K4                           // 6291554975e2 or 6291d54975e2
	VPCMPEQW Z22, Z5, K1, K4                           // 62b1554975e6 or 62b1d54975e6
	VPCMPEQW 15(R8)(R14*4), Z5, K1, K4                 // 6291554975a4b00f000000 or 6291d54975a4b00f000000
	VPCMPEQW -7(CX)(DX*4), Z5, K1, K4                  // 62f1554975a491f9ffffff or 62f1d54975a491f9ffffff
	VPCMPEQW Z26, Z30, K1, K6                          // 62910d4175f2 or 62918d4175f2
	VPCMPEQW Z22, Z30, K1, K6                          // 62b10d4175f6 or 62b18d4175f6
	VPCMPEQW 15(R8)(R14*4), Z30, K1, K6                // 62910d4175b4b00f000000 or 62918d4175b4b00f000000
	VPCMPEQW -7(CX)(DX*4), Z30, K1, K6                 // 62f10d4175b491f9ffffff or 62f18d4175b491f9ffffff
	VPCMPEQW Z26, Z5, K1, K6                           // 6291554975f2 or 6291d54975f2
	VPCMPEQW Z22, Z5, K1, K6                           // 62b1554975f6 or 62b1d54975f6
	VPCMPEQW 15(R8)(R14*4), Z5, K1, K6                 // 6291554975b4b00f000000 or 6291d54975b4b00f000000
	VPCMPEQW -7(CX)(DX*4), Z5, K1, K6                  // 62f1554975b491f9ffffff or 62f1d54975b491f9ffffff
	VPCMPGTB X26, X8, K1, K1                           // 62913d0964ca or 6291bd0964ca
	VPCMPGTB (BX), X8, K1, K1                          // 62f13d09640b or 62f1bd09640b
	VPCMPGTB -17(BP)(SI*1), X8, K1, K1                 // 62f13d09648c35efffffff or 62f1bd09648c35efffffff
	VPCMPGTB X26, X8, K1, K3                           // 62913d0964da or 6291bd0964da
	VPCMPGTB (BX), X8, K1, K3                          // 62f13d09641b or 62f1bd09641b
	VPCMPGTB -17(BP)(SI*1), X8, K1, K3                 // 62f13d09649c35efffffff or 62f1bd09649c35efffffff
	VPCMPGTB Y1, Y9, K7, K6                            // 62f1352f64f1 or 62f1b52f64f1
	VPCMPGTB 15(R8), Y9, K7, K6                        // 62d1352f64b00f000000 or 62d1b52f64b00f000000
	VPCMPGTB (BP), Y9, K7, K6                          // 62f1352f647500 or 62f1b52f647500
	VPCMPGTB Y1, Y9, K7, K7                            // 62f1352f64f9 or 62f1b52f64f9
	VPCMPGTB 15(R8), Y9, K7, K7                        // 62d1352f64b80f000000 or 62d1b52f64b80f000000
	VPCMPGTB (BP), Y9, K7, K7                          // 62f1352f647d00 or 62f1b52f647d00
	VPCMPGTB Z16, Z7, K2, K6                           // 62b1454a64f0 or 62b1c54a64f0
	VPCMPGTB Z25, Z7, K2, K6                           // 6291454a64f1 or 6291c54a64f1
	VPCMPGTB (R8), Z7, K2, K6                          // 62d1454a6430 or 62d1c54a6430
	VPCMPGTB 15(DX)(BX*2), Z7, K2, K6                  // 62f1454a64b45a0f000000 or 62f1c54a64b45a0f000000
	VPCMPGTB Z16, Z21, K2, K6                          // 62b1554264f0 or 62b1d54264f0
	VPCMPGTB Z25, Z21, K2, K6                          // 6291554264f1 or 6291d54264f1
	VPCMPGTB (R8), Z21, K2, K6                         // 62d155426430 or 62d1d5426430
	VPCMPGTB 15(DX)(BX*2), Z21, K2, K6                 // 62f1554264b45a0f000000 or 62f1d54264b45a0f000000
	VPCMPGTB Z16, Z7, K2, K4                           // 62b1454a64e0 or 62b1c54a64e0
	VPCMPGTB Z25, Z7, K2, K4                           // 6291454a64e1 or 6291c54a64e1
	VPCMPGTB (R8), Z7, K2, K4                          // 62d1454a6420 or 62d1c54a6420
	VPCMPGTB 15(DX)(BX*2), Z7, K2, K4                  // 62f1454a64a45a0f000000 or 62f1c54a64a45a0f000000
	VPCMPGTB Z16, Z21, K2, K4                          // 62b1554264e0 or 62b1d54264e0
	VPCMPGTB Z25, Z21, K2, K4                          // 6291554264e1 or 6291d54264e1
	VPCMPGTB (R8), Z21, K2, K4                         // 62d155426420 or 62d1d5426420
	VPCMPGTB 15(DX)(BX*2), Z21, K2, K4                 // 62f1554264a45a0f000000 or 62f1d54264a45a0f000000
	VPCMPGTW X11, X23, K7, K3                          // 62d1450765db or 62d1c50765db
	VPCMPGTW 17(SP)(BP*1), X23, K7, K3                 // 62f14507659c2c11000000 or 62f1c507659c2c11000000
	VPCMPGTW -7(CX)(DX*8), X23, K7, K3                 // 62f14507659cd1f9ffffff or 62f1c507659cd1f9ffffff
	VPCMPGTW X11, X23, K7, K1                          // 62d1450765cb or 62d1c50765cb
	VPCMPGTW 17(SP)(BP*1), X23, K7, K1                 // 62f14507658c2c11000000 or 62f1c507658c2c11000000
	VPCMPGTW -7(CX)(DX*8), X23, K7, K1                 // 62f14507658cd1f9ffffff or 62f1c507658cd1f9ffffff
	VPCMPGTW Y21, Y12, K6, K5                          // 62b11d2e65ed or 62b19d2e65ed
	VPCMPGTW 7(AX)(CX*4), Y12, K6, K5                  // 62f11d2e65ac8807000000 or 62f19d2e65ac8807000000
	VPCMPGTW 7(AX)(CX*1), Y12, K6, K5                  // 62f11d2e65ac0807000000 or 62f19d2e65ac0807000000
	VPCMPGTW Y21, Y12, K6, K4                          // 62b11d2e65e5 or 62b19d2e65e5
	VPCMPGTW 7(AX)(CX*4), Y12, K6, K4                  // 62f11d2e65a48807000000 or 62f19d2e65a48807000000
	VPCMPGTW 7(AX)(CX*1), Y12, K6, K4                  // 62f11d2e65a40807000000 or 62f19d2e65a40807000000
	VPCMPGTW Z23, Z27, K3, K7                          // 62b1254365ff or 62b1a54365ff
	VPCMPGTW Z9, Z27, K3, K7                           // 62d1254365f9 or 62d1a54365f9
	VPCMPGTW 15(R8)(R14*1), Z27, K3, K7                // 6291254365bc300f000000 or 6291a54365bc300f000000
	VPCMPGTW 15(R8)(R14*2), Z27, K3, K7                // 6291254365bc700f000000 or 6291a54365bc700f000000
	VPCMPGTW Z23, Z25, K3, K7                          // 62b1354365ff or 62b1b54365ff
	VPCMPGTW Z9, Z25, K3, K7                           // 62d1354365f9 or 62d1b54365f9
	VPCMPGTW 15(R8)(R14*1), Z25, K3, K7                // 6291354365bc300f000000 or 6291b54365bc300f000000
	VPCMPGTW 15(R8)(R14*2), Z25, K3, K7                // 6291354365bc700f000000 or 6291b54365bc700f000000
	VPCMPGTW Z23, Z27, K3, K6                          // 62b1254365f7 or 62b1a54365f7
	VPCMPGTW Z9, Z27, K3, K6                           // 62d1254365f1 or 62d1a54365f1
	VPCMPGTW 15(R8)(R14*1), Z27, K3, K6                // 6291254365b4300f000000 or 6291a54365b4300f000000
	VPCMPGTW 15(R8)(R14*2), Z27, K3, K6                // 6291254365b4700f000000 or 6291a54365b4700f000000
	VPCMPGTW Z23, Z25, K3, K6                          // 62b1354365f7 or 62b1b54365f7
	VPCMPGTW Z9, Z25, K3, K6                           // 62d1354365f1 or 62d1b54365f1
	VPCMPGTW 15(R8)(R14*1), Z25, K3, K6                // 6291354365b4300f000000 or 6291b54365b4300f000000
	VPCMPGTW 15(R8)(R14*2), Z25, K3, K6                // 6291354365b4700f000000 or 6291b54365b4700f000000
	VPCMPUB $121, X0, X14, K7, K4                      // 62f30d0f3ee079
	VPCMPUB $121, 15(R8)(R14*1), X14, K7, K4           // 62930d0f3ea4300f00000079
	VPCMPUB $121, 15(R8)(R14*2), X14, K7, K4           // 62930d0f3ea4700f00000079
	VPCMPUB $121, X0, X14, K7, K6                      // 62f30d0f3ef079
	VPCMPUB $121, 15(R8)(R14*1), X14, K7, K6           // 62930d0f3eb4300f00000079
	VPCMPUB $121, 15(R8)(R14*2), X14, K7, K6           // 62930d0f3eb4700f00000079
	VPCMPUB $13, Y7, Y26, K2, K1                       // 62f32d223ecf0d
	VPCMPUB $13, 17(SP)(BP*8), Y26, K2, K1             // 62f32d223e8cec110000000d
	VPCMPUB $13, 17(SP)(BP*4), Y26, K2, K1             // 62f32d223e8cac110000000d
	VPCMPUB $13, Y7, Y26, K2, K3                       // 62f32d223edf0d
	VPCMPUB $13, 17(SP)(BP*8), Y26, K2, K3             // 62f32d223e9cec110000000d
	VPCMPUB $13, 17(SP)(BP*4), Y26, K2, K3             // 62f32d223e9cac110000000d
	VPCMPUB $65, Z8, Z14, K5, K6                       // 62d30d4d3ef041
	VPCMPUB $65, Z24, Z14, K5, K6                      // 62930d4d3ef041
	VPCMPUB $65, 99(R15)(R15*4), Z14, K5, K6           // 62930d4d3eb4bf6300000041
	VPCMPUB $65, 15(DX), Z14, K5, K6                   // 62f30d4d3eb20f00000041
	VPCMPUB $65, Z8, Z7, K5, K6                        // 62d3454d3ef041
	VPCMPUB $65, Z24, Z7, K5, K6                       // 6293454d3ef041
	VPCMPUB $65, 99(R15)(R15*4), Z7, K5, K6            // 6293454d3eb4bf6300000041
	VPCMPUB $65, 15(DX), Z7, K5, K6                    // 62f3454d3eb20f00000041
	VPCMPUB $65, Z8, Z14, K5, K7                       // 62d30d4d3ef841
	VPCMPUB $65, Z24, Z14, K5, K7                      // 62930d4d3ef841
	VPCMPUB $65, 99(R15)(R15*4), Z14, K5, K7           // 62930d4d3ebcbf6300000041
	VPCMPUB $65, 15(DX), Z14, K5, K7                   // 62f30d4d3eba0f00000041
	VPCMPUB $65, Z8, Z7, K5, K7                        // 62d3454d3ef841
	VPCMPUB $65, Z24, Z7, K5, K7                       // 6293454d3ef841
	VPCMPUB $65, 99(R15)(R15*4), Z7, K5, K7            // 6293454d3ebcbf6300000041
	VPCMPUB $65, 15(DX), Z7, K5, K7                    // 62f3454d3eba0f00000041
	VPCMPUW $79, X25, X5, K3, K1                       // 6293d50b3ec94f
	VPCMPUW $79, (CX), X5, K3, K1                      // 62f3d50b3e094f
	VPCMPUW $79, 99(R15), X5, K3, K1                   // 62d3d50b3e8f630000004f
	VPCMPUW $79, X25, X5, K3, K5                       // 6293d50b3ee94f
	VPCMPUW $79, (CX), X5, K3, K5                      // 62f3d50b3e294f
	VPCMPUW $79, 99(R15), X5, K3, K5                   // 62d3d50b3eaf630000004f
	VPCMPUW $64, Y6, Y22, K2, K3                       // 62f3cd223ede40
	VPCMPUW $64, 7(AX), Y22, K2, K3                    // 62f3cd223e980700000040
	VPCMPUW $64, (DI), Y22, K2, K3                     // 62f3cd223e1f40
	VPCMPUW $64, Y6, Y22, K2, K1                       // 62f3cd223ece40
	VPCMPUW $64, 7(AX), Y22, K2, K1                    // 62f3cd223e880700000040
	VPCMPUW $64, (DI), Y22, K2, K1                     // 62f3cd223e0f40
	VPCMPUW $27, Z14, Z15, K1, K5                      // 62d385493eee1b
	VPCMPUW $27, Z27, Z15, K1, K5                      // 629385493eeb1b
	VPCMPUW $27, -7(CX)(DX*1), Z15, K1, K5             // 62f385493eac11f9ffffff1b
	VPCMPUW $27, -15(R14)(R15*4), Z15, K1, K5          // 629385493eacbef1ffffff1b
	VPCMPUW $27, Z14, Z12, K1, K5                      // 62d39d493eee1b
	VPCMPUW $27, Z27, Z12, K1, K5                      // 62939d493eeb1b
	VPCMPUW $27, -7(CX)(DX*1), Z12, K1, K5             // 62f39d493eac11f9ffffff1b
	VPCMPUW $27, -15(R14)(R15*4), Z12, K1, K5          // 62939d493eacbef1ffffff1b
	VPCMPUW $27, Z14, Z15, K1, K4                      // 62d385493ee61b
	VPCMPUW $27, Z27, Z15, K1, K4                      // 629385493ee31b
	VPCMPUW $27, -7(CX)(DX*1), Z15, K1, K4             // 62f385493ea411f9ffffff1b
	VPCMPUW $27, -15(R14)(R15*4), Z15, K1, K4          // 629385493ea4bef1ffffff1b
	VPCMPUW $27, Z14, Z12, K1, K4                      // 62d39d493ee61b
	VPCMPUW $27, Z27, Z12, K1, K4                      // 62939d493ee31b
	VPCMPUW $27, -7(CX)(DX*1), Z12, K1, K4             // 62f39d493ea411f9ffffff1b
	VPCMPUW $27, -15(R14)(R15*4), Z12, K1, K4          // 62939d493ea4bef1ffffff1b
	VPCMPW $47, X9, X0, K2, K7                         // 62d3fd0a3ff92f
	VPCMPW $47, 99(R15)(R15*2), X0, K2, K7             // 6293fd0a3fbc7f630000002f
	VPCMPW $47, -7(DI), X0, K2, K7                     // 62f3fd0a3fbff9ffffff2f
	VPCMPW $47, X9, X0, K2, K6                         // 62d3fd0a3ff12f
	VPCMPW $47, 99(R15)(R15*2), X0, K2, K6             // 6293fd0a3fb47f630000002f
	VPCMPW $47, -7(DI), X0, K2, K6                     // 62f3fd0a3fb7f9ffffff2f
	VPCMPW $82, Y7, Y21, K1, K4                        // 62f3d5213fe752
	VPCMPW $82, 99(R15)(R15*1), Y21, K1, K4            // 6293d5213fa43f6300000052
	VPCMPW $82, (DX), Y21, K1, K4                      // 62f3d5213f2252
	VPCMPW $82, Y7, Y21, K1, K6                        // 62f3d5213ff752
	VPCMPW $82, 99(R15)(R15*1), Y21, K1, K6            // 6293d5213fb43f6300000052
	VPCMPW $82, (DX), Y21, K1, K6                      // 62f3d5213f3252
	VPCMPW $126, Z13, Z11, K7, K0                      // 62d3a54f3fc57e
	VPCMPW $126, Z14, Z11, K7, K0                      // 62d3a54f3fc67e
	VPCMPW $126, 15(DX)(BX*1), Z11, K7, K0             // 62f3a54f3f841a0f0000007e
	VPCMPW $126, -7(CX)(DX*2), Z11, K7, K0             // 62f3a54f3f8451f9ffffff7e
	VPCMPW $126, Z13, Z5, K7, K0                       // 62d3d54f3fc57e
	VPCMPW $126, Z14, Z5, K7, K0                       // 62d3d54f3fc67e
	VPCMPW $126, 15(DX)(BX*1), Z5, K7, K0              // 62f3d54f3f841a0f0000007e
	VPCMPW $126, -7(CX)(DX*2), Z5, K7, K0              // 62f3d54f3f8451f9ffffff7e
	VPCMPW $126, Z13, Z11, K7, K7                      // 62d3a54f3ffd7e
	VPCMPW $126, Z14, Z11, K7, K7                      // 62d3a54f3ffe7e
	VPCMPW $126, 15(DX)(BX*1), Z11, K7, K7             // 62f3a54f3fbc1a0f0000007e
	VPCMPW $126, -7(CX)(DX*2), Z11, K7, K7             // 62f3a54f3fbc51f9ffffff7e
	VPCMPW $126, Z13, Z5, K7, K7                       // 62d3d54f3ffd7e
	VPCMPW $126, Z14, Z5, K7, K7                       // 62d3d54f3ffe7e
	VPCMPW $126, 15(DX)(BX*1), Z5, K7, K7              // 62f3d54f3fbc1a0f0000007e
	VPCMPW $126, -7(CX)(DX*2), Z5, K7, K7              // 62f3d54f3fbc51f9ffffff7e
	VPERMI2W X16, X20, K2, X7                          // 62b2dd0275f8
	VPERMI2W 7(SI)(DI*1), X20, K2, X7                  // 62f2dd0275bc3e07000000
	VPERMI2W 15(DX)(BX*8), X20, K2, X7                 // 62f2dd0275bcda0f000000
	VPERMI2W Y18, Y14, K5, Y12                         // 62328d2d75e2
	VPERMI2W -7(CX)(DX*1), Y14, K5, Y12                // 62728d2d75a411f9ffffff
	VPERMI2W -15(R14)(R15*4), Y14, K5, Y12             // 62128d2d75a4bef1ffffff
	VPERMI2W Z28, Z12, K3, Z1                          // 62929d4b75cc
	VPERMI2W Z13, Z12, K3, Z1                          // 62d29d4b75cd
	VPERMI2W 99(R15)(R15*8), Z12, K3, Z1               // 62929d4b758cff63000000
	VPERMI2W 7(AX)(CX*8), Z12, K3, Z1                  // 62f29d4b758cc807000000
	VPERMI2W Z28, Z16, K3, Z1                          // 6292fd4375cc
	VPERMI2W Z13, Z16, K3, Z1                          // 62d2fd4375cd
	VPERMI2W 99(R15)(R15*8), Z16, K3, Z1               // 6292fd43758cff63000000
	VPERMI2W 7(AX)(CX*8), Z16, K3, Z1                  // 62f2fd43758cc807000000
	VPERMI2W Z28, Z12, K3, Z3                          // 62929d4b75dc
	VPERMI2W Z13, Z12, K3, Z3                          // 62d29d4b75dd
	VPERMI2W 99(R15)(R15*8), Z12, K3, Z3               // 62929d4b759cff63000000
	VPERMI2W 7(AX)(CX*8), Z12, K3, Z3                  // 62f29d4b759cc807000000
	VPERMI2W Z28, Z16, K3, Z3                          // 6292fd4375dc
	VPERMI2W Z13, Z16, K3, Z3                          // 62d2fd4375dd
	VPERMI2W 99(R15)(R15*8), Z16, K3, Z3               // 6292fd43759cff63000000
	VPERMI2W 7(AX)(CX*8), Z16, K3, Z3                  // 62f2fd43759cc807000000
	VPERMT2W X0, X0, K3, X14                           // 6272fd0b7df0
	VPERMT2W 15(R8)(R14*1), X0, K3, X14                // 6212fd0b7db4300f000000
	VPERMT2W 15(R8)(R14*2), X0, K3, X14                // 6212fd0b7db4700f000000
	VPERMT2W Y8, Y27, K2, Y22                          // 62c2a5227df0
	VPERMT2W 7(SI)(DI*8), Y27, K2, Y22                 // 62e2a5227db4fe07000000
	VPERMT2W -15(R14), Y27, K2, Y22                    // 62c2a5227db6f1ffffff
	VPERMT2W Z22, Z8, K1, Z14                          // 6232bd497df6
	VPERMT2W Z25, Z8, K1, Z14                          // 6212bd497df1
	VPERMT2W 17(SP)(BP*2), Z8, K1, Z14                 // 6272bd497db46c11000000
	VPERMT2W -7(DI)(R8*4), Z8, K1, Z14                 // 6232bd497db487f9ffffff
	VPERMT2W Z22, Z24, K1, Z14                         // 6232bd417df6
	VPERMT2W Z25, Z24, K1, Z14                         // 6212bd417df1
	VPERMT2W 17(SP)(BP*2), Z24, K1, Z14                // 6272bd417db46c11000000
	VPERMT2W -7(DI)(R8*4), Z24, K1, Z14                // 6232bd417db487f9ffffff
	VPERMT2W Z22, Z8, K1, Z7                           // 62b2bd497dfe
	VPERMT2W Z25, Z8, K1, Z7                           // 6292bd497df9
	VPERMT2W 17(SP)(BP*2), Z8, K1, Z7                  // 62f2bd497dbc6c11000000
	VPERMT2W -7(DI)(R8*4), Z8, K1, Z7                  // 62b2bd497dbc87f9ffffff
	VPERMT2W Z22, Z24, K1, Z7                          // 62b2bd417dfe
	VPERMT2W Z25, Z24, K1, Z7                          // 6292bd417df9
	VPERMT2W 17(SP)(BP*2), Z24, K1, Z7                 // 62f2bd417dbc6c11000000
	VPERMT2W -7(DI)(R8*4), Z24, K1, Z7                 // 62b2bd417dbc87f9ffffff
	VPERMW X17, X11, K2, X25                           // 6222a50a8dc9
	VPERMW (R14), X11, K2, X25                         // 6242a50a8d0e
	VPERMW -7(DI)(R8*8), X11, K2, X25                  // 6222a50a8d8cc7f9ffffff
	VPERMW Y9, Y22, K1, Y9                             // 6252cd218dc9
	VPERMW 7(SI)(DI*1), Y22, K1, Y9                    // 6272cd218d8c3e07000000
	VPERMW 15(DX)(BX*8), Y22, K1, Y9                   // 6272cd218d8cda0f000000
	VPERMW Z0, Z6, K7, Z1                              // 62f2cd4f8dc8
	VPERMW Z8, Z6, K7, Z1                              // 62d2cd4f8dc8
	VPERMW 15(R8), Z6, K7, Z1                          // 62d2cd4f8d880f000000
	VPERMW (BP), Z6, K7, Z1                            // 62f2cd4f8d4d00
	VPERMW Z0, Z2, K7, Z1                              // 62f2ed4f8dc8
	VPERMW Z8, Z2, K7, Z1                              // 62d2ed4f8dc8
	VPERMW 15(R8), Z2, K7, Z1                          // 62d2ed4f8d880f000000
	VPERMW (BP), Z2, K7, Z1                            // 62f2ed4f8d4d00
	VPERMW Z0, Z6, K7, Z16                             // 62e2cd4f8dc0
	VPERMW Z8, Z6, K7, Z16                             // 62c2cd4f8dc0
	VPERMW 15(R8), Z6, K7, Z16                         // 62c2cd4f8d800f000000
	VPERMW (BP), Z6, K7, Z16                           // 62e2cd4f8d4500
	VPERMW Z0, Z2, K7, Z16                             // 62e2ed4f8dc0
	VPERMW Z8, Z2, K7, Z16                             // 62c2ed4f8dc0
	VPERMW 15(R8), Z2, K7, Z16                         // 62c2ed4f8d800f000000
	VPERMW (BP), Z2, K7, Z16                           // 62e2ed4f8d4500
	VPEXTRB $79, X26, AX                               // 62637d0814d04f or 6263fd0814d04f
	VPEXTRB $79, X26, R9                               // 62437d0814d14f or 6243fd0814d14f
	VPEXTRB $79, X26, 7(SI)(DI*1)                      // 62637d0814543e074f or 6263fd0814543e074f
	VPEXTRB $79, X26, 15(DX)(BX*8)                     // 62637d081454da0f4f or 6263fd081454da0f4f
	VPMADDUBSW X21, X16, K2, X0                        // 62b27d0204c5 or 62b2fd0204c5
	VPMADDUBSW 15(R8)(R14*8), X16, K2, X0              // 62927d020484f00f000000 or 6292fd020484f00f000000
	VPMADDUBSW -15(R14)(R15*2), X16, K2, X0            // 62927d0204847ef1ffffff or 6292fd0204847ef1ffffff
	VPMADDUBSW Y3, Y31, K4, Y11                        // 6272052404db or 6272852404db
	VPMADDUBSW -17(BP)(SI*2), Y31, K4, Y11             // 62720524049c75efffffff or 62728524049c75efffffff
	VPMADDUBSW 7(AX)(CX*2), Y31, K4, Y11               // 62720524049c4807000000 or 62728524049c4807000000
	VPMADDUBSW Z6, Z22, K1, Z12                        // 62724d4104e6 or 6272cd4104e6
	VPMADDUBSW Z8, Z22, K1, Z12                        // 62524d4104e0 or 6252cd4104e0
	VPMADDUBSW 99(R15)(R15*1), Z22, K1, Z12            // 62124d4104a43f63000000 or 6212cd4104a43f63000000
	VPMADDUBSW (DX), Z22, K1, Z12                      // 62724d410422 or 6272cd410422
	VPMADDUBSW Z6, Z11, K1, Z12                        // 6272254904e6 or 6272a54904e6
	VPMADDUBSW Z8, Z11, K1, Z12                        // 6252254904e0 or 6252a54904e0
	VPMADDUBSW 99(R15)(R15*1), Z11, K1, Z12            // 6212254904a43f63000000 or 6212a54904a43f63000000
	VPMADDUBSW (DX), Z11, K1, Z12                      // 627225490422 or 6272a5490422
	VPMADDUBSW Z6, Z22, K1, Z27                        // 62624d4104de or 6262cd4104de
	VPMADDUBSW Z8, Z22, K1, Z27                        // 62424d4104d8 or 6242cd4104d8
	VPMADDUBSW 99(R15)(R15*1), Z22, K1, Z27            // 62024d41049c3f63000000 or 6202cd41049c3f63000000
	VPMADDUBSW (DX), Z22, K1, Z27                      // 62624d41041a or 6262cd41041a
	VPMADDUBSW Z6, Z11, K1, Z27                        // 6262254904de or 6262a54904de
	VPMADDUBSW Z8, Z11, K1, Z27                        // 6242254904d8 or 6242a54904d8
	VPMADDUBSW 99(R15)(R15*1), Z11, K1, Z27            // 62022549049c3f63000000 or 6202a549049c3f63000000
	VPMADDUBSW (DX), Z11, K1, Z27                      // 62622549041a or 6262a549041a
	VPMADDWD X22, X28, K3, X0                          // 62b11d03f5c6 or 62b19d03f5c6
	VPMADDWD -15(R14)(R15*1), X28, K3, X0              // 62911d03f5843ef1ffffff or 62919d03f5843ef1ffffff
	VPMADDWD -15(BX), X28, K3, X0                      // 62f11d03f583f1ffffff or 62f19d03f583f1ffffff
	VPMADDWD Y13, Y2, K4, Y14                          // 62516d2cf5f5 or 6251ed2cf5f5
	VPMADDWD 15(R8)(R14*1), Y2, K4, Y14                // 62116d2cf5b4300f000000 or 6211ed2cf5b4300f000000
	VPMADDWD 15(R8)(R14*2), Y2, K4, Y14                // 62116d2cf5b4700f000000 or 6211ed2cf5b4700f000000
	VPMADDWD Z9, Z12, K5, Z25                          // 62411d4df5c9 or 62419d4df5c9
	VPMADDWD Z12, Z12, K5, Z25                         // 62411d4df5cc or 62419d4df5cc
	VPMADDWD -17(BP)(SI*8), Z12, K5, Z25               // 62611d4df58cf5efffffff or 62619d4df58cf5efffffff
	VPMADDWD (R15), Z12, K5, Z25                       // 62411d4df50f or 62419d4df50f
	VPMADDWD Z9, Z17, K5, Z25                          // 62417545f5c9 or 6241f545f5c9
	VPMADDWD Z12, Z17, K5, Z25                         // 62417545f5cc or 6241f545f5cc
	VPMADDWD -17(BP)(SI*8), Z17, K5, Z25               // 62617545f58cf5efffffff or 6261f545f58cf5efffffff
	VPMADDWD (R15), Z17, K5, Z25                       // 62417545f50f or 6241f545f50f
	VPMADDWD Z9, Z12, K5, Z12                          // 62511d4df5e1 or 62519d4df5e1
	VPMADDWD Z12, Z12, K5, Z12                         // 62511d4df5e4 or 62519d4df5e4
	VPMADDWD -17(BP)(SI*8), Z12, K5, Z12               // 62711d4df5a4f5efffffff or 62719d4df5a4f5efffffff
	VPMADDWD (R15), Z12, K5, Z12                       // 62511d4df527 or 62519d4df527
	VPMADDWD Z9, Z17, K5, Z12                          // 62517545f5e1 or 6251f545f5e1
	VPMADDWD Z12, Z17, K5, Z12                         // 62517545f5e4 or 6251f545f5e4
	VPMADDWD -17(BP)(SI*8), Z17, K5, Z12               // 62717545f5a4f5efffffff or 6271f545f5a4f5efffffff
	VPMADDWD (R15), Z17, K5, Z12                       // 62517545f527 or 6251f545f527
	VPMAXSB X7, X19, K7, X7                            // 62f265073cff or 62f2e5073cff
	VPMAXSB 7(AX)(CX*4), X19, K7, X7                   // 62f265073cbc8807000000 or 62f2e5073cbc8807000000
	VPMAXSB 7(AX)(CX*1), X19, K7, X7                   // 62f265073cbc0807000000 or 62f2e5073cbc0807000000
	VPMAXSB Y22, Y15, K7, Y27                          // 6222052f3cde or 6222852f3cde
	VPMAXSB (R14), Y15, K7, Y27                        // 6242052f3c1e or 6242852f3c1e
	VPMAXSB -7(DI)(R8*8), Y15, K7, Y27                 // 6222052f3c9cc7f9ffffff or 6222852f3c9cc7f9ffffff
	VPMAXSB Z8, Z3, K6, Z6                             // 62d2654e3cf0 or 62d2e54e3cf0
	VPMAXSB Z2, Z3, K6, Z6                             // 62f2654e3cf2 or 62f2e54e3cf2
	VPMAXSB 7(SI)(DI*8), Z3, K6, Z6                    // 62f2654e3cb4fe07000000 or 62f2e54e3cb4fe07000000
	VPMAXSB -15(R14), Z3, K6, Z6                       // 62d2654e3cb6f1ffffff or 62d2e54e3cb6f1ffffff
	VPMAXSB Z8, Z21, K6, Z6                            // 62d255463cf0 or 62d2d5463cf0
	VPMAXSB Z2, Z21, K6, Z6                            // 62f255463cf2 or 62f2d5463cf2
	VPMAXSB 7(SI)(DI*8), Z21, K6, Z6                   // 62f255463cb4fe07000000 or 62f2d5463cb4fe07000000
	VPMAXSB -15(R14), Z21, K6, Z6                      // 62d255463cb6f1ffffff or 62d2d5463cb6f1ffffff
	VPMAXSB Z8, Z3, K6, Z25                            // 6242654e3cc8 or 6242e54e3cc8
	VPMAXSB Z2, Z3, K6, Z25                            // 6262654e3cca or 6262e54e3cca
	VPMAXSB 7(SI)(DI*8), Z3, K6, Z25                   // 6262654e3c8cfe07000000 or 6262e54e3c8cfe07000000
	VPMAXSB -15(R14), Z3, K6, Z25                      // 6242654e3c8ef1ffffff or 6242e54e3c8ef1ffffff
	VPMAXSB Z8, Z21, K6, Z25                           // 624255463cc8 or 6242d5463cc8
	VPMAXSB Z2, Z21, K6, Z25                           // 626255463cca or 6262d5463cca
	VPMAXSB 7(SI)(DI*8), Z21, K6, Z25                  // 626255463c8cfe07000000 or 6262d5463c8cfe07000000
	VPMAXSB -15(R14), Z21, K6, Z25                     // 624255463c8ef1ffffff or 6242d5463c8ef1ffffff
	VPMAXSW X12, X0, K5, X12                           // 62517d0deee4 or 6251fd0deee4
	VPMAXSW 7(SI)(DI*4), X0, K5, X12                   // 62717d0deea4be07000000 or 6271fd0deea4be07000000
	VPMAXSW -7(DI)(R8*2), X0, K5, X12                  // 62317d0deea447f9ffffff or 6231fd0deea447f9ffffff
	VPMAXSW Y14, Y19, K3, Y23                          // 62c16523eefe or 62c1e523eefe
	VPMAXSW 99(R15)(R15*2), Y19, K3, Y23               // 62816523eebc7f63000000 or 6281e523eebc7f63000000
	VPMAXSW -7(DI), Y19, K3, Y23                       // 62e16523eebff9ffffff or 62e1e523eebff9ffffff
	VPMAXSW Z18, Z11, K4, Z12                          // 6231254ceee2 or 6231a54ceee2
	VPMAXSW Z24, Z11, K4, Z12                          // 6211254ceee0 or 6211a54ceee0
	VPMAXSW -7(CX), Z11, K4, Z12                       // 6271254ceea1f9ffffff or 6271a54ceea1f9ffffff
	VPMAXSW 15(DX)(BX*4), Z11, K4, Z12                 // 6271254ceea49a0f000000 or 6271a54ceea49a0f000000
	VPMAXSW Z18, Z5, K4, Z12                           // 6231554ceee2 or 6231d54ceee2
	VPMAXSW Z24, Z5, K4, Z12                           // 6211554ceee0 or 6211d54ceee0
	VPMAXSW -7(CX), Z5, K4, Z12                        // 6271554ceea1f9ffffff or 6271d54ceea1f9ffffff
	VPMAXSW 15(DX)(BX*4), Z5, K4, Z12                  // 6271554ceea49a0f000000 or 6271d54ceea49a0f000000
	VPMAXSW Z18, Z11, K4, Z22                          // 62a1254ceef2 or 62a1a54ceef2
	VPMAXSW Z24, Z11, K4, Z22                          // 6281254ceef0 or 6281a54ceef0
	VPMAXSW -7(CX), Z11, K4, Z22                       // 62e1254ceeb1f9ffffff or 62e1a54ceeb1f9ffffff
	VPMAXSW 15(DX)(BX*4), Z11, K4, Z22                 // 62e1254ceeb49a0f000000 or 62e1a54ceeb49a0f000000
	VPMAXSW Z18, Z5, K4, Z22                           // 62a1554ceef2 or 62a1d54ceef2
	VPMAXSW Z24, Z5, K4, Z22                           // 6281554ceef0 or 6281d54ceef0
	VPMAXSW -7(CX), Z5, K4, Z22                        // 62e1554ceeb1f9ffffff or 62e1d54ceeb1f9ffffff
	VPMAXSW 15(DX)(BX*4), Z5, K4, Z22                  // 62e1554ceeb49a0f000000 or 62e1d54ceeb49a0f000000
	VPMAXUB X17, X5, K2, X14                           // 6231550adef1 or 6231d50adef1
	VPMAXUB 17(SP), X5, K2, X14                        // 6271550adeb42411000000 or 6271d50adeb42411000000
	VPMAXUB -17(BP)(SI*4), X5, K2, X14                 // 6271550adeb4b5efffffff or 6271d50adeb4b5efffffff
	VPMAXUB Y16, Y5, K2, Y21                           // 62a1552adee8 or 62a1d52adee8
	VPMAXUB -7(CX)(DX*1), Y5, K2, Y21                  // 62e1552adeac11f9ffffff or 62e1d52adeac11f9ffffff
	VPMAXUB -15(R14)(R15*4), Y5, K2, Y21               // 6281552adeacbef1ffffff or 6281d52adeacbef1ffffff
	VPMAXUB Z6, Z7, K3, Z2                             // 62f1454bded6 or 62f1c54bded6
	VPMAXUB Z16, Z7, K3, Z2                            // 62b1454bded0 or 62b1c54bded0
	VPMAXUB 99(R15)(R15*8), Z7, K3, Z2                 // 6291454bde94ff63000000 or 6291c54bde94ff63000000
	VPMAXUB 7(AX)(CX*8), Z7, K3, Z2                    // 62f1454bde94c807000000 or 62f1c54bde94c807000000
	VPMAXUB Z6, Z13, K3, Z2                            // 62f1154bded6 or 62f1954bded6
	VPMAXUB Z16, Z13, K3, Z2                           // 62b1154bded0 or 62b1954bded0
	VPMAXUB 99(R15)(R15*8), Z13, K3, Z2                // 6291154bde94ff63000000 or 6291954bde94ff63000000
	VPMAXUB 7(AX)(CX*8), Z13, K3, Z2                   // 62f1154bde94c807000000 or 62f1954bde94c807000000
	VPMAXUB Z6, Z7, K3, Z21                            // 62e1454bdeee or 62e1c54bdeee
	VPMAXUB Z16, Z7, K3, Z21                           // 62a1454bdee8 or 62a1c54bdee8
	VPMAXUB 99(R15)(R15*8), Z7, K3, Z21                // 6281454bdeacff63000000 or 6281c54bdeacff63000000
	VPMAXUB 7(AX)(CX*8), Z7, K3, Z21                   // 62e1454bdeacc807000000 or 62e1c54bdeacc807000000
	VPMAXUB Z6, Z13, K3, Z21                           // 62e1154bdeee or 62e1954bdeee
	VPMAXUB Z16, Z13, K3, Z21                          // 62a1154bdee8 or 62a1954bdee8
	VPMAXUB 99(R15)(R15*8), Z13, K3, Z21               // 6281154bdeacff63000000 or 6281954bdeacff63000000
	VPMAXUB 7(AX)(CX*8), Z13, K3, Z21                  // 62e1154bdeacc807000000 or 62e1954bdeacc807000000
	VPMAXUW X9, X24, K7, X28                           // 62423d073ee1 or 6242bd073ee1
	VPMAXUW -17(BP)(SI*8), X24, K7, X28                // 62623d073ea4f5efffffff or 6262bd073ea4f5efffffff
	VPMAXUW (R15), X24, K7, X28                        // 62423d073e27 or 6242bd073e27
	VPMAXUW Y7, Y19, K1, Y11                           // 627265213edf or 6272e5213edf
	VPMAXUW 17(SP)(BP*2), Y19, K1, Y11                 // 627265213e9c6c11000000 or 6272e5213e9c6c11000000
	VPMAXUW -7(DI)(R8*4), Y19, K1, Y11                 // 623265213e9c87f9ffffff or 6232e5213e9c87f9ffffff
	VPMAXUW Z12, Z1, K1, Z20                           // 62c275493ee4 or 62c2f5493ee4
	VPMAXUW Z16, Z1, K1, Z20                           // 62a275493ee0 or 62a2f5493ee0
	VPMAXUW 15(R8)(R14*4), Z1, K1, Z20                 // 628275493ea4b00f000000 or 6282f5493ea4b00f000000
	VPMAXUW -7(CX)(DX*4), Z1, K1, Z20                  // 62e275493ea491f9ffffff or 62e2f5493ea491f9ffffff
	VPMAXUW Z12, Z3, K1, Z20                           // 62c265493ee4 or 62c2e5493ee4
	VPMAXUW Z16, Z3, K1, Z20                           // 62a265493ee0 or 62a2e5493ee0
	VPMAXUW 15(R8)(R14*4), Z3, K1, Z20                 // 628265493ea4b00f000000 or 6282e5493ea4b00f000000
	VPMAXUW -7(CX)(DX*4), Z3, K1, Z20                  // 62e265493ea491f9ffffff or 62e2e5493ea491f9ffffff
	VPMAXUW Z12, Z1, K1, Z9                            // 625275493ecc or 6252f5493ecc
	VPMAXUW Z16, Z1, K1, Z9                            // 623275493ec8 or 6232f5493ec8
	VPMAXUW 15(R8)(R14*4), Z1, K1, Z9                  // 621275493e8cb00f000000 or 6212f5493e8cb00f000000
	VPMAXUW -7(CX)(DX*4), Z1, K1, Z9                   // 627275493e8c91f9ffffff or 6272f5493e8c91f9ffffff
	VPMAXUW Z12, Z3, K1, Z9                            // 625265493ecc or 6252e5493ecc
	VPMAXUW Z16, Z3, K1, Z9                            // 623265493ec8 or 6232e5493ec8
	VPMAXUW 15(R8)(R14*4), Z3, K1, Z9                  // 621265493e8cb00f000000 or 6212e5493e8cb00f000000
	VPMAXUW -7(CX)(DX*4), Z3, K1, Z9                   // 627265493e8c91f9ffffff or 6272e5493e8c91f9ffffff
	VPMINSB X18, X26, K1, X15                          // 62322d0138fa or 6232ad0138fa
	VPMINSB 7(SI)(DI*8), X26, K1, X15                  // 62722d0138bcfe07000000 or 6272ad0138bcfe07000000
	VPMINSB -15(R14), X26, K1, X15                     // 62522d0138bef1ffffff or 6252ad0138bef1ffffff
	VPMINSB Y3, Y0, K7, Y6                             // 62f27d2f38f3 or 62f2fd2f38f3
	VPMINSB 15(R8), Y0, K7, Y6                         // 62d27d2f38b00f000000 or 62d2fd2f38b00f000000
	VPMINSB (BP), Y0, K7, Y6                           // 62f27d2f387500 or 62f2fd2f387500
	VPMINSB Z3, Z14, K2, Z28                           // 62620d4a38e3 or 62628d4a38e3
	VPMINSB Z12, Z14, K2, Z28                          // 62420d4a38e4 or 62428d4a38e4
	VPMINSB (R8), Z14, K2, Z28                         // 62420d4a3820 or 62428d4a3820
	VPMINSB 15(DX)(BX*2), Z14, K2, Z28                 // 62620d4a38a45a0f000000 or 62628d4a38a45a0f000000
	VPMINSB Z3, Z28, K2, Z28                           // 62621d4238e3 or 62629d4238e3
	VPMINSB Z12, Z28, K2, Z28                          // 62421d4238e4 or 62429d4238e4
	VPMINSB (R8), Z28, K2, Z28                         // 62421d423820 or 62429d423820
	VPMINSB 15(DX)(BX*2), Z28, K2, Z28                 // 62621d4238a45a0f000000 or 62629d4238a45a0f000000
	VPMINSB Z3, Z14, K2, Z13                           // 62720d4a38eb or 62728d4a38eb
	VPMINSB Z12, Z14, K2, Z13                          // 62520d4a38ec or 62528d4a38ec
	VPMINSB (R8), Z14, K2, Z13                         // 62520d4a3828 or 62528d4a3828
	VPMINSB 15(DX)(BX*2), Z14, K2, Z13                 // 62720d4a38ac5a0f000000 or 62728d4a38ac5a0f000000
	VPMINSB Z3, Z28, K2, Z13                           // 62721d4238eb or 62729d4238eb
	VPMINSB Z12, Z28, K2, Z13                          // 62521d4238ec or 62529d4238ec
	VPMINSB (R8), Z28, K2, Z13                         // 62521d423828 or 62529d423828
	VPMINSB 15(DX)(BX*2), Z28, K2, Z13                 // 62721d4238ac5a0f000000 or 62729d4238ac5a0f000000
	VPMINSW X24, X0, K7, X0                            // 62917d0feac0 or 6291fd0feac0
	VPMINSW -7(CX), X0, K7, X0                         // 62f17d0fea81f9ffffff or 62f1fd0fea81f9ffffff
	VPMINSW 15(DX)(BX*4), X0, K7, X0                   // 62f17d0fea849a0f000000 or 62f1fd0fea849a0f000000
	VPMINSW Y22, Y0, K6, Y7                            // 62b17d2eeafe or 62b1fd2eeafe
	VPMINSW 7(AX)(CX*4), Y0, K6, Y7                    // 62f17d2eeabc8807000000 or 62f1fd2eeabc8807000000
	VPMINSW 7(AX)(CX*1), Y0, K6, Y7                    // 62f17d2eeabc0807000000 or 62f1fd2eeabc0807000000
	VPMINSW Z23, Z20, K3, Z16                          // 62a15d43eac7 or 62a1dd43eac7
	VPMINSW Z19, Z20, K3, Z16                          // 62a15d43eac3 or 62a1dd43eac3
	VPMINSW 15(R8)(R14*1), Z20, K3, Z16                // 62815d43ea84300f000000 or 6281dd43ea84300f000000
	VPMINSW 15(R8)(R14*2), Z20, K3, Z16                // 62815d43ea84700f000000 or 6281dd43ea84700f000000
	VPMINSW Z23, Z0, K3, Z16                           // 62a17d4beac7 or 62a1fd4beac7
	VPMINSW Z19, Z0, K3, Z16                           // 62a17d4beac3 or 62a1fd4beac3
	VPMINSW 15(R8)(R14*1), Z0, K3, Z16                 // 62817d4bea84300f000000 or 6281fd4bea84300f000000
	VPMINSW 15(R8)(R14*2), Z0, K3, Z16                 // 62817d4bea84700f000000 or 6281fd4bea84700f000000
	VPMINSW Z23, Z20, K3, Z9                           // 62315d43eacf or 6231dd43eacf
	VPMINSW Z19, Z20, K3, Z9                           // 62315d43eacb or 6231dd43eacb
	VPMINSW 15(R8)(R14*1), Z20, K3, Z9                 // 62115d43ea8c300f000000 or 6211dd43ea8c300f000000
	VPMINSW 15(R8)(R14*2), Z20, K3, Z9                 // 62115d43ea8c700f000000 or 6211dd43ea8c700f000000
	VPMINSW Z23, Z0, K3, Z9                            // 62317d4beacf or 6231fd4beacf
	VPMINSW Z19, Z0, K3, Z9                            // 62317d4beacb or 6231fd4beacb
	VPMINSW 15(R8)(R14*1), Z0, K3, Z9                  // 62117d4bea8c300f000000 or 6211fd4bea8c300f000000
	VPMINSW 15(R8)(R14*2), Z0, K3, Z9                  // 62117d4bea8c700f000000 or 6211fd4bea8c700f000000
	VPMINUB X9, X7, K7, X20                            // 62c1450fdae1 or 62c1c50fdae1
	VPMINUB 99(R15)(R15*8), X7, K7, X20                // 6281450fdaa4ff63000000 or 6281c50fdaa4ff63000000
	VPMINUB 7(AX)(CX*8), X7, K7, X20                   // 62e1450fdaa4c807000000 or 62e1c50fdaa4c807000000
	VPMINUB Y1, Y12, K4, Y13                           // 62711d2cdae9 or 62719d2cdae9
	VPMINUB (SI), Y12, K4, Y13                         // 62711d2cda2e or 62719d2cda2e
	VPMINUB 7(SI)(DI*2), Y12, K4, Y13                  // 62711d2cdaac7e07000000 or 62719d2cdaac7e07000000
	VPMINUB Z24, Z0, K4, Z0                            // 62917d4cdac0 or 6291fd4cdac0
	VPMINUB Z12, Z0, K4, Z0                            // 62d17d4cdac4 or 62d1fd4cdac4
	VPMINUB (R14), Z0, K4, Z0                          // 62d17d4cda06 or 62d1fd4cda06
	VPMINUB -7(DI)(R8*8), Z0, K4, Z0                   // 62b17d4cda84c7f9ffffff or 62b1fd4cda84c7f9ffffff
	VPMINUB Z24, Z25, K4, Z0                           // 62913544dac0 or 6291b544dac0
	VPMINUB Z12, Z25, K4, Z0                           // 62d13544dac4 or 62d1b544dac4
	VPMINUB (R14), Z25, K4, Z0                         // 62d13544da06 or 62d1b544da06
	VPMINUB -7(DI)(R8*8), Z25, K4, Z0                  // 62b13544da84c7f9ffffff or 62b1b544da84c7f9ffffff
	VPMINUB Z24, Z0, K4, Z11                           // 62117d4cdad8 or 6211fd4cdad8
	VPMINUB Z12, Z0, K4, Z11                           // 62517d4cdadc or 6251fd4cdadc
	VPMINUB (R14), Z0, K4, Z11                         // 62517d4cda1e or 6251fd4cda1e
	VPMINUB -7(DI)(R8*8), Z0, K4, Z11                  // 62317d4cda9cc7f9ffffff or 6231fd4cda9cc7f9ffffff
	VPMINUB Z24, Z25, K4, Z11                          // 62113544dad8 or 6211b544dad8
	VPMINUB Z12, Z25, K4, Z11                          // 62513544dadc or 6251b544dadc
	VPMINUB (R14), Z25, K4, Z11                        // 62513544da1e or 6251b544da1e
	VPMINUB -7(DI)(R8*8), Z25, K4, Z11                 // 62313544da9cc7f9ffffff or 6231b544da9cc7f9ffffff
	VPMINUW X13, X11, K2, X1                           // 62d2250a3acd or 62d2a50a3acd
	VPMINUW 15(R8)(R14*4), X11, K2, X1                 // 6292250a3a8cb00f000000 or 6292a50a3a8cb00f000000
	VPMINUW -7(CX)(DX*4), X11, K2, X1                  // 62f2250a3a8c91f9ffffff or 62f2a50a3a8c91f9ffffff
	VPMINUW Y13, Y28, K3, Y1                           // 62d21d233acd or 62d29d233acd
	VPMINUW 17(SP), Y28, K3, Y1                        // 62f21d233a8c2411000000 or 62f29d233a8c2411000000
	VPMINUW -17(BP)(SI*4), Y28, K3, Y1                 // 62f21d233a8cb5efffffff or 62f29d233a8cb5efffffff
	VPMINUW Z21, Z31, K3, Z17                          // 62a205433acd or 62a285433acd
	VPMINUW Z9, Z31, K3, Z17                           // 62c205433ac9 or 62c285433ac9
	VPMINUW 99(R15)(R15*2), Z31, K3, Z17               // 628205433a8c7f63000000 or 628285433a8c7f63000000
	VPMINUW -7(DI), Z31, K3, Z17                       // 62e205433a8ff9ffffff or 62e285433a8ff9ffffff
	VPMINUW Z21, Z0, K3, Z17                           // 62a27d4b3acd or 62a2fd4b3acd
	VPMINUW Z9, Z0, K3, Z17                            // 62c27d4b3ac9 or 62c2fd4b3ac9
	VPMINUW 99(R15)(R15*2), Z0, K3, Z17                // 62827d4b3a8c7f63000000 or 6282fd4b3a8c7f63000000
	VPMINUW -7(DI), Z0, K3, Z17                        // 62e27d4b3a8ff9ffffff or 62e2fd4b3a8ff9ffffff
	VPMINUW Z21, Z31, K3, Z23                          // 62a205433afd or 62a285433afd
	VPMINUW Z9, Z31, K3, Z23                           // 62c205433af9 or 62c285433af9
	VPMINUW 99(R15)(R15*2), Z31, K3, Z23               // 628205433abc7f63000000 or 628285433abc7f63000000
	VPMINUW -7(DI), Z31, K3, Z23                       // 62e205433abff9ffffff or 62e285433abff9ffffff
	VPMINUW Z21, Z0, K3, Z23                           // 62a27d4b3afd or 62a2fd4b3afd
	VPMINUW Z9, Z0, K3, Z23                            // 62c27d4b3af9 or 62c2fd4b3af9
	VPMINUW 99(R15)(R15*2), Z0, K3, Z23                // 62827d4b3abc7f63000000 or 6282fd4b3abc7f63000000
	VPMINUW -7(DI), Z0, K3, Z23                        // 62e27d4b3abff9ffffff or 62e2fd4b3abff9ffffff
	VPMOVB2M X0, K5                                    // 62f27e0829e8
	VPMOVB2M X0, K4                                    // 62f27e0829e0
	VPMOVB2M Y7, K4                                    // 62f27e2829e7
	VPMOVB2M Y7, K6                                    // 62f27e2829f7
	VPMOVB2M Z6, K1                                    // 62f27e4829ce
	VPMOVB2M Z9, K1                                    // 62d27e4829c9
	VPMOVB2M Z6, K3                                    // 62f27e4829de
	VPMOVB2M Z9, K3                                    // 62d27e4829d9
	VPMOVM2B K4, X26                                   // 62627e0828d4
	VPMOVM2B K5, X26                                   // 62627e0828d5
	VPMOVM2B K2, Y1                                    // 62f27e2828ca
	VPMOVM2B K7, Y1                                    // 62f27e2828cf
	VPMOVM2B K0, Z26                                   // 62627e4828d0
	VPMOVM2B K5, Z26                                   // 62627e4828d5
	VPMOVM2B K0, Z22                                   // 62e27e4828f0
	VPMOVM2B K5, Z22                                   // 62e27e4828f5
	VPMOVM2W K0, X16                                   // 62e2fe0828c0
	VPMOVM2W K7, X16                                   // 62e2fe0828c7
	VPMOVM2W K5, Y2                                    // 62f2fe2828d5
	VPMOVM2W K4, Y2                                    // 62f2fe2828d4
	VPMOVM2W K4, Z14                                   // 6272fe4828f4
	VPMOVM2W K6, Z14                                   // 6272fe4828f6
	VPMOVM2W K4, Z13                                   // 6272fe4828ec
	VPMOVM2W K6, Z13                                   // 6272fe4828ee
	VPMOVSWB X18, K3, X0                               // 62e27e0b20d0
	VPMOVSWB X18, K3, -7(CX)                           // 62e27e0b2091f9ffffff
	VPMOVSWB X18, K3, 15(DX)(BX*4)                     // 62e27e0b20949a0f000000
	VPMOVSWB Y6, K3, X8                                // 62d27e2b20f0
	VPMOVSWB Y6, K3, -7(CX)(DX*1)                      // 62f27e2b20b411f9ffffff
	VPMOVSWB Y6, K3, -15(R14)(R15*4)                   // 62927e2b20b4bef1ffffff
	VPMOVSWB Z22, K3, Y21                              // 62a27e4b20f5
	VPMOVSWB Z25, K3, Y21                              // 62227e4b20cd
	VPMOVSWB Z22, K3, 7(SI)(DI*1)                      // 62e27e4b20b43e07000000
	VPMOVSWB Z25, K3, 7(SI)(DI*1)                      // 62627e4b208c3e07000000
	VPMOVSWB Z22, K3, 15(DX)(BX*8)                     // 62e27e4b20b4da0f000000
	VPMOVSWB Z25, K3, 15(DX)(BX*8)                     // 62627e4b208cda0f000000
	VPMOVSXBW X13, K1, Y28                             // 62427d2920e5 or 6242fd2920e5
	VPMOVSXBW -17(BP), K1, Y28                         // 62627d2920a5efffffff or 6262fd2920a5efffffff
	VPMOVSXBW -15(R14)(R15*8), K1, Y28                 // 62027d2920a4fef1ffffff or 6202fd2920a4fef1ffffff
	VPMOVSXBW X24, K1, X8                              // 62127d0920c0 or 6212fd0920c0
	VPMOVSXBW (BX), K1, X8                             // 62727d092003 or 6272fd092003
	VPMOVSXBW -17(BP)(SI*1), K1, X8                    // 62727d09208435efffffff or 6272fd09208435efffffff
	VPMOVSXBW Y20, K7, Z0                              // 62b27d4f20c4 or 62b2fd4f20c4
	VPMOVSXBW -7(DI)(R8*1), K7, Z0                     // 62b27d4f208407f9ffffff or 62b2fd4f208407f9ffffff
	VPMOVSXBW (SP), K7, Z0                             // 62f27d4f200424 or 62f2fd4f200424
	VPMOVSXBW Y20, K7, Z8                              // 62327d4f20c4 or 6232fd4f20c4
	VPMOVSXBW -7(DI)(R8*1), K7, Z8                     // 62327d4f208407f9ffffff or 6232fd4f208407f9ffffff
	VPMOVSXBW (SP), K7, Z8                             // 62727d4f200424 or 6272fd4f200424
	VPMOVUSWB X6, K1, X6                               // 62f27e0910f6
	VPMOVUSWB X6, K1, 99(R15)(R15*2)                   // 62927e0910b47f63000000
	VPMOVUSWB X6, K1, -7(DI)                           // 62f27e0910b7f9ffffff
	VPMOVUSWB Y15, K2, X22                             // 62327e2a10fe
	VPMOVUSWB Y15, K2, 7(SI)(DI*4)                     // 62727e2a10bcbe07000000
	VPMOVUSWB Y15, K2, -7(DI)(R8*2)                    // 62327e2a10bc47f9ffffff
	VPMOVUSWB Z28, K1, Y1                              // 62627e4910e1
	VPMOVUSWB Z6, K1, Y1                               // 62f27e4910f1
	VPMOVUSWB Z28, K1, 15(R8)(R14*4)                   // 62027e4910a4b00f000000
	VPMOVUSWB Z6, K1, 15(R8)(R14*4)                    // 62927e4910b4b00f000000
	VPMOVUSWB Z28, K1, -7(CX)(DX*4)                    // 62627e4910a491f9ffffff
	VPMOVUSWB Z6, K1, -7(CX)(DX*4)                     // 62f27e4910b491f9ffffff
	VPMOVW2M X12, K4                                   // 62d2fe0829e4
	VPMOVW2M X12, K6                                   // 62d2fe0829f4
	VPMOVW2M Y27, K4                                   // 6292fe2829e3
	VPMOVW2M Y27, K5                                   // 6292fe2829eb
	VPMOVW2M Z13, K2                                   // 62d2fe4829d5
	VPMOVW2M Z21, K2                                   // 62b2fe4829d5
	VPMOVW2M Z13, K7                                   // 62d2fe4829fd
	VPMOVW2M Z21, K7                                   // 62b2fe4829fd
	VPMOVWB X28, K7, X16                               // 62227e0f30e0
	VPMOVWB X28, K7, -7(CX)(DX*1)                      // 62627e0f30a411f9ffffff
	VPMOVWB X28, K7, -15(R14)(R15*4)                   // 62027e0f30a4bef1ffffff
	VPMOVWB Y19, K1, X8                                // 62c27e2930d8
	VPMOVWB Y19, K1, 17(SP)                            // 62e27e29309c2411000000
	VPMOVWB Y19, K1, -17(BP)(SI*4)                     // 62e27e29309cb5efffffff
	VPMOVWB Z26, K1, Y5                                // 62627e4930d5
	VPMOVWB Z3, K1, Y5                                 // 62f27e4930dd
	VPMOVWB Z26, K1, (R8)                              // 62427e493010
	VPMOVWB Z3, K1, (R8)                               // 62d27e493018
	VPMOVWB Z26, K1, 15(DX)(BX*2)                      // 62627e4930945a0f000000
	VPMOVWB Z3, K1, 15(DX)(BX*2)                       // 62f27e49309c5a0f000000
	VPMOVZXBW X0, K4, Y21                              // 62e27d2c30e8 or 62e2fd2c30e8
	VPMOVZXBW 99(R15)(R15*1), K4, Y21                  // 62827d2c30ac3f63000000 or 6282fd2c30ac3f63000000
	VPMOVZXBW (DX), K4, Y21                            // 62e27d2c302a or 62e2fd2c302a
	VPMOVZXBW X11, K5, X25                             // 62427d0d30cb or 6242fd0d30cb
	VPMOVZXBW 17(SP)(BP*2), K5, X25                    // 62627d0d308c6c11000000 or 6262fd0d308c6c11000000
	VPMOVZXBW -7(DI)(R8*4), K5, X25                    // 62227d0d308c87f9ffffff or 6222fd0d308c87f9ffffff
	VPMOVZXBW Y7, K7, Z11                              // 62727d4f30df or 6272fd4f30df
	VPMOVZXBW 17(SP)(BP*1), K7, Z11                    // 62727d4f309c2c11000000 or 6272fd4f309c2c11000000
	VPMOVZXBW -7(CX)(DX*8), K7, Z11                    // 62727d4f309cd1f9ffffff or 6272fd4f309cd1f9ffffff
	VPMOVZXBW Y7, K7, Z25                              // 62627d4f30cf or 6262fd4f30cf
	VPMOVZXBW 17(SP)(BP*1), K7, Z25                    // 62627d4f308c2c11000000 or 6262fd4f308c2c11000000
	VPMOVZXBW -7(CX)(DX*8), K7, Z25                    // 62627d4f308cd1f9ffffff or 6262fd4f308cd1f9ffffff
	VPMULHRSW X30, X15, K2, X11                        // 6212050a0bde or 6212850a0bde
	VPMULHRSW -7(CX), X15, K2, X11                     // 6272050a0b99f9ffffff or 6272850a0b99f9ffffff
	VPMULHRSW 15(DX)(BX*4), X15, K2, X11               // 6272050a0b9c9a0f000000 or 6272850a0b9c9a0f000000
	VPMULHRSW Y16, Y21, K3, Y24                        // 622255230bc0 or 6222d5230bc0
	VPMULHRSW 99(R15)(R15*4), Y21, K3, Y24             // 620255230b84bf63000000 or 6202d5230b84bf63000000
	VPMULHRSW 15(DX), Y21, K3, Y24                     // 626255230b820f000000 or 6262d5230b820f000000
	VPMULHRSW Z22, Z12, K3, Z16                        // 62a21d4b0bc6 or 62a29d4b0bc6
	VPMULHRSW Z11, Z12, K3, Z16                        // 62c21d4b0bc3 or 62c29d4b0bc3
	VPMULHRSW 15(DX)(BX*1), Z12, K3, Z16               // 62e21d4b0b841a0f000000 or 62e29d4b0b841a0f000000
	VPMULHRSW -7(CX)(DX*2), Z12, K3, Z16               // 62e21d4b0b8451f9ffffff or 62e29d4b0b8451f9ffffff
	VPMULHRSW Z22, Z27, K3, Z16                        // 62a225430bc6 or 62a2a5430bc6
	VPMULHRSW Z11, Z27, K3, Z16                        // 62c225430bc3 or 62c2a5430bc3
	VPMULHRSW 15(DX)(BX*1), Z27, K3, Z16               // 62e225430b841a0f000000 or 62e2a5430b841a0f000000
	VPMULHRSW -7(CX)(DX*2), Z27, K3, Z16               // 62e225430b8451f9ffffff or 62e2a5430b8451f9ffffff
	VPMULHRSW Z22, Z12, K3, Z13                        // 62321d4b0bee or 62329d4b0bee
	VPMULHRSW Z11, Z12, K3, Z13                        // 62521d4b0beb or 62529d4b0beb
	VPMULHRSW 15(DX)(BX*1), Z12, K3, Z13               // 62721d4b0bac1a0f000000 or 62729d4b0bac1a0f000000
	VPMULHRSW -7(CX)(DX*2), Z12, K3, Z13               // 62721d4b0bac51f9ffffff or 62729d4b0bac51f9ffffff
	VPMULHRSW Z22, Z27, K3, Z13                        // 623225430bee or 6232a5430bee
	VPMULHRSW Z11, Z27, K3, Z13                        // 625225430beb or 6252a5430beb
	VPMULHRSW 15(DX)(BX*1), Z27, K3, Z13               // 627225430bac1a0f000000 or 6272a5430bac1a0f000000
	VPMULHRSW -7(CX)(DX*2), Z27, K3, Z13               // 627225430bac51f9ffffff or 6272a5430bac51f9ffffff
	VPMULHUW X12, X6, K3, X13                          // 62514d0be4ec or 6251cd0be4ec
	VPMULHUW 99(R15)(R15*8), X6, K3, X13               // 62114d0be4acff63000000 or 6211cd0be4acff63000000
	VPMULHUW 7(AX)(CX*8), X6, K3, X13                  // 62714d0be4acc807000000 or 6271cd0be4acc807000000
	VPMULHUW Y9, Y13, K2, Y9                           // 6251152ae4c9 or 6251952ae4c9
	VPMULHUW (CX), Y13, K2, Y9                         // 6271152ae409 or 6271952ae409
	VPMULHUW 99(R15), Y13, K2, Y9                      // 6251152ae48f63000000 or 6251952ae48f63000000
	VPMULHUW Z12, Z25, K1, Z6                          // 62d13541e4f4 or 62d1b541e4f4
	VPMULHUW Z17, Z25, K1, Z6                          // 62b13541e4f1 or 62b1b541e4f1
	VPMULHUW -17(BP), Z25, K1, Z6                      // 62f13541e4b5efffffff or 62f1b541e4b5efffffff
	VPMULHUW -15(R14)(R15*8), Z25, K1, Z6              // 62913541e4b4fef1ffffff or 6291b541e4b4fef1ffffff
	VPMULHUW Z12, Z12, K1, Z6                          // 62d11d49e4f4 or 62d19d49e4f4
	VPMULHUW Z17, Z12, K1, Z6                          // 62b11d49e4f1 or 62b19d49e4f1
	VPMULHUW -17(BP), Z12, K1, Z6                      // 62f11d49e4b5efffffff or 62f19d49e4b5efffffff
	VPMULHUW -15(R14)(R15*8), Z12, K1, Z6              // 62911d49e4b4fef1ffffff or 62919d49e4b4fef1ffffff
	VPMULHUW Z12, Z25, K1, Z8                          // 62513541e4c4 or 6251b541e4c4
	VPMULHUW Z17, Z25, K1, Z8                          // 62313541e4c1 or 6231b541e4c1
	VPMULHUW -17(BP), Z25, K1, Z8                      // 62713541e485efffffff or 6271b541e485efffffff
	VPMULHUW -15(R14)(R15*8), Z25, K1, Z8              // 62113541e484fef1ffffff or 6211b541e484fef1ffffff
	VPMULHUW Z12, Z12, K1, Z8                          // 62511d49e4c4 or 62519d49e4c4
	VPMULHUW Z17, Z12, K1, Z8                          // 62311d49e4c1 or 62319d49e4c1
	VPMULHUW -17(BP), Z12, K1, Z8                      // 62711d49e485efffffff or 62719d49e485efffffff
	VPMULHUW -15(R14)(R15*8), Z12, K1, Z8              // 62111d49e484fef1ffffff or 62119d49e484fef1ffffff
	VPMULHW X8, X30, K2, X23                           // 62c10d02e5f8 or 62c18d02e5f8
	VPMULHW (AX), X30, K2, X23                         // 62e10d02e538 or 62e18d02e538
	VPMULHW 7(SI), X30, K2, X23                        // 62e10d02e5be07000000 or 62e18d02e5be07000000
	VPMULHW Y7, Y3, K1, Y6                             // 62f16529e5f7 or 62f1e529e5f7
	VPMULHW 99(R15)(R15*2), Y3, K1, Y6                 // 62916529e5b47f63000000 or 6291e529e5b47f63000000
	VPMULHW -7(DI), Y3, K1, Y6                         // 62f16529e5b7f9ffffff or 62f1e529e5b7f9ffffff
	VPMULHW Z3, Z6, K7, Z9                             // 62714d4fe5cb or 6271cd4fe5cb
	VPMULHW Z21, Z6, K7, Z9                            // 62314d4fe5cd or 6231cd4fe5cd
	VPMULHW 17(SP)(BP*2), Z6, K7, Z9                   // 62714d4fe58c6c11000000 or 6271cd4fe58c6c11000000
	VPMULHW -7(DI)(R8*4), Z6, K7, Z9                   // 62314d4fe58c87f9ffffff or 6231cd4fe58c87f9ffffff
	VPMULHW Z3, Z25, K7, Z9                            // 62713547e5cb or 6271b547e5cb
	VPMULHW Z21, Z25, K7, Z9                           // 62313547e5cd or 6231b547e5cd
	VPMULHW 17(SP)(BP*2), Z25, K7, Z9                  // 62713547e58c6c11000000 or 6271b547e58c6c11000000
	VPMULHW -7(DI)(R8*4), Z25, K7, Z9                  // 62313547e58c87f9ffffff or 6231b547e58c87f9ffffff
	VPMULHW Z3, Z6, K7, Z12                            // 62714d4fe5e3 or 6271cd4fe5e3
	VPMULHW Z21, Z6, K7, Z12                           // 62314d4fe5e5 or 6231cd4fe5e5
	VPMULHW 17(SP)(BP*2), Z6, K7, Z12                  // 62714d4fe5a46c11000000 or 6271cd4fe5a46c11000000
	VPMULHW -7(DI)(R8*4), Z6, K7, Z12                  // 62314d4fe5a487f9ffffff or 6231cd4fe5a487f9ffffff
	VPMULHW Z3, Z25, K7, Z12                           // 62713547e5e3 or 6271b547e5e3
	VPMULHW Z21, Z25, K7, Z12                          // 62313547e5e5 or 6231b547e5e5
	VPMULHW 17(SP)(BP*2), Z25, K7, Z12                 // 62713547e5a46c11000000 or 6271b547e5a46c11000000
	VPMULHW -7(DI)(R8*4), Z25, K7, Z12                 // 62313547e5a487f9ffffff or 6231b547e5a487f9ffffff
	VPMULLW X7, X16, K1, X31                           // 62617d01d5ff or 6261fd01d5ff
	VPMULLW (R8), X16, K1, X31                         // 62417d01d538 or 6241fd01d538
	VPMULLW 15(DX)(BX*2), X16, K1, X31                 // 62617d01d5bc5a0f000000 or 6261fd01d5bc5a0f000000
	VPMULLW Y18, Y31, K3, Y18                          // 62a10523d5d2 or 62a18523d5d2
	VPMULLW -17(BP), Y31, K3, Y18                      // 62e10523d595efffffff or 62e18523d595efffffff
	VPMULLW -15(R14)(R15*8), Y31, K3, Y18              // 62810523d594fef1ffffff or 62818523d594fef1ffffff
	VPMULLW Z11, Z12, K4, Z9                           // 62511d4cd5cb or 62519d4cd5cb
	VPMULLW Z5, Z12, K4, Z9                            // 62711d4cd5cd or 62719d4cd5cd
	VPMULLW -15(R14)(R15*1), Z12, K4, Z9               // 62111d4cd58c3ef1ffffff or 62119d4cd58c3ef1ffffff
	VPMULLW -15(BX), Z12, K4, Z9                       // 62711d4cd58bf1ffffff or 62719d4cd58bf1ffffff
	VPMULLW Z11, Z22, K4, Z9                           // 62514d44d5cb or 6251cd44d5cb
	VPMULLW Z5, Z22, K4, Z9                            // 62714d44d5cd or 6271cd44d5cd
	VPMULLW -15(R14)(R15*1), Z22, K4, Z9               // 62114d44d58c3ef1ffffff or 6211cd44d58c3ef1ffffff
	VPMULLW -15(BX), Z22, K4, Z9                       // 62714d44d58bf1ffffff or 6271cd44d58bf1ffffff
	VPMULLW Z11, Z12, K4, Z19                          // 62c11d4cd5db or 62c19d4cd5db
	VPMULLW Z5, Z12, K4, Z19                           // 62e11d4cd5dd or 62e19d4cd5dd
	VPMULLW -15(R14)(R15*1), Z12, K4, Z19              // 62811d4cd59c3ef1ffffff or 62819d4cd59c3ef1ffffff
	VPMULLW -15(BX), Z12, K4, Z19                      // 62e11d4cd59bf1ffffff or 62e19d4cd59bf1ffffff
	VPMULLW Z11, Z22, K4, Z19                          // 62c14d44d5db or 62c1cd44d5db
	VPMULLW Z5, Z22, K4, Z19                           // 62e14d44d5dd or 62e1cd44d5dd
	VPMULLW -15(R14)(R15*1), Z22, K4, Z19              // 62814d44d59c3ef1ffffff or 6281cd44d59c3ef1ffffff
	VPMULLW -15(BX), Z22, K4, Z19                      // 62e14d44d59bf1ffffff or 62e1cd44d59bf1ffffff
	VPSADBW X7, X3, X31                                // 62616508f6ff or 6261e508f6ff
	VPSADBW 17(SP)(BP*8), X3, X31                      // 62616508f6bcec11000000 or 6261e508f6bcec11000000
	VPSADBW 17(SP)(BP*4), X3, X31                      // 62616508f6bcac11000000 or 6261e508f6bcac11000000
	VPSADBW Y14, Y9, Y22                               // 62c13528f6f6 or 62c1b528f6f6
	VPSADBW 99(R15)(R15*8), Y9, Y22                    // 62813528f6b4ff63000000 or 6281b528f6b4ff63000000
	VPSADBW 7(AX)(CX*8), Y9, Y22                       // 62e13528f6b4c807000000 or 62e1b528f6b4c807000000
	VPSADBW Z7, Z26, Z30                               // 62612d40f6f7 or 6261ad40f6f7
	VPSADBW Z21, Z26, Z30                              // 62212d40f6f5 or 6221ad40f6f5
	VPSADBW (R8), Z26, Z30                             // 62412d40f630 or 6241ad40f630
	VPSADBW 15(DX)(BX*2), Z26, Z30                     // 62612d40f6b45a0f000000 or 6261ad40f6b45a0f000000
	VPSADBW Z7, Z22, Z30                               // 62614d40f6f7 or 6261cd40f6f7
	VPSADBW Z21, Z22, Z30                              // 62214d40f6f5 or 6221cd40f6f5
	VPSADBW (R8), Z22, Z30                             // 62414d40f630 or 6241cd40f630
	VPSADBW 15(DX)(BX*2), Z22, Z30                     // 62614d40f6b45a0f000000 or 6261cd40f6b45a0f000000
	VPSADBW Z7, Z26, Z5                                // 62f12d40f6ef or 62f1ad40f6ef
	VPSADBW Z21, Z26, Z5                               // 62b12d40f6ed or 62b1ad40f6ed
	VPSADBW (R8), Z26, Z5                              // 62d12d40f628 or 62d1ad40f628
	VPSADBW 15(DX)(BX*2), Z26, Z5                      // 62f12d40f6ac5a0f000000 or 62f1ad40f6ac5a0f000000
	VPSADBW Z7, Z22, Z5                                // 62f14d40f6ef or 62f1cd40f6ef
	VPSADBW Z21, Z22, Z5                               // 62b14d40f6ed or 62b1cd40f6ed
	VPSADBW (R8), Z22, Z5                              // 62d14d40f628 or 62d1cd40f628
	VPSADBW 15(DX)(BX*2), Z22, Z5                      // 62f14d40f6ac5a0f000000 or 62f1cd40f6ac5a0f000000
	VPSHUFB X13, X9, K5, X0                            // 62d2350d00c5 or 62d2b50d00c5
	VPSHUFB 15(R8)(R14*4), X9, K5, X0                  // 6292350d0084b00f000000 or 6292b50d0084b00f000000
	VPSHUFB -7(CX)(DX*4), X9, K5, X0                   // 62f2350d008491f9ffffff or 62f2b50d008491f9ffffff
	VPSHUFB Y2, Y16, K7, Y5                            // 62f27d2700ea or 62f2fd2700ea
	VPSHUFB 15(DX)(BX*1), Y16, K7, Y5                  // 62f27d2700ac1a0f000000 or 62f2fd2700ac1a0f000000
	VPSHUFB -7(CX)(DX*2), Y16, K7, Y5                  // 62f27d2700ac51f9ffffff or 62f2fd2700ac51f9ffffff
	VPSHUFB Z9, Z12, K7, Z25                           // 62421d4f00c9 or 62429d4f00c9
	VPSHUFB Z12, Z12, K7, Z25                          // 62421d4f00cc or 62429d4f00cc
	VPSHUFB 15(R8)(R14*8), Z12, K7, Z25                // 62021d4f008cf00f000000 or 62029d4f008cf00f000000
	VPSHUFB -15(R14)(R15*2), Z12, K7, Z25              // 62021d4f008c7ef1ffffff or 62029d4f008c7ef1ffffff
	VPSHUFB Z9, Z17, K7, Z25                           // 6242754700c9 or 6242f54700c9
	VPSHUFB Z12, Z17, K7, Z25                          // 6242754700cc or 6242f54700cc
	VPSHUFB 15(R8)(R14*8), Z17, K7, Z25                // 62027547008cf00f000000 or 6202f547008cf00f000000
	VPSHUFB -15(R14)(R15*2), Z17, K7, Z25              // 62027547008c7ef1ffffff or 6202f547008c7ef1ffffff
	VPSHUFB Z9, Z12, K7, Z12                           // 62521d4f00e1 or 62529d4f00e1
	VPSHUFB Z12, Z12, K7, Z12                          // 62521d4f00e4 or 62529d4f00e4
	VPSHUFB 15(R8)(R14*8), Z12, K7, Z12                // 62121d4f00a4f00f000000 or 62129d4f00a4f00f000000
	VPSHUFB -15(R14)(R15*2), Z12, K7, Z12              // 62121d4f00a47ef1ffffff or 62129d4f00a47ef1ffffff
	VPSHUFB Z9, Z17, K7, Z12                           // 6252754700e1 or 6252f54700e1
	VPSHUFB Z12, Z17, K7, Z12                          // 6252754700e4 or 6252f54700e4
	VPSHUFB 15(R8)(R14*8), Z17, K7, Z12                // 6212754700a4f00f000000 or 6212f54700a4f00f000000
	VPSHUFB -15(R14)(R15*2), Z17, K7, Z12              // 6212754700a47ef1ffffff or 6212f54700a47ef1ffffff
	VPSHUFHW $13, X11, K2, X31                         // 62417e0a70fb0d or 6241fe0a70fb0d
	VPSHUFHW $13, -17(BP)(SI*2), K2, X31               // 62617e0a70bc75efffffff0d or 6261fe0a70bc75efffffff0d
	VPSHUFHW $13, 7(AX)(CX*2), K2, X31                 // 62617e0a70bc48070000000d or 6261fe0a70bc48070000000d
	VPSHUFHW $65, Y11, K5, Y6                          // 62d17e2d70f341 or 62d1fe2d70f341
	VPSHUFHW $65, 15(R8), K5, Y6                       // 62d17e2d70b00f00000041 or 62d1fe2d70b00f00000041
	VPSHUFHW $65, (BP), K5, Y6                         // 62f17e2d70750041 or 62f1fe2d70750041
	VPSHUFHW $67, Z0, K3, Z7                           // 62f17e4b70f843 or 62f1fe4b70f843
	VPSHUFHW $67, Z6, K3, Z7                           // 62f17e4b70fe43 or 62f1fe4b70fe43
	VPSHUFHW $67, (SI), K3, Z7                         // 62f17e4b703e43 or 62f1fe4b703e43
	VPSHUFHW $67, 7(SI)(DI*2), K3, Z7                  // 62f17e4b70bc7e0700000043 or 62f1fe4b70bc7e0700000043
	VPSHUFHW $67, Z0, K3, Z9                           // 62717e4b70c843 or 6271fe4b70c843
	VPSHUFHW $67, Z6, K3, Z9                           // 62717e4b70ce43 or 6271fe4b70ce43
	VPSHUFHW $67, (SI), K3, Z9                         // 62717e4b700e43 or 6271fe4b700e43
	VPSHUFHW $67, 7(SI)(DI*2), K3, Z9                  // 62717e4b708c7e0700000043 or 6271fe4b708c7e0700000043
	VPSHUFLW $127, X5, K4, X22                         // 62e17f0c70f57f or 62e1ff0c70f57f
	VPSHUFLW $127, 15(R8)(R14*1), K4, X22              // 62817f0c70b4300f0000007f or 6281ff0c70b4300f0000007f
	VPSHUFLW $127, 15(R8)(R14*2), K4, X22              // 62817f0c70b4700f0000007f or 6281ff0c70b4700f0000007f
	VPSHUFLW $0, Y7, K2, Y19                           // 62e17f2a70df00 or 62e1ff2a70df00
	VPSHUFLW $0, 15(R8)(R14*8), K2, Y19                // 62817f2a709cf00f00000000 or 6281ff2a709cf00f00000000
	VPSHUFLW $0, -15(R14)(R15*2), K2, Y19              // 62817f2a709c7ef1ffffff00 or 6281ff2a709c7ef1ffffff00
	VPSHUFLW $97, Z3, K2, Z20                          // 62e17f4a70e361 or 62e1ff4a70e361
	VPSHUFLW $97, Z30, K2, Z20                         // 62817f4a70e661 or 6281ff4a70e661
	VPSHUFLW $97, 17(SP)(BP*8), K2, Z20                // 62e17f4a70a4ec1100000061 or 62e1ff4a70a4ec1100000061
	VPSHUFLW $97, 17(SP)(BP*4), K2, Z20                // 62e17f4a70a4ac1100000061 or 62e1ff4a70a4ac1100000061
	VPSHUFLW $97, Z3, K2, Z28                          // 62617f4a70e361 or 6261ff4a70e361
	VPSHUFLW $97, Z30, K2, Z28                         // 62017f4a70e661 or 6201ff4a70e661
	VPSHUFLW $97, 17(SP)(BP*8), K2, Z28                // 62617f4a70a4ec1100000061 or 6261ff4a70a4ec1100000061
	VPSHUFLW $97, 17(SP)(BP*4), K2, Z28                // 62617f4a70a4ac1100000061 or 6261ff4a70a4ac1100000061
	VPSLLDQ $64, X8, X18                               // 62d16d0073f840 or 62d1ed0073f840
	VPSLLDQ $64, -7(CX)(DX*1), X18                     // 62f16d0073bc11f9ffffff40 or 62f1ed0073bc11f9ffffff40
	VPSLLDQ $64, -15(R14)(R15*4), X18                  // 62916d0073bcbef1ffffff40 or 6291ed0073bcbef1ffffff40
	VPSLLDQ $27, Y12, Y20                              // 62d15d2073fc1b or 62d1dd2073fc1b
	VPSLLDQ $27, 7(AX)(CX*4), Y20                      // 62f15d2073bc88070000001b or 62f1dd2073bc88070000001b
	VPSLLDQ $27, 7(AX)(CX*1), Y20                      // 62f15d2073bc08070000001b or 62f1dd2073bc08070000001b
	VPSLLDQ $47, Z7, Z2                                // 62f16d4873ff2f or 62f1ed4873ff2f
	VPSLLDQ $47, Z13, Z2                               // 62d16d4873fd2f or 62d1ed4873fd2f
	VPSLLDQ $47, 17(SP), Z2                            // 62f16d4873bc24110000002f or 62f1ed4873bc24110000002f
	VPSLLDQ $47, -17(BP)(SI*4), Z2                     // 62f16d4873bcb5efffffff2f or 62f1ed4873bcb5efffffff2f
	VPSLLDQ $47, Z7, Z21                               // 62f1554073ff2f or 62f1d54073ff2f
	VPSLLDQ $47, Z13, Z21                              // 62d1554073fd2f or 62d1d54073fd2f
	VPSLLDQ $47, 17(SP), Z21                           // 62f1554073bc24110000002f or 62f1d54073bc24110000002f
	VPSLLDQ $47, -17(BP)(SI*4), Z21                    // 62f1554073bcb5efffffff2f or 62f1d54073bcb5efffffff2f
	VPSLLVW X11, X1, K7, X22                           // 62c2f50f12f3
	VPSLLVW 7(AX)(CX*4), X1, K7, X22                   // 62e2f50f12b48807000000
	VPSLLVW 7(AX)(CX*1), X1, K7, X22                   // 62e2f50f12b40807000000
	VPSLLVW Y9, Y7, K7, Y17                            // 62c2c52f12c9
	VPSLLVW 17(SP), Y7, K7, Y17                        // 62e2c52f128c2411000000
	VPSLLVW -17(BP)(SI*4), Y7, K7, Y17                 // 62e2c52f128cb5efffffff
	VPSLLVW Z3, Z14, K6, Z28                           // 62628d4e12e3
	VPSLLVW Z12, Z14, K6, Z28                          // 62428d4e12e4
	VPSLLVW 7(SI)(DI*8), Z14, K6, Z28                  // 62628d4e12a4fe07000000
	VPSLLVW -15(R14), Z14, K6, Z28                     // 62428d4e12a6f1ffffff
	VPSLLVW Z3, Z28, K6, Z28                           // 62629d4612e3
	VPSLLVW Z12, Z28, K6, Z28                          // 62429d4612e4
	VPSLLVW 7(SI)(DI*8), Z28, K6, Z28                  // 62629d4612a4fe07000000
	VPSLLVW -15(R14), Z28, K6, Z28                     // 62429d4612a6f1ffffff
	VPSLLVW Z3, Z14, K6, Z13                           // 62728d4e12eb
	VPSLLVW Z12, Z14, K6, Z13                          // 62528d4e12ec
	VPSLLVW 7(SI)(DI*8), Z14, K6, Z13                  // 62728d4e12acfe07000000
	VPSLLVW -15(R14), Z14, K6, Z13                     // 62528d4e12aef1ffffff
	VPSLLVW Z3, Z28, K6, Z13                           // 62729d4612eb
	VPSLLVW Z12, Z28, K6, Z13                          // 62529d4612ec
	VPSLLVW 7(SI)(DI*8), Z28, K6, Z13                  // 62729d4612acfe07000000
	VPSLLVW -15(R14), Z28, K6, Z13                     // 62529d4612aef1ffffff
	VPSLLW $121, X7, K3, X6                            // 62f14d0b71f779 or 62f1cd0b71f779
	VPSLLW $121, (SI), K3, X6                          // 62f14d0b713679 or 62f1cd0b713679
	VPSLLW $121, 7(SI)(DI*2), K3, X6                   // 62f14d0b71b47e0700000079 or 62f1cd0b71b47e0700000079
	VPSLLW $13, Y8, K7, Y31                            // 62d1052771f00d or 62d1852771f00d
	VPSLLW $13, 7(AX), K7, Y31                         // 62f1052771b0070000000d or 62f1852771b0070000000d
	VPSLLW $13, (DI), K7, Y31                          // 62f1052771370d or 62f1852771370d
	VPSLLW $65, Z19, K4, Z15                           // 62b1054c71f341 or 62b1854c71f341
	VPSLLW $65, Z15, K4, Z15                           // 62d1054c71f741 or 62d1854c71f741
	VPSLLW $65, 7(SI)(DI*1), K4, Z15                   // 62f1054c71b43e0700000041 or 62f1854c71b43e0700000041
	VPSLLW $65, 15(DX)(BX*8), K4, Z15                  // 62f1054c71b4da0f00000041 or 62f1854c71b4da0f00000041
	VPSLLW $65, Z19, K4, Z30                           // 62b10d4471f341 or 62b18d4471f341
	VPSLLW $65, Z15, K4, Z30                           // 62d10d4471f741 or 62d18d4471f741
	VPSLLW $65, 7(SI)(DI*1), K4, Z30                   // 62f10d4471b43e0700000041 or 62f18d4471b43e0700000041
	VPSLLW $65, 15(DX)(BX*8), K4, Z30                  // 62f10d4471b4da0f00000041 or 62f18d4471b4da0f00000041
	VPSLLW X3, X31, K4, X8                             // 62710504f1c3 or 62718504f1c3
	VPSLLW 17(SP)(BP*8), X31, K4, X8                   // 62710504f184ec11000000 or 62718504f184ec11000000
	VPSLLW 17(SP)(BP*4), X31, K4, X8                   // 62710504f184ac11000000 or 62718504f184ac11000000
	VPSLLW X28, Y28, K7, Y1                            // 62911d27f1cc or 62919d27f1cc
	VPSLLW 7(SI)(DI*4), Y28, K7, Y1                    // 62f11d27f18cbe07000000 or 62f19d27f18cbe07000000
	VPSLLW -7(DI)(R8*2), Y28, K7, Y1                   // 62b11d27f18c47f9ffffff or 62b19d27f18c47f9ffffff
	VPSLLW X20, Z3, K2, Z5                             // 62b1654af1ec or 62b1e54af1ec
	VPSLLW 17(SP), Z3, K2, Z5                          // 62f1654af1ac2411000000 or 62f1e54af1ac2411000000
	VPSLLW -17(BP)(SI*4), Z3, K2, Z5                   // 62f1654af1acb5efffffff or 62f1e54af1acb5efffffff
	VPSLLW X20, Z5, K2, Z5                             // 62b1554af1ec or 62b1d54af1ec
	VPSLLW 17(SP), Z5, K2, Z5                          // 62f1554af1ac2411000000 or 62f1d54af1ac2411000000
	VPSLLW -17(BP)(SI*4), Z5, K2, Z5                   // 62f1554af1acb5efffffff or 62f1d54af1acb5efffffff
	VPSLLW X20, Z3, K2, Z1                             // 62b1654af1cc or 62b1e54af1cc
	VPSLLW 17(SP), Z3, K2, Z1                          // 62f1654af18c2411000000 or 62f1e54af18c2411000000
	VPSLLW -17(BP)(SI*4), Z3, K2, Z1                   // 62f1654af18cb5efffffff or 62f1e54af18cb5efffffff
	VPSLLW X20, Z5, K2, Z1                             // 62b1554af1cc or 62b1d54af1cc
	VPSLLW 17(SP), Z5, K2, Z1                          // 62f1554af18c2411000000 or 62f1d54af18c2411000000
	VPSLLW -17(BP)(SI*4), Z5, K2, Z1                   // 62f1554af18cb5efffffff or 62f1d54af18cb5efffffff
	VPSRAVW X8, X28, K4, X16                           // 62c29d0411c0
	VPSRAVW 15(R8)(R14*4), X28, K4, X16                // 62829d041184b00f000000
	VPSRAVW -7(CX)(DX*4), X28, K4, X16                 // 62e29d04118491f9ffffff
	VPSRAVW Y7, Y26, K1, Y30                           // 6262ad2111f7
	VPSRAVW -7(DI)(R8*1), Y26, K1, Y30                 // 6222ad2111b407f9ffffff
	VPSRAVW (SP), Y26, K1, Y30                         // 6262ad21113424
	VPSRAVW Z21, Z31, K3, Z17                          // 62a2854311cd
	VPSRAVW Z9, Z31, K3, Z17                           // 62c2854311c9
	VPSRAVW (BX), Z31, K3, Z17                         // 62e28543110b
	VPSRAVW -17(BP)(SI*1), Z31, K3, Z17                // 62e28543118c35efffffff
	VPSRAVW Z21, Z0, K3, Z17                           // 62a2fd4b11cd
	VPSRAVW Z9, Z0, K3, Z17                            // 62c2fd4b11c9
	VPSRAVW (BX), Z0, K3, Z17                          // 62e2fd4b110b
	VPSRAVW -17(BP)(SI*1), Z0, K3, Z17                 // 62e2fd4b118c35efffffff
	VPSRAVW Z21, Z31, K3, Z23                          // 62a2854311fd
	VPSRAVW Z9, Z31, K3, Z23                           // 62c2854311f9
	VPSRAVW (BX), Z31, K3, Z23                         // 62e28543113b
	VPSRAVW -17(BP)(SI*1), Z31, K3, Z23                // 62e2854311bc35efffffff
	VPSRAVW Z21, Z0, K3, Z23                           // 62a2fd4b11fd
	VPSRAVW Z9, Z0, K3, Z23                            // 62c2fd4b11f9
	VPSRAVW (BX), Z0, K3, Z23                          // 62e2fd4b113b
	VPSRAVW -17(BP)(SI*1), Z0, K3, Z23                 // 62e2fd4b11bc35efffffff
	VPSRAW $79, X11, K4, X15                           // 62d1050c71e34f or 62d1850c71e34f
	VPSRAW $79, (R8), K4, X15                          // 62d1050c71204f or 62d1850c71204f
	VPSRAW $79, 15(DX)(BX*2), K4, X15                  // 62f1050c71a45a0f0000004f or 62f1850c71a45a0f0000004f
	VPSRAW $64, Y1, K5, Y16                            // 62f17d2571e140 or 62f1fd2571e140
	VPSRAW $64, -7(CX), K5, Y16                        // 62f17d2571a1f9ffffff40 or 62f1fd2571a1f9ffffff40
	VPSRAW $64, 15(DX)(BX*4), K5, Y16                  // 62f17d2571a49a0f00000040 or 62f1fd2571a49a0f00000040
	VPSRAW $27, Z1, K7, Z6                             // 62f14d4f71e11b or 62f1cd4f71e11b
	VPSRAW $27, Z9, K7, Z6                             // 62d14d4f71e11b or 62d1cd4f71e11b
	VPSRAW $27, 15(R8)(R14*4), K7, Z6                  // 62914d4f71a4b00f0000001b or 6291cd4f71a4b00f0000001b
	VPSRAW $27, -7(CX)(DX*4), K7, Z6                   // 62f14d4f71a491f9ffffff1b or 62f1cd4f71a491f9ffffff1b
	VPSRAW $27, Z1, K7, Z9                             // 62f1354f71e11b or 62f1b54f71e11b
	VPSRAW $27, Z9, K7, Z9                             // 62d1354f71e11b or 62d1b54f71e11b
	VPSRAW $27, 15(R8)(R14*4), K7, Z9                  // 6291354f71a4b00f0000001b or 6291b54f71a4b00f0000001b
	VPSRAW $27, -7(CX)(DX*4), K7, Z9                   // 62f1354f71a491f9ffffff1b or 62f1b54f71a491f9ffffff1b
	VPSRAW X13, X19, K7, X1                            // 62d16507e1cd or 62d1e507e1cd
	VPSRAW 17(SP)(BP*1), X19, K7, X1                   // 62f16507e18c2c11000000 or 62f1e507e18c2c11000000
	VPSRAW -7(CX)(DX*8), X19, K7, X1                   // 62f16507e18cd1f9ffffff or 62f1e507e18cd1f9ffffff
	VPSRAW X2, Y31, K6, Y30                            // 62610526e1f2 or 62618526e1f2
	VPSRAW -17(BP)(SI*2), Y31, K6, Y30                 // 62610526e1b475efffffff or 62618526e1b475efffffff
	VPSRAW 7(AX)(CX*2), Y31, K6, Y30                   // 62610526e1b44807000000 or 62618526e1b44807000000
	VPSRAW X14, Z30, K3, Z20                           // 62c10d43e1e6 or 62c18d43e1e6
	VPSRAW 15(R8)(R14*1), Z30, K3, Z20                 // 62810d43e1a4300f000000 or 62818d43e1a4300f000000
	VPSRAW 15(R8)(R14*2), Z30, K3, Z20                 // 62810d43e1a4700f000000 or 62818d43e1a4700f000000
	VPSRAW X14, Z5, K3, Z20                            // 62c1554be1e6 or 62c1d54be1e6
	VPSRAW 15(R8)(R14*1), Z5, K3, Z20                  // 6281554be1a4300f000000 or 6281d54be1a4300f000000
	VPSRAW 15(R8)(R14*2), Z5, K3, Z20                  // 6281554be1a4700f000000 or 6281d54be1a4700f000000
	VPSRAW X14, Z30, K3, Z9                            // 62510d43e1ce or 62518d43e1ce
	VPSRAW 15(R8)(R14*1), Z30, K3, Z9                  // 62110d43e18c300f000000 or 62118d43e18c300f000000
	VPSRAW 15(R8)(R14*2), Z30, K3, Z9                  // 62110d43e18c700f000000 or 62118d43e18c700f000000
	VPSRAW X14, Z5, K3, Z9                             // 6251554be1ce or 6251d54be1ce
	VPSRAW 15(R8)(R14*1), Z5, K3, Z9                   // 6211554be18c300f000000 or 6211d54be18c300f000000
	VPSRAW 15(R8)(R14*2), Z5, K3, Z9                   // 6211554be18c700f000000 or 6211d54be18c700f000000
	VPSRLDQ $94, -7(CX)(DX*1), X9                      // 62f13508739c11f9ffffff5e or 62f1b508739c11f9ffffff5e
	VPSRLDQ $94, -15(R14)(R15*4), X9                   // 62913508739cbef1ffffff5e or 6291b508739cbef1ffffff5e
	VPSRLDQ $121, Y28, Y0                              // 62917d2873dc79 or 6291fd2873dc79
	VPSRLDQ $121, (AX), Y0                             // 62f17d28731879 or 62f1fd28731879
	VPSRLDQ $121, 7(SI), Y0                            // 62f17d28739e0700000079 or 62f1fd28739e0700000079
	VPSRLDQ $13, Z21, Z12                              // 62b11d4873dd0d or 62b19d4873dd0d
	VPSRLDQ $13, Z9, Z12                               // 62d11d4873d90d or 62d19d4873d90d
	VPSRLDQ $13, 17(SP)(BP*1), Z12                     // 62f11d48739c2c110000000d or 62f19d48739c2c110000000d
	VPSRLDQ $13, -7(CX)(DX*8), Z12                     // 62f11d48739cd1f9ffffff0d or 62f19d48739cd1f9ffffff0d
	VPSRLDQ $13, Z21, Z13                              // 62b1154873dd0d or 62b1954873dd0d
	VPSRLDQ $13, Z9, Z13                               // 62d1154873d90d or 62d1954873d90d
	VPSRLDQ $13, 17(SP)(BP*1), Z13                     // 62f11548739c2c110000000d or 62f19548739c2c110000000d
	VPSRLDQ $13, -7(CX)(DX*8), Z13                     // 62f11548739cd1f9ffffff0d or 62f19548739cd1f9ffffff0d
	VPSRLVW X30, X23, K1, X12                          // 6212c50110e6
	VPSRLVW 7(AX)(CX*4), X23, K1, X12                  // 6272c50110a48807000000
	VPSRLVW 7(AX)(CX*1), X23, K1, X12                  // 6272c50110a40807000000
	VPSRLVW Y3, Y22, K1, Y12                           // 6272cd2110e3
	VPSRLVW 17(SP)(BP*1), Y22, K1, Y12                 // 6272cd2110a42c11000000
	VPSRLVW -7(CX)(DX*8), Y22, K1, Y12                 // 6272cd2110a4d1f9ffffff
	VPSRLVW Z14, Z15, K1, Z0                           // 62d2854910c6
	VPSRLVW Z27, Z15, K1, Z0                           // 6292854910c3
	VPSRLVW 99(R15)(R15*4), Z15, K1, Z0                // 629285491084bf63000000
	VPSRLVW 15(DX), Z15, K1, Z0                        // 62f2854910820f000000
	VPSRLVW Z14, Z12, K1, Z0                           // 62d29d4910c6
	VPSRLVW Z27, Z12, K1, Z0                           // 62929d4910c3
	VPSRLVW 99(R15)(R15*4), Z12, K1, Z0                // 62929d491084bf63000000
	VPSRLVW 15(DX), Z12, K1, Z0                        // 62f29d4910820f000000
	VPSRLVW Z14, Z15, K1, Z8                           // 6252854910c6
	VPSRLVW Z27, Z15, K1, Z8                           // 6212854910c3
	VPSRLVW 99(R15)(R15*4), Z15, K1, Z8                // 621285491084bf63000000
	VPSRLVW 15(DX), Z15, K1, Z8                        // 6272854910820f000000
	VPSRLVW Z14, Z12, K1, Z8                           // 62529d4910c6
	VPSRLVW Z27, Z12, K1, Z8                           // 62129d4910c3
	VPSRLVW 99(R15)(R15*4), Z12, K1, Z8                // 62129d491084bf63000000
	VPSRLVW 15(DX), Z12, K1, Z8                        // 62729d4910820f000000
	VPSRLW $0, X20, K7, X8                             // 62b13d0f71d400 or 62b1bd0f71d400
	VPSRLW $0, (SI), K7, X8                            // 62f13d0f711600 or 62f1bd0f711600
	VPSRLW $0, 7(SI)(DI*2), K7, X8                     // 62f13d0f71947e0700000000 or 62f1bd0f71947e0700000000
	VPSRLW $97, Y1, K2, Y15                            // 62f1052a71d161 or 62f1852a71d161
	VPSRLW $97, -17(BP)(SI*2), K2, Y15                 // 62f1052a719475efffffff61 or 62f1852a719475efffffff61
	VPSRLW $97, 7(AX)(CX*2), K2, Y15                   // 62f1052a7194480700000061 or 62f1852a7194480700000061
	VPSRLW $81, Z13, K4, Z11                           // 62d1254c71d551 or 62d1a54c71d551
	VPSRLW $81, Z14, K4, Z11                           // 62d1254c71d651 or 62d1a54c71d651
	VPSRLW $81, (CX), K4, Z11                          // 62f1254c711151 or 62f1a54c711151
	VPSRLW $81, 99(R15), K4, Z11                       // 62d1254c71976300000051 or 62d1a54c71976300000051
	VPSRLW $81, Z13, K4, Z5                            // 62d1554c71d551 or 62d1d54c71d551
	VPSRLW $81, Z14, K4, Z5                            // 62d1554c71d651 or 62d1d54c71d651
	VPSRLW $81, (CX), K4, Z5                           // 62f1554c711151 or 62f1d54c711151
	VPSRLW $81, 99(R15), K4, Z5                        // 62d1554c71976300000051 or 62d1d54c71976300000051
	VPSRLW X26, X9, K1, X2                             // 62913509d1d2 or 6291b509d1d2
	VPSRLW 17(SP)(BP*8), X9, K1, X2                    // 62f13509d194ec11000000 or 62f1b509d194ec11000000
	VPSRLW 17(SP)(BP*4), X9, K1, X2                    // 62f13509d194ac11000000 or 62f1b509d194ac11000000
	VPSRLW X19, Y19, K3, Y27                           // 62216523d1db or 6221e523d1db
	VPSRLW 7(SI)(DI*4), Y19, K3, Y27                   // 62616523d19cbe07000000 or 6261e523d19cbe07000000
	VPSRLW -7(DI)(R8*2), Y19, K3, Y27                  // 62216523d19c47f9ffffff or 6221e523d19c47f9ffffff
	VPSRLW X0, Z2, K4, Z5                              // 62f16d4cd1e8 or 62f1ed4cd1e8
	VPSRLW 17(SP), Z2, K4, Z5                          // 62f16d4cd1ac2411000000 or 62f1ed4cd1ac2411000000
	VPSRLW -17(BP)(SI*4), Z2, K4, Z5                   // 62f16d4cd1acb5efffffff or 62f1ed4cd1acb5efffffff
	VPSRLW X0, Z2, K4, Z23                             // 62e16d4cd1f8 or 62e1ed4cd1f8
	VPSRLW 17(SP), Z2, K4, Z23                         // 62e16d4cd1bc2411000000 or 62e1ed4cd1bc2411000000
	VPSRLW -17(BP)(SI*4), Z2, K4, Z23                  // 62e16d4cd1bcb5efffffff or 62e1ed4cd1bcb5efffffff
	VPSUBB X7, X16, K5, X31                            // 62617d05f8ff or 6261fd05f8ff
	VPSUBB 7(AX), X16, K5, X31                         // 62617d05f8b807000000 or 6261fd05f8b807000000
	VPSUBB (DI), X16, K5, X31                          // 62617d05f83f or 6261fd05f83f
	VPSUBB Y13, Y17, K7, Y5                            // 62d17527f8ed or 62d1f527f8ed
	VPSUBB 15(R8)(R14*1), Y17, K7, Y5                  // 62917527f8ac300f000000 or 6291f527f8ac300f000000
	VPSUBB 15(R8)(R14*2), Y17, K7, Y5                  // 62917527f8ac700f000000 or 6291f527f8ac700f000000
	VPSUBB Z28, Z26, K7, Z6                            // 62912d47f8f4 or 6291ad47f8f4
	VPSUBB Z6, Z26, K7, Z6                             // 62f12d47f8f6 or 62f1ad47f8f6
	VPSUBB 99(R15)(R15*2), Z26, K7, Z6                 // 62912d47f8b47f63000000 or 6291ad47f8b47f63000000
	VPSUBB -7(DI), Z26, K7, Z6                         // 62f12d47f8b7f9ffffff or 62f1ad47f8b7f9ffffff
	VPSUBB Z28, Z14, K7, Z6                            // 62910d4ff8f4 or 62918d4ff8f4
	VPSUBB Z6, Z14, K7, Z6                             // 62f10d4ff8f6 or 62f18d4ff8f6
	VPSUBB 99(R15)(R15*2), Z14, K7, Z6                 // 62910d4ff8b47f63000000 or 62918d4ff8b47f63000000
	VPSUBB -7(DI), Z14, K7, Z6                         // 62f10d4ff8b7f9ffffff or 62f18d4ff8b7f9ffffff
	VPSUBB Z28, Z26, K7, Z14                           // 62112d47f8f4 or 6211ad47f8f4
	VPSUBB Z6, Z26, K7, Z14                            // 62712d47f8f6 or 6271ad47f8f6
	VPSUBB 99(R15)(R15*2), Z26, K7, Z14                // 62112d47f8b47f63000000 or 6211ad47f8b47f63000000
	VPSUBB -7(DI), Z26, K7, Z14                        // 62712d47f8b7f9ffffff or 6271ad47f8b7f9ffffff
	VPSUBB Z28, Z14, K7, Z14                           // 62110d4ff8f4 or 62118d4ff8f4
	VPSUBB Z6, Z14, K7, Z14                            // 62710d4ff8f6 or 62718d4ff8f6
	VPSUBB 99(R15)(R15*2), Z14, K7, Z14                // 62110d4ff8b47f63000000 or 62118d4ff8b47f63000000
	VPSUBB -7(DI), Z14, K7, Z14                        // 62710d4ff8b7f9ffffff or 62718d4ff8b7f9ffffff
	VPSUBSB X28, X0, K2, X21                           // 62817d0ae8ec or 6281fd0ae8ec
	VPSUBSB 7(SI)(DI*8), X0, K2, X21                   // 62e17d0ae8acfe07000000 or 62e1fd0ae8acfe07000000
	VPSUBSB -15(R14), X0, K2, X21                      // 62c17d0ae8aef1ffffff or 62c1fd0ae8aef1ffffff
	VPSUBSB Y24, Y11, K5, Y8                           // 6211252de8c0 or 6211a52de8c0
	VPSUBSB (CX), Y11, K5, Y8                          // 6271252de801 or 6271a52de801
	VPSUBSB 99(R15), Y11, K5, Y8                       // 6251252de88763000000 or 6251a52de88763000000
	VPSUBSB Z23, Z23, K3, Z27                          // 62214543e8df or 6221c543e8df
	VPSUBSB Z6, Z23, K3, Z27                           // 62614543e8de or 6261c543e8de
	VPSUBSB -17(BP), Z23, K3, Z27                      // 62614543e89defffffff or 6261c543e89defffffff
	VPSUBSB -15(R14)(R15*8), Z23, K3, Z27              // 62014543e89cfef1ffffff or 6201c543e89cfef1ffffff
	VPSUBSB Z23, Z5, K3, Z27                           // 6221554be8df or 6221d54be8df
	VPSUBSB Z6, Z5, K3, Z27                            // 6261554be8de or 6261d54be8de
	VPSUBSB -17(BP), Z5, K3, Z27                       // 6261554be89defffffff or 6261d54be89defffffff
	VPSUBSB -15(R14)(R15*8), Z5, K3, Z27               // 6201554be89cfef1ffffff or 6201d54be89cfef1ffffff
	VPSUBSB Z23, Z23, K3, Z15                          // 62314543e8ff or 6231c543e8ff
	VPSUBSB Z6, Z23, K3, Z15                           // 62714543e8fe or 6271c543e8fe
	VPSUBSB -17(BP), Z23, K3, Z15                      // 62714543e8bdefffffff or 6271c543e8bdefffffff
	VPSUBSB -15(R14)(R15*8), Z23, K3, Z15              // 62114543e8bcfef1ffffff or 6211c543e8bcfef1ffffff
	VPSUBSB Z23, Z5, K3, Z15                           // 6231554be8ff or 6231d54be8ff
	VPSUBSB Z6, Z5, K3, Z15                            // 6271554be8fe or 6271d54be8fe
	VPSUBSB -17(BP), Z5, K3, Z15                       // 6271554be8bdefffffff or 6271d54be8bdefffffff
	VPSUBSB -15(R14)(R15*8), Z5, K3, Z15               // 6211554be8bcfef1ffffff or 6211d54be8bcfef1ffffff
	VPSUBSW X19, X7, K4, X22                           // 62a1450ce9f3 or 62a1c50ce9f3
	VPSUBSW 7(SI)(DI*1), X7, K4, X22                   // 62e1450ce9b43e07000000 or 62e1c50ce9b43e07000000
	VPSUBSW 15(DX)(BX*8), X7, K4, X22                  // 62e1450ce9b4da0f000000 or 62e1c50ce9b4da0f000000
	VPSUBSW Y21, Y24, K2, Y5                           // 62b13d22e9ed or 62b1bd22e9ed
	VPSUBSW 99(R15)(R15*2), Y24, K2, Y5                // 62913d22e9ac7f63000000 or 6291bd22e9ac7f63000000
	VPSUBSW -7(DI), Y24, K2, Y5                        // 62f13d22e9aff9ffffff or 62f1bd22e9aff9ffffff
	VPSUBSW Z16, Z21, K2, Z8                           // 62315542e9c0 or 6231d542e9c0
	VPSUBSW Z13, Z21, K2, Z8                           // 62515542e9c5 or 6251d542e9c5
	VPSUBSW 17(SP)(BP*2), Z21, K2, Z8                  // 62715542e9846c11000000 or 6271d542e9846c11000000
	VPSUBSW -7(DI)(R8*4), Z21, K2, Z8                  // 62315542e98487f9ffffff or 6231d542e98487f9ffffff
	VPSUBSW Z16, Z5, K2, Z8                            // 6231554ae9c0 or 6231d54ae9c0
	VPSUBSW Z13, Z5, K2, Z8                            // 6251554ae9c5 or 6251d54ae9c5
	VPSUBSW 17(SP)(BP*2), Z5, K2, Z8                   // 6271554ae9846c11000000 or 6271d54ae9846c11000000
	VPSUBSW -7(DI)(R8*4), Z5, K2, Z8                   // 6231554ae98487f9ffffff or 6231d54ae98487f9ffffff
	VPSUBSW Z16, Z21, K2, Z28                          // 62215542e9e0 or 6221d542e9e0
	VPSUBSW Z13, Z21, K2, Z28                          // 62415542e9e5 or 6241d542e9e5
	VPSUBSW 17(SP)(BP*2), Z21, K2, Z28                 // 62615542e9a46c11000000 or 6261d542e9a46c11000000
	VPSUBSW -7(DI)(R8*4), Z21, K2, Z28                 // 62215542e9a487f9ffffff or 6221d542e9a487f9ffffff
	VPSUBSW Z16, Z5, K2, Z28                           // 6221554ae9e0 or 6221d54ae9e0
	VPSUBSW Z13, Z5, K2, Z28                           // 6241554ae9e5 or 6241d54ae9e5
	VPSUBSW 17(SP)(BP*2), Z5, K2, Z28                  // 6261554ae9a46c11000000 or 6261d54ae9a46c11000000
	VPSUBSW -7(DI)(R8*4), Z5, K2, Z28                  // 6221554ae9a487f9ffffff or 6221d54ae9a487f9ffffff
	VPSUBUSB X31, X16, K3, X7                          // 62917d03d8ff or 6291fd03d8ff
	VPSUBUSB -7(DI)(R8*1), X16, K3, X7                 // 62b17d03d8bc07f9ffffff or 62b1fd03d8bc07f9ffffff
	VPSUBUSB (SP), X16, K3, X7                         // 62f17d03d83c24 or 62f1fd03d83c24
	VPSUBUSB Y13, Y9, K3, Y16                          // 62c1352bd8c5 or 62c1b52bd8c5
	VPSUBUSB -7(CX)(DX*1), Y9, K3, Y16                 // 62e1352bd88411f9ffffff or 62e1b52bd88411f9ffffff
	VPSUBUSB -15(R14)(R15*4), Y9, K3, Y16              // 6281352bd884bef1ffffff or 6281b52bd884bef1ffffff
	VPSUBUSB Z6, Z22, K3, Z12                          // 62714d43d8e6 or 6271cd43d8e6
	VPSUBUSB Z8, Z22, K3, Z12                          // 62514d43d8e0 or 6251cd43d8e0
	VPSUBUSB 15(R8), Z22, K3, Z12                      // 62514d43d8a00f000000 or 6251cd43d8a00f000000
	VPSUBUSB (BP), Z22, K3, Z12                        // 62714d43d86500 or 6271cd43d86500
	VPSUBUSB Z6, Z11, K3, Z12                          // 6271254bd8e6 or 6271a54bd8e6
	VPSUBUSB Z8, Z11, K3, Z12                          // 6251254bd8e0 or 6251a54bd8e0
	VPSUBUSB 15(R8), Z11, K3, Z12                      // 6251254bd8a00f000000 or 6251a54bd8a00f000000
	VPSUBUSB (BP), Z11, K3, Z12                        // 6271254bd86500 or 6271a54bd86500
	VPSUBUSB Z6, Z22, K3, Z27                          // 62614d43d8de or 6261cd43d8de
	VPSUBUSB Z8, Z22, K3, Z27                          // 62414d43d8d8 or 6241cd43d8d8
	VPSUBUSB 15(R8), Z22, K3, Z27                      // 62414d43d8980f000000 or 6241cd43d8980f000000
	VPSUBUSB (BP), Z22, K3, Z27                        // 62614d43d85d00 or 6261cd43d85d00
	VPSUBUSB Z6, Z11, K3, Z27                          // 6261254bd8de or 6261a54bd8de
	VPSUBUSB Z8, Z11, K3, Z27                          // 6241254bd8d8 or 6241a54bd8d8
	VPSUBUSB 15(R8), Z11, K3, Z27                      // 6241254bd8980f000000 or 6241a54bd8980f000000
	VPSUBUSB (BP), Z11, K3, Z27                        // 6261254bd85d00 or 6261a54bd85d00
	VPSUBUSW X9, X7, K2, X1                            // 62d1450ad9c9 or 62d1c50ad9c9
	VPSUBUSW -7(CX), X7, K2, X1                        // 62f1450ad989f9ffffff or 62f1c50ad989f9ffffff
	VPSUBUSW 15(DX)(BX*4), X7, K2, X1                  // 62f1450ad98c9a0f000000 or 62f1c50ad98c9a0f000000
	VPSUBUSW Y3, Y6, K1, Y9                            // 62714d29d9cb or 6271cd29d9cb
	VPSUBUSW 15(DX)(BX*1), Y6, K1, Y9                  // 62714d29d98c1a0f000000 or 6271cd29d98c1a0f000000
	VPSUBUSW -7(CX)(DX*2), Y6, K1, Y9                  // 62714d29d98c51f9ffffff or 6271cd29d98c51f9ffffff
	VPSUBUSW Z9, Z12, K2, Z25                          // 62411d4ad9c9 or 62419d4ad9c9
	VPSUBUSW Z12, Z12, K2, Z25                         // 62411d4ad9cc or 62419d4ad9cc
	VPSUBUSW 15(R8)(R14*8), Z12, K2, Z25               // 62011d4ad98cf00f000000 or 62019d4ad98cf00f000000
	VPSUBUSW -15(R14)(R15*2), Z12, K2, Z25             // 62011d4ad98c7ef1ffffff or 62019d4ad98c7ef1ffffff
	VPSUBUSW Z9, Z17, K2, Z25                          // 62417542d9c9 or 6241f542d9c9
	VPSUBUSW Z12, Z17, K2, Z25                         // 62417542d9cc or 6241f542d9cc
	VPSUBUSW 15(R8)(R14*8), Z17, K2, Z25               // 62017542d98cf00f000000 or 6201f542d98cf00f000000
	VPSUBUSW -15(R14)(R15*2), Z17, K2, Z25             // 62017542d98c7ef1ffffff or 6201f542d98c7ef1ffffff
	VPSUBUSW Z9, Z12, K2, Z12                          // 62511d4ad9e1 or 62519d4ad9e1
	VPSUBUSW Z12, Z12, K2, Z12                         // 62511d4ad9e4 or 62519d4ad9e4
	VPSUBUSW 15(R8)(R14*8), Z12, K2, Z12               // 62111d4ad9a4f00f000000 or 62119d4ad9a4f00f000000
	VPSUBUSW -15(R14)(R15*2), Z12, K2, Z12             // 62111d4ad9a47ef1ffffff or 62119d4ad9a47ef1ffffff
	VPSUBUSW Z9, Z17, K2, Z12                          // 62517542d9e1 or 6251f542d9e1
	VPSUBUSW Z12, Z17, K2, Z12                         // 62517542d9e4 or 6251f542d9e4
	VPSUBUSW 15(R8)(R14*8), Z17, K2, Z12               // 62117542d9a4f00f000000 or 6211f542d9a4f00f000000
	VPSUBUSW -15(R14)(R15*2), Z17, K2, Z12             // 62117542d9a47ef1ffffff or 6211f542d9a47ef1ffffff
	VPSUBW X0, X12, K1, X15                            // 62711d09f9f8 or 62719d09f9f8
	VPSUBW 99(R15)(R15*8), X12, K1, X15                // 62111d09f9bcff63000000 or 62119d09f9bcff63000000
	VPSUBW 7(AX)(CX*8), X12, K1, X15                   // 62711d09f9bcc807000000 or 62719d09f9bcc807000000
	VPSUBW Y26, Y6, K7, Y7                             // 62914d2ff9fa or 6291cd2ff9fa
	VPSUBW -17(BP), Y6, K7, Y7                         // 62f14d2ff9bdefffffff or 62f1cd2ff9bdefffffff
	VPSUBW -15(R14)(R15*8), Y6, K7, Y7                 // 62914d2ff9bcfef1ffffff or 6291cd2ff9bcfef1ffffff
	VPSUBW Z8, Z3, K1, Z6                              // 62d16549f9f0 or 62d1e549f9f0
	VPSUBW Z2, Z3, K1, Z6                              // 62f16549f9f2 or 62f1e549f9f2
	VPSUBW -15(R14)(R15*1), Z3, K1, Z6                 // 62916549f9b43ef1ffffff or 6291e549f9b43ef1ffffff
	VPSUBW -15(BX), Z3, K1, Z6                         // 62f16549f9b3f1ffffff or 62f1e549f9b3f1ffffff
	VPSUBW Z8, Z21, K1, Z6                             // 62d15541f9f0 or 62d1d541f9f0
	VPSUBW Z2, Z21, K1, Z6                             // 62f15541f9f2 or 62f1d541f9f2
	VPSUBW -15(R14)(R15*1), Z21, K1, Z6                // 62915541f9b43ef1ffffff or 6291d541f9b43ef1ffffff
	VPSUBW -15(BX), Z21, K1, Z6                        // 62f15541f9b3f1ffffff or 62f1d541f9b3f1ffffff
	VPSUBW Z8, Z3, K1, Z25                             // 62416549f9c8 or 6241e549f9c8
	VPSUBW Z2, Z3, K1, Z25                             // 62616549f9ca or 6261e549f9ca
	VPSUBW -15(R14)(R15*1), Z3, K1, Z25                // 62016549f98c3ef1ffffff or 6201e549f98c3ef1ffffff
	VPSUBW -15(BX), Z3, K1, Z25                        // 62616549f98bf1ffffff or 6261e549f98bf1ffffff
	VPSUBW Z8, Z21, K1, Z25                            // 62415541f9c8 or 6241d541f9c8
	VPSUBW Z2, Z21, K1, Z25                            // 62615541f9ca or 6261d541f9ca
	VPSUBW -15(R14)(R15*1), Z21, K1, Z25               // 62015541f98c3ef1ffffff or 6201d541f98c3ef1ffffff
	VPSUBW -15(BX), Z21, K1, Z25                       // 62615541f98bf1ffffff or 6261d541f98bf1ffffff
	VPTESTMB X26, X3, K3, K3                           // 6292650b26da
	VPTESTMB 15(R8)(R14*4), X3, K3, K3                 // 6292650b269cb00f000000
	VPTESTMB -7(CX)(DX*4), X3, K3, K3                  // 62f2650b269c91f9ffffff
	VPTESTMB X26, X3, K3, K1                           // 6292650b26ca
	VPTESTMB 15(R8)(R14*4), X3, K3, K1                 // 6292650b268cb00f000000
	VPTESTMB -7(CX)(DX*4), X3, K3, K1                  // 62f2650b268c91f9ffffff
	VPTESTMB Y3, Y18, K4, K5                           // 62f26d2426eb
	VPTESTMB 15(R8)(R14*8), Y18, K4, K5                // 62926d2426acf00f000000
	VPTESTMB -15(R14)(R15*2), Y18, K4, K5              // 62926d2426ac7ef1ffffff
	VPTESTMB Y3, Y18, K4, K4                           // 62f26d2426e3
	VPTESTMB 15(R8)(R14*8), Y18, K4, K4                // 62926d2426a4f00f000000
	VPTESTMB -15(R14)(R15*2), Y18, K4, K4              // 62926d2426a47ef1ffffff
	VPTESTMB Z11, Z12, K5, K7                          // 62d21d4d26fb
	VPTESTMB Z5, Z12, K5, K7                           // 62f21d4d26fd
	VPTESTMB 17(SP)(BP*8), Z12, K5, K7                 // 62f21d4d26bcec11000000
	VPTESTMB 17(SP)(BP*4), Z12, K5, K7                 // 62f21d4d26bcac11000000
	VPTESTMB Z11, Z22, K5, K7                          // 62d24d4526fb
	VPTESTMB Z5, Z22, K5, K7                           // 62f24d4526fd
	VPTESTMB 17(SP)(BP*8), Z22, K5, K7                 // 62f24d4526bcec11000000
	VPTESTMB 17(SP)(BP*4), Z22, K5, K7                 // 62f24d4526bcac11000000
	VPTESTMB Z11, Z12, K5, K6                          // 62d21d4d26f3
	VPTESTMB Z5, Z12, K5, K6                           // 62f21d4d26f5
	VPTESTMB 17(SP)(BP*8), Z12, K5, K6                 // 62f21d4d26b4ec11000000
	VPTESTMB 17(SP)(BP*4), Z12, K5, K6                 // 62f21d4d26b4ac11000000
	VPTESTMB Z11, Z22, K5, K6                          // 62d24d4526f3
	VPTESTMB Z5, Z22, K5, K6                           // 62f24d4526f5
	VPTESTMB 17(SP)(BP*8), Z22, K5, K6                 // 62f24d4526b4ec11000000
	VPTESTMB 17(SP)(BP*4), Z22, K5, K6                 // 62f24d4526b4ac11000000
	VPTESTMW X15, X9, K4, K6                           // 62d2b50c26f7
	VPTESTMW -17(BP)(SI*2), X9, K4, K6                 // 62f2b50c26b475efffffff
	VPTESTMW 7(AX)(CX*2), X9, K4, K6                   // 62f2b50c26b44807000000
	VPTESTMW X15, X9, K4, K4                           // 62d2b50c26e7
	VPTESTMW -17(BP)(SI*2), X9, K4, K4                 // 62f2b50c26a475efffffff
	VPTESTMW 7(AX)(CX*2), X9, K4, K4                   // 62f2b50c26a44807000000
	VPTESTMW Y8, Y14, K7, K4                           // 62d28d2f26e0
	VPTESTMW (SI), Y14, K7, K4                         // 62f28d2f2626
	VPTESTMW 7(SI)(DI*2), Y14, K7, K4                  // 62f28d2f26a47e07000000
	VPTESTMW Y8, Y14, K7, K6                           // 62d28d2f26f0
	VPTESTMW (SI), Y14, K7, K6                         // 62f28d2f2636
	VPTESTMW 7(SI)(DI*2), Y14, K7, K6                  // 62f28d2f26b47e07000000
	VPTESTMW Z1, Z6, K2, K4                            // 62f2cd4a26e1
	VPTESTMW Z15, Z6, K2, K4                           // 62d2cd4a26e7
	VPTESTMW 7(AX), Z6, K2, K4                         // 62f2cd4a26a007000000
	VPTESTMW (DI), Z6, K2, K4                          // 62f2cd4a2627
	VPTESTMW Z1, Z22, K2, K4                           // 62f2cd4226e1
	VPTESTMW Z15, Z22, K2, K4                          // 62d2cd4226e7
	VPTESTMW 7(AX), Z22, K2, K4                        // 62f2cd4226a007000000
	VPTESTMW (DI), Z22, K2, K4                         // 62f2cd422627
	VPTESTMW Z1, Z6, K2, K5                            // 62f2cd4a26e9
	VPTESTMW Z15, Z6, K2, K5                           // 62d2cd4a26ef
	VPTESTMW 7(AX), Z6, K2, K5                         // 62f2cd4a26a807000000
	VPTESTMW (DI), Z6, K2, K5                          // 62f2cd4a262f
	VPTESTMW Z1, Z22, K2, K5                           // 62f2cd4226e9
	VPTESTMW Z15, Z22, K2, K5                          // 62d2cd4226ef
	VPTESTMW 7(AX), Z22, K2, K5                        // 62f2cd4226a807000000
	VPTESTMW (DI), Z22, K2, K5                         // 62f2cd42262f
	VPTESTNMB X18, X26, K5, K2                         // 62b22e0526d2
	VPTESTNMB 15(R8)(R14*1), X26, K5, K2               // 62922e052694300f000000
	VPTESTNMB 15(R8)(R14*2), X26, K5, K2               // 62922e052694700f000000
	VPTESTNMB X18, X26, K5, K7                         // 62b22e0526fa
	VPTESTNMB 15(R8)(R14*1), X26, K5, K7               // 62922e0526bc300f000000
	VPTESTNMB 15(R8)(R14*2), X26, K5, K7               // 62922e0526bc700f000000
	VPTESTNMB Y11, Y20, K3, K0                         // 62d25e2326c3
	VPTESTNMB 17(SP)(BP*8), Y20, K3, K0                // 62f25e232684ec11000000
	VPTESTNMB 17(SP)(BP*4), Y20, K3, K0                // 62f25e232684ac11000000
	VPTESTNMB Y11, Y20, K3, K5                         // 62d25e2326eb
	VPTESTNMB 17(SP)(BP*8), Y20, K3, K5                // 62f25e2326acec11000000
	VPTESTNMB 17(SP)(BP*4), Y20, K3, K5                // 62f25e2326acac11000000
	VPTESTNMB Z18, Z13, K4, K6                         // 62b2164c26f2
	VPTESTNMB Z8, Z13, K4, K6                          // 62d2164c26f0
	VPTESTNMB 99(R15)(R15*1), Z13, K4, K6              // 6292164c26b43f63000000
	VPTESTNMB (DX), Z13, K4, K6                        // 62f2164c2632
	VPTESTNMB Z18, Z13, K4, K5                         // 62b2164c26ea
	VPTESTNMB Z8, Z13, K4, K5                          // 62d2164c26e8
	VPTESTNMB 99(R15)(R15*1), Z13, K4, K5              // 6292164c26ac3f63000000
	VPTESTNMB (DX), Z13, K4, K5                        // 62f2164c262a
	VPTESTNMW X7, X3, K1, K5                           // 62f2e60926ef
	VPTESTNMW (CX), X3, K1, K5                         // 62f2e6092629
	VPTESTNMW 99(R15), X3, K1, K5                      // 62d2e60926af63000000
	VPTESTNMW X7, X3, K1, K4                           // 62f2e60926e7
	VPTESTNMW (CX), X3, K1, K4                         // 62f2e6092621
	VPTESTNMW 99(R15), X3, K1, K4                      // 62d2e60926a763000000
	VPTESTNMW Y20, Y20, K2, K4                         // 62b2de2226e4
	VPTESTNMW 7(AX), Y20, K2, K4                       // 62f2de2226a007000000
	VPTESTNMW (DI), Y20, K2, K4                        // 62f2de222627
	VPTESTNMW Y20, Y20, K2, K6                         // 62b2de2226f4
	VPTESTNMW 7(AX), Y20, K2, K6                       // 62f2de2226b007000000
	VPTESTNMW (DI), Y20, K2, K6                        // 62f2de222637
	VPTESTNMW Z28, Z12, K1, K1                         // 62929e4926cc
	VPTESTNMW Z13, Z12, K1, K1                         // 62d29e4926cd
	VPTESTNMW 7(SI)(DI*1), Z12, K1, K1                 // 62f29e49268c3e07000000
	VPTESTNMW 15(DX)(BX*8), Z12, K1, K1                // 62f29e49268cda0f000000
	VPTESTNMW Z28, Z16, K1, K1                         // 6292fe4126cc
	VPTESTNMW Z13, Z16, K1, K1                         // 62d2fe4126cd
	VPTESTNMW 7(SI)(DI*1), Z16, K1, K1                 // 62f2fe41268c3e07000000
	VPTESTNMW 15(DX)(BX*8), Z16, K1, K1                // 62f2fe41268cda0f000000
	VPTESTNMW Z28, Z12, K1, K3                         // 62929e4926dc
	VPTESTNMW Z13, Z12, K1, K3                         // 62d29e4926dd
	VPTESTNMW 7(SI)(DI*1), Z12, K1, K3                 // 62f29e49269c3e07000000
	VPTESTNMW 15(DX)(BX*8), Z12, K1, K3                // 62f29e49269cda0f000000
	VPTESTNMW Z28, Z16, K1, K3                         // 6292fe4126dc
	VPTESTNMW Z13, Z16, K1, K3                         // 62d2fe4126dd
	VPTESTNMW 7(SI)(DI*1), Z16, K1, K3                 // 62f2fe41269c3e07000000
	VPTESTNMW 15(DX)(BX*8), Z16, K1, K3                // 62f2fe41269cda0f000000
	VPUNPCKHBW X24, X0, K7, X0                         // 62917d0f68c0 or 6291fd0f68c0
	VPUNPCKHBW 99(R15)(R15*2), X0, K7, X0              // 62917d0f68847f63000000 or 6291fd0f68847f63000000
	VPUNPCKHBW -7(DI), X0, K7, X0                      // 62f17d0f6887f9ffffff or 62f1fd0f6887f9ffffff
	VPUNPCKHBW Y28, Y28, K1, Y9                        // 62111d2168cc or 62119d2168cc
	VPUNPCKHBW 99(R15)(R15*1), Y28, K1, Y9             // 62111d21688c3f63000000 or 62119d21688c3f63000000
	VPUNPCKHBW (DX), Y28, K1, Y9                       // 62711d21680a or 62719d21680a
	VPUNPCKHBW Z15, Z3, K1, Z14                        // 6251654968f7 or 6251e54968f7
	VPUNPCKHBW Z30, Z3, K1, Z14                        // 6211654968f6 or 6211e54968f6
	VPUNPCKHBW -7(DI)(R8*1), Z3, K1, Z14               // 6231654968b407f9ffffff or 6231e54968b407f9ffffff
	VPUNPCKHBW (SP), Z3, K1, Z14                       // 62716549683424 or 6271e549683424
	VPUNPCKHBW Z15, Z12, K1, Z14                       // 62511d4968f7 or 62519d4968f7
	VPUNPCKHBW Z30, Z12, K1, Z14                       // 62111d4968f6 or 62119d4968f6
	VPUNPCKHBW -7(DI)(R8*1), Z12, K1, Z14              // 62311d4968b407f9ffffff or 62319d4968b407f9ffffff
	VPUNPCKHBW (SP), Z12, K1, Z14                      // 62711d49683424 or 62719d49683424
	VPUNPCKHBW Z15, Z3, K1, Z28                        // 6241654968e7 or 6241e54968e7
	VPUNPCKHBW Z30, Z3, K1, Z28                        // 6201654968e6 or 6201e54968e6
	VPUNPCKHBW -7(DI)(R8*1), Z3, K1, Z28               // 6221654968a407f9ffffff or 6221e54968a407f9ffffff
	VPUNPCKHBW (SP), Z3, K1, Z28                       // 62616549682424 or 6261e549682424
	VPUNPCKHBW Z15, Z12, K1, Z28                       // 62411d4968e7 or 62419d4968e7
	VPUNPCKHBW Z30, Z12, K1, Z28                       // 62011d4968e6 or 62019d4968e6
	VPUNPCKHBW -7(DI)(R8*1), Z12, K1, Z28              // 62211d4968a407f9ffffff or 62219d4968a407f9ffffff
	VPUNPCKHBW (SP), Z12, K1, Z28                      // 62611d49682424 or 62619d49682424
	VPUNPCKHWD X21, X3, K4, X31                        // 6221650c69fd or 6221e50c69fd
	VPUNPCKHWD -17(BP), X3, K4, X31                    // 6261650c69bdefffffff or 6261e50c69bdefffffff
	VPUNPCKHWD -15(R14)(R15*8), X3, K4, X31            // 6201650c69bcfef1ffffff or 6201e50c69bcfef1ffffff
	VPUNPCKHWD Y26, Y6, K5, Y12                        // 62114d2d69e2 or 6211cd2d69e2
	VPUNPCKHWD 7(SI)(DI*1), Y6, K5, Y12                // 62714d2d69a43e07000000 or 6271cd2d69a43e07000000
	VPUNPCKHWD 15(DX)(BX*8), Y6, K5, Y12               // 62714d2d69a4da0f000000 or 6271cd2d69a4da0f000000
	VPUNPCKHWD Z0, Z23, K7, Z20                        // 62e1454769e0 or 62e1c54769e0
	VPUNPCKHWD Z11, Z23, K7, Z20                       // 62c1454769e3 or 62c1c54769e3
	VPUNPCKHWD (AX), Z23, K7, Z20                      // 62e145476920 or 62e1c5476920
	VPUNPCKHWD 7(SI), Z23, K7, Z20                     // 62e1454769a607000000 or 62e1c54769a607000000
	VPUNPCKHWD Z0, Z19, K7, Z20                        // 62e1654769e0 or 62e1e54769e0
	VPUNPCKHWD Z11, Z19, K7, Z20                       // 62c1654769e3 or 62c1e54769e3
	VPUNPCKHWD (AX), Z19, K7, Z20                      // 62e165476920 or 62e1e5476920
	VPUNPCKHWD 7(SI), Z19, K7, Z20                     // 62e1654769a607000000 or 62e1e54769a607000000
	VPUNPCKHWD Z0, Z23, K7, Z0                         // 62f1454769c0 or 62f1c54769c0
	VPUNPCKHWD Z11, Z23, K7, Z0                        // 62d1454769c3 or 62d1c54769c3
	VPUNPCKHWD (AX), Z23, K7, Z0                       // 62f145476900 or 62f1c5476900
	VPUNPCKHWD 7(SI), Z23, K7, Z0                      // 62f14547698607000000 or 62f1c547698607000000
	VPUNPCKHWD Z0, Z19, K7, Z0                         // 62f1654769c0 or 62f1e54769c0
	VPUNPCKHWD Z11, Z19, K7, Z0                        // 62d1654769c3 or 62d1e54769c3
	VPUNPCKHWD (AX), Z19, K7, Z0                       // 62f165476900 or 62f1e5476900
	VPUNPCKHWD 7(SI), Z19, K7, Z0                      // 62f16547698607000000 or 62f1e547698607000000
	VPUNPCKLBW X13, X11, K7, X1                        // 62d1250f60cd or 62d1a50f60cd
	VPUNPCKLBW 17(SP)(BP*2), X11, K7, X1               // 62f1250f608c6c11000000 or 62f1a50f608c6c11000000
	VPUNPCKLBW -7(DI)(R8*4), X11, K7, X1               // 62b1250f608c87f9ffffff or 62b1a50f608c87f9ffffff
	VPUNPCKLBW Y28, Y8, K6, Y3                         // 62913d2e60dc or 6291bd2e60dc
	VPUNPCKLBW -7(DI)(R8*1), Y8, K6, Y3                // 62b13d2e609c07f9ffffff or 62b1bd2e609c07f9ffffff
	VPUNPCKLBW (SP), Y8, K6, Y3                        // 62f13d2e601c24 or 62f1bd2e601c24
	VPUNPCKLBW Z0, Z24, K3, Z0                         // 62f13d4360c0 or 62f1bd4360c0
	VPUNPCKLBW Z26, Z24, K3, Z0                        // 62913d4360c2 or 6291bd4360c2
	VPUNPCKLBW (BX), Z24, K3, Z0                       // 62f13d436003 or 62f1bd436003
	VPUNPCKLBW -17(BP)(SI*1), Z24, K3, Z0              // 62f13d43608435efffffff or 62f1bd43608435efffffff
	VPUNPCKLBW Z0, Z12, K3, Z0                         // 62f11d4b60c0 or 62f19d4b60c0
	VPUNPCKLBW Z26, Z12, K3, Z0                        // 62911d4b60c2 or 62919d4b60c2
	VPUNPCKLBW (BX), Z12, K3, Z0                       // 62f11d4b6003 or 62f19d4b6003
	VPUNPCKLBW -17(BP)(SI*1), Z12, K3, Z0              // 62f11d4b608435efffffff or 62f19d4b608435efffffff
	VPUNPCKLBW Z0, Z24, K3, Z25                        // 62613d4360c8 or 6261bd4360c8
	VPUNPCKLBW Z26, Z24, K3, Z25                       // 62013d4360ca or 6201bd4360ca
	VPUNPCKLBW (BX), Z24, K3, Z25                      // 62613d43600b or 6261bd43600b
	VPUNPCKLBW -17(BP)(SI*1), Z24, K3, Z25             // 62613d43608c35efffffff or 6261bd43608c35efffffff
	VPUNPCKLBW Z0, Z12, K3, Z25                        // 62611d4b60c8 or 62619d4b60c8
	VPUNPCKLBW Z26, Z12, K3, Z25                       // 62011d4b60ca or 62019d4b60ca
	VPUNPCKLBW (BX), Z12, K3, Z25                      // 62611d4b600b or 62619d4b600b
	VPUNPCKLBW -17(BP)(SI*1), Z12, K3, Z25             // 62611d4b608c35efffffff or 62619d4b608c35efffffff
	VPUNPCKLWD X8, X8, K3, X19                         // 62c13d0b61d8 or 62c1bd0b61d8
	VPUNPCKLWD -15(R14)(R15*1), X8, K3, X19            // 62813d0b619c3ef1ffffff or 6281bd0b619c3ef1ffffff
	VPUNPCKLWD -15(BX), X8, K3, X19                    // 62e13d0b619bf1ffffff or 62e1bd0b619bf1ffffff
	VPUNPCKLWD Y8, Y27, K4, Y22                        // 62c1252461f0 or 62c1a52461f0
	VPUNPCKLWD (AX), Y27, K4, Y22                      // 62e125246130 or 62e1a5246130
	VPUNPCKLWD 7(SI), Y27, K4, Y22                     // 62e1252461b607000000 or 62e1a52461b607000000
	VPUNPCKLWD Z6, Z21, K2, Z31                        // 6261554261fe or 6261d54261fe
	VPUNPCKLWD Z9, Z21, K2, Z31                        // 6241554261f9 or 6241d54261f9
	VPUNPCKLWD 17(SP)(BP*1), Z21, K2, Z31              // 6261554261bc2c11000000 or 6261d54261bc2c11000000
	VPUNPCKLWD -7(CX)(DX*8), Z21, K2, Z31              // 6261554261bcd1f9ffffff or 6261d54261bcd1f9ffffff
	VPUNPCKLWD Z6, Z9, K2, Z31                         // 6261354a61fe or 6261b54a61fe
	VPUNPCKLWD Z9, Z9, K2, Z31                         // 6241354a61f9 or 6241b54a61f9
	VPUNPCKLWD 17(SP)(BP*1), Z9, K2, Z31               // 6261354a61bc2c11000000 or 6261b54a61bc2c11000000
	VPUNPCKLWD -7(CX)(DX*8), Z9, K2, Z31               // 6261354a61bcd1f9ffffff or 6261b54a61bcd1f9ffffff
	VPUNPCKLWD Z6, Z21, K2, Z0                         // 62f1554261c6 or 62f1d54261c6
	VPUNPCKLWD Z9, Z21, K2, Z0                         // 62d1554261c1 or 62d1d54261c1
	VPUNPCKLWD 17(SP)(BP*1), Z21, K2, Z0               // 62f1554261842c11000000 or 62f1d54261842c11000000
	VPUNPCKLWD -7(CX)(DX*8), Z21, K2, Z0               // 62f155426184d1f9ffffff or 62f1d5426184d1f9ffffff
	VPUNPCKLWD Z6, Z9, K2, Z0                          // 62f1354a61c6 or 62f1b54a61c6
	VPUNPCKLWD Z9, Z9, K2, Z0                          // 62d1354a61c1 or 62d1b54a61c1
	VPUNPCKLWD 17(SP)(BP*1), Z9, K2, Z0                // 62f1354a61842c11000000 or 62f1b54a61842c11000000
	VPUNPCKLWD -7(CX)(DX*8), Z9, K2, Z0                // 62f1354a6184d1f9ffffff or 62f1b54a6184d1f9ffffff
	RET
