rsc.io/quote@v0.0.0-20180709162918-a91498bed0a7

-- .mod --
module rsc.io/quote

require rsc.io/sampler v1.3.0
-- .info --
{"Version":"v0.0.0-20180709162918-a91498bed0a7","Name":"a91498bed0a73d4bb9c1fb2597925f7883bc40a7","Short":"a91498bed0a7","Time":"2018-07-09T16:29:18Z"}
-- buggy/buggy_test.go --
// Copyright 2018 The Go Authors. All rights reserved.
// Use of this source code is governed by a BSD-style
// license that can be found in the LICENSE file.

package buggy

import "testing"

func Test(t *testing.T) {
	t.Fatal("buggy!")
}
-- go.mod --
module rsc.io/quote

require rsc.io/sampler v1.3.0
-- quote.go --
// Copyright 2018 The Go Authors. All rights reserved.
// Use of this source code is governed by a BSD-style
// license that can be found in the LICENSE file.

// Package quote collects pithy sayings.
package quote // import "rsc.io/quote"

import "rsc.io/quote/v3"

// Hello returns a greeting.
func Hello() string {
	return quote.HelloV3()
}

// Glass returns a useful phrase for world travelers.
func Glass() string {
	// See http://www.oocities.org/nodotus/hbglass.html.
	return quote.GlassV3()
}

// Go returns a Go proverb.
func Go() string {
	return quote.GoV3()
}

// Opt returns an optimization truth.
func Opt() string {
	// Wisdom from ken.
	return quote.OptV3()
}
-- quote_test.go --
// Copyright 2018 The Go Authors. All rights reserved.
// Use of this source code is governed by a BSD-style
// license that can be found in the LICENSE file.

package quote

import (
	"os"
	"testing"
)

func init() {
	os.Setenv("LC_ALL", "en")
}

func TestHello(t *testing.T) {
	hello := "Hello, world."
	if out := Hello(); out != hello {
		t.Errorf("Hello() = %q, want %q", out, hello)
	}
}

func TestGlass(t *testing.T) {
	glass := "I can eat glass and it doesn't hurt me."
	if out := Glass(); out != glass {
		t.Errorf("Glass() = %q, want %q", out, glass)
	}
}

func TestGo(t *testing.T) {
	go1 := "Don't communicate by sharing memory, share memory by communicating."
	if out := Go(); out != go1 {
		t.Errorf("Go() = %q, want %q", out, go1)
	}
}

func TestOpt(t *testing.T) {
	opt := "If a program is too slow, it must have a loop."
	if out := Opt(); out != opt {
		t.Errorf("Opt() = %q, want %q", out, opt)
	}
}
