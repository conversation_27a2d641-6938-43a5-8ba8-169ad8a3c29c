env GO111MODULE=on

go get
go list -m all
stdout '^rsc.io/quote v1.5.2'
stdout '^rsc.io/QUOTE v1.5.2'

go list -f 'DIR {{.Dir}} DEPS {{.Deps}}' rsc.io/QUOTE/QUOTE
stdout 'DEPS.*rsc.io/quote'
stdout 'DIR.*!q!u!o!t!e'

go get rsc.io/QUOTE@v1.5.3-PRE
go list -m all
stdout '^rsc.io/QUOTE v1.5.3-PRE'

go list -f '{{.Dir}}' rsc.io/QUOTE/QUOTE
stdout '!q!u!o!t!e@v1.5.3-!p!r!e'

-- go.mod --
module x

-- use.go --
package use

import _ "rsc.io/QUOTE/QUOTE"
