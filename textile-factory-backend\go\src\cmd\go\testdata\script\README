This file is generated by 'go generate cmd/go'. DO NOT EDIT.

This directory holds test scripts *.txt run during 'go test cmd/go'.
To run a specific script foo.txt

	go test cmd/go -run=Script/^foo$

In general script files should have short names: a few words, not whole sentences.
The first word should be the general category of behavior being tested,
often the name of a go subcommand (list, build, test, ...) or concept (vendor, pattern).

Each script is a text archive (go doc internal/txtar).
The script begins with an actual command script to run
followed by the content of zero or more supporting files to
create in the script's temporary file system before it starts executing.

As an example, run_hello.txt says:

	# hello world
	go run hello.go
	stderr 'hello world'
	! stdout .

	-- hello.go --
	package main
	func main() { println("hello world") }

Each script runs in a fresh temporary work directory tree, available to scripts as $WORK.
Scripts also have access to other environment variables, including:

	GOARCH=<target GOARCH>
	GOCACHE=<actual GOCACHE being used outside the test>
	GOEXE=<executable file suffix: .exe on Windows, empty on other systems>
	GOOS=<target GOOS>
	GOPATH=$WORK/gopath
	GOPROXY=<local module proxy serving from cmd/go/testdata/mod>
	GOROOT=<actual GOROOT>
	GOROOT_FINAL=<actual GOROOT_FINAL>
	TESTGO_GOROOT=<GOROOT used to build cmd/go, for use in tests that may change GOROOT>
	HOME=/no-home
	PATH=<actual PATH>
	TMPDIR=$WORK/tmp
	GODEBUG=<actual GODEBUG>
	devnull=<value of os.DevNull>
	goversion=<current Go version; for example, 1.12>

On Plan 9, the variables $path and $home are set instead of $PATH and $HOME.
On Windows, the variables $USERPROFILE and $TMP are set instead of
$HOME and $TMPDIR.

The lines at the top of the script are a sequence of commands to be executed by
a small script engine configured in ../../script_test.go (not the system shell).

The scripts' supporting files are unpacked relative to $GOPATH/src
(aka $WORK/gopath/src) and then the script begins execution in that directory as
well. Thus the example above runs in $WORK/gopath/src with GOPATH=$WORK/gopath
and $WORK/gopath/src/hello.go containing the listed contents.

Each line of a script is parsed into a sequence of space-separated command
words, with environment variable expansion within each word and # marking
an end-of-line comment. Additional variables named ':' and '/' are expanded
within script arguments (expanding to the value of os.PathListSeparator and
os.PathSeparator respectively) but are not inherited in subprocess environments.

Adding single quotes around text keeps spaces in that text from being treated
as word separators and also disables environment variable expansion. Inside a
single-quoted block of text, a repeated single quote indicates a literal single
quote, as in:

    'Don''t communicate by sharing memory.'

A line beginning with # is a comment and conventionally explains what is being
done or tested at the start of a new section of the script.

Commands are executed one at a time, and errors are checked for each command;
if any command fails unexpectedly, no subsequent commands in the script are
executed. The command prefix ! indicates that the command on the rest of the
line (typically go or a matching predicate) must fail instead of succeeding.
The command prefix ? indicates that the command may or may not succeed, but the
script should continue regardless.

The command prefix [cond] indicates that the command on the rest of the line
should only run when the condition is satisfied.

A condition can be negated: [!root] means to run the rest of the line only if
the user is not root. Multiple conditions may be given for a single command,
for example, '[linux] [amd64] skip'. The command will run if all conditions are
satisfied.

When TestScript runs a script and the script fails, by default TestScript shows
the execution of the most recent phase of the script (since the last # comment)
and only shows the # comments for earlier phases. For example, here is a
multi-phase script with a bug in it:

	# GOPATH with p1 in d2, p2 in d2
	env GOPATH=$WORK${/}d1${:}$WORK${/}d2

	# build & install p1
	env
	go install -i p1
	! stale p1
	! stale p2

	# modify p2 - p1 should appear stale
	cp $WORK/p2x.go $WORK/d2/src/p2/p2.go
	stale p1 p2

	# build & install p1 again
	go install -i p11
	! stale p1
	! stale p2

	-- $WORK/d1/src/p1/p1.go --
	package p1
	import "p2"
	func F() { p2.F() }
	-- $WORK/d2/src/p2/p2.go --
	package p2
	func F() {}
	-- $WORK/p2x.go --
	package p2
	func F() {}
	func G() {}

The bug is that the final phase installs p11 instead of p1. The test failure looks like:

	$ go test -run=Script
	--- FAIL: TestScript (3.75s)
	    --- FAIL: TestScript/install_rebuild_gopath (0.16s)
	        script_test.go:223:
	            # GOPATH with p1 in d2, p2 in d2 (0.000s)
	            # build & install p1 (0.087s)
	            # modify p2 - p1 should appear stale (0.029s)
	            # build & install p1 again (0.022s)
	            > go install -i p11
	            [stderr]
	            can't load package: package p11: cannot find package "p11" in any of:
	            	/Users/<USER>/go/src/p11 (from $GOROOT)
	            	$WORK/d1/src/p11 (from $GOPATH)
	            	$WORK/d2/src/p11
	            [exit status 1]
	            FAIL: unexpected go command failure

	        script_test.go:73: failed at testdata/script/install_rebuild_gopath.txt:15 in $WORK/gopath/src

	FAIL
	exit status 1
	FAIL	cmd/go	4.875s
	$

Note that the commands in earlier phases have been hidden, so that the relevant
commands are more easily found, and the elapsed time for a completed phase
is shown next to the phase heading. To see the entire execution, use "go test -v",
which also adds an initial environment dump to the beginning of the log.

Note also that in reported output, the actual name of the per-script temporary directory
has been consistently replaced with the literal string $WORK.

The cmd/go test flag -testwork (which must appear on the "go test" command line after
standard test flags) causes each test to log the name of its $WORK directory and other
environment variable settings and also to leave that directory behind when it exits,
for manual debugging of failing tests:

	$ go test -run=Script -work
	--- FAIL: TestScript (3.75s)
	    --- FAIL: TestScript/install_rebuild_gopath (0.16s)
	        script_test.go:223:
	            WORK=/tmp/cmd-go-test-745953508/script-install_rebuild_gopath
	            GOARCH=
	            GOCACHE=/Users/<USER>/Library/Caches/go-build
	            GOOS=
	            GOPATH=$WORK/gopath
	            GOROOT=/Users/<USER>/go
	            HOME=/no-home
	            TMPDIR=$WORK/tmp
	            exe=

	            # GOPATH with p1 in d2, p2 in d2 (0.000s)
	            # build & install p1 (0.085s)
	            # modify p2 - p1 should appear stale (0.030s)
	            # build & install p1 again (0.019s)
	            > go install -i p11
	            [stderr]
	            can't load package: package p11: cannot find package "p11" in any of:
	            	/Users/<USER>/go/src/p11 (from $GOROOT)
	            	$WORK/d1/src/p11 (from $GOPATH)
	            	$WORK/d2/src/p11
	            [exit status 1]
	            FAIL: unexpected go command failure

	        script_test.go:73: failed at testdata/script/install_rebuild_gopath.txt:15 in $WORK/gopath/src

	FAIL
	exit status 1
	FAIL	cmd/go	4.875s
	$

	$ WORK=/tmp/cmd-go-test-745953508/script-install_rebuild_gopath
	$ cd $WORK/d1/src/p1
	$ cat p1.go
	package p1
	import "p2"
	func F() { p2.F() }
	$

The available commands are:
cat files...
	concatenate files and print to the script's stdout buffer


cc args...
	run the platform C compiler


cd dir
	change the working directory


chmod perm paths...
	change file mode bits

	Changes the permissions of the named files or directories to
	be equal to perm.
	Only numerical permissions are supported.

cmp [-q] file1 file2
	compare files for differences

	By convention, file1 is the actual data and file2 is the
	expected data.
	The command succeeds if the file contents are identical.
	File1 can be 'stdout' or 'stderr' to compare the stdout or
	stderr buffer from the most recent command.

cmpenv [-q] file1 file2
	compare files for differences, with environment expansion

	By convention, file1 is the actual data and file2 is the
	expected data.
	The command succeeds if the file contents are identical
	after substituting variables from the script environment.
	File1 can be 'stdout' or 'stderr' to compare the script's
	stdout or stderr buffer.

cp src... dst
	copy files to a target file or directory

	src can include 'stdout' or 'stderr' to copy from the
	script's stdout or stderr buffer.

echo string...
	display a line of text


env [key[=value]...]
	set or log the values of environment variables

	With no arguments, print the script environment to the log.
	Otherwise, add the listed key=value pairs to the environment
	or print the listed keys.

exec program [args...] [&]
	run an executable program with arguments

	Note that 'exec' does not terminate the script (unlike Unix
	shells).

exists [-readonly] [-exec] file...
	check that files exist


go [args...] [&]
	run the 'go' program provided by the script host


grep [-count=N] [-q] 'pattern' file
	find lines in a file that match a pattern

	The command succeeds if at least one match (or the exact
	count, if given) is found.
	The -q flag suppresses printing of matches.

help [-v] name...
	log help text for commands and conditions

	To display help for a specific condition, enclose it in
	brackets: 'help [amd64]'.
	To display complete documentation when listing all commands,
	pass the -v flag.

mkdir path...
	create directories, if they do not already exist

	Unlike Unix mkdir, parent directories are always created if
	needed.

mv old new
	rename a file or directory to a new path

	OS-specific restrictions may apply when old and new are in
	different directories.

replace [old new]... file
	replace strings in a file

	The 'old' and 'new' arguments are unquoted as if in quoted
	Go strings.

rm path...
	remove a file or directory

	If the path is a directory, its contents are removed
	recursively.

skip [msg]
	skip the current test


sleep duration [&]
	sleep for a specified duration

	The duration must be given as a Go time.Duration string.

stale target...
	check that build targets are stale


stderr [-count=N] [-q] 'pattern' file
	find lines in the stderr buffer that match a pattern

	The command succeeds if at least one match (or the exact
	count, if given) is found.
	The -q flag suppresses printing of matches.

stdout [-count=N] [-q] 'pattern' file
	find lines in the stdout buffer that match a pattern

	The command succeeds if at least one match (or the exact
	count, if given) is found.
	The -q flag suppresses printing of matches.

stop [msg]
	stop execution of the script

	The message is written to the script log, but no error is
	reported from the script engine.

symlink path -> target
	create a symlink

	Creates path as a symlink to target.
	The '->' token (like in 'ls -l' output on Unix) is required.

wait 
	wait for completion of background commands

	Waits for all background commands to complete.
	The output (and any error) from each command is printed to
	the log in the order in which the commands were started.
	After the call to 'wait', the script's stdout and stderr
	buffers contain the concatenation of the background
	commands' outputs.



The available conditions are:
[GOARCH:*]
	runtime.GOARCH == <suffix>
[GODEBUG:*]
	GODEBUG contains <suffix>
[GOEXPERIMENT:*]
	GOEXPERIMENT <suffix> is enabled
[GOOS:*]
	runtime.GOOS == <suffix>
[abscc]
	default $CC path is absolute and exists
[asan]
	GOOS/GOARCH supports -asan
[buildmode:*]
	go supports -buildmode=<suffix>
[case-sensitive]
	$WORK filesystem is case-sensitive
[cc:*]
	go env CC = <suffix> (ignoring the go/env file)
[cgo]
	host CGO_ENABLED
[cgolinkext]
	platform requires external linking for cgo
[compiler:*]
	runtime.Compiler == <suffix>
[cross]
	cmd/go GOOS/GOARCH != GOHOSTOS/GOHOSTARCH
[exec:*]
	<suffix> names an executable in the test binary's PATH
[fuzz]
	GOOS/GOARCH supports -fuzz
[fuzz-instrumented]
	GOOS/GOARCH supports -fuzz with instrumentation
[git]
	the 'git' executable exists and provides the standard CLI
[go-builder]
	GO_BUILDER_NAME is non-empty
[link]
	testenv.HasLink()
[mismatched-goroot]
	test's GOROOT_FINAL does not match the real GOROOT
[msan]
	GOOS/GOARCH supports -msan
[mustlinkext]
	platform always requires external linking
[net:*]
	can connect to external network host <suffix>
[race]
	GOOS/GOARCH supports -race
[root]
	os.Geteuid() == 0
[short]
	testing.Short()
[symlink]
	testenv.HasSymlink()
[trimpath]
	test binary was built with -trimpath
[verbose]
	testing.Verbose()

