package models

import (
	"time"
)

type User struct {
	ID        uint      `json:"id" gorm:"primaryKey"`
	Username  string    `json:"username" gorm:"uniqueIndex;not null"`
	Password  string    `json:"-" gorm:"not null"`
	Role      string    `json:"role" gorm:"not null;check:role IN ('织工','机修工')"`
	Name      string    `json:"name" gorm:"not null"`
	CreatedAt time.Time `json:"created_at"`
	UpdatedAt time.Time `json:"updated_at"`
}

type Machine struct {
	ID        uint      `json:"id" gorm:"primaryKey"`
	Code      string    `json:"code" gorm:"uniqueIndex;not null"`
	Name      string    `json:"name" gorm:"not null"`
	Location  string    `json:"location" gorm:"not null"`
	Status    string    `json:"status" gorm:"default:'正常';check:status IN ('正常','异常','维修中')"`
	QRCode    string    `json:"qr_code" gorm:"not null"`
	CreatedAt time.Time `json:"created_at"`
	UpdatedAt time.Time `json:"updated_at"`
}

type Anomaly struct {
	ID          uint      `json:"id" gorm:"primaryKey"`
	MachineID   uint      `json:"machine_id" gorm:"not null"`
	UserID      uint      `json:"user_id" gorm:"not null"`
	Description string    `json:"description" gorm:"not null"`
	Severity    string    `json:"severity" gorm:"not null;check:severity IN ('低','中','高')"`
	Status      string    `json:"status" gorm:"default:'待维修';check:status IN ('待维修','维修中','已完成')"`
	Remark      string    `json:"remark"`
	CreatedAt   time.Time `json:"created_at"`
	UpdatedAt   time.Time `json:"updated_at"`

	Machine Machine `json:"machine" gorm:"foreignKey:MachineID"`
	User    User    `json:"user" gorm:"foreignKey:UserID"`
}

type Repair struct {
	ID         uint       `json:"id" gorm:"primaryKey"`
	AnomalyID  uint       `json:"anomaly_id" gorm:"not null;uniqueIndex"`
	MechanicID uint       `json:"mechanic_id" gorm:"not null"`
	StartTime  time.Time  `json:"start_time"`
	EndTime    *time.Time `json:"end_time"`
	Process    string     `json:"process"`
	Parts      string     `json:"parts"`
	Duration   int        `json:"duration"`
	Status     string     `json:"status" gorm:"default:'进行中';check:status IN ('进行中','已完成')"`
	CreatedAt  time.Time  `json:"created_at"`
	UpdatedAt  time.Time  `json:"updated_at"`

	Anomaly  Anomaly `json:"anomaly" gorm:"foreignKey:AnomalyID"`
	Mechanic User    `json:"mechanic" gorm:"foreignKey:MechanicID"`
}
