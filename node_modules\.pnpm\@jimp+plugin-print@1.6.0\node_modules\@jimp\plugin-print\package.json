{"name": "@jimp/plugin-print", "version": "1.6.0", "repository": "jimp-dev/jimp", "engines": {"node": ">=18"}, "sideEffects": false, "scripts": {"lint": "eslint .", "test": "vitest", "build": "tshy && cp -r fonts dist", "dev": "tshy --watch", "clean": "rm -rf node_modules .tshy .tshy-build dist .turbo"}, "author": "<PERSON> <<EMAIL>>", "license": "MIT", "devDependencies": {"@jimp/config-eslint": "1.6.0", "@jimp/config-typescript": "1.6.0", "@jimp/js-png": "workspace:*", "@jimp/test-utils": "1.6.0", "@types/node": "^18.19.48", "eslint": "^9.9.1", "tshy": "^3.0.2", "typescript": "^5.5.4", "vite-plugin-node-polyfills": "^0.22.0", "vitest": "^2.0.5"}, "tshy": {"exclude": ["**/*.test.ts"], "exports": {"./package.json": "./package.json", ".": "./src/index.ts", "./load-font": "./src/load-font.ts", "./fonts": "./src/fonts.ts"}}, "exports": {"./package.json": "./package.json", ".": {"import": {"types": "./dist/esm/index.d.ts", "default": "./dist/esm/index.js"}, "require": {"types": "./dist/commonjs/index.d.ts", "default": "./dist/commonjs/index.js"}}, "./load-font": {"import": {"types": "./dist/esm/load-font.d.ts", "default": "./dist/esm/load-font.js"}, "require": {"types": "./dist/commonjs/load-font.d.ts", "default": "./dist/commonjs/load-font.js"}}, "./fonts": {"import": {"types": "./dist/esm/fonts.d.ts", "default": "./dist/esm/fonts.js"}, "require": {"types": "./dist/commonjs/fonts.d.ts", "default": "./dist/commonjs/fonts.js"}}}, "main": "./dist/commonjs/index.js", "types": "./dist/commonjs/index.d.ts", "type": "module", "dependencies": {"@jimp/core": "1.6.0", "@jimp/js-jpeg": "1.6.0", "@jimp/js-png": "1.6.0", "@jimp/plugin-blit": "1.6.0", "@jimp/types": "1.6.0", "parse-bmfont-ascii": "^1.0.6", "parse-bmfont-binary": "^1.0.6", "parse-bmfont-xml": "^1.1.6", "simple-xml-to-json": "^1.2.2", "zod": "^3.23.8"}, "publishConfig": {"access": "public"}, "module": "./dist/esm/index.js", "gitHead": "c88abe6046dccbdb6e4f5f00c3dd403c81d83515"}