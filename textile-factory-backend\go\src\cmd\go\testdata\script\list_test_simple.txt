[short] skip

# Test
go test -list=Test
stdout TestSimple

# Benchmark
go test -list=Benchmark
stdout BenchmarkSimple

# Examples
go test -list=Example
stdout ExampleSimple
stdout ExampleWithEmptyOutput

-- go.mod --
module m

go 1.16
-- bench_test.go --
package testlist

import (
	"fmt"
	"testing"
)

func BenchmarkSimplefunc(b *testing.B) {
	b.StopTimer()
	b.StartTimer()
	for i := 0; i < b.N; i++ {
		_ = fmt.Sprint("Test for bench")
	}
}
-- example_test.go --
package testlist

import (
	"fmt"
)

func ExampleSimple() {
	fmt.Println("Test with Output.")

	// Output: Test with Output.
}

func ExampleWithEmptyOutput() {
	fmt.Println("")

	// Output:
}

func ExampleNoOutput() {
	_ = fmt.Sprint("Test with no output")
}
-- test_test.go --
package testlist

import (
	"fmt"
	"testing"
)

func TestSimple(t *testing.T) {
	_ = fmt.Sprint("Test simple")
}
