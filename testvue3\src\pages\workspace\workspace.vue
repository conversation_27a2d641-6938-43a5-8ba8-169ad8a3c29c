<script lang="ts" setup>
import { ref, onMounted } from 'vue'

// 用户信息
const userInfo = ref({
  username: '',
  role: '',
  roleText: ''
})

// 统计数据
const stats = ref({
  todayTasks: 0,
  completedTasks: 0,
  pendingTasks: 0,
  totalTasks: 0
})

// 快捷功能
const quickActions = ref([])

// 历史记录（织工专用）
const reportHistory = ref([
  {
    id: 1,
    machineCode: 'M001',
    machineName: '织机A-01',
    reportTime: '2024-01-15 14:30',
    status: 'pending',
    description: '机器异响，疑似轴承问题'
  },
  {
    id: 2,
    machineCode: 'M002',
    machineName: '织机B-03',
    reportTime: '2024-01-15 10:15',
    status: 'completed',
    description: '线头断裂频繁'
  },
  {
    id: 3,
    machineCode: 'M003',
    machineName: '织机C-05',
    reportTime: '2024-01-14 16:45',
    status: 'repairing',
    description: '温度过高，需要检查冷却系统'
  }
])

// 获取状态信息
const getStatusInfo = (status: string) => {
  const statusMap = {
    pending: { text: '待维修', color: '#FF9800' },
    repairing: { text: '维修中', color: '#2196F3' },
    completed: { text: '已完成', color: '#4CAF50' }
  }
  return statusMap[status as keyof typeof statusMap] || { text: '未知', color: '#999999' }
}

// 获取用户信息
const getUserInfo = () => {
  const info = uni.getStorageSync('userInfo')
  if (info) {
    userInfo.value = {
      username: info.username,
      role: info.role,
      roleText: info.role === 'worker' ? '织工' : '机修工'
    }
    
    // 根据角色设置快捷功能
    if (info.role === 'worker') {
      quickActions.value = [
        { icon: '📱', text: '扫码上报', action: 'scan' },
        { icon: '📋', text: '上报记录', action: 'history' },
        { icon: '📊', text: '工作统计', action: 'stats' },
        { icon: '🔧', text: '设备状态', action: 'equipment' }
      ]
    } else {
      quickActions.value = [
        { icon: '🔧', text: '维修任务', path: '/pages/mechanic/home' },
        { icon: '📝', text: '维修记录', action: 'repair_history' },
        { icon: '📊', text: '工作统计', action: 'stats' },
        { icon: '⚙️', text: '设备管理', action: 'equipment' }
      ]
    }
  }
}

// 模拟获取统计数据
const getStats = () => {
  if (userInfo.value.role === 'worker') {
    stats.value = {
      todayTasks: 3,
      completedTasks: 2,
      pendingTasks: 1,
      totalTasks: 15
    }
  } else {
    stats.value = {
      todayTasks: 5,
      completedTasks: 3,
      pendingTasks: 2,
      totalTasks: 28
    }
  }
}

// 扫码上报
const handleScanReport = () => {
  uni.scanCode({
    success: (res) => {
      console.log('扫码结果:', res.result)
      uni.navigateTo({
        url: `/pages/report/report?machineCode=${res.result}`
      })
    },
    fail: (err) => {
      console.error('扫码失败:', err)
      uni.showToast({
        title: '扫码失败',
        icon: 'none'
      })
    }
  })
}

// 查看报告详情
const viewReportDetail = (report: any) => {
  uni.showModal({
    title: '上报详情',
    content: `机器：${report.machineName}\n时间：${report.reportTime}\n状态：${getStatusInfo(report.status).text}\n描述：${report.description}`,
    showCancel: false
  })
}

// 退出登录
const handleLogout = () => {
  uni.showModal({
    title: '确认退出',
    content: '确定要退出登录吗？',
    success: (res) => {
      if (res.confirm) {
        uni.removeStorageSync('userInfo')
        uni.reLaunch({
          url: '/pages/login/login'
        })
      }
    }
  })
}

// 处理快捷功能点击
const handleQuickAction = (action: any) => {
  if (action.path) {
    uni.navigateTo({
      url: action.path
    })
  } else if (action.action === 'scan') {
    handleScanReport()
  } else {
    uni.showToast({
      title: '功能开发中',
      icon: 'none'
    })
  }
}

onMounted(() => {
  getUserInfo()
  getStats()
})
</script>

<template>
  <view class="workspace-container">
    <!-- 欢迎信息 -->
    <view class="welcome-section">
      <view class="welcome-content">
        <view class="welcome-info">
          <text class="welcome-text">你好，{{ userInfo.username }}</text>
          <text class="role-text">{{ userInfo.roleText }}工作台</text>
        </view>
        <view class="logout-btn" @click="handleLogout">
          <text class="logout-text">退出</text>
        </view>
      </view>
    </view>

    <!-- 统计卡片 -->
    <view class="stats-section">
      <view class="stats-card">
        <view class="stat-item">
          <text class="stat-number">{{ stats.todayTasks }}</text>
          <text class="stat-label">今日任务</text>
        </view>
        <view class="stat-item">
          <text class="stat-number">{{ stats.completedTasks }}</text>
          <text class="stat-label">已完成</text>
        </view>
        <view class="stat-item">
          <text class="stat-number">{{ stats.pendingTasks }}</text>
          <text class="stat-label">待处理</text>
        </view>
        <view class="stat-item">
          <text class="stat-number">{{ stats.totalTasks }}</text>
          <text class="stat-label">总任务</text>
        </view>
      </view>
    </view>

    <!-- 快捷功能 -->
    <view class="quick-actions">
      <view class="section-title">快捷功能</view>
      <view class="actions-grid">
        <view 
          class="action-item" 
          v-for="(action, index) in quickActions" 
          :key="index"
          @click="handleQuickAction(action)"
        >
          <view class="action-icon">{{ action.icon }}</view>
          <text class="action-text">{{ action.text }}</text>
        </view>
      </view>
    </view>

    <!-- 织工历史记录 -->
    <view class="recent-section" v-if="userInfo.role === 'worker'">
      <view class="section-header">
        <view class="section-title">最近上报</view>
        <text class="view-all">查看全部</text>
      </view>
      
      <view class="history-list">
        <view 
          class="history-item" 
          v-for="item in reportHistory" 
          :key="item.id"
          @click="viewReportDetail(item)"
        >
          <view class="machine-info">
            <text class="machine-name">{{ item.machineName }}</text>
            <text class="machine-code">{{ item.machineCode }}</text>
          </view>
          <view class="report-info">
            <text class="report-time">{{ item.reportTime }}</text>
            <view 
              class="status-tag" 
              :style="{ backgroundColor: getStatusInfo(item.status).color }"
            >
              <text class="status-text">{{ getStatusInfo(item.status).text }}</text>
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 机修工最近活动 -->
    <view class="recent-section" v-else>
      <view class="section-title">最近活动</view>
      <view class="activity-list">
        <view class="activity-item">
          <view class="activity-icon">🔧</view>
          <view class="activity-content">
            <text class="activity-title">设备维修完成</text>
            <text class="activity-time">2小时前</text>
          </view>
        </view>
        
        <view class="activity-item">
          <view class="activity-icon">📱</view>
          <view class="activity-content">
            <text class="activity-title">异常上报</text>
            <text class="activity-time">4小时前</text>
          </view>
        </view>
        
        <view class="activity-item">
          <view class="activity-icon">✅</view>
          <view class="activity-content">
            <text class="activity-title">任务完成</text>
            <text class="activity-time">昨天</text>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<style lang="scss">
.workspace-container {
  min-height: 100vh;
  background: #f5f5f5;
  padding: 20rpx;
}

.welcome-section {
  background: linear-gradient(135deg, #2196F3 0%, #1976D2 100%);
  border-radius: 20rpx;
  padding: 40rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 8rpx 25rpx rgba(33, 150, 243, 0.3);
}

.welcome-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.welcome-info {
  display: flex;
  flex-direction: column;
}

.welcome-text {
  display: block;
  font-size: 36rpx;
  font-weight: bold;
  color: #ffffff;
  margin-bottom: 10rpx;
}

.role-text {
  display: block;
  font-size: 28rpx;
  color: rgba(255, 255, 255, 0.8);
}

.logout-btn {
  padding: 16rpx 24rpx;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 20rpx;
  
  &:active {
    background: rgba(255, 255, 255, 0.3);
  }
}

.logout-text {
  font-size: 24rpx;
  color: #ffffff;
  font-weight: 500;
}

.stats-section {
  margin-bottom: 30rpx;
}

.stats-card {
  background: #ffffff;
  border-radius: 20rpx;
  padding: 30rpx;
  display: flex;
  justify-content: space-between;
  box-shadow: 0 4rpx 15rpx rgba(0, 0, 0, 0.1);
}

.stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  flex: 1;
}

.stat-number {
  font-size: 48rpx;
  font-weight: bold;
  color: #2196F3;
  margin-bottom: 10rpx;
}

.stat-label {
  font-size: 24rpx;
  color: #666666;
}

.quick-actions {
  margin-bottom: 30rpx;
}

.section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333333;
  margin-bottom: 20rpx;
  padding: 0 10rpx;
}

.actions-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 20rpx;
}

.action-item {
  background: #ffffff;
  border-radius: 16rpx;
  padding: 30rpx 20rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  box-shadow: 0 4rpx 15rpx rgba(0, 0, 0, 0.1);
  
  &:active {
    transform: scale(0.95);
    background: #f8f8f8;
  }
}

.action-icon {
  font-size: 48rpx;
  margin-bottom: 15rpx;
}

.action-text {
  font-size: 24rpx;
  color: #333333;
  text-align: center;
}

.recent-section {
  margin-bottom: 30rpx;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
  padding: 0 10rpx;
}

.view-all {
  font-size: 24rpx;
  color: #2196F3;
}

.history-list {
  background: #ffffff;
  border-radius: 20rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 15rpx rgba(0, 0, 0, 0.1);
}

.history-item {
  padding: 30rpx;
  border-bottom: 1rpx solid #f0f0f0;
  display: flex;
  justify-content: space-between;
  align-items: center;
  
  &:last-child {
    border-bottom: none;
  }
  
  &:active {
    background: #f8f8f8;
  }
}

.machine-info {
  display: flex;
  flex-direction: column;
}

.machine-name {
  font-size: 28rpx;
  font-weight: 500;
  color: #333333;
  margin-bottom: 8rpx;
}

.machine-code {
  font-size: 24rpx;
  color: #666666;
}

.report-info {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
}

.report-time {
  font-size: 24rpx;
  color: #666666;
  margin-bottom: 8rpx;
}

.status-tag {
  padding: 8rpx 16rpx;
  border-radius: 12rpx;
}

.status-text {
  font-size: 20rpx;
  color: #ffffff;
  font-weight: 500;
}

.activity-list {
  background: #ffffff;
  border-radius: 20rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 15rpx rgba(0, 0, 0, 0.1);
}

.activity-item {
  display: flex;
  align-items: center;
  padding: 30rpx;
  border-bottom: 1rpx solid #f0f0f0;
  
  &:last-child {
    border-bottom: none;
  }
}

.activity-icon {
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 32rpx;
  margin-right: 30rpx;
}

.activity-content {
  flex: 1;
}

.activity-title {
  display: block;
  font-size: 28rpx;
  color: #333333;
  margin-bottom: 8rpx;
}

.activity-time {
  display: block;
  font-size: 24rpx;
  color: #999999;
}
</style>