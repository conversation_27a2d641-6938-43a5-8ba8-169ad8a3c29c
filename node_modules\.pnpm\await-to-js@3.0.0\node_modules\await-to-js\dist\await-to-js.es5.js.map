{"version": 3, "file": "await-to-js.es5.js", "sources": ["../src/await-to-js.ts"], "sourcesContent": ["/**\n * @param { Promise } promise\n * @param { Object= } errorExt - Additional Information you can pass to the err object\n * @return { Promise }\n */\nexport function to<T, U = Error> (\n  promise: Promise<T>,\n  errorExt?: object\n): Promise<[U, undefined] | [null, T]> {\n  return promise\n    .then<[null, T]>((data: T) => [null, data])\n    .catch<[U, undefined]>((err: U) => {\n      if (errorExt) {\n        Object.assign(err, errorExt);\n      }\n\n      return [err, undefined];\n    });\n}\n\nexport default to;\n"], "names": [], "mappings": "AAAA;;;;;AAKA,YACE,OAAmB,EACnB,QAAiB;IAEjB,OAAO,OAAO;SACX,IAAI,CAAY,UAAC,IAAO,IAAK,OAAA,CAAC,IAAI,EAAE,IAAI,CAAC,GAAA,CAAC;SAC1C,KAAK,CAAiB,UAAC,GAAM;QAC5B,IAAI,QAAQ,EAAE;YACZ,MAAM,CAAC,MAAM,CAAC,GAAG,EAAE,QAAQ,CAAC,CAAC;SAC9B;QAED,OAAO,CAAC,GAAG,EAAE,SAAS,CAAC,CAAC;KACzB,CAAC,CAAC;CACN;;;;;"}