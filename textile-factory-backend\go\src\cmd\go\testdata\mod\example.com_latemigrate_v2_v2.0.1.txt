example.com/latemigrate/v2 v2.0.1
written by hand

This repository migrated to modules in v2.0.1 after v2.0.0 was already tagged.
All versions require rsc.io/quote so we can test downgrades.

v2.0.1 belongs to example.com/latemigrate/v2.

-- .mod --
module example.com/latemigrate/v2

require rsc.io/quote v1.3.0
-- .info --
{"Version":"v2.0.1"}
-- go.mod --
module example.com/latemigrate/v2

require rsc.io/quote v1.3.0
-- late.go --
package late
