This is a list of possible improvements to the SSA pass of the compiler.

Optimizations (better compiled code)
------------------------------------
- Reduce register pressure in scheduler
- Make dead store pass inter-block
- If there are a lot of MOVQ $0, ..., then load
   0 into a register and use the register as the source instead.
- Allow large structs to be SSAable (issue 24416)
- Allow arrays of length >1 to be SSAable
- If strings are being passed around without being interpreted (ptr
  and len fields being accessed) pass them in xmm registers?
  Same for interfaces?
- any pointer generated by unsafe arithmetic must be non-nil?
  (Of course that may not be true in general, but it is for all uses
   in the runtime, and we can play games with unsafe.)

Optimizations (better compiler)
-------------------------------
- <PERSON><PERSON> signed division overflow and sign extension earlier

Regalloc
--------
- Make liveness analysis non-quadratic
