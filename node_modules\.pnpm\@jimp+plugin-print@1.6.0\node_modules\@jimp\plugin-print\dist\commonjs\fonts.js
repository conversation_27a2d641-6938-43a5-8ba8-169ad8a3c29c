"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.SANS_128_WHITE = exports.SANS_64_WHITE = exports.SANS_32_WHITE = exports.SANS_16_WHITE = exports.SANS_8_WHITE = exports.SANS_128_BLACK = exports.SANS_64_BLACK = exports.SANS_32_BLACK = exports.SANS_16_BLACK = exports.SANS_14_BLACK = exports.SANS_12_BLACK = exports.SANS_10_BLACK = exports.SANS_8_BLACK = void 0;
const path_1 = __importDefault(require("path"));
const dirname_js_1 = require("./dirname.js");
const dir = path_1.default.join(dirname_js_1.dirname, "../");
exports.SANS_8_BLACK = path_1.default.join(dir, "fonts/open-sans/open-sans-8-black/open-sans-8-black.fnt");
exports.SANS_10_BLACK = path_1.default.join(dir, "fonts/open-sans/open-sans-10-black/open-sans-10-black.fnt");
exports.SANS_12_BLACK = path_1.default.join(dir, "fonts/open-sans/open-sans-12-black/open-sans-12-black.fnt");
exports.SANS_14_BLACK = path_1.default.join(dir, "fonts/open-sans/open-sans-14-black/open-sans-14-black.fnt");
exports.SANS_16_BLACK = path_1.default.join(dir, "fonts/open-sans/open-sans-16-black/open-sans-16-black.fnt");
exports.SANS_32_BLACK = path_1.default.join(dir, "fonts/open-sans/open-sans-32-black/open-sans-32-black.fnt");
exports.SANS_64_BLACK = path_1.default.join(dir, "fonts/open-sans/open-sans-64-black/open-sans-64-black.fnt");
exports.SANS_128_BLACK = path_1.default.join(dir, "fonts/open-sans/open-sans-128-black/open-sans-128-black.fnt");
exports.SANS_8_WHITE = path_1.default.join(dir, "fonts/open-sans/open-sans-8-white/open-sans-8-white.fnt");
exports.SANS_16_WHITE = path_1.default.join(dir, "fonts/open-sans/open-sans-16-white/open-sans-16-white.fnt");
exports.SANS_32_WHITE = path_1.default.join(dir, "fonts/open-sans/open-sans-32-white/open-sans-32-white.fnt");
exports.SANS_64_WHITE = path_1.default.join(dir, "fonts/open-sans/open-sans-64-white/open-sans-64-white.fnt");
exports.SANS_128_WHITE = path_1.default.join(dir, "fonts/open-sans/open-sans-128-white/open-sans-128-white.fnt");
//# sourceMappingURL=fonts.js.map