

> @jimp/utils@1.1.2 test /Users/<USER>/Documents/jimp/packages/utils
> vitest "--watch=false"


[7m[1m[36m RUN [39m[22m[27m [36mv2.0.5[39m [90m/Users/<USER>/Documents/jimp/packages/utils[39m

[?25l [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (3)[22m
   [32m✓[39m colorDiff[2m (3)[22m
     [32m✓[39m totally opaque (no alpha defined)
     [32m✓[39m totally transparent
     [32m✓[39m different alpha
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (3)[22m
   [32m✓[39m colorDiff[2m (3)[22m
     [32m✓[39m totally opaque (no alpha defined)
     [32m✓[39m totally transparent
     [32m✓[39m different alpha

[2m Test Files [22m [1m[32m1 passed[39m[22m[90m (1)[39m
[2m      Tests [22m [1m[32m3 passed[39m[22m[90m (3)[39m
[2m   Start at [22m 01:33:25
[2m   Duration [22m 780ms[2m (transform 280ms, setup 0ms, collect 280ms, tests 4ms, environment 0ms, prepare 106ms)[22m

[?25h[?25h
