# Test go build -pgo flag.
# Specifically, the build cache handles profile content correctly.

[short] skip 'compiles and links executables'

# build without <PERSON><PERSON><PERSON>
go build triv.go

# build with <PERSON><PERSON><PERSON>, should trigger rebuild
# starting with an empty profile (the compiler accepts it)
go build -x -pgo=prof -o triv.exe triv.go
stderr 'compile.*-pgoprofile=.*prof.*triv.go'

# check that <PERSON><PERSON><PERSON> appears in build info
# N.B. we can't start the stdout check with -pgo because the script assumes that
# if the first arg starts with - it is a grep flag.
go version -m triv.exe
stdout 'build\s+-pgo=.*'${/}'prof'

# store the build ID
go list -export -json=BuildID -pgo=prof triv.go
stdout '"BuildID":' # check that output actually contains a build ID
cp stdout list.out

# build again with the same profile, should be cached
go build -x -pgo=prof -o triv.exe triv.go
! stderr 'compile.*triv.go'

# check that the build ID is the same
go list -export -json=BuildID -pgo=prof triv.go
cmp stdout list.out

# overwrite the prof
go run overwrite.go

# build again, profile content changed, should trigger rebuild
go build -n -pgo=prof triv.go
stderr 'compile.*-pgoprofile=.*prof.*p.go'

# check that the build ID is different
go list -export -json=BuildID -pgo=prof triv.go
! cmp stdout list.out

# build with trimpath, buildinfo path should be trimmed
go build -x -pgo=prof -trimpath -o triv.exe triv.go

# check that path is trimmed
go version -m triv.exe
stdout 'build\s+-pgo=prof'

-- prof --
-- triv.go --
package main
func main() {}
-- overwrite.go --
package main

import (
	"os"
	"runtime/pprof"
)

func main() {
	f, err := os.Create("prof")
	if err != nil {
		panic(err)
	}
	err = pprof.StartCPUProfile(f)
	if err != nil {
		panic(err)
	}
	pprof.StopCPUProfile()
	f.Close()
}
