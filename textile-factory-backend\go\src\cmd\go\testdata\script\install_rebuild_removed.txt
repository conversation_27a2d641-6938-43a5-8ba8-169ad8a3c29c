env GO111MODULE=off

# go command should detect package staleness as source file set changes
go install mypkg
! stale mypkg

# z.go was not compiled; removing it should NOT make mypkg stale
rm mypkg/z.go
! stale mypkg

# y.go was compiled; removing it should make mypkg stale
rm mypkg/y.go
stale mypkg

# go command should detect executable staleness too
go install mycmd
! stale mycmd
rm mycmd/z.go
! stale mycmd
rm mycmd/y.go
stale mycmd

-- mypkg/x.go --
package mypkg

-- mypkg/y.go --
package mypkg

-- mypkg/z.go --
// +build missingtag

package mypkg

-- mycmd/x.go --
package main
func main() {}

-- mycmd/y.go --
package main

-- mycmd/z.go --
// +build missingtag

package main
