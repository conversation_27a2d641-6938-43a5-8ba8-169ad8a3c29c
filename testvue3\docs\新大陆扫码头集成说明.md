# 新大陆扫码头集成说明

## 概述

本项目已集成新大陆二维扫码头功能，支持安卓PDA设备的扫码功能。当在非安卓平台或扫码头不可用时，会自动降级到uni-app的`uni.scanCode`功能。

## 功能特性

- ✅ 支持新大陆扫码头硬件扫码
- ✅ 自动降级到系统扫码功能
- ✅ 页面生命周期管理
- ✅ 广播接收器自动注册/注销
- ✅ 多页面扫码状态管理

## 技术实现

### 核心文件

1. **`src/utils/scannerUtils.js`** - 扫码工具类
   - 处理新大陆扫码头的广播配置
   - 管理广播接收器的注册/注销
   - 提供降级方案

2. **`src/utils/scannerMixin.js`** - 页面生命周期混入
   - 统一管理页面显示/隐藏时的扫码状态

### 集成页面

- **机修工首页** (`src/pages/mechanic/home.vue`)
- **织工首页** (`src/pages/worker/home.vue`)

## 使用方法

### 基本用法

```javascript
import scannerUtils from '@/utils/scannerUtils.js'

// 初始化扫码功能
scannerUtils.initScanner((result) => {
  if (result.success) {
    console.log('扫码结果:', result.result)
    // 处理扫码结果
  } else {
    console.error('扫码失败:', result.error)
  }
}, true) // true表示页面当前活跃
```

### 页面生命周期管理

```javascript
// 在页面的onShow中激活扫码
onShow() {
  scannerUtils.setPageActive(true)
}

// 在页面的onHide中停用扫码
onHide() {
  scannerUtils.setPageActive(false)
}

// 在页面的onUnload中清理资源
onUnload() {
  scannerUtils.destroy()
}
```

## 配置说明

### 广播配置

扫码头使用以下广播配置：

```javascript
const intent = new Intent("com.android.scanner.service_settings")
intent.putExtra("action_barcode_broadcast", "com.android.server.scannerservice.broadcast")
intent.putExtra("key_barcode_broadcast", "scannerdata")
```

### 广播接收

监听广播Action：`com.android.server.scannerservice.broadcast`
数据键名：`scannerdata`

## 设备兼容性

### 支持的设备
- 新大陆安卓PDA设备
- 其他支持相同广播协议的设备

### 降级支持
- iOS设备：自动使用`uni.scanCode`
- 不支持广播的安卓设备：自动使用`uni.scanCode`
- 扫码头初始化失败：自动使用`uni.scanCode`

## 调试信息

扫码功能会输出详细的调试信息：

```
扫码枪广播配置完成
扫码广播接收器注册成功
页面显示，激活扫码功能
扫码结果: QR_CODE_CONTENT
页面隐藏，停用扫码功能
页面卸载，清理扫码资源
```

## 错误处理

### 常见错误

1. **广播配置失败**
   - 原因：设备不支持或权限不足
   - 解决：自动降级到`uni.scanCode`

2. **广播接收器注册失败**
   - 原因：Android系统限制或权限问题
   - 解决：自动降级到`uni.scanCode`

3. **扫码数据解析失败**
   - 原因：广播数据格式异常
   - 解决：输出错误日志，提示用户重试

## 性能优化

1. **单例模式**：`scannerUtils`使用单例模式，避免重复初始化
2. **页面状态管理**：只在页面活跃时处理扫码结果
3. **资源清理**：页面卸载时自动清理广播接收器

## 注意事项

1. **权限要求**：确保应用有相机权限和系统广播权限
2. **设备测试**：在实际PDA设备上测试扫码功能
3. **降级测试**：在非PDA设备上测试降级功能
4. **页面切换**：确保页面切换时正确管理扫码状态

## 参考文档

- [新大陆扫码头开发文档](https://developer.sunmi.com/docs/zh-CN/xeghjk491/cifeghjk535)
- [扫码头开发及用户文档](https://sunmi-ota.oss-cn-hangzhou.aliyuncs.com/DOC/resource/re_cn/%E6%89%AB%E7%A0%81%E5%A4%B4/%E6%89%AB%E7%A0%81%E5%A4%B4%E5%BC%80%E5%8F%91%E5%8F%8A%E7%94%A8%E6%88%B7%E6%96%87%E6%A1%A3.pdf)
