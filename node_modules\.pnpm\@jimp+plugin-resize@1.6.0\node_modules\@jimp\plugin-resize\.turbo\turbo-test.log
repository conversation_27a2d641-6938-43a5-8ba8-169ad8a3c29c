

> @jimp/plugin-resize@1.1.2 test /Users/<USER>/Documents/jimp/plugins/plugin-resize
> vitest "--watch=false"


[7m[1m[36m RUN [39m[22m[27m [36mv2.0.5[39m [90m/Users/<USER>/Documents/jimp/plugins/plugin-resize[39m

[?25l       [90m·[39m to BEZIER 2x5
     [90m·[39m max contrast 12x12 with dots[2m (6)[22m
       [90m·[39m to default 6x6
       [90m·[39m to NEAREST_NEIGHBOR 6x6
       [90m·[39m to BILINEAR 6x6
       [90m·[39m to BICUBIC 6x6
       [90m·[39m to HERMITE 6x6
       [90m·[39m to BEZIER 6x6
     [90m·[39m mutch contrast 4x4[2m (6)[22m
       [90m·[39m to default 6x6
       [90m·[39m to NEAREST_NEIGHBOR 6x6
       [90m·[39m to BILINEAR 6x6
       [90m·[39m to BICUBIC 6x6
       [90m·[39m to HERMITE 6x6
       [90m·[39m to BEZIER 6x6
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G       [32m✓[39m to BEZIER 2x5
     [32m✓[39m max contrast 12x12 with dots[2m (6)[22m
       [32m✓[39m to default 6x6
       [32m✓[39m to NEAREST_NEIGHBOR 6x6
       [32m✓[39m to BILINEAR 6x6
       [32m✓[39m to BICUBIC 6x6
       [32m✓[39m to HERMITE 6x6
       [32m✓[39m to BEZIER 6x6
     [32m✓[39m mutch contrast 4x4[2m (6)[22m
       [32m✓[39m to default 6x6
       [32m✓[39m to NEAREST_NEIGHBOR 6x6
       [32m✓[39m to BILINEAR 6x6
       [32m✓[39m to BICUBIC 6x6
       [32m✓[39m to HERMITE 6x6
       [32m✓[39m to BEZIER 6x6
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (24)[22m
   [32m✓[39m Resize images[2m (24)[22m
     [32m✓[39m max contrast 8x8[2m (12)[22m
       [32m✓[39m to default 4x4
       [32m✓[39m to NEAREST_NEIGHBOR 4x4
       [32m✓[39m to BILINEAR 4x4
       [32m✓[39m to BICUBIC 4x4
       [32m✓[39m to HERMITE 4x4
       [32m✓[39m to BEZIER 4x4
       [32m✓[39m to default 2x5
       [32m✓[39m to NEAREST_NEIGHBOR 2x5
       [32m✓[39m to BILINEAR 2x5
       [32m✓[39m to BICUBIC 2x5
       [32m✓[39m to HERMITE 2x5
       [32m✓[39m to BEZIER 2x5
     [32m✓[39m max contrast 12x12 with dots[2m (6)[22m
       [32m✓[39m to default 6x6
       [32m✓[39m to NEAREST_NEIGHBOR 6x6
       [32m✓[39m to BILINEAR 6x6
       [32m✓[39m to BICUBIC 6x6
       [32m✓[39m to HERMITE 6x6
       [32m✓[39m to BEZIER 6x6
     [32m✓[39m mutch contrast 4x4[2m (6)[22m
       [32m✓[39m to default 6x6
       [32m✓[39m to NEAREST_NEIGHBOR 6x6
       [32m✓[39m to BILINEAR 6x6
       [32m✓[39m to BICUBIC 6x6
       [32m✓[39m to HERMITE 6x6
       [32m✓[39m to BEZIER 6x6

[2m Test Files [22m [1m[32m1 passed[39m[22m[90m (1)[39m
[2m      Tests [22m [1m[32m24 passed[39m[22m[90m (24)[39m
[2m   Start at [22m 01:33:45
[2m   Duration [22m 2.28s[2m (transform 518ms, setup 0ms, collect 842ms, tests 14ms, environment 1ms, prepare 531ms)[22m

[?25h[?25h
