# Issue 14450: go get -u .../ tried to import not downloaded package

[!net:github.com] skip
[!git] skip
env GO111MODULE=off

go get github.com/tmwh/go-get-issue-14450/a
! go get -u .../
stderr 'cannot find package.*d-dependency/e'

# Even though get -u failed, the source for others should be downloaded.
exists github.com/tmwh/go-get-issue-14450/b
exists github.com/tmwh/go-get-issue-14450-b-dependency/c
exists github.com/tmwh/go-get-issue-14450-b-dependency/d

! exists github.com/tmwh/go-get-issue-14450-c-dependency/e
