// Code generated by avx512test. DO NOT EDIT.

#include "../../../../../../runtime/textflag.h"

TEXT asmtest_avx512f(SB), NOSPLIT, $0
	KANDNW K4, K4, K6                                  // c5dc42f4
	KANDNW K5, K4, K6                                  // c5dc42f5
	KANDNW K4, K6, K6                                  // c5cc42f4
	KANDNW K5, K6, K6                                  // c5cc42f5
	KANDNW K4, K4, K4                                  // c5dc42e4
	KANDNW K5, K4, K4                                  // c5dc42e5
	KANDNW K4, K6, K4                                  // c5cc42e4
	KANDNW K5, K6, K4                                  // c5cc42e5
	KANDW K5, K3, K1                                   // c5e441cd
	KANDW K4, K3, K1                                   // c5e441cc
	KANDW K5, K1, K1                                   // c5f441cd
	KANDW K4, K1, K1                                   // c5f441cc
	KANDW K5, K3, K5                                   // c5e441ed
	KANDW K4, K3, K5                                   // c5e441ec
	KANDW K5, K1, K5                                   // c5f441ed
	KANDW K4, K1, K5                                   // c5f441ec
	KMOVW K5, 17(SP)                                   // c5f8916c2411
	KMOVW K4, 17(SP)                                   // c5f891642411
	KMOVW K5, -17(BP)(SI*4)                            // c5f8916cb5ef
	KMOVW K4, -17(BP)(SI*4)                            // c5f89164b5ef
	KMOVW K7, SP                                       // c5f893e7
	KMOVW K6, SP                                       // c5f893e6
	KMOVW K7, R14                                      // c57893f7
	KMOVW K6, R14                                      // c57893f6
	KMOVW K0, K4                                       // c5f890e0
	KMOVW K7, K4                                       // c5f890e7
	KMOVW 7(AX), K4                                    // c5f8906007
	KMOVW (DI), K4                                     // c5f89027
	KMOVW K0, K6                                       // c5f890f0
	KMOVW K7, K6                                       // c5f890f7
	KMOVW 7(AX), K6                                    // c5f8907007
	KMOVW (DI), K6                                     // c5f89037
	KMOVW AX, K5                                       // c5f892e8
	KMOVW R9, K5                                       // c4c17892e9
	KMOVW AX, K4                                       // c5f892e0
	KMOVW R9, K4                                       // c4c17892e1
	KNOTW K0, K2                                       // c5f844d0
	KNOTW K5, K2                                       // c5f844d5
	KNOTW K0, K7                                       // c5f844f8
	KNOTW K5, K7                                       // c5f844fd
	KORTESTW K6, K0                                    // c5f898c6
	KORTESTW K5, K0                                    // c5f898c5
	KORTESTW K6, K5                                    // c5f898ee
	KORTESTW K5, K5                                    // c5f898ed
	KORW K5, K3, K1                                    // c5e445cd
	KORW K4, K3, K1                                    // c5e445cc
	KORW K5, K1, K1                                    // c5f445cd
	KORW K4, K1, K1                                    // c5f445cc
	KORW K5, K3, K5                                    // c5e445ed
	KORW K4, K3, K5                                    // c5e445ec
	KORW K5, K1, K5                                    // c5f445ed
	KORW K4, K1, K5                                    // c5f445ec
	KSHIFTLW $81, K6, K6                               // c4e3f932f651
	KSHIFTLW $81, K4, K6                               // c4e3f932f451
	KSHIFTLW $81, K6, K7                               // c4e3f932fe51
	KSHIFTLW $81, K4, K7                               // c4e3f932fc51
	KSHIFTRW $27, K5, K3                               // c4e3f930dd1b
	KSHIFTRW $27, K4, K3                               // c4e3f930dc1b
	KSHIFTRW $27, K5, K1                               // c4e3f930cd1b
	KSHIFTRW $27, K4, K1                               // c4e3f930cc1b
	KUNPCKBW K2, K4, K4                                // c5dd4be2
	KUNPCKBW K7, K4, K4                                // c5dd4be7
	KUNPCKBW K2, K5, K4                                // c5d54be2
	KUNPCKBW K7, K5, K4                                // c5d54be7
	KUNPCKBW K2, K4, K6                                // c5dd4bf2
	KUNPCKBW K7, K4, K6                                // c5dd4bf7
	KUNPCKBW K2, K5, K6                                // c5d54bf2
	KUNPCKBW K7, K5, K6                                // c5d54bf7
	KXNORW K6, K0, K2                                  // c5fc46d6
	KXNORW K5, K0, K2                                  // c5fc46d5
	KXNORW K6, K5, K2                                  // c5d446d6
	KXNORW K5, K5, K2                                  // c5d446d5
	KXNORW K6, K0, K7                                  // c5fc46fe
	KXNORW K5, K0, K7                                  // c5fc46fd
	KXNORW K6, K5, K7                                  // c5d446fe
	KXNORW K5, K5, K7                                  // c5d446fd
	KXORW K4, K6, K6                                   // c5cc47f4
	KXORW K6, K6, K6                                   // c5cc47f6
	KXORW K4, K4, K6                                   // c5dc47f4
	KXORW K6, K4, K6                                   // c5dc47f6
	KXORW K4, K6, K7                                   // c5cc47fc
	KXORW K6, K6, K7                                   // c5cc47fe
	KXORW K4, K4, K7                                   // c5dc47fc
	KXORW K6, K4, K7                                   // c5dc47fe
	VADDPD X15, X11, K2, X3                            // 62d1a50a58df
	VADDPD 7(SI)(DI*8), X11, K2, X3                    // 62f1a50a589cfe07000000
	VADDPD -15(R14), X11, K2, X3                       // 62d1a50a589ef1ffffff
	VADDPD Y25, Y31, K2, Y14                           // 6211852258f1
	VADDPD 17(SP), Y31, K2, Y14                        // 6271852258b42411000000
	VADDPD -17(BP)(SI*4), Y31, K2, Y14                 // 6271852258b4b5efffffff
	VADDPD Z13, Z11, K3, Z14                           // 6251a54b58f5
	VADDPD Z14, Z11, K3, Z14                           // 6251a54b58f6
	VADDPD Z13, Z5, K3, Z14                            // 6251d54b58f5
	VADDPD Z14, Z5, K3, Z14                            // 6251d54b58f6
	VADDPD Z13, Z11, K3, Z27                           // 6241a54b58dd
	VADDPD Z14, Z11, K3, Z27                           // 6241a54b58de
	VADDPD Z13, Z5, K3, Z27                            // 6241d54b58dd
	VADDPD Z14, Z5, K3, Z27                            // 6241d54b58de
	VADDPD Z6, Z2, K3, Z5                              // 62f1ed4b58ee
	VADDPD Z14, Z2, K3, Z5                             // 62d1ed4b58ee
	VADDPD 17(SP), Z2, K3, Z5                          // 62f1ed4b58ac2411000000
	VADDPD -17(BP)(SI*4), Z2, K3, Z5                   // 62f1ed4b58acb5efffffff
	VADDPD Z6, Z2, K3, Z23                             // 62e1ed4b58fe
	VADDPD Z14, Z2, K3, Z23                            // 62c1ed4b58fe
	VADDPD 17(SP), Z2, K3, Z23                         // 62e1ed4b58bc2411000000
	VADDPD -17(BP)(SI*4), Z2, K3, Z23                  // 62e1ed4b58bcb5efffffff
	VADDPS X6, X13, K3, X30                            // 6261140b58f6
	VADDPS 7(SI)(DI*1), X13, K3, X30                   // 6261140b58b43e07000000
	VADDPS 15(DX)(BX*8), X13, K3, X30                  // 6261140b58b4da0f000000
	VADDPS Y27, Y22, K2, Y2                            // 62914c2258d3
	VADDPS 7(AX), Y22, K2, Y2                          // 62f14c22589007000000
	VADDPS (DI), Y22, K2, Y2                           // 62f14c225817
	VADDPS Z13, Z28, K1, Z26                           // 62411c4158d5
	VADDPS Z21, Z28, K1, Z26                           // 62211c4158d5
	VADDPS Z13, Z6, K1, Z26                            // 62414c4958d5
	VADDPS Z21, Z6, K1, Z26                            // 62214c4958d5
	VADDPS Z13, Z28, K1, Z14                           // 62511c4158f5
	VADDPS Z21, Z28, K1, Z14                           // 62311c4158f5
	VADDPS Z13, Z6, K1, Z14                            // 62514c4958f5
	VADDPS Z21, Z6, K1, Z14                            // 62314c4958f5
	VADDPS Z21, Z3, K2, Z26                            // 6221644a58d5
	VADDPS Z13, Z3, K2, Z26                            // 6241644a58d5
	VADDPS 7(AX), Z3, K2, Z26                          // 6261644a589007000000
	VADDPS (DI), Z3, K2, Z26                           // 6261644a5817
	VADDPS Z21, Z0, K2, Z26                            // 62217c4a58d5
	VADDPS Z13, Z0, K2, Z26                            // 62417c4a58d5
	VADDPS 7(AX), Z0, K2, Z26                          // 62617c4a589007000000
	VADDPS (DI), Z0, K2, Z26                           // 62617c4a5817
	VADDPS Z21, Z3, K2, Z3                             // 62b1644a58dd
	VADDPS Z13, Z3, K2, Z3                             // 62d1644a58dd
	VADDPS 7(AX), Z3, K2, Z3                           // 62f1644a589807000000
	VADDPS (DI), Z3, K2, Z3                            // 62f1644a581f
	VADDPS Z21, Z0, K2, Z3                             // 62b17c4a58dd
	VADDPS Z13, Z0, K2, Z3                             // 62d17c4a58dd
	VADDPS 7(AX), Z0, K2, Z3                           // 62f17c4a589807000000
	VADDPS (DI), Z0, K2, Z3                            // 62f17c4a581f
	VADDSD X30, X23, K1, X12                           // 6211c70158e6
	VADDSD X2, X20, K7, X8                             // 6271df0758c2 or 6271df2758c2 or 6271df4758c2
	VADDSD 99(R15)(R15*1), X20, K7, X8                 // 6211df0758843f63000000 or 6211df2758843f63000000 or 6211df4758843f63000000
	VADDSD (DX), X20, K7, X8                           // 6271df075802 or 6271df275802 or 6271df475802
	VADDSS X19, X26, K1, X9                            // 62312e0158cb
	VADDSS X16, X31, K1, X0                            // 62b1060158c0 or 62b1062158c0 or 62b1064158c0
	VADDSS 99(R15)(R15*1), X31, K1, X0                 // 6291060158843f63000000 or 6291062158843f63000000 or 6291064158843f63000000
	VADDSS (DX), X31, K1, X0                           // 62f106015802 or 62f106215802 or 62f106415802
	VALIGND $47, X16, X7, K1, X19                      // 62a3450903d82f
	VALIGND $47, (BX), X7, K1, X19                     // 62e34509031b2f
	VALIGND $47, -17(BP)(SI*1), X7, K1, X19            // 62e34509039c35efffffff2f
	VALIGND $82, Y23, Y9, K7, Y22                      // 62a3352f03f752
	VALIGND $82, -7(DI)(R8*1), Y9, K7, Y22             // 62a3352f03b407f9ffffff52
	VALIGND $82, (SP), Y9, K7, Y22                     // 62e3352f03342452
	VALIGND $126, Z6, Z9, K2, Z12                      // 6273354a03e67e
	VALIGND $126, Z25, Z9, K2, Z12                     // 6213354a03e17e
	VALIGND $126, -7(DI)(R8*1), Z9, K2, Z12            // 6233354a03a407f9ffffff7e
	VALIGND $126, (SP), Z9, K2, Z12                    // 6273354a0324247e
	VALIGND $126, Z6, Z12, K2, Z12                     // 62731d4a03e67e
	VALIGND $126, Z25, Z12, K2, Z12                    // 62131d4a03e17e
	VALIGND $126, -7(DI)(R8*1), Z12, K2, Z12           // 62331d4a03a407f9ffffff7e
	VALIGND $126, (SP), Z12, K2, Z12                   // 62731d4a0324247e
	VALIGND $126, Z6, Z9, K2, Z17                      // 62e3354a03ce7e
	VALIGND $126, Z25, Z9, K2, Z17                     // 6283354a03c97e
	VALIGND $126, -7(DI)(R8*1), Z9, K2, Z17            // 62a3354a038c07f9ffffff7e
	VALIGND $126, (SP), Z9, K2, Z17                    // 62e3354a030c247e
	VALIGND $126, Z6, Z12, K2, Z17                     // 62e31d4a03ce7e
	VALIGND $126, Z25, Z12, K2, Z17                    // 62831d4a03c97e
	VALIGND $126, -7(DI)(R8*1), Z12, K2, Z17           // 62a31d4a038c07f9ffffff7e
	VALIGND $126, (SP), Z12, K2, Z17                   // 62e31d4a030c247e
	VALIGNQ $94, X7, X1, K4, X31                       // 6263f50c03ff5e
	VALIGNQ $94, 15(R8)(R14*4), X1, K4, X31            // 6203f50c03bcb00f0000005e
	VALIGNQ $94, -7(CX)(DX*4), X1, K4, X31             // 6263f50c03bc91f9ffffff5e
	VALIGNQ $121, Y0, Y5, K1, Y31                      // 6263d52903f879
	VALIGNQ $121, -7(CX), Y5, K1, Y31                  // 6263d52903b9f9ffffff79
	VALIGNQ $121, 15(DX)(BX*4), Y5, K1, Y31            // 6263d52903bc9a0f00000079
	VALIGNQ $13, Z3, Z8, K3, Z3                        // 62f3bd4b03db0d
	VALIGNQ $13, Z27, Z8, K3, Z3                       // 6293bd4b03db0d
	VALIGNQ $13, -7(CX), Z8, K3, Z3                    // 62f3bd4b0399f9ffffff0d
	VALIGNQ $13, 15(DX)(BX*4), Z8, K3, Z3              // 62f3bd4b039c9a0f0000000d
	VALIGNQ $13, Z3, Z2, K3, Z3                        // 62f3ed4b03db0d
	VALIGNQ $13, Z27, Z2, K3, Z3                       // 6293ed4b03db0d
	VALIGNQ $13, -7(CX), Z2, K3, Z3                    // 62f3ed4b0399f9ffffff0d
	VALIGNQ $13, 15(DX)(BX*4), Z2, K3, Z3              // 62f3ed4b039c9a0f0000000d
	VALIGNQ $13, Z3, Z8, K3, Z21                       // 62e3bd4b03eb0d
	VALIGNQ $13, Z27, Z8, K3, Z21                      // 6283bd4b03eb0d
	VALIGNQ $13, -7(CX), Z8, K3, Z21                   // 62e3bd4b03a9f9ffffff0d
	VALIGNQ $13, 15(DX)(BX*4), Z8, K3, Z21             // 62e3bd4b03ac9a0f0000000d
	VALIGNQ $13, Z3, Z2, K3, Z21                       // 62e3ed4b03eb0d
	VALIGNQ $13, Z27, Z2, K3, Z21                      // 6283ed4b03eb0d
	VALIGNQ $13, -7(CX), Z2, K3, Z21                   // 62e3ed4b03a9f9ffffff0d
	VALIGNQ $13, 15(DX)(BX*4), Z2, K3, Z21             // 62e3ed4b03ac9a0f0000000d
	VBLENDMPD X28, X13, K3, X23                        // 6282950b65fc
	VBLENDMPD (R14), X13, K3, X23                      // 62c2950b653e
	VBLENDMPD -7(DI)(R8*8), X13, K3, X23               // 62a2950b65bcc7f9ffffff
	VBLENDMPD Y27, Y13, K4, Y2                         // 6292952c65d3
	VBLENDMPD (R8), Y13, K4, Y2                        // 62d2952c6510
	VBLENDMPD 15(DX)(BX*2), Y13, K4, Y2                // 62f2952c65945a0f000000
	VBLENDMPD Z18, Z13, K2, Z1                         // 62b2954a65ca
	VBLENDMPD Z8, Z13, K2, Z1                          // 62d2954a65c8
	VBLENDMPD (R8), Z13, K2, Z1                        // 62d2954a6508
	VBLENDMPD 15(DX)(BX*2), Z13, K2, Z1                // 62f2954a658c5a0f000000
	VBLENDMPD Z18, Z13, K2, Z15                        // 6232954a65fa
	VBLENDMPD Z8, Z13, K2, Z15                         // 6252954a65f8
	VBLENDMPD (R8), Z13, K2, Z15                       // 6252954a6538
	VBLENDMPD 15(DX)(BX*2), Z13, K2, Z15               // 6272954a65bc5a0f000000
	VBLENDMPS X15, X9, K2, X24                         // 6242350a65c7
	VBLENDMPS 99(R15)(R15*4), X9, K2, X24              // 6202350a6584bf63000000
	VBLENDMPS 15(DX), X9, K2, X24                      // 6262350a65820f000000
	VBLENDMPS Y20, Y22, K3, Y15                        // 62324d2365fc
	VBLENDMPS 17(SP)(BP*1), Y22, K3, Y15               // 62724d2365bc2c11000000
	VBLENDMPS -7(CX)(DX*8), Y22, K3, Y15               // 62724d2365bcd1f9ffffff
	VBLENDMPS Z20, Z2, K3, Z22                         // 62a26d4b65f4
	VBLENDMPS Z9, Z2, K3, Z22                          // 62c26d4b65f1
	VBLENDMPS 17(SP)(BP*1), Z2, K3, Z22                // 62e26d4b65b42c11000000
	VBLENDMPS -7(CX)(DX*8), Z2, K3, Z22                // 62e26d4b65b4d1f9ffffff
	VBLENDMPS Z20, Z31, K3, Z22                        // 62a2054365f4
	VBLENDMPS Z9, Z31, K3, Z22                         // 62c2054365f1
	VBLENDMPS 17(SP)(BP*1), Z31, K3, Z22               // 62e2054365b42c11000000
	VBLENDMPS -7(CX)(DX*8), Z31, K3, Z22               // 62e2054365b4d1f9ffffff
	VBLENDMPS Z20, Z2, K3, Z7                          // 62b26d4b65fc
	VBLENDMPS Z9, Z2, K3, Z7                           // 62d26d4b65f9
	VBLENDMPS 17(SP)(BP*1), Z2, K3, Z7                 // 62f26d4b65bc2c11000000
	VBLENDMPS -7(CX)(DX*8), Z2, K3, Z7                 // 62f26d4b65bcd1f9ffffff
	VBLENDMPS Z20, Z31, K3, Z7                         // 62b2054365fc
	VBLENDMPS Z9, Z31, K3, Z7                          // 62d2054365f9
	VBLENDMPS 17(SP)(BP*1), Z31, K3, Z7                // 62f2054365bc2c11000000
	VBLENDMPS -7(CX)(DX*8), Z31, K3, Z7                // 62f2054365bcd1f9ffffff
	VBROADCASTF32X4 (CX), K1, Y24                      // 62627d291a01
	VBROADCASTF32X4 99(R15), K1, Y24                   // 62427d291a8763000000
	VBROADCASTF32X4 99(R15)(R15*2), K2, Z12            // 62127d4a1aa47f63000000
	VBROADCASTF32X4 -7(DI), K2, Z12                    // 62727d4a1aa7f9ffffff
	VBROADCASTF32X4 99(R15)(R15*2), K2, Z16            // 62827d4a1a847f63000000
	VBROADCASTF32X4 -7(DI), K2, Z16                    // 62e27d4a1a87f9ffffff
	VBROADCASTF64X4 15(R8)(R14*1), K1, Z3              // 6292fd491b9c300f000000
	VBROADCASTF64X4 15(R8)(R14*2), K1, Z3              // 6292fd491b9c700f000000
	VBROADCASTF64X4 15(R8)(R14*1), K1, Z12             // 6212fd491ba4300f000000
	VBROADCASTF64X4 15(R8)(R14*2), K1, Z12             // 6212fd491ba4700f000000
	VBROADCASTI32X4 -17(BP), K4, Y19                   // 62e27d2c5a9defffffff
	VBROADCASTI32X4 -15(R14)(R15*8), K4, Y19           // 62827d2c5a9cfef1ffffff
	VBROADCASTI32X4 17(SP)(BP*2), K1, Z19              // 62e27d495a9c6c11000000
	VBROADCASTI32X4 -7(DI)(R8*4), K1, Z19              // 62a27d495a9c87f9ffffff
	VBROADCASTI32X4 17(SP)(BP*2), K1, Z15              // 62727d495abc6c11000000
	VBROADCASTI32X4 -7(DI)(R8*4), K1, Z15              // 62327d495abc87f9ffffff
	VBROADCASTI64X4 99(R15)(R15*4), K7, Z14            // 6212fd4f5bb4bf63000000
	VBROADCASTI64X4 15(DX), K7, Z14                    // 6272fd4f5bb20f000000
	VBROADCASTI64X4 99(R15)(R15*4), K7, Z15            // 6212fd4f5bbcbf63000000
	VBROADCASTI64X4 15(DX), K7, Z15                    // 6272fd4f5bba0f000000
	VBROADCASTSD X3, K7, Y19                           // 62e2fd2f19db
	VBROADCASTSD 99(R15)(R15*8), K7, Y19               // 6282fd2f199cff63000000
	VBROADCASTSD 7(AX)(CX*8), K7, Y19                  // 62e2fd2f199cc807000000
	VBROADCASTSD X7, K6, Z21                           // 62e2fd4e19ef
	VBROADCASTSD (AX), K6, Z21                         // 62e2fd4e1928
	VBROADCASTSD 7(SI), K6, Z21                        // 62e2fd4e19ae07000000
	VBROADCASTSD X7, K6, Z8                            // 6272fd4e19c7
	VBROADCASTSD (AX), K6, Z8                          // 6272fd4e1900
	VBROADCASTSD 7(SI), K6, Z8                         // 6272fd4e198607000000
	VBROADCASTSS X0, K3, X0                            // 62f27d0b18c0
	VBROADCASTSS -17(BP)(SI*8), K3, X0                 // 62f27d0b1884f5efffffff
	VBROADCASTSS (R15), K3, X0                         // 62d27d0b1807
	VBROADCASTSS X24, K7, Y14                          // 62127d2f18f0
	VBROADCASTSS 7(SI)(DI*8), K7, Y14                  // 62727d2f18b4fe07000000
	VBROADCASTSS -15(R14), K7, Y14                     // 62527d2f18b6f1ffffff
	VBROADCASTSS X20, K4, Z16                          // 62a27d4c18c4
	VBROADCASTSS 7(SI)(DI*1), K4, Z16                  // 62e27d4c18843e07000000
	VBROADCASTSS 15(DX)(BX*8), K4, Z16                 // 62e27d4c1884da0f000000
	VBROADCASTSS X20, K4, Z9                           // 62327d4c18cc
	VBROADCASTSS 7(SI)(DI*1), K4, Z9                   // 62727d4c188c3e07000000
	VBROADCASTSS 15(DX)(BX*8), K4, Z9                  // 62727d4c188cda0f000000
	VCMPPD $65, X9, X7, K4, K4                         // 62d1c50cc2e141
	VCMPPD $65, -15(R14)(R15*1), X7, K4, K4            // 6291c50cc2a43ef1ffffff41
	VCMPPD $65, -15(BX), X7, K4, K4                    // 62f1c50cc2a3f1ffffff41
	VCMPPD $65, X9, X7, K4, K5                         // 62d1c50cc2e941
	VCMPPD $65, -15(R14)(R15*1), X7, K4, K5            // 6291c50cc2ac3ef1ffffff41
	VCMPPD $65, -15(BX), X7, K4, K5                    // 62f1c50cc2abf1ffffff41
	VCMPPD $67, Y5, Y21, K7, K2                        // 62f1d527c2d543
	VCMPPD $67, (CX), Y21, K7, K2                      // 62f1d527c21143
	VCMPPD $67, 99(R15), Y21, K7, K2                   // 62d1d527c2976300000043
	VCMPPD $67, Y5, Y21, K7, K7                        // 62f1d527c2fd43
	VCMPPD $67, (CX), Y21, K7, K7                      // 62f1d527c23943
	VCMPPD $67, 99(R15), Y21, K7, K7                   // 62d1d527c2bf6300000043
	VCMPPD $127, Z23, Z20, K2, K0                      // 62b1dd42c2c77f
	VCMPPD $127, Z19, Z20, K2, K0                      // 62b1dd42c2c37f
	VCMPPD $127, Z23, Z0, K2, K0                       // 62b1fd4ac2c77f
	VCMPPD $127, Z19, Z0, K2, K0                       // 62b1fd4ac2c37f
	VCMPPD $127, Z23, Z20, K2, K5                      // 62b1dd42c2ef7f
	VCMPPD $127, Z19, Z20, K2, K5                      // 62b1dd42c2eb7f
	VCMPPD $127, Z23, Z0, K2, K5                       // 62b1fd4ac2ef7f
	VCMPPD $127, Z19, Z0, K2, K5                       // 62b1fd4ac2eb7f
	VCMPPD $0, Z0, Z0, K5, K6                          // 62f1fd4dc2f000
	VCMPPD $0, Z25, Z0, K5, K6                         // 6291fd4dc2f100
	VCMPPD $0, -17(BP)(SI*2), Z0, K5, K6               // 62f1fd4dc2b475efffffff00
	VCMPPD $0, 7(AX)(CX*2), Z0, K5, K6                 // 62f1fd4dc2b4480700000000
	VCMPPD $0, Z0, Z11, K5, K6                         // 62f1a54dc2f000
	VCMPPD $0, Z25, Z11, K5, K6                        // 6291a54dc2f100
	VCMPPD $0, -17(BP)(SI*2), Z11, K5, K6              // 62f1a54dc2b475efffffff00
	VCMPPD $0, 7(AX)(CX*2), Z11, K5, K6                // 62f1a54dc2b4480700000000
	VCMPPD $0, Z0, Z0, K5, K5                          // 62f1fd4dc2e800
	VCMPPD $0, Z25, Z0, K5, K5                         // 6291fd4dc2e900
	VCMPPD $0, -17(BP)(SI*2), Z0, K5, K5               // 62f1fd4dc2ac75efffffff00
	VCMPPD $0, 7(AX)(CX*2), Z0, K5, K5                 // 62f1fd4dc2ac480700000000
	VCMPPD $0, Z0, Z11, K5, K5                         // 62f1a54dc2e800
	VCMPPD $0, Z25, Z11, K5, K5                        // 6291a54dc2e900
	VCMPPD $0, -17(BP)(SI*2), Z11, K5, K5              // 62f1a54dc2ac75efffffff00
	VCMPPD $0, 7(AX)(CX*2), Z11, K5, K5                // 62f1a54dc2ac480700000000
	VCMPPS $97, X14, X7, K3, K1                        // 62d1440bc2ce61
	VCMPPS $97, 7(AX)(CX*4), X7, K3, K1                // 62f1440bc28c880700000061
	VCMPPS $97, 7(AX)(CX*1), X7, K3, K1                // 62f1440bc28c080700000061
	VCMPPS $97, X14, X7, K3, K5                        // 62d1440bc2ee61
	VCMPPS $97, 7(AX)(CX*4), X7, K3, K5                // 62f1440bc2ac880700000061
	VCMPPS $97, 7(AX)(CX*1), X7, K3, K5                // 62f1440bc2ac080700000061
	VCMPPS $81, Y2, Y16, K4, K3                        // 62f17c24c2da51
	VCMPPS $81, 99(R15)(R15*2), Y16, K4, K3            // 62917c24c29c7f6300000051
	VCMPPS $81, -7(DI), Y16, K4, K3                    // 62f17c24c29ff9ffffff51
	VCMPPS $81, Y2, Y16, K4, K1                        // 62f17c24c2ca51
	VCMPPS $81, 99(R15)(R15*2), Y16, K4, K1            // 62917c24c28c7f6300000051
	VCMPPS $81, -7(DI), Y16, K4, K1                    // 62f17c24c28ff9ffffff51
	VCMPPS $42, Z0, Z24, K2, K5                        // 62f13c42c2e82a
	VCMPPS $42, Z26, Z24, K2, K5                       // 62913c42c2ea2a
	VCMPPS $42, Z0, Z12, K2, K5                        // 62f11c4ac2e82a
	VCMPPS $42, Z26, Z12, K2, K5                       // 62911c4ac2ea2a
	VCMPPS $42, Z0, Z24, K2, K4                        // 62f13c42c2e02a
	VCMPPS $42, Z26, Z24, K2, K4                       // 62913c42c2e22a
	VCMPPS $42, Z0, Z12, K2, K4                        // 62f11c4ac2e02a
	VCMPPS $42, Z26, Z12, K2, K4                       // 62911c4ac2e22a
	VCMPPS $79, Z9, Z9, K2, K7                         // 62d1344ac2f94f
	VCMPPS $79, Z25, Z9, K2, K7                        // 6291344ac2f94f
	VCMPPS $79, 15(R8)(R14*1), Z9, K2, K7              // 6291344ac2bc300f0000004f
	VCMPPS $79, 15(R8)(R14*2), Z9, K2, K7              // 6291344ac2bc700f0000004f
	VCMPPS $79, Z9, Z3, K2, K7                         // 62d1644ac2f94f
	VCMPPS $79, Z25, Z3, K2, K7                        // 6291644ac2f94f
	VCMPPS $79, 15(R8)(R14*1), Z3, K2, K7              // 6291644ac2bc300f0000004f
	VCMPPS $79, 15(R8)(R14*2), Z3, K2, K7              // 6291644ac2bc700f0000004f
	VCMPPS $79, Z9, Z9, K2, K6                         // 62d1344ac2f14f
	VCMPPS $79, Z25, Z9, K2, K6                        // 6291344ac2f14f
	VCMPPS $79, 15(R8)(R14*1), Z9, K2, K6              // 6291344ac2b4300f0000004f
	VCMPPS $79, 15(R8)(R14*2), Z9, K2, K6              // 6291344ac2b4700f0000004f
	VCMPPS $79, Z9, Z3, K2, K6                         // 62d1644ac2f14f
	VCMPPS $79, Z25, Z3, K2, K6                        // 6291644ac2f14f
	VCMPPS $79, 15(R8)(R14*1), Z3, K2, K6              // 6291644ac2b4300f0000004f
	VCMPPS $79, 15(R8)(R14*2), Z3, K2, K6              // 6291644ac2b4700f0000004f
	VCMPSD $64, X31, X5, K3, K4                        // 6291d70bc2e740
	VCMPSD $64, X31, X5, K3, K6                        // 6291d70bc2f740
	VCMPSD $27, X21, X3, K3, K0                        // 62b1e70bc2c51b or 62b1e72bc2c51b or 62b1e74bc2c51b
	VCMPSD $27, (BX), X3, K3, K0                       // 62f1e70bc2031b or 62f1e72bc2031b or 62f1e74bc2031b
	VCMPSD $27, -17(BP)(SI*1), X3, K3, K0              // 62f1e70bc28435efffffff1b or 62f1e72bc28435efffffff1b or 62f1e74bc28435efffffff1b
	VCMPSD $27, X21, X3, K3, K7                        // 62b1e70bc2fd1b or 62b1e72bc2fd1b or 62b1e74bc2fd1b
	VCMPSD $27, (BX), X3, K3, K7                       // 62f1e70bc23b1b or 62f1e72bc23b1b or 62f1e74bc23b1b
	VCMPSD $27, -17(BP)(SI*1), X3, K3, K7              // 62f1e70bc2bc35efffffff1b or 62f1e72bc2bc35efffffff1b or 62f1e74bc2bc35efffffff1b
	VCMPSS $47, X11, X1, K3, K5                        // 62d1760bc2eb2f
	VCMPSS $47, X11, X1, K3, K4                        // 62d1760bc2e32f
	VCMPSS $82, X0, X13, K2, K4                        // 62f1160ac2e052 or 62f1162ac2e052 or 62f1164ac2e052
	VCMPSS $82, -7(DI)(R8*1), X13, K2, K4              // 62b1160ac2a407f9ffffff52 or 62b1162ac2a407f9ffffff52 or 62b1164ac2a407f9ffffff52
	VCMPSS $82, (SP), X13, K2, K4                      // 62f1160ac2242452 or 62f1162ac2242452 or 62f1164ac2242452
	VCMPSS $82, X0, X13, K2, K6                        // 62f1160ac2f052 or 62f1162ac2f052 or 62f1164ac2f052
	VCMPSS $82, -7(DI)(R8*1), X13, K2, K6              // 62b1160ac2b407f9ffffff52 or 62b1162ac2b407f9ffffff52 or 62b1164ac2b407f9ffffff52
	VCMPSS $82, (SP), X13, K2, K6                      // 62f1160ac2342452 or 62f1162ac2342452 or 62f1164ac2342452
	VCOMISD X16, X30                                   // 6221fd082ff0
	VCOMISS X19, X14                                   // 62317c082ff3
	VCOMPRESSPD X23, K1, X26                           // 6282fd098afa
	VCOMPRESSPD X23, K1, (SI)                          // 62e2fd098a3e
	VCOMPRESSPD X23, K1, 7(SI)(DI*2)                   // 62e2fd098abc7e07000000
	VCOMPRESSPD Y20, K2, Y21                           // 62a2fd2a8ae5
	VCOMPRESSPD Y20, K2, -7(CX)(DX*1)                  // 62e2fd2a8aa411f9ffffff
	VCOMPRESSPD Y20, K2, -15(R14)(R15*4)               // 6282fd2a8aa4bef1ffffff
	VCOMPRESSPD Z20, K1, Z9                            // 62c2fd498ae1
	VCOMPRESSPD Z0, K1, Z9                             // 62d2fd498ac1
	VCOMPRESSPD Z20, K1, Z28                           // 6282fd498ae4
	VCOMPRESSPD Z0, K1, Z28                            // 6292fd498ac4
	VCOMPRESSPD Z20, K1, (R14)                         // 62c2fd498a26
	VCOMPRESSPD Z0, K1, (R14)                          // 62d2fd498a06
	VCOMPRESSPD Z20, K1, -7(DI)(R8*8)                  // 62a2fd498aa4c7f9ffffff
	VCOMPRESSPD Z0, K1, -7(DI)(R8*8)                   // 62b2fd498a84c7f9ffffff
	VCOMPRESSPS X16, K7, X12                           // 62c27d0f8ac4
	VCOMPRESSPS X16, K7, 17(SP)(BP*8)                  // 62e27d0f8a84ec11000000
	VCOMPRESSPS X16, K7, 17(SP)(BP*4)                  // 62e27d0f8a84ac11000000
	VCOMPRESSPS Y31, K1, Y6                            // 62627d298afe
	VCOMPRESSPS Y31, K1, 15(DX)(BX*1)                  // 62627d298abc1a0f000000
	VCOMPRESSPS Y31, K1, -7(CX)(DX*2)                  // 62627d298abc51f9ffffff
	VCOMPRESSPS Z17, K1, Z17                           // 62a27d498ac9
	VCOMPRESSPS Z23, K1, Z17                           // 62a27d498af9
	VCOMPRESSPS Z17, K1, Z0                            // 62e27d498ac8
	VCOMPRESSPS Z23, K1, Z0                            // 62e27d498af8
	VCOMPRESSPS Z17, K1, 99(R15)(R15*4)                // 62827d498a8cbf63000000
	VCOMPRESSPS Z23, K1, 99(R15)(R15*4)                // 62827d498abcbf63000000
	VCOMPRESSPS Z17, K1, 15(DX)                        // 62e27d498a8a0f000000
	VCOMPRESSPS Z23, K1, 15(DX)                        // 62e27d498aba0f000000
	VCVTDQ2PD X23, K1, X23                             // 62a17e09e6ff
	VCVTDQ2PD 7(SI)(DI*4), K1, X23                     // 62e17e09e6bcbe07000000
	VCVTDQ2PD -7(DI)(R8*2), K1, X23                    // 62a17e09e6bc47f9ffffff
	VCVTDQ2PD X11, K7, Y6                              // 62d17e2fe6f3
	VCVTDQ2PD -17(BP), K7, Y6                          // 62f17e2fe6b5efffffff
	VCVTDQ2PD -15(R14)(R15*8), K7, Y6                  // 62917e2fe6b4fef1ffffff
	VCVTDQ2PD Y11, K2, Z31                             // 62417e4ae6fb
	VCVTDQ2PD (CX), K2, Z31                            // 62617e4ae639
	VCVTDQ2PD 99(R15), K2, Z31                         // 62417e4ae6bf63000000
	VCVTDQ2PD Y11, K2, Z0                              // 62d17e4ae6c3
	VCVTDQ2PD (CX), K2, Z0                             // 62f17e4ae601
	VCVTDQ2PD 99(R15), K2, Z0                          // 62d17e4ae68763000000
	VCVTDQ2PS X24, K4, X31                             // 62017c0c5bf8
	VCVTDQ2PS 17(SP), K4, X31                          // 62617c0c5bbc2411000000
	VCVTDQ2PS -17(BP)(SI*4), K4, X31                   // 62617c0c5bbcb5efffffff
	VCVTDQ2PS Y7, K1, Y19                              // 62e17c295bdf
	VCVTDQ2PS 17(SP)(BP*2), K1, Y19                    // 62e17c295b9c6c11000000
	VCVTDQ2PS -7(DI)(R8*4), K1, Y19                    // 62a17c295b9c87f9ffffff
	VCVTDQ2PS Z6, K3, Z21                              // 62e17c4b5bee
	VCVTDQ2PS Z9, K3, Z21                              // 62c17c4b5be9
	VCVTDQ2PS Z6, K3, Z9                               // 62717c4b5bce
	VCVTDQ2PS Z9, K3, Z9                               // 62517c4b5bc9
	VCVTDQ2PS Z20, K4, Z1                              // 62b17c4c5bcc
	VCVTDQ2PS Z9, K4, Z1                               // 62d17c4c5bc9
	VCVTDQ2PS 99(R15)(R15*2), K4, Z1                   // 62917c4c5b8c7f63000000
	VCVTDQ2PS -7(DI), K4, Z1                           // 62f17c4c5b8ff9ffffff
	VCVTDQ2PS Z20, K4, Z9                              // 62317c4c5bcc
	VCVTDQ2PS Z9, K4, Z9                               // 62517c4c5bc9
	VCVTDQ2PS 99(R15)(R15*2), K4, Z9                   // 62117c4c5b8c7f63000000
	VCVTDQ2PS -7(DI), K4, Z9                           // 62717c4c5b8ff9ffffff
	VCVTPD2DQ Z30, K5, Y6                              // 6291ff4de6f6
	VCVTPD2DQ Z5, K5, Y6                               // 62f1ff4de6f5
	VCVTPD2DQ Z26, K7, Y0                              // 6291ff4fe6c2
	VCVTPD2DQ Z22, K7, Y0                              // 62b1ff4fe6c6
	VCVTPD2DQ -7(CX)(DX*1), K7, Y0                     // 62f1ff4fe68411f9ffffff
	VCVTPD2DQ -15(R14)(R15*4), K7, Y0                  // 6291ff4fe684bef1ffffff
	VCVTPD2DQX X0, K7, X14                             // 6271ff0fe6f0
	VCVTPD2DQX 7(AX), K7, X14                          // 6271ff0fe6b007000000
	VCVTPD2DQX (DI), K7, X14                           // 6271ff0fe637
	VCVTPD2DQY Y3, K6, X11                             // 6271ff2ee6db
	VCVTPD2DQY 15(R8), K6, X11                         // 6251ff2ee6980f000000
	VCVTPD2DQY (BP), K6, X11                           // 6271ff2ee65d00
	VCVTPD2PS Z7, K3, Y5                               // 62f1fd4b5aef
	VCVTPD2PS Z21, K3, Y5                              // 62b1fd4b5aed
	VCVTPD2PS Z16, K7, Y20                             // 62a1fd4f5ae0
	VCVTPD2PS Z25, K7, Y20                             // 6281fd4f5ae1
	VCVTPD2PS 15(DX)(BX*1), K7, Y20                    // 62e1fd4f5aa41a0f000000
	VCVTPD2PS -7(CX)(DX*2), K7, Y20                    // 62e1fd4f5aa451f9ffffff
	VCVTPD2PSX X2, K4, X23                             // 62e1fd0c5afa
	VCVTPD2PSX 99(R15)(R15*1), K4, X23                 // 6281fd0c5abc3f63000000
	VCVTPD2PSX (DX), K4, X23                           // 62e1fd0c5a3a
	VCVTPD2PSY Y12, K4, X20                            // 62c1fd2c5ae4
	VCVTPD2PSY 15(R8)(R14*8), K4, X20                  // 6281fd2c5aa4f00f000000
	VCVTPD2PSY -15(R14)(R15*2), K4, X20                // 6281fd2c5aa47ef1ffffff
	VCVTPD2UDQ Z27, K4, Y28                            // 6201fc4c79e3
	VCVTPD2UDQ Z25, K4, Y28                            // 6201fc4c79e1
	VCVTPD2UDQ Z23, K2, Y7                             // 62b1fc4a79ff
	VCVTPD2UDQ Z9, K2, Y7                              // 62d1fc4a79f9
	VCVTPD2UDQ 17(SP)(BP*2), K2, Y7                    // 62f1fc4a79bc6c11000000
	VCVTPD2UDQ -7(DI)(R8*4), K2, Y7                    // 62b1fc4a79bc87f9ffffff
	VCVTPD2UDQX X9, K2, X0                             // 62d1fc0a79c1
	VCVTPD2UDQX 7(SI)(DI*8), K2, X0                    // 62f1fc0a7984fe07000000
	VCVTPD2UDQX -15(R14), K2, X0                       // 62d1fc0a7986f1ffffff
	VCVTPD2UDQY Y0, K3, X13                            // 6271fc2b79e8
	VCVTPD2UDQY 7(AX)(CX*4), K3, X13                   // 6271fc2b79ac8807000000
	VCVTPD2UDQY 7(AX)(CX*1), K3, X13                   // 6271fc2b79ac0807000000
	VCVTPH2PS X9, K2, Y12                              // 62527d2a13e1
	VCVTPH2PS -7(DI)(R8*1), K2, Y12                    // 62327d2a13a407f9ffffff
	VCVTPH2PS (SP), K2, Y12                            // 62727d2a132424
	VCVTPH2PS X31, K1, X2                              // 62927d0913d7
	VCVTPH2PS (R8), K1, X2                             // 62d27d091310
	VCVTPH2PS 15(DX)(BX*2), K1, X2                     // 62f27d0913945a0f000000
	VCVTPH2PS Y1, K7, Z22                              // 62e27d4f13f1
	VCVTPH2PS Y1, K7, Z25                              // 62627d4f13c9
	VCVTPH2PS Y14, K1, Z1                              // 62d27d4913ce
	VCVTPH2PS 17(SP)(BP*8), K1, Z1                     // 62f27d49138cec11000000
	VCVTPH2PS 17(SP)(BP*4), K1, Z1                     // 62f27d49138cac11000000
	VCVTPH2PS Y14, K1, Z16                             // 62c27d4913c6
	VCVTPH2PS 17(SP)(BP*8), K1, Z16                    // 62e27d491384ec11000000
	VCVTPH2PS 17(SP)(BP*4), K1, Z16                    // 62e27d491384ac11000000
	VCVTPS2DQ X22, K1, X11                             // 62317d095bde
	VCVTPS2DQ -7(CX), K1, X11                          // 62717d095b99f9ffffff
	VCVTPS2DQ 15(DX)(BX*4), K1, X11                    // 62717d095b9c9a0f000000
	VCVTPS2DQ Y7, K1, Y17                              // 62e17d295bcf
	VCVTPS2DQ 7(SI)(DI*4), K1, Y17                     // 62e17d295b8cbe07000000
	VCVTPS2DQ -7(DI)(R8*2), K1, Y17                    // 62a17d295b8c47f9ffffff
	VCVTPS2DQ Z0, K7, Z6                               // 62f17d4f5bf0
	VCVTPS2DQ Z8, K7, Z6                               // 62d17d4f5bf0
	VCVTPS2DQ Z0, K7, Z2                               // 62f17d4f5bd0
	VCVTPS2DQ Z8, K7, Z2                               // 62d17d4f5bd0
	VCVTPS2DQ Z14, K2, Z15                             // 62517d4a5bfe
	VCVTPS2DQ Z27, K2, Z15                             // 62117d4a5bfb
	VCVTPS2DQ 15(R8)(R14*8), K2, Z15                   // 62117d4a5bbcf00f000000
	VCVTPS2DQ -15(R14)(R15*2), K2, Z15                 // 62117d4a5bbc7ef1ffffff
	VCVTPS2DQ Z14, K2, Z12                             // 62517d4a5be6
	VCVTPS2DQ Z27, K2, Z12                             // 62117d4a5be3
	VCVTPS2DQ 15(R8)(R14*8), K2, Z12                   // 62117d4a5ba4f00f000000
	VCVTPS2DQ -15(R14)(R15*2), K2, Z12                 // 62117d4a5ba47ef1ffffff
	VCVTPS2PD X14, K4, X5                              // 62d17c0c5aee
	VCVTPS2PD 99(R15)(R15*8), K4, X5                   // 62917c0c5aacff63000000
	VCVTPS2PD 7(AX)(CX*8), K4, X5                      // 62f17c0c5aacc807000000
	VCVTPS2PD X0, K1, Y9                               // 62717c295ac8
	VCVTPS2PD 17(SP), K1, Y9                           // 62717c295a8c2411000000
	VCVTPS2PD -17(BP)(SI*4), K1, Y9                    // 62717c295a8cb5efffffff
	VCVTPS2PD Y31, K3, Z11                             // 62117c4b5adf
	VCVTPS2PD Y31, K3, Z5                              // 62917c4b5aef
	VCVTPS2PD Y8, K4, Z13                              // 62517c4c5ae8
	VCVTPS2PD -15(R14)(R15*1), K4, Z13                 // 62117c4c5aac3ef1ffffff
	VCVTPS2PD -15(BX), K4, Z13                         // 62717c4c5aabf1ffffff
	VCVTPS2PD Y8, K4, Z14                              // 62517c4c5af0
	VCVTPS2PD -15(R14)(R15*1), K4, Z14                 // 62117c4c5ab43ef1ffffff
	VCVTPS2PD -15(BX), K4, Z14                         // 62717c4c5ab3f1ffffff
	VCVTPS2PH $126, X7, K5, X17                        // 62b37d0d1df97e
	VCVTPS2PH $126, X7, K5, 17(SP)(BP*1)               // 62f37d0d1dbc2c110000007e
	VCVTPS2PH $126, X7, K5, -7(CX)(DX*8)               // 62f37d0d1dbcd1f9ffffff7e
	VCVTPS2PH $94, Y1, K7, X15                         // 62d37d2f1dcf5e
	VCVTPS2PH $94, Y1, K7, (AX)                        // 62f37d2f1d085e
	VCVTPS2PH $94, Y1, K7, 7(SI)                       // 62f37d2f1d8e070000005e
	VCVTPS2PH $121, Z5, K7, Y28                        // 62937d4f1dec79
	VCVTPS2PH $121, Z23, K7, Y28                       // 62837d4f1dfc79
	VCVTPS2PH $121, Z5, K7, 7(AX)                      // 62f37d4f1da80700000079
	VCVTPS2PH $121, Z23, K7, 7(AX)                     // 62e37d4f1db80700000079
	VCVTPS2PH $121, Z5, K7, (DI)                       // 62f37d4f1d2f79
	VCVTPS2PH $121, Z23, K7, (DI)                      // 62e37d4f1d3f79
	VCVTPS2PH $13, Z2, K6, Y13                         // 62d37d4e1dd50d
	VCVTPS2UDQ X27, K7, X8                             // 62117c0f79c3
	VCVTPS2UDQ 15(R8)(R14*4), K7, X8                   // 62117c0f7984b00f000000
	VCVTPS2UDQ -7(CX)(DX*4), K7, X8                    // 62717c0f798491f9ffffff
	VCVTPS2UDQ Y9, K2, Y12                             // 62517c2a79e1
	VCVTPS2UDQ -17(BP)(SI*8), K2, Y12                  // 62717c2a79a4f5efffffff
	VCVTPS2UDQ (R15), K2, Y12                          // 62517c2a7927
	VCVTPS2UDQ Z13, K5, Z28                            // 62417c4d79e5
	VCVTPS2UDQ Z21, K5, Z28                            // 62217c4d79e5
	VCVTPS2UDQ Z13, K5, Z6                             // 62d17c4d79f5
	VCVTPS2UDQ Z21, K5, Z6                             // 62b17c4d79f5
	VCVTPS2UDQ Z3, K3, Z26                             // 62617c4b79d3
	VCVTPS2UDQ Z0, K3, Z26                             // 62617c4b79d0
	VCVTPS2UDQ (SI), K3, Z26                           // 62617c4b7916
	VCVTPS2UDQ 7(SI)(DI*2), K3, Z26                    // 62617c4b79947e07000000
	VCVTPS2UDQ Z3, K3, Z3                              // 62f17c4b79db
	VCVTPS2UDQ Z0, K3, Z3                              // 62f17c4b79d8
	VCVTPS2UDQ (SI), K3, Z3                            // 62f17c4b791e
	VCVTPS2UDQ 7(SI)(DI*2), K3, Z3                     // 62f17c4b799c7e07000000
	VCVTSD2SI X24, R14                                 // 62117f082df0 or 62117f282df0 or 62117f482df0
	VCVTSD2SI X24, AX                                  // 62917f082dc0 or 62917f282dc0 or 62917f482dc0
	VCVTSD2SS X11, X1, K1, X22                         // 62c1f7095af3
	VCVTSD2SS X8, X7, K1, X6                           // 62d1c7095af0 or 62d1c7295af0 or 62d1c7495af0
	VCVTSD2SS (R14), X7, K1, X6                        // 62d1c7095a36 or 62d1c7295a36 or 62d1c7495a36
	VCVTSD2SS -7(DI)(R8*8), X7, K1, X6                 // 62b1c7095ab4c7f9ffffff or 62b1c7295ab4c7f9ffffff or 62b1c7495ab4c7f9ffffff
	VCVTSD2USIL X31, R9                                // 62117f0879cf
	VCVTSD2USIL X31, CX                                // 62917f0879cf
	VCVTSD2USIL X3, SP                                 // 62f17f0879e3 or 62f17f2879e3 or 62f17f4879e3
	VCVTSD2USIL 99(R15)(R15*4), SP                     // 62917f0879a4bf63000000 or 62917f2879a4bf63000000 or 62917f4879a4bf63000000
	VCVTSD2USIL 15(DX), SP                             // 62f17f0879a20f000000 or 62f17f2879a20f000000 or 62f17f4879a20f000000
	VCVTSD2USIL X3, R14                                // 62717f0879f3 or 62717f2879f3 or 62717f4879f3
	VCVTSD2USIL 99(R15)(R15*4), R14                    // 62117f0879b4bf63000000 or 62117f2879b4bf63000000 or 62117f4879b4bf63000000
	VCVTSD2USIL 15(DX), R14                            // 62717f0879b20f000000 or 62717f2879b20f000000 or 62717f4879b20f000000
	VCVTSD2USIQ X28, R10                               // 6211ff0879d4
	VCVTSD2USIQ X28, CX                                // 6291ff0879cc
	VCVTSD2USIQ X20, R9                                // 6231ff0879cc or 6231ff2879cc or 6231ff4879cc
	VCVTSD2USIQ (CX), R9                               // 6271ff087909 or 6271ff287909 or 6271ff487909
	VCVTSD2USIQ 99(R15), R9                            // 6251ff08798f63000000 or 6251ff28798f63000000 or 6251ff48798f63000000
	VCVTSD2USIQ X20, R13                               // 6231ff0879ec or 6231ff2879ec or 6231ff4879ec
	VCVTSD2USIQ (CX), R13                              // 6271ff087929 or 6271ff287929 or 6271ff487929
	VCVTSD2USIQ 99(R15), R13                           // 6251ff0879af63000000 or 6251ff2879af63000000 or 6251ff4879af63000000
	VCVTSI2SDL AX, X7, X24                             // 626147082ac0 or 626147282ac0 or 626147482ac0
	VCVTSI2SDL R9, X7, X24                             // 624147082ac1 or 624147282ac1 or 624147482ac1
	VCVTSI2SDL 99(R15)(R15*8), X7, X24                 // 620147082a84ff63000000 or 620147282a84ff63000000 or 620147482a84ff63000000
	VCVTSI2SDL 7(AX)(CX*8), X7, X24                    // 626147082a84c807000000 or 626147282a84c807000000 or 626147482a84c807000000
	VCVTSI2SDQ DX, X16, X20                            // 62e1ff002ae2 or 62e1ff202ae2 or 62e1ff402ae2
	VCVTSI2SDQ BP, X16, X20                            // 62e1ff002ae5 or 62e1ff202ae5 or 62e1ff402ae5
	VCVTSI2SDQ 99(R15)(R15*2), X16, X20                // 6281ff002aa47f63000000 or 6281ff202aa47f63000000 or 6281ff402aa47f63000000
	VCVTSI2SDQ -7(DI), X16, X20                        // 62e1ff002aa7f9ffffff or 62e1ff202aa7f9ffffff or 62e1ff402aa7f9ffffff
	VCVTSI2SSL CX, X28, X17                            // 62e11e002ac9 or 62e11e202ac9 or 62e11e402ac9
	VCVTSI2SSL SP, X28, X17                            // 62e11e002acc or 62e11e202acc or 62e11e402acc
	VCVTSI2SSL (AX), X28, X17                          // 62e11e002a08 or 62e11e202a08 or 62e11e402a08
	VCVTSI2SSL 7(SI), X28, X17                         // 62e11e002a8e07000000 or 62e11e202a8e07000000 or 62e11e402a8e07000000
	VCVTSS2SD X6, X16, K7, X11                         // 62717e075ade
	VCVTSS2SD X12, X22, K2, X6                         // 62d14e025af4 or 62d14e225af4 or 62d14e425af4
	VCVTSS2SD (BX), X22, K2, X6                        // 62f14e025a33 or 62f14e225a33 or 62f14e425a33
	VCVTSS2SD -17(BP)(SI*1), X22, K2, X6               // 62f14e025ab435efffffff or 62f14e225ab435efffffff or 62f14e425ab435efffffff
	VCVTSS2SI X16, R9                                  // 62317e082dc8
	VCVTSS2SI X16, CX                                  // 62b17e082dc8
	VCVTSS2SI X28, SP                                  // 62917e082de4 or 62917e282de4 or 62917e482de4
	VCVTSS2SI X28, R14                                 // 62117e082df4 or 62117e282df4 or 62117e482df4
	VCVTSS2USIL X11, AX                                // 62d17e0879c3
	VCVTSS2USIL X11, R9                                // 62517e0879cb
	VCVTSS2USIL X1, CX                                 // 62f17e0879c9 or 62f17e2879c9 or 62f17e4879c9
	VCVTSS2USIL 17(SP)(BP*1), CX                       // 62f17e08798c2c11000000 or 62f17e28798c2c11000000 or 62f17e48798c2c11000000
	VCVTSS2USIL -7(CX)(DX*8), CX                       // 62f17e08798cd1f9ffffff or 62f17e28798cd1f9ffffff or 62f17e48798cd1f9ffffff
	VCVTSS2USIL X1, SP                                 // 62f17e0879e1 or 62f17e2879e1 or 62f17e4879e1
	VCVTSS2USIL 17(SP)(BP*1), SP                       // 62f17e0879a42c11000000 or 62f17e2879a42c11000000 or 62f17e4879a42c11000000
	VCVTSS2USIL -7(CX)(DX*8), SP                       // 62f17e0879a4d1f9ffffff or 62f17e2879a4d1f9ffffff or 62f17e4879a4d1f9ffffff
	VCVTSS2USIQ X19, DX                                // 62b1fe0879d3
	VCVTSS2USIQ X19, BP                                // 62b1fe0879eb
	VCVTSS2USIQ X13, R10                               // 6251fe0879d5 or 6251fe2879d5 or 6251fe4879d5
	VCVTSS2USIQ -17(BP)(SI*2), R10                     // 6271fe08799475efffffff or 6271fe28799475efffffff or 6271fe48799475efffffff
	VCVTSS2USIQ 7(AX)(CX*2), R10                       // 6271fe0879944807000000 or 6271fe2879944807000000 or 6271fe4879944807000000
	VCVTSS2USIQ X13, CX                                // 62d1fe0879cd or 62d1fe2879cd or 62d1fe4879cd
	VCVTSS2USIQ -17(BP)(SI*2), CX                      // 62f1fe08798c75efffffff or 62f1fe28798c75efffffff or 62f1fe48798c75efffffff
	VCVTSS2USIQ 7(AX)(CX*2), CX                        // 62f1fe08798c4807000000 or 62f1fe28798c4807000000 or 62f1fe48798c4807000000
	VCVTTPD2DQ Z16, K4, Y30                            // 6221fd4ce6f0
	VCVTTPD2DQ Z13, K4, Y30                            // 6241fd4ce6f5
	VCVTTPD2DQ Z12, K1, Y26                            // 6241fd49e6d4
	VCVTTPD2DQ Z27, K1, Y26                            // 6201fd49e6d3
	VCVTTPD2DQ 7(AX), K1, Y26                          // 6261fd49e69007000000
	VCVTTPD2DQ (DI), K1, Y26                           // 6261fd49e617
	VCVTTPD2DQX X14, K3, X2                            // 62d1fd0be6d6
	VCVTTPD2DQX 15(R8)(R14*1), K3, X2                  // 6291fd0be694300f000000
	VCVTTPD2DQX 15(R8)(R14*2), K3, X2                  // 6291fd0be694700f000000
	VCVTTPD2DQY Y7, K4, X0                             // 62f1fd2ce6c7
	VCVTTPD2DQY -7(CX), K4, X0                         // 62f1fd2ce681f9ffffff
	VCVTTPD2DQY 15(DX)(BX*4), K4, X0                   // 62f1fd2ce6849a0f000000
	VCVTTPD2UDQ Z9, K3, Y30                            // 6241fc4b78f1
	VCVTTPD2UDQ Z12, K3, Y30                           // 6241fc4b78f4
	VCVTTPD2UDQ Z6, K7, Y31                            // 6261fc4f78fe
	VCVTTPD2UDQ Z25, K7, Y31                           // 6201fc4f78f9
	VCVTTPD2UDQ -17(BP)(SI*8), K7, Y31                 // 6261fc4f78bcf5efffffff
	VCVTTPD2UDQ (R15), K7, Y31                         // 6241fc4f783f
	VCVTTPD2UDQX X17, K4, X11                          // 6231fc0c78d9
	VCVTTPD2UDQX 99(R15)(R15*4), K4, X11               // 6211fc0c789cbf63000000
	VCVTTPD2UDQX 15(DX), K4, X11                       // 6271fc0c789a0f000000
	VCVTTPD2UDQY Y22, K4, X18                          // 62a1fc2c78d6
	VCVTTPD2UDQY (AX), K4, X18                         // 62e1fc2c7810
	VCVTTPD2UDQY 7(SI), K4, X18                        // 62e1fc2c789607000000
	VCVTTPS2DQ X24, K4, X2                             // 62917e0c5bd0
	VCVTTPS2DQ 99(R15)(R15*2), K4, X2                  // 62917e0c5b947f63000000
	VCVTTPS2DQ -7(DI), K4, X2                          // 62f17e0c5b97f9ffffff
	VCVTTPS2DQ Y0, K2, Y7                              // 62f17e2a5bf8
	VCVTTPS2DQ 15(R8)(R14*4), K2, Y7                   // 62917e2a5bbcb00f000000
	VCVTTPS2DQ -7(CX)(DX*4), K2, Y7                    // 62f17e2a5bbc91f9ffffff
	VCVTTPS2DQ Z20, K2, Z0                             // 62b17e4a5bc4
	VCVTTPS2DQ Z28, K2, Z0                             // 62917e4a5bc4
	VCVTTPS2DQ Z20, K2, Z6                             // 62b17e4a5bf4
	VCVTTPS2DQ Z28, K2, Z6                             // 62917e4a5bf4
	VCVTTPS2DQ Z9, K3, Z3                              // 62d17e4b5bd9
	VCVTTPS2DQ Z19, K3, Z3                             // 62b17e4b5bdb
	VCVTTPS2DQ 7(SI)(DI*1), K3, Z3                     // 62f17e4b5b9c3e07000000
	VCVTTPS2DQ 15(DX)(BX*8), K3, Z3                    // 62f17e4b5b9cda0f000000
	VCVTTPS2DQ Z9, K3, Z30                             // 62417e4b5bf1
	VCVTTPS2DQ Z19, K3, Z30                            // 62217e4b5bf3
	VCVTTPS2DQ 7(SI)(DI*1), K3, Z30                    // 62617e4b5bb43e07000000
	VCVTTPS2DQ 15(DX)(BX*8), K3, Z30                   // 62617e4b5bb4da0f000000
	VCVTTPS2UDQ X22, K2, X26                           // 62217c0a78d6
	VCVTTPS2UDQ 15(DX)(BX*1), K2, X26                  // 62617c0a78941a0f000000
	VCVTTPS2UDQ -7(CX)(DX*2), K2, X26                  // 62617c0a789451f9ffffff
	VCVTTPS2UDQ Y13, K1, Y24                           // 62417c2978c5
	VCVTTPS2UDQ 17(SP)(BP*1), K1, Y24                  // 62617c2978842c11000000
	VCVTTPS2UDQ -7(CX)(DX*8), K1, Y24                  // 62617c297884d1f9ffffff
	VCVTTPS2UDQ Z2, K7, Z18                            // 62e17c4f78d2
	VCVTTPS2UDQ Z21, K7, Z18                           // 62a17c4f78d5
	VCVTTPS2UDQ Z2, K7, Z24                            // 62617c4f78c2
	VCVTTPS2UDQ Z21, K7, Z24                           // 62217c4f78c5
	VCVTTPS2UDQ Z6, K1, Z7                             // 62f17c4978fe
	VCVTTPS2UDQ Z16, K1, Z7                            // 62b17c4978f8
	VCVTTPS2UDQ -7(CX), K1, Z7                         // 62f17c4978b9f9ffffff
	VCVTTPS2UDQ 15(DX)(BX*4), K1, Z7                   // 62f17c4978bc9a0f000000
	VCVTTPS2UDQ Z6, K1, Z13                            // 62717c4978ee
	VCVTTPS2UDQ Z16, K1, Z13                           // 62317c4978e8
	VCVTTPS2UDQ -7(CX), K1, Z13                        // 62717c4978a9f9ffffff
	VCVTTPS2UDQ 15(DX)(BX*4), K1, Z13                  // 62717c4978ac9a0f000000
	VCVTTSD2SI X30, R9                                 // 62117f082cce or 62117f282cce or 62117f482cce
	VCVTTSD2SI X30, CX                                 // 62917f082cce or 62917f282cce or 62917f482cce
	VCVTTSD2USIL X12, SP                               // 62d17f0878e4
	VCVTTSD2USIL X12, R14                              // 62517f0878f4
	VCVTTSD2USIL X23, AX                               // 62b17f0878c7 or 62b17f2878c7 or 62b17f4878c7
	VCVTTSD2USIL 17(SP)(BP*2), AX                      // 62f17f0878846c11000000 or 62f17f2878846c11000000 or 62f17f4878846c11000000
	VCVTTSD2USIL -7(DI)(R8*4), AX                      // 62b17f08788487f9ffffff or 62b17f28788487f9ffffff or 62b17f48788487f9ffffff
	VCVTTSD2USIL X23, R9                               // 62317f0878cf or 62317f2878cf or 62317f4878cf
	VCVTTSD2USIL 17(SP)(BP*2), R9                      // 62717f08788c6c11000000 or 62717f28788c6c11000000 or 62717f48788c6c11000000
	VCVTTSD2USIL -7(DI)(R8*4), R9                      // 62317f08788c87f9ffffff or 62317f28788c87f9ffffff or 62317f48788c87f9ffffff
	VCVTTSD2USIQ X30, R10                              // 6211ff0878d6
	VCVTTSD2USIQ X30, CX                               // 6291ff0878ce
	VCVTTSD2USIQ X8, R9                                // 6251ff0878c8 or 6251ff2878c8 or 6251ff4878c8
	VCVTTSD2USIQ 15(R8), R9                            // 6251ff0878880f000000 or 6251ff2878880f000000 or 6251ff4878880f000000
	VCVTTSD2USIQ (BP), R9                              // 6271ff08784d00 or 6271ff28784d00 or 6271ff48784d00
	VCVTTSD2USIQ X8, R13                               // 6251ff0878e8 or 6251ff2878e8 or 6251ff4878e8
	VCVTTSD2USIQ 15(R8), R13                           // 6251ff0878a80f000000 or 6251ff2878a80f000000 or 6251ff4878a80f000000
	VCVTTSD2USIQ (BP), R13                             // 6271ff08786d00 or 6271ff28786d00 or 6271ff48786d00
	VCVTTSS2SI X20, CX                                 // 62b17e082ccc
	VCVTTSS2SI X20, SP                                 // 62b17e082ce4
	VCVTTSS2SIQ X26, R10                               // 6211fe082cd2 or 6211fe282cd2 or 6211fe482cd2
	VCVTTSS2SIQ X26, CX                                // 6291fe082cca or 6291fe282cca or 6291fe482cca
	VCVTTSS2USIL X19, R9                               // 62317e0878cb
	VCVTTSS2USIL X19, CX                               // 62b17e0878cb
	VCVTTSS2USIL X0, SP                                // 62f17e0878e0 or 62f17e2878e0 or 62f17e4878e0
	VCVTTSS2USIL 99(R15)(R15*4), SP                    // 62917e0878a4bf63000000 or 62917e2878a4bf63000000 or 62917e4878a4bf63000000
	VCVTTSS2USIL 15(DX), SP                            // 62f17e0878a20f000000 or 62f17e2878a20f000000 or 62f17e4878a20f000000
	VCVTTSS2USIL X0, R14                               // 62717e0878f0 or 62717e2878f0 or 62717e4878f0
	VCVTTSS2USIL 99(R15)(R15*4), R14                   // 62117e0878b4bf63000000 or 62117e2878b4bf63000000 or 62117e4878b4bf63000000
	VCVTTSS2USIL 15(DX), R14                           // 62717e0878b20f000000 or 62717e2878b20f000000 or 62717e4878b20f000000
	VCVTTSS2USIQ X31, R9                               // 6211fe0878cf
	VCVTTSS2USIQ X31, R13                              // 6211fe0878ef
	VCVTTSS2USIQ X16, DX                               // 62b1fe0878d0 or 62b1fe2878d0 or 62b1fe4878d0
	VCVTTSS2USIQ (CX), DX                              // 62f1fe087811 or 62f1fe287811 or 62f1fe487811
	VCVTTSS2USIQ 99(R15), DX                           // 62d1fe08789763000000 or 62d1fe28789763000000 or 62d1fe48789763000000
	VCVTTSS2USIQ X16, BP                               // 62b1fe0878e8 or 62b1fe2878e8 or 62b1fe4878e8
	VCVTTSS2USIQ (CX), BP                              // 62f1fe087829 or 62f1fe287829 or 62f1fe487829
	VCVTTSS2USIQ 99(R15), BP                           // 62d1fe0878af63000000 or 62d1fe2878af63000000 or 62d1fe4878af63000000
	VCVTUDQ2PD X8, K4, X7                              // 62d17e0c7af8
	VCVTUDQ2PD 17(SP)(BP*2), K4, X7                    // 62f17e0c7abc6c11000000
	VCVTUDQ2PD -7(DI)(R8*4), K4, X7                    // 62b17e0c7abc87f9ffffff
	VCVTUDQ2PD X1, K1, Y1                              // 62f17e297ac9
	VCVTUDQ2PD 15(R8)(R14*1), K1, Y1                   // 62917e297a8c300f000000
	VCVTUDQ2PD 15(R8)(R14*2), K1, Y1                   // 62917e297a8c700f000000
	VCVTUDQ2PD Y26, K3, Z13                            // 62117e4b7aea
	VCVTUDQ2PD (AX), K3, Z13                           // 62717e4b7a28
	VCVTUDQ2PD 7(SI), K3, Z13                          // 62717e4b7aae07000000
	VCVTUDQ2PS X15, K4, X0                             // 62d17f0c7ac7
	VCVTUDQ2PS 15(R8), K4, X0                          // 62d17f0c7a800f000000
	VCVTUDQ2PS (BP), K4, X0                            // 62f17f0c7a4500
	VCVTUDQ2PS Y12, K5, Y30                            // 62417f2d7af4
	VCVTUDQ2PS (R14), K5, Y30                          // 62417f2d7a36
	VCVTUDQ2PS -7(DI)(R8*8), K5, Y30                   // 62217f2d7ab4c7f9ffffff
	VCVTUDQ2PS Z22, K7, Z18                            // 62a17f4f7ad6
	VCVTUDQ2PS Z7, K7, Z18                             // 62e17f4f7ad7
	VCVTUDQ2PS Z22, K7, Z8                             // 62317f4f7ac6
	VCVTUDQ2PS Z7, K7, Z8                              // 62717f4f7ac7
	VCVTUDQ2PS Z20, K7, Z2                             // 62b17f4f7ad4
	VCVTUDQ2PS Z9, K7, Z2                              // 62d17f4f7ad1
	VCVTUDQ2PS (BX), K7, Z2                            // 62f17f4f7a13
	VCVTUDQ2PS -17(BP)(SI*1), K7, Z2                   // 62f17f4f7a9435efffffff
	VCVTUDQ2PS Z20, K7, Z31                            // 62217f4f7afc
	VCVTUDQ2PS Z9, K7, Z31                             // 62417f4f7af9
	VCVTUDQ2PS (BX), K7, Z31                           // 62617f4f7a3b
	VCVTUDQ2PS -17(BP)(SI*1), K7, Z31                  // 62617f4f7abc35efffffff
	VCVTUSI2SDL AX, X7, X22                            // 62e147087bf0 or 62e147287bf0 or 62e147487bf0
	VCVTUSI2SDL R9, X7, X22                            // 62c147087bf1 or 62c147287bf1 or 62c147487bf1
	VCVTUSI2SDL 99(R15)(R15*2), X7, X22                // 628147087bb47f63000000 or 628147287bb47f63000000 or 628147487bb47f63000000
	VCVTUSI2SDL -7(DI), X7, X22                        // 62e147087bb7f9ffffff or 62e147287bb7f9ffffff or 62e147487bb7f9ffffff
	VCVTUSI2SDQ R10, X7, X19                           // 62c1c7087bda or 62c1c7287bda or 62c1c7487bda
	VCVTUSI2SDQ CX, X7, X19                            // 62e1c7087bd9 or 62e1c7287bd9 or 62e1c7487bd9
	VCVTUSI2SDQ 15(R8)(R14*8), X7, X19                 // 6281c7087b9cf00f000000 or 6281c7287b9cf00f000000 or 6281c7487b9cf00f000000
	VCVTUSI2SDQ -15(R14)(R15*2), X7, X19               // 6281c7087b9c7ef1ffffff or 6281c7287b9c7ef1ffffff or 6281c7487b9c7ef1ffffff
	VCVTUSI2SDQ R9, X31, X16                           // 62c187007bc1
	VCVTUSI2SDQ R13, X31, X16                          // 62c187007bc5
	VCVTUSI2SSL CX, X7, X1                             // 62f146087bc9 or 62f146287bc9 or 62f146487bc9
	VCVTUSI2SSL SP, X7, X1                             // 62f146087bcc or 62f146287bcc or 62f146487bcc
	VCVTUSI2SSL -7(CX)(DX*1), X7, X1                   // 62f146087b8c11f9ffffff or 62f146287b8c11f9ffffff or 62f146487b8c11f9ffffff
	VCVTUSI2SSL -15(R14)(R15*4), X7, X1                // 629146087b8cbef1ffffff or 629146287b8cbef1ffffff or 629146487b8cbef1ffffff
	VCVTUSI2SSL R14, X15, X9                           // 625106087bce
	VCVTUSI2SSL AX, X15, X9                            // 627106087bc8
	VCVTUSI2SSQ DX, X0, X12                            // 6271fe087be2 or 6271fe287be2 or 6271fe487be2
	VCVTUSI2SSQ BP, X0, X12                            // 6271fe087be5 or 6271fe287be5 or 6271fe487be5
	VCVTUSI2SSQ -15(R14)(R15*1), X0, X12               // 6211fe087ba43ef1ffffff or 6211fe287ba43ef1ffffff or 6211fe487ba43ef1ffffff
	VCVTUSI2SSQ -15(BX), X0, X12                       // 6271fe087ba3f1ffffff or 6271fe287ba3f1ffffff or 6271fe487ba3f1ffffff
	VCVTUSI2SSQ R10, X14, X12                          // 62518e087be2
	VCVTUSI2SSQ CX, X14, X12                           // 62718e087be1
	VDIVPD X26, X3, K2, X8                             // 6211e50a5ec2
	VDIVPD (SI), X3, K2, X8                            // 6271e50a5e06
	VDIVPD 7(SI)(DI*2), X3, K2, X8                     // 6271e50a5e847e07000000
	VDIVPD Y7, Y21, K3, Y13                            // 6271d5235eef
	VDIVPD -7(CX)(DX*1), Y21, K3, Y13                  // 6271d5235eac11f9ffffff
	VDIVPD -15(R14)(R15*4), Y21, K3, Y13               // 6211d5235eacbef1ffffff
	VDIVPD Z16, Z21, K3, Z14                           // 6231d5435ef0
	VDIVPD Z9, Z21, K3, Z14                            // 6251d5435ef1
	VDIVPD Z16, Z8, K3, Z14                            // 6231bd4b5ef0
	VDIVPD Z9, Z8, K3, Z14                             // 6251bd4b5ef1
	VDIVPD Z16, Z21, K3, Z15                           // 6231d5435ef8
	VDIVPD Z9, Z21, K3, Z15                            // 6251d5435ef9
	VDIVPD Z16, Z8, K3, Z15                            // 6231bd4b5ef8
	VDIVPD Z9, Z8, K3, Z15                             // 6251bd4b5ef9
	VDIVPD Z0, Z23, K3, Z20                            // 62e1c5435ee0
	VDIVPD Z11, Z23, K3, Z20                           // 62c1c5435ee3
	VDIVPD -17(BP)(SI*2), Z23, K3, Z20                 // 62e1c5435ea475efffffff
	VDIVPD 7(AX)(CX*2), Z23, K3, Z20                   // 62e1c5435ea44807000000
	VDIVPD Z0, Z19, K3, Z20                            // 62e1e5435ee0
	VDIVPD Z11, Z19, K3, Z20                           // 62c1e5435ee3
	VDIVPD -17(BP)(SI*2), Z19, K3, Z20                 // 62e1e5435ea475efffffff
	VDIVPD 7(AX)(CX*2), Z19, K3, Z20                   // 62e1e5435ea44807000000
	VDIVPD Z0, Z23, K3, Z0                             // 62f1c5435ec0
	VDIVPD Z11, Z23, K3, Z0                            // 62d1c5435ec3
	VDIVPD -17(BP)(SI*2), Z23, K3, Z0                  // 62f1c5435e8475efffffff
	VDIVPD 7(AX)(CX*2), Z23, K3, Z0                    // 62f1c5435e844807000000
	VDIVPD Z0, Z19, K3, Z0                             // 62f1e5435ec0
	VDIVPD Z11, Z19, K3, Z0                            // 62d1e5435ec3
	VDIVPD -17(BP)(SI*2), Z19, K3, Z0                  // 62f1e5435e8475efffffff
	VDIVPD 7(AX)(CX*2), Z19, K3, Z0                    // 62f1e5435e844807000000
	VDIVPS X28, X13, K2, X23                           // 6281140a5efc
	VDIVPS 17(SP)(BP*8), X13, K2, X23                  // 62e1140a5ebcec11000000
	VDIVPS 17(SP)(BP*4), X13, K2, X23                  // 62e1140a5ebcac11000000
	VDIVPS Y18, Y13, K1, Y30                           // 622114295ef2
	VDIVPS 15(DX)(BX*1), Y13, K1, Y30                  // 626114295eb41a0f000000
	VDIVPS -7(CX)(DX*2), Y13, K1, Y30                  // 626114295eb451f9ffffff
	VDIVPS Z0, Z24, K2, Z0                             // 62f13c425ec0
	VDIVPS Z26, Z24, K2, Z0                            // 62913c425ec2
	VDIVPS Z0, Z12, K2, Z0                             // 62f11c4a5ec0
	VDIVPS Z26, Z12, K2, Z0                            // 62911c4a5ec2
	VDIVPS Z0, Z24, K2, Z25                            // 62613c425ec8
	VDIVPS Z26, Z24, K2, Z25                           // 62013c425eca
	VDIVPS Z0, Z12, K2, Z25                            // 62611c4a5ec8
	VDIVPS Z26, Z12, K2, Z25                           // 62011c4a5eca
	VDIVPS Z9, Z9, K1, Z9                              // 625134495ec9
	VDIVPS Z28, Z9, K1, Z9                             // 621134495ecc
	VDIVPS 15(R8)(R14*1), Z9, K1, Z9                   // 621134495e8c300f000000
	VDIVPS 15(R8)(R14*2), Z9, K1, Z9                   // 621134495e8c700f000000
	VDIVPS Z9, Z25, K1, Z9                             // 625134415ec9
	VDIVPS Z28, Z25, K1, Z9                            // 621134415ecc
	VDIVPS 15(R8)(R14*1), Z25, K1, Z9                  // 621134415e8c300f000000
	VDIVPS 15(R8)(R14*2), Z25, K1, Z9                  // 621134415e8c700f000000
	VDIVPS Z9, Z9, K1, Z3                              // 62d134495ed9
	VDIVPS Z28, Z9, K1, Z3                             // 629134495edc
	VDIVPS 15(R8)(R14*1), Z9, K1, Z3                   // 629134495e9c300f000000
	VDIVPS 15(R8)(R14*2), Z9, K1, Z3                   // 629134495e9c700f000000
	VDIVPS Z9, Z25, K1, Z3                             // 62d134415ed9
	VDIVPS Z28, Z25, K1, Z3                            // 629134415edc
	VDIVPS 15(R8)(R14*1), Z25, K1, Z3                  // 629134415e9c300f000000
	VDIVPS 15(R8)(R14*2), Z25, K1, Z3                  // 629134415e9c700f000000
	VDIVSD X15, X9, K7, X24                            // 6241b70f5ec7
	VDIVSD X21, X18, K1, X26                           // 6221ef015ed5 or 6221ef215ed5 or 6221ef415ed5
	VDIVSD 7(AX)(CX*4), X18, K1, X26                   // 6261ef015e948807000000 or 6261ef215e948807000000 or 6261ef415e948807000000
	VDIVSD 7(AX)(CX*1), X18, K1, X26                   // 6261ef015e940807000000 or 6261ef215e940807000000 or 6261ef415e940807000000
	VDIVSS X31, X11, K1, X1                            // 629126095ecf
	VDIVSS X0, X7, K1, X3                              // 62f146095ed8 or 62f146295ed8 or 62f146495ed8
	VDIVSS 15(DX)(BX*1), X7, K1, X3                    // 62f146095e9c1a0f000000 or 62f146295e9c1a0f000000 or 62f146495e9c1a0f000000
	VDIVSS -7(CX)(DX*2), X7, K1, X3                    // 62f146095e9c51f9ffffff or 62f146295e9c51f9ffffff or 62f146495e9c51f9ffffff
	VEXPANDPD X24, K3, X0                              // 6292fd0b88c0
	VEXPANDPD 7(SI)(DI*4), K3, X0                      // 62f2fd0b8884be07000000
	VEXPANDPD -7(DI)(R8*2), K3, X0                     // 62b2fd0b888447f9ffffff
	VEXPANDPD Y8, K4, Y24                              // 6242fd2c88c0
	VEXPANDPD -17(BP), K4, Y24                         // 6262fd2c8885efffffff
	VEXPANDPD -15(R14)(R15*8), K4, Y24                 // 6202fd2c8884fef1ffffff
	VEXPANDPD Z26, K5, Z30                             // 6202fd4d88f2
	VEXPANDPD Z22, K5, Z30                             // 6222fd4d88f6
	VEXPANDPD (CX), K5, Z30                            // 6262fd4d8831
	VEXPANDPD 99(R15), K5, Z30                         // 6242fd4d88b763000000
	VEXPANDPD Z26, K5, Z5                              // 6292fd4d88ea
	VEXPANDPD Z22, K5, Z5                              // 62b2fd4d88ee
	VEXPANDPD (CX), K5, Z5                             // 62f2fd4d8829
	VEXPANDPD 99(R15), K5, Z5                          // 62d2fd4d88af63000000
	VEXPANDPS X7, K7, X20                              // 62e27d0f88e7
	VEXPANDPS 17(SP), K7, X20                          // 62e27d0f88a42411000000
	VEXPANDPS -17(BP)(SI*4), K7, X20                   // 62e27d0f88a4b5efffffff
	VEXPANDPS Y24, K7, Y11                             // 62127d2f88d8
	VEXPANDPS 17(SP)(BP*2), K7, Y11                    // 62727d2f889c6c11000000
	VEXPANDPS -7(DI)(R8*4), K7, Y11                    // 62327d2f889c87f9ffffff
	VEXPANDPS Z16, K6, Z7                              // 62b27d4e88f8
	VEXPANDPS Z25, K6, Z7                              // 62927d4e88f9
	VEXPANDPS 99(R15)(R15*2), K6, Z7                   // 62927d4e88bc7f63000000
	VEXPANDPS -7(DI), K6, Z7                           // 62f27d4e88bff9ffffff
	VEXPANDPS Z16, K6, Z21                             // 62a27d4e88e8
	VEXPANDPS Z25, K6, Z21                             // 62827d4e88e9
	VEXPANDPS 99(R15)(R15*2), K6, Z21                  // 62827d4e88ac7f63000000
	VEXPANDPS -7(DI), K6, Z21                          // 62e27d4e88aff9ffffff
	VEXTRACTF32X4 $1, Y5, K3, X9                       // 62d37d2b19e901
	VEXTRACTF32X4 $1, Y5, K3, 7(AX)                    // 62f37d2b19a80700000001
	VEXTRACTF32X4 $1, Y5, K3, (DI)                     // 62f37d2b192f01
	VEXTRACTF32X4 $3, Z14, K7, X7                      // 62737d4f19f703
	VEXTRACTF32X4 $3, Z13, K7, X7                      // 62737d4f19ef03
	VEXTRACTF32X4 $3, Z14, K7, 99(R15)(R15*1)          // 62137d4f19b43f6300000003
	VEXTRACTF32X4 $3, Z13, K7, 99(R15)(R15*1)          // 62137d4f19ac3f6300000003
	VEXTRACTF32X4 $3, Z14, K7, (DX)                    // 62737d4f193203
	VEXTRACTF32X4 $3, Z13, K7, (DX)                    // 62737d4f192a03
	VEXTRACTF64X4 $0, Z2, K2, Y16                      // 62b3fd4a1bd000
	VEXTRACTF64X4 $0, Z7, K2, Y16                      // 62b3fd4a1bf800
	VEXTRACTF64X4 $0, Z2, K2, 15(R8)(R14*8)            // 6293fd4a1b94f00f00000000
	VEXTRACTF64X4 $0, Z7, K2, 15(R8)(R14*8)            // 6293fd4a1bbcf00f00000000
	VEXTRACTF64X4 $0, Z2, K2, -15(R14)(R15*2)          // 6293fd4a1b947ef1ffffff00
	VEXTRACTF64X4 $0, Z7, K2, -15(R14)(R15*2)          // 6293fd4a1bbc7ef1ffffff00
	VEXTRACTI32X4 $0, Y9, K5, X31                      // 62137d2d39cf00
	VEXTRACTI32X4 $0, Y9, K5, 7(SI)(DI*1)              // 62737d2d398c3e0700000000
	VEXTRACTI32X4 $0, Y9, K5, 15(DX)(BX*8)             // 62737d2d398cda0f00000000
	VEXTRACTI32X4 $1, Z27, K3, X3                      // 62637d4b39db01
	VEXTRACTI32X4 $1, Z25, K3, X3                      // 62637d4b39cb01
	VEXTRACTI32X4 $1, Z27, K3, -7(DI)(R8*1)            // 62237d4b399c07f9ffffff01
	VEXTRACTI32X4 $1, Z25, K3, -7(DI)(R8*1)            // 62237d4b398c07f9ffffff01
	VEXTRACTI32X4 $1, Z27, K3, (SP)                    // 62637d4b391c2401
	VEXTRACTI32X4 $1, Z25, K3, (SP)                    // 62637d4b390c2401
	VEXTRACTI64X4 $1, Z3, K3, Y6                       // 62f3fd4b3bde01
	VEXTRACTI64X4 $1, Z0, K3, Y6                       // 62f3fd4b3bc601
	VEXTRACTI64X4 $1, Z3, K3, 7(AX)(CX*4)              // 62f3fd4b3b9c880700000001
	VEXTRACTI64X4 $1, Z0, K3, 7(AX)(CX*4)              // 62f3fd4b3b84880700000001
	VEXTRACTI64X4 $1, Z3, K3, 7(AX)(CX*1)              // 62f3fd4b3b9c080700000001
	VEXTRACTI64X4 $1, Z0, K3, 7(AX)(CX*1)              // 62f3fd4b3b84080700000001
	VFIXUPIMMPD $97, X30, X0, K3, X13                  // 6213fd0b54ee61
	VFIXUPIMMPD $97, (AX), X0, K3, X13                 // 6273fd0b542861
	VFIXUPIMMPD $97, 7(SI), X0, K3, X13                // 6273fd0b54ae0700000061
	VFIXUPIMMPD $81, Y6, Y7, K3, Y3                    // 62f3c52b54de51
	VFIXUPIMMPD $81, (SI), Y7, K3, Y3                  // 62f3c52b541e51
	VFIXUPIMMPD $81, 7(SI)(DI*2), Y7, K3, Y3           // 62f3c52b549c7e0700000051
	VFIXUPIMMPD $42, Z22, Z8, K2, Z14                  // 6233bd4a54f62a
	VFIXUPIMMPD $42, Z25, Z8, K2, Z14                  // 6213bd4a54f12a
	VFIXUPIMMPD $42, Z22, Z24, K2, Z14                 // 6233bd4254f62a
	VFIXUPIMMPD $42, Z25, Z24, K2, Z14                 // 6213bd4254f12a
	VFIXUPIMMPD $42, Z22, Z8, K2, Z7                   // 62b3bd4a54fe2a
	VFIXUPIMMPD $42, Z25, Z8, K2, Z7                   // 6293bd4a54f92a
	VFIXUPIMMPD $42, Z22, Z24, K2, Z7                  // 62b3bd4254fe2a
	VFIXUPIMMPD $42, Z25, Z24, K2, Z7                  // 6293bd4254f92a
	VFIXUPIMMPD $79, Z0, Z6, K1, Z1                    // 62f3cd4954c84f
	VFIXUPIMMPD $79, Z8, Z6, K1, Z1                    // 62d3cd4954c84f
	VFIXUPIMMPD $79, -7(CX)(DX*1), Z6, K1, Z1          // 62f3cd49548c11f9ffffff4f
	VFIXUPIMMPD $79, -15(R14)(R15*4), Z6, K1, Z1       // 6293cd49548cbef1ffffff4f
	VFIXUPIMMPD $79, Z0, Z2, K1, Z1                    // 62f3ed4954c84f
	VFIXUPIMMPD $79, Z8, Z2, K1, Z1                    // 62d3ed4954c84f
	VFIXUPIMMPD $79, -7(CX)(DX*1), Z2, K1, Z1          // 62f3ed49548c11f9ffffff4f
	VFIXUPIMMPD $79, -15(R14)(R15*4), Z2, K1, Z1       // 6293ed49548cbef1ffffff4f
	VFIXUPIMMPD $79, Z0, Z6, K1, Z16                   // 62e3cd4954c04f
	VFIXUPIMMPD $79, Z8, Z6, K1, Z16                   // 62c3cd4954c04f
	VFIXUPIMMPD $79, -7(CX)(DX*1), Z6, K1, Z16         // 62e3cd49548411f9ffffff4f
	VFIXUPIMMPD $79, -15(R14)(R15*4), Z6, K1, Z16      // 6283cd495484bef1ffffff4f
	VFIXUPIMMPD $79, Z0, Z2, K1, Z16                   // 62e3ed4954c04f
	VFIXUPIMMPD $79, Z8, Z2, K1, Z16                   // 62c3ed4954c04f
	VFIXUPIMMPD $79, -7(CX)(DX*1), Z2, K1, Z16         // 62e3ed49548411f9ffffff4f
	VFIXUPIMMPD $79, -15(R14)(R15*4), Z2, K1, Z16      // 6283ed495484bef1ffffff4f
	VFIXUPIMMPS $64, X11, X14, K2, X16                 // 62c30d0a54c340
	VFIXUPIMMPS $64, (BX), X14, K2, X16                // 62e30d0a540340
	VFIXUPIMMPS $64, -17(BP)(SI*1), X14, K2, X16       // 62e30d0a548435efffffff40
	VFIXUPIMMPS $27, Y26, Y11, K1, Y26                 // 6203252954d21b
	VFIXUPIMMPS $27, 17(SP)(BP*8), Y11, K1, Y26        // 626325295494ec110000001b
	VFIXUPIMMPS $27, 17(SP)(BP*4), Y11, K1, Y26        // 626325295494ac110000001b
	VFIXUPIMMPS $47, Z11, Z14, K7, Z15                 // 62530d4f54fb2f
	VFIXUPIMMPS $47, Z5, Z14, K7, Z15                  // 62730d4f54fd2f
	VFIXUPIMMPS $47, Z11, Z27, K7, Z15                 // 6253254754fb2f
	VFIXUPIMMPS $47, Z5, Z27, K7, Z15                  // 6273254754fd2f
	VFIXUPIMMPS $47, Z11, Z14, K7, Z12                 // 62530d4f54e32f
	VFIXUPIMMPS $47, Z5, Z14, K7, Z12                  // 62730d4f54e52f
	VFIXUPIMMPS $47, Z11, Z27, K7, Z12                 // 6253254754e32f
	VFIXUPIMMPS $47, Z5, Z27, K7, Z12                  // 6273254754e52f
	VFIXUPIMMPS $82, Z2, Z5, K1, Z13                   // 6273554954ea52
	VFIXUPIMMPS $82, 15(DX)(BX*1), Z5, K1, Z13         // 6273554954ac1a0f00000052
	VFIXUPIMMPS $82, -7(CX)(DX*2), Z5, K1, Z13         // 6273554954ac51f9ffffff52
	VFIXUPIMMPS $82, Z2, Z23, K1, Z13                  // 6273454154ea52
	VFIXUPIMMPS $82, 15(DX)(BX*1), Z23, K1, Z13        // 6273454154ac1a0f00000052
	VFIXUPIMMPS $82, -7(CX)(DX*2), Z23, K1, Z13        // 6273454154ac51f9ffffff52
	VFIXUPIMMPS $82, Z2, Z5, K1, Z14                   // 6273554954f252
	VFIXUPIMMPS $82, 15(DX)(BX*1), Z5, K1, Z14         // 6273554954b41a0f00000052
	VFIXUPIMMPS $82, -7(CX)(DX*2), Z5, K1, Z14         // 6273554954b451f9ffffff52
	VFIXUPIMMPS $82, Z2, Z23, K1, Z14                  // 6273454154f252
	VFIXUPIMMPS $82, 15(DX)(BX*1), Z23, K1, Z14        // 6273454154b41a0f00000052
	VFIXUPIMMPS $82, -7(CX)(DX*2), Z23, K1, Z14        // 6273454154b451f9ffffff52
	VFIXUPIMMSD $126, X8, X19, K1, X14                 // 6253e50155f07e
	VFIXUPIMMSD $94, X23, X26, K1, X8                  // 6233ad0155c75e or 6233ad2155c75e or 6233ad4155c75e
	VFIXUPIMMSD $94, (SI), X26, K1, X8                 // 6273ad0155065e or 6273ad2155065e or 6273ad4155065e
	VFIXUPIMMSD $94, 7(SI)(DI*2), X26, K1, X8          // 6273ad0155847e070000005e or 6273ad2155847e070000005e or 6273ad4155847e070000005e
	VFIXUPIMMSS $121, X23, X16, K7, X12                // 62337d0755e779
	VFIXUPIMMSS $13, X31, X11, K2, X23                 // 6283250a55ff0d or 6283252a55ff0d or 6283254a55ff0d
	VFIXUPIMMSS $13, 17(SP)(BP*2), X11, K2, X23        // 62e3250a55bc6c110000000d or 62e3252a55bc6c110000000d or 62e3254a55bc6c110000000d
	VFIXUPIMMSS $13, -7(DI)(R8*4), X11, K2, X23        // 62a3250a55bc87f9ffffff0d or 62a3252a55bc87f9ffffff0d or 62a3254a55bc87f9ffffff0d
	VFMADD132PD X0, X14, K4, X24                       // 62628d0c98c0
	VFMADD132PD 15(R8)(R14*4), X14, K4, X24            // 62028d0c9884b00f000000
	VFMADD132PD -7(CX)(DX*4), X14, K4, X24             // 62628d0c988491f9ffffff
	VFMADD132PD Y18, Y14, K1, Y12                      // 62328d2998e2
	VFMADD132PD 7(SI)(DI*4), Y14, K1, Y12              // 62728d2998a4be07000000
	VFMADD132PD -7(DI)(R8*2), Y14, K1, Y12             // 62328d2998a447f9ffffff
	VFMADD132PD Z28, Z26, K3, Z6                       // 6292ad4398f4
	VFMADD132PD Z6, Z26, K3, Z6                        // 62f2ad4398f6
	VFMADD132PD Z28, Z14, K3, Z6                       // 62928d4b98f4
	VFMADD132PD Z6, Z14, K3, Z6                        // 62f28d4b98f6
	VFMADD132PD Z28, Z26, K3, Z14                      // 6212ad4398f4
	VFMADD132PD Z6, Z26, K3, Z14                       // 6272ad4398f6
	VFMADD132PD Z28, Z14, K3, Z14                      // 62128d4b98f4
	VFMADD132PD Z6, Z14, K3, Z14                       // 62728d4b98f6
	VFMADD132PD Z3, Z26, K4, Z13                       // 6272ad4498eb
	VFMADD132PD Z0, Z26, K4, Z13                       // 6272ad4498e8
	VFMADD132PD -17(BP), Z26, K4, Z13                  // 6272ad4498adefffffff
	VFMADD132PD -15(R14)(R15*8), Z26, K4, Z13          // 6212ad4498acfef1ffffff
	VFMADD132PD Z3, Z3, K4, Z13                        // 6272e54c98eb
	VFMADD132PD Z0, Z3, K4, Z13                        // 6272e54c98e8
	VFMADD132PD -17(BP), Z3, K4, Z13                   // 6272e54c98adefffffff
	VFMADD132PD -15(R14)(R15*8), Z3, K4, Z13           // 6212e54c98acfef1ffffff
	VFMADD132PD Z3, Z26, K4, Z21                       // 62e2ad4498eb
	VFMADD132PD Z0, Z26, K4, Z21                       // 62e2ad4498e8
	VFMADD132PD -17(BP), Z26, K4, Z21                  // 62e2ad4498adefffffff
	VFMADD132PD -15(R14)(R15*8), Z26, K4, Z21          // 6282ad4498acfef1ffffff
	VFMADD132PD Z3, Z3, K4, Z21                        // 62e2e54c98eb
	VFMADD132PD Z0, Z3, K4, Z21                        // 62e2e54c98e8
	VFMADD132PD -17(BP), Z3, K4, Z21                   // 62e2e54c98adefffffff
	VFMADD132PD -15(R14)(R15*8), Z3, K4, Z21           // 6282e54c98acfef1ffffff
	VFMADD132PS X2, X23, K5, X11                       // 6272450598da
	VFMADD132PS (R8), X23, K5, X11                     // 625245059818
	VFMADD132PS 15(DX)(BX*2), X23, K5, X11             // 62724505989c5a0f000000
	VFMADD132PS Y3, Y18, K7, Y31                       // 62626d2798fb
	VFMADD132PS 17(SP), Y18, K7, Y31                   // 62626d2798bc2411000000
	VFMADD132PS -17(BP)(SI*4), Y18, K7, Y31            // 62626d2798bcb5efffffff
	VFMADD132PS Z3, Z11, K7, Z21                       // 62e2254f98eb
	VFMADD132PS Z12, Z11, K7, Z21                      // 62c2254f98ec
	VFMADD132PS Z3, Z25, K7, Z21                       // 62e2354798eb
	VFMADD132PS Z12, Z25, K7, Z21                      // 62c2354798ec
	VFMADD132PS Z3, Z11, K7, Z13                       // 6272254f98eb
	VFMADD132PS Z12, Z11, K7, Z13                      // 6252254f98ec
	VFMADD132PS Z3, Z25, K7, Z13                       // 6272354798eb
	VFMADD132PS Z12, Z25, K7, Z13                      // 6252354798ec
	VFMADD132PS Z23, Z23, K6, Z27                      // 6222454698df
	VFMADD132PS Z6, Z23, K6, Z27                       // 6262454698de
	VFMADD132PS 17(SP)(BP*2), Z23, K6, Z27             // 62624546989c6c11000000
	VFMADD132PS -7(DI)(R8*4), Z23, K6, Z27             // 62224546989c87f9ffffff
	VFMADD132PS Z23, Z5, K6, Z27                       // 6222554e98df
	VFMADD132PS Z6, Z5, K6, Z27                        // 6262554e98de
	VFMADD132PS 17(SP)(BP*2), Z5, K6, Z27              // 6262554e989c6c11000000
	VFMADD132PS -7(DI)(R8*4), Z5, K6, Z27              // 6222554e989c87f9ffffff
	VFMADD132PS Z23, Z23, K6, Z15                      // 6232454698ff
	VFMADD132PS Z6, Z23, K6, Z15                       // 6272454698fe
	VFMADD132PS 17(SP)(BP*2), Z23, K6, Z15             // 6272454698bc6c11000000
	VFMADD132PS -7(DI)(R8*4), Z23, K6, Z15             // 6232454698bc87f9ffffff
	VFMADD132PS Z23, Z5, K6, Z15                       // 6232554e98ff
	VFMADD132PS Z6, Z5, K6, Z15                        // 6272554e98fe
	VFMADD132PS 17(SP)(BP*2), Z5, K6, Z15              // 6272554e98bc6c11000000
	VFMADD132PS -7(DI)(R8*4), Z5, K6, Z15              // 6232554e98bc87f9ffffff
	VFMADD132SD X25, X5, K3, X20                       // 6282d50b99e1
	VFMADD132SD X13, X9, K7, X0                        // 62d2b50f99c5 or 62d2b52f99c5 or 62d2b54f99c5
	VFMADD132SD 17(SP)(BP*8), X9, K7, X0               // 62f2b50f9984ec11000000 or 62f2b52f9984ec11000000 or 62f2b54f9984ec11000000
	VFMADD132SD 17(SP)(BP*4), X9, K7, X0               // 62f2b50f9984ac11000000 or 62f2b52f9984ac11000000 or 62f2b54f9984ac11000000
	VFMADD132SS X9, X8, K4, X2                         // 62d23d0c99d1
	VFMADD132SS X11, X31, K4, X2                       // 62d2050499d3 or 62d2052499d3 or 62d2054499d3
	VFMADD132SS 15(R8), X31, K4, X2                    // 62d2050499900f000000 or 62d2052499900f000000 or 62d2054499900f000000
	VFMADD132SS (BP), X31, K4, X2                      // 62f20504995500 or 62f20524995500 or 62f20544995500
	VFMADD213PD X14, X5, K7, X22                       // 62c2d50fa8f6
	VFMADD213PD 17(SP)(BP*1), X5, K7, X22              // 62e2d50fa8b42c11000000
	VFMADD213PD -7(CX)(DX*8), X5, K7, X22              // 62e2d50fa8b4d1f9ffffff
	VFMADD213PD Y7, Y2, K2, Y24                        // 6262ed2aa8c7
	VFMADD213PD 7(AX), Y2, K2, Y24                     // 6262ed2aa88007000000
	VFMADD213PD (DI), Y2, K2, Y24                      // 6262ed2aa807
	VFMADD213PD Z16, Z21, K5, Z8                       // 6232d545a8c0
	VFMADD213PD Z13, Z21, K5, Z8                       // 6252d545a8c5
	VFMADD213PD Z16, Z5, K5, Z8                        // 6232d54da8c0
	VFMADD213PD Z13, Z5, K5, Z8                        // 6252d54da8c5
	VFMADD213PD Z16, Z21, K5, Z28                      // 6222d545a8e0
	VFMADD213PD Z13, Z21, K5, Z28                      // 6242d545a8e5
	VFMADD213PD Z16, Z5, K5, Z28                       // 6222d54da8e0
	VFMADD213PD Z13, Z5, K5, Z28                       // 6242d54da8e5
	VFMADD213PD Z6, Z22, K3, Z12                       // 6272cd43a8e6
	VFMADD213PD Z8, Z22, K3, Z12                       // 6252cd43a8e0
	VFMADD213PD 15(R8), Z22, K3, Z12                   // 6252cd43a8a00f000000
	VFMADD213PD (BP), Z22, K3, Z12                     // 6272cd43a86500
	VFMADD213PD Z6, Z11, K3, Z12                       // 6272a54ba8e6
	VFMADD213PD Z8, Z11, K3, Z12                       // 6252a54ba8e0
	VFMADD213PD 15(R8), Z11, K3, Z12                   // 6252a54ba8a00f000000
	VFMADD213PD (BP), Z11, K3, Z12                     // 6272a54ba86500
	VFMADD213PD Z6, Z22, K3, Z27                       // 6262cd43a8de
	VFMADD213PD Z8, Z22, K3, Z27                       // 6242cd43a8d8
	VFMADD213PD 15(R8), Z22, K3, Z27                   // 6242cd43a8980f000000
	VFMADD213PD (BP), Z22, K3, Z27                     // 6262cd43a85d00
	VFMADD213PD Z6, Z11, K3, Z27                       // 6262a54ba8de
	VFMADD213PD Z8, Z11, K3, Z27                       // 6242a54ba8d8
	VFMADD213PD 15(R8), Z11, K3, Z27                   // 6242a54ba8980f000000
	VFMADD213PD (BP), Z11, K3, Z27                     // 6262a54ba85d00
	VFMADD213PS X7, X17, K4, X0                        // 62f27504a8c7
	VFMADD213PS -17(BP)(SI*2), X17, K4, X0             // 62f27504a88475efffffff
	VFMADD213PS 7(AX)(CX*2), X17, K4, X0               // 62f27504a8844807000000
	VFMADD213PS Y8, Y14, K2, Y21                       // 62c20d2aa8e8
	VFMADD213PS 99(R15)(R15*1), Y14, K2, Y21           // 62820d2aa8ac3f63000000
	VFMADD213PS (DX), Y14, K2, Y21                     // 62e20d2aa82a
	VFMADD213PS Z9, Z12, K2, Z25                       // 62421d4aa8c9
	VFMADD213PS Z12, Z12, K2, Z25                      // 62421d4aa8cc
	VFMADD213PS Z9, Z17, K2, Z25                       // 62427542a8c9
	VFMADD213PS Z12, Z17, K2, Z25                      // 62427542a8cc
	VFMADD213PS Z9, Z12, K2, Z12                       // 62521d4aa8e1
	VFMADD213PS Z12, Z12, K2, Z12                      // 62521d4aa8e4
	VFMADD213PS Z9, Z17, K2, Z12                       // 62527542a8e1
	VFMADD213PS Z12, Z17, K2, Z12                      // 62527542a8e4
	VFMADD213PS Z8, Z3, K3, Z6                         // 62d2654ba8f0
	VFMADD213PS Z2, Z3, K3, Z6                         // 62f2654ba8f2
	VFMADD213PS 15(R8)(R14*8), Z3, K3, Z6              // 6292654ba8b4f00f000000
	VFMADD213PS -15(R14)(R15*2), Z3, K3, Z6            // 6292654ba8b47ef1ffffff
	VFMADD213PS Z8, Z21, K3, Z6                        // 62d25543a8f0
	VFMADD213PS Z2, Z21, K3, Z6                        // 62f25543a8f2
	VFMADD213PS 15(R8)(R14*8), Z21, K3, Z6             // 62925543a8b4f00f000000
	VFMADD213PS -15(R14)(R15*2), Z21, K3, Z6           // 62925543a8b47ef1ffffff
	VFMADD213PS Z8, Z3, K3, Z25                        // 6242654ba8c8
	VFMADD213PS Z2, Z3, K3, Z25                        // 6262654ba8ca
	VFMADD213PS 15(R8)(R14*8), Z3, K3, Z25             // 6202654ba88cf00f000000
	VFMADD213PS -15(R14)(R15*2), Z3, K3, Z25           // 6202654ba88c7ef1ffffff
	VFMADD213PS Z8, Z21, K3, Z25                       // 62425543a8c8
	VFMADD213PS Z2, Z21, K3, Z25                       // 62625543a8ca
	VFMADD213PS 15(R8)(R14*8), Z21, K3, Z25            // 62025543a88cf00f000000
	VFMADD213PS -15(R14)(R15*2), Z21, K3, Z25          // 62025543a88c7ef1ffffff
	VFMADD213SD X0, X11, K3, X15                       // 6272a50ba9f8
	VFMADD213SD X27, X8, K3, X18                       // 6282bd0ba9d3 or 6282bd2ba9d3 or 6282bd4ba9d3
	VFMADD213SD 7(SI)(DI*4), X8, K3, X18               // 62e2bd0ba994be07000000 or 62e2bd2ba994be07000000 or 62e2bd4ba994be07000000
	VFMADD213SD -7(DI)(R8*2), X8, K3, X18              // 62a2bd0ba99447f9ffffff or 62a2bd2ba99447f9ffffff or 62a2bd4ba99447f9ffffff
	VFMADD213SS X18, X3, K2, X25                       // 6222650aa9ca
	VFMADD213SS X15, X28, K1, X15                      // 62521d01a9ff or 62521d21a9ff or 62521d41a9ff
	VFMADD213SS 15(R8)(R14*8), X28, K1, X15            // 62121d01a9bcf00f000000 or 62121d21a9bcf00f000000 or 62121d41a9bcf00f000000
	VFMADD213SS -15(R14)(R15*2), X28, K1, X15          // 62121d01a9bc7ef1ffffff or 62121d21a9bc7ef1ffffff or 62121d41a9bc7ef1ffffff
	VFMADD231PD X8, X13, K2, X7                        // 62d2950ab8f8
	VFMADD231PD 15(R8)(R14*1), X13, K2, X7             // 6292950ab8bc300f000000
	VFMADD231PD 15(R8)(R14*2), X13, K2, X7             // 6292950ab8bc700f000000
	VFMADD231PD Y24, Y11, K1, Y20                      // 6282a529b8e0
	VFMADD231PD -17(BP)(SI*8), Y11, K1, Y20            // 62e2a529b8a4f5efffffff
	VFMADD231PD (R15), Y11, K1, Y20                    // 62c2a529b827
	VFMADD231PD Z0, Z7, K7, Z3                         // 62f2c54fb8d8
	VFMADD231PD Z6, Z7, K7, Z3                         // 62f2c54fb8de
	VFMADD231PD Z0, Z9, K7, Z3                         // 62f2b54fb8d8
	VFMADD231PD Z6, Z9, K7, Z3                         // 62f2b54fb8de
	VFMADD231PD Z0, Z7, K7, Z27                        // 6262c54fb8d8
	VFMADD231PD Z6, Z7, K7, Z27                        // 6262c54fb8de
	VFMADD231PD Z0, Z9, K7, Z27                        // 6262b54fb8d8
	VFMADD231PD Z6, Z9, K7, Z27                        // 6262b54fb8de
	VFMADD231PD Z9, Z3, K1, Z20                        // 62c2e549b8e1
	VFMADD231PD Z19, Z3, K1, Z20                       // 62a2e549b8e3
	VFMADD231PD -15(R14)(R15*1), Z3, K1, Z20           // 6282e549b8a43ef1ffffff
	VFMADD231PD -15(BX), Z3, K1, Z20                   // 62e2e549b8a3f1ffffff
	VFMADD231PD Z9, Z30, K1, Z20                       // 62c28d41b8e1
	VFMADD231PD Z19, Z30, K1, Z20                      // 62a28d41b8e3
	VFMADD231PD -15(R14)(R15*1), Z30, K1, Z20          // 62828d41b8a43ef1ffffff
	VFMADD231PD -15(BX), Z30, K1, Z20                  // 62e28d41b8a3f1ffffff
	VFMADD231PD Z9, Z3, K1, Z28                        // 6242e549b8e1
	VFMADD231PD Z19, Z3, K1, Z28                       // 6222e549b8e3
	VFMADD231PD -15(R14)(R15*1), Z3, K1, Z28           // 6202e549b8a43ef1ffffff
	VFMADD231PD -15(BX), Z3, K1, Z28                   // 6262e549b8a3f1ffffff
	VFMADD231PD Z9, Z30, K1, Z28                       // 62428d41b8e1
	VFMADD231PD Z19, Z30, K1, Z28                      // 62228d41b8e3
	VFMADD231PD -15(R14)(R15*1), Z30, K1, Z28          // 62028d41b8a43ef1ffffff
	VFMADD231PD -15(BX), Z30, K1, Z28                  // 62628d41b8a3f1ffffff
	VFMADD231PS X0, X7, K1, X24                        // 62624509b8c0
	VFMADD231PS (R14), X7, K1, X24                     // 62424509b806
	VFMADD231PS -7(DI)(R8*8), X7, K1, X24              // 62224509b884c7f9ffffff
	VFMADD231PS Y18, Y5, K1, Y1                        // 62b25529b8ca
	VFMADD231PS 7(SI)(DI*8), Y5, K1, Y1                // 62f25529b88cfe07000000
	VFMADD231PS -15(R14), Y5, K1, Y1                   // 62d25529b88ef1ffffff
	VFMADD231PS Z18, Z11, K7, Z12                      // 6232254fb8e2
	VFMADD231PS Z24, Z11, K7, Z12                      // 6212254fb8e0
	VFMADD231PS Z18, Z5, K7, Z12                       // 6232554fb8e2
	VFMADD231PS Z24, Z5, K7, Z12                       // 6212554fb8e0
	VFMADD231PS Z18, Z11, K7, Z22                      // 62a2254fb8f2
	VFMADD231PS Z24, Z11, K7, Z22                      // 6282254fb8f0
	VFMADD231PS Z18, Z5, K7, Z22                       // 62a2554fb8f2
	VFMADD231PS Z24, Z5, K7, Z22                       // 6282554fb8f0
	VFMADD231PS Z6, Z7, K2, Z2                         // 62f2454ab8d6
	VFMADD231PS Z16, Z7, K2, Z2                        // 62b2454ab8d0
	VFMADD231PS 7(AX)(CX*4), Z7, K2, Z2                // 62f2454ab8948807000000
	VFMADD231PS 7(AX)(CX*1), Z7, K2, Z2                // 62f2454ab8940807000000
	VFMADD231PS Z6, Z13, K2, Z2                        // 62f2154ab8d6
	VFMADD231PS Z16, Z13, K2, Z2                       // 62b2154ab8d0
	VFMADD231PS 7(AX)(CX*4), Z13, K2, Z2               // 62f2154ab8948807000000
	VFMADD231PS 7(AX)(CX*1), Z13, K2, Z2               // 62f2154ab8940807000000
	VFMADD231PS Z6, Z7, K2, Z21                        // 62e2454ab8ee
	VFMADD231PS Z16, Z7, K2, Z21                       // 62a2454ab8e8
	VFMADD231PS 7(AX)(CX*4), Z7, K2, Z21               // 62e2454ab8ac8807000000
	VFMADD231PS 7(AX)(CX*1), Z7, K2, Z21               // 62e2454ab8ac0807000000
	VFMADD231PS Z6, Z13, K2, Z21                       // 62e2154ab8ee
	VFMADD231PS Z16, Z13, K2, Z21                      // 62a2154ab8e8
	VFMADD231PS 7(AX)(CX*4), Z13, K2, Z21              // 62e2154ab8ac8807000000
	VFMADD231PS 7(AX)(CX*1), Z13, K2, Z21              // 62e2154ab8ac0807000000
	VFMADD231SD X11, X1, K4, X22                       // 62c2f50cb9f3
	VFMADD231SD X8, X7, K1, X6                         // 62d2c509b9f0 or 62d2c529b9f0 or 62d2c549b9f0
	VFMADD231SD 17(SP), X7, K1, X6                     // 62f2c509b9b42411000000 or 62f2c529b9b42411000000 or 62f2c549b9b42411000000
	VFMADD231SD -17(BP)(SI*4), X7, K1, X6              // 62f2c509b9b4b5efffffff or 62f2c529b9b4b5efffffff or 62f2c549b9b4b5efffffff
	VFMADD231SS X28, X3, K3, X31                       // 6202650bb9fc
	VFMADD231SS X7, X24, K4, X20                       // 62e23d04b9e7 or 62e23d24b9e7 or 62e23d44b9e7
	VFMADD231SS -15(R14)(R15*1), X24, K4, X20          // 62823d04b9a43ef1ffffff or 62823d24b9a43ef1ffffff or 62823d44b9a43ef1ffffff
	VFMADD231SS -15(BX), X24, K4, X20                  // 62e23d04b9a3f1ffffff or 62e23d24b9a3f1ffffff or 62e23d44b9a3f1ffffff
	VFMADDSUB132PD X12, X16, K5, X20                   // 62c2fd0596e4
	VFMADDSUB132PD 99(R15)(R15*4), X16, K5, X20        // 6282fd0596a4bf63000000
	VFMADDSUB132PD 15(DX), X16, K5, X20                // 62e2fd0596a20f000000
	VFMADDSUB132PD Y9, Y20, K7, Y20                    // 62c2dd2796e1
	VFMADDSUB132PD 7(SI)(DI*1), Y20, K7, Y20           // 62e2dd2796a43e07000000
	VFMADDSUB132PD 15(DX)(BX*8), Y20, K7, Y20          // 62e2dd2796a4da0f000000
	VFMADDSUB132PD Z13, Z1, K7, Z6                     // 62d2f54f96f5
	VFMADDSUB132PD Z13, Z15, K7, Z6                    // 62d2854f96f5
	VFMADDSUB132PD Z13, Z1, K7, Z22                    // 62c2f54f96f5
	VFMADDSUB132PD Z13, Z15, K7, Z22                   // 62c2854f96f5
	VFMADDSUB132PD Z2, Z22, K6, Z18                    // 62e2cd4696d2
	VFMADDSUB132PD Z31, Z22, K6, Z18                   // 6282cd4696d7
	VFMADDSUB132PD (SI), Z22, K6, Z18                  // 62e2cd469616
	VFMADDSUB132PD 7(SI)(DI*2), Z22, K6, Z18           // 62e2cd4696947e07000000
	VFMADDSUB132PD Z2, Z7, K6, Z18                     // 62e2c54e96d2
	VFMADDSUB132PD Z31, Z7, K6, Z18                    // 6282c54e96d7
	VFMADDSUB132PD (SI), Z7, K6, Z18                   // 62e2c54e9616
	VFMADDSUB132PD 7(SI)(DI*2), Z7, K6, Z18            // 62e2c54e96947e07000000
	VFMADDSUB132PD Z2, Z22, K6, Z8                     // 6272cd4696c2
	VFMADDSUB132PD Z31, Z22, K6, Z8                    // 6212cd4696c7
	VFMADDSUB132PD (SI), Z22, K6, Z8                   // 6272cd469606
	VFMADDSUB132PD 7(SI)(DI*2), Z22, K6, Z8            // 6272cd4696847e07000000
	VFMADDSUB132PD Z2, Z7, K6, Z8                      // 6272c54e96c2
	VFMADDSUB132PD Z31, Z7, K6, Z8                     // 6212c54e96c7
	VFMADDSUB132PD (SI), Z7, K6, Z8                    // 6272c54e9606
	VFMADDSUB132PD 7(SI)(DI*2), Z7, K6, Z8             // 6272c54e96847e07000000
	VFMADDSUB132PS X28, X17, K3, X6                    // 6292750396f4
	VFMADDSUB132PS (CX), X17, K3, X6                   // 62f275039631
	VFMADDSUB132PS 99(R15), X17, K3, X6                // 62d2750396b763000000
	VFMADDSUB132PS Y1, Y28, K7, Y28                    // 62621d2796e1
	VFMADDSUB132PS -7(DI)(R8*1), Y28, K7, Y28          // 62221d2796a407f9ffffff
	VFMADDSUB132PS (SP), Y28, K7, Y28                  // 62621d27962424
	VFMADDSUB132PS Z12, Z1, K4, Z20                    // 62c2754c96e4
	VFMADDSUB132PS Z16, Z1, K4, Z20                    // 62a2754c96e0
	VFMADDSUB132PS Z12, Z3, K4, Z20                    // 62c2654c96e4
	VFMADDSUB132PS Z16, Z3, K4, Z20                    // 62a2654c96e0
	VFMADDSUB132PS Z12, Z1, K4, Z9                     // 6252754c96cc
	VFMADDSUB132PS Z16, Z1, K4, Z9                     // 6232754c96c8
	VFMADDSUB132PS Z12, Z3, K4, Z9                     // 6252654c96cc
	VFMADDSUB132PS Z16, Z3, K4, Z9                     // 6232654c96c8
	VFMADDSUB132PS Z3, Z14, K4, Z28                    // 62620d4c96e3
	VFMADDSUB132PS Z12, Z14, K4, Z28                   // 62420d4c96e4
	VFMADDSUB132PS 17(SP)(BP*8), Z14, K4, Z28          // 62620d4c96a4ec11000000
	VFMADDSUB132PS 17(SP)(BP*4), Z14, K4, Z28          // 62620d4c96a4ac11000000
	VFMADDSUB132PS Z3, Z28, K4, Z28                    // 62621d4496e3
	VFMADDSUB132PS Z12, Z28, K4, Z28                   // 62421d4496e4
	VFMADDSUB132PS 17(SP)(BP*8), Z28, K4, Z28          // 62621d4496a4ec11000000
	VFMADDSUB132PS 17(SP)(BP*4), Z28, K4, Z28          // 62621d4496a4ac11000000
	VFMADDSUB132PS Z3, Z14, K4, Z13                    // 62720d4c96eb
	VFMADDSUB132PS Z12, Z14, K4, Z13                   // 62520d4c96ec
	VFMADDSUB132PS 17(SP)(BP*8), Z14, K4, Z13          // 62720d4c96acec11000000
	VFMADDSUB132PS 17(SP)(BP*4), Z14, K4, Z13          // 62720d4c96acac11000000
	VFMADDSUB132PS Z3, Z28, K4, Z13                    // 62721d4496eb
	VFMADDSUB132PS Z12, Z28, K4, Z13                   // 62521d4496ec
	VFMADDSUB132PS 17(SP)(BP*8), Z28, K4, Z13          // 62721d4496acec11000000
	VFMADDSUB132PS 17(SP)(BP*4), Z28, K4, Z13          // 62721d4496acac11000000
	VFMADDSUB213PD X8, X1, K7, X6                      // 62d2f50fa6f0
	VFMADDSUB213PD 99(R15)(R15*2), X1, K7, X6          // 6292f50fa6b47f63000000
	VFMADDSUB213PD -7(DI), X1, K7, X6                  // 62f2f50fa6b7f9ffffff
	VFMADDSUB213PD Y27, Y11, K2, Y8                    // 6212a52aa6c3
	VFMADDSUB213PD -7(CX), Y11, K2, Y8                 // 6272a52aa681f9ffffff
	VFMADDSUB213PD 15(DX)(BX*4), Y11, K2, Y8           // 6272a52aa6849a0f000000
	VFMADDSUB213PD Z5, Z19, K5, Z15                    // 6272e545a6fd
	VFMADDSUB213PD Z1, Z19, K5, Z15                    // 6272e545a6f9
	VFMADDSUB213PD Z5, Z15, K5, Z15                    // 6272854da6fd
	VFMADDSUB213PD Z1, Z15, K5, Z15                    // 6272854da6f9
	VFMADDSUB213PD Z5, Z19, K5, Z30                    // 6262e545a6f5
	VFMADDSUB213PD Z1, Z19, K5, Z30                    // 6262e545a6f1
	VFMADDSUB213PD Z5, Z15, K5, Z30                    // 6262854da6f5
	VFMADDSUB213PD Z1, Z15, K5, Z30                    // 6262854da6f1
	VFMADDSUB213PD Z21, Z14, K3, Z3                    // 62b28d4ba6dd
	VFMADDSUB213PD Z8, Z14, K3, Z3                     // 62d28d4ba6d8
	VFMADDSUB213PD 7(SI)(DI*4), Z14, K3, Z3            // 62f28d4ba69cbe07000000
	VFMADDSUB213PD -7(DI)(R8*2), Z14, K3, Z3           // 62b28d4ba69c47f9ffffff
	VFMADDSUB213PD Z21, Z15, K3, Z3                    // 62b2854ba6dd
	VFMADDSUB213PD Z8, Z15, K3, Z3                     // 62d2854ba6d8
	VFMADDSUB213PD 7(SI)(DI*4), Z15, K3, Z3            // 62f2854ba69cbe07000000
	VFMADDSUB213PD -7(DI)(R8*2), Z15, K3, Z3           // 62b2854ba69c47f9ffffff
	VFMADDSUB213PD Z21, Z14, K3, Z5                    // 62b28d4ba6ed
	VFMADDSUB213PD Z8, Z14, K3, Z5                     // 62d28d4ba6e8
	VFMADDSUB213PD 7(SI)(DI*4), Z14, K3, Z5            // 62f28d4ba6acbe07000000
	VFMADDSUB213PD -7(DI)(R8*2), Z14, K3, Z5           // 62b28d4ba6ac47f9ffffff
	VFMADDSUB213PD Z21, Z15, K3, Z5                    // 62b2854ba6ed
	VFMADDSUB213PD Z8, Z15, K3, Z5                     // 62d2854ba6e8
	VFMADDSUB213PD 7(SI)(DI*4), Z15, K3, Z5            // 62f2854ba6acbe07000000
	VFMADDSUB213PD -7(DI)(R8*2), Z15, K3, Z5           // 62b2854ba6ac47f9ffffff
	VFMADDSUB213PS X0, X6, K4, X8                      // 62724d0ca6c0
	VFMADDSUB213PS -7(CX)(DX*1), X6, K4, X8            // 62724d0ca68411f9ffffff
	VFMADDSUB213PS -15(R14)(R15*4), X6, K4, X8         // 62124d0ca684bef1ffffff
	VFMADDSUB213PS Y12, Y16, K2, Y17                   // 62c27d22a6cc
	VFMADDSUB213PS 99(R15)(R15*8), Y16, K2, Y17        // 62827d22a68cff63000000
	VFMADDSUB213PS 7(AX)(CX*8), Y16, K2, Y17           // 62e27d22a68cc807000000
	VFMADDSUB213PS Z23, Z20, K2, Z16                   // 62a25d42a6c7
	VFMADDSUB213PS Z19, Z20, K2, Z16                   // 62a25d42a6c3
	VFMADDSUB213PS Z23, Z0, K2, Z16                    // 62a27d4aa6c7
	VFMADDSUB213PS Z19, Z0, K2, Z16                    // 62a27d4aa6c3
	VFMADDSUB213PS Z23, Z20, K2, Z9                    // 62325d42a6cf
	VFMADDSUB213PS Z19, Z20, K2, Z9                    // 62325d42a6cb
	VFMADDSUB213PS Z23, Z0, K2, Z9                     // 62327d4aa6cf
	VFMADDSUB213PS Z19, Z0, K2, Z9                     // 62327d4aa6cb
	VFMADDSUB213PS Z24, Z0, K3, Z0                     // 62927d4ba6c0
	VFMADDSUB213PS Z12, Z0, K3, Z0                     // 62d27d4ba6c4
	VFMADDSUB213PS 17(SP), Z0, K3, Z0                  // 62f27d4ba6842411000000
	VFMADDSUB213PS -17(BP)(SI*4), Z0, K3, Z0           // 62f27d4ba684b5efffffff
	VFMADDSUB213PS Z24, Z25, K3, Z0                    // 62923543a6c0
	VFMADDSUB213PS Z12, Z25, K3, Z0                    // 62d23543a6c4
	VFMADDSUB213PS 17(SP), Z25, K3, Z0                 // 62f23543a6842411000000
	VFMADDSUB213PS -17(BP)(SI*4), Z25, K3, Z0          // 62f23543a684b5efffffff
	VFMADDSUB213PS Z24, Z0, K3, Z11                    // 62127d4ba6d8
	VFMADDSUB213PS Z12, Z0, K3, Z11                    // 62527d4ba6dc
	VFMADDSUB213PS 17(SP), Z0, K3, Z11                 // 62727d4ba69c2411000000
	VFMADDSUB213PS -17(BP)(SI*4), Z0, K3, Z11          // 62727d4ba69cb5efffffff
	VFMADDSUB213PS Z24, Z25, K3, Z11                   // 62123543a6d8
	VFMADDSUB213PS Z12, Z25, K3, Z11                   // 62523543a6dc
	VFMADDSUB213PS 17(SP), Z25, K3, Z11                // 62723543a69c2411000000
	VFMADDSUB213PS -17(BP)(SI*4), Z25, K3, Z11         // 62723543a69cb5efffffff
	VFMADDSUB231PD X6, X16, K3, X11                    // 6272fd03b6de
	VFMADDSUB231PD 15(DX)(BX*1), X16, K3, X11          // 6272fd03b69c1a0f000000
	VFMADDSUB231PD -7(CX)(DX*2), X16, K3, X11          // 6272fd03b69c51f9ffffff
	VFMADDSUB231PD Y3, Y26, K3, Y6                     // 62f2ad23b6f3
	VFMADDSUB231PD (AX), Y26, K3, Y6                   // 62f2ad23b630
	VFMADDSUB231PD 7(SI), Y26, K3, Y6                  // 62f2ad23b6b607000000
	VFMADDSUB231PD Z9, Z9, K2, Z0                      // 62d2b54ab6c1
	VFMADDSUB231PD Z25, Z9, K2, Z0                     // 6292b54ab6c1
	VFMADDSUB231PD Z9, Z3, K2, Z0                      // 62d2e54ab6c1
	VFMADDSUB231PD Z25, Z3, K2, Z0                     // 6292e54ab6c1
	VFMADDSUB231PD Z9, Z9, K2, Z26                     // 6242b54ab6d1
	VFMADDSUB231PD Z25, Z9, K2, Z26                    // 6202b54ab6d1
	VFMADDSUB231PD Z9, Z3, K2, Z26                     // 6242e54ab6d1
	VFMADDSUB231PD Z25, Z3, K2, Z26                    // 6202e54ab6d1
	VFMADDSUB231PD Z17, Z20, K1, Z9                    // 6232dd41b6c9
	VFMADDSUB231PD Z0, Z20, K1, Z9                     // 6272dd41b6c8
	VFMADDSUB231PD 7(AX), Z20, K1, Z9                  // 6272dd41b68807000000
	VFMADDSUB231PD (DI), Z20, K1, Z9                   // 6272dd41b60f
	VFMADDSUB231PD Z17, Z0, K1, Z9                     // 6232fd49b6c9
	VFMADDSUB231PD Z0, Z0, K1, Z9                      // 6272fd49b6c8
	VFMADDSUB231PD 7(AX), Z0, K1, Z9                   // 6272fd49b68807000000
	VFMADDSUB231PD (DI), Z0, K1, Z9                    // 6272fd49b60f
	VFMADDSUB231PD Z17, Z20, K1, Z28                   // 6222dd41b6e1
	VFMADDSUB231PD Z0, Z20, K1, Z28                    // 6262dd41b6e0
	VFMADDSUB231PD 7(AX), Z20, K1, Z28                 // 6262dd41b6a007000000
	VFMADDSUB231PD (DI), Z20, K1, Z28                  // 6262dd41b627
	VFMADDSUB231PD Z17, Z0, K1, Z28                    // 6222fd49b6e1
	VFMADDSUB231PD Z0, Z0, K1, Z28                     // 6262fd49b6e0
	VFMADDSUB231PD 7(AX), Z0, K1, Z28                  // 6262fd49b6a007000000
	VFMADDSUB231PD (DI), Z0, K1, Z28                   // 6262fd49b627
	VFMADDSUB231PS X12, X22, K2, X6                    // 62d24d02b6f4
	VFMADDSUB231PS -17(BP), X22, K2, X6                // 62f24d02b6b5efffffff
	VFMADDSUB231PS -15(R14)(R15*8), X22, K2, X6        // 62924d02b6b4fef1ffffff
	VFMADDSUB231PS Y1, Y28, K1, Y8                     // 62721d21b6c1
	VFMADDSUB231PS (BX), Y28, K1, Y8                   // 62721d21b603
	VFMADDSUB231PS -17(BP)(SI*1), Y28, K1, Y8          // 62721d21b68435efffffff
	VFMADDSUB231PS Z21, Z31, K7, Z17                   // 62a20547b6cd
	VFMADDSUB231PS Z9, Z31, K7, Z17                    // 62c20547b6c9
	VFMADDSUB231PS Z21, Z0, K7, Z17                    // 62a27d4fb6cd
	VFMADDSUB231PS Z9, Z0, K7, Z17                     // 62c27d4fb6c9
	VFMADDSUB231PS Z21, Z31, K7, Z23                   // 62a20547b6fd
	VFMADDSUB231PS Z9, Z31, K7, Z23                    // 62c20547b6f9
	VFMADDSUB231PS Z21, Z0, K7, Z23                    // 62a27d4fb6fd
	VFMADDSUB231PS Z9, Z0, K7, Z23                     // 62c27d4fb6f9
	VFMADDSUB231PS Z20, Z1, K1, Z6                     // 62b27549b6f4
	VFMADDSUB231PS Z9, Z1, K1, Z6                      // 62d27549b6f1
	VFMADDSUB231PS 99(R15)(R15*1), Z1, K1, Z6          // 62927549b6b43f63000000
	VFMADDSUB231PS (DX), Z1, K1, Z6                    // 62f27549b632
	VFMADDSUB231PS Z20, Z9, K1, Z6                     // 62b23549b6f4
	VFMADDSUB231PS Z9, Z9, K1, Z6                      // 62d23549b6f1
	VFMADDSUB231PS 99(R15)(R15*1), Z9, K1, Z6          // 62923549b6b43f63000000
	VFMADDSUB231PS (DX), Z9, K1, Z6                    // 62f23549b632
	VFMADDSUB231PS Z20, Z1, K1, Z9                     // 62327549b6cc
	VFMADDSUB231PS Z9, Z1, K1, Z9                      // 62527549b6c9
	VFMADDSUB231PS 99(R15)(R15*1), Z1, K1, Z9          // 62127549b68c3f63000000
	VFMADDSUB231PS (DX), Z1, K1, Z9                    // 62727549b60a
	VFMADDSUB231PS Z20, Z9, K1, Z9                     // 62323549b6cc
	VFMADDSUB231PS Z9, Z9, K1, Z9                      // 62523549b6c9
	VFMADDSUB231PS 99(R15)(R15*1), Z9, K1, Z9          // 62123549b68c3f63000000
	VFMADDSUB231PS (DX), Z9, K1, Z9                    // 62723549b60a
	VFMSUB132PD X8, X28, K1, X16                       // 62c29d019ac0
	VFMSUB132PD 17(SP)(BP*2), X28, K1, X16             // 62e29d019a846c11000000
	VFMSUB132PD -7(DI)(R8*4), X28, K1, X16             // 62a29d019a8487f9ffffff
	VFMSUB132PD Y31, Y14, K1, Y23                      // 62828d299aff
	VFMSUB132PD 15(R8)(R14*4), Y14, K1, Y23            // 62828d299abcb00f000000
	VFMSUB132PD -7(CX)(DX*4), Y14, K1, Y23             // 62e28d299abc91f9ffffff
	VFMSUB132PD Z7, Z26, K7, Z30                       // 6262ad479af7
	VFMSUB132PD Z21, Z26, K7, Z30                      // 6222ad479af5
	VFMSUB132PD Z7, Z22, K7, Z30                       // 6262cd479af7
	VFMSUB132PD Z21, Z22, K7, Z30                      // 6222cd479af5
	VFMSUB132PD Z7, Z26, K7, Z5                        // 62f2ad479aef
	VFMSUB132PD Z21, Z26, K7, Z5                       // 62b2ad479aed
	VFMSUB132PD Z7, Z22, K7, Z5                        // 62f2cd479aef
	VFMSUB132PD Z21, Z22, K7, Z5                       // 62b2cd479aed
	VFMSUB132PD Z12, Z14, K2, Z16                      // 62c28d4a9ac4
	VFMSUB132PD Z13, Z14, K2, Z16                      // 62c28d4a9ac5
	VFMSUB132PD -17(BP)(SI*8), Z14, K2, Z16            // 62e28d4a9a84f5efffffff
	VFMSUB132PD (R15), Z14, K2, Z16                    // 62c28d4a9a07
	VFMSUB132PD Z12, Z13, K2, Z16                      // 62c2954a9ac4
	VFMSUB132PD Z13, Z13, K2, Z16                      // 62c2954a9ac5
	VFMSUB132PD -17(BP)(SI*8), Z13, K2, Z16            // 62e2954a9a84f5efffffff
	VFMSUB132PD (R15), Z13, K2, Z16                    // 62c2954a9a07
	VFMSUB132PD Z12, Z14, K2, Z25                      // 62428d4a9acc
	VFMSUB132PD Z13, Z14, K2, Z25                      // 62428d4a9acd
	VFMSUB132PD -17(BP)(SI*8), Z14, K2, Z25            // 62628d4a9a8cf5efffffff
	VFMSUB132PD (R15), Z14, K2, Z25                    // 62428d4a9a0f
	VFMSUB132PD Z12, Z13, K2, Z25                      // 6242954a9acc
	VFMSUB132PD Z13, Z13, K2, Z25                      // 6242954a9acd
	VFMSUB132PD -17(BP)(SI*8), Z13, K2, Z25            // 6262954a9a8cf5efffffff
	VFMSUB132PD (R15), Z13, K2, Z25                    // 6242954a9a0f
	VFMSUB132PS X1, X11, K4, X15                       // 6272250c9af9
	VFMSUB132PS 15(R8), X11, K4, X15                   // 6252250c9ab80f000000
	VFMSUB132PS (BP), X11, K4, X15                     // 6272250c9a7d00
	VFMSUB132PS Y22, Y2, K1, Y25                       // 62226d299ace
	VFMSUB132PS (R8), Y2, K1, Y25                      // 62426d299a08
	VFMSUB132PS 15(DX)(BX*2), Y2, K1, Y25              // 62626d299a8c5a0f000000
	VFMSUB132PS Z27, Z2, K3, Z21                       // 62826d4b9aeb
	VFMSUB132PS Z25, Z2, K3, Z21                       // 62826d4b9ae9
	VFMSUB132PS Z27, Z7, K3, Z21                       // 6282454b9aeb
	VFMSUB132PS Z25, Z7, K3, Z21                       // 6282454b9ae9
	VFMSUB132PS Z27, Z2, K3, Z9                        // 62126d4b9acb
	VFMSUB132PS Z25, Z2, K3, Z9                        // 62126d4b9ac9
	VFMSUB132PS Z27, Z7, K3, Z9                        // 6212454b9acb
	VFMSUB132PS Z25, Z7, K3, Z9                        // 6212454b9ac9
	VFMSUB132PS Z3, Z27, K4, Z23                       // 62e225449afb
	VFMSUB132PS Z0, Z27, K4, Z23                       // 62e225449af8
	VFMSUB132PS 7(SI)(DI*8), Z27, K4, Z23              // 62e225449abcfe07000000
	VFMSUB132PS -15(R14), Z27, K4, Z23                 // 62c225449abef1ffffff
	VFMSUB132PS Z3, Z14, K4, Z23                       // 62e20d4c9afb
	VFMSUB132PS Z0, Z14, K4, Z23                       // 62e20d4c9af8
	VFMSUB132PS 7(SI)(DI*8), Z14, K4, Z23              // 62e20d4c9abcfe07000000
	VFMSUB132PS -15(R14), Z14, K4, Z23                 // 62c20d4c9abef1ffffff
	VFMSUB132PS Z3, Z27, K4, Z9                        // 627225449acb
	VFMSUB132PS Z0, Z27, K4, Z9                        // 627225449ac8
	VFMSUB132PS 7(SI)(DI*8), Z27, K4, Z9               // 627225449a8cfe07000000
	VFMSUB132PS -15(R14), Z27, K4, Z9                  // 625225449a8ef1ffffff
	VFMSUB132PS Z3, Z14, K4, Z9                        // 62720d4c9acb
	VFMSUB132PS Z0, Z14, K4, Z9                        // 62720d4c9ac8
	VFMSUB132PS 7(SI)(DI*8), Z14, K4, Z9               // 62720d4c9a8cfe07000000
	VFMSUB132PS -15(R14), Z14, K4, Z9                  // 62520d4c9a8ef1ffffff
	VFMSUB132SD X2, X13, K5, X19                       // 62e2950d9bda
	VFMSUB132SD X0, X0, K7, X14                        // 6272fd0f9bf0 or 6272fd2f9bf0 or 6272fd4f9bf0
	VFMSUB132SD 7(AX), X0, K7, X14                     // 6272fd0f9bb007000000 or 6272fd2f9bb007000000 or 6272fd4f9bb007000000
	VFMSUB132SD (DI), X0, K7, X14                      // 6272fd0f9b37 or 6272fd2f9b37 or 6272fd4f9b37
	VFMSUB132SS X17, X11, K7, X25                      // 6222250f9bc9
	VFMSUB132SS X9, X11, K6, X18                       // 62c2250e9bd1 or 62c2252e9bd1 or 62c2254e9bd1
	VFMSUB132SS 7(AX)(CX*4), X11, K6, X18              // 62e2250e9b948807000000 or 62e2252e9b948807000000 or 62e2254e9b948807000000
	VFMSUB132SS 7(AX)(CX*1), X11, K6, X18              // 62e2250e9b940807000000 or 62e2252e9b940807000000 or 62e2254e9b940807000000
	VFMSUB213PD X2, X24, K3, X2                        // 62f2bd03aad2
	VFMSUB213PD 15(R8)(R14*8), X24, K3, X2             // 6292bd03aa94f00f000000
	VFMSUB213PD -15(R14)(R15*2), X24, K3, X2           // 6292bd03aa947ef1ffffff
	VFMSUB213PD Y9, Y8, K7, Y27                        // 6242bd2faad9
	VFMSUB213PD 17(SP)(BP*1), Y8, K7, Y27              // 6262bd2faa9c2c11000000
	VFMSUB213PD -7(CX)(DX*8), Y8, K7, Y27              // 6262bd2faa9cd1f9ffffff
	VFMSUB213PD Z22, Z8, K4, Z14                       // 6232bd4caaf6
	VFMSUB213PD Z25, Z8, K4, Z14                       // 6212bd4caaf1
	VFMSUB213PD Z22, Z24, K4, Z14                      // 6232bd44aaf6
	VFMSUB213PD Z25, Z24, K4, Z14                      // 6212bd44aaf1
	VFMSUB213PD Z22, Z8, K4, Z7                        // 62b2bd4caafe
	VFMSUB213PD Z25, Z8, K4, Z7                        // 6292bd4caaf9
	VFMSUB213PD Z22, Z24, K4, Z7                       // 62b2bd44aafe
	VFMSUB213PD Z25, Z24, K4, Z7                       // 6292bd44aaf9
	VFMSUB213PD Z0, Z6, K4, Z1                         // 62f2cd4caac8
	VFMSUB213PD Z8, Z6, K4, Z1                         // 62d2cd4caac8
	VFMSUB213PD 7(SI)(DI*1), Z6, K4, Z1                // 62f2cd4caa8c3e07000000
	VFMSUB213PD 15(DX)(BX*8), Z6, K4, Z1               // 62f2cd4caa8cda0f000000
	VFMSUB213PD Z0, Z2, K4, Z1                         // 62f2ed4caac8
	VFMSUB213PD Z8, Z2, K4, Z1                         // 62d2ed4caac8
	VFMSUB213PD 7(SI)(DI*1), Z2, K4, Z1                // 62f2ed4caa8c3e07000000
	VFMSUB213PD 15(DX)(BX*8), Z2, K4, Z1               // 62f2ed4caa8cda0f000000
	VFMSUB213PD Z0, Z6, K4, Z16                        // 62e2cd4caac0
	VFMSUB213PD Z8, Z6, K4, Z16                        // 62c2cd4caac0
	VFMSUB213PD 7(SI)(DI*1), Z6, K4, Z16               // 62e2cd4caa843e07000000
	VFMSUB213PD 15(DX)(BX*8), Z6, K4, Z16              // 62e2cd4caa84da0f000000
	VFMSUB213PD Z0, Z2, K4, Z16                        // 62e2ed4caac0
	VFMSUB213PD Z8, Z2, K4, Z16                        // 62c2ed4caac0
	VFMSUB213PD 7(SI)(DI*1), Z2, K4, Z16               // 62e2ed4caa843e07000000
	VFMSUB213PD 15(DX)(BX*8), Z2, K4, Z16              // 62e2ed4caa84da0f000000
	VFMSUB213PS X26, X27, K7, X2                       // 62922507aad2
	VFMSUB213PS -15(R14)(R15*1), X27, K7, X2           // 62922507aa943ef1ffffff
	VFMSUB213PS -15(BX), X27, K7, X2                   // 62f22507aa93f1ffffff
	VFMSUB213PS Y14, Y9, K2, Y22                       // 62c2352aaaf6
	VFMSUB213PS -17(BP)(SI*2), Y9, K2, Y22             // 62e2352aaab475efffffff
	VFMSUB213PS 7(AX)(CX*2), Y9, K2, Y22               // 62e2352aaab44807000000
	VFMSUB213PS Z11, Z14, K5, Z15                      // 62520d4daafb
	VFMSUB213PS Z5, Z14, K5, Z15                       // 62720d4daafd
	VFMSUB213PS Z11, Z27, K5, Z15                      // 62522545aafb
	VFMSUB213PS Z5, Z27, K5, Z15                       // 62722545aafd
	VFMSUB213PS Z11, Z14, K5, Z12                      // 62520d4daae3
	VFMSUB213PS Z5, Z14, K5, Z12                       // 62720d4daae5
	VFMSUB213PS Z11, Z27, K5, Z12                      // 62522545aae3
	VFMSUB213PS Z5, Z27, K5, Z12                       // 62722545aae5
	VFMSUB213PS Z2, Z5, K3, Z13                        // 6272554baaea
	VFMSUB213PS -7(DI)(R8*1), Z5, K3, Z13              // 6232554baaac07f9ffffff
	VFMSUB213PS (SP), Z5, K3, Z13                      // 6272554baa2c24
	VFMSUB213PS Z2, Z23, K3, Z13                       // 62724543aaea
	VFMSUB213PS -7(DI)(R8*1), Z23, K3, Z13             // 62324543aaac07f9ffffff
	VFMSUB213PS (SP), Z23, K3, Z13                     // 62724543aa2c24
	VFMSUB213PS Z2, Z5, K3, Z14                        // 6272554baaf2
	VFMSUB213PS -7(DI)(R8*1), Z5, K3, Z14              // 6232554baab407f9ffffff
	VFMSUB213PS (SP), Z5, K3, Z14                      // 6272554baa3424
	VFMSUB213PS Z2, Z23, K3, Z14                       // 62724543aaf2
	VFMSUB213PS -7(DI)(R8*1), Z23, K3, Z14             // 62324543aab407f9ffffff
	VFMSUB213PS (SP), Z23, K3, Z14                     // 62724543aa3424
	VFMSUB213SD X3, X30, K4, X22                       // 62e28d04abf3
	VFMSUB213SD X30, X15, K2, X11                      // 6212850aabde or 6212852aabde or 6212854aabde
	VFMSUB213SD 99(R15)(R15*1), X15, K2, X11           // 6212850aab9c3f63000000 or 6212852aab9c3f63000000 or 6212854aab9c3f63000000
	VFMSUB213SD (DX), X15, K2, X11                     // 6272850aab1a or 6272852aab1a or 6272854aab1a
	VFMSUB213SS X12, X6, K2, X13                       // 62524d0aabec
	VFMSUB213SS X8, X30, K3, X23                       // 62c20d03abf8 or 62c20d23abf8 or 62c20d43abf8
	VFMSUB213SS (SI), X30, K3, X23                     // 62e20d03ab3e or 62e20d23ab3e or 62e20d43ab3e
	VFMSUB213SS 7(SI)(DI*2), X30, K3, X23              // 62e20d03abbc7e07000000 or 62e20d23abbc7e07000000 or 62e20d43abbc7e07000000
	VFMSUB231PD X9, X2, K3, X20                        // 62c2ed0bbae1
	VFMSUB231PD 7(AX)(CX*4), X2, K3, X20               // 62e2ed0bbaa48807000000
	VFMSUB231PD 7(AX)(CX*1), X2, K3, X20               // 62e2ed0bbaa40807000000
	VFMSUB231PD Y1, Y6, K3, Y1                         // 62f2cd2bbac9
	VFMSUB231PD 15(R8)(R14*1), Y6, K3, Y1              // 6292cd2bba8c300f000000
	VFMSUB231PD 15(R8)(R14*2), Y6, K3, Y1              // 6292cd2bba8c700f000000
	VFMSUB231PD Z28, Z26, K2, Z6                       // 6292ad42baf4
	VFMSUB231PD Z6, Z26, K2, Z6                        // 62f2ad42baf6
	VFMSUB231PD Z28, Z14, K2, Z6                       // 62928d4abaf4
	VFMSUB231PD Z6, Z14, K2, Z6                        // 62f28d4abaf6
	VFMSUB231PD Z28, Z26, K2, Z14                      // 6212ad42baf4
	VFMSUB231PD Z6, Z26, K2, Z14                       // 6272ad42baf6
	VFMSUB231PD Z28, Z14, K2, Z14                      // 62128d4abaf4
	VFMSUB231PD Z6, Z14, K2, Z14                       // 62728d4abaf6
	VFMSUB231PD Z3, Z26, K1, Z13                       // 6272ad41baeb
	VFMSUB231PD Z0, Z26, K1, Z13                       // 6272ad41bae8
	VFMSUB231PD -7(CX), Z26, K1, Z13                   // 6272ad41baa9f9ffffff
	VFMSUB231PD 15(DX)(BX*4), Z26, K1, Z13             // 6272ad41baac9a0f000000
	VFMSUB231PD Z3, Z3, K1, Z13                        // 6272e549baeb
	VFMSUB231PD Z0, Z3, K1, Z13                        // 6272e549bae8
	VFMSUB231PD -7(CX), Z3, K1, Z13                    // 6272e549baa9f9ffffff
	VFMSUB231PD 15(DX)(BX*4), Z3, K1, Z13              // 6272e549baac9a0f000000
	VFMSUB231PD Z3, Z26, K1, Z21                       // 62e2ad41baeb
	VFMSUB231PD Z0, Z26, K1, Z21                       // 62e2ad41bae8
	VFMSUB231PD -7(CX), Z26, K1, Z21                   // 62e2ad41baa9f9ffffff
	VFMSUB231PD 15(DX)(BX*4), Z26, K1, Z21             // 62e2ad41baac9a0f000000
	VFMSUB231PD Z3, Z3, K1, Z21                        // 62e2e549baeb
	VFMSUB231PD Z0, Z3, K1, Z21                        // 62e2e549bae8
	VFMSUB231PD -7(CX), Z3, K1, Z21                    // 62e2e549baa9f9ffffff
	VFMSUB231PD 15(DX)(BX*4), Z3, K1, Z21              // 62e2e549baac9a0f000000
	VFMSUB231PS X0, X19, K2, X26                       // 62626502bad0
	VFMSUB231PS (SI), X19, K2, X26                     // 62626502ba16
	VFMSUB231PS 7(SI)(DI*2), X19, K2, X26              // 62626502ba947e07000000
	VFMSUB231PS Y19, Y0, K1, Y9                        // 62327d29bacb
	VFMSUB231PS (R14), Y0, K1, Y9                      // 62527d29ba0e
	VFMSUB231PS -7(DI)(R8*8), Y0, K1, Y9               // 62327d29ba8cc7f9ffffff
	VFMSUB231PS Z3, Z11, K7, Z21                       // 62e2254fbaeb
	VFMSUB231PS Z12, Z11, K7, Z21                      // 62c2254fbaec
	VFMSUB231PS Z3, Z25, K7, Z21                       // 62e23547baeb
	VFMSUB231PS Z12, Z25, K7, Z21                      // 62c23547baec
	VFMSUB231PS Z3, Z11, K7, Z13                       // 6272254fbaeb
	VFMSUB231PS Z12, Z11, K7, Z13                      // 6252254fbaec
	VFMSUB231PS Z3, Z25, K7, Z13                       // 62723547baeb
	VFMSUB231PS Z12, Z25, K7, Z13                      // 62523547baec
	VFMSUB231PS Z23, Z23, K1, Z27                      // 62224541badf
	VFMSUB231PS Z6, Z23, K1, Z27                       // 62624541bade
	VFMSUB231PS 99(R15)(R15*8), Z23, K1, Z27           // 62024541ba9cff63000000
	VFMSUB231PS 7(AX)(CX*8), Z23, K1, Z27              // 62624541ba9cc807000000
	VFMSUB231PS Z23, Z5, K1, Z27                       // 62225549badf
	VFMSUB231PS Z6, Z5, K1, Z27                        // 62625549bade
	VFMSUB231PS 99(R15)(R15*8), Z5, K1, Z27            // 62025549ba9cff63000000
	VFMSUB231PS 7(AX)(CX*8), Z5, K1, Z27               // 62625549ba9cc807000000
	VFMSUB231PS Z23, Z23, K1, Z15                      // 62324541baff
	VFMSUB231PS Z6, Z23, K1, Z15                       // 62724541bafe
	VFMSUB231PS 99(R15)(R15*8), Z23, K1, Z15           // 62124541babcff63000000
	VFMSUB231PS 7(AX)(CX*8), Z23, K1, Z15              // 62724541babcc807000000
	VFMSUB231PS Z23, Z5, K1, Z15                       // 62325549baff
	VFMSUB231PS Z6, Z5, K1, Z15                        // 62725549bafe
	VFMSUB231PS 99(R15)(R15*8), Z5, K1, Z15            // 62125549babcff63000000
	VFMSUB231PS 7(AX)(CX*8), Z5, K1, Z15               // 62725549babcc807000000
	VFMSUB231SD X7, X16, K1, X31                       // 6262fd01bbff
	VFMSUB231SD X0, X1, K1, X8                         // 6272f509bbc0 or 6272f529bbc0 or 6272f549bbc0
	VFMSUB231SD -17(BP)(SI*8), X1, K1, X8              // 6272f509bb84f5efffffff or 6272f529bb84f5efffffff or 6272f549bb84f5efffffff
	VFMSUB231SD (R15), X1, K1, X8                      // 6252f509bb07 or 6252f529bb07 or 6252f549bb07
	VFMSUB231SS X16, X0, K7, X15                       // 62327d0fbbf8
	VFMSUB231SS X28, X0, K2, X21                       // 62827d0abbec or 62827d2abbec or 62827d4abbec
	VFMSUB231SS 17(SP)(BP*8), X0, K2, X21              // 62e27d0abbacec11000000 or 62e27d2abbacec11000000 or 62e27d4abbacec11000000
	VFMSUB231SS 17(SP)(BP*4), X0, K2, X21              // 62e27d0abbacac11000000 or 62e27d2abbacac11000000 or 62e27d4abbacac11000000
	VFMSUBADD132PD X19, X7, K4, X22                    // 62a2c50c97f3
	VFMSUBADD132PD 17(SP)(BP*8), X7, K4, X22           // 62e2c50c97b4ec11000000
	VFMSUBADD132PD 17(SP)(BP*4), X7, K4, X22           // 62e2c50c97b4ac11000000
	VFMSUBADD132PD Y9, Y22, K1, Y31                    // 6242cd2197f9
	VFMSUBADD132PD 99(R15)(R15*4), Y22, K1, Y31        // 6202cd2197bcbf63000000
	VFMSUBADD132PD 15(DX), Y22, K1, Y31                // 6262cd2197ba0f000000
	VFMSUBADD132PD Z16, Z21, K3, Z8                    // 6232d54397c0
	VFMSUBADD132PD Z13, Z21, K3, Z8                    // 6252d54397c5
	VFMSUBADD132PD Z16, Z5, K3, Z8                     // 6232d54b97c0
	VFMSUBADD132PD Z13, Z5, K3, Z8                     // 6252d54b97c5
	VFMSUBADD132PD Z16, Z21, K3, Z28                   // 6222d54397e0
	VFMSUBADD132PD Z13, Z21, K3, Z28                   // 6242d54397e5
	VFMSUBADD132PD Z16, Z5, K3, Z28                    // 6222d54b97e0
	VFMSUBADD132PD Z13, Z5, K3, Z28                    // 6242d54b97e5
	VFMSUBADD132PD Z6, Z22, K4, Z12                    // 6272cd4497e6
	VFMSUBADD132PD Z8, Z22, K4, Z12                    // 6252cd4497e0
	VFMSUBADD132PD (AX), Z22, K4, Z12                  // 6272cd449720
	VFMSUBADD132PD 7(SI), Z22, K4, Z12                 // 6272cd4497a607000000
	VFMSUBADD132PD Z6, Z11, K4, Z12                    // 6272a54c97e6
	VFMSUBADD132PD Z8, Z11, K4, Z12                    // 6252a54c97e0
	VFMSUBADD132PD (AX), Z11, K4, Z12                  // 6272a54c9720
	VFMSUBADD132PD 7(SI), Z11, K4, Z12                 // 6272a54c97a607000000
	VFMSUBADD132PD Z6, Z22, K4, Z27                    // 6262cd4497de
	VFMSUBADD132PD Z8, Z22, K4, Z27                    // 6242cd4497d8
	VFMSUBADD132PD (AX), Z22, K4, Z27                  // 6262cd449718
	VFMSUBADD132PD 7(SI), Z22, K4, Z27                 // 6262cd44979e07000000
	VFMSUBADD132PD Z6, Z11, K4, Z27                    // 6262a54c97de
	VFMSUBADD132PD Z8, Z11, K4, Z27                    // 6242a54c97d8
	VFMSUBADD132PD (AX), Z11, K4, Z27                  // 6262a54c9718
	VFMSUBADD132PD 7(SI), Z11, K4, Z27                 // 6262a54c979e07000000
	VFMSUBADD132PS X31, X16, K5, X7                    // 62927d0597ff
	VFMSUBADD132PS 7(SI)(DI*4), X16, K5, X7            // 62f27d0597bcbe07000000
	VFMSUBADD132PS -7(DI)(R8*2), X16, K5, X7           // 62b27d0597bc47f9ffffff
	VFMSUBADD132PS Y5, Y31, K7, Y23                    // 62e2052797fd
	VFMSUBADD132PS (CX), Y31, K7, Y23                  // 62e205279739
	VFMSUBADD132PS 99(R15), Y31, K7, Y23               // 62c2052797bf63000000
	VFMSUBADD132PS Z9, Z12, K7, Z25                    // 62421d4f97c9
	VFMSUBADD132PS Z12, Z12, K7, Z25                   // 62421d4f97cc
	VFMSUBADD132PS Z9, Z17, K7, Z25                    // 6242754797c9
	VFMSUBADD132PS Z12, Z17, K7, Z25                   // 6242754797cc
	VFMSUBADD132PS Z9, Z12, K7, Z12                    // 62521d4f97e1
	VFMSUBADD132PS Z12, Z12, K7, Z12                   // 62521d4f97e4
	VFMSUBADD132PS Z9, Z17, K7, Z12                    // 6252754797e1
	VFMSUBADD132PS Z12, Z17, K7, Z12                   // 6252754797e4
	VFMSUBADD132PS Z8, Z3, K6, Z6                      // 62d2654e97f0
	VFMSUBADD132PS Z2, Z3, K6, Z6                      // 62f2654e97f2
	VFMSUBADD132PS (BX), Z3, K6, Z6                    // 62f2654e9733
	VFMSUBADD132PS -17(BP)(SI*1), Z3, K6, Z6           // 62f2654e97b435efffffff
	VFMSUBADD132PS Z8, Z21, K6, Z6                     // 62d2554697f0
	VFMSUBADD132PS Z2, Z21, K6, Z6                     // 62f2554697f2
	VFMSUBADD132PS (BX), Z21, K6, Z6                   // 62f255469733
	VFMSUBADD132PS -17(BP)(SI*1), Z21, K6, Z6          // 62f2554697b435efffffff
	VFMSUBADD132PS Z8, Z3, K6, Z25                     // 6242654e97c8
	VFMSUBADD132PS Z2, Z3, K6, Z25                     // 6262654e97ca
	VFMSUBADD132PS (BX), Z3, K6, Z25                   // 6262654e970b
	VFMSUBADD132PS -17(BP)(SI*1), Z3, K6, Z25          // 6262654e978c35efffffff
	VFMSUBADD132PS Z8, Z21, K6, Z25                    // 6242554697c8
	VFMSUBADD132PS Z2, Z21, K6, Z25                    // 6262554697ca
	VFMSUBADD132PS (BX), Z21, K6, Z25                  // 62625546970b
	VFMSUBADD132PS -17(BP)(SI*1), Z21, K6, Z25         // 62625546978c35efffffff
	VFMSUBADD213PD X9, X7, K3, X1                      // 62d2c50ba7c9
	VFMSUBADD213PD 17(SP), X7, K3, X1                  // 62f2c50ba78c2411000000
	VFMSUBADD213PD -17(BP)(SI*4), X7, K3, X1           // 62f2c50ba78cb5efffffff
	VFMSUBADD213PD Y19, Y5, K7, Y0                     // 62b2d52fa7c3
	VFMSUBADD213PD 99(R15)(R15*2), Y5, K7, Y0          // 6292d52fa7847f63000000
	VFMSUBADD213PD -7(DI), Y5, K7, Y0                  // 62f2d52fa787f9ffffff
	VFMSUBADD213PD Z0, Z7, K4, Z3                      // 62f2c54ca7d8
	VFMSUBADD213PD Z6, Z7, K4, Z3                      // 62f2c54ca7de
	VFMSUBADD213PD Z0, Z9, K4, Z3                      // 62f2b54ca7d8
	VFMSUBADD213PD Z6, Z9, K4, Z3                      // 62f2b54ca7de
	VFMSUBADD213PD Z0, Z7, K4, Z27                     // 6262c54ca7d8
	VFMSUBADD213PD Z6, Z7, K4, Z27                     // 6262c54ca7de
	VFMSUBADD213PD Z0, Z9, K4, Z27                     // 6262b54ca7d8
	VFMSUBADD213PD Z6, Z9, K4, Z27                     // 6262b54ca7de
	VFMSUBADD213PD Z9, Z3, K4, Z20                     // 62c2e54ca7e1
	VFMSUBADD213PD Z19, Z3, K4, Z20                    // 62a2e54ca7e3
	VFMSUBADD213PD 15(R8)(R14*4), Z3, K4, Z20          // 6282e54ca7a4b00f000000
	VFMSUBADD213PD -7(CX)(DX*4), Z3, K4, Z20           // 62e2e54ca7a491f9ffffff
	VFMSUBADD213PD Z9, Z30, K4, Z20                    // 62c28d44a7e1
	VFMSUBADD213PD Z19, Z30, K4, Z20                   // 62a28d44a7e3
	VFMSUBADD213PD 15(R8)(R14*4), Z30, K4, Z20         // 62828d44a7a4b00f000000
	VFMSUBADD213PD -7(CX)(DX*4), Z30, K4, Z20          // 62e28d44a7a491f9ffffff
	VFMSUBADD213PD Z9, Z3, K4, Z28                     // 6242e54ca7e1
	VFMSUBADD213PD Z19, Z3, K4, Z28                    // 6222e54ca7e3
	VFMSUBADD213PD 15(R8)(R14*4), Z3, K4, Z28          // 6202e54ca7a4b00f000000
	VFMSUBADD213PD -7(CX)(DX*4), Z3, K4, Z28           // 6262e54ca7a491f9ffffff
	VFMSUBADD213PD Z9, Z30, K4, Z28                    // 62428d44a7e1
	VFMSUBADD213PD Z19, Z30, K4, Z28                   // 62228d44a7e3
	VFMSUBADD213PD 15(R8)(R14*4), Z30, K4, Z28         // 62028d44a7a4b00f000000
	VFMSUBADD213PD -7(CX)(DX*4), Z30, K4, Z28          // 62628d44a7a491f9ffffff
	VFMSUBADD213PS X0, X12, K7, X15                    // 62721d0fa7f8
	VFMSUBADD213PS 7(AX), X12, K7, X15                 // 62721d0fa7b807000000
	VFMSUBADD213PS (DI), X12, K7, X15                  // 62721d0fa73f
	VFMSUBADD213PS Y2, Y28, K2, Y31                    // 62621d22a7fa
	VFMSUBADD213PS -7(CX)(DX*1), Y28, K2, Y31          // 62621d22a7bc11f9ffffff
	VFMSUBADD213PS -15(R14)(R15*4), Y28, K2, Y31       // 62021d22a7bcbef1ffffff
	VFMSUBADD213PS Z18, Z11, K5, Z12                   // 6232254da7e2
	VFMSUBADD213PS Z24, Z11, K5, Z12                   // 6212254da7e0
	VFMSUBADD213PS Z18, Z5, K5, Z12                    // 6232554da7e2
	VFMSUBADD213PS Z24, Z5, K5, Z12                    // 6212554da7e0
	VFMSUBADD213PS Z18, Z11, K5, Z22                   // 62a2254da7f2
	VFMSUBADD213PS Z24, Z11, K5, Z22                   // 6282254da7f0
	VFMSUBADD213PS Z18, Z5, K5, Z22                    // 62a2554da7f2
	VFMSUBADD213PS Z24, Z5, K5, Z22                    // 6282554da7f0
	VFMSUBADD213PS Z6, Z7, K3, Z2                      // 62f2454ba7d6
	VFMSUBADD213PS Z16, Z7, K3, Z2                     // 62b2454ba7d0
	VFMSUBADD213PS (R8), Z7, K3, Z2                    // 62d2454ba710
	VFMSUBADD213PS 15(DX)(BX*2), Z7, K3, Z2            // 62f2454ba7945a0f000000
	VFMSUBADD213PS Z6, Z13, K3, Z2                     // 62f2154ba7d6
	VFMSUBADD213PS Z16, Z13, K3, Z2                    // 62b2154ba7d0
	VFMSUBADD213PS (R8), Z13, K3, Z2                   // 62d2154ba710
	VFMSUBADD213PS 15(DX)(BX*2), Z13, K3, Z2           // 62f2154ba7945a0f000000
	VFMSUBADD213PS Z6, Z7, K3, Z21                     // 62e2454ba7ee
	VFMSUBADD213PS Z16, Z7, K3, Z21                    // 62a2454ba7e8
	VFMSUBADD213PS (R8), Z7, K3, Z21                   // 62c2454ba728
	VFMSUBADD213PS 15(DX)(BX*2), Z7, K3, Z21           // 62e2454ba7ac5a0f000000
	VFMSUBADD213PS Z6, Z13, K3, Z21                    // 62e2154ba7ee
	VFMSUBADD213PS Z16, Z13, K3, Z21                   // 62a2154ba7e8
	VFMSUBADD213PS (R8), Z13, K3, Z21                  // 62c2154ba728
	VFMSUBADD213PS 15(DX)(BX*2), Z13, K3, Z21          // 62e2154ba7ac5a0f000000
	VFMSUBADD231PD X5, X14, K4, X12                    // 62728d0cb7e5
	VFMSUBADD231PD 99(R15)(R15*1), X14, K4, X12        // 62128d0cb7a43f63000000
	VFMSUBADD231PD (DX), X14, K4, X12                  // 62728d0cb722
	VFMSUBADD231PD Y0, Y27, K2, Y24                    // 6262a522b7c0
	VFMSUBADD231PD 15(DX)(BX*1), Y27, K2, Y24          // 6262a522b7841a0f000000
	VFMSUBADD231PD -7(CX)(DX*2), Y27, K2, Y24          // 6262a522b78451f9ffffff
	VFMSUBADD231PD Z13, Z1, K2, Z6                     // 62d2f54ab7f5
	VFMSUBADD231PD Z13, Z15, K2, Z6                    // 62d2854ab7f5
	VFMSUBADD231PD Z13, Z1, K2, Z22                    // 62c2f54ab7f5
	VFMSUBADD231PD Z13, Z15, K2, Z22                   // 62c2854ab7f5
	VFMSUBADD231PD Z2, Z22, K3, Z18                    // 62e2cd43b7d2
	VFMSUBADD231PD Z31, Z22, K3, Z18                   // 6282cd43b7d7
	VFMSUBADD231PD 17(SP)(BP*1), Z22, K3, Z18          // 62e2cd43b7942c11000000
	VFMSUBADD231PD -7(CX)(DX*8), Z22, K3, Z18          // 62e2cd43b794d1f9ffffff
	VFMSUBADD231PD Z2, Z7, K3, Z18                     // 62e2c54bb7d2
	VFMSUBADD231PD Z31, Z7, K3, Z18                    // 6282c54bb7d7
	VFMSUBADD231PD 17(SP)(BP*1), Z7, K3, Z18           // 62e2c54bb7942c11000000
	VFMSUBADD231PD -7(CX)(DX*8), Z7, K3, Z18           // 62e2c54bb794d1f9ffffff
	VFMSUBADD231PD Z2, Z22, K3, Z8                     // 6272cd43b7c2
	VFMSUBADD231PD Z31, Z22, K3, Z8                    // 6212cd43b7c7
	VFMSUBADD231PD 17(SP)(BP*1), Z22, K3, Z8           // 6272cd43b7842c11000000
	VFMSUBADD231PD -7(CX)(DX*8), Z22, K3, Z8           // 6272cd43b784d1f9ffffff
	VFMSUBADD231PD Z2, Z7, K3, Z8                      // 6272c54bb7c2
	VFMSUBADD231PD Z31, Z7, K3, Z8                     // 6212c54bb7c7
	VFMSUBADD231PD 17(SP)(BP*1), Z7, K3, Z8            // 6272c54bb7842c11000000
	VFMSUBADD231PD -7(CX)(DX*8), Z7, K3, Z8            // 6272c54bb784d1f9ffffff
	VFMSUBADD231PS X8, X15, K3, X17                    // 62c2050bb7c8
	VFMSUBADD231PS -17(BP)(SI*8), X15, K3, X17         // 62e2050bb78cf5efffffff
	VFMSUBADD231PS (R15), X15, K3, X17                 // 62c2050bb70f
	VFMSUBADD231PS Y3, Y31, K3, Y11                    // 62720523b7db
	VFMSUBADD231PS -17(BP), Y31, K3, Y11               // 62720523b79defffffff
	VFMSUBADD231PS -15(R14)(R15*8), Y31, K3, Y11       // 62120523b79cfef1ffffff
	VFMSUBADD231PS Z12, Z1, K2, Z20                    // 62c2754ab7e4
	VFMSUBADD231PS Z16, Z1, K2, Z20                    // 62a2754ab7e0
	VFMSUBADD231PS Z12, Z3, K2, Z20                    // 62c2654ab7e4
	VFMSUBADD231PS Z16, Z3, K2, Z20                    // 62a2654ab7e0
	VFMSUBADD231PS Z12, Z1, K2, Z9                     // 6252754ab7cc
	VFMSUBADD231PS Z16, Z1, K2, Z9                     // 6232754ab7c8
	VFMSUBADD231PS Z12, Z3, K2, Z9                     // 6252654ab7cc
	VFMSUBADD231PS Z16, Z3, K2, Z9                     // 6232654ab7c8
	VFMSUBADD231PS Z3, Z14, K1, Z28                    // 62620d49b7e3
	VFMSUBADD231PS Z12, Z14, K1, Z28                   // 62420d49b7e4
	VFMSUBADD231PS -17(BP)(SI*2), Z14, K1, Z28         // 62620d49b7a475efffffff
	VFMSUBADD231PS 7(AX)(CX*2), Z14, K1, Z28           // 62620d49b7a44807000000
	VFMSUBADD231PS Z3, Z28, K1, Z28                    // 62621d41b7e3
	VFMSUBADD231PS Z12, Z28, K1, Z28                   // 62421d41b7e4
	VFMSUBADD231PS -17(BP)(SI*2), Z28, K1, Z28         // 62621d41b7a475efffffff
	VFMSUBADD231PS 7(AX)(CX*2), Z28, K1, Z28           // 62621d41b7a44807000000
	VFMSUBADD231PS Z3, Z14, K1, Z13                    // 62720d49b7eb
	VFMSUBADD231PS Z12, Z14, K1, Z13                   // 62520d49b7ec
	VFMSUBADD231PS -17(BP)(SI*2), Z14, K1, Z13         // 62720d49b7ac75efffffff
	VFMSUBADD231PS 7(AX)(CX*2), Z14, K1, Z13           // 62720d49b7ac4807000000
	VFMSUBADD231PS Z3, Z28, K1, Z13                    // 62721d41b7eb
	VFMSUBADD231PS Z12, Z28, K1, Z13                   // 62521d41b7ec
	VFMSUBADD231PS -17(BP)(SI*2), Z28, K1, Z13         // 62721d41b7ac75efffffff
	VFMSUBADD231PS 7(AX)(CX*2), Z28, K1, Z13           // 62721d41b7ac4807000000
	VFNMADD132PD X23, X26, K2, X3                      // 62b2ad029cdf
	VFNMADD132PD 7(SI)(DI*8), X26, K2, X3              // 62f2ad029c9cfe07000000
	VFNMADD132PD -15(R14), X26, K2, X3                 // 62d2ad029c9ef1ffffff
	VFNMADD132PD Y13, Y2, K1, Y14                      // 6252ed299cf5
	VFNMADD132PD 17(SP)(BP*2), Y2, K1, Y14             // 6272ed299cb46c11000000
	VFNMADD132PD -7(DI)(R8*4), Y2, K1, Y14             // 6232ed299cb487f9ffffff
	VFNMADD132PD Z5, Z19, K7, Z15                      // 6272e5479cfd
	VFNMADD132PD Z1, Z19, K7, Z15                      // 6272e5479cf9
	VFNMADD132PD Z5, Z15, K7, Z15                      // 6272854f9cfd
	VFNMADD132PD Z1, Z15, K7, Z15                      // 6272854f9cf9
	VFNMADD132PD Z5, Z19, K7, Z30                      // 6262e5479cf5
	VFNMADD132PD Z1, Z19, K7, Z30                      // 6262e5479cf1
	VFNMADD132PD Z5, Z15, K7, Z30                      // 6262854f9cf5
	VFNMADD132PD Z1, Z15, K7, Z30                      // 6262854f9cf1
	VFNMADD132PD Z21, Z14, K1, Z3                      // 62b28d499cdd
	VFNMADD132PD Z8, Z14, K1, Z3                       // 62d28d499cd8
	VFNMADD132PD 15(R8)(R14*1), Z14, K1, Z3            // 62928d499c9c300f000000
	VFNMADD132PD 15(R8)(R14*2), Z14, K1, Z3            // 62928d499c9c700f000000
	VFNMADD132PD Z21, Z15, K1, Z3                      // 62b285499cdd
	VFNMADD132PD Z8, Z15, K1, Z3                       // 62d285499cd8
	VFNMADD132PD 15(R8)(R14*1), Z15, K1, Z3            // 629285499c9c300f000000
	VFNMADD132PD 15(R8)(R14*2), Z15, K1, Z3            // 629285499c9c700f000000
	VFNMADD132PD Z21, Z14, K1, Z5                      // 62b28d499ced
	VFNMADD132PD Z8, Z14, K1, Z5                       // 62d28d499ce8
	VFNMADD132PD 15(R8)(R14*1), Z14, K1, Z5            // 62928d499cac300f000000
	VFNMADD132PD 15(R8)(R14*2), Z14, K1, Z5            // 62928d499cac700f000000
	VFNMADD132PD Z21, Z15, K1, Z5                      // 62b285499ced
	VFNMADD132PD Z8, Z15, K1, Z5                       // 62d285499ce8
	VFNMADD132PD 15(R8)(R14*1), Z15, K1, Z5            // 629285499cac300f000000
	VFNMADD132PD 15(R8)(R14*2), Z15, K1, Z5            // 629285499cac700f000000
	VFNMADD132PS X24, X28, K1, X13                     // 62121d019ce8
	VFNMADD132PS 7(SI)(DI*1), X28, K1, X13             // 62721d019cac3e07000000
	VFNMADD132PS 15(DX)(BX*8), X28, K1, X13            // 62721d019cacda0f000000
	VFNMADD132PS Y22, Y15, K1, Y27                     // 622205299cde
	VFNMADD132PS 15(R8), Y15, K1, Y27                  // 624205299c980f000000
	VFNMADD132PS (BP), Y15, K1, Y27                    // 626205299c5d00
	VFNMADD132PS Z23, Z20, K7, Z16                     // 62a25d479cc7
	VFNMADD132PS Z19, Z20, K7, Z16                     // 62a25d479cc3
	VFNMADD132PS Z23, Z0, K7, Z16                      // 62a27d4f9cc7
	VFNMADD132PS Z19, Z0, K7, Z16                      // 62a27d4f9cc3
	VFNMADD132PS Z23, Z20, K7, Z9                      // 62325d479ccf
	VFNMADD132PS Z19, Z20, K7, Z9                      // 62325d479ccb
	VFNMADD132PS Z23, Z0, K7, Z9                       // 62327d4f9ccf
	VFNMADD132PS Z19, Z0, K7, Z9                       // 62327d4f9ccb
	VFNMADD132PS Z24, Z0, K2, Z0                       // 62927d4a9cc0
	VFNMADD132PS Z12, Z0, K2, Z0                       // 62d27d4a9cc4
	VFNMADD132PS (R14), Z0, K2, Z0                     // 62d27d4a9c06
	VFNMADD132PS -7(DI)(R8*8), Z0, K2, Z0              // 62b27d4a9c84c7f9ffffff
	VFNMADD132PS Z24, Z25, K2, Z0                      // 629235429cc0
	VFNMADD132PS Z12, Z25, K2, Z0                      // 62d235429cc4
	VFNMADD132PS (R14), Z25, K2, Z0                    // 62d235429c06
	VFNMADD132PS -7(DI)(R8*8), Z25, K2, Z0             // 62b235429c84c7f9ffffff
	VFNMADD132PS Z24, Z0, K2, Z11                      // 62127d4a9cd8
	VFNMADD132PS Z12, Z0, K2, Z11                      // 62527d4a9cdc
	VFNMADD132PS (R14), Z0, K2, Z11                    // 62527d4a9c1e
	VFNMADD132PS -7(DI)(R8*8), Z0, K2, Z11             // 62327d4a9c9cc7f9ffffff
	VFNMADD132PS Z24, Z25, K2, Z11                     // 621235429cd8
	VFNMADD132PS Z12, Z25, K2, Z11                     // 625235429cdc
	VFNMADD132PS (R14), Z25, K2, Z11                   // 625235429c1e
	VFNMADD132PS -7(DI)(R8*8), Z25, K2, Z11            // 623235429c9cc7f9ffffff
	VFNMADD132SD X26, X15, K4, X9                      // 6212850c9dca
	VFNMADD132SD X1, X21, K1, X18                      // 62e2d5019dd1 or 62e2d5219dd1 or 62e2d5419dd1
	VFNMADD132SD 7(SI)(DI*8), X21, K1, X18             // 62e2d5019d94fe07000000 or 62e2d5219d94fe07000000 or 62e2d5419d94fe07000000
	VFNMADD132SD -15(R14), X21, K1, X18                // 62c2d5019d96f1ffffff or 62c2d5219d96f1ffffff or 62c2d5419d96f1ffffff
	VFNMADD132SS X3, X31, K3, X11                      // 627205039ddb
	VFNMADD132SS X0, X0, K4, X7                        // 62f27d0c9df8 or 62f27d2c9df8 or 62f27d4c9df8
	VFNMADD132SS 7(SI)(DI*4), X0, K4, X7               // 62f27d0c9dbcbe07000000 or 62f27d2c9dbcbe07000000 or 62f27d4c9dbcbe07000000
	VFNMADD132SS -7(DI)(R8*2), X0, K4, X7              // 62b27d0c9dbc47f9ffffff or 62b27d2c9dbc47f9ffffff or 62b27d4c9dbc47f9ffffff
	VFNMADD213PD X7, X20, K5, X24                      // 6262dd05acc7
	VFNMADD213PD -7(DI)(R8*1), X20, K5, X24            // 6222dd05ac8407f9ffffff
	VFNMADD213PD (SP), X20, K5, X24                    // 6262dd05ac0424
	VFNMADD213PD Y24, Y18, K7, Y20                     // 6282ed27ace0
	VFNMADD213PD 15(R8)(R14*8), Y18, K7, Y20           // 6282ed27aca4f00f000000
	VFNMADD213PD -15(R14)(R15*2), Y18, K7, Y20         // 6282ed27aca47ef1ffffff
	VFNMADD213PD Z9, Z9, K7, Z0                        // 62d2b54facc1
	VFNMADD213PD Z25, Z9, K7, Z0                       // 6292b54facc1
	VFNMADD213PD Z9, Z3, K7, Z0                        // 62d2e54facc1
	VFNMADD213PD Z25, Z3, K7, Z0                       // 6292e54facc1
	VFNMADD213PD Z9, Z9, K7, Z26                       // 6242b54facd1
	VFNMADD213PD Z25, Z9, K7, Z26                      // 6202b54facd1
	VFNMADD213PD Z9, Z3, K7, Z26                       // 6242e54facd1
	VFNMADD213PD Z25, Z3, K7, Z26                      // 6202e54facd1
	VFNMADD213PD Z17, Z20, K6, Z9                      // 6232dd46acc9
	VFNMADD213PD Z0, Z20, K6, Z9                       // 6272dd46acc8
	VFNMADD213PD 99(R15)(R15*4), Z20, K6, Z9           // 6212dd46ac8cbf63000000
	VFNMADD213PD 15(DX), Z20, K6, Z9                   // 6272dd46ac8a0f000000
	VFNMADD213PD Z17, Z0, K6, Z9                       // 6232fd4eacc9
	VFNMADD213PD Z0, Z0, K6, Z9                        // 6272fd4eacc8
	VFNMADD213PD 99(R15)(R15*4), Z0, K6, Z9            // 6212fd4eac8cbf63000000
	VFNMADD213PD 15(DX), Z0, K6, Z9                    // 6272fd4eac8a0f000000
	VFNMADD213PD Z17, Z20, K6, Z28                     // 6222dd46ace1
	VFNMADD213PD Z0, Z20, K6, Z28                      // 6262dd46ace0
	VFNMADD213PD 99(R15)(R15*4), Z20, K6, Z28          // 6202dd46aca4bf63000000
	VFNMADD213PD 15(DX), Z20, K6, Z28                  // 6262dd46aca20f000000
	VFNMADD213PD Z17, Z0, K6, Z28                      // 6222fd4eace1
	VFNMADD213PD Z0, Z0, K6, Z28                       // 6262fd4eace0
	VFNMADD213PD 99(R15)(R15*4), Z0, K6, Z28           // 6202fd4eaca4bf63000000
	VFNMADD213PD 15(DX), Z0, K6, Z28                   // 6262fd4eaca20f000000
	VFNMADD213PS X14, X7, K3, X9                       // 6252450bacce
	VFNMADD213PS -7(CX), X7, K3, X9                    // 6272450bac89f9ffffff
	VFNMADD213PS 15(DX)(BX*4), X7, K3, X9              // 6272450bac8c9a0f000000
	VFNMADD213PS Y19, Y3, K7, Y9                       // 6232652faccb
	VFNMADD213PS -15(R14)(R15*1), Y3, K7, Y9           // 6212652fac8c3ef1ffffff
	VFNMADD213PS -15(BX), Y3, K7, Y9                   // 6272652fac8bf1ffffff
	VFNMADD213PS Z21, Z31, K4, Z17                     // 62a20544accd
	VFNMADD213PS Z9, Z31, K4, Z17                      // 62c20544acc9
	VFNMADD213PS Z21, Z0, K4, Z17                      // 62a27d4caccd
	VFNMADD213PS Z9, Z0, K4, Z17                       // 62c27d4cacc9
	VFNMADD213PS Z21, Z31, K4, Z23                     // 62a20544acfd
	VFNMADD213PS Z9, Z31, K4, Z23                      // 62c20544acf9
	VFNMADD213PS Z21, Z0, K4, Z23                      // 62a27d4cacfd
	VFNMADD213PS Z9, Z0, K4, Z23                       // 62c27d4cacf9
	VFNMADD213PS Z20, Z1, K4, Z6                       // 62b2754cacf4
	VFNMADD213PS Z9, Z1, K4, Z6                        // 62d2754cacf1
	VFNMADD213PS (CX), Z1, K4, Z6                      // 62f2754cac31
	VFNMADD213PS 99(R15), Z1, K4, Z6                   // 62d2754cacb763000000
	VFNMADD213PS Z20, Z9, K4, Z6                       // 62b2354cacf4
	VFNMADD213PS Z9, Z9, K4, Z6                        // 62d2354cacf1
	VFNMADD213PS (CX), Z9, K4, Z6                      // 62f2354cac31
	VFNMADD213PS 99(R15), Z9, K4, Z6                   // 62d2354cacb763000000
	VFNMADD213PS Z20, Z1, K4, Z9                       // 6232754caccc
	VFNMADD213PS Z9, Z1, K4, Z9                        // 6252754cacc9
	VFNMADD213PS (CX), Z1, K4, Z9                      // 6272754cac09
	VFNMADD213PS 99(R15), Z1, K4, Z9                   // 6252754cac8f63000000
	VFNMADD213PS Z20, Z9, K4, Z9                       // 6232354caccc
	VFNMADD213PS Z9, Z9, K4, Z9                        // 6252354cacc9
	VFNMADD213PS (CX), Z9, K4, Z9                      // 6272354cac09
	VFNMADD213PS 99(R15), Z9, K4, Z9                   // 6252354cac8f63000000
	VFNMADD213SD X3, X31, K7, X5                       // 62f28507adeb
	VFNMADD213SD X11, X1, K2, X21                      // 62c2f50aadeb or 62c2f52aadeb or 62c2f54aadeb
	VFNMADD213SD 7(SI)(DI*1), X1, K2, X21              // 62e2f50aadac3e07000000 or 62e2f52aadac3e07000000 or 62e2f54aadac3e07000000
	VFNMADD213SD 15(DX)(BX*8), X1, K2, X21             // 62e2f50aadacda0f000000 or 62e2f52aadacda0f000000 or 62e2f54aadacda0f000000
	VFNMADD213SS X30, X0, K5, X13                      // 62127d0dadee
	VFNMADD213SS X11, X14, K3, X16                     // 62c20d0badc3 or 62c20d2badc3 or 62c20d4badc3
	VFNMADD213SS 17(SP), X14, K3, X16                  // 62e20d0bad842411000000 or 62e20d2bad842411000000 or 62e20d4bad842411000000
	VFNMADD213SS -17(BP)(SI*4), X14, K3, X16           // 62e20d0bad84b5efffffff or 62e20d2bad84b5efffffff or 62e20d4bad84b5efffffff
	VFNMADD231PD X8, X19, K4, X14                      // 6252e504bcf0
	VFNMADD231PD 99(R15)(R15*8), X19, K4, X14          // 6212e504bcb4ff63000000
	VFNMADD231PD 7(AX)(CX*8), X19, K4, X14             // 6272e504bcb4c807000000
	VFNMADD231PD Y14, Y19, K2, Y23                     // 62c2e522bcfe
	VFNMADD231PD 7(AX)(CX*4), Y19, K2, Y23             // 62e2e522bcbc8807000000
	VFNMADD231PD 7(AX)(CX*1), Y19, K2, Y23             // 62e2e522bcbc0807000000
	VFNMADD231PD Z7, Z26, K2, Z30                      // 6262ad42bcf7
	VFNMADD231PD Z21, Z26, K2, Z30                     // 6222ad42bcf5
	VFNMADD231PD Z7, Z22, K2, Z30                      // 6262cd42bcf7
	VFNMADD231PD Z21, Z22, K2, Z30                     // 6222cd42bcf5
	VFNMADD231PD Z7, Z26, K2, Z5                       // 62f2ad42bcef
	VFNMADD231PD Z21, Z26, K2, Z5                      // 62b2ad42bced
	VFNMADD231PD Z7, Z22, K2, Z5                       // 62f2cd42bcef
	VFNMADD231PD Z21, Z22, K2, Z5                      // 62b2cd42bced
	VFNMADD231PD Z12, Z14, K3, Z16                     // 62c28d4bbcc4
	VFNMADD231PD Z13, Z14, K3, Z16                     // 62c28d4bbcc5
	VFNMADD231PD 99(R15)(R15*2), Z14, K3, Z16          // 62828d4bbc847f63000000
	VFNMADD231PD -7(DI), Z14, K3, Z16                  // 62e28d4bbc87f9ffffff
	VFNMADD231PD Z12, Z13, K3, Z16                     // 62c2954bbcc4
	VFNMADD231PD Z13, Z13, K3, Z16                     // 62c2954bbcc5
	VFNMADD231PD 99(R15)(R15*2), Z13, K3, Z16          // 6282954bbc847f63000000
	VFNMADD231PD -7(DI), Z13, K3, Z16                  // 62e2954bbc87f9ffffff
	VFNMADD231PD Z12, Z14, K3, Z25                     // 62428d4bbccc
	VFNMADD231PD Z13, Z14, K3, Z25                     // 62428d4bbccd
	VFNMADD231PD 99(R15)(R15*2), Z14, K3, Z25          // 62028d4bbc8c7f63000000
	VFNMADD231PD -7(DI), Z14, K3, Z25                  // 62628d4bbc8ff9ffffff
	VFNMADD231PD Z12, Z13, K3, Z25                     // 6242954bbccc
	VFNMADD231PD Z13, Z13, K3, Z25                     // 6242954bbccd
	VFNMADD231PD 99(R15)(R15*2), Z13, K3, Z25          // 6202954bbc8c7f63000000
	VFNMADD231PD -7(DI), Z13, K3, Z25                  // 6262954bbc8ff9ffffff
	VFNMADD231PS X23, X26, K3, X8                      // 62322d03bcc7
	VFNMADD231PS (AX), X26, K3, X8                     // 62722d03bc00
	VFNMADD231PS 7(SI), X26, K3, X8                    // 62722d03bc8607000000
	VFNMADD231PS Y16, Y5, K3, Y21                      // 62a2552bbce8
	VFNMADD231PS (SI), Y5, K3, Y21                     // 62e2552bbc2e
	VFNMADD231PS 7(SI)(DI*2), Y5, K3, Y21              // 62e2552bbcac7e07000000
	VFNMADD231PS Z27, Z2, K2, Z21                      // 62826d4abceb
	VFNMADD231PS Z25, Z2, K2, Z21                      // 62826d4abce9
	VFNMADD231PS Z27, Z7, K2, Z21                      // 6282454abceb
	VFNMADD231PS Z25, Z7, K2, Z21                      // 6282454abce9
	VFNMADD231PS Z27, Z2, K2, Z9                       // 62126d4abccb
	VFNMADD231PS Z25, Z2, K2, Z9                       // 62126d4abcc9
	VFNMADD231PS Z27, Z7, K2, Z9                       // 6212454abccb
	VFNMADD231PS Z25, Z7, K2, Z9                       // 6212454abcc9
	VFNMADD231PS Z3, Z27, K1, Z23                      // 62e22541bcfb
	VFNMADD231PS Z0, Z27, K1, Z23                      // 62e22541bcf8
	VFNMADD231PS -7(CX)(DX*1), Z27, K1, Z23            // 62e22541bcbc11f9ffffff
	VFNMADD231PS -15(R14)(R15*4), Z27, K1, Z23         // 62822541bcbcbef1ffffff
	VFNMADD231PS Z3, Z14, K1, Z23                      // 62e20d49bcfb
	VFNMADD231PS Z0, Z14, K1, Z23                      // 62e20d49bcf8
	VFNMADD231PS -7(CX)(DX*1), Z14, K1, Z23            // 62e20d49bcbc11f9ffffff
	VFNMADD231PS -15(R14)(R15*4), Z14, K1, Z23         // 62820d49bcbcbef1ffffff
	VFNMADD231PS Z3, Z27, K1, Z9                       // 62722541bccb
	VFNMADD231PS Z0, Z27, K1, Z9                       // 62722541bcc8
	VFNMADD231PS -7(CX)(DX*1), Z27, K1, Z9             // 62722541bc8c11f9ffffff
	VFNMADD231PS -15(R14)(R15*4), Z27, K1, Z9          // 62122541bc8cbef1ffffff
	VFNMADD231PS Z3, Z14, K1, Z9                       // 62720d49bccb
	VFNMADD231PS Z0, Z14, K1, Z9                       // 62720d49bcc8
	VFNMADD231PS -7(CX)(DX*1), Z14, K1, Z9             // 62720d49bc8c11f9ffffff
	VFNMADD231PS -15(R14)(R15*4), Z14, K1, Z9          // 62120d49bc8cbef1ffffff
	VFNMADD231SD X23, X16, K2, X12                     // 6232fd02bde7
	VFNMADD231SD X31, X11, K1, X23                     // 6282a509bdff or 6282a529bdff or 6282a549bdff
	VFNMADD231SD -7(DI)(R8*1), X11, K1, X23            // 62a2a509bdbc07f9ffffff or 62a2a529bdbc07f9ffffff or 62a2a549bdbc07f9ffffff
	VFNMADD231SD (SP), X11, K1, X23                    // 62e2a509bd3c24 or 62e2a529bd3c24 or 62e2a549bd3c24
	VFNMADD231SS X0, X14, K7, X24                      // 62620d0fbdc0
	VFNMADD231SS X2, X23, K1, X11                      // 62724501bdda or 62724521bdda or 62724541bdda
	VFNMADD231SS 7(AX), X23, K1, X11                   // 62724501bd9807000000 or 62724521bd9807000000 or 62724541bd9807000000
	VFNMADD231SS (DI), X23, K1, X11                    // 62724501bd1f or 62724521bd1f or 62724541bd1f
	VFNMSUB132PD X25, X5, K1, X20                      // 6282d5099ee1
	VFNMSUB132PD (BX), X5, K1, X20                     // 62e2d5099e23
	VFNMSUB132PD -17(BP)(SI*1), X5, K1, X20            // 62e2d5099ea435efffffff
	VFNMSUB132PD Y20, Y21, K1, Y2                      // 62b2d5219ed4
	VFNMSUB132PD 17(SP)(BP*8), Y21, K1, Y2             // 62f2d5219e94ec11000000
	VFNMSUB132PD 17(SP)(BP*4), Y21, K1, Y2             // 62f2d5219e94ac11000000
	VFNMSUB132PD Z22, Z8, K7, Z14                      // 6232bd4f9ef6
	VFNMSUB132PD Z25, Z8, K7, Z14                      // 6212bd4f9ef1
	VFNMSUB132PD Z22, Z24, K7, Z14                     // 6232bd479ef6
	VFNMSUB132PD Z25, Z24, K7, Z14                     // 6212bd479ef1
	VFNMSUB132PD Z22, Z8, K7, Z7                       // 62b2bd4f9efe
	VFNMSUB132PD Z25, Z8, K7, Z7                       // 6292bd4f9ef9
	VFNMSUB132PD Z22, Z24, K7, Z7                      // 62b2bd479efe
	VFNMSUB132PD Z25, Z24, K7, Z7                      // 6292bd479ef9
	VFNMSUB132PD Z0, Z6, K2, Z1                        // 62f2cd4a9ec8
	VFNMSUB132PD Z8, Z6, K2, Z1                        // 62d2cd4a9ec8
	VFNMSUB132PD 15(DX)(BX*1), Z6, K2, Z1              // 62f2cd4a9e8c1a0f000000
	VFNMSUB132PD -7(CX)(DX*2), Z6, K2, Z1              // 62f2cd4a9e8c51f9ffffff
	VFNMSUB132PD Z0, Z2, K2, Z1                        // 62f2ed4a9ec8
	VFNMSUB132PD Z8, Z2, K2, Z1                        // 62d2ed4a9ec8
	VFNMSUB132PD 15(DX)(BX*1), Z2, K2, Z1              // 62f2ed4a9e8c1a0f000000
	VFNMSUB132PD -7(CX)(DX*2), Z2, K2, Z1              // 62f2ed4a9e8c51f9ffffff
	VFNMSUB132PD Z0, Z6, K2, Z16                       // 62e2cd4a9ec0
	VFNMSUB132PD Z8, Z6, K2, Z16                       // 62c2cd4a9ec0
	VFNMSUB132PD 15(DX)(BX*1), Z6, K2, Z16             // 62e2cd4a9e841a0f000000
	VFNMSUB132PD -7(CX)(DX*2), Z6, K2, Z16             // 62e2cd4a9e8451f9ffffff
	VFNMSUB132PD Z0, Z2, K2, Z16                       // 62e2ed4a9ec0
	VFNMSUB132PD Z8, Z2, K2, Z16                       // 62c2ed4a9ec0
	VFNMSUB132PD 15(DX)(BX*1), Z2, K2, Z16             // 62e2ed4a9e841a0f000000
	VFNMSUB132PD -7(CX)(DX*2), Z2, K2, Z16             // 62e2ed4a9e8451f9ffffff
	VFNMSUB132PS X13, X9, K4, X0                       // 62d2350c9ec5
	VFNMSUB132PS 15(R8)(R14*4), X9, K4, X0             // 6292350c9e84b00f000000
	VFNMSUB132PS -7(CX)(DX*4), X9, K4, X0              // 62f2350c9e8491f9ffffff
	VFNMSUB132PS Y6, Y31, K1, Y6                       // 62f205219ef6
	VFNMSUB132PS 7(SI)(DI*4), Y31, K1, Y6              // 62f205219eb4be07000000
	VFNMSUB132PS -7(DI)(R8*2), Y31, K1, Y6             // 62b205219eb447f9ffffff
	VFNMSUB132PS Z11, Z14, K3, Z15                     // 62520d4b9efb
	VFNMSUB132PS Z5, Z14, K3, Z15                      // 62720d4b9efd
	VFNMSUB132PS Z11, Z27, K3, Z15                     // 625225439efb
	VFNMSUB132PS Z5, Z27, K3, Z15                      // 627225439efd
	VFNMSUB132PS Z11, Z14, K3, Z12                     // 62520d4b9ee3
	VFNMSUB132PS Z5, Z14, K3, Z12                      // 62720d4b9ee5
	VFNMSUB132PS Z11, Z27, K3, Z12                     // 625225439ee3
	VFNMSUB132PS Z5, Z27, K3, Z12                      // 627225439ee5
	VFNMSUB132PS Z2, Z5, K4, Z13                       // 6272554c9eea
	VFNMSUB132PS -17(BP), Z5, K4, Z13                  // 6272554c9eadefffffff
	VFNMSUB132PS -15(R14)(R15*8), Z5, K4, Z13          // 6212554c9eacfef1ffffff
	VFNMSUB132PS Z2, Z23, K4, Z13                      // 627245449eea
	VFNMSUB132PS -17(BP), Z23, K4, Z13                 // 627245449eadefffffff
	VFNMSUB132PS -15(R14)(R15*8), Z23, K4, Z13         // 621245449eacfef1ffffff
	VFNMSUB132PS Z2, Z5, K4, Z14                       // 6272554c9ef2
	VFNMSUB132PS -17(BP), Z5, K4, Z14                  // 6272554c9eb5efffffff
	VFNMSUB132PS -15(R14)(R15*8), Z5, K4, Z14          // 6212554c9eb4fef1ffffff
	VFNMSUB132PS Z2, Z23, K4, Z14                      // 627245449ef2
	VFNMSUB132PS -17(BP), Z23, K4, Z14                 // 627245449eb5efffffff
	VFNMSUB132PS -15(R14)(R15*8), Z23, K4, Z14         // 621245449eb4fef1ffffff
	VFNMSUB132SD X9, X8, K5, X2                        // 62d2bd0d9fd1
	VFNMSUB132SD X11, X31, K7, X2                      // 62d285079fd3 or 62d285279fd3 or 62d285479fd3
	VFNMSUB132SD -7(CX), X31, K7, X2                   // 62f285079f91f9ffffff or 62f285279f91f9ffffff or 62f285479f91f9ffffff
	VFNMSUB132SD 15(DX)(BX*4), X31, K7, X2             // 62f285079f949a0f000000 or 62f285279f949a0f000000 or 62f285479f949a0f000000
	VFNMSUB132SS X14, X5, K7, X22                      // 62c2550f9ff6
	VFNMSUB132SS X7, X17, K6, X0                       // 62f275069fc7 or 62f275269fc7 or 62f275469fc7
	VFNMSUB132SS 99(R15)(R15*1), X17, K6, X0           // 629275069f843f63000000 or 629275269f843f63000000 or 629275469f843f63000000
	VFNMSUB132SS (DX), X17, K6, X0                     // 62f275069f02 or 62f275269f02 or 62f275469f02
	VFNMSUB213PD X0, X11, K3, X15                      // 6272a50baef8
	VFNMSUB213PD (R8), X11, K3, X15                    // 6252a50bae38
	VFNMSUB213PD 15(DX)(BX*2), X11, K3, X15            // 6272a50baebc5a0f000000
	VFNMSUB213PD Y7, Y19, K7, Y11                      // 6272e527aedf
	VFNMSUB213PD 17(SP), Y19, K7, Y11                  // 6272e527ae9c2411000000
	VFNMSUB213PD -17(BP)(SI*4), Y19, K7, Y11           // 6272e527ae9cb5efffffff
	VFNMSUB213PD Z28, Z26, K4, Z6                      // 6292ad44aef4
	VFNMSUB213PD Z6, Z26, K4, Z6                       // 62f2ad44aef6
	VFNMSUB213PD Z28, Z14, K4, Z6                      // 62928d4caef4
	VFNMSUB213PD Z6, Z14, K4, Z6                       // 62f28d4caef6
	VFNMSUB213PD Z28, Z26, K4, Z14                     // 6212ad44aef4
	VFNMSUB213PD Z6, Z26, K4, Z14                      // 6272ad44aef6
	VFNMSUB213PD Z28, Z14, K4, Z14                     // 62128d4caef4
	VFNMSUB213PD Z6, Z14, K4, Z14                      // 62728d4caef6
	VFNMSUB213PD Z3, Z26, K4, Z13                      // 6272ad44aeeb
	VFNMSUB213PD Z0, Z26, K4, Z13                      // 6272ad44aee8
	VFNMSUB213PD 17(SP)(BP*2), Z26, K4, Z13            // 6272ad44aeac6c11000000
	VFNMSUB213PD -7(DI)(R8*4), Z26, K4, Z13            // 6232ad44aeac87f9ffffff
	VFNMSUB213PD Z3, Z3, K4, Z13                       // 6272e54caeeb
	VFNMSUB213PD Z0, Z3, K4, Z13                       // 6272e54caee8
	VFNMSUB213PD 17(SP)(BP*2), Z3, K4, Z13             // 6272e54caeac6c11000000
	VFNMSUB213PD -7(DI)(R8*4), Z3, K4, Z13             // 6232e54caeac87f9ffffff
	VFNMSUB213PD Z3, Z26, K4, Z21                      // 62e2ad44aeeb
	VFNMSUB213PD Z0, Z26, K4, Z21                      // 62e2ad44aee8
	VFNMSUB213PD 17(SP)(BP*2), Z26, K4, Z21            // 62e2ad44aeac6c11000000
	VFNMSUB213PD -7(DI)(R8*4), Z26, K4, Z21            // 62a2ad44aeac87f9ffffff
	VFNMSUB213PD Z3, Z3, K4, Z21                       // 62e2e54caeeb
	VFNMSUB213PD Z0, Z3, K4, Z21                       // 62e2e54caee8
	VFNMSUB213PD 17(SP)(BP*2), Z3, K4, Z21             // 62e2e54caeac6c11000000
	VFNMSUB213PD -7(DI)(R8*4), Z3, K4, Z21             // 62a2e54caeac87f9ffffff
	VFNMSUB213PS X27, X8, K7, X18                      // 62823d0faed3
	VFNMSUB213PS 17(SP)(BP*1), X8, K7, X18             // 62e23d0fae942c11000000
	VFNMSUB213PS -7(CX)(DX*8), X8, K7, X18             // 62e23d0fae94d1f9ffffff
	VFNMSUB213PS Y3, Y0, K2, Y6                        // 62f27d2aaef3
	VFNMSUB213PS 7(AX), Y0, K2, Y6                     // 62f27d2aaeb007000000
	VFNMSUB213PS (DI), Y0, K2, Y6                      // 62f27d2aae37
	VFNMSUB213PS Z3, Z11, K5, Z21                      // 62e2254daeeb
	VFNMSUB213PS Z12, Z11, K5, Z21                     // 62c2254daeec
	VFNMSUB213PS Z3, Z25, K5, Z21                      // 62e23545aeeb
	VFNMSUB213PS Z12, Z25, K5, Z21                     // 62c23545aeec
	VFNMSUB213PS Z3, Z11, K5, Z13                      // 6272254daeeb
	VFNMSUB213PS Z12, Z11, K5, Z13                     // 6252254daeec
	VFNMSUB213PS Z3, Z25, K5, Z13                      // 62723545aeeb
	VFNMSUB213PS Z12, Z25, K5, Z13                     // 62523545aeec
	VFNMSUB213PS Z23, Z23, K3, Z27                     // 62224543aedf
	VFNMSUB213PS Z6, Z23, K3, Z27                      // 62624543aede
	VFNMSUB213PS 15(R8), Z23, K3, Z27                  // 62424543ae980f000000
	VFNMSUB213PS (BP), Z23, K3, Z27                    // 62624543ae5d00
	VFNMSUB213PS Z23, Z5, K3, Z27                      // 6222554baedf
	VFNMSUB213PS Z6, Z5, K3, Z27                       // 6262554baede
	VFNMSUB213PS 15(R8), Z5, K3, Z27                   // 6242554bae980f000000
	VFNMSUB213PS (BP), Z5, K3, Z27                     // 6262554bae5d00
	VFNMSUB213PS Z23, Z23, K3, Z15                     // 62324543aeff
	VFNMSUB213PS Z6, Z23, K3, Z15                      // 62724543aefe
	VFNMSUB213PS 15(R8), Z23, K3, Z15                  // 62524543aeb80f000000
	VFNMSUB213PS (BP), Z23, K3, Z15                    // 62724543ae7d00
	VFNMSUB213PS Z23, Z5, K3, Z15                      // 6232554baeff
	VFNMSUB213PS Z6, Z5, K3, Z15                       // 6272554baefe
	VFNMSUB213PS 15(R8), Z5, K3, Z15                   // 6252554baeb80f000000
	VFNMSUB213PS (BP), Z5, K3, Z15                     // 6272554bae7d00
	VFNMSUB213SD X18, X3, K4, X25                      // 6222e50cafca
	VFNMSUB213SD X15, X28, K2, X15                     // 62529d02afff or 62529d22afff or 62529d42afff
	VFNMSUB213SD 99(R15)(R15*8), X28, K2, X15          // 62129d02afbcff63000000 or 62129d22afbcff63000000 or 62129d42afbcff63000000
	VFNMSUB213SD 7(AX)(CX*8), X28, K2, X15             // 62729d02afbcc807000000 or 62729d22afbcc807000000 or 62729d42afbcc807000000
	VFNMSUB213SS X8, X13, K2, X7                       // 62d2150aaff8
	VFNMSUB213SS X0, X7, K3, X24                       // 6262450bafc0 or 6262452bafc0 or 6262454bafc0
	VFNMSUB213SS -17(BP)(SI*8), X7, K3, X24            // 6262450baf84f5efffffff or 6262452baf84f5efffffff or 6262454baf84f5efffffff
	VFNMSUB213SS (R15), X7, K3, X24                    // 6242450baf07 or 6242452baf07 or 6242454baf07
	VFNMSUB231PD X11, X1, K3, X22                      // 62c2f50bbef3
	VFNMSUB231PD -17(BP)(SI*2), X1, K3, X22            // 62e2f50bbeb475efffffff
	VFNMSUB231PD 7(AX)(CX*2), X1, K3, X22              // 62e2f50bbeb44807000000
	VFNMSUB231PD Y12, Y20, K3, Y5                      // 62d2dd23beec
	VFNMSUB231PD 99(R15)(R15*1), Y20, K3, Y5           // 6292dd23beac3f63000000
	VFNMSUB231PD (DX), Y20, K3, Y5                     // 62f2dd23be2a
	VFNMSUB231PD Z16, Z21, K2, Z8                      // 6232d542bec0
	VFNMSUB231PD Z13, Z21, K2, Z8                      // 6252d542bec5
	VFNMSUB231PD Z16, Z5, K2, Z8                       // 6232d54abec0
	VFNMSUB231PD Z13, Z5, K2, Z8                       // 6252d54abec5
	VFNMSUB231PD Z16, Z21, K2, Z28                     // 6222d542bee0
	VFNMSUB231PD Z13, Z21, K2, Z28                     // 6242d542bee5
	VFNMSUB231PD Z16, Z5, K2, Z28                      // 6222d54abee0
	VFNMSUB231PD Z13, Z5, K2, Z28                      // 6242d54abee5
	VFNMSUB231PD Z6, Z22, K1, Z12                      // 6272cd41bee6
	VFNMSUB231PD Z8, Z22, K1, Z12                      // 6252cd41bee0
	VFNMSUB231PD 15(R8)(R14*8), Z22, K1, Z12           // 6212cd41bea4f00f000000
	VFNMSUB231PD -15(R14)(R15*2), Z22, K1, Z12         // 6212cd41bea47ef1ffffff
	VFNMSUB231PD Z6, Z11, K1, Z12                      // 6272a549bee6
	VFNMSUB231PD Z8, Z11, K1, Z12                      // 6252a549bee0
	VFNMSUB231PD 15(R8)(R14*8), Z11, K1, Z12           // 6212a549bea4f00f000000
	VFNMSUB231PD -15(R14)(R15*2), Z11, K1, Z12         // 6212a549bea47ef1ffffff
	VFNMSUB231PD Z6, Z22, K1, Z27                      // 6262cd41bede
	VFNMSUB231PD Z8, Z22, K1, Z27                      // 6242cd41bed8
	VFNMSUB231PD 15(R8)(R14*8), Z22, K1, Z27           // 6202cd41be9cf00f000000
	VFNMSUB231PD -15(R14)(R15*2), Z22, K1, Z27         // 6202cd41be9c7ef1ffffff
	VFNMSUB231PD Z6, Z11, K1, Z27                      // 6262a549bede
	VFNMSUB231PD Z8, Z11, K1, Z27                      // 6242a549bed8
	VFNMSUB231PD 15(R8)(R14*8), Z11, K1, Z27           // 6202a549be9cf00f000000
	VFNMSUB231PD -15(R14)(R15*2), Z11, K1, Z27         // 6202a549be9c7ef1ffffff
	VFNMSUB231PS X8, X7, K2, X6                        // 62d2450abef0
	VFNMSUB231PS 15(R8)(R14*1), X7, K2, X6             // 6292450abeb4300f000000
	VFNMSUB231PS 15(R8)(R14*2), X7, K2, X6             // 6292450abeb4700f000000
	VFNMSUB231PS Y28, Y5, K1, Y3                       // 62925529bedc
	VFNMSUB231PS -17(BP)(SI*8), Y5, K1, Y3             // 62f25529be9cf5efffffff
	VFNMSUB231PS (R15), Y5, K1, Y3                     // 62d25529be1f
	VFNMSUB231PS Z9, Z12, K7, Z25                      // 62421d4fbec9
	VFNMSUB231PS Z12, Z12, K7, Z25                     // 62421d4fbecc
	VFNMSUB231PS Z9, Z17, K7, Z25                      // 62427547bec9
	VFNMSUB231PS Z12, Z17, K7, Z25                     // 62427547becc
	VFNMSUB231PS Z9, Z12, K7, Z12                      // 62521d4fbee1
	VFNMSUB231PS Z12, Z12, K7, Z12                     // 62521d4fbee4
	VFNMSUB231PS Z9, Z17, K7, Z12                      // 62527547bee1
	VFNMSUB231PS Z12, Z17, K7, Z12                     // 62527547bee4
	VFNMSUB231PS Z8, Z3, K1, Z6                        // 62d26549bef0
	VFNMSUB231PS Z2, Z3, K1, Z6                        // 62f26549bef2
	VFNMSUB231PS -15(R14)(R15*1), Z3, K1, Z6           // 62926549beb43ef1ffffff
	VFNMSUB231PS -15(BX), Z3, K1, Z6                   // 62f26549beb3f1ffffff
	VFNMSUB231PS Z8, Z21, K1, Z6                       // 62d25541bef0
	VFNMSUB231PS Z2, Z21, K1, Z6                       // 62f25541bef2
	VFNMSUB231PS -15(R14)(R15*1), Z21, K1, Z6          // 62925541beb43ef1ffffff
	VFNMSUB231PS -15(BX), Z21, K1, Z6                  // 62f25541beb3f1ffffff
	VFNMSUB231PS Z8, Z3, K1, Z25                       // 62426549bec8
	VFNMSUB231PS Z2, Z3, K1, Z25                       // 62626549beca
	VFNMSUB231PS -15(R14)(R15*1), Z3, K1, Z25          // 62026549be8c3ef1ffffff
	VFNMSUB231PS -15(BX), Z3, K1, Z25                  // 62626549be8bf1ffffff
	VFNMSUB231PS Z8, Z21, K1, Z25                      // 62425541bec8
	VFNMSUB231PS Z2, Z21, K1, Z25                      // 62625541beca
	VFNMSUB231PS -15(R14)(R15*1), Z21, K1, Z25         // 62025541be8c3ef1ffffff
	VFNMSUB231PS -15(BX), Z21, K1, Z25                 // 62625541be8bf1ffffff
	VFNMSUB231SD X28, X3, K1, X31                      // 6202e509bffc
	VFNMSUB231SD X7, X24, K1, X20                      // 62e2bd01bfe7 or 62e2bd21bfe7 or 62e2bd41bfe7
	VFNMSUB231SD (AX), X24, K1, X20                    // 62e2bd01bf20 or 62e2bd21bf20 or 62e2bd41bf20
	VFNMSUB231SD 7(SI), X24, K1, X20                   // 62e2bd01bfa607000000 or 62e2bd21bfa607000000 or 62e2bd41bfa607000000
	VFNMSUB231SS X12, X16, K7, X20                     // 62c27d07bfe4
	VFNMSUB231SS X28, X17, K2, X6                      // 62927502bff4 or 62927522bff4 or 62927542bff4
	VFNMSUB231SS 7(SI)(DI*8), X17, K2, X6              // 62f27502bfb4fe07000000 or 62f27522bfb4fe07000000 or 62f27542bfb4fe07000000
	VFNMSUB231SS -15(R14), X17, K2, X6                 // 62d27502bfb6f1ffffff or 62d27522bfb6f1ffffff or 62d27542bfb6f1ffffff
	VGATHERDPD (AX)(X4*1), K3, X6                      // 62f2fd0b923420
	VGATHERDPD (BP)(X10*2), K3, X6                     // 62b2fd0b92745500
	VGATHERDPD (R10)(X29*8), K3, X6                    // 6292fd039234ea
	VGATHERDPD (DX)(X10*4), K7, Y22                    // 62a2fd2f923492
	VGATHERDPD (SP)(X4*2), K7, Y22                     // 62e2fd2f923464
	VGATHERDPD (R14)(X29*8), K7, Y22                   // 6282fd279234ee
	VGATHERDPD (R10)(Y29*8), K4, Z0                    // 6292fd449204ea
	VGATHERDPD (SP)(Y4*2), K4, Z0                      // 62f2fd4c920464
	VGATHERDPD (DX)(Y10*4), K4, Z0                     // 62b2fd4c920492
	VGATHERDPD (R10)(Y29*8), K4, Z6                    // 6292fd449234ea
	VGATHERDPD (SP)(Y4*2), K4, Z6                      // 62f2fd4c923464
	VGATHERDPD (DX)(Y10*4), K4, Z6                     // 62b2fd4c923492
	VGATHERDPS (AX)(X4*1), K4, X0                      // 62f27d0c920420
	VGATHERDPS (BP)(X10*2), K4, X0                     // 62b27d0c92445500
	VGATHERDPS (R10)(X29*8), K4, X0                    // 62927d049204ea
	VGATHERDPS (R14)(Y29*8), K7, Y13                   // 62127d27922cee
	VGATHERDPS (AX)(Y4*1), K7, Y13                     // 62727d2f922c20
	VGATHERDPS (BP)(Y10*2), K7, Y13                    // 62327d2f926c5500
	VGATHERDPS (DX)(Z10*4), K2, Z20                    // 62a27d4a922492
	VGATHERDPS (AX)(Z4*1), K2, Z20                     // 62e27d4a922420
	VGATHERDPS (SP)(Z4*2), K2, Z20                     // 62e27d4a922464
	VGATHERDPS (DX)(Z10*4), K2, Z28                    // 62227d4a922492
	VGATHERDPS (AX)(Z4*1), K2, Z28                     // 62627d4a922420
	VGATHERDPS (SP)(Z4*2), K2, Z28                     // 62627d4a922464
	VGATHERQPD (AX)(X4*1), K2, X11                     // 6272fd0a931c20
	VGATHERQPD (BP)(X10*2), K2, X11                    // 6232fd0a935c5500
	VGATHERQPD (R10)(X29*8), K2, X11                   // 6212fd02931cea
	VGATHERQPD (R10)(Y29*8), K1, Y12                   // 6212fd219324ea
	VGATHERQPD (SP)(Y4*2), K1, Y12                     // 6272fd29932464
	VGATHERQPD (DX)(Y10*4), K1, Y12                    // 6232fd29932492
	VGATHERQPD (DX)(Z10*4), K2, Z3                     // 62b2fd4a931c92
	VGATHERQPD (AX)(Z4*1), K2, Z3                      // 62f2fd4a931c20
	VGATHERQPD (SP)(Z4*2), K2, Z3                      // 62f2fd4a931c64
	VGATHERQPD (DX)(Z10*4), K2, Z30                    // 6222fd4a933492
	VGATHERQPD (AX)(Z4*1), K2, Z30                     // 6262fd4a933420
	VGATHERQPD (SP)(Z4*2), K2, Z30                     // 6262fd4a933464
	VGATHERQPS (DX)(X10*4), K1, X16                    // 62a27d09930492
	VGATHERQPS (SP)(X4*2), K1, X16                     // 62e27d09930464
	VGATHERQPS (R14)(X29*8), K1, X16                   // 62827d019304ee
	VGATHERQPS (R14)(Y29*8), K7, X6                    // 62927d279334ee
	VGATHERQPS (AX)(Y4*1), K7, X6                      // 62f27d2f933420
	VGATHERQPS (BP)(Y10*2), K7, X6                     // 62b27d2f93745500
	VGATHERQPS (BP)(Z10*2), K1, Y1                     // 62b27d49934c5500
	VGATHERQPS (R10)(Z29*8), K1, Y1                    // 62927d41930cea
	VGATHERQPS (R14)(Z29*8), K1, Y1                    // 62927d41930cee
	VGETEXPPD X22, K1, X6                              // 62b2fd0942f6
	VGETEXPPD (CX), K1, X6                             // 62f2fd094231
	VGETEXPPD 99(R15), K1, X6                          // 62d2fd0942b763000000
	VGETEXPPD Y17, K1, Y14                             // 6232fd2942f1
	VGETEXPPD -7(DI)(R8*1), K1, Y14                    // 6232fd2942b407f9ffffff
	VGETEXPPD (SP), K1, Y14                            // 6272fd29423424
	VGETEXPPD Z12, K7, Z9                              // 6252fd4f42cc
	VGETEXPPD Z22, K7, Z9                              // 6232fd4f42ce
	VGETEXPPD Z12, K7, Z19                             // 62c2fd4f42dc
	VGETEXPPD Z22, K7, Z19                             // 62a2fd4f42de
	VGETEXPPD Z18, K2, Z11                             // 6232fd4a42da
	VGETEXPPD Z24, K2, Z11                             // 6212fd4a42d8
	VGETEXPPD 17(SP)(BP*8), K2, Z11                    // 6272fd4a429cec11000000
	VGETEXPPD 17(SP)(BP*4), K2, Z11                    // 6272fd4a429cac11000000
	VGETEXPPD Z18, K2, Z5                              // 62b2fd4a42ea
	VGETEXPPD Z24, K2, Z5                              // 6292fd4a42e8
	VGETEXPPD 17(SP)(BP*8), K2, Z5                     // 62f2fd4a42acec11000000
	VGETEXPPD 17(SP)(BP*4), K2, Z5                     // 62f2fd4a42acac11000000
	VGETEXPPS X16, K4, X12                             // 62327d0c42e0
	VGETEXPPS 99(R15)(R15*2), K4, X12                  // 62127d0c42a47f63000000
	VGETEXPPS -7(DI), K4, X12                          // 62727d0c42a7f9ffffff
	VGETEXPPS Y9, K1, Y7                               // 62d27d2942f9
	VGETEXPPS -7(CX), K1, Y7                           // 62f27d2942b9f9ffffff
	VGETEXPPS 15(DX)(BX*4), K1, Y7                     // 62f27d2942bc9a0f000000
	VGETEXPPS Z7, K3, Z2                               // 62f27d4b42d7
	VGETEXPPS Z13, K3, Z2                              // 62d27d4b42d5
	VGETEXPPS Z7, K3, Z21                              // 62e27d4b42ef
	VGETEXPPS Z13, K3, Z21                             // 62c27d4b42ed
	VGETEXPPS Z6, K4, Z6                               // 62f27d4c42f6
	VGETEXPPS Z22, K4, Z6                              // 62b27d4c42f6
	VGETEXPPS 7(SI)(DI*4), K4, Z6                      // 62f27d4c42b4be07000000
	VGETEXPPS -7(DI)(R8*2), K4, Z6                     // 62b27d4c42b447f9ffffff
	VGETEXPPS Z6, K4, Z16                              // 62e27d4c42c6
	VGETEXPPS Z22, K4, Z16                             // 62a27d4c42c6
	VGETEXPPS 7(SI)(DI*4), K4, Z16                     // 62e27d4c4284be07000000
	VGETEXPPS -7(DI)(R8*2), K4, Z16                    // 62a27d4c428447f9ffffff
	VGETEXPSD X15, X8, K5, X28                         // 6242bd0d43e7
	VGETEXPSD X19, X1, K7, X11                         // 6232f50f43db or 6232f52f43db or 6232f54f43db
	VGETEXPSD 15(R8)(R14*4), X1, K7, X11               // 6212f50f439cb00f000000 or 6212f52f439cb00f000000 or 6212f54f439cb00f000000
	VGETEXPSD -7(CX)(DX*4), X1, K7, X11                // 6272f50f439c91f9ffffff or 6272f52f439c91f9ffffff or 6272f54f439c91f9ffffff
	VGETEXPSS X14, X2, K7, X13                         // 62526d0f43ee
	VGETEXPSS X25, X0, K6, X0                          // 62927d0e43c1 or 62927d2e43c1 or 62927d4e43c1
	VGETEXPSS -7(DI)(R8*1), X0, K6, X0                 // 62b27d0e438407f9ffffff or 62b27d2e438407f9ffffff or 62b27d4e438407f9ffffff
	VGETEXPSS (SP), X0, K6, X0                         // 62f27d0e430424 or 62f27d2e430424 or 62f27d4e430424
	VGETMANTPD $15, X17, K3, X11                       // 6233fd0b26d90f
	VGETMANTPD $15, -7(CX)(DX*1), K3, X11              // 6273fd0b269c11f9ffffff0f
	VGETMANTPD $15, -15(R14)(R15*4), K3, X11           // 6213fd0b269cbef1ffffff0f
	VGETMANTPD $0, Y8, K7, Y31                         // 6243fd2f26f800
	VGETMANTPD $0, 99(R15)(R15*8), K7, Y31             // 6203fd2f26bcff6300000000
	VGETMANTPD $0, 7(AX)(CX*8), K7, Y31                // 6263fd2f26bcc80700000000
	VGETMANTPD $1, Z13, K4, Z1                         // 62d3fd4c26cd01
	VGETMANTPD $1, Z13, K4, Z15                        // 6253fd4c26fd01
	VGETMANTPD $2, Z22, K4, Z18                        // 62a3fd4c26d602
	VGETMANTPD $2, Z7, K4, Z18                         // 62e3fd4c26d702
	VGETMANTPD $2, 17(SP), K4, Z18                     // 62e3fd4c2694241100000002
	VGETMANTPD $2, -17(BP)(SI*4), K4, Z18              // 62e3fd4c2694b5efffffff02
	VGETMANTPD $2, Z22, K4, Z8                         // 6233fd4c26c602
	VGETMANTPD $2, Z7, K4, Z8                          // 6273fd4c26c702
	VGETMANTPD $2, 17(SP), K4, Z8                      // 6273fd4c2684241100000002
	VGETMANTPD $2, -17(BP)(SI*4), K4, Z8               // 6273fd4c2684b5efffffff02
	VGETMANTPS $3, X11, K7, X18                        // 62c37d0f26d303
	VGETMANTPS $3, 15(DX)(BX*1), K7, X18               // 62e37d0f26941a0f00000003
	VGETMANTPS $3, -7(CX)(DX*2), K7, X18               // 62e37d0f269451f9ffffff03
	VGETMANTPS $4, Y28, K2, Y1                         // 62937d2a26cc04
	VGETMANTPS $4, (AX), K2, Y1                        // 62f37d2a260804
	VGETMANTPS $4, 7(SI), K2, Y1                       // 62f37d2a268e0700000004
	VGETMANTPS $5, Z20, K5, Z2                         // 62b37d4d26d405
	VGETMANTPS $5, Z9, K5, Z2                          // 62d37d4d26d105
	VGETMANTPS $5, Z20, K5, Z31                        // 62237d4d26fc05
	VGETMANTPS $5, Z9, K5, Z31                         // 62437d4d26f905
	VGETMANTPS $6, Z12, K3, Z1                         // 62d37d4b26cc06
	VGETMANTPS $6, Z16, K3, Z1                         // 62b37d4b26c806
	VGETMANTPS $6, 7(AX), K3, Z1                       // 62f37d4b26880700000006
	VGETMANTPS $6, (DI), K3, Z1                        // 62f37d4b260f06
	VGETMANTPS $6, Z12, K3, Z3                         // 62d37d4b26dc06
	VGETMANTPS $6, Z16, K3, Z3                         // 62b37d4b26d806
	VGETMANTPS $6, 7(AX), K3, Z3                       // 62f37d4b26980700000006
	VGETMANTPS $6, (DI), K3, Z3                        // 62f37d4b261f06
	VGETMANTSD $7, X24, X2, K4, X9                     // 6213ed0c27c807
	VGETMANTSD $8, X27, X2, K2, X2                     // 6293ed0a27d308 or 6293ed2a27d308 or 6293ed4a27d308
	VGETMANTSD $8, (R8), X2, K2, X2                    // 62d3ed0a271008 or 62d3ed2a271008 or 62d3ed4a271008
	VGETMANTSD $8, 15(DX)(BX*2), X2, K2, X2            // 62f3ed0a27945a0f00000008 or 62f3ed2a27945a0f00000008 or 62f3ed4a27945a0f00000008
	VGETMANTSS $9, X30, X22, K2, X26                   // 62034d0227d609
	VGETMANTSS $10, X15, X11, K3, X3                   // 62d3250b27df0a or 62d3252b27df0a or 62d3254b27df0a
	VGETMANTSS $10, -7(CX), X11, K3, X3                // 62f3250b2799f9ffffff0a or 62f3252b2799f9ffffff0a or 62f3254b2799f9ffffff0a
	VGETMANTSS $10, 15(DX)(BX*4), X11, K3, X3          // 62f3250b279c9a0f0000000a or 62f3252b279c9a0f0000000a or 62f3254b279c9a0f0000000a
	VINSERTF32X4 $0, X9, Y9, K1, Y2                    // 62d3352918d100
	VINSERTF32X4 $0, 15(R8)(R14*8), Y9, K1, Y2         // 629335291894f00f00000000
	VINSERTF32X4 $0, -15(R14)(R15*2), Y9, K1, Y2       // 6293352918947ef1ffffff00
	VINSERTF32X4 $0, X26, Z20, K7, Z16                 // 62835d4718c200
	VINSERTF32X4 $0, -15(R14)(R15*1), Z20, K7, Z16     // 62835d4718843ef1ffffff00
	VINSERTF32X4 $0, -15(BX), Z20, K7, Z16             // 62e35d471883f1ffffff00
	VINSERTF32X4 $0, X26, Z0, K7, Z16                  // 62837d4f18c200
	VINSERTF32X4 $0, -15(R14)(R15*1), Z0, K7, Z16      // 62837d4f18843ef1ffffff00
	VINSERTF32X4 $0, -15(BX), Z0, K7, Z16              // 62e37d4f1883f1ffffff00
	VINSERTF32X4 $0, X26, Z20, K7, Z9                  // 62135d4718ca00
	VINSERTF32X4 $0, -15(R14)(R15*1), Z20, K7, Z9      // 62135d47188c3ef1ffffff00
	VINSERTF32X4 $0, -15(BX), Z20, K7, Z9              // 62735d47188bf1ffffff00
	VINSERTF32X4 $0, X26, Z0, K7, Z9                   // 62137d4f18ca00
	VINSERTF32X4 $0, -15(R14)(R15*1), Z0, K7, Z9       // 62137d4f188c3ef1ffffff00
	VINSERTF32X4 $0, -15(BX), Z0, K7, Z9               // 62737d4f188bf1ffffff00
	VINSERTF64X4 $1, Y30, Z9, K3, Z0                   // 6293b54b1ac601
	VINSERTF64X4 $1, -17(BP)(SI*2), Z9, K3, Z0         // 62f3b54b1a8475efffffff01
	VINSERTF64X4 $1, 7(AX)(CX*2), Z9, K3, Z0           // 62f3b54b1a84480700000001
	VINSERTF64X4 $1, Y30, Z3, K3, Z0                   // 6293e54b1ac601
	VINSERTF64X4 $1, -17(BP)(SI*2), Z3, K3, Z0         // 62f3e54b1a8475efffffff01
	VINSERTF64X4 $1, 7(AX)(CX*2), Z3, K3, Z0           // 62f3e54b1a84480700000001
	VINSERTF64X4 $1, Y30, Z9, K3, Z26                  // 6203b54b1ad601
	VINSERTF64X4 $1, -17(BP)(SI*2), Z9, K3, Z26        // 6263b54b1a9475efffffff01
	VINSERTF64X4 $1, 7(AX)(CX*2), Z9, K3, Z26          // 6263b54b1a94480700000001
	VINSERTF64X4 $1, Y30, Z3, K3, Z26                  // 6203e54b1ad601
	VINSERTF64X4 $1, -17(BP)(SI*2), Z3, K3, Z26        // 6263e54b1a9475efffffff01
	VINSERTF64X4 $1, 7(AX)(CX*2), Z3, K3, Z26          // 6263e54b1a94480700000001
	VINSERTI32X4 $0, X31, Y7, K4, Y26                  // 6203452c38d700
	VINSERTI32X4 $0, 17(SP)(BP*8), Y7, K4, Y26         // 6263452c3894ec1100000000
	VINSERTI32X4 $0, 17(SP)(BP*4), Y7, K4, Y26         // 6263452c3894ac1100000000
	VINSERTI32X4 $2, X16, Z9, K5, Z9                   // 6233354d38c802
	VINSERTI32X4 $2, 7(SI)(DI*4), Z9, K5, Z9           // 6273354d388cbe0700000002
	VINSERTI32X4 $2, -7(DI)(R8*2), Z9, K5, Z9          // 6233354d388c47f9ffffff02
	VINSERTI32X4 $2, X16, Z28, K5, Z9                  // 62331d4538c802
	VINSERTI32X4 $2, 7(SI)(DI*4), Z28, K5, Z9          // 62731d45388cbe0700000002
	VINSERTI32X4 $2, -7(DI)(R8*2), Z28, K5, Z9         // 62331d45388c47f9ffffff02
	VINSERTI32X4 $2, X16, Z9, K5, Z25                  // 6223354d38c802
	VINSERTI32X4 $2, 7(SI)(DI*4), Z9, K5, Z25          // 6263354d388cbe0700000002
	VINSERTI32X4 $2, -7(DI)(R8*2), Z9, K5, Z25         // 6223354d388c47f9ffffff02
	VINSERTI32X4 $2, X16, Z28, K5, Z25                 // 62231d4538c802
	VINSERTI32X4 $2, 7(SI)(DI*4), Z28, K5, Z25         // 62631d45388cbe0700000002
	VINSERTI32X4 $2, -7(DI)(R8*2), Z28, K5, Z25        // 62231d45388c47f9ffffff02
	VINSERTI64X4 $1, Y31, Z6, K3, Z21                  // 6283cd4b3aef01
	VINSERTI64X4 $1, (R14), Z6, K3, Z21                // 62c3cd4b3a2e01
	VINSERTI64X4 $1, -7(DI)(R8*8), Z6, K3, Z21         // 62a3cd4b3aacc7f9ffffff01
	VINSERTI64X4 $1, Y31, Z9, K3, Z21                  // 6283b54b3aef01
	VINSERTI64X4 $1, (R14), Z9, K3, Z21                // 62c3b54b3a2e01
	VINSERTI64X4 $1, -7(DI)(R8*8), Z9, K3, Z21         // 62a3b54b3aacc7f9ffffff01
	VINSERTI64X4 $1, Y31, Z6, K3, Z9                   // 6213cd4b3acf01
	VINSERTI64X4 $1, (R14), Z6, K3, Z9                 // 6253cd4b3a0e01
	VINSERTI64X4 $1, -7(DI)(R8*8), Z6, K3, Z9          // 6233cd4b3a8cc7f9ffffff01
	VINSERTI64X4 $1, Y31, Z9, K3, Z9                   // 6213b54b3acf01
	VINSERTI64X4 $1, (R14), Z9, K3, Z9                 // 6253b54b3a0e01
	VINSERTI64X4 $1, -7(DI)(R8*8), Z9, K3, Z9          // 6233b54b3a8cc7f9ffffff01
	VMAXPD X21, X16, K7, X0                            // 62b1fd075fc5
	VMAXPD 99(R15)(R15*1), X16, K7, X0                 // 6291fd075f843f63000000
	VMAXPD (DX), X16, K7, X0                           // 62f1fd075f02
	VMAXPD Y21, Y6, K4, Y22                            // 62a1cd2c5ff5
	VMAXPD 99(R15)(R15*4), Y6, K4, Y22                 // 6281cd2c5fb4bf63000000
	VMAXPD 15(DX), Y6, K4, Y22                         // 62e1cd2c5fb20f000000
	VMAXPD Z30, Z20, K4, Z1                            // 6291dd445fce
	VMAXPD Z5, Z20, K4, Z1                             // 62f1dd445fcd
	VMAXPD Z30, Z9, K4, Z1                             // 6291b54c5fce
	VMAXPD Z5, Z9, K4, Z1                              // 62f1b54c5fcd
	VMAXPD Z30, Z20, K4, Z9                            // 6211dd445fce
	VMAXPD Z5, Z20, K4, Z9                             // 6271dd445fcd
	VMAXPD Z30, Z9, K4, Z9                             // 6211b54c5fce
	VMAXPD Z5, Z9, K4, Z9                              // 6271b54c5fcd
	VMAXPD Z16, Z7, K7, Z26                            // 6221c54f5fd0
	VMAXPD Z25, Z7, K7, Z26                            // 6201c54f5fd1
	VMAXPD 7(SI)(DI*1), Z7, K7, Z26                    // 6261c54f5f943e07000000
	VMAXPD 15(DX)(BX*8), Z7, K7, Z26                   // 6261c54f5f94da0f000000
	VMAXPD Z16, Z21, K7, Z26                           // 6221d5475fd0
	VMAXPD Z25, Z21, K7, Z26                           // 6201d5475fd1
	VMAXPD 7(SI)(DI*1), Z21, K7, Z26                   // 6261d5475f943e07000000
	VMAXPD 15(DX)(BX*8), Z21, K7, Z26                  // 6261d5475f94da0f000000
	VMAXPD Z16, Z7, K7, Z22                            // 62a1c54f5ff0
	VMAXPD Z25, Z7, K7, Z22                            // 6281c54f5ff1
	VMAXPD 7(SI)(DI*1), Z7, K7, Z22                    // 62e1c54f5fb43e07000000
	VMAXPD 15(DX)(BX*8), Z7, K7, Z22                   // 62e1c54f5fb4da0f000000
	VMAXPD Z16, Z21, K7, Z22                           // 62a1d5475ff0
	VMAXPD Z25, Z21, K7, Z22                           // 6281d5475ff1
	VMAXPD 7(SI)(DI*1), Z21, K7, Z22                   // 62e1d5475fb43e07000000
	VMAXPD 15(DX)(BX*8), Z21, K7, Z22                  // 62e1d5475fb4da0f000000
	VMAXPS X22, X28, K2, X0                            // 62b11c025fc6
	VMAXPS -17(BP)(SI*8), X28, K2, X0                  // 62f11c025f84f5efffffff
	VMAXPS (R15), X28, K2, X0                          // 62d11c025f07
	VMAXPS Y28, Y0, K5, Y7                             // 62917c2d5ffc
	VMAXPS (CX), Y0, K5, Y7                            // 62f17c2d5f39
	VMAXPS 99(R15), Y0, K5, Y7                         // 62d17c2d5fbf63000000
	VMAXPS Z21, Z12, K3, Z14                           // 62311c4b5ff5
	VMAXPS Z9, Z12, K3, Z14                            // 62511c4b5ff1
	VMAXPS Z21, Z13, K3, Z14                           // 6231144b5ff5
	VMAXPS Z9, Z13, K3, Z14                            // 6251144b5ff1
	VMAXPS Z21, Z12, K3, Z13                           // 62311c4b5fed
	VMAXPS Z9, Z12, K3, Z13                            // 62511c4b5fe9
	VMAXPS Z21, Z13, K3, Z13                           // 6231144b5fed
	VMAXPS Z9, Z13, K3, Z13                            // 6251144b5fe9
	VMAXPS Z23, Z27, K4, Z2                            // 62b124445fd7
	VMAXPS Z9, Z27, K4, Z2                             // 62d124445fd1
	VMAXPS -7(DI)(R8*1), Z27, K4, Z2                   // 62b124445f9407f9ffffff
	VMAXPS (SP), Z27, K4, Z2                           // 62f124445f1424
	VMAXPS Z23, Z25, K4, Z2                            // 62b134445fd7
	VMAXPS Z9, Z25, K4, Z2                             // 62d134445fd1
	VMAXPS -7(DI)(R8*1), Z25, K4, Z2                   // 62b134445f9407f9ffffff
	VMAXPS (SP), Z25, K4, Z2                           // 62f134445f1424
	VMAXPS Z23, Z27, K4, Z7                            // 62b124445fff
	VMAXPS Z9, Z27, K4, Z7                             // 62d124445ff9
	VMAXPS -7(DI)(R8*1), Z27, K4, Z7                   // 62b124445fbc07f9ffffff
	VMAXPS (SP), Z27, K4, Z7                           // 62f124445f3c24
	VMAXPS Z23, Z25, K4, Z7                            // 62b134445fff
	VMAXPS Z9, Z25, K4, Z7                             // 62d134445ff9
	VMAXPS -7(DI)(R8*1), Z25, K4, Z7                   // 62b134445fbc07f9ffffff
	VMAXPS (SP), Z25, K4, Z7                           // 62f134445f3c24
	VMAXSD X7, X19, K2, X7                             // 62f1e7025fff
	VMAXSD X1, X31, K2, X16                            // 62e187025fc1 or 62e187225fc1 or 62e187425fc1
	VMAXSD 17(SP)(BP*1), X31, K2, X16                  // 62e187025f842c11000000 or 62e187225f842c11000000 or 62e187425f842c11000000
	VMAXSD -7(CX)(DX*8), X31, K2, X16                  // 62e187025f84d1f9ffffff or 62e187225f84d1f9ffffff or 62e187425f84d1f9ffffff
	VMAXSS X15, X9, K3, X7                             // 62d1360b5fff
	VMAXSS X12, X0, K3, X12                            // 62517e0b5fe4 or 62517e2b5fe4 or 62517e4b5fe4
	VMAXSS (AX), X0, K3, X12                           // 62717e0b5f20 or 62717e2b5f20 or 62717e4b5f20
	VMAXSS 7(SI), X0, K3, X12                          // 62717e0b5fa607000000 or 62717e2b5fa607000000 or 62717e4b5fa607000000
	VMINPD X17, X5, K3, X14                            // 6231d50b5df1
	VMINPD 7(SI)(DI*8), X5, K3, X14                    // 6271d50b5db4fe07000000
	VMINPD -15(R14), X5, K3, X14                       // 6251d50b5db6f1ffffff
	VMINPD Y24, Y14, K2, Y20                           // 62818d2a5de0
	VMINPD 99(R15)(R15*2), Y14, K2, Y20                // 62818d2a5da47f63000000
	VMINPD -7(DI), Y14, K2, Y20                        // 62e18d2a5da7f9ffffff
	VMINPD Z14, Z3, K1, Z27                            // 6241e5495dde
	VMINPD Z7, Z3, K1, Z27                             // 6261e5495ddf
	VMINPD Z14, Z0, K1, Z27                            // 6241fd495dde
	VMINPD Z7, Z0, K1, Z27                             // 6261fd495ddf
	VMINPD Z14, Z3, K1, Z14                            // 6251e5495df6
	VMINPD Z7, Z3, K1, Z14                             // 6271e5495df7
	VMINPD Z14, Z0, K1, Z14                            // 6251fd495df6
	VMINPD Z7, Z0, K1, Z14                             // 6271fd495df7
	VMINPD Z1, Z22, K2, Z8                             // 6271cd425dc1
	VMINPD Z16, Z22, K2, Z8                            // 6231cd425dc0
	VMINPD -7(CX), Z22, K2, Z8                         // 6271cd425d81f9ffffff
	VMINPD 15(DX)(BX*4), Z22, K2, Z8                   // 6271cd425d849a0f000000
	VMINPD Z1, Z25, K2, Z8                             // 6271b5425dc1
	VMINPD Z16, Z25, K2, Z8                            // 6231b5425dc0
	VMINPD -7(CX), Z25, K2, Z8                         // 6271b5425d81f9ffffff
	VMINPD 15(DX)(BX*4), Z25, K2, Z8                   // 6271b5425d849a0f000000
	VMINPD Z1, Z22, K2, Z24                            // 6261cd425dc1
	VMINPD Z16, Z22, K2, Z24                           // 6221cd425dc0
	VMINPD -7(CX), Z22, K2, Z24                        // 6261cd425d81f9ffffff
	VMINPD 15(DX)(BX*4), Z22, K2, Z24                  // 6261cd425d849a0f000000
	VMINPD Z1, Z25, K2, Z24                            // 6261b5425dc1
	VMINPD Z16, Z25, K2, Z24                           // 6221b5425dc0
	VMINPD -7(CX), Z25, K2, Z24                        // 6261b5425d81f9ffffff
	VMINPD 15(DX)(BX*4), Z25, K2, Z24                  // 6261b5425d849a0f000000
	VMINPS X3, X8, K1, X15                             // 62713c095dfb
	VMINPS 7(SI)(DI*1), X8, K1, X15                    // 62713c095dbc3e07000000
	VMINPS 15(DX)(BX*8), X8, K1, X15                   // 62713c095dbcda0f000000
	VMINPS Y14, Y20, K7, Y13                           // 62515c275dee
	VMINPS -7(CX)(DX*1), Y20, K7, Y13                  // 62715c275dac11f9ffffff
	VMINPS -15(R14)(R15*4), Y20, K7, Y13               // 62115c275dacbef1ffffff
	VMINPS Z15, Z0, K1, Z6                             // 62d17c495df7
	VMINPS Z12, Z0, K1, Z6                             // 62d17c495df4
	VMINPS Z15, Z8, K1, Z6                             // 62d13c495df7
	VMINPS Z12, Z8, K1, Z6                             // 62d13c495df4
	VMINPS Z15, Z0, K1, Z2                             // 62d17c495dd7
	VMINPS Z12, Z0, K1, Z2                             // 62d17c495dd4
	VMINPS Z15, Z8, K1, Z2                             // 62d13c495dd7
	VMINPS Z12, Z8, K1, Z2                             // 62d13c495dd4
	VMINPS Z13, Z11, K1, Z14                           // 625124495df5
	VMINPS Z14, Z11, K1, Z14                           // 625124495df6
	VMINPS 99(R15)(R15*8), Z11, K1, Z14                // 621124495db4ff63000000
	VMINPS 7(AX)(CX*8), Z11, K1, Z14                   // 627124495db4c807000000
	VMINPS Z13, Z5, K1, Z14                            // 625154495df5
	VMINPS Z14, Z5, K1, Z14                            // 625154495df6
	VMINPS 99(R15)(R15*8), Z5, K1, Z14                 // 621154495db4ff63000000
	VMINPS 7(AX)(CX*8), Z5, K1, Z14                    // 627154495db4c807000000
	VMINPS Z13, Z11, K1, Z27                           // 624124495ddd
	VMINPS Z14, Z11, K1, Z27                           // 624124495dde
	VMINPS 99(R15)(R15*8), Z11, K1, Z27                // 620124495d9cff63000000
	VMINPS 7(AX)(CX*8), Z11, K1, Z27                   // 626124495d9cc807000000
	VMINPS Z13, Z5, K1, Z27                            // 624154495ddd
	VMINPS Z14, Z5, K1, Z27                            // 624154495dde
	VMINPS 99(R15)(R15*8), Z5, K1, Z27                 // 620154495d9cff63000000
	VMINPS 7(AX)(CX*8), Z5, K1, Z27                    // 626154495d9cc807000000
	VMINSD X13, X23, K1, X26                           // 6241c7015dd5
	VMINSD X9, X24, K7, X28                            // 6241bf075de1 or 6241bf275de1 or 6241bf475de1
	VMINSD -17(BP)(SI*2), X24, K7, X28                 // 6261bf075da475efffffff or 6261bf275da475efffffff or 6261bf475da475efffffff
	VMINSD 7(AX)(CX*2), X24, K7, X28                   // 6261bf075da44807000000 or 6261bf275da44807000000 or 6261bf475da44807000000
	VMINSS X18, X26, K2, X15                           // 62312e025dfa
	VMINSS X11, X1, K4, X21                            // 62c1760c5deb or 62c1762c5deb or 62c1764c5deb
	VMINSS (BX), X1, K4, X21                           // 62e1760c5d2b or 62e1762c5d2b or 62e1764c5d2b
	VMINSS -17(BP)(SI*1), X1, K4, X21                  // 62e1760c5dac35efffffff or 62e1762c5dac35efffffff or 62e1764c5dac35efffffff
	VMOVAPD X3, K1, X31                                // 6291fd0929df
	VMOVAPD X3, K1, -7(DI)(R8*1)                       // 62b1fd09299c07f9ffffff
	VMOVAPD X3, K1, (SP)                               // 62f1fd09291c24
	VMOVAPD X0, K3, X7                                 // 62f1fd0b29c7
	VMOVAPD -7(CX), K3, X7                             // 62f1fd0b28b9f9ffffff
	VMOVAPD 15(DX)(BX*4), K3, X7                       // 62f1fd0b28bc9a0f000000
	VMOVAPD Y1, K4, Y21                                // 62b1fd2c29cd
	VMOVAPD Y1, K4, 15(DX)(BX*1)                       // 62f1fd2c298c1a0f000000
	VMOVAPD Y1, K4, -7(CX)(DX*2)                       // 62f1fd2c298c51f9ffffff
	VMOVAPD Y30, K5, Y26                               // 6201fd2d29f2
	VMOVAPD -17(BP), K5, Y26                           // 6261fd2d2895efffffff
	VMOVAPD -15(R14)(R15*8), K5, Y26                   // 6201fd2d2894fef1ffffff
	VMOVAPD Z2, K7, Z5                                 // 62f1fd4f29d5
	VMOVAPD Z2, K7, Z23                                // 62b1fd4f29d7
	VMOVAPD Z2, K7, (AX)                               // 62f1fd4f2910
	VMOVAPD Z2, K7, 7(SI)                              // 62f1fd4f299607000000
	VMOVAPD Z26, K7, Z6                                // 6261fd4f29d6
	VMOVAPD Z14, K7, Z6                                // 6271fd4f29f6
	VMOVAPD (BX), K7, Z6                               // 62f1fd4f2833
	VMOVAPD -17(BP)(SI*1), K7, Z6                      // 62f1fd4f28b435efffffff
	VMOVAPD Z26, K7, Z14                               // 6241fd4f29d6
	VMOVAPD Z14, K7, Z14                               // 6251fd4f29f6
	VMOVAPD (BX), K7, Z14                              // 6271fd4f2833
	VMOVAPD -17(BP)(SI*1), K7, Z14                     // 6271fd4f28b435efffffff
	VMOVAPS X24, K6, X0                                // 62617c0e29c0
	VMOVAPS X24, K6, 99(R15)(R15*8)                    // 62017c0e2984ff63000000
	VMOVAPS X24, K6, 7(AX)(CX*8)                       // 62617c0e2984c807000000
	VMOVAPS X7, K3, X20                                // 62b17c0b29fc
	VMOVAPS (AX), K3, X20                              // 62e17c0b2820
	VMOVAPS 7(SI), K3, X20                             // 62e17c0b28a607000000
	VMOVAPS Y22, K7, Y12                               // 62c17c2f29f4
	VMOVAPS Y22, K7, 17(SP)(BP*2)                      // 62e17c2f29b46c11000000
	VMOVAPS Y22, K7, -7(DI)(R8*4)                      // 62a17c2f29b487f9ffffff
	VMOVAPS Y15, K4, Y3                                // 62717c2c29fb
	VMOVAPS 15(R8), K4, Y3                             // 62d17c2c28980f000000
	VMOVAPS (BP), K4, Y3                               // 62f17c2c285d00
	VMOVAPS Z13, K4, Z28                               // 62117c4c29ec
	VMOVAPS Z21, K4, Z28                               // 62817c4c29ec
	VMOVAPS Z13, K4, Z6                                // 62717c4c29ee
	VMOVAPS Z21, K4, Z6                                // 62e17c4c29ee
	VMOVAPS Z13, K4, 15(R8)(R14*4)                     // 62117c4c29acb00f000000
	VMOVAPS Z21, K4, 15(R8)(R14*4)                     // 62817c4c29acb00f000000
	VMOVAPS Z13, K4, -7(CX)(DX*4)                      // 62717c4c29ac91f9ffffff
	VMOVAPS Z21, K4, -7(CX)(DX*4)                      // 62e17c4c29ac91f9ffffff
	VMOVAPS Z3, K7, Z26                                // 62917c4f29da
	VMOVAPS Z0, K7, Z26                                // 62917c4f29c2
	VMOVAPS (R8), K7, Z26                              // 62417c4f2810
	VMOVAPS 15(DX)(BX*2), K7, Z26                      // 62617c4f28945a0f000000
	VMOVAPS Z3, K7, Z3                                 // 62f17c4f29db
	VMOVAPS Z0, K7, Z3                                 // 62f17c4f29c3
	VMOVAPS (R8), K7, Z3                               // 62d17c4f2818
	VMOVAPS 15(DX)(BX*2), K7, Z3                       // 62f17c4f289c5a0f000000
	VMOVDDUP X5, K2, X14                               // 6271ff0a12f5
	VMOVDDUP 15(R8)(R14*1), K2, X14                    // 6211ff0a12b4300f000000
	VMOVDDUP 15(R8)(R14*2), K2, X14                    // 6211ff0a12b4700f000000
	VMOVDDUP Y27, K5, Y1                               // 6291ff2d12cb
	VMOVDDUP 15(R8)(R14*8), K5, Y1                     // 6291ff2d128cf00f000000
	VMOVDDUP -15(R14)(R15*2), K5, Y1                   // 6291ff2d128c7ef1ffffff
	VMOVDDUP Z11, K3, Z21                              // 62c1ff4b12eb
	VMOVDDUP Z25, K3, Z21                              // 6281ff4b12e9
	VMOVDDUP 17(SP)(BP*1), K3, Z21                     // 62e1ff4b12ac2c11000000
	VMOVDDUP -7(CX)(DX*8), K3, Z21                     // 62e1ff4b12acd1f9ffffff
	VMOVDDUP Z11, K3, Z13                              // 6251ff4b12eb
	VMOVDDUP Z25, K3, Z13                              // 6211ff4b12e9
	VMOVDDUP 17(SP)(BP*1), K3, Z13                     // 6271ff4b12ac2c11000000
	VMOVDDUP -7(CX)(DX*8), K3, Z13                     // 6271ff4b12acd1f9ffffff
	VMOVDQA32 X3, K4, X31                              // 62917d0c7fdf
	VMOVDQA32 X3, K4, (BX)                             // 62f17d0c7f1b
	VMOVDQA32 X3, K4, -17(BP)(SI*1)                    // 62f17d0c7f9c35efffffff
	VMOVDQA32 X1, K2, X21                              // 62b17d0a7fcd
	VMOVDQA32 15(R8)(R14*4), K2, X21                   // 62817d0a6facb00f000000
	VMOVDQA32 -7(CX)(DX*4), K2, X21                    // 62e17d0a6fac91f9ffffff
	VMOVDQA32 Y5, K2, Y19                              // 62b17d2a7feb
	VMOVDQA32 Y5, K2, -15(R14)(R15*1)                  // 62917d2a7fac3ef1ffffff
	VMOVDQA32 Y5, K2, -15(BX)                          // 62f17d2a7fabf1ffffff
	VMOVDQA32 Y13, K3, Y17                             // 62317d2b7fe9
	VMOVDQA32 7(AX)(CX*4), K3, Y17                     // 62e17d2b6f8c8807000000
	VMOVDQA32 7(AX)(CX*1), K3, Y17                     // 62e17d2b6f8c0807000000
	VMOVDQA32 Z27, K3, Z3                              // 62617d4b7fdb
	VMOVDQA32 Z15, K3, Z3                              // 62717d4b7ffb
	VMOVDQA32 Z27, K3, Z12                             // 62417d4b7fdc
	VMOVDQA32 Z15, K3, Z12                             // 62517d4b7ffc
	VMOVDQA32 Z27, K3, -17(BP)(SI*2)                   // 62617d4b7f9c75efffffff
	VMOVDQA32 Z15, K3, -17(BP)(SI*2)                   // 62717d4b7fbc75efffffff
	VMOVDQA32 Z27, K3, 7(AX)(CX*2)                     // 62617d4b7f9c4807000000
	VMOVDQA32 Z15, K3, 7(AX)(CX*2)                     // 62717d4b7fbc4807000000
	VMOVDQA32 Z23, K3, Z23                             // 62a17d4b7fff
	VMOVDQA32 Z6, K3, Z23                              // 62b17d4b7ff7
	VMOVDQA32 15(R8)(R14*1), K3, Z23                   // 62817d4b6fbc300f000000
	VMOVDQA32 15(R8)(R14*2), K3, Z23                   // 62817d4b6fbc700f000000
	VMOVDQA32 Z23, K3, Z5                              // 62e17d4b7ffd
	VMOVDQA32 Z6, K3, Z5                               // 62f17d4b7ff5
	VMOVDQA32 15(R8)(R14*1), K3, Z5                    // 62917d4b6fac300f000000
	VMOVDQA32 15(R8)(R14*2), K3, Z5                    // 62917d4b6fac700f000000
	VMOVDQA64 X13, K2, X11                             // 6251fd0a7feb
	VMOVDQA64 X13, K2, (R8)                            // 6251fd0a7f28
	VMOVDQA64 X13, K2, 15(DX)(BX*2)                    // 6271fd0a7fac5a0f000000
	VMOVDQA64 X30, K1, X0                              // 6261fd097ff0
	VMOVDQA64 17(SP)(BP*1), K1, X0                     // 62f1fd096f842c11000000
	VMOVDQA64 -7(CX)(DX*8), K1, X0                     // 62f1fd096f84d1f9ffffff
	VMOVDQA64 Y7, K2, Y21                              // 62b1fd2a7ffd
	VMOVDQA64 Y7, K2, (SI)                             // 62f1fd2a7f3e
	VMOVDQA64 Y7, K2, 7(SI)(DI*2)                      // 62f1fd2a7fbc7e07000000
	VMOVDQA64 Y13, K1, Y30                             // 6211fd297fee
	VMOVDQA64 17(SP)(BP*8), K1, Y30                    // 6261fd296fb4ec11000000
	VMOVDQA64 17(SP)(BP*4), K1, Y30                    // 6261fd296fb4ac11000000
	VMOVDQA64 Z21, K7, Z8                              // 62c1fd4f7fe8
	VMOVDQA64 Z5, K7, Z8                               // 62d1fd4f7fe8
	VMOVDQA64 Z21, K7, Z28                             // 6281fd4f7fec
	VMOVDQA64 Z5, K7, Z28                              // 6291fd4f7fec
	VMOVDQA64 Z21, K7, (R14)                           // 62c1fd4f7f2e
	VMOVDQA64 Z5, K7, (R14)                            // 62d1fd4f7f2e
	VMOVDQA64 Z21, K7, -7(DI)(R8*8)                    // 62a1fd4f7facc7f9ffffff
	VMOVDQA64 Z5, K7, -7(DI)(R8*8)                     // 62b1fd4f7facc7f9ffffff
	VMOVDQA64 Z12, K1, Z16                             // 6231fd497fe0
	VMOVDQA64 Z27, K1, Z16                             // 6221fd497fd8
	VMOVDQA64 99(R15)(R15*4), K1, Z16                  // 6281fd496f84bf63000000
	VMOVDQA64 15(DX), K1, Z16                          // 62e1fd496f820f000000
	VMOVDQA64 Z12, K1, Z13                             // 6251fd497fe5
	VMOVDQA64 Z27, K1, Z13                             // 6241fd497fdd
	VMOVDQA64 99(R15)(R15*4), K1, Z13                  // 6211fd496facbf63000000
	VMOVDQA64 15(DX), K1, Z13                          // 6271fd496faa0f000000
	VMOVDQU32 X8, K3, X19                              // 62317e0b7fc3
	VMOVDQU32 X8, K3, (R14)                            // 62517e0b7f06
	VMOVDQU32 X8, K3, -7(DI)(R8*8)                     // 62317e0b7f84c7f9ffffff
	VMOVDQU32 X26, K4, X8                              // 62417e0c7fd0
	VMOVDQU32 99(R15)(R15*4), K4, X8                   // 62117e0c6f84bf63000000
	VMOVDQU32 15(DX), K4, X8                           // 62717e0c6f820f000000
	VMOVDQU32 Y5, K5, Y24                              // 62917e2d7fe8
	VMOVDQU32 Y5, K5, 7(AX)                            // 62f17e2d7fa807000000
	VMOVDQU32 Y5, K5, (DI)                             // 62f17e2d7f2f
	VMOVDQU32 Y21, K7, Y24                             // 62817e2f7fe8
	VMOVDQU32 99(R15)(R15*1), K7, Y24                  // 62017e2f6f843f63000000
	VMOVDQU32 (DX), K7, Y24                            // 62617e2f6f02
	VMOVDQU32 Z6, K7, Z9                               // 62d17e4f7ff1
	VMOVDQU32 Z25, K7, Z9                              // 62417e4f7fc9
	VMOVDQU32 Z6, K7, Z12                              // 62d17e4f7ff4
	VMOVDQU32 Z25, K7, Z12                             // 62417e4f7fcc
	VMOVDQU32 Z6, K7, -7(CX)(DX*1)                     // 62f17e4f7fb411f9ffffff
	VMOVDQU32 Z25, K7, -7(CX)(DX*1)                    // 62617e4f7f8c11f9ffffff
	VMOVDQU32 Z6, K7, -15(R14)(R15*4)                  // 62917e4f7fb4bef1ffffff
	VMOVDQU32 Z25, K7, -15(R14)(R15*4)                 // 62017e4f7f8cbef1ffffff
	VMOVDQU32 Z8, K6, Z3                               // 62717e4e7fc3
	VMOVDQU32 Z2, K6, Z3                               // 62f17e4e7fd3
	VMOVDQU32 15(DX)(BX*1), K6, Z3                     // 62f17e4e6f9c1a0f000000
	VMOVDQU32 -7(CX)(DX*2), K6, Z3                     // 62f17e4e6f9c51f9ffffff
	VMOVDQU32 Z8, K6, Z21                              // 62317e4e7fc5
	VMOVDQU32 Z2, K6, Z21                              // 62b17e4e7fd5
	VMOVDQU32 15(DX)(BX*1), K6, Z21                    // 62e17e4e6fac1a0f000000
	VMOVDQU32 -7(CX)(DX*2), K6, Z21                    // 62e17e4e6fac51f9ffffff
	VMOVDQU64 X12, K3, X23                             // 6231fe0b7fe7
	VMOVDQU64 X12, K3, (CX)                            // 6271fe0b7f21
	VMOVDQU64 X12, K3, 99(R15)                         // 6251fe0b7fa763000000
	VMOVDQU64 X23, K7, X16                             // 62a1fe0f7ff8
	VMOVDQU64 99(R15)(R15*2), K7, X16                  // 6281fe0f6f847f63000000
	VMOVDQU64 -7(DI), K7, X16                          // 62e1fe0f6f87f9ffffff
	VMOVDQU64 Y9, K4, Y16                              // 6231fe2c7fc8
	VMOVDQU64 Y9, K4, -17(BP)(SI*8)                    // 6271fe2c7f8cf5efffffff
	VMOVDQU64 Y9, K4, (R15)                            // 6251fe2c7f0f
	VMOVDQU64 Y9, K4, Y13                              // 6251fe2c7fcd
	VMOVDQU64 7(SI)(DI*8), K4, Y13                     // 6271fe2c6facfe07000000
	VMOVDQU64 -15(R14), K4, Y13                        // 6251fe2c6faef1ffffff
	VMOVDQU64 Z7, K7, Z3                               // 62f1fe4f7ffb
	VMOVDQU64 Z9, K7, Z3                               // 6271fe4f7fcb
	VMOVDQU64 Z7, K7, Z27                              // 6291fe4f7ffb
	VMOVDQU64 Z9, K7, Z27                              // 6211fe4f7fcb
	VMOVDQU64 Z7, K7, -17(BP)                          // 62f1fe4f7fbdefffffff
	VMOVDQU64 Z9, K7, -17(BP)                          // 6271fe4f7f8defffffff
	VMOVDQU64 Z7, K7, -15(R14)(R15*8)                  // 6291fe4f7fbcfef1ffffff
	VMOVDQU64 Z9, K7, -15(R14)(R15*8)                  // 6211fe4f7f8cfef1ffffff
	VMOVDQU64 Z20, K2, Z0                              // 62e1fe4a7fe0
	VMOVDQU64 Z28, K2, Z0                              // 6261fe4a7fe0
	VMOVDQU64 17(SP)(BP*2), K2, Z0                     // 62f1fe4a6f846c11000000
	VMOVDQU64 -7(DI)(R8*4), K2, Z0                     // 62b1fe4a6f8487f9ffffff
	VMOVDQU64 Z20, K2, Z6                              // 62e1fe4a7fe6
	VMOVDQU64 Z28, K2, Z6                              // 6261fe4a7fe6
	VMOVDQU64 17(SP)(BP*2), K2, Z6                     // 62f1fe4a6fb46c11000000
	VMOVDQU64 -7(DI)(R8*4), K2, Z6                     // 62b1fe4a6fb487f9ffffff
	VMOVHPS (R14), X2, X23                             // 62c16c08163e
	VMOVHPS -7(DI)(R8*8), X2, X23                      // 62a16c0816bcc7f9ffffff
	VMOVHPS X20, 99(R15)(R15*4)                        // 62817c0817a4bf63000000
	VMOVHPS X20, 15(DX)                                // 62e17c0817a20f000000
	VMOVLHPS X0, X25, X5                               // 62f1340016e8
	VMOVNTDQ Y26, -7(CX)                               // 62617d28e791f9ffffff
	VMOVNTDQ Y26, 15(DX)(BX*4)                         // 62617d28e7949a0f000000
	VMOVNTDQ Z18, -15(R14)(R15*1)                      // 62817d48e7943ef1ffffff
	VMOVNTDQ Z24, -15(R14)(R15*1)                      // 62017d48e7843ef1ffffff
	VMOVNTDQ Z18, -15(BX)                              // 62e17d48e793f1ffffff
	VMOVNTDQ Z24, -15(BX)                              // 62617d48e783f1ffffff
	VMOVNTDQA 7(AX)(CX*4), Z2                          // 62f27d482a948807000000
	VMOVNTDQA 7(AX)(CX*1), Z2                          // 62f27d482a940807000000
	VMOVNTDQA 7(AX)(CX*4), Z21                         // 62e27d482aac8807000000
	VMOVNTDQA 7(AX)(CX*1), Z21                         // 62e27d482aac0807000000
	VMOVNTPD Y26, (AX)                                 // 6261fd282b10
	VMOVNTPD Y26, 7(SI)                                // 6261fd282b9607000000
	VMOVNTPD Z7, (SI)                                  // 62f1fd482b3e
	VMOVNTPD Z13, (SI)                                 // 6271fd482b2e
	VMOVNTPD Z7, 7(SI)(DI*2)                           // 62f1fd482bbc7e07000000
	VMOVNTPD Z13, 7(SI)(DI*2)                          // 6271fd482bac7e07000000
	VMOVNTPS X31, 15(R8)(R14*8)                        // 62017c082bbcf00f000000
	VMOVNTPS X31, -15(R14)(R15*2)                      // 62017c082bbc7ef1ffffff
	VMOVNTPS Z6, 17(SP)(BP*8)                          // 62f17c482bb4ec11000000
	VMOVNTPS Z16, 17(SP)(BP*8)                         // 62e17c482b84ec11000000
	VMOVNTPS Z6, 17(SP)(BP*4)                          // 62f17c482bb4ac11000000
	VMOVNTPS Z16, 17(SP)(BP*4)                         // 62e17c482b84ac11000000
	VMOVSD -7(CX)(DX*1), K3, X11                       // 6271ff0b109c11f9ffffff or 6271ff2b109c11f9ffffff or 6271ff4b109c11f9ffffff
	VMOVSD -15(R14)(R15*4), K3, X11                    // 6211ff0b109cbef1ffffff or 6211ff2b109cbef1ffffff or 6211ff4b109cbef1ffffff
	VMOVSD X14, X5, K3, X22                            // 6231d70b11f6 or 6231d72b11f6 or 6231d74b11f6
	VMOVSD X0, K2, 15(DX)(BX*1)                        // 62f1ff0a11841a0f000000 or 62f1ff2a11841a0f000000 or 62f1ff4a11841a0f000000
	VMOVSD X0, K2, -7(CX)(DX*2)                        // 62f1ff0a118451f9ffffff or 62f1ff2a118451f9ffffff or 62f1ff4a118451f9ffffff
	VMOVSD X15, X7, K1, X17                            // 6231c70911f9 or 6231c72911f9 or 6231c74911f9
	VMOVSHDUP X0, K2, X11                              // 62717e0a16d8
	VMOVSHDUP -15(R14)(R15*1), K2, X11                 // 62117e0a169c3ef1ffffff
	VMOVSHDUP -15(BX), K2, X11                         // 62717e0a169bf1ffffff
	VMOVSHDUP Y18, K1, Y14                             // 62317e2916f2
	VMOVSHDUP 15(R8)(R14*4), K1, Y14                   // 62117e2916b4b00f000000
	VMOVSHDUP -7(CX)(DX*4), K1, Y14                    // 62717e2916b491f9ffffff
	VMOVSHDUP Z1, K7, Z6                               // 62f17e4f16f1
	VMOVSHDUP Z15, K7, Z6                              // 62d17e4f16f7
	VMOVSHDUP 7(SI)(DI*4), K7, Z6                      // 62f17e4f16b4be07000000
	VMOVSHDUP -7(DI)(R8*2), K7, Z6                     // 62b17e4f16b447f9ffffff
	VMOVSHDUP Z1, K7, Z22                              // 62e17e4f16f1
	VMOVSHDUP Z15, K7, Z22                             // 62c17e4f16f7
	VMOVSHDUP 7(SI)(DI*4), K7, Z22                     // 62e17e4f16b4be07000000
	VMOVSHDUP -7(DI)(R8*2), K7, Z22                    // 62a17e4f16b447f9ffffff
	VMOVSLDUP X8, K1, X18                              // 62c17e0912d0
	VMOVSLDUP 7(AX)(CX*4), K1, X18                     // 62e17e0912948807000000
	VMOVSLDUP 7(AX)(CX*1), K1, X18                     // 62e17e0912940807000000
	VMOVSLDUP Y18, K1, Y31                             // 62217e2912fa
	VMOVSLDUP (R8), K1, Y31                            // 62417e291238
	VMOVSLDUP 15(DX)(BX*2), K1, Y31                    // 62617e2912bc5a0f000000
	VMOVSLDUP Z18, K1, Z13                             // 62317e4912ea
	VMOVSLDUP Z8, K1, Z13                              // 62517e4912e8
	VMOVSLDUP 17(SP), K1, Z13                          // 62717e4912ac2411000000
	VMOVSLDUP -17(BP)(SI*4), K1, Z13                   // 62717e4912acb5efffffff
	VMOVSS 17(SP)(BP*1), K7, X27                       // 62617e0f109c2c11000000 or 62617e2f109c2c11000000 or 62617e4f109c2c11000000
	VMOVSS -7(CX)(DX*8), K7, X27                       // 62617e0f109cd1f9ffffff or 62617e2f109cd1f9ffffff or 62617e4f109cd1f9ffffff
	VMOVSS X18, X3, K2, X25                            // 6281660a11d1 or 6281662a11d1 or 6281664a11d1
	VMOVSS X15, K4, -17(BP)(SI*2)                      // 62717e0c11bc75efffffff or 62717e2c11bc75efffffff or 62717e4c11bc75efffffff
	VMOVSS X15, K4, 7(AX)(CX*2)                        // 62717e0c11bc4807000000 or 62717e2c11bc4807000000 or 62717e4c11bc4807000000
	VMOVSS X7, X15, K1, X28                            // 6291060911fc or 6291062911fc or 6291064911fc
	VMOVUPD X8, K3, X13                                // 6251fd0b11c5
	VMOVUPD X8, K3, (SI)                               // 6271fd0b1106
	VMOVUPD X8, K3, 7(SI)(DI*2)                        // 6271fd0b11847e07000000
	VMOVUPD X7, K4, X24                                // 6291fd0c11f8
	VMOVUPD 17(SP)(BP*8), K4, X24                      // 6261fd0c1084ec11000000
	VMOVUPD 17(SP)(BP*4), K4, X24                      // 6261fd0c1084ac11000000
	VMOVUPD Y24, K5, Y3                                // 6261fd2d11c3
	VMOVUPD Y24, K5, 17(SP)(BP*1)                      // 6261fd2d11842c11000000
	VMOVUPD Y24, K5, -7(CX)(DX*8)                      // 6261fd2d1184d1f9ffffff
	VMOVUPD Y7, K7, Y2                                 // 62f1fd2f11fa
	VMOVUPD -17(BP)(SI*2), K7, Y2                      // 62f1fd2f109475efffffff
	VMOVUPD 7(AX)(CX*2), K7, Y2                        // 62f1fd2f10944807000000
	VMOVUPD Z2, K7, Z22                                // 62b1fd4f11d6
	VMOVUPD Z31, K7, Z22                               // 6221fd4f11fe
	VMOVUPD Z2, K7, Z7                                 // 62f1fd4f11d7
	VMOVUPD Z31, K7, Z7                                // 6261fd4f11ff
	VMOVUPD Z2, K7, 7(AX)                              // 62f1fd4f119007000000
	VMOVUPD Z31, K7, 7(AX)                             // 6261fd4f11b807000000
	VMOVUPD Z2, K7, (DI)                               // 62f1fd4f1117
	VMOVUPD Z31, K7, (DI)                              // 6261fd4f113f
	VMOVUPD Z1, K6, Z20                                // 62b1fd4e11cc
	VMOVUPD Z3, K6, Z20                                // 62b1fd4e11dc
	VMOVUPD 99(R15)(R15*1), K6, Z20                    // 6281fd4e10a43f63000000
	VMOVUPD (DX), K6, Z20                              // 62e1fd4e1022
	VMOVUPD Z1, K6, Z9                                 // 62d1fd4e11c9
	VMOVUPD Z3, K6, Z9                                 // 62d1fd4e11d9
	VMOVUPD 99(R15)(R15*1), K6, Z9                     // 6211fd4e108c3f63000000
	VMOVUPD (DX), K6, Z9                               // 6271fd4e100a
	VMOVUPS X22, K3, X0                                // 62e17c0b11f0
	VMOVUPS X22, K3, 7(SI)(DI*4)                       // 62e17c0b11b4be07000000
	VMOVUPS X22, K3, -7(DI)(R8*2)                      // 62a17c0b11b447f9ffffff
	VMOVUPS X11, K7, X1                                // 62717c0f11d9
	VMOVUPS 17(SP), K7, X1                             // 62f17c0f108c2411000000
	VMOVUPS -17(BP)(SI*4), K7, X1                      // 62f17c0f108cb5efffffff
	VMOVUPS Y14, K4, Y21                               // 62317c2c11f5
	VMOVUPS Y14, K4, 15(R8)(R14*1)                     // 62117c2c11b4300f000000
	VMOVUPS Y14, K4, 15(R8)(R14*2)                     // 62117c2c11b4700f000000
	VMOVUPS Y20, K4, Y8                                // 62c17c2c11e0
	VMOVUPS (R14), K4, Y8                              // 62517c2c1006
	VMOVUPS -7(DI)(R8*8), K4, Y8                       // 62317c2c1084c7f9ffffff
	VMOVUPS Z28, K7, Z12                               // 62417c4f11e4
	VMOVUPS Z13, K7, Z12                               // 62517c4f11ec
	VMOVUPS Z28, K7, Z16                               // 62217c4f11e0
	VMOVUPS Z13, K7, Z16                               // 62317c4f11e8
	VMOVUPS Z28, K7, -17(BP)(SI*8)                     // 62617c4f11a4f5efffffff
	VMOVUPS Z13, K7, -17(BP)(SI*8)                     // 62717c4f11acf5efffffff
	VMOVUPS Z28, K7, (R15)                             // 62417c4f1127
	VMOVUPS Z13, K7, (R15)                             // 62517c4f112f
	VMOVUPS Z3, K2, Z14                                // 62d17c4a11de
	VMOVUPS Z12, K2, Z14                               // 62517c4a11e6
	VMOVUPS 7(SI)(DI*8), K2, Z14                       // 62717c4a10b4fe07000000
	VMOVUPS -15(R14), K2, Z14                          // 62517c4a10b6f1ffffff
	VMOVUPS Z3, K2, Z28                                // 62917c4a11dc
	VMOVUPS Z12, K2, Z28                               // 62117c4a11e4
	VMOVUPS 7(SI)(DI*8), K2, Z28                       // 62617c4a10a4fe07000000
	VMOVUPS -15(R14), K2, Z28                          // 62417c4a10a6f1ffffff
	VMULPD X8, X7, K5, X6                              // 62d1c50d59f0
	VMULPD 7(AX), X7, K5, X6                           // 62f1c50d59b007000000
	VMULPD (DI), X7, K5, X6                            // 62f1c50d5937
	VMULPD Y1, Y24, K3, Y11                            // 6271bd2359d9
	VMULPD 99(R15)(R15*4), Y24, K3, Y11                // 6211bd23599cbf63000000
	VMULPD 15(DX), Y24, K3, Y11                        // 6271bd23599a0f000000
	VMULPD Z5, Z19, K4, Z15                            // 6271e54459fd
	VMULPD Z1, Z19, K4, Z15                            // 6271e54459f9
	VMULPD Z5, Z15, K4, Z15                            // 6271854c59fd
	VMULPD Z1, Z15, K4, Z15                            // 6271854c59f9
	VMULPD Z5, Z19, K4, Z30                            // 6261e54459f5
	VMULPD Z1, Z19, K4, Z30                            // 6261e54459f1
	VMULPD Z5, Z15, K4, Z30                            // 6261854c59f5
	VMULPD Z1, Z15, K4, Z30                            // 6261854c59f1
	VMULPD Z21, Z14, K2, Z3                            // 62b18d4a59dd
	VMULPD Z8, Z14, K2, Z3                             // 62d18d4a59d8
	VMULPD 7(SI)(DI*1), Z14, K2, Z3                    // 62f18d4a599c3e07000000
	VMULPD 15(DX)(BX*8), Z14, K2, Z3                   // 62f18d4a599cda0f000000
	VMULPD Z21, Z15, K2, Z3                            // 62b1854a59dd
	VMULPD Z8, Z15, K2, Z3                             // 62d1854a59d8
	VMULPD 7(SI)(DI*1), Z15, K2, Z3                    // 62f1854a599c3e07000000
	VMULPD 15(DX)(BX*8), Z15, K2, Z3                   // 62f1854a599cda0f000000
	VMULPD Z21, Z14, K2, Z5                            // 62b18d4a59ed
	VMULPD Z8, Z14, K2, Z5                             // 62d18d4a59e8
	VMULPD 7(SI)(DI*1), Z14, K2, Z5                    // 62f18d4a59ac3e07000000
	VMULPD 15(DX)(BX*8), Z14, K2, Z5                   // 62f18d4a59acda0f000000
	VMULPD Z21, Z15, K2, Z5                            // 62b1854a59ed
	VMULPD Z8, Z15, K2, Z5                             // 62d1854a59e8
	VMULPD 7(SI)(DI*1), Z15, K2, Z5                    // 62f1854a59ac3e07000000
	VMULPD 15(DX)(BX*8), Z15, K2, Z5                   // 62f1854a59acda0f000000
	VMULPS X28, X3, K2, X31                            // 6201640a59fc
	VMULPS 99(R15)(R15*1), X3, K2, X31                 // 6201640a59bc3f63000000
	VMULPS (DX), X3, K2, X31                           // 6261640a593a
	VMULPS Y20, Y18, K3, Y5                            // 62b16c2359ec
	VMULPS (CX), Y18, K3, Y5                           // 62f16c235929
	VMULPS 99(R15), Y18, K3, Y5                        // 62d16c2359af63000000
	VMULPS Z23, Z20, K3, Z16                           // 62a15c4359c7
	VMULPS Z19, Z20, K3, Z16                           // 62a15c4359c3
	VMULPS Z23, Z0, K3, Z16                            // 62a17c4b59c7
	VMULPS Z19, Z0, K3, Z16                            // 62a17c4b59c3
	VMULPS Z23, Z20, K3, Z9                            // 62315c4359cf
	VMULPS Z19, Z20, K3, Z9                            // 62315c4359cb
	VMULPS Z23, Z0, K3, Z9                             // 62317c4b59cf
	VMULPS Z19, Z0, K3, Z9                             // 62317c4b59cb
	VMULPS Z24, Z0, K3, Z0                             // 62917c4b59c0
	VMULPS Z12, Z0, K3, Z0                             // 62d17c4b59c4
	VMULPS -7(DI)(R8*1), Z0, K3, Z0                    // 62b17c4b598407f9ffffff
	VMULPS (SP), Z0, K3, Z0                            // 62f17c4b590424
	VMULPS Z24, Z25, K3, Z0                            // 6291344359c0
	VMULPS Z12, Z25, K3, Z0                            // 62d1344359c4
	VMULPS -7(DI)(R8*1), Z25, K3, Z0                   // 62b13443598407f9ffffff
	VMULPS (SP), Z25, K3, Z0                           // 62f13443590424
	VMULPS Z24, Z0, K3, Z11                            // 62117c4b59d8
	VMULPS Z12, Z0, K3, Z11                            // 62517c4b59dc
	VMULPS -7(DI)(R8*1), Z0, K3, Z11                   // 62317c4b599c07f9ffffff
	VMULPS (SP), Z0, K3, Z11                           // 62717c4b591c24
	VMULPS Z24, Z25, K3, Z11                           // 6211344359d8
	VMULPS Z12, Z25, K3, Z11                           // 6251344359dc
	VMULPS -7(DI)(R8*1), Z25, K3, Z11                  // 62313443599c07f9ffffff
	VMULPS (SP), Z25, K3, Z11                          // 62713443591c24
	VMULSD X7, X24, K2, X20                            // 62e1bf0259e7
	VMULSD X12, X16, K1, X20                           // 62c1ff0159e4 or 62c1ff2159e4 or 62c1ff4159e4
	VMULSD -17(BP), X16, K1, X20                       // 62e1ff0159a5efffffff or 62e1ff2159a5efffffff or 62e1ff4159a5efffffff
	VMULSD -15(R14)(R15*8), X16, K1, X20               // 6281ff0159a4fef1ffffff or 6281ff2159a4fef1ffffff or 6281ff4159a4fef1ffffff
	VMULSS X28, X17, K2, X6                            // 6291760259f4
	VMULSS X8, X1, K1, X6                              // 62d1760959f0 or 62d1762959f0 or 62d1764959f0
	VMULSS 15(R8)(R14*1), X1, K1, X6                   // 6291760959b4300f000000 or 6291762959b4300f000000 or 6291764959b4300f000000
	VMULSS 15(R8)(R14*2), X1, K1, X6                   // 6291760959b4700f000000 or 6291762959b4700f000000 or 6291764959b4700f000000
	VPABSD X16, K7, X12                                // 62327d0f1ee0
	VPABSD 99(R15)(R15*8), K7, X12                     // 62127d0f1ea4ff63000000
	VPABSD 7(AX)(CX*8), K7, X12                        // 62727d0f1ea4c807000000
	VPABSD Y16, K7, Y17                                // 62a27d2f1ec8
	VPABSD -17(BP), K7, Y17                            // 62e27d2f1e8defffffff
	VPABSD -15(R14)(R15*8), K7, Y17                    // 62827d2f1e8cfef1ffffff
	VPABSD Z20, K6, Z1                                 // 62b27d4e1ecc
	VPABSD Z9, K6, Z1                                  // 62d27d4e1ec9
	VPABSD (BX), K6, Z1                                // 62f27d4e1e0b
	VPABSD -17(BP)(SI*1), K6, Z1                       // 62f27d4e1e8c35efffffff
	VPABSD Z20, K6, Z9                                 // 62327d4e1ecc
	VPABSD Z9, K6, Z9                                  // 62527d4e1ec9
	VPABSD (BX), K6, Z9                                // 62727d4e1e0b
	VPABSD -17(BP)(SI*1), K6, Z9                       // 62727d4e1e8c35efffffff
	VPABSQ X8, K3, X28                                 // 6242fd0b1fe0
	VPABSQ (AX), K3, X28                               // 6262fd0b1f20
	VPABSQ 7(SI), K3, X28                              // 6262fd0b1fa607000000
	VPABSQ Y6, K7, Y12                                 // 6272fd2f1fe6
	VPABSQ 17(SP)(BP*2), K7, Y12                       // 6272fd2f1fa46c11000000
	VPABSQ -7(DI)(R8*4), K7, Y12                       // 6232fd2f1fa487f9ffffff
	VPABSQ Z26, K4, Z30                                // 6202fd4c1ff2
	VPABSQ Z22, K4, Z30                                // 6222fd4c1ff6
	VPABSQ 15(R8)(R14*4), K4, Z30                      // 6202fd4c1fb4b00f000000
	VPABSQ -7(CX)(DX*4), K4, Z30                       // 6262fd4c1fb491f9ffffff
	VPABSQ Z26, K4, Z5                                 // 6292fd4c1fea
	VPABSQ Z22, K4, Z5                                 // 62b2fd4c1fee
	VPABSQ 15(R8)(R14*4), K4, Z5                       // 6292fd4c1facb00f000000
	VPABSQ -7(CX)(DX*4), K4, Z5                        // 62f2fd4c1fac91f9ffffff
	VPADDD X27, X2, K1, X2                             // 62916d09fed3
	VPADDD (R14), X2, K1, X2                           // 62d16d09fe16
	VPADDD -7(DI)(R8*8), X2, K1, X2                    // 62b16d09fe94c7f9ffffff
	VPADDD Y1, Y6, K7, Y1                              // 62f14d2ffec9
	VPADDD 7(SI)(DI*4), Y6, K7, Y1                     // 62f14d2ffe8cbe07000000
	VPADDD -7(DI)(R8*2), Y6, K7, Y1                    // 62b14d2ffe8c47f9ffffff
	VPADDD Z13, Z11, K2, Z14                           // 6251254afef5
	VPADDD Z14, Z11, K2, Z14                           // 6251254afef6
	VPADDD (CX), Z11, K2, Z14                          // 6271254afe31
	VPADDD 99(R15), Z11, K2, Z14                       // 6251254afeb763000000
	VPADDD Z13, Z5, K2, Z14                            // 6251554afef5
	VPADDD Z14, Z5, K2, Z14                            // 6251554afef6
	VPADDD (CX), Z5, K2, Z14                           // 6271554afe31
	VPADDD 99(R15), Z5, K2, Z14                        // 6251554afeb763000000
	VPADDD Z13, Z11, K2, Z27                           // 6241254afedd
	VPADDD Z14, Z11, K2, Z27                           // 6241254afede
	VPADDD (CX), Z11, K2, Z27                          // 6261254afe19
	VPADDD 99(R15), Z11, K2, Z27                       // 6241254afe9f63000000
	VPADDD Z13, Z5, K2, Z27                            // 6241554afedd
	VPADDD Z14, Z5, K2, Z27                            // 6241554afede
	VPADDD (CX), Z5, K2, Z27                           // 6261554afe19
	VPADDD 99(R15), Z5, K2, Z27                        // 6241554afe9f63000000
	VPADDQ X30, X22, K4, X26                           // 6201cd04d4d6
	VPADDQ 99(R15)(R15*4), X22, K4, X26                // 6201cd04d494bf63000000
	VPADDQ 15(DX), X22, K4, X26                        // 6261cd04d4920f000000
	VPADDQ Y19, Y0, K1, Y9                             // 6231fd29d4cb
	VPADDQ 17(SP), Y0, K1, Y9                          // 6271fd29d48c2411000000
	VPADDQ -17(BP)(SI*4), Y0, K1, Y9                   // 6271fd29d48cb5efffffff
	VPADDQ Z6, Z2, K3, Z5                              // 62f1ed4bd4ee
	VPADDQ Z14, Z2, K3, Z5                             // 62d1ed4bd4ee
	VPADDQ 99(R15)(R15*2), Z2, K3, Z5                  // 6291ed4bd4ac7f63000000
	VPADDQ -7(DI), Z2, K3, Z5                          // 62f1ed4bd4aff9ffffff
	VPADDQ Z6, Z2, K3, Z23                             // 62e1ed4bd4fe
	VPADDQ Z14, Z2, K3, Z23                            // 62c1ed4bd4fe
	VPADDQ 99(R15)(R15*2), Z2, K3, Z23                 // 6281ed4bd4bc7f63000000
	VPADDQ -7(DI), Z2, K3, Z23                         // 62e1ed4bd4bff9ffffff
	VPANDD X1, X8, K3, X7                              // 62f13d0bdbf9
	VPANDD 15(R8), X8, K3, X7                          // 62d13d0bdbb80f000000
	VPANDD (BP), X8, K3, X7                            // 62f13d0bdb7d00
	VPANDD Y13, Y2, K2, Y14                            // 62516d2adbf5
	VPANDD -7(CX), Y2, K2, Y14                         // 62716d2adbb1f9ffffff
	VPANDD 15(DX)(BX*4), Y2, K2, Y14                   // 62716d2adbb49a0f000000
	VPANDD Z6, Z9, K1, Z12                             // 62713549dbe6
	VPANDD Z25, Z9, K1, Z12                            // 62113549dbe1
	VPANDD -15(R14)(R15*1), Z9, K1, Z12                // 62113549dba43ef1ffffff
	VPANDD -15(BX), Z9, K1, Z12                        // 62713549dba3f1ffffff
	VPANDD Z6, Z12, K1, Z12                            // 62711d49dbe6
	VPANDD Z25, Z12, K1, Z12                           // 62111d49dbe1
	VPANDD -15(R14)(R15*1), Z12, K1, Z12               // 62111d49dba43ef1ffffff
	VPANDD -15(BX), Z12, K1, Z12                       // 62711d49dba3f1ffffff
	VPANDD Z6, Z9, K1, Z17                             // 62e13549dbce
	VPANDD Z25, Z9, K1, Z17                            // 62813549dbc9
	VPANDD -15(R14)(R15*1), Z9, K1, Z17                // 62813549db8c3ef1ffffff
	VPANDD -15(BX), Z9, K1, Z17                        // 62e13549db8bf1ffffff
	VPANDD Z6, Z12, K1, Z17                            // 62e11d49dbce
	VPANDD Z25, Z12, K1, Z17                           // 62811d49dbc9
	VPANDD -15(R14)(R15*1), Z12, K1, Z17               // 62811d49db8c3ef1ffffff
	VPANDD -15(BX), Z12, K1, Z17                       // 62e11d49db8bf1ffffff
	VPANDND X0, X15, K2, X0                            // 62f1050adfc0
	VPANDND 15(R8)(R14*8), X15, K2, X0                 // 6291050adf84f00f000000
	VPANDND -15(R14)(R15*2), X15, K2, X0               // 6291050adf847ef1ffffff
	VPANDND Y22, Y15, K1, Y27                          // 62210529dfde
	VPANDND 99(R15)(R15*8), Y15, K1, Y27               // 62010529df9cff63000000
	VPANDND 7(AX)(CX*8), Y15, K1, Y27                  // 62610529df9cc807000000
	VPANDND Z3, Z8, K7, Z3                             // 62f13d4fdfdb
	VPANDND Z27, Z8, K7, Z3                            // 62913d4fdfdb
	VPANDND 7(AX)(CX*4), Z8, K7, Z3                    // 62f13d4fdf9c8807000000
	VPANDND 7(AX)(CX*1), Z8, K7, Z3                    // 62f13d4fdf9c0807000000
	VPANDND Z3, Z2, K7, Z3                             // 62f16d4fdfdb
	VPANDND Z27, Z2, K7, Z3                            // 62916d4fdfdb
	VPANDND 7(AX)(CX*4), Z2, K7, Z3                    // 62f16d4fdf9c8807000000
	VPANDND 7(AX)(CX*1), Z2, K7, Z3                    // 62f16d4fdf9c0807000000
	VPANDND Z3, Z8, K7, Z21                            // 62e13d4fdfeb
	VPANDND Z27, Z8, K7, Z21                           // 62813d4fdfeb
	VPANDND 7(AX)(CX*4), Z8, K7, Z21                   // 62e13d4fdfac8807000000
	VPANDND 7(AX)(CX*1), Z8, K7, Z21                   // 62e13d4fdfac0807000000
	VPANDND Z3, Z2, K7, Z21                            // 62e16d4fdfeb
	VPANDND Z27, Z2, K7, Z21                           // 62816d4fdfeb
	VPANDND 7(AX)(CX*4), Z2, K7, Z21                   // 62e16d4fdfac8807000000
	VPANDND 7(AX)(CX*1), Z2, K7, Z21                   // 62e16d4fdfac0807000000
	VPANDNQ X0, X21, K1, X16                           // 62e1d501dfc0
	VPANDNQ -15(R14)(R15*1), X21, K1, X16              // 6281d501df843ef1ffffff
	VPANDNQ -15(BX), X21, K1, X16                      // 62e1d501df83f1ffffff
	VPANDNQ Y24, Y18, K1, Y20                          // 6281ed21dfe0
	VPANDNQ (AX), Y18, K1, Y20                         // 62e1ed21df20
	VPANDNQ 7(SI), Y18, K1, Y20                        // 62e1ed21dfa607000000
	VPANDNQ Z20, Z0, K1, Z7                            // 62b1fd49dffc
	VPANDNQ Z28, Z0, K1, Z7                            // 6291fd49dffc
	VPANDNQ (SI), Z0, K1, Z7                           // 62f1fd49df3e
	VPANDNQ 7(SI)(DI*2), Z0, K1, Z7                    // 62f1fd49dfbc7e07000000
	VPANDNQ Z20, Z6, K1, Z7                            // 62b1cd49dffc
	VPANDNQ Z28, Z6, K1, Z7                            // 6291cd49dffc
	VPANDNQ (SI), Z6, K1, Z7                           // 62f1cd49df3e
	VPANDNQ 7(SI)(DI*2), Z6, K1, Z7                    // 62f1cd49dfbc7e07000000
	VPANDNQ Z20, Z0, K1, Z9                            // 6231fd49dfcc
	VPANDNQ Z28, Z0, K1, Z9                            // 6211fd49dfcc
	VPANDNQ (SI), Z0, K1, Z9                           // 6271fd49df0e
	VPANDNQ 7(SI)(DI*2), Z0, K1, Z9                    // 6271fd49df8c7e07000000
	VPANDNQ Z20, Z6, K1, Z9                            // 6231cd49dfcc
	VPANDNQ Z28, Z6, K1, Z9                            // 6211cd49dfcc
	VPANDNQ (SI), Z6, K1, Z9                           // 6271cd49df0e
	VPANDNQ 7(SI)(DI*2), Z6, K1, Z9                    // 6271cd49df8c7e07000000
	VPANDQ X7, X22, K7, X28                            // 6261cd07dbe7
	VPANDQ 7(AX)(CX*4), X22, K7, X28                   // 6261cd07dba48807000000
	VPANDQ 7(AX)(CX*1), X22, K7, X28                   // 6261cd07dba40807000000
	VPANDQ Y19, Y3, K2, Y9                             // 6231e52adbcb
	VPANDQ (BX), Y3, K2, Y9                            // 6271e52adb0b
	VPANDQ -17(BP)(SI*1), Y3, K2, Y9                   // 6271e52adb8c35efffffff
	VPANDQ Z12, Z9, K4, Z3                             // 62d1b54cdbdc
	VPANDQ Z22, Z9, K4, Z3                             // 62b1b54cdbde
	VPANDQ 17(SP)(BP*8), Z9, K4, Z3                    // 62f1b54cdb9cec11000000
	VPANDQ 17(SP)(BP*4), Z9, K4, Z3                    // 62f1b54cdb9cac11000000
	VPANDQ Z12, Z19, K4, Z3                            // 62d1e544dbdc
	VPANDQ Z22, Z19, K4, Z3                            // 62b1e544dbde
	VPANDQ 17(SP)(BP*8), Z19, K4, Z3                   // 62f1e544db9cec11000000
	VPANDQ 17(SP)(BP*4), Z19, K4, Z3                   // 62f1e544db9cac11000000
	VPANDQ Z12, Z9, K4, Z30                            // 6241b54cdbf4
	VPANDQ Z22, Z9, K4, Z30                            // 6221b54cdbf6
	VPANDQ 17(SP)(BP*8), Z9, K4, Z30                   // 6261b54cdbb4ec11000000
	VPANDQ 17(SP)(BP*4), Z9, K4, Z30                   // 6261b54cdbb4ac11000000
	VPANDQ Z12, Z19, K4, Z30                           // 6241e544dbf4
	VPANDQ Z22, Z19, K4, Z30                           // 6221e544dbf6
	VPANDQ 17(SP)(BP*8), Z19, K4, Z30                  // 6261e544dbb4ec11000000
	VPANDQ 17(SP)(BP*4), Z19, K4, Z30                  // 6261e544dbb4ac11000000
	VPBLENDMD X14, X12, K4, X0                         // 62d21d0c64c6
	VPBLENDMD 17(SP), X12, K4, X0                      // 62f21d0c64842411000000
	VPBLENDMD -17(BP)(SI*4), X12, K4, X0               // 62f21d0c6484b5efffffff
	VPBLENDMD Y6, Y31, K4, Y6                          // 62f2052464f6
	VPBLENDMD -17(BP)(SI*2), Y31, K4, Y6               // 62f2052464b475efffffff
	VPBLENDMD 7(AX)(CX*2), Y31, K4, Y6                 // 62f2052464b44807000000
	VPBLENDMD Z20, Z2, K7, Z22                         // 62a26d4f64f4
	VPBLENDMD Z9, Z2, K7, Z22                          // 62c26d4f64f1
	VPBLENDMD 99(R15)(R15*1), Z2, K7, Z22              // 62826d4f64b43f63000000
	VPBLENDMD (DX), Z2, K7, Z22                        // 62e26d4f6432
	VPBLENDMD Z20, Z31, K7, Z22                        // 62a2054764f4
	VPBLENDMD Z9, Z31, K7, Z22                         // 62c2054764f1
	VPBLENDMD 99(R15)(R15*1), Z31, K7, Z22             // 6282054764b43f63000000
	VPBLENDMD (DX), Z31, K7, Z22                       // 62e205476432
	VPBLENDMD Z20, Z2, K7, Z7                          // 62b26d4f64fc
	VPBLENDMD Z9, Z2, K7, Z7                           // 62d26d4f64f9
	VPBLENDMD 99(R15)(R15*1), Z2, K7, Z7               // 62926d4f64bc3f63000000
	VPBLENDMD (DX), Z2, K7, Z7                         // 62f26d4f643a
	VPBLENDMD Z20, Z31, K7, Z7                         // 62b2054764fc
	VPBLENDMD Z9, Z31, K7, Z7                          // 62d2054764f9
	VPBLENDMD 99(R15)(R15*1), Z31, K7, Z7              // 6292054764bc3f63000000
	VPBLENDMD (DX), Z31, K7, Z7                        // 62f20547643a
	VPBLENDMQ X15, X17, K2, X5                         // 62d2f50264ef
	VPBLENDMQ 7(AX), X17, K2, X5                       // 62f2f50264a807000000
	VPBLENDMQ (DI), X17, K2, X5                        // 62f2f502642f
	VPBLENDMQ Y7, Y19, K5, Y11                         // 6272e52564df
	VPBLENDMQ 15(R8)(R14*1), Y19, K5, Y11              // 6212e525649c300f000000
	VPBLENDMQ 15(R8)(R14*2), Y19, K5, Y11              // 6212e525649c700f000000
	VPBLENDMQ Z28, Z12, K3, Z1                         // 62929d4b64cc
	VPBLENDMQ Z13, Z12, K3, Z1                         // 62d29d4b64cd
	VPBLENDMQ -17(BP)(SI*8), Z12, K3, Z1               // 62f29d4b648cf5efffffff
	VPBLENDMQ (R15), Z12, K3, Z1                       // 62d29d4b640f
	VPBLENDMQ Z28, Z16, K3, Z1                         // 6292fd4364cc
	VPBLENDMQ Z13, Z16, K3, Z1                         // 62d2fd4364cd
	VPBLENDMQ -17(BP)(SI*8), Z16, K3, Z1               // 62f2fd43648cf5efffffff
	VPBLENDMQ (R15), Z16, K3, Z1                       // 62d2fd43640f
	VPBLENDMQ Z28, Z12, K3, Z3                         // 62929d4b64dc
	VPBLENDMQ Z13, Z12, K3, Z3                         // 62d29d4b64dd
	VPBLENDMQ -17(BP)(SI*8), Z12, K3, Z3               // 62f29d4b649cf5efffffff
	VPBLENDMQ (R15), Z12, K3, Z3                       // 62d29d4b641f
	VPBLENDMQ Z28, Z16, K3, Z3                         // 6292fd4364dc
	VPBLENDMQ Z13, Z16, K3, Z3                         // 62d2fd4364dd
	VPBLENDMQ -17(BP)(SI*8), Z16, K3, Z3               // 62f2fd43649cf5efffffff
	VPBLENDMQ (R15), Z16, K3, Z3                       // 62d2fd43641f
	VPBROADCASTD SP, K1, X15                           // 62727d097cfc
	VPBROADCASTD R14, K1, X15                          // 62527d097cfe
	VPBROADCASTD AX, K7, Y12                           // 62727d2f7ce0
	VPBROADCASTD R9, K7, Y12                           // 62527d2f7ce1
	VPBROADCASTD CX, K1, Z3                            // 62f27d497cd9
	VPBROADCASTD SP, K1, Z3                            // 62f27d497cdc
	VPBROADCASTD CX, K1, Z5                            // 62f27d497ce9
	VPBROADCASTD SP, K1, Z5                            // 62f27d497cec
	VPBROADCASTD X18, K1, X26                          // 62227d0958d2
	VPBROADCASTD (R14), K1, X26                        // 62427d095816
	VPBROADCASTD -7(DI)(R8*8), K1, X26                 // 62227d095894c7f9ffffff
	VPBROADCASTD X21, K1, Y3                           // 62b27d2958dd
	VPBROADCASTD 99(R15)(R15*4), K1, Y3                // 62927d29589cbf63000000
	VPBROADCASTD 15(DX), K1, Y3                        // 62f27d29589a0f000000
	VPBROADCASTD X1, K7, Z14                           // 62727d4f58f1
	VPBROADCASTD (CX), K7, Z14                         // 62727d4f5831
	VPBROADCASTD 99(R15), K7, Z14                      // 62527d4f58b763000000
	VPBROADCASTD X1, K7, Z15                           // 62727d4f58f9
	VPBROADCASTD (CX), K7, Z15                         // 62727d4f5839
	VPBROADCASTD 99(R15), K7, Z15                      // 62527d4f58bf63000000
	VPBROADCASTQ R9, K2, X3                            // 62d2fd0a7cd9
	VPBROADCASTQ R13, K2, X3                           // 62d2fd0a7cdd
	VPBROADCASTQ DX, K4, Y7                            // 62f2fd2c7cfa
	VPBROADCASTQ BP, K4, Y7                            // 62f2fd2c7cfd
	VPBROADCASTQ R10, K1, Z20                          // 62c2fd497ce2
	VPBROADCASTQ CX, K1, Z20                           // 62e2fd497ce1
	VPBROADCASTQ R10, K1, Z0                           // 62d2fd497cc2
	VPBROADCASTQ CX, K1, Z0                            // 62f2fd497cc1
	VPBROADCASTQ X0, K3, X7                            // 62f2fd0b59f8
	VPBROADCASTQ 17(SP)(BP*2), K3, X7                  // 62f2fd0b59bc6c11000000
	VPBROADCASTQ -7(DI)(R8*4), K3, X7                  // 62b2fd0b59bc87f9ffffff
	VPBROADCASTQ X0, K4, Y0                            // 62f2fd2c59c0
	VPBROADCASTQ 15(R8), K4, Y0                        // 62d2fd2c59800f000000
	VPBROADCASTQ (BP), K4, Y0                          // 62f2fd2c594500
	VPBROADCASTQ X24, K5, Z23                          // 6282fd4d59f8
	VPBROADCASTQ 15(R8)(R14*8), K5, Z23                // 6282fd4d59bcf00f000000
	VPBROADCASTQ -15(R14)(R15*2), K5, Z23              // 6282fd4d59bc7ef1ffffff
	VPBROADCASTQ X24, K5, Z19                          // 6282fd4d59d8
	VPBROADCASTQ 15(R8)(R14*8), K5, Z19                // 6282fd4d599cf00f000000
	VPBROADCASTQ -15(R14)(R15*2), K5, Z19              // 6282fd4d599c7ef1ffffff
	VPCMPD $64, X13, X11, K5, K6                       // 62d3250d1ff540
	VPCMPD $64, 7(SI)(DI*1), X11, K5, K6               // 62f3250d1fb43e0700000040
	VPCMPD $64, 15(DX)(BX*8), X11, K5, K6              // 62f3250d1fb4da0f00000040
	VPCMPD $64, X13, X11, K5, K7                       // 62d3250d1ffd40
	VPCMPD $64, 7(SI)(DI*1), X11, K5, K7               // 62f3250d1fbc3e0700000040
	VPCMPD $64, 15(DX)(BX*8), X11, K5, K7              // 62f3250d1fbcda0f00000040
	VPCMPD $27, Y31, Y9, K3, K6                        // 6293352b1ff71b
	VPCMPD $27, 99(R15)(R15*2), Y9, K3, K6             // 6293352b1fb47f630000001b
	VPCMPD $27, -7(DI), Y9, K3, K6                     // 62f3352b1fb7f9ffffff1b
	VPCMPD $27, Y31, Y9, K3, K4                        // 6293352b1fe71b
	VPCMPD $27, 99(R15)(R15*2), Y9, K3, K4             // 6293352b1fa47f630000001b
	VPCMPD $27, -7(DI), Y9, K3, K4                     // 62f3352b1fa7f9ffffff1b
	VPCMPD $47, Z17, Z20, K4, K4                       // 62b35d441fe12f
	VPCMPD $47, Z0, Z20, K4, K4                        // 62f35d441fe02f
	VPCMPD $47, -7(CX), Z20, K4, K4                    // 62f35d441fa1f9ffffff2f
	VPCMPD $47, 15(DX)(BX*4), Z20, K4, K4              // 62f35d441fa49a0f0000002f
	VPCMPD $47, Z17, Z0, K4, K4                        // 62b37d4c1fe12f
	VPCMPD $47, Z0, Z0, K4, K4                         // 62f37d4c1fe02f
	VPCMPD $47, -7(CX), Z0, K4, K4                     // 62f37d4c1fa1f9ffffff2f
	VPCMPD $47, 15(DX)(BX*4), Z0, K4, K4               // 62f37d4c1fa49a0f0000002f
	VPCMPD $47, Z17, Z20, K4, K6                       // 62b35d441ff12f
	VPCMPD $47, Z0, Z20, K4, K6                        // 62f35d441ff02f
	VPCMPD $47, -7(CX), Z20, K4, K6                    // 62f35d441fb1f9ffffff2f
	VPCMPD $47, 15(DX)(BX*4), Z20, K4, K6              // 62f35d441fb49a0f0000002f
	VPCMPD $47, Z17, Z0, K4, K6                        // 62b37d4c1ff12f
	VPCMPD $47, Z0, Z0, K4, K6                         // 62f37d4c1ff02f
	VPCMPD $47, -7(CX), Z0, K4, K6                     // 62f37d4c1fb1f9ffffff2f
	VPCMPD $47, 15(DX)(BX*4), Z0, K4, K6               // 62f37d4c1fb49a0f0000002f
	VPCMPEQD X14, X16, K3, K6                          // 62d17d0376f6
	VPCMPEQD -7(CX), X16, K3, K6                       // 62f17d0376b1f9ffffff
	VPCMPEQD 15(DX)(BX*4), X16, K3, K6                 // 62f17d0376b49a0f000000
	VPCMPEQD X14, X16, K3, K5                          // 62d17d0376ee
	VPCMPEQD -7(CX), X16, K3, K5                       // 62f17d0376a9f9ffffff
	VPCMPEQD 15(DX)(BX*4), X16, K3, K5                 // 62f17d0376ac9a0f000000
	VPCMPEQD Y13, Y28, K3, K1                          // 62d11d2376cd
	VPCMPEQD 15(DX)(BX*1), Y28, K3, K1                 // 62f11d23768c1a0f000000
	VPCMPEQD -7(CX)(DX*2), Y28, K3, K1                 // 62f11d23768c51f9ffffff
	VPCMPEQD Y13, Y28, K3, K5                          // 62d11d2376ed
	VPCMPEQD 15(DX)(BX*1), Y28, K3, K5                 // 62f11d2376ac1a0f000000
	VPCMPEQD -7(CX)(DX*2), Y28, K3, K5                 // 62f11d2376ac51f9ffffff
	VPCMPEQD Z6, Z21, K2, K3                           // 62f1554276de
	VPCMPEQD Z9, Z21, K2, K3                           // 62d1554276d9
	VPCMPEQD (AX), Z21, K2, K3                         // 62f155427618
	VPCMPEQD 7(SI), Z21, K2, K3                        // 62f15542769e07000000
	VPCMPEQD Z6, Z9, K2, K3                            // 62f1354a76de
	VPCMPEQD Z9, Z9, K2, K3                            // 62d1354a76d9
	VPCMPEQD (AX), Z9, K2, K3                          // 62f1354a7618
	VPCMPEQD 7(SI), Z9, K2, K3                         // 62f1354a769e07000000
	VPCMPEQD Z6, Z21, K2, K1                           // 62f1554276ce
	VPCMPEQD Z9, Z21, K2, K1                           // 62d1554276c9
	VPCMPEQD (AX), Z21, K2, K1                         // 62f155427608
	VPCMPEQD 7(SI), Z21, K2, K1                        // 62f15542768e07000000
	VPCMPEQD Z6, Z9, K2, K1                            // 62f1354a76ce
	VPCMPEQD Z9, Z9, K2, K1                            // 62d1354a76c9
	VPCMPEQD (AX), Z9, K2, K1                          // 62f1354a7608
	VPCMPEQD 7(SI), Z9, K2, K1                         // 62f1354a768e07000000
	VPCMPEQQ X14, X11, K1, K5                          // 62d2a50929ee
	VPCMPEQQ 99(R15)(R15*8), X11, K1, K5               // 6292a50929acff63000000
	VPCMPEQQ 7(AX)(CX*8), X11, K1, K5                  // 62f2a50929acc807000000
	VPCMPEQQ X14, X11, K1, K4                          // 62d2a50929e6
	VPCMPEQQ 99(R15)(R15*8), X11, K1, K4               // 6292a50929a4ff63000000
	VPCMPEQQ 7(AX)(CX*8), X11, K1, K4                  // 62f2a50929a4c807000000
	VPCMPEQQ Y2, Y7, K2, K7                            // 62f2c52a29fa
	VPCMPEQQ -17(BP), Y7, K2, K7                       // 62f2c52a29bdefffffff
	VPCMPEQQ -15(R14)(R15*8), Y7, K2, K7               // 6292c52a29bcfef1ffffff
	VPCMPEQQ Y2, Y7, K2, K6                            // 62f2c52a29f2
	VPCMPEQQ -17(BP), Y7, K2, K6                       // 62f2c52a29b5efffffff
	VPCMPEQQ -15(R14)(R15*8), Y7, K2, K6               // 6292c52a29b4fef1ffffff
	VPCMPEQQ Z20, Z1, K1, K4                           // 62b2f54929e4
	VPCMPEQQ Z9, Z1, K1, K4                            // 62d2f54929e1
	VPCMPEQQ (BX), Z1, K1, K4                          // 62f2f5492923
	VPCMPEQQ -17(BP)(SI*1), Z1, K1, K4                 // 62f2f54929a435efffffff
	VPCMPEQQ Z20, Z9, K1, K4                           // 62b2b54929e4
	VPCMPEQQ Z9, Z9, K1, K4                            // 62d2b54929e1
	VPCMPEQQ (BX), Z9, K1, K4                          // 62f2b5492923
	VPCMPEQQ -17(BP)(SI*1), Z9, K1, K4                 // 62f2b54929a435efffffff
	VPCMPEQQ Z20, Z1, K1, K6                           // 62b2f54929f4
	VPCMPEQQ Z9, Z1, K1, K6                            // 62d2f54929f1
	VPCMPEQQ (BX), Z1, K1, K6                          // 62f2f5492933
	VPCMPEQQ -17(BP)(SI*1), Z1, K1, K6                 // 62f2f54929b435efffffff
	VPCMPEQQ Z20, Z9, K1, K6                           // 62b2b54929f4
	VPCMPEQQ Z9, Z9, K1, K6                            // 62d2b54929f1
	VPCMPEQQ (BX), Z9, K1, K6                          // 62f2b5492933
	VPCMPEQQ -17(BP)(SI*1), Z9, K1, K6                 // 62f2b54929b435efffffff
	VPCMPGTD X12, X23, K4, K4                          // 62d1450466e4
	VPCMPGTD 15(R8)(R14*4), X23, K4, K4                // 6291450466a4b00f000000
	VPCMPGTD -7(CX)(DX*4), X23, K4, K4                 // 62f1450466a491f9ffffff
	VPCMPGTD X12, X23, K4, K6                          // 62d1450466f4
	VPCMPGTD 15(R8)(R14*4), X23, K4, K6                // 6291450466b4b00f000000
	VPCMPGTD -7(CX)(DX*4), X23, K4, K6                 // 62f1450466b491f9ffffff
	VPCMPGTD Y3, Y9, K1, K4                            // 62f1352966e3
	VPCMPGTD 15(R8)(R14*8), Y9, K1, K4                 // 6291352966a4f00f000000
	VPCMPGTD -15(R14)(R15*2), Y9, K1, K4               // 6291352966a47ef1ffffff
	VPCMPGTD Y3, Y9, K1, K5                            // 62f1352966eb
	VPCMPGTD 15(R8)(R14*8), Y9, K1, K5                 // 6291352966acf00f000000
	VPCMPGTD -15(R14)(R15*2), Y9, K1, K5               // 6291352966ac7ef1ffffff
	VPCMPGTD Z12, Z14, K3, K2                          // 62d10d4b66d4
	VPCMPGTD Z13, Z14, K3, K2                          // 62d10d4b66d5
	VPCMPGTD 17(SP)(BP*1), Z14, K3, K2                 // 62f10d4b66942c11000000
	VPCMPGTD -7(CX)(DX*8), Z14, K3, K2                 // 62f10d4b6694d1f9ffffff
	VPCMPGTD Z12, Z13, K3, K2                          // 62d1154b66d4
	VPCMPGTD Z13, Z13, K3, K2                          // 62d1154b66d5
	VPCMPGTD 17(SP)(BP*1), Z13, K3, K2                 // 62f1154b66942c11000000
	VPCMPGTD -7(CX)(DX*8), Z13, K3, K2                 // 62f1154b6694d1f9ffffff
	VPCMPGTD Z12, Z14, K3, K7                          // 62d10d4b66fc
	VPCMPGTD Z13, Z14, K3, K7                          // 62d10d4b66fd
	VPCMPGTD 17(SP)(BP*1), Z14, K3, K7                 // 62f10d4b66bc2c11000000
	VPCMPGTD -7(CX)(DX*8), Z14, K3, K7                 // 62f10d4b66bcd1f9ffffff
	VPCMPGTD Z12, Z13, K3, K7                          // 62d1154b66fc
	VPCMPGTD Z13, Z13, K3, K7                          // 62d1154b66fd
	VPCMPGTD 17(SP)(BP*1), Z13, K3, K7                 // 62f1154b66bc2c11000000
	VPCMPGTD -7(CX)(DX*8), Z13, K3, K7                 // 62f1154b66bcd1f9ffffff
	VPCMPGTQ X23, X16, K4, K0                          // 62b2fd0437c7
	VPCMPGTQ (R8), X16, K4, K0                         // 62d2fd043700
	VPCMPGTQ 15(DX)(BX*2), X16, K4, K0                 // 62f2fd0437845a0f000000
	VPCMPGTQ X23, X16, K4, K5                          // 62b2fd0437ef
	VPCMPGTQ (R8), X16, K4, K5                         // 62d2fd043728
	VPCMPGTQ 15(DX)(BX*2), X16, K4, K5                 // 62f2fd0437ac5a0f000000
	VPCMPGTQ Y9, Y2, K5, K6                            // 62d2ed2d37f1
	VPCMPGTQ -15(R14)(R15*1), Y2, K5, K6               // 6292ed2d37b43ef1ffffff
	VPCMPGTQ -15(BX), Y2, K5, K6                       // 62f2ed2d37b3f1ffffff
	VPCMPGTQ Y9, Y2, K5, K5                            // 62d2ed2d37e9
	VPCMPGTQ -15(R14)(R15*1), Y2, K5, K5               // 6292ed2d37ac3ef1ffffff
	VPCMPGTQ -15(BX), Y2, K5, K5                       // 62f2ed2d37abf1ffffff
	VPCMPGTQ Z2, Z21, K7, K1                           // 62f2d54737ca
	VPCMPGTQ Z7, Z21, K7, K1                           // 62f2d54737cf
	VPCMPGTQ -17(BP)(SI*2), Z21, K7, K1                // 62f2d547378c75efffffff
	VPCMPGTQ 7(AX)(CX*2), Z21, K7, K1                  // 62f2d547378c4807000000
	VPCMPGTQ Z2, Z9, K7, K1                            // 62f2b54f37ca
	VPCMPGTQ Z7, Z9, K7, K1                            // 62f2b54f37cf
	VPCMPGTQ -17(BP)(SI*2), Z9, K7, K1                 // 62f2b54f378c75efffffff
	VPCMPGTQ 7(AX)(CX*2), Z9, K7, K1                   // 62f2b54f378c4807000000
	VPCMPGTQ Z2, Z21, K7, K5                           // 62f2d54737ea
	VPCMPGTQ Z7, Z21, K7, K5                           // 62f2d54737ef
	VPCMPGTQ -17(BP)(SI*2), Z21, K7, K5                // 62f2d54737ac75efffffff
	VPCMPGTQ 7(AX)(CX*2), Z21, K7, K5                  // 62f2d54737ac4807000000
	VPCMPGTQ Z2, Z9, K7, K5                            // 62f2b54f37ea
	VPCMPGTQ Z7, Z9, K7, K5                            // 62f2b54f37ef
	VPCMPGTQ -17(BP)(SI*2), Z9, K7, K5                 // 62f2b54f37ac75efffffff
	VPCMPGTQ 7(AX)(CX*2), Z9, K7, K5                   // 62f2b54f37ac4807000000
	VPCMPQ $82, X24, X31, K7, K4                       // 629385071fe052
	VPCMPQ $82, -17(BP)(SI*2), X31, K7, K4             // 62f385071fa475efffffff52
	VPCMPQ $82, 7(AX)(CX*2), X31, K7, K4               // 62f385071fa4480700000052
	VPCMPQ $82, X24, X31, K7, K6                       // 629385071ff052
	VPCMPQ $82, -17(BP)(SI*2), X31, K7, K6             // 62f385071fb475efffffff52
	VPCMPQ $82, 7(AX)(CX*2), X31, K7, K6               // 62f385071fb4480700000052
	VPCMPQ $126, Y30, Y14, K4, K0                      // 62938d2c1fc67e
	VPCMPQ $126, (SI), Y14, K4, K0                     // 62f38d2c1f067e
	VPCMPQ $126, 7(SI)(DI*2), Y14, K4, K0              // 62f38d2c1f847e070000007e
	VPCMPQ $126, Y30, Y14, K4, K7                      // 62938d2c1ffe7e
	VPCMPQ $126, (SI), Y14, K4, K7                     // 62f38d2c1f3e7e
	VPCMPQ $126, 7(SI)(DI*2), Y14, K4, K7              // 62f38d2c1fbc7e070000007e
	VPCMPQ $94, Z3, Z27, K4, K5                        // 62f3a5441feb5e
	VPCMPQ $94, Z0, Z27, K4, K5                        // 62f3a5441fe85e
	VPCMPQ $94, (R14), Z27, K4, K5                     // 62d3a5441f2e5e
	VPCMPQ $94, -7(DI)(R8*8), Z27, K4, K5              // 62b3a5441facc7f9ffffff5e
	VPCMPQ $94, Z3, Z14, K4, K5                        // 62f38d4c1feb5e
	VPCMPQ $94, Z0, Z14, K4, K5                        // 62f38d4c1fe85e
	VPCMPQ $94, (R14), Z14, K4, K5                     // 62d38d4c1f2e5e
	VPCMPQ $94, -7(DI)(R8*8), Z14, K4, K5              // 62b38d4c1facc7f9ffffff5e
	VPCMPQ $94, Z3, Z27, K4, K4                        // 62f3a5441fe35e
	VPCMPQ $94, Z0, Z27, K4, K4                        // 62f3a5441fe05e
	VPCMPQ $94, (R14), Z27, K4, K4                     // 62d3a5441f265e
	VPCMPQ $94, -7(DI)(R8*8), Z27, K4, K4              // 62b3a5441fa4c7f9ffffff5e
	VPCMPQ $94, Z3, Z14, K4, K4                        // 62f38d4c1fe35e
	VPCMPQ $94, Z0, Z14, K4, K4                        // 62f38d4c1fe05e
	VPCMPQ $94, (R14), Z14, K4, K4                     // 62d38d4c1f265e
	VPCMPQ $94, -7(DI)(R8*8), Z14, K4, K4              // 62b38d4c1fa4c7f9ffffff5e
	VPCMPUD $67, X23, X11, K3, K6                      // 62b3250b1ef743
	VPCMPUD $67, (R14), X11, K3, K6                    // 62d3250b1e3643
	VPCMPUD $67, -7(DI)(R8*8), X11, K3, K6             // 62b3250b1eb4c7f9ffffff43
	VPCMPUD $67, X23, X11, K3, K4                      // 62b3250b1ee743
	VPCMPUD $67, (R14), X11, K3, K4                    // 62d3250b1e2643
	VPCMPUD $67, -7(DI)(R8*8), X11, K3, K4             // 62b3250b1ea4c7f9ffffff43
	VPCMPUD $127, Y1, Y16, K4, K4                      // 62f37d241ee17f
	VPCMPUD $127, 7(SI)(DI*4), Y16, K4, K4             // 62f37d241ea4be070000007f
	VPCMPUD $127, -7(DI)(R8*2), Y16, K4, K4            // 62b37d241ea447f9ffffff7f
	VPCMPUD $127, Y1, Y16, K4, K6                      // 62f37d241ef17f
	VPCMPUD $127, 7(SI)(DI*4), Y16, K4, K6             // 62f37d241eb4be070000007f
	VPCMPUD $127, -7(DI)(R8*2), Y16, K4, K6            // 62b37d241eb447f9ffffff7f
	VPCMPUD $0, Z1, Z22, K2, K4                        // 62f34d421ee100
	VPCMPUD $0, Z16, Z22, K2, K4                       // 62b34d421ee000
	VPCMPUD $0, (CX), Z22, K2, K4                      // 62f34d421e2100
	VPCMPUD $0, 99(R15), Z22, K2, K4                   // 62d34d421ea76300000000
	VPCMPUD $0, Z1, Z25, K2, K4                        // 62f335421ee100
	VPCMPUD $0, Z16, Z25, K2, K4                       // 62b335421ee000
	VPCMPUD $0, (CX), Z25, K2, K4                      // 62f335421e2100
	VPCMPUD $0, 99(R15), Z25, K2, K4                   // 62d335421ea76300000000
	VPCMPUD $0, Z1, Z22, K2, K5                        // 62f34d421ee900
	VPCMPUD $0, Z16, Z22, K2, K5                       // 62b34d421ee800
	VPCMPUD $0, (CX), Z22, K2, K5                      // 62f34d421e2900
	VPCMPUD $0, 99(R15), Z22, K2, K5                   // 62d34d421eaf6300000000
	VPCMPUD $0, Z1, Z25, K2, K5                        // 62f335421ee900
	VPCMPUD $0, Z16, Z25, K2, K5                       // 62b335421ee800
	VPCMPUD $0, (CX), Z25, K2, K5                      // 62f335421e2900
	VPCMPUD $0, 99(R15), Z25, K2, K5                   // 62d335421eaf6300000000
	VPCMPUQ $97, X20, X2, K2, K2                       // 62b3ed0a1ed461
	VPCMPUQ $97, 99(R15)(R15*4), X2, K2, K2            // 6293ed0a1e94bf6300000061
	VPCMPUQ $97, 15(DX), X2, K2, K2                    // 62f3ed0a1e920f00000061
	VPCMPUQ $97, X20, X2, K2, K7                       // 62b3ed0a1efc61
	VPCMPUQ $97, 99(R15)(R15*4), X2, K2, K7            // 6293ed0a1ebcbf6300000061
	VPCMPUQ $97, 15(DX), X2, K2, K7                    // 62f3ed0a1eba0f00000061
	VPCMPUQ $81, Y31, Y30, K3, K0                      // 62938d231ec751
	VPCMPUQ $81, 17(SP), Y30, K3, K0                   // 62f38d231e84241100000051
	VPCMPUQ $81, -17(BP)(SI*4), Y30, K3, K0            // 62f38d231e84b5efffffff51
	VPCMPUQ $81, Y31, Y30, K3, K5                      // 62938d231eef51
	VPCMPUQ $81, 17(SP), Y30, K3, K5                   // 62f38d231eac241100000051
	VPCMPUQ $81, -17(BP)(SI*4), Y30, K3, K5            // 62f38d231eacb5efffffff51
	VPCMPUQ $42, Z0, Z6, K3, K6                        // 62f3cd4b1ef02a
	VPCMPUQ $42, Z8, Z6, K3, K6                        // 62d3cd4b1ef02a
	VPCMPUQ $42, 99(R15)(R15*2), Z6, K3, K6            // 6293cd4b1eb47f630000002a
	VPCMPUQ $42, -7(DI), Z6, K3, K6                    // 62f3cd4b1eb7f9ffffff2a
	VPCMPUQ $42, Z0, Z2, K3, K6                        // 62f3ed4b1ef02a
	VPCMPUQ $42, Z8, Z2, K3, K6                        // 62d3ed4b1ef02a
	VPCMPUQ $42, 99(R15)(R15*2), Z2, K3, K6            // 6293ed4b1eb47f630000002a
	VPCMPUQ $42, -7(DI), Z2, K3, K6                    // 62f3ed4b1eb7f9ffffff2a
	VPCMPUQ $42, Z0, Z6, K3, K5                        // 62f3cd4b1ee82a
	VPCMPUQ $42, Z8, Z6, K3, K5                        // 62d3cd4b1ee82a
	VPCMPUQ $42, 99(R15)(R15*2), Z6, K3, K5            // 6293cd4b1eac7f630000002a
	VPCMPUQ $42, -7(DI), Z6, K3, K5                    // 62f3cd4b1eaff9ffffff2a
	VPCMPUQ $42, Z0, Z2, K3, K5                        // 62f3ed4b1ee82a
	VPCMPUQ $42, Z8, Z2, K3, K5                        // 62d3ed4b1ee82a
	VPCMPUQ $42, 99(R15)(R15*2), Z2, K3, K5            // 6293ed4b1eac7f630000002a
	VPCMPUQ $42, -7(DI), Z2, K3, K5                    // 62f3ed4b1eaff9ffffff2a
	VPCOMPRESSD X9, K7, X8                             // 62527d0f8bc8
	VPCOMPRESSD X9, K7, 15(DX)(BX*1)                   // 62727d0f8b8c1a0f000000
	VPCOMPRESSD X9, K7, -7(CX)(DX*2)                   // 62727d0f8b8c51f9ffffff
	VPCOMPRESSD Y14, K2, Y20                           // 62327d2a8bf4
	VPCOMPRESSD Y14, K2, 7(SI)(DI*8)                   // 62727d2a8bb4fe07000000
	VPCOMPRESSD Y14, K2, -15(R14)                      // 62527d2a8bb6f1ffffff
	VPCOMPRESSD Z26, K4, Z6                            // 62627d4c8bd6
	VPCOMPRESSD Z14, K4, Z6                            // 62727d4c8bf6
	VPCOMPRESSD Z26, K4, Z14                           // 62427d4c8bd6
	VPCOMPRESSD Z14, K4, Z14                           // 62527d4c8bf6
	VPCOMPRESSD Z26, K4, 17(SP)(BP*2)                  // 62627d4c8b946c11000000
	VPCOMPRESSD Z14, K4, 17(SP)(BP*2)                  // 62727d4c8bb46c11000000
	VPCOMPRESSD Z26, K4, -7(DI)(R8*4)                  // 62227d4c8b9487f9ffffff
	VPCOMPRESSD Z14, K4, -7(DI)(R8*4)                  // 62327d4c8bb487f9ffffff
	VPCOMPRESSQ X31, K1, X2                            // 6262fd098bfa
	VPCOMPRESSQ X31, K1, -17(BP)                       // 6262fd098bbdefffffff
	VPCOMPRESSQ X31, K1, -15(R14)(R15*8)               // 6202fd098bbcfef1ffffff
	VPCOMPRESSQ Y13, K3, Y24                           // 6212fd2b8be8
	VPCOMPRESSQ Y13, K3, 7(SI)(DI*1)                   // 6272fd2b8bac3e07000000
	VPCOMPRESSQ Y13, K3, 15(DX)(BX*8)                  // 6272fd2b8bacda0f000000
	VPCOMPRESSQ Z13, K4, Z28                           // 6212fd4c8bec
	VPCOMPRESSQ Z21, K4, Z28                           // 6282fd4c8bec
	VPCOMPRESSQ Z13, K4, Z6                            // 6272fd4c8bee
	VPCOMPRESSQ Z21, K4, Z6                            // 62e2fd4c8bee
	VPCOMPRESSQ Z13, K4, 15(R8)                        // 6252fd4c8ba80f000000
	VPCOMPRESSQ Z21, K4, 15(R8)                        // 62c2fd4c8ba80f000000
	VPCOMPRESSQ Z13, K4, (BP)                          // 6272fd4c8b6d00
	VPCOMPRESSQ Z21, K4, (BP)                          // 62e2fd4c8b6d00
	VPERMD Y11, Y8, K1, Y24                            // 62423d2936c3
	VPERMD -17(BP)(SI*2), Y8, K1, Y24                  // 62623d29368475efffffff
	VPERMD 7(AX)(CX*2), Y8, K1, Y24                    // 62623d2936844807000000
	VPERMD Z20, Z0, K1, Z7                             // 62b27d4936fc
	VPERMD Z28, Z0, K1, Z7                             // 62927d4936fc
	VPERMD 99(R15)(R15*1), Z0, K1, Z7                  // 62927d4936bc3f63000000
	VPERMD (DX), Z0, K1, Z7                            // 62f27d49363a
	VPERMD Z20, Z6, K1, Z7                             // 62b24d4936fc
	VPERMD Z28, Z6, K1, Z7                             // 62924d4936fc
	VPERMD 99(R15)(R15*1), Z6, K1, Z7                  // 62924d4936bc3f63000000
	VPERMD (DX), Z6, K1, Z7                            // 62f24d49363a
	VPERMD Z20, Z0, K1, Z9                             // 62327d4936cc
	VPERMD Z28, Z0, K1, Z9                             // 62127d4936cc
	VPERMD 99(R15)(R15*1), Z0, K1, Z9                  // 62127d49368c3f63000000
	VPERMD (DX), Z0, K1, Z9                            // 62727d49360a
	VPERMD Z20, Z6, K1, Z9                             // 62324d4936cc
	VPERMD Z28, Z6, K1, Z9                             // 62124d4936cc
	VPERMD 99(R15)(R15*1), Z6, K1, Z9                  // 62124d49368c3f63000000
	VPERMD (DX), Z6, K1, Z9                            // 62724d49360a
	VPERMI2D X1, X22, K1, X0                           // 62f24d0176c1
	VPERMI2D 7(AX), X22, K1, X0                        // 62f24d01768007000000
	VPERMI2D (DI), X22, K1, X0                         // 62f24d017607
	VPERMI2D Y9, Y16, K3, Y21                          // 62c27d2376e9
	VPERMI2D (R14), Y16, K3, Y21                       // 62c27d23762e
	VPERMI2D -7(DI)(R8*8), Y16, K3, Y21                // 62a27d2376acc7f9ffffff
	VPERMI2D Z2, Z18, K4, Z11                          // 62726d4476da
	VPERMI2D Z21, Z18, K4, Z11                         // 62326d4476dd
	VPERMI2D 7(SI)(DI*8), Z18, K4, Z11                 // 62726d44769cfe07000000
	VPERMI2D -15(R14), Z18, K4, Z11                    // 62526d44769ef1ffffff
	VPERMI2D Z2, Z24, K4, Z11                          // 62723d4476da
	VPERMI2D Z21, Z24, K4, Z11                         // 62323d4476dd
	VPERMI2D 7(SI)(DI*8), Z24, K4, Z11                 // 62723d44769cfe07000000
	VPERMI2D -15(R14), Z24, K4, Z11                    // 62523d44769ef1ffffff
	VPERMI2D Z2, Z18, K4, Z5                           // 62f26d4476ea
	VPERMI2D Z21, Z18, K4, Z5                          // 62b26d4476ed
	VPERMI2D 7(SI)(DI*8), Z18, K4, Z5                  // 62f26d4476acfe07000000
	VPERMI2D -15(R14), Z18, K4, Z5                     // 62d26d4476aef1ffffff
	VPERMI2D Z2, Z24, K4, Z5                           // 62f23d4476ea
	VPERMI2D Z21, Z24, K4, Z5                          // 62b23d4476ed
	VPERMI2D 7(SI)(DI*8), Z24, K4, Z5                  // 62f23d4476acfe07000000
	VPERMI2D -15(R14), Z24, K4, Z5                     // 62d23d4476aef1ffffff
	VPERMI2PD X7, X6, K5, X11                          // 6272cd0d77df
	VPERMI2PD 99(R15)(R15*1), X6, K5, X11              // 6212cd0d779c3f63000000
	VPERMI2PD (DX), X6, K5, X11                        // 6272cd0d771a
	VPERMI2PD Y6, Y9, K7, Y13                          // 6272b52f77ee
	VPERMI2PD 99(R15)(R15*4), Y9, K7, Y13              // 6212b52f77acbf63000000
	VPERMI2PD 15(DX), Y9, K7, Y13                      // 6272b52f77aa0f000000
	VPERMI2PD Z6, Z6, K7, Z7                           // 62f2cd4f77fe
	VPERMI2PD Z22, Z6, K7, Z7                          // 62b2cd4f77fe
	VPERMI2PD 7(SI)(DI*1), Z6, K7, Z7                  // 62f2cd4f77bc3e07000000
	VPERMI2PD 15(DX)(BX*8), Z6, K7, Z7                 // 62f2cd4f77bcda0f000000
	VPERMI2PD Z6, Z16, K7, Z7                          // 62f2fd4777fe
	VPERMI2PD Z22, Z16, K7, Z7                         // 62b2fd4777fe
	VPERMI2PD 7(SI)(DI*1), Z16, K7, Z7                 // 62f2fd4777bc3e07000000
	VPERMI2PD 15(DX)(BX*8), Z16, K7, Z7                // 62f2fd4777bcda0f000000
	VPERMI2PD Z6, Z6, K7, Z13                          // 6272cd4f77ee
	VPERMI2PD Z22, Z6, K7, Z13                         // 6232cd4f77ee
	VPERMI2PD 7(SI)(DI*1), Z6, K7, Z13                 // 6272cd4f77ac3e07000000
	VPERMI2PD 15(DX)(BX*8), Z6, K7, Z13                // 6272cd4f77acda0f000000
	VPERMI2PD Z6, Z16, K7, Z13                         // 6272fd4777ee
	VPERMI2PD Z22, Z16, K7, Z13                        // 6232fd4777ee
	VPERMI2PD 7(SI)(DI*1), Z16, K7, Z13                // 6272fd4777ac3e07000000
	VPERMI2PD 15(DX)(BX*8), Z16, K7, Z13               // 6272fd4777acda0f000000
	VPERMI2PS X3, X31, K6, X8                          // 6272050677c3
	VPERMI2PS -17(BP)(SI*8), X31, K6, X8               // 627205067784f5efffffff
	VPERMI2PS (R15), X31, K6, X8                       // 625205067707
	VPERMI2PS Y6, Y7, K3, Y3                           // 62f2452b77de
	VPERMI2PS (CX), Y7, K3, Y3                         // 62f2452b7719
	VPERMI2PS 99(R15), Y7, K3, Y3                      // 62d2452b779f63000000
	VPERMI2PS Z18, Z13, K7, Z1                         // 62b2154f77ca
	VPERMI2PS Z8, Z13, K7, Z1                          // 62d2154f77c8
	VPERMI2PS -7(DI)(R8*1), Z13, K7, Z1                // 62b2154f778c07f9ffffff
	VPERMI2PS (SP), Z13, K7, Z1                        // 62f2154f770c24
	VPERMI2PS Z18, Z13, K7, Z15                        // 6232154f77fa
	VPERMI2PS Z8, Z13, K7, Z15                         // 6252154f77f8
	VPERMI2PS -7(DI)(R8*1), Z13, K7, Z15               // 6232154f77bc07f9ffffff
	VPERMI2PS (SP), Z13, K7, Z15                       // 6272154f773c24
	VPERMI2Q X24, X20, K4, X28                         // 6202dd0476e0
	VPERMI2Q 7(SI)(DI*8), X20, K4, X28                 // 6262dd0476a4fe07000000
	VPERMI2Q -15(R14), X20, K4, X28                    // 6242dd0476a6f1ffffff
	VPERMI2Q Y26, Y11, K4, Y26                         // 6202a52c76d2
	VPERMI2Q 99(R15)(R15*2), Y11, K4, Y26              // 6202a52c76947f63000000
	VPERMI2Q -7(DI), Y11, K4, Y26                      // 6262a52c7697f9ffffff
	VPERMI2Q Z20, Z2, K7, Z22                          // 62a2ed4f76f4
	VPERMI2Q Z9, Z2, K7, Z22                           // 62c2ed4f76f1
	VPERMI2Q -7(CX), Z2, K7, Z22                       // 62e2ed4f76b1f9ffffff
	VPERMI2Q 15(DX)(BX*4), Z2, K7, Z22                 // 62e2ed4f76b49a0f000000
	VPERMI2Q Z20, Z31, K7, Z22                         // 62a2854776f4
	VPERMI2Q Z9, Z31, K7, Z22                          // 62c2854776f1
	VPERMI2Q -7(CX), Z31, K7, Z22                      // 62e2854776b1f9ffffff
	VPERMI2Q 15(DX)(BX*4), Z31, K7, Z22                // 62e2854776b49a0f000000
	VPERMI2Q Z20, Z2, K7, Z7                           // 62b2ed4f76fc
	VPERMI2Q Z9, Z2, K7, Z7                            // 62d2ed4f76f9
	VPERMI2Q -7(CX), Z2, K7, Z7                        // 62f2ed4f76b9f9ffffff
	VPERMI2Q 15(DX)(BX*4), Z2, K7, Z7                  // 62f2ed4f76bc9a0f000000
	VPERMI2Q Z20, Z31, K7, Z7                          // 62b2854776fc
	VPERMI2Q Z9, Z31, K7, Z7                           // 62d2854776f9
	VPERMI2Q -7(CX), Z31, K7, Z7                       // 62f2854776b9f9ffffff
	VPERMI2Q 15(DX)(BX*4), Z31, K7, Z7                 // 62f2854776bc9a0f000000
	VPERMILPD $94, X6, K4, X12                         // 6273fd0c05e65e
	VPERMILPD $94, -7(DI)(R8*1), K4, X12               // 6233fd0c05a407f9ffffff5e
	VPERMILPD $94, (SP), K4, X12                       // 6273fd0c0524245e
	VPERMILPD $121, Y18, K2, Y31                       // 6223fd2a05fa79
	VPERMILPD $121, 15(DX)(BX*1), K2, Y31              // 6263fd2a05bc1a0f00000079
	VPERMILPD $121, -7(CX)(DX*2), K2, Y31              // 6263fd2a05bc51f9ffffff79
	VPERMILPD $13, Z3, K2, Z14                         // 6273fd4a05f30d
	VPERMILPD $13, Z12, K2, Z14                        // 6253fd4a05f40d
	VPERMILPD $13, (AX), K2, Z14                       // 6273fd4a05300d
	VPERMILPD $13, 7(SI), K2, Z14                      // 6273fd4a05b6070000000d
	VPERMILPD $13, Z3, K2, Z28                         // 6263fd4a05e30d
	VPERMILPD $13, Z12, K2, Z28                        // 6243fd4a05e40d
	VPERMILPD $13, (AX), K2, Z28                       // 6263fd4a05200d
	VPERMILPD $13, 7(SI), K2, Z28                      // 6263fd4a05a6070000000d
	VPERMILPD X6, X28, K3, X17                         // 62e29d030dce
	VPERMILPD -7(CX), X28, K3, X17                     // 62e29d030d89f9ffffff
	VPERMILPD 15(DX)(BX*4), X28, K3, X17               // 62e29d030d8c9a0f000000
	VPERMILPD Y2, Y24, K3, Y3                          // 62f2bd230dda
	VPERMILPD -17(BP), Y24, K3, Y3                     // 62f2bd230d9defffffff
	VPERMILPD -15(R14)(R15*8), Y24, K3, Y3             // 6292bd230d9cfef1ffffff
	VPERMILPD Z5, Z19, K3, Z15                         // 6272e5430dfd
	VPERMILPD Z1, Z19, K3, Z15                         // 6272e5430df9
	VPERMILPD (BX), Z19, K3, Z15                       // 6272e5430d3b
	VPERMILPD -17(BP)(SI*1), Z19, K3, Z15              // 6272e5430dbc35efffffff
	VPERMILPD Z5, Z15, K3, Z15                         // 6272854b0dfd
	VPERMILPD Z1, Z15, K3, Z15                         // 6272854b0df9
	VPERMILPD (BX), Z15, K3, Z15                       // 6272854b0d3b
	VPERMILPD -17(BP)(SI*1), Z15, K3, Z15              // 6272854b0dbc35efffffff
	VPERMILPD Z5, Z19, K3, Z30                         // 6262e5430df5
	VPERMILPD Z1, Z19, K3, Z30                         // 6262e5430df1
	VPERMILPD (BX), Z19, K3, Z30                       // 6262e5430d33
	VPERMILPD -17(BP)(SI*1), Z19, K3, Z30              // 6262e5430db435efffffff
	VPERMILPD Z5, Z15, K3, Z30                         // 6262854b0df5
	VPERMILPD Z1, Z15, K3, Z30                         // 6262854b0df1
	VPERMILPD (BX), Z15, K3, Z30                       // 6262854b0d33
	VPERMILPD -17(BP)(SI*1), Z15, K3, Z30              // 6262854b0db435efffffff
	VPERMILPS $65, X8, K2, X1                          // 62d37d0a04c841
	VPERMILPS $65, 99(R15)(R15*8), K2, X1              // 62937d0a048cff6300000041
	VPERMILPS $65, 7(AX)(CX*8), K2, X1                 // 62f37d0a048cc80700000041
	VPERMILPS $67, Y21, K1, Y7                         // 62b37d2904fd43
	VPERMILPS $67, 17(SP)(BP*2), K1, Y7                // 62f37d2904bc6c1100000043
	VPERMILPS $67, -7(DI)(R8*4), K1, Y7                // 62b37d2904bc87f9ffffff43
	VPERMILPS $127, Z14, K2, Z3                        // 62d37d4a04de7f
	VPERMILPS $127, Z15, K2, Z3                        // 62d37d4a04df7f
	VPERMILPS $127, 15(R8)(R14*4), K2, Z3              // 62937d4a049cb00f0000007f
	VPERMILPS $127, -7(CX)(DX*4), K2, Z3               // 62f37d4a049c91f9ffffff7f
	VPERMILPS $127, Z14, K2, Z5                        // 62d37d4a04ee7f
	VPERMILPS $127, Z15, K2, Z5                        // 62d37d4a04ef7f
	VPERMILPS $127, 15(R8)(R14*4), K2, Z5              // 62937d4a04acb00f0000007f
	VPERMILPS $127, -7(CX)(DX*4), K2, Z5               // 62f37d4a04ac91f9ffffff7f
	VPERMILPS X0, X6, K1, X8                           // 62724d090cc0
	VPERMILPS (AX), X6, K1, X8                         // 62724d090c00
	VPERMILPS 7(SI), X6, K1, X8                        // 62724d090c8607000000
	VPERMILPS Y20, Y8, K7, Y14                         // 62323d2f0cf4
	VPERMILPS 15(R8), Y8, K7, Y14                      // 62523d2f0cb00f000000
	VPERMILPS (BP), Y8, K7, Y14                        // 62723d2f0c7500
	VPERMILPS Z20, Z16, K1, Z21                        // 62a27d410cec
	VPERMILPS Z0, Z16, K1, Z21                         // 62e27d410ce8
	VPERMILPS (R8), Z16, K1, Z21                       // 62c27d410c28
	VPERMILPS 15(DX)(BX*2), Z16, K1, Z21               // 62e27d410cac5a0f000000
	VPERMILPS Z20, Z9, K1, Z21                         // 62a235490cec
	VPERMILPS Z0, Z9, K1, Z21                          // 62e235490ce8
	VPERMILPS (R8), Z9, K1, Z21                        // 62c235490c28
	VPERMILPS 15(DX)(BX*2), Z9, K1, Z21                // 62e235490cac5a0f000000
	VPERMILPS Z20, Z16, K1, Z8                         // 62327d410cc4
	VPERMILPS Z0, Z16, K1, Z8                          // 62727d410cc0
	VPERMILPS (R8), Z16, K1, Z8                        // 62527d410c00
	VPERMILPS 15(DX)(BX*2), Z16, K1, Z8                // 62727d410c845a0f000000
	VPERMILPS Z20, Z9, K1, Z8                          // 623235490cc4
	VPERMILPS Z0, Z9, K1, Z8                           // 627235490cc0
	VPERMILPS (R8), Z9, K1, Z8                         // 625235490c00
	VPERMILPS 15(DX)(BX*2), Z9, K1, Z8                 // 627235490c845a0f000000
	VPERMPD $0, Y24, K1, Y11                           // 6213fd2901d800
	VPERMPD $0, 15(R8)(R14*8), K1, Y11                 // 6213fd29019cf00f00000000
	VPERMPD $0, -15(R14)(R15*2), K1, Y11               // 6213fd29019c7ef1ffffff00
	VPERMPD $97, Z0, K1, Z23                           // 62e3fd4901f861
	VPERMPD $97, Z11, K1, Z23                          // 62c3fd4901fb61
	VPERMPD $97, 17(SP)(BP*1), K1, Z23                 // 62e3fd4901bc2c1100000061
	VPERMPD $97, -7(CX)(DX*8), K1, Z23                 // 62e3fd4901bcd1f9ffffff61
	VPERMPD $97, Z0, K1, Z19                           // 62e3fd4901d861
	VPERMPD $97, Z11, K1, Z19                          // 62c3fd4901db61
	VPERMPD $97, 17(SP)(BP*1), K1, Z19                 // 62e3fd49019c2c1100000061
	VPERMPD $97, -7(CX)(DX*8), K1, Z19                 // 62e3fd49019cd1f9ffffff61
	VPERMPD Y18, Y5, K7, Y1                            // 62b2d52f16ca
	VPERMPD -15(R14)(R15*1), Y5, K7, Y1                // 6292d52f168c3ef1ffffff
	VPERMPD -15(BX), Y5, K7, Y1                        // 62f2d52f168bf1ffffff
	VPERMPD Z0, Z24, K2, Z0                            // 62f2bd4216c0
	VPERMPD Z26, Z24, K2, Z0                           // 6292bd4216c2
	VPERMPD -17(BP)(SI*2), Z24, K2, Z0                 // 62f2bd42168475efffffff
	VPERMPD 7(AX)(CX*2), Z24, K2, Z0                   // 62f2bd4216844807000000
	VPERMPD Z0, Z12, K2, Z0                            // 62f29d4a16c0
	VPERMPD Z26, Z12, K2, Z0                           // 62929d4a16c2
	VPERMPD -17(BP)(SI*2), Z12, K2, Z0                 // 62f29d4a168475efffffff
	VPERMPD 7(AX)(CX*2), Z12, K2, Z0                   // 62f29d4a16844807000000
	VPERMPD Z0, Z24, K2, Z25                           // 6262bd4216c8
	VPERMPD Z26, Z24, K2, Z25                          // 6202bd4216ca
	VPERMPD -17(BP)(SI*2), Z24, K2, Z25                // 6262bd42168c75efffffff
	VPERMPD 7(AX)(CX*2), Z24, K2, Z25                  // 6262bd42168c4807000000
	VPERMPD Z0, Z12, K2, Z25                           // 62629d4a16c8
	VPERMPD Z26, Z12, K2, Z25                          // 62029d4a16ca
	VPERMPD -17(BP)(SI*2), Z12, K2, Z25                // 62629d4a168c75efffffff
	VPERMPD 7(AX)(CX*2), Z12, K2, Z25                  // 62629d4a168c4807000000
	VPERMPS Y9, Y20, K4, Y20                           // 62c25d2416e1
	VPERMPS 7(AX)(CX*4), Y20, K4, Y20                  // 62e25d2416a48807000000
	VPERMPS 7(AX)(CX*1), Y20, K4, Y20                  // 62e25d2416a40807000000
	VPERMPS Z9, Z9, K1, Z9                             // 6252354916c9
	VPERMPS Z28, Z9, K1, Z9                            // 6212354916cc
	VPERMPS 15(R8)(R14*1), Z9, K1, Z9                  // 62123549168c300f000000
	VPERMPS 15(R8)(R14*2), Z9, K1, Z9                  // 62123549168c700f000000
	VPERMPS Z9, Z25, K1, Z9                            // 6252354116c9
	VPERMPS Z28, Z25, K1, Z9                           // 6212354116cc
	VPERMPS 15(R8)(R14*1), Z25, K1, Z9                 // 62123541168c300f000000
	VPERMPS 15(R8)(R14*2), Z25, K1, Z9                 // 62123541168c700f000000
	VPERMPS Z9, Z9, K1, Z3                             // 62d2354916d9
	VPERMPS Z28, Z9, K1, Z3                            // 6292354916dc
	VPERMPS 15(R8)(R14*1), Z9, K1, Z3                  // 62923549169c300f000000
	VPERMPS 15(R8)(R14*2), Z9, K1, Z3                  // 62923549169c700f000000
	VPERMPS Z9, Z25, K1, Z3                            // 62d2354116d9
	VPERMPS Z28, Z25, K1, Z3                           // 6292354116dc
	VPERMPS 15(R8)(R14*1), Z25, K1, Z3                 // 62923541169c300f000000
	VPERMPS 15(R8)(R14*2), Z25, K1, Z3                 // 62923541169c700f000000
	VPERMQ $81, Y28, K3, Y28                           // 6203fd2b00e451
	VPERMQ $81, (SI), K3, Y28                          // 6263fd2b002651
	VPERMQ $81, 7(SI)(DI*2), K3, Y28                   // 6263fd2b00a47e0700000051
	VPERMQ $42, Z17, K4, Z20                           // 62a3fd4c00e12a
	VPERMQ $42, Z0, K4, Z20                            // 62e3fd4c00e02a
	VPERMQ $42, (R14), K4, Z20                         // 62c3fd4c00262a
	VPERMQ $42, -7(DI)(R8*8), K4, Z20                  // 62a3fd4c00a4c7f9ffffff2a
	VPERMQ $42, Z17, K4, Z0                            // 62b3fd4c00c12a
	VPERMQ $42, Z0, K4, Z0                             // 62f3fd4c00c02a
	VPERMQ $42, (R14), K4, Z0                          // 62d3fd4c00062a
	VPERMQ $42, -7(DI)(R8*8), K4, Z0                   // 62b3fd4c0084c7f9ffffff2a
	VPERMQ Y11, Y8, K5, Y1                             // 62d2bd2d36cb
	VPERMQ 17(SP)(BP*8), Y8, K5, Y1                    // 62f2bd2d368cec11000000
	VPERMQ 17(SP)(BP*4), Y8, K5, Y1                    // 62f2bd2d368cac11000000
	VPERMQ Z21, Z31, K7, Z17                           // 62a2854736cd
	VPERMQ Z9, Z31, K7, Z17                            // 62c2854736c9
	VPERMQ 99(R15)(R15*4), Z31, K7, Z17                // 62828547368cbf63000000
	VPERMQ 15(DX), Z31, K7, Z17                        // 62e28547368a0f000000
	VPERMQ Z21, Z0, K7, Z17                            // 62a2fd4f36cd
	VPERMQ Z9, Z0, K7, Z17                             // 62c2fd4f36c9
	VPERMQ 99(R15)(R15*4), Z0, K7, Z17                 // 6282fd4f368cbf63000000
	VPERMQ 15(DX), Z0, K7, Z17                         // 62e2fd4f368a0f000000
	VPERMQ Z21, Z31, K7, Z23                           // 62a2854736fd
	VPERMQ Z9, Z31, K7, Z23                            // 62c2854736f9
	VPERMQ 99(R15)(R15*4), Z31, K7, Z23                // 6282854736bcbf63000000
	VPERMQ 15(DX), Z31, K7, Z23                        // 62e2854736ba0f000000
	VPERMQ Z21, Z0, K7, Z23                            // 62a2fd4f36fd
	VPERMQ Z9, Z0, K7, Z23                             // 62c2fd4f36f9
	VPERMQ 99(R15)(R15*4), Z0, K7, Z23                 // 6282fd4f36bcbf63000000
	VPERMQ 15(DX), Z0, K7, Z23                         // 62e2fd4f36ba0f000000
	VPERMT2D X12, X22, K7, X6                          // 62d24d077ef4
	VPERMT2D 15(R8)(R14*4), X22, K7, X6                // 62924d077eb4b00f000000
	VPERMT2D -7(CX)(DX*4), X22, K7, X6                 // 62f24d077eb491f9ffffff
	VPERMT2D Y26, Y6, K4, Y12                          // 62124d2c7ee2
	VPERMT2D 17(SP), Y6, K4, Y12                       // 62724d2c7ea42411000000
	VPERMT2D -17(BP)(SI*4), Y6, K4, Y12                // 62724d2c7ea4b5efffffff
	VPERMT2D Z7, Z26, K4, Z30                          // 62622d447ef7
	VPERMT2D Z21, Z26, K4, Z30                         // 62222d447ef5
	VPERMT2D 99(R15)(R15*2), Z26, K4, Z30              // 62022d447eb47f63000000
	VPERMT2D -7(DI), Z26, K4, Z30                      // 62622d447eb7f9ffffff
	VPERMT2D Z7, Z22, K4, Z30                          // 62624d447ef7
	VPERMT2D Z21, Z22, K4, Z30                         // 62224d447ef5
	VPERMT2D 99(R15)(R15*2), Z22, K4, Z30              // 62024d447eb47f63000000
	VPERMT2D -7(DI), Z22, K4, Z30                      // 62624d447eb7f9ffffff
	VPERMT2D Z7, Z26, K4, Z5                           // 62f22d447eef
	VPERMT2D Z21, Z26, K4, Z5                          // 62b22d447eed
	VPERMT2D 99(R15)(R15*2), Z26, K4, Z5               // 62922d447eac7f63000000
	VPERMT2D -7(DI), Z26, K4, Z5                       // 62f22d447eaff9ffffff
	VPERMT2D Z7, Z22, K4, Z5                           // 62f24d447eef
	VPERMT2D Z21, Z22, K4, Z5                          // 62b24d447eed
	VPERMT2D 99(R15)(R15*2), Z22, K4, Z5               // 62924d447eac7f63000000
	VPERMT2D -7(DI), Z22, K4, Z5                       // 62f24d447eaff9ffffff
	VPERMT2PD X8, X28, K7, X16                         // 62c29d077fc0
	VPERMT2PD (R8), X28, K7, X16                       // 62c29d077f00
	VPERMT2PD 15(DX)(BX*2), X28, K7, X16               // 62e29d077f845a0f000000
	VPERMT2PD Y28, Y8, K2, Y3                          // 6292bd2a7fdc
	VPERMT2PD 7(AX), Y8, K2, Y3                        // 62f2bd2a7f9807000000
	VPERMT2PD (DI), Y8, K2, Y3                         // 62f2bd2a7f1f
	VPERMT2PD Z12, Z14, K5, Z16                        // 62c28d4d7fc4
	VPERMT2PD Z13, Z14, K5, Z16                        // 62c28d4d7fc5
	VPERMT2PD -7(CX)(DX*1), Z14, K5, Z16               // 62e28d4d7f8411f9ffffff
	VPERMT2PD -15(R14)(R15*4), Z14, K5, Z16            // 62828d4d7f84bef1ffffff
	VPERMT2PD Z12, Z13, K5, Z16                        // 62c2954d7fc4
	VPERMT2PD Z13, Z13, K5, Z16                        // 62c2954d7fc5
	VPERMT2PD -7(CX)(DX*1), Z13, K5, Z16               // 62e2954d7f8411f9ffffff
	VPERMT2PD -15(R14)(R15*4), Z13, K5, Z16            // 6282954d7f84bef1ffffff
	VPERMT2PD Z12, Z14, K5, Z25                        // 62428d4d7fcc
	VPERMT2PD Z13, Z14, K5, Z25                        // 62428d4d7fcd
	VPERMT2PD -7(CX)(DX*1), Z14, K5, Z25               // 62628d4d7f8c11f9ffffff
	VPERMT2PD -15(R14)(R15*4), Z14, K5, Z25            // 62028d4d7f8cbef1ffffff
	VPERMT2PD Z12, Z13, K5, Z25                        // 6242954d7fcc
	VPERMT2PD Z13, Z13, K5, Z25                        // 6242954d7fcd
	VPERMT2PD -7(CX)(DX*1), Z13, K5, Z25               // 6262954d7f8c11f9ffffff
	VPERMT2PD -15(R14)(R15*4), Z13, K5, Z25            // 6202954d7f8cbef1ffffff
	VPERMT2PS X1, X11, K3, X15                         // 6272250b7ff9
	VPERMT2PS 17(SP)(BP*1), X11, K3, X15               // 6272250b7fbc2c11000000
	VPERMT2PS -7(CX)(DX*8), X11, K3, X15               // 6272250b7fbcd1f9ffffff
	VPERMT2PS Y14, Y23, K4, Y1                         // 62d245247fce
	VPERMT2PS 99(R15)(R15*1), Y23, K4, Y1              // 629245247f8c3f63000000
	VPERMT2PS (DX), Y23, K4, Y1                        // 62f245247f0a
	VPERMT2PS Z27, Z2, K2, Z21                         // 62826d4a7feb
	VPERMT2PS Z25, Z2, K2, Z21                         // 62826d4a7fe9
	VPERMT2PS 15(DX)(BX*1), Z2, K2, Z21                // 62e26d4a7fac1a0f000000
	VPERMT2PS -7(CX)(DX*2), Z2, K2, Z21                // 62e26d4a7fac51f9ffffff
	VPERMT2PS Z27, Z7, K2, Z21                         // 6282454a7feb
	VPERMT2PS Z25, Z7, K2, Z21                         // 6282454a7fe9
	VPERMT2PS 15(DX)(BX*1), Z7, K2, Z21                // 62e2454a7fac1a0f000000
	VPERMT2PS -7(CX)(DX*2), Z7, K2, Z21                // 62e2454a7fac51f9ffffff
	VPERMT2PS Z27, Z2, K2, Z9                          // 62126d4a7fcb
	VPERMT2PS Z25, Z2, K2, Z9                          // 62126d4a7fc9
	VPERMT2PS 15(DX)(BX*1), Z2, K2, Z9                 // 62726d4a7f8c1a0f000000
	VPERMT2PS -7(CX)(DX*2), Z2, K2, Z9                 // 62726d4a7f8c51f9ffffff
	VPERMT2PS Z27, Z7, K2, Z9                          // 6212454a7fcb
	VPERMT2PS Z25, Z7, K2, Z9                          // 6212454a7fc9
	VPERMT2PS 15(DX)(BX*1), Z7, K2, Z9                 // 6272454a7f8c1a0f000000
	VPERMT2PS -7(CX)(DX*2), Z7, K2, Z9                 // 6272454a7f8c51f9ffffff
	VPERMT2Q X2, X13, K2, X19                          // 62e2950a7eda
	VPERMT2Q -17(BP)(SI*2), X13, K2, X19               // 62e2950a7e9c75efffffff
	VPERMT2Q 7(AX)(CX*2), X13, K2, X19                 // 62e2950a7e9c4807000000
	VPERMT2Q Y2, Y25, K3, Y31                          // 6262b5237efa
	VPERMT2Q -17(BP)(SI*8), Y25, K3, Y31               // 6262b5237ebcf5efffffff
	VPERMT2Q (R15), Y25, K3, Y31                       // 6242b5237e3f
	VPERMT2Q Z3, Z27, K3, Z23                          // 62e2a5437efb
	VPERMT2Q Z0, Z27, K3, Z23                          // 62e2a5437ef8
	VPERMT2Q -17(BP), Z27, K3, Z23                     // 62e2a5437ebdefffffff
	VPERMT2Q -15(R14)(R15*8), Z27, K3, Z23             // 6282a5437ebcfef1ffffff
	VPERMT2Q Z3, Z14, K3, Z23                          // 62e28d4b7efb
	VPERMT2Q Z0, Z14, K3, Z23                          // 62e28d4b7ef8
	VPERMT2Q -17(BP), Z14, K3, Z23                     // 62e28d4b7ebdefffffff
	VPERMT2Q -15(R14)(R15*8), Z14, K3, Z23             // 62828d4b7ebcfef1ffffff
	VPERMT2Q Z3, Z27, K3, Z9                           // 6272a5437ecb
	VPERMT2Q Z0, Z27, K3, Z9                           // 6272a5437ec8
	VPERMT2Q -17(BP), Z27, K3, Z9                      // 6272a5437e8defffffff
	VPERMT2Q -15(R14)(R15*8), Z27, K3, Z9              // 6212a5437e8cfef1ffffff
	VPERMT2Q Z3, Z14, K3, Z9                           // 62728d4b7ecb
	VPERMT2Q Z0, Z14, K3, Z9                           // 62728d4b7ec8
	VPERMT2Q -17(BP), Z14, K3, Z9                      // 62728d4b7e8defffffff
	VPERMT2Q -15(R14)(R15*8), Z14, K3, Z9              // 62128d4b7e8cfef1ffffff
	VPEXPANDD X2, K7, X9                               // 62727d0f89ca
	VPEXPANDD (CX), K7, X9                             // 62727d0f8909
	VPEXPANDD 99(R15), K7, X9                          // 62527d0f898f63000000
	VPEXPANDD Y1, K2, Y6                               // 62f27d2a89f1
	VPEXPANDD -7(CX), K2, Y6                           // 62f27d2a89b1f9ffffff
	VPEXPANDD 15(DX)(BX*4), K2, Y6                     // 62f27d2a89b49a0f000000
	VPEXPANDD Z13, K4, Z11                             // 62527d4c89dd
	VPEXPANDD Z14, K4, Z11                             // 62527d4c89de
	VPEXPANDD -15(R14)(R15*1), K4, Z11                 // 62127d4c899c3ef1ffffff
	VPEXPANDD -15(BX), K4, Z11                         // 62727d4c899bf1ffffff
	VPEXPANDD Z13, K4, Z5                              // 62d27d4c89ed
	VPEXPANDD Z14, K4, Z5                              // 62d27d4c89ee
	VPEXPANDD -15(R14)(R15*1), K4, Z5                  // 62927d4c89ac3ef1ffffff
	VPEXPANDD -15(BX), K4, Z5                          // 62f27d4c89abf1ffffff
	VPEXPANDQ X2, K1, X24                              // 6262fd0989c2
	VPEXPANDQ 99(R15)(R15*2), K1, X24                  // 6202fd0989847f63000000
	VPEXPANDQ -7(DI), K1, X24                          // 6262fd098987f9ffffff
	VPEXPANDQ Y0, K3, Y9                               // 6272fd2b89c8
	VPEXPANDQ 99(R15)(R15*8), K3, Y9                   // 6212fd2b898cff63000000
	VPEXPANDQ 7(AX)(CX*8), K3, Y9                      // 6272fd2b898cc807000000
	VPEXPANDQ Z2, K4, Z5                               // 62f2fd4c89ea
	VPEXPANDQ 7(AX)(CX*4), K4, Z5                      // 62f2fd4c89ac8807000000
	VPEXPANDQ 7(AX)(CX*1), K4, Z5                      // 62f2fd4c89ac0807000000
	VPEXPANDQ Z2, K4, Z23                              // 62e2fd4c89fa
	VPEXPANDQ 7(AX)(CX*4), K4, Z23                     // 62e2fd4c89bc8807000000
	VPEXPANDQ 7(AX)(CX*1), K4, Z23                     // 62e2fd4c89bc0807000000
	VPGATHERDD (DX)(X10*4), K6, X3                     // 62b27d0e901c92
	VPGATHERDD (SP)(X4*2), K6, X3                      // 62f27d0e901c64
	VPGATHERDD (R14)(X29*8), K6, X3                    // 62927d06901cee
	VPGATHERDD (R10)(Y29*8), K3, Y22                   // 62827d239034ea
	VPGATHERDD (SP)(Y4*2), K3, Y22                     // 62e27d2b903464
	VPGATHERDD (DX)(Y10*4), K3, Y22                    // 62a27d2b903492
	VPGATHERDD (BP)(Z10*2), K7, Z28                    // 62227d4f90645500
	VPGATHERDD (R10)(Z29*8), K7, Z28                   // 62027d479024ea
	VPGATHERDD (R14)(Z29*8), K7, Z28                   // 62027d479024ee
	VPGATHERDD (BP)(Z10*2), K7, Z6                     // 62b27d4f90745500
	VPGATHERDD (R10)(Z29*8), K7, Z6                    // 62927d479034ea
	VPGATHERDD (R14)(Z29*8), K7, Z6                    // 62927d479034ee
	VPGATHERDQ (AX)(X4*1), K4, X11                     // 6272fd0c901c20
	VPGATHERDQ (BP)(X10*2), K4, X11                    // 6232fd0c905c5500
	VPGATHERDQ (R10)(X29*8), K4, X11                   // 6212fd04901cea
	VPGATHERDQ (DX)(X10*4), K4, Y9                     // 6232fd2c900c92
	VPGATHERDQ (SP)(X4*2), K4, Y9                      // 6272fd2c900c64
	VPGATHERDQ (R14)(X29*8), K4, Y9                    // 6212fd24900cee
	VPGATHERDQ (R14)(Y29*8), K7, Z13                   // 6212fd47902cee
	VPGATHERDQ (AX)(Y4*1), K7, Z13                     // 6272fd4f902c20
	VPGATHERDQ (BP)(Y10*2), K7, Z13                    // 6232fd4f906c5500
	VPGATHERDQ (R14)(Y29*8), K7, Z21                   // 6282fd47902cee
	VPGATHERDQ (AX)(Y4*1), K7, Z21                     // 62e2fd4f902c20
	VPGATHERDQ (BP)(Y10*2), K7, Z21                    // 62a2fd4f906c5500
	VPGATHERQD (AX)(X4*1), K2, X15                     // 62727d0a913c20
	VPGATHERQD (BP)(X10*2), K2, X15                    // 62327d0a917c5500
	VPGATHERQD (R10)(X29*8), K2, X15                   // 62127d02913cea
	VPGATHERQD (R10)(Y29*8), K5, X30                   // 62027d259134ea
	VPGATHERQD (SP)(Y4*2), K5, X30                     // 62627d2d913464
	VPGATHERQD (DX)(Y10*4), K5, X30                    // 62227d2d913492
	VPGATHERQD (DX)(Z10*4), K3, Y23                    // 62a27d4b913c92
	VPGATHERQD (AX)(Z4*1), K3, Y23                     // 62e27d4b913c20
	VPGATHERQD (SP)(Z4*2), K3, Y23                     // 62e27d4b913c64
	VPGATHERQQ (DX)(X10*4), K4, X13                    // 6232fd0c912c92
	VPGATHERQQ (SP)(X4*2), K4, X13                     // 6272fd0c912c64
	VPGATHERQQ (R14)(X29*8), K4, X13                   // 6212fd04912cee
	VPGATHERQQ (R14)(Y29*8), K2, Y31                   // 6202fd22913cee
	VPGATHERQQ (AX)(Y4*1), K2, Y31                     // 6262fd2a913c20
	VPGATHERQQ (BP)(Y10*2), K2, Y31                    // 6222fd2a917c5500
	VPGATHERQQ (BP)(Z10*2), K2, Z26                    // 6222fd4a91545500
	VPGATHERQQ (R10)(Z29*8), K2, Z26                   // 6202fd429114ea
	VPGATHERQQ (R14)(Z29*8), K2, Z26                   // 6202fd429114ee
	VPGATHERQQ (BP)(Z10*2), K2, Z3                     // 62b2fd4a915c5500
	VPGATHERQQ (R10)(Z29*8), K2, Z3                    // 6292fd42911cea
	VPGATHERQQ (R14)(Z29*8), K2, Z3                    // 6292fd42911cee
	VPMAXSD X1, X31, K3, X16                           // 62e205033dc1
	VPMAXSD (SI), X31, K3, X16                         // 62e205033d06
	VPMAXSD 7(SI)(DI*2), X31, K3, X16                  // 62e205033d847e07000000
	VPMAXSD Y24, Y18, K7, Y20                          // 62826d273de0
	VPMAXSD 99(R15)(R15*4), Y18, K7, Y20               // 62826d273da4bf63000000
	VPMAXSD 15(DX), Y18, K7, Y20                       // 62e26d273da20f000000
	VPMAXSD Z0, Z7, K4, Z3                             // 62f2454c3dd8
	VPMAXSD Z6, Z7, K4, Z3                             // 62f2454c3dde
	VPMAXSD 7(SI)(DI*1), Z7, K4, Z3                    // 62f2454c3d9c3e07000000
	VPMAXSD 15(DX)(BX*8), Z7, K4, Z3                   // 62f2454c3d9cda0f000000
	VPMAXSD Z0, Z9, K4, Z3                             // 62f2354c3dd8
	VPMAXSD Z6, Z9, K4, Z3                             // 62f2354c3dde
	VPMAXSD 7(SI)(DI*1), Z9, K4, Z3                    // 62f2354c3d9c3e07000000
	VPMAXSD 15(DX)(BX*8), Z9, K4, Z3                   // 62f2354c3d9cda0f000000
	VPMAXSD Z0, Z7, K4, Z27                            // 6262454c3dd8
	VPMAXSD Z6, Z7, K4, Z27                            // 6262454c3dde
	VPMAXSD 7(SI)(DI*1), Z7, K4, Z27                   // 6262454c3d9c3e07000000
	VPMAXSD 15(DX)(BX*8), Z7, K4, Z27                  // 6262454c3d9cda0f000000
	VPMAXSD Z0, Z9, K4, Z27                            // 6262354c3dd8
	VPMAXSD Z6, Z9, K4, Z27                            // 6262354c3dde
	VPMAXSD 7(SI)(DI*1), Z9, K4, Z27                   // 6262354c3d9c3e07000000
	VPMAXSD 15(DX)(BX*8), Z9, K4, Z27                  // 6262354c3d9cda0f000000
	VPMAXSQ X15, X9, K4, X7                            // 62d2b50c3dff
	VPMAXSQ 17(SP)(BP*8), X9, K4, X7                   // 62f2b50c3dbcec11000000
	VPMAXSQ 17(SP)(BP*4), X9, K4, X7                   // 62f2b50c3dbcac11000000
	VPMAXSQ Y19, Y3, K7, Y9                            // 6232e52f3dcb
	VPMAXSQ (CX), Y3, K7, Y9                           // 6272e52f3d09
	VPMAXSQ 99(R15), Y3, K7, Y9                        // 6252e52f3d8f63000000
	VPMAXSQ Z9, Z3, K2, Z20                            // 62c2e54a3de1
	VPMAXSQ Z19, Z3, K2, Z20                           // 62a2e54a3de3
	VPMAXSQ -7(DI)(R8*1), Z3, K2, Z20                  // 62a2e54a3da407f9ffffff
	VPMAXSQ (SP), Z3, K2, Z20                          // 62e2e54a3d2424
	VPMAXSQ Z9, Z30, K2, Z20                           // 62c28d423de1
	VPMAXSQ Z19, Z30, K2, Z20                          // 62a28d423de3
	VPMAXSQ -7(DI)(R8*1), Z30, K2, Z20                 // 62a28d423da407f9ffffff
	VPMAXSQ (SP), Z30, K2, Z20                         // 62e28d423d2424
	VPMAXSQ Z9, Z3, K2, Z28                            // 6242e54a3de1
	VPMAXSQ Z19, Z3, K2, Z28                           // 6222e54a3de3
	VPMAXSQ -7(DI)(R8*1), Z3, K2, Z28                  // 6222e54a3da407f9ffffff
	VPMAXSQ (SP), Z3, K2, Z28                          // 6262e54a3d2424
	VPMAXSQ Z9, Z30, K2, Z28                           // 62428d423de1
	VPMAXSQ Z19, Z30, K2, Z28                          // 62228d423de3
	VPMAXSQ -7(DI)(R8*1), Z30, K2, Z28                 // 62228d423da407f9ffffff
	VPMAXSQ (SP), Z30, K2, Z28                         // 62628d423d2424
	VPMAXUD X3, X8, K3, X15                            // 62723d0b3ffb
	VPMAXUD 7(AX), X8, K3, X15                         // 62723d0b3fb807000000
	VPMAXUD (DI), X8, K3, X15                          // 62723d0b3f3f
	VPMAXUD Y20, Y21, K3, Y2                           // 62b255233fd4
	VPMAXUD 15(DX)(BX*1), Y21, K3, Y2                  // 62f255233f941a0f000000
	VPMAXUD -7(CX)(DX*2), Y21, K3, Y2                  // 62f255233f9451f9ffffff
	VPMAXUD Z13, Z1, K2, Z6                            // 62d2754a3ff5
	VPMAXUD (AX), Z1, K2, Z6                           // 62f2754a3f30
	VPMAXUD 7(SI), Z1, K2, Z6                          // 62f2754a3fb607000000
	VPMAXUD Z13, Z15, K2, Z6                           // 62d2054a3ff5
	VPMAXUD (AX), Z15, K2, Z6                          // 62f2054a3f30
	VPMAXUD 7(SI), Z15, K2, Z6                         // 62f2054a3fb607000000
	VPMAXUD Z13, Z1, K2, Z22                           // 62c2754a3ff5
	VPMAXUD (AX), Z1, K2, Z22                          // 62e2754a3f30
	VPMAXUD 7(SI), Z1, K2, Z22                         // 62e2754a3fb607000000
	VPMAXUD Z13, Z15, K2, Z22                          // 62c2054a3ff5
	VPMAXUD (AX), Z15, K2, Z22                         // 62e2054a3f30
	VPMAXUD 7(SI), Z15, K2, Z22                        // 62e2054a3fb607000000
	VPMAXUQ X13, X23, K1, X26                          // 6242c5013fd5
	VPMAXUQ 99(R15)(R15*1), X23, K1, X26               // 6202c5013f943f63000000
	VPMAXUQ (DX), X23, K1, X26                         // 6262c5013f12
	VPMAXUQ Y6, Y31, K2, Y6                            // 62f285223ff6
	VPMAXUQ -17(BP), Y31, K2, Y6                       // 62f285223fb5efffffff
	VPMAXUQ -15(R14)(R15*8), Y31, K2, Y6               // 629285223fb4fef1ffffff
	VPMAXUQ Z2, Z22, K1, Z18                           // 62e2cd413fd2
	VPMAXUQ Z31, Z22, K1, Z18                          // 6282cd413fd7
	VPMAXUQ (BX), Z22, K1, Z18                         // 62e2cd413f13
	VPMAXUQ -17(BP)(SI*1), Z22, K1, Z18                // 62e2cd413f9435efffffff
	VPMAXUQ Z2, Z7, K1, Z18                            // 62e2c5493fd2
	VPMAXUQ Z31, Z7, K1, Z18                           // 6282c5493fd7
	VPMAXUQ (BX), Z7, K1, Z18                          // 62e2c5493f13
	VPMAXUQ -17(BP)(SI*1), Z7, K1, Z18                 // 62e2c5493f9435efffffff
	VPMAXUQ Z2, Z22, K1, Z8                            // 6272cd413fc2
	VPMAXUQ Z31, Z22, K1, Z8                           // 6212cd413fc7
	VPMAXUQ (BX), Z22, K1, Z8                          // 6272cd413f03
	VPMAXUQ -17(BP)(SI*1), Z22, K1, Z8                 // 6272cd413f8435efffffff
	VPMAXUQ Z2, Z7, K1, Z8                             // 6272c5493fc2
	VPMAXUQ Z31, Z7, K1, Z8                            // 6212c5493fc7
	VPMAXUQ (BX), Z7, K1, Z8                           // 6272c5493f03
	VPMAXUQ -17(BP)(SI*1), Z7, K1, Z8                  // 6272c5493f8435efffffff
	VPMINSD X11, X1, K4, X21                           // 62c2750c39eb
	VPMINSD 7(SI)(DI*1), X1, K4, X21                   // 62e2750c39ac3e07000000
	VPMINSD 15(DX)(BX*8), X1, K4, X21                  // 62e2750c39acda0f000000
	VPMINSD Y12, Y20, K1, Y5                           // 62d25d2139ec
	VPMINSD 15(R8)(R14*8), Y20, K1, Y5                 // 62925d2139acf00f000000
	VPMINSD -15(R14)(R15*2), Y20, K1, Y5               // 62925d2139ac7ef1ffffff
	VPMINSD Z5, Z19, K3, Z15                           // 6272654339fd
	VPMINSD Z1, Z19, K3, Z15                           // 6272654339f9
	VPMINSD 17(SP)(BP*1), Z19, K3, Z15                 // 6272654339bc2c11000000
	VPMINSD -7(CX)(DX*8), Z19, K3, Z15                 // 6272654339bcd1f9ffffff
	VPMINSD Z5, Z15, K3, Z15                           // 6272054b39fd
	VPMINSD Z1, Z15, K3, Z15                           // 6272054b39f9
	VPMINSD 17(SP)(BP*1), Z15, K3, Z15                 // 6272054b39bc2c11000000
	VPMINSD -7(CX)(DX*8), Z15, K3, Z15                 // 6272054b39bcd1f9ffffff
	VPMINSD Z5, Z19, K3, Z30                           // 6262654339f5
	VPMINSD Z1, Z19, K3, Z30                           // 6262654339f1
	VPMINSD 17(SP)(BP*1), Z19, K3, Z30                 // 6262654339b42c11000000
	VPMINSD -7(CX)(DX*8), Z19, K3, Z30                 // 6262654339b4d1f9ffffff
	VPMINSD Z5, Z15, K3, Z30                           // 6262054b39f5
	VPMINSD Z1, Z15, K3, Z30                           // 6262054b39f1
	VPMINSD 17(SP)(BP*1), Z15, K3, Z30                 // 6262054b39b42c11000000
	VPMINSD -7(CX)(DX*8), Z15, K3, Z30                 // 6262054b39b4d1f9ffffff
	VPMINSQ X7, X3, K4, X31                            // 6262e50c39ff
	VPMINSQ -7(DI)(R8*1), X3, K4, X31                  // 6222e50c39bc07f9ffffff
	VPMINSQ (SP), X3, K4, X31                          // 6262e50c393c24
	VPMINSQ Y28, Y5, K5, Y3                            // 6292d52d39dc
	VPMINSQ -15(R14)(R15*1), Y5, K5, Y3                // 6292d52d399c3ef1ffffff
	VPMINSQ -15(BX), Y5, K5, Y3                        // 62f2d52d399bf1ffffff
	VPMINSQ Z21, Z14, K7, Z3                           // 62b28d4f39dd
	VPMINSQ Z8, Z14, K7, Z3                            // 62d28d4f39d8
	VPMINSQ -17(BP)(SI*2), Z14, K7, Z3                 // 62f28d4f399c75efffffff
	VPMINSQ 7(AX)(CX*2), Z14, K7, Z3                   // 62f28d4f399c4807000000
	VPMINSQ Z21, Z15, K7, Z3                           // 62b2854f39dd
	VPMINSQ Z8, Z15, K7, Z3                            // 62d2854f39d8
	VPMINSQ -17(BP)(SI*2), Z15, K7, Z3                 // 62f2854f399c75efffffff
	VPMINSQ 7(AX)(CX*2), Z15, K7, Z3                   // 62f2854f399c4807000000
	VPMINSQ Z21, Z14, K7, Z5                           // 62b28d4f39ed
	VPMINSQ Z8, Z14, K7, Z5                            // 62d28d4f39e8
	VPMINSQ -17(BP)(SI*2), Z14, K7, Z5                 // 62f28d4f39ac75efffffff
	VPMINSQ 7(AX)(CX*2), Z14, K7, Z5                   // 62f28d4f39ac4807000000
	VPMINSQ Z21, Z15, K7, Z5                           // 62b2854f39ed
	VPMINSQ Z8, Z15, K7, Z5                            // 62d2854f39e8
	VPMINSQ -17(BP)(SI*2), Z15, K7, Z5                 // 62f2854f39ac75efffffff
	VPMINSQ 7(AX)(CX*2), Z15, K7, Z5                   // 62f2854f39ac4807000000
	VPMINUD X5, X14, K7, X7                            // 62f20d0f3bfd
	VPMINUD (AX), X14, K7, X7                          // 62f20d0f3b38
	VPMINUD 7(SI), X14, K7, X7                         // 62f20d0f3bbe07000000
	VPMINUD Y7, Y17, K2, Y14                           // 627275223bf7
	VPMINUD 17(SP)(BP*8), Y17, K2, Y14                 // 627275223bb4ec11000000
	VPMINUD 17(SP)(BP*4), Y17, K2, Y14                 // 627275223bb4ac11000000
	VPMINUD Z9, Z9, K5, Z0                             // 62d2354d3bc1
	VPMINUD Z25, Z9, K5, Z0                            // 6292354d3bc1
	VPMINUD 99(R15)(R15*4), Z9, K5, Z0                 // 6292354d3b84bf63000000
	VPMINUD 15(DX), Z9, K5, Z0                         // 62f2354d3b820f000000
	VPMINUD Z9, Z3, K5, Z0                             // 62d2654d3bc1
	VPMINUD Z25, Z3, K5, Z0                            // 6292654d3bc1
	VPMINUD 99(R15)(R15*4), Z3, K5, Z0                 // 6292654d3b84bf63000000
	VPMINUD 15(DX), Z3, K5, Z0                         // 62f2654d3b820f000000
	VPMINUD Z9, Z9, K5, Z26                            // 6242354d3bd1
	VPMINUD Z25, Z9, K5, Z26                           // 6202354d3bd1
	VPMINUD 99(R15)(R15*4), Z9, K5, Z26                // 6202354d3b94bf63000000
	VPMINUD 15(DX), Z9, K5, Z26                        // 6262354d3b920f000000
	VPMINUD Z9, Z3, K5, Z26                            // 6242654d3bd1
	VPMINUD Z25, Z3, K5, Z26                           // 6202654d3bd1
	VPMINUD 99(R15)(R15*4), Z3, K5, Z26                // 6202654d3b94bf63000000
	VPMINUD 15(DX), Z3, K5, Z26                        // 6262654d3b920f000000
	VPMINUQ X21, X3, K3, X31                           // 6222e50b3bfd
	VPMINUQ (BX), X3, K3, X31                          // 6262e50b3b3b
	VPMINUQ -17(BP)(SI*1), X3, K3, X31                 // 6262e50b3bbc35efffffff
	VPMINUQ Y8, Y31, K4, Y9                            // 625285243bc8
	VPMINUQ 7(SI)(DI*4), Y31, K4, Y9                   // 627285243b8cbe07000000
	VPMINUQ -7(DI)(R8*2), Y31, K4, Y9                  // 623285243b8c47f9ffffff
	VPMINUQ Z17, Z20, K2, Z9                           // 6232dd423bc9
	VPMINUQ Z0, Z20, K2, Z9                            // 6272dd423bc8
	VPMINUQ (CX), Z20, K2, Z9                          // 6272dd423b09
	VPMINUQ 99(R15), Z20, K2, Z9                       // 6252dd423b8f63000000
	VPMINUQ Z17, Z0, K2, Z9                            // 6232fd4a3bc9
	VPMINUQ Z0, Z0, K2, Z9                             // 6272fd4a3bc8
	VPMINUQ (CX), Z0, K2, Z9                           // 6272fd4a3b09
	VPMINUQ 99(R15), Z0, K2, Z9                        // 6252fd4a3b8f63000000
	VPMINUQ Z17, Z20, K2, Z28                          // 6222dd423be1
	VPMINUQ Z0, Z20, K2, Z28                           // 6262dd423be0
	VPMINUQ (CX), Z20, K2, Z28                         // 6262dd423b21
	VPMINUQ 99(R15), Z20, K2, Z28                      // 6242dd423ba763000000
	VPMINUQ Z17, Z0, K2, Z28                           // 6222fd4a3be1
	VPMINUQ Z0, Z0, K2, Z28                            // 6262fd4a3be0
	VPMINUQ (CX), Z0, K2, Z28                          // 6262fd4a3b21
	VPMINUQ 99(R15), Z0, K2, Z28                       // 6242fd4a3ba763000000
	VPMOVDB X14, K3, X16                               // 62327e0b31f0
	VPMOVDB X14, K3, 15(DX)(BX*1)                      // 62727e0b31b41a0f000000
	VPMOVDB X14, K3, -7(CX)(DX*2)                      // 62727e0b31b451f9ffffff
	VPMOVDB Y21, K2, X11                               // 62c27e2a31eb
	VPMOVDB Y21, K2, (SI)                              // 62e27e2a312e
	VPMOVDB Y21, K2, 7(SI)(DI*2)                       // 62e27e2a31ac7e07000000
	VPMOVDB Z20, K1, X14                               // 62c27e4931e6
	VPMOVDB Z9, K1, X14                                // 62527e4931ce
	VPMOVDB Z20, K1, (R8)                              // 62c27e493120
	VPMOVDB Z9, K1, (R8)                               // 62527e493108
	VPMOVDB Z20, K1, 15(DX)(BX*2)                      // 62e27e4931a45a0f000000
	VPMOVDB Z9, K1, 15(DX)(BX*2)                       // 62727e49318c5a0f000000
	VPMOVDW X8, K2, X19                                // 62327e0a33c3
	VPMOVDW X8, K2, 17(SP)(BP*8)                       // 62727e0a3384ec11000000
	VPMOVDW X8, K2, 17(SP)(BP*4)                       // 62727e0a3384ac11000000
	VPMOVDW Y12, K1, X8                                // 62527e2933e0
	VPMOVDW Y12, K1, 17(SP)(BP*1)                      // 62727e2933a42c11000000
	VPMOVDW Y12, K1, -7(CX)(DX*8)                      // 62727e2933a4d1f9ffffff
	VPMOVDW Z30, K7, Y9                                // 62427e4f33f1
	VPMOVDW Z5, K7, Y9                                 // 62d27e4f33e9
	VPMOVDW Z30, K7, 7(AX)                             // 62627e4f33b007000000
	VPMOVDW Z5, K7, 7(AX)                              // 62f27e4f33a807000000
	VPMOVDW Z30, K7, (DI)                              // 62627e4f3337
	VPMOVDW Z5, K7, (DI)                               // 62f27e4f332f
	VPMOVQB X11, K1, X23                               // 62327e0932df
	VPMOVQB X11, K1, -7(DI)(R8*1)                      // 62327e09329c07f9ffffff
	VPMOVQB X11, K1, (SP)                              // 62727e09321c24
	VPMOVQB Y12, K1, X31                               // 62127e2932e7
	VPMOVQB Y12, K1, -17(BP)                           // 62727e2932a5efffffff
	VPMOVQB Y12, K1, -15(R14)(R15*8)                   // 62127e2932a4fef1ffffff
	VPMOVQB Z21, K1, X24                               // 62827e4932e8
	VPMOVQB Z9, K1, X24                                // 62127e4932c8
	VPMOVQB Z21, K1, 7(SI)(DI*4)                       // 62e27e4932acbe07000000
	VPMOVQB Z9, K1, 7(SI)(DI*4)                        // 62727e49328cbe07000000
	VPMOVQB Z21, K1, -7(DI)(R8*2)                      // 62a27e4932ac47f9ffffff
	VPMOVQB Z9, K1, -7(DI)(R8*2)                       // 62327e49328c47f9ffffff
	VPMOVQD X0, K7, X14                                // 62d27e0f35c6
	VPMOVQD X0, K7, 17(SP)                             // 62f27e0f35842411000000
	VPMOVQD X0, K7, -17(BP)(SI*4)                      // 62f27e0f3584b5efffffff
	VPMOVQD Y21, K2, X11                               // 62c27e2a35eb
	VPMOVQD Y21, K2, -17(BP)(SI*2)                     // 62e27e2a35ac75efffffff
	VPMOVQD Y21, K2, 7(AX)(CX*2)                       // 62e27e2a35ac4807000000
	VPMOVQD Z2, K4, Y14                                // 62d27e4c35d6
	VPMOVQD Z7, K4, Y14                                // 62d27e4c35fe
	VPMOVQD Z2, K4, 99(R15)(R15*1)                     // 62927e4c35943f63000000
	VPMOVQD Z7, K4, 99(R15)(R15*1)                     // 62927e4c35bc3f63000000
	VPMOVQD Z2, K4, (DX)                               // 62f27e4c3512
	VPMOVQD Z7, K4, (DX)                               // 62f27e4c353a
	VPMOVQW X2, K1, X23                                // 62b27e0934d7
	VPMOVQW X2, K1, 17(SP)(BP*2)                       // 62f27e0934946c11000000
	VPMOVQW X2, K1, -7(DI)(R8*4)                       // 62b27e09349487f9ffffff
	VPMOVQW Y30, K3, X20                               // 62227e2b34f4
	VPMOVQW Y30, K3, 7(AX)                             // 62627e2b34b007000000
	VPMOVQW Y30, K3, (DI)                              // 62627e2b3437
	VPMOVQW Z27, K4, X5                                // 62627e4c34dd
	VPMOVQW Z25, K4, X5                                // 62627e4c34cd
	VPMOVQW Z27, K4, 15(R8)(R14*1)                     // 62027e4c349c300f000000
	VPMOVQW Z25, K4, 15(R8)(R14*1)                     // 62027e4c348c300f000000
	VPMOVQW Z27, K4, 15(R8)(R14*2)                     // 62027e4c349c700f000000
	VPMOVQW Z25, K4, 15(R8)(R14*2)                     // 62027e4c348c700f000000
	VPMOVSDB X0, K5, X25                               // 62927e0d21c1
	VPMOVSDB X0, K5, 15(R8)                            // 62d27e0d21800f000000
	VPMOVSDB X0, K5, (BP)                              // 62f27e0d214500
	VPMOVSDB Y26, K7, X9                               // 62427e2f21d1
	VPMOVSDB Y26, K7, 99(R15)(R15*1)                   // 62027e2f21943f63000000
	VPMOVSDB Y26, K7, (DX)                             // 62627e2f2112
	VPMOVSDB Z23, K7, X13                              // 62c27e4f21fd
	VPMOVSDB Z9, K7, X13                               // 62527e4f21cd
	VPMOVSDB Z23, K7, (R14)                            // 62c27e4f213e
	VPMOVSDB Z9, K7, (R14)                             // 62527e4f210e
	VPMOVSDB Z23, K7, -7(DI)(R8*8)                     // 62a27e4f21bcc7f9ffffff
	VPMOVSDB Z9, K7, -7(DI)(R8*8)                      // 62327e4f218cc7f9ffffff
	VPMOVSDW X8, K6, X2                                // 62727e0e23c2
	VPMOVSDW X8, K6, -17(BP)(SI*8)                     // 62727e0e2384f5efffffff
	VPMOVSDW X8, K6, (R15)                             // 62527e0e2307
	VPMOVSDW Y7, K3, X9                                // 62d27e2b23f9
	VPMOVSDW Y7, K3, 99(R15)(R15*4)                    // 62927e2b23bcbf63000000
	VPMOVSDW Y7, K3, 15(DX)                            // 62f27e2b23ba0f000000
	VPMOVSDW Z27, K7, Y16                              // 62227e4f23d8
	VPMOVSDW Z14, K7, Y16                              // 62327e4f23f0
	VPMOVSDW Z27, K7, -17(BP)(SI*8)                    // 62627e4f239cf5efffffff
	VPMOVSDW Z14, K7, -17(BP)(SI*8)                    // 62727e4f23b4f5efffffff
	VPMOVSDW Z27, K7, (R15)                            // 62427e4f231f
	VPMOVSDW Z14, K7, (R15)                            // 62527e4f2337
	VPMOVSQB X31, K4, X2                               // 62627e0c22fa
	VPMOVSQB X31, K4, -7(CX)                           // 62627e0c22b9f9ffffff
	VPMOVSQB X31, K4, 15(DX)(BX*4)                     // 62627e0c22bc9a0f000000
	VPMOVSQB Y1, K4, X11                               // 62d27e2c22cb
	VPMOVSQB Y1, K4, 15(R8)(R14*8)                     // 62927e2c228cf00f000000
	VPMOVSQB Y1, K4, -15(R14)(R15*2)                   // 62927e2c228c7ef1ffffff
	VPMOVSQB Z3, K7, X22                               // 62b27e4f22de
	VPMOVSQB Z0, K7, X22                               // 62b27e4f22c6
	VPMOVSQB Z3, K7, 7(SI)(DI*8)                       // 62f27e4f229cfe07000000
	VPMOVSQB Z0, K7, 7(SI)(DI*8)                       // 62f27e4f2284fe07000000
	VPMOVSQB Z3, K7, -15(R14)                          // 62d27e4f229ef1ffffff
	VPMOVSQB Z0, K7, -15(R14)                          // 62d27e4f2286f1ffffff
	VPMOVSQD X14, K2, X5                               // 62727e0a25f5
	VPMOVSQD X14, K2, 7(SI)(DI*1)                      // 62727e0a25b43e07000000
	VPMOVSQD X14, K2, 15(DX)(BX*8)                     // 62727e0a25b4da0f000000
	VPMOVSQD Y30, K5, X0                               // 62627e2d25f0
	VPMOVSQD Y30, K5, (CX)                             // 62627e2d2531
	VPMOVSQD Y30, K5, 99(R15)                          // 62427e2d25b763000000
	VPMOVSQD Z14, K3, Y31                              // 62127e4b25f7
	VPMOVSQD Z7, K3, Y31                               // 62927e4b25ff
	VPMOVSQD Z14, K3, 7(SI)(DI*8)                      // 62727e4b25b4fe07000000
	VPMOVSQD Z7, K3, 7(SI)(DI*8)                       // 62f27e4b25bcfe07000000
	VPMOVSQD Z14, K3, -15(R14)                         // 62527e4b25b6f1ffffff
	VPMOVSQD Z7, K3, -15(R14)                          // 62d27e4b25bef1ffffff
	VPMOVSQW X7, K4, X17                               // 62b27e0c24f9
	VPMOVSQW X7, K4, -15(R14)(R15*1)                   // 62927e0c24bc3ef1ffffff
	VPMOVSQW X7, K4, -15(BX)                           // 62f27e0c24bbf1ffffff
	VPMOVSQW Y22, K2, X15                              // 62c27e2a24f7
	VPMOVSQW Y22, K2, -7(DI)(R8*1)                     // 62a27e2a24b407f9ffffff
	VPMOVSQW Y22, K2, (SP)                             // 62e27e2a243424
	VPMOVSQW Z8, K2, X11                               // 62527e4a24c3
	VPMOVSQW Z24, K2, X11                              // 62427e4a24c3
	VPMOVSQW Z8, K2, 99(R15)(R15*2)                    // 62127e4a24847f63000000
	VPMOVSQW Z24, K2, 99(R15)(R15*2)                   // 62027e4a24847f63000000
	VPMOVSQW Z8, K2, -7(DI)                            // 62727e4a2487f9ffffff
	VPMOVSQW Z24, K2, -7(DI)                           // 62627e4a2487f9ffffff
	VPMOVSXBD X27, K2, Z1                              // 62927d4a21cb or 6292fd4a21cb
	VPMOVSXBD 15(DX)(BX*1), K2, Z1                     // 62f27d4a218c1a0f000000 or 62f2fd4a218c1a0f000000
	VPMOVSXBD -7(CX)(DX*2), K2, Z1                     // 62f27d4a218c51f9ffffff or 62f2fd4a218c51f9ffffff
	VPMOVSXBD X27, K2, Z16                             // 62827d4a21c3 or 6282fd4a21c3
	VPMOVSXBD 15(DX)(BX*1), K2, Z16                    // 62e27d4a21841a0f000000 or 62e2fd4a21841a0f000000
	VPMOVSXBD -7(CX)(DX*2), K2, Z16                    // 62e27d4a218451f9ffffff or 62e2fd4a218451f9ffffff
	VPMOVSXBD X3, K1, X25                              // 62627d0921cb or 6262fd0921cb
	VPMOVSXBD 7(AX)(CX*4), K1, X25                     // 62627d09218c8807000000 or 6262fd09218c8807000000
	VPMOVSXBD 7(AX)(CX*1), K1, X25                     // 62627d09218c0807000000 or 6262fd09218c0807000000
	VPMOVSXBD X18, K2, Y7                              // 62b27d2a21fa or 62b2fd2a21fa
	VPMOVSXBD 99(R15)(R15*8), K2, Y7                   // 62927d2a21bcff63000000 or 6292fd2a21bcff63000000
	VPMOVSXBD 7(AX)(CX*8), K2, Y7                      // 62f27d2a21bcc807000000 or 62f2fd2a21bcc807000000
	VPMOVSXBQ X28, K1, X15                             // 62127d0922fc or 6212fd0922fc
	VPMOVSXBQ 99(R15)(R15*8), K1, X15                  // 62127d0922bcff63000000 or 6212fd0922bcff63000000
	VPMOVSXBQ 7(AX)(CX*8), K1, X15                     // 62727d0922bcc807000000 or 6272fd0922bcc807000000
	VPMOVSXBQ X15, K7, Y0                              // 62d27d2f22c7 or 62d2fd2f22c7
	VPMOVSXBQ (SI), K7, Y0                             // 62f27d2f2206 or 62f2fd2f2206
	VPMOVSXBQ 7(SI)(DI*2), K7, Y0                      // 62f27d2f22847e07000000 or 62f2fd2f22847e07000000
	VPMOVSXBQ X7, K1, Z6                               // 62f27d4922f7 or 62f2fd4922f7
	VPMOVSXBQ (AX), K1, Z6                             // 62f27d492230 or 62f2fd492230
	VPMOVSXBQ 7(SI), K1, Z6                            // 62f27d4922b607000000 or 62f2fd4922b607000000
	VPMOVSXBQ X7, K1, Z2                               // 62f27d4922d7 or 62f2fd4922d7
	VPMOVSXBQ (AX), K1, Z2                             // 62f27d492210 or 62f2fd492210
	VPMOVSXBQ 7(SI), K1, Z2                            // 62f27d49229607000000 or 62f2fd49229607000000
	VPMOVSXDQ X7, K2, Y14                              // 62727d2a25f7
	VPMOVSXDQ 17(SP)(BP*2), K2, Y14                    // 62727d2a25b46c11000000
	VPMOVSXDQ -7(DI)(R8*4), K2, Y14                    // 62327d2a25b487f9ffffff
	VPMOVSXDQ X22, K4, X0                              // 62b27d0c25c6
	VPMOVSXDQ 15(R8)(R14*4), K4, X0                    // 62927d0c2584b00f000000
	VPMOVSXDQ -7(CX)(DX*4), K4, X0                     // 62f27d0c258491f9ffffff
	VPMOVSXDQ Y24, K1, Z15                             // 62127d4925f8
	VPMOVSXDQ -7(CX), K1, Z15                          // 62727d4925b9f9ffffff
	VPMOVSXDQ 15(DX)(BX*4), K1, Z15                    // 62727d4925bc9a0f000000
	VPMOVSXDQ Y24, K1, Z12                             // 62127d4925e0
	VPMOVSXDQ -7(CX), K1, Z12                          // 62727d4925a1f9ffffff
	VPMOVSXDQ 15(DX)(BX*4), K1, Z12                    // 62727d4925a49a0f000000
	VPMOVSXWD X1, K3, Y13                              // 62727d2b23e9 or 6272fd2b23e9
	VPMOVSXWD 15(R8), K3, Y13                          // 62527d2b23a80f000000 or 6252fd2b23a80f000000
	VPMOVSXWD (BP), K3, Y13                            // 62727d2b236d00 or 6272fd2b236d00
	VPMOVSXWD X6, K4, X11                              // 62727d0c23de or 6272fd0c23de
	VPMOVSXWD (R8), K4, X11                            // 62527d0c2318 or 6252fd0c2318
	VPMOVSXWD 15(DX)(BX*2), K4, X11                    // 62727d0c239c5a0f000000 or 6272fd0c239c5a0f000000
	VPMOVSXWD Y20, K5, Z14                             // 62327d4d23f4 or 6232fd4d23f4
	VPMOVSXWD 99(R15)(R15*8), K5, Z14                  // 62127d4d23b4ff63000000 or 6212fd4d23b4ff63000000
	VPMOVSXWD 7(AX)(CX*8), K5, Z14                     // 62727d4d23b4c807000000 or 6272fd4d23b4c807000000
	VPMOVSXWD Y20, K5, Z27                             // 62227d4d23dc or 6222fd4d23dc
	VPMOVSXWD 99(R15)(R15*8), K5, Z27                  // 62027d4d239cff63000000 or 6202fd4d239cff63000000
	VPMOVSXWD 7(AX)(CX*8), K5, Z27                     // 62627d4d239cc807000000 or 6262fd4d239cc807000000
	VPMOVSXWQ X7, K7, Z11                              // 62727d4f24df or 6272fd4f24df
	VPMOVSXWQ 15(R8)(R14*8), K7, Z11                   // 62127d4f249cf00f000000 or 6212fd4f249cf00f000000
	VPMOVSXWQ -15(R14)(R15*2), K7, Z11                 // 62127d4f249c7ef1ffffff or 6212fd4f249c7ef1ffffff
	VPMOVSXWQ X7, K7, Z5                               // 62f27d4f24ef or 62f2fd4f24ef
	VPMOVSXWQ 15(R8)(R14*8), K7, Z5                    // 62927d4f24acf00f000000 or 6292fd4f24acf00f000000
	VPMOVSXWQ -15(R14)(R15*2), K7, Z5                  // 62927d4f24ac7ef1ffffff or 6292fd4f24ac7ef1ffffff
	VPMOVSXWQ X31, K7, X8                              // 62127d0f24c7 or 6212fd0f24c7
	VPMOVSXWQ 17(SP)(BP*8), K7, X8                     // 62727d0f2484ec11000000 or 6272fd0f2484ec11000000
	VPMOVSXWQ 17(SP)(BP*4), K7, X8                     // 62727d0f2484ac11000000 or 6272fd0f2484ac11000000
	VPMOVSXWQ X3, K6, Y14                              // 62727d2e24f3 or 6272fd2e24f3
	VPMOVSXWQ 17(SP)(BP*1), K6, Y14                    // 62727d2e24b42c11000000 or 6272fd2e24b42c11000000
	VPMOVSXWQ -7(CX)(DX*8), K6, Y14                    // 62727d2e24b4d1f9ffffff or 6272fd2e24b4d1f9ffffff
	VPMOVUSDB X20, K3, X28                             // 62827e0b11e4
	VPMOVUSDB X20, K3, 7(SI)(DI*4)                     // 62e27e0b11a4be07000000
	VPMOVUSDB X20, K3, -7(DI)(R8*2)                    // 62a27e0b11a447f9ffffff
	VPMOVUSDB Y21, K7, X24                             // 62827e2f11e8
	VPMOVUSDB Y21, K7, -17(BP)(SI*2)                   // 62e27e2f11ac75efffffff
	VPMOVUSDB Y21, K7, 7(AX)(CX*2)                     // 62e27e2f11ac4807000000
	VPMOVUSDB Z13, K4, X7                              // 62727e4c11ef
	VPMOVUSDB Z14, K4, X7                              // 62727e4c11f7
	VPMOVUSDB Z13, K4, -15(R14)(R15*1)                 // 62127e4c11ac3ef1ffffff
	VPMOVUSDB Z14, K4, -15(R14)(R15*1)                 // 62127e4c11b43ef1ffffff
	VPMOVUSDB Z13, K4, -15(BX)                         // 62727e4c11abf1ffffff
	VPMOVUSDB Z14, K4, -15(BX)                         // 62727e4c11b3f1ffffff
	VPMOVUSDW X16, K4, X20                             // 62a27e0c13c4
	VPMOVUSDW X16, K4, 15(R8)(R14*1)                   // 62827e0c1384300f000000
	VPMOVUSDW X16, K4, 15(R8)(R14*2)                   // 62827e0c1384700f000000
	VPMOVUSDW Y1, K7, X12                              // 62d27e2f13cc
	VPMOVUSDW Y1, K7, 7(AX)(CX*4)                      // 62f27e2f138c8807000000
	VPMOVUSDW Y1, K7, 7(AX)(CX*1)                      // 62f27e2f138c0807000000
	VPMOVUSDW Z5, K2, Y26                              // 62927e4a13ea
	VPMOVUSDW Z23, K2, Y26                             // 62827e4a13fa
	VPMOVUSDW Z5, K2, (AX)                             // 62f27e4a1328
	VPMOVUSDW Z23, K2, (AX)                            // 62e27e4a1338
	VPMOVUSDW Z5, K2, 7(SI)                            // 62f27e4a13ae07000000
	VPMOVUSDW Z23, K2, 7(SI)                           // 62e27e4a13be07000000
	VPMOVUSQB X17, K5, X6                              // 62e27e0d12ce
	VPMOVUSQB X17, K5, (AX)                            // 62e27e0d1208
	VPMOVUSQB X17, K5, 7(SI)                           // 62e27e0d128e07000000
	VPMOVUSQB Y30, K3, X28                             // 62027e2b12f4
	VPMOVUSQB Y30, K3, 17(SP)                          // 62627e2b12b42411000000
	VPMOVUSQB Y30, K3, -17(BP)(SI*4)                   // 62627e2b12b4b5efffffff
	VPMOVUSQB Z2, K4, X6                               // 62f27e4c12d6
	VPMOVUSQB Z2, K4, (R14)                            // 62d27e4c1216
	VPMOVUSQB Z2, K4, -7(DI)(R8*8)                     // 62b27e4c1294c7f9ffffff
	VPMOVUSQD X8, K2, X1                               // 62727e0a15c1
	VPMOVUSQD X8, K2, 99(R15)(R15*4)                   // 62127e0a1584bf63000000
	VPMOVUSQD X8, K2, 15(DX)                           // 62727e0a15820f000000
	VPMOVUSQD Y12, K2, X8                              // 62527e2a15e0
	VPMOVUSQD Y12, K2, (SI)                            // 62727e2a1526
	VPMOVUSQD Y12, K2, 7(SI)(DI*2)                     // 62727e2a15a47e07000000
	VPMOVUSQD Z6, K3, Y22                              // 62b27e4b15f6
	VPMOVUSQD Z14, K3, Y22                             // 62327e4b15f6
	VPMOVUSQD Z6, K3, (BX)                             // 62f27e4b1533
	VPMOVUSQD Z14, K3, (BX)                            // 62727e4b1533
	VPMOVUSQD Z6, K3, -17(BP)(SI*1)                    // 62f27e4b15b435efffffff
	VPMOVUSQD Z14, K3, -17(BP)(SI*1)                   // 62727e4b15b435efffffff
	VPMOVUSQW X0, K3, X6                               // 62f27e0b14c6
	VPMOVUSQW X0, K3, 7(AX)                            // 62f27e0b148007000000
	VPMOVUSQW X0, K3, (DI)                             // 62f27e0b1407
	VPMOVUSQW Y3, K3, X11                              // 62d27e2b14db
	VPMOVUSQW Y3, K3, (CX)                             // 62f27e2b1419
	VPMOVUSQW Y3, K3, 99(R15)                          // 62d27e2b149f63000000
	VPMOVUSQW Z26, K2, X16                             // 62227e4a14d0
	VPMOVUSQW Z14, K2, X16                             // 62327e4a14f0
	VPMOVUSQW Z26, K2, 17(SP)(BP*8)                    // 62627e4a1494ec11000000
	VPMOVUSQW Z14, K2, 17(SP)(BP*8)                    // 62727e4a14b4ec11000000
	VPMOVUSQW Z26, K2, 17(SP)(BP*4)                    // 62627e4a1494ac11000000
	VPMOVUSQW Z14, K2, 17(SP)(BP*4)                    // 62727e4a14b4ac11000000
	VPMOVZXBD X15, K1, Z3                              // 62d27d4931df or 62d2fd4931df
	VPMOVZXBD 7(AX), K1, Z3                            // 62f27d49319807000000 or 62f2fd49319807000000
	VPMOVZXBD (DI), K1, Z3                             // 62f27d49311f or 62f2fd49311f
	VPMOVZXBD X15, K1, Z0                              // 62d27d4931c7 or 62d2fd4931c7
	VPMOVZXBD 7(AX), K1, Z0                            // 62f27d49318007000000 or 62f2fd49318007000000
	VPMOVZXBD (DI), K1, Z0                             // 62f27d493107 or 62f2fd493107
	VPMOVZXBD X1, K7, X11                              // 62727d0f31d9 or 6272fd0f31d9
	VPMOVZXBD 99(R15)(R15*1), K7, X11                  // 62127d0f319c3f63000000 or 6212fd0f319c3f63000000
	VPMOVZXBD (DX), K7, X11                            // 62727d0f311a or 6272fd0f311a
	VPMOVZXBD X19, K2, Y17                             // 62a27d2a31cb or 62a2fd2a31cb
	VPMOVZXBD 15(DX)(BX*1), K2, Y17                    // 62e27d2a318c1a0f000000 or 62e2fd2a318c1a0f000000
	VPMOVZXBD -7(CX)(DX*2), K2, Y17                    // 62e27d2a318c51f9ffffff or 62e2fd2a318c51f9ffffff
	VPMOVZXBQ X2, K4, X13                              // 62727d0c32ea or 6272fd0c32ea
	VPMOVZXBQ (BX), K4, X13                            // 62727d0c322b or 6272fd0c322b
	VPMOVZXBQ -17(BP)(SI*1), K4, X13                   // 62727d0c32ac35efffffff or 6272fd0c32ac35efffffff
	VPMOVZXBQ X14, K1, Y13                             // 62527d2932ee or 6252fd2932ee
	VPMOVZXBQ -17(BP)(SI*8), K1, Y13                   // 62727d2932acf5efffffff or 6272fd2932acf5efffffff
	VPMOVZXBQ (R15), K1, Y13                           // 62527d29322f or 6252fd29322f
	VPMOVZXBQ X0, K3, Z21                              // 62e27d4b32e8 or 62e2fd4b32e8
	VPMOVZXBQ -17(BP), K3, Z21                         // 62e27d4b32adefffffff or 62e2fd4b32adefffffff
	VPMOVZXBQ -15(R14)(R15*8), K3, Z21                 // 62827d4b32acfef1ffffff or 6282fd4b32acfef1ffffff
	VPMOVZXBQ X0, K3, Z13                              // 62727d4b32e8 or 6272fd4b32e8
	VPMOVZXBQ -17(BP), K3, Z13                         // 62727d4b32adefffffff or 6272fd4b32adefffffff
	VPMOVZXBQ -15(R14)(R15*8), K3, Z13                 // 62127d4b32acfef1ffffff or 6212fd4b32acfef1ffffff
	VPMOVZXDQ X17, K7, Y30                             // 62227d2f35f1
	VPMOVZXDQ -17(BP)(SI*8), K7, Y30                   // 62627d2f35b4f5efffffff
	VPMOVZXDQ (R15), K7, Y30                           // 62427d2f3537
	VPMOVZXDQ X11, K6, X18                             // 62c27d0e35d3
	VPMOVZXDQ 15(R8), K6, X18                          // 62c27d0e35900f000000
	VPMOVZXDQ (BP), K6, X18                            // 62e27d0e355500
	VPMOVZXDQ Y13, K3, Z3                              // 62d27d4b35dd
	VPMOVZXDQ -17(BP)(SI*2), K3, Z3                    // 62f27d4b359c75efffffff
	VPMOVZXDQ 7(AX)(CX*2), K3, Z3                      // 62f27d4b359c4807000000
	VPMOVZXDQ Y13, K3, Z12                             // 62527d4b35e5
	VPMOVZXDQ -17(BP)(SI*2), K3, Z12                   // 62727d4b35a475efffffff
	VPMOVZXDQ 7(AX)(CX*2), K3, Z12                     // 62727d4b35a44807000000
	VPMOVZXWD X9, K7, Y18                              // 62c27d2f33d1 or 62c2fd2f33d1
	VPMOVZXWD 7(SI)(DI*8), K7, Y18                     // 62e27d2f3394fe07000000 or 62e2fd2f3394fe07000000
	VPMOVZXWD -15(R14), K7, Y18                        // 62c27d2f3396f1ffffff or 62c2fd2f3396f1ffffff
	VPMOVZXWD X24, K4, X2                              // 62927d0c33d0 or 6292fd0c33d0
	VPMOVZXWD 15(R8)(R14*8), K4, X2                    // 62927d0c3394f00f000000 or 6292fd0c3394f00f000000
	VPMOVZXWD -15(R14)(R15*2), K4, X2                  // 62927d0c33947ef1ffffff or 6292fd0c33947ef1ffffff
	VPMOVZXWD Y24, K4, Z27                             // 62027d4c33d8 or 6202fd4c33d8
	VPMOVZXWD 15(R8)(R14*1), K4, Z27                   // 62027d4c339c300f000000 or 6202fd4c339c300f000000
	VPMOVZXWD 15(R8)(R14*2), K4, Z27                   // 62027d4c339c700f000000 or 6202fd4c339c700f000000
	VPMOVZXWD Y24, K4, Z15                             // 62127d4c33f8 or 6212fd4c33f8
	VPMOVZXWD 15(R8)(R14*1), K4, Z15                   // 62127d4c33bc300f000000 or 6212fd4c33bc300f000000
	VPMOVZXWD 15(R8)(R14*2), K4, Z15                   // 62127d4c33bc700f000000 or 6212fd4c33bc700f000000
	VPMOVZXWQ X2, K7, Z23                              // 62e27d4f34fa or 62e2fd4f34fa
	VPMOVZXWQ 7(SI)(DI*1), K7, Z23                     // 62e27d4f34bc3e07000000 or 62e2fd4f34bc3e07000000
	VPMOVZXWQ 15(DX)(BX*8), K7, Z23                    // 62e27d4f34bcda0f000000 or 62e2fd4f34bcda0f000000
	VPMOVZXWQ X2, K7, Z5                               // 62f27d4f34ea or 62f2fd4f34ea
	VPMOVZXWQ 7(SI)(DI*1), K7, Z5                      // 62f27d4f34ac3e07000000 or 62f2fd4f34ac3e07000000
	VPMOVZXWQ 15(DX)(BX*8), K7, Z5                     // 62f27d4f34acda0f000000 or 62f2fd4f34acda0f000000
	VPMOVZXWQ X27, K2, X2                              // 62927d0a34d3 or 6292fd0a34d3
	VPMOVZXWQ 7(SI)(DI*8), K2, X2                      // 62f27d0a3494fe07000000 or 62f2fd0a3494fe07000000
	VPMOVZXWQ -15(R14), K2, X2                         // 62d27d0a3496f1ffffff or 62d2fd0a3496f1ffffff
	VPMOVZXWQ X26, K5, Y8                              // 62127d2d34c2 or 6212fd2d34c2
	VPMOVZXWQ -15(R14)(R15*1), K5, Y8                  // 62127d2d34843ef1ffffff or 6212fd2d34843ef1ffffff
	VPMOVZXWQ -15(BX), K5, Y8                          // 62727d2d3483f1ffffff or 6272fd2d3483f1ffffff
	VPMULDQ X3, X30, K3, X22                           // 62e28d0328f3
	VPMULDQ -7(DI)(R8*1), X30, K3, X22                 // 62a28d0328b407f9ffffff
	VPMULDQ (SP), X30, K3, X22                         // 62e28d03283424
	VPMULDQ Y5, Y24, K4, Y11                           // 6272bd2428dd
	VPMULDQ (R14), Y24, K4, Y11                        // 6252bd24281e
	VPMULDQ -7(DI)(R8*8), Y24, K4, Y11                 // 6232bd24289cc7f9ffffff
	VPMULDQ Z21, Z8, K2, Z23                           // 62a2bd4a28fd
	VPMULDQ Z5, Z8, K2, Z23                            // 62e2bd4a28fd
	VPMULDQ -7(CX)(DX*1), Z8, K2, Z23                  // 62e2bd4a28bc11f9ffffff
	VPMULDQ -15(R14)(R15*4), Z8, K2, Z23               // 6282bd4a28bcbef1ffffff
	VPMULDQ Z21, Z28, K2, Z23                          // 62a29d4228fd
	VPMULDQ Z5, Z28, K2, Z23                           // 62e29d4228fd
	VPMULDQ -7(CX)(DX*1), Z28, K2, Z23                 // 62e29d4228bc11f9ffffff
	VPMULDQ -15(R14)(R15*4), Z28, K2, Z23              // 62829d4228bcbef1ffffff
	VPMULDQ Z21, Z8, K2, Z6                            // 62b2bd4a28f5
	VPMULDQ Z5, Z8, K2, Z6                             // 62f2bd4a28f5
	VPMULDQ -7(CX)(DX*1), Z8, K2, Z6                   // 62f2bd4a28b411f9ffffff
	VPMULDQ -15(R14)(R15*4), Z8, K2, Z6                // 6292bd4a28b4bef1ffffff
	VPMULDQ Z21, Z28, K2, Z6                           // 62b29d4228f5
	VPMULDQ Z5, Z28, K2, Z6                            // 62f29d4228f5
	VPMULDQ -7(CX)(DX*1), Z28, K2, Z6                  // 62f29d4228b411f9ffffff
	VPMULDQ -15(R14)(R15*4), Z28, K2, Z6               // 62929d4228b4bef1ffffff
	VPMULLD X9, X2, K1, X20                            // 62c26d0940e1
	VPMULLD (BX), X2, K1, X20                          // 62e26d094023
	VPMULLD -17(BP)(SI*1), X2, K1, X20                 // 62e26d0940a435efffffff
	VPMULLD Y11, Y26, K1, Y6                           // 62d22d2140f3
	VPMULLD -7(CX)(DX*1), Y26, K1, Y6                  // 62f22d2140b411f9ffffff
	VPMULLD -15(R14)(R15*4), Y26, K1, Y6               // 62922d2140b4bef1ffffff
	VPMULLD Z7, Z3, K1, Z8                             // 6272654940c7
	VPMULLD Z9, Z3, K1, Z8                             // 6252654940c1
	VPMULLD 15(R8), Z3, K1, Z8                         // 6252654940800f000000
	VPMULLD (BP), Z3, K1, Z8                           // 62726549404500
	VPMULLD Z7, Z27, K1, Z8                            // 6272254140c7
	VPMULLD Z9, Z27, K1, Z8                            // 6252254140c1
	VPMULLD 15(R8), Z27, K1, Z8                        // 6252254140800f000000
	VPMULLD (BP), Z27, K1, Z8                          // 62722541404500
	VPMULLD Z7, Z3, K1, Z2                             // 62f2654940d7
	VPMULLD Z9, Z3, K1, Z2                             // 62d2654940d1
	VPMULLD 15(R8), Z3, K1, Z2                         // 62d2654940900f000000
	VPMULLD (BP), Z3, K1, Z2                           // 62f26549405500
	VPMULLD Z7, Z27, K1, Z2                            // 62f2254140d7
	VPMULLD Z9, Z27, K1, Z2                            // 62d2254140d1
	VPMULLD 15(R8), Z27, K1, Z2                        // 62d2254140900f000000
	VPMULLD (BP), Z27, K1, Z2                          // 62f22541405500
	VPMULUDQ X16, X0, K6, X15                          // 6231fd0ef4f8
	VPMULUDQ -17(BP)(SI*2), X0, K6, X15                // 6271fd0ef4bc75efffffff
	VPMULUDQ 7(AX)(CX*2), X0, K6, X15                  // 6271fd0ef4bc4807000000
	VPMULUDQ Y14, Y21, K3, Y7                          // 62d1d523f4fe
	VPMULUDQ 15(R8), Y21, K3, Y7                       // 62d1d523f4b80f000000
	VPMULUDQ (BP), Y21, K3, Y7                         // 62f1d523f47d00
	VPMULUDQ Z1, Z6, K7, Z6                            // 62f1cd4ff4f1
	VPMULUDQ Z15, Z6, K7, Z6                           // 62d1cd4ff4f7
	VPMULUDQ (SI), Z6, K7, Z6                          // 62f1cd4ff436
	VPMULUDQ 7(SI)(DI*2), Z6, K7, Z6                   // 62f1cd4ff4b47e07000000
	VPMULUDQ Z1, Z22, K7, Z6                           // 62f1cd47f4f1
	VPMULUDQ Z15, Z22, K7, Z6                          // 62d1cd47f4f7
	VPMULUDQ (SI), Z22, K7, Z6                         // 62f1cd47f436
	VPMULUDQ 7(SI)(DI*2), Z22, K7, Z6                  // 62f1cd47f4b47e07000000
	VPMULUDQ Z1, Z6, K7, Z16                           // 62e1cd4ff4c1
	VPMULUDQ Z15, Z6, K7, Z16                          // 62c1cd4ff4c7
	VPMULUDQ (SI), Z6, K7, Z16                         // 62e1cd4ff406
	VPMULUDQ 7(SI)(DI*2), Z6, K7, Z16                  // 62e1cd4ff4847e07000000
	VPMULUDQ Z1, Z22, K7, Z16                          // 62e1cd47f4c1
	VPMULUDQ Z15, Z22, K7, Z16                         // 62c1cd47f4c7
	VPMULUDQ (SI), Z22, K7, Z16                        // 62e1cd47f406
	VPMULUDQ 7(SI)(DI*2), Z22, K7, Z16                 // 62e1cd47f4847e07000000
	VPORD X7, X1, K2, X31                              // 6261750aebff
	VPORD 99(R15)(R15*2), X1, K2, X31                  // 6201750aebbc7f63000000
	VPORD -7(DI), X1, K2, X31                          // 6261750aebbff9ffffff
	VPORD Y28, Y9, K1, Y20                             // 62813529ebe4
	VPORD 17(SP)(BP*8), Y9, K1, Y20                    // 62e13529eba4ec11000000
	VPORD 17(SP)(BP*4), Y9, K1, Y20                    // 62e13529eba4ac11000000
	VPORD Z15, Z3, K2, Z14                             // 6251654aebf7
	VPORD Z30, Z3, K2, Z14                             // 6211654aebf6
	VPORD 99(R15)(R15*1), Z3, K2, Z14                  // 6211654aebb43f63000000
	VPORD (DX), Z3, K2, Z14                            // 6271654aeb32
	VPORD Z15, Z12, K2, Z14                            // 62511d4aebf7
	VPORD Z30, Z12, K2, Z14                            // 62111d4aebf6
	VPORD 99(R15)(R15*1), Z12, K2, Z14                 // 62111d4aebb43f63000000
	VPORD (DX), Z12, K2, Z14                           // 62711d4aeb32
	VPORD Z15, Z3, K2, Z28                             // 6241654aebe7
	VPORD Z30, Z3, K2, Z28                             // 6201654aebe6
	VPORD 99(R15)(R15*1), Z3, K2, Z28                  // 6201654aeba43f63000000
	VPORD (DX), Z3, K2, Z28                            // 6261654aeb22
	VPORD Z15, Z12, K2, Z28                            // 62411d4aebe7
	VPORD Z30, Z12, K2, Z28                            // 62011d4aebe6
	VPORD 99(R15)(R15*1), Z12, K2, Z28                 // 62011d4aeba43f63000000
	VPORD (DX), Z12, K2, Z28                           // 62611d4aeb22
	VPORQ X12, X15, K1, X9                             // 62518509ebcc
	VPORQ -7(CX)(DX*1), X15, K1, X9                    // 62718509eb8c11f9ffffff
	VPORQ -15(R14)(R15*4), X15, K1, X9                 // 62118509eb8cbef1ffffff
	VPORQ Y8, Y1, K7, Y28                              // 6241f52febe0
	VPORQ 7(SI)(DI*4), Y1, K7, Y28                     // 6261f52feba4be07000000
	VPORQ -7(DI)(R8*2), Y1, K7, Y28                    // 6221f52feba447f9ffffff
	VPORQ Z3, Z5, K1, Z19                              // 62e1d549ebdb
	VPORQ Z5, Z5, K1, Z19                              // 62e1d549ebdd
	VPORQ -17(BP)(SI*8), Z5, K1, Z19                   // 62e1d549eb9cf5efffffff
	VPORQ (R15), Z5, K1, Z19                           // 62c1d549eb1f
	VPORQ Z3, Z1, K1, Z19                              // 62e1f549ebdb
	VPORQ Z5, Z1, K1, Z19                              // 62e1f549ebdd
	VPORQ -17(BP)(SI*8), Z1, K1, Z19                   // 62e1f549eb9cf5efffffff
	VPORQ (R15), Z1, K1, Z19                           // 62c1f549eb1f
	VPORQ Z3, Z5, K1, Z15                              // 6271d549ebfb
	VPORQ Z5, Z5, K1, Z15                              // 6271d549ebfd
	VPORQ -17(BP)(SI*8), Z5, K1, Z15                   // 6271d549ebbcf5efffffff
	VPORQ (R15), Z5, K1, Z15                           // 6251d549eb3f
	VPORQ Z3, Z1, K1, Z15                              // 6271f549ebfb
	VPORQ Z5, Z1, K1, Z15                              // 6271f549ebfd
	VPORQ -17(BP)(SI*8), Z1, K1, Z15                   // 6271f549ebbcf5efffffff
	VPORQ (R15), Z1, K1, Z15                           // 6251f549eb3f
	VPROLD $121, X12, K1, X0                           // 62d17d0972cc79
	VPROLD $121, 15(DX)(BX*1), K1, X0                  // 62f17d09728c1a0f00000079
	VPROLD $121, -7(CX)(DX*2), K1, X0                  // 62f17d09728c51f9ffffff79
	VPROLD $13, Y27, K1, Y11                           // 6291252972cb0d
	VPROLD $13, 17(SP), K1, Y11                        // 62f12529728c24110000000d
	VPROLD $13, -17(BP)(SI*4), K1, Y11                 // 62f12529728cb5efffffff0d
	VPROLD $65, Z21, K7, Z14                           // 62b10d4f72cd41
	VPROLD $65, Z8, K7, Z14                            // 62d10d4f72c841
	VPROLD $65, 7(SI)(DI*8), K7, Z14                   // 62f10d4f728cfe0700000041
	VPROLD $65, -15(R14), K7, Z14                      // 62d10d4f728ef1ffffff41
	VPROLD $65, Z21, K7, Z15                           // 62b1054f72cd41
	VPROLD $65, Z8, K7, Z15                            // 62d1054f72c841
	VPROLD $65, 7(SI)(DI*8), K7, Z15                   // 62f1054f728cfe0700000041
	VPROLD $65, -15(R14), K7, Z15                      // 62d1054f728ef1ffffff41
	VPROLQ $67, X5, K2, X14                            // 62f18d0a72cd43
	VPROLQ $67, -17(BP), K2, X14                       // 62f18d0a728defffffff43
	VPROLQ $67, -15(R14)(R15*8), K2, X14               // 62918d0a728cfef1ffffff43
	VPROLQ $127, Y16, K4, Y17                          // 62b1f52472c87f
	VPROLQ $127, 7(AX), K4, Y17                        // 62f1f5247288070000007f
	VPROLQ $127, (DI), K4, Y17                         // 62f1f524720f7f
	VPROLQ $0, Z20, K1, Z16                            // 62b1fd4172cc00
	VPROLQ $0, Z0, K1, Z16                             // 62f1fd4172c800
	VPROLQ $0, 7(SI)(DI*1), K1, Z16                    // 62f1fd41728c3e0700000000
	VPROLQ $0, 15(DX)(BX*8), K1, Z16                   // 62f1fd41728cda0f00000000
	VPROLQ $0, Z20, K1, Z9                             // 62b1b54972cc00
	VPROLQ $0, Z0, K1, Z9                              // 62f1b54972c800
	VPROLQ $0, 7(SI)(DI*1), K1, Z9                     // 62f1b549728c3e0700000000
	VPROLQ $0, 15(DX)(BX*8), K1, Z9                    // 62f1b549728cda0f00000000
	VPROLVD X8, X15, K3, X17                           // 62c2050b15c8
	VPROLVD 17(SP)(BP*2), X15, K3, X17                 // 62e2050b158c6c11000000
	VPROLVD -7(DI)(R8*4), X15, K3, X17                 // 62a2050b158c87f9ffffff
	VPROLVD Y26, Y6, K4, Y12                           // 62124d2c15e2
	VPROLVD 99(R15)(R15*1), Y6, K4, Y12                // 62124d2c15a43f63000000
	VPROLVD (DX), Y6, K4, Y12                          // 62724d2c1522
	VPROLVD Z0, Z0, K5, Z23                            // 62e27d4d15f8
	VPROLVD Z25, Z0, K5, Z23                           // 62827d4d15f9
	VPROLVD -7(DI)(R8*1), Z0, K5, Z23                  // 62a27d4d15bc07f9ffffff
	VPROLVD (SP), Z0, K5, Z23                          // 62e27d4d153c24
	VPROLVD Z0, Z11, K5, Z23                           // 62e2254d15f8
	VPROLVD Z25, Z11, K5, Z23                          // 6282254d15f9
	VPROLVD -7(DI)(R8*1), Z11, K5, Z23                 // 62a2254d15bc07f9ffffff
	VPROLVD (SP), Z11, K5, Z23                         // 62e2254d153c24
	VPROLVD Z0, Z0, K5, Z19                            // 62e27d4d15d8
	VPROLVD Z25, Z0, K5, Z19                           // 62827d4d15d9
	VPROLVD -7(DI)(R8*1), Z0, K5, Z19                  // 62a27d4d159c07f9ffffff
	VPROLVD (SP), Z0, K5, Z19                          // 62e27d4d151c24
	VPROLVD Z0, Z11, K5, Z19                           // 62e2254d15d8
	VPROLVD Z25, Z11, K5, Z19                          // 6282254d15d9
	VPROLVD -7(DI)(R8*1), Z11, K5, Z19                 // 62a2254d159c07f9ffffff
	VPROLVD (SP), Z11, K5, Z19                         // 62e2254d151c24
	VPROLVQ X23, X26, K7, X3                           // 62b2ad0715df
	VPROLVQ 15(R8), X26, K7, X3                        // 62d2ad0715980f000000
	VPROLVQ (BP), X26, K7, X3                          // 62f2ad07155d00
	VPROLVQ Y28, Y8, K7, Y3                            // 6292bd2f15dc
	VPROLVQ -17(BP)(SI*8), Y8, K7, Y3                  // 62f2bd2f159cf5efffffff
	VPROLVQ (R15), Y8, K7, Y3                          // 62d2bd2f151f
	VPROLVQ Z9, Z0, K6, Z24                            // 6242fd4e15c1
	VPROLVQ Z3, Z0, K6, Z24                            // 6262fd4e15c3
	VPROLVQ -7(CX), Z0, K6, Z24                        // 6262fd4e1581f9ffffff
	VPROLVQ 15(DX)(BX*4), Z0, K6, Z24                  // 6262fd4e15849a0f000000
	VPROLVQ Z9, Z26, K6, Z24                           // 6242ad4615c1
	VPROLVQ Z3, Z26, K6, Z24                           // 6262ad4615c3
	VPROLVQ -7(CX), Z26, K6, Z24                       // 6262ad461581f9ffffff
	VPROLVQ 15(DX)(BX*4), Z26, K6, Z24                 // 6262ad4615849a0f000000
	VPROLVQ Z9, Z0, K6, Z12                            // 6252fd4e15e1
	VPROLVQ Z3, Z0, K6, Z12                            // 6272fd4e15e3
	VPROLVQ -7(CX), Z0, K6, Z12                        // 6272fd4e15a1f9ffffff
	VPROLVQ 15(DX)(BX*4), Z0, K6, Z12                  // 6272fd4e15a49a0f000000
	VPROLVQ Z9, Z26, K6, Z12                           // 6252ad4615e1
	VPROLVQ Z3, Z26, K6, Z12                           // 6272ad4615e3
	VPROLVQ -7(CX), Z26, K6, Z12                       // 6272ad4615a1f9ffffff
	VPROLVQ 15(DX)(BX*4), Z26, K6, Z12                 // 6272ad4615a49a0f000000
	VPRORD $97, X28, K3, X13                           // 6291150b72c461
	VPRORD $97, 15(R8)(R14*8), K3, X13                 // 6291150b7284f00f00000061
	VPRORD $97, -15(R14)(R15*2), K3, X13               // 6291150b72847ef1ffffff61
	VPRORD $81, Y23, K7, Y1                            // 62b1752f72c751
	VPRORD $81, 7(SI)(DI*8), K7, Y1                    // 62f1752f7284fe0700000051
	VPRORD $81, -15(R14), K7, Y1                       // 62d1752f7286f1ffffff51
	VPRORD $42, Z9, K4, Z9                             // 62d1354c72c12a
	VPRORD $42, Z28, K4, Z9                            // 6291354c72c42a
	VPRORD $42, 99(R15)(R15*8), K4, Z9                 // 6291354c7284ff630000002a
	VPRORD $42, 7(AX)(CX*8), K4, Z9                    // 62f1354c7284c8070000002a
	VPRORD $42, Z9, K4, Z25                            // 62d1354472c12a
	VPRORD $42, Z28, K4, Z25                           // 6291354472c42a
	VPRORD $42, 99(R15)(R15*8), K4, Z25                // 629135447284ff630000002a
	VPRORD $42, 7(AX)(CX*8), K4, Z25                   // 62f135447284c8070000002a
	VPRORQ $79, X9, K4, X24                            // 62d1bd0472c14f
	VPRORQ $79, -15(R14)(R15*1), K4, X24               // 6291bd0472843ef1ffffff4f
	VPRORQ $79, -15(BX), K4, X24                       // 62f1bd047283f1ffffff4f
	VPRORQ $64, Y31, K7, Y14                           // 62918d2f72c740
	VPRORQ $64, 7(SI)(DI*1), K7, Y14                   // 62f18d2f72843e0700000040
	VPRORQ $64, 15(DX)(BX*8), K7, Y14                  // 62f18d2f7284da0f00000040
	VPRORQ $27, Z17, K2, Z20                           // 62b1dd4272c11b
	VPRORQ $27, Z0, K2, Z20                            // 62f1dd4272c01b
	VPRORQ $27, (AX), K2, Z20                          // 62f1dd4272001b
	VPRORQ $27, 7(SI), K2, Z20                         // 62f1dd427286070000001b
	VPRORQ $27, Z17, K2, Z0                            // 62b1fd4a72c11b
	VPRORQ $27, Z0, K2, Z0                             // 62f1fd4a72c01b
	VPRORQ $27, (AX), K2, Z0                           // 62f1fd4a72001b
	VPRORQ $27, 7(SI), K2, Z0                          // 62f1fd4a7286070000001b
	VPRORVD X18, X26, K5, X15                          // 62322d0514fa
	VPRORVD 7(AX)(CX*4), X26, K5, X15                  // 62722d0514bc8807000000
	VPRORVD 7(AX)(CX*1), X26, K5, X15                  // 62722d0514bc0807000000
	VPRORVD Y22, Y2, K3, Y25                           // 62226d2b14ce
	VPRORVD -7(DI)(R8*1), Y2, K3, Y25                  // 62226d2b148c07f9ffffff
	VPRORVD (SP), Y2, K3, Y25                          // 62626d2b140c24
	VPRORVD Z21, Z31, K4, Z17                          // 62a2054414cd
	VPRORVD Z9, Z31, K4, Z17                           // 62c2054414c9
	VPRORVD (BX), Z31, K4, Z17                         // 62e20544140b
	VPRORVD -17(BP)(SI*1), Z31, K4, Z17                // 62e20544148c35efffffff
	VPRORVD Z21, Z0, K4, Z17                           // 62a27d4c14cd
	VPRORVD Z9, Z0, K4, Z17                            // 62c27d4c14c9
	VPRORVD (BX), Z0, K4, Z17                          // 62e27d4c140b
	VPRORVD -17(BP)(SI*1), Z0, K4, Z17                 // 62e27d4c148c35efffffff
	VPRORVD Z21, Z31, K4, Z23                          // 62a2054414fd
	VPRORVD Z9, Z31, K4, Z23                           // 62c2054414f9
	VPRORVD (BX), Z31, K4, Z23                         // 62e20544143b
	VPRORVD -17(BP)(SI*1), Z31, K4, Z23                // 62e2054414bc35efffffff
	VPRORVD Z21, Z0, K4, Z23                           // 62a27d4c14fd
	VPRORVD Z9, Z0, K4, Z23                            // 62c27d4c14f9
	VPRORVD (BX), Z0, K4, Z23                          // 62e27d4c143b
	VPRORVD -17(BP)(SI*1), Z0, K4, Z23                 // 62e27d4c14bc35efffffff
	VPRORVQ X11, X1, K2, X21                           // 62c2f50a14eb
	VPRORVQ (SI), X1, K2, X21                          // 62e2f50a142e
	VPRORVQ 7(SI)(DI*2), X1, K2, X21                   // 62e2f50a14ac7e07000000
	VPRORVQ Y9, Y8, K2, Y27                            // 6242bd2a14d9
	VPRORVQ -7(CX), Y8, K2, Y27                        // 6262bd2a1499f9ffffff
	VPRORVQ 15(DX)(BX*4), Y8, K2, Y27                  // 6262bd2a149c9a0f000000
	VPRORVQ Z20, Z1, K3, Z6                            // 62b2f54b14f4
	VPRORVQ Z9, Z1, K3, Z6                             // 62d2f54b14f1
	VPRORVQ 15(R8)(R14*4), Z1, K3, Z6                  // 6292f54b14b4b00f000000
	VPRORVQ -7(CX)(DX*4), Z1, K3, Z6                   // 62f2f54b14b491f9ffffff
	VPRORVQ Z20, Z9, K3, Z6                            // 62b2b54b14f4
	VPRORVQ Z9, Z9, K3, Z6                             // 62d2b54b14f1
	VPRORVQ 15(R8)(R14*4), Z9, K3, Z6                  // 6292b54b14b4b00f000000
	VPRORVQ -7(CX)(DX*4), Z9, K3, Z6                   // 62f2b54b14b491f9ffffff
	VPRORVQ Z20, Z1, K3, Z9                            // 6232f54b14cc
	VPRORVQ Z9, Z1, K3, Z9                             // 6252f54b14c9
	VPRORVQ 15(R8)(R14*4), Z1, K3, Z9                  // 6212f54b148cb00f000000
	VPRORVQ -7(CX)(DX*4), Z1, K3, Z9                   // 6272f54b148c91f9ffffff
	VPRORVQ Z20, Z9, K3, Z9                            // 6232b54b14cc
	VPRORVQ Z9, Z9, K3, Z9                             // 6252b54b14c9
	VPRORVQ 15(R8)(R14*4), Z9, K3, Z9                  // 6212b54b148cb00f000000
	VPRORVQ -7(CX)(DX*4), Z9, K3, Z9                   // 6272b54b148c91f9ffffff
	VPSCATTERDD X0, K3, (AX)(X4*1)                     // 62f27d0ba00420
	VPSCATTERDD X0, K3, (BP)(X10*2)                    // 62b27d0ba0445500
	VPSCATTERDD X0, K3, (R10)(X29*8)                   // 62927d03a004ea
	VPSCATTERDD Y1, K3, (R10)(Y29*8)                   // 62927d23a00cea
	VPSCATTERDD Y1, K3, (SP)(Y4*2)                     // 62f27d2ba00c64
	VPSCATTERDD Y1, K3, (DX)(Y10*4)                    // 62b27d2ba00c92
	VPSCATTERDD Z16, K2, (DX)(Z10*4)                   // 62a27d4aa00492
	VPSCATTERDD Z25, K2, (DX)(Z10*4)                   // 62227d4aa00c92
	VPSCATTERDD Z16, K2, (AX)(Z4*1)                    // 62e27d4aa00420
	VPSCATTERDD Z25, K2, (AX)(Z4*1)                    // 62627d4aa00c20
	VPSCATTERDD Z16, K2, (SP)(Z4*2)                    // 62e27d4aa00464
	VPSCATTERDD Z25, K2, (SP)(Z4*2)                    // 62627d4aa00c64
	VPSCATTERDQ X0, K1, (DX)(X10*4)                    // 62b2fd09a00492
	VPSCATTERDQ X0, K1, (SP)(X4*2)                     // 62f2fd09a00464
	VPSCATTERDQ X0, K1, (R14)(X29*8)                   // 6292fd01a004ee
	VPSCATTERDQ Y6, K2, (AX)(X4*1)                     // 62f2fd2aa03420
	VPSCATTERDQ Y6, K2, (BP)(X10*2)                    // 62b2fd2aa0745500
	VPSCATTERDQ Y6, K2, (R10)(X29*8)                   // 6292fd22a034ea
	VPSCATTERDQ Z14, K1, (R14)(Y29*8)                  // 6212fd41a034ee
	VPSCATTERDQ Z13, K1, (R14)(Y29*8)                  // 6212fd41a02cee
	VPSCATTERDQ Z14, K1, (AX)(Y4*1)                    // 6272fd49a03420
	VPSCATTERDQ Z13, K1, (AX)(Y4*1)                    // 6272fd49a02c20
	VPSCATTERDQ Z14, K1, (BP)(Y10*2)                   // 6232fd49a0745500
	VPSCATTERDQ Z13, K1, (BP)(Y10*2)                   // 6232fd49a06c5500
	VPSCATTERQD X24, K7, (AX)(X4*1)                    // 62627d0fa10420
	VPSCATTERQD X24, K7, (BP)(X10*2)                   // 62227d0fa1445500
	VPSCATTERQD X24, K7, (R10)(X29*8)                  // 62027d07a104ea
	VPSCATTERQD X20, K1, (R10)(Y29*8)                  // 62827d21a124ea
	VPSCATTERQD X20, K1, (SP)(Y4*2)                    // 62e27d29a12464
	VPSCATTERQD X20, K1, (DX)(Y10*4)                   // 62a27d29a12492
	VPSCATTERQD Y1, K1, (DX)(Z10*4)                    // 62b27d49a10c92
	VPSCATTERQD Y1, K1, (AX)(Z4*1)                     // 62f27d49a10c20
	VPSCATTERQD Y1, K1, (SP)(Z4*2)                     // 62f27d49a10c64
	VPSCATTERQQ X7, K1, (DX)(X10*4)                    // 62b2fd09a13c92
	VPSCATTERQQ X7, K1, (SP)(X4*2)                     // 62f2fd09a13c64
	VPSCATTERQQ X7, K1, (R14)(X29*8)                   // 6292fd01a13cee
	VPSCATTERQQ Y9, K7, (R14)(Y29*8)                   // 6212fd27a10cee
	VPSCATTERQQ Y9, K7, (AX)(Y4*1)                     // 6272fd2fa10c20
	VPSCATTERQQ Y9, K7, (BP)(Y10*2)                    // 6232fd2fa14c5500
	VPSCATTERQQ Z12, K2, (BP)(Z10*2)                   // 6232fd4aa1645500
	VPSCATTERQQ Z13, K2, (BP)(Z10*2)                   // 6232fd4aa16c5500
	VPSCATTERQQ Z12, K2, (R10)(Z29*8)                  // 6212fd42a124ea
	VPSCATTERQQ Z13, K2, (R10)(Z29*8)                  // 6212fd42a12cea
	VPSCATTERQQ Z12, K2, (R14)(Z29*8)                  // 6212fd42a124ee
	VPSCATTERQQ Z13, K2, (R14)(Z29*8)                  // 6212fd42a12cee
	VPSHUFD $126, X2, K4, X9                           // 62717d0c70ca7e
	VPSHUFD $126, 17(SP)(BP*1), K4, X9                 // 62717d0c708c2c110000007e
	VPSHUFD $126, -7(CX)(DX*8), K4, X9                 // 62717d0c708cd1f9ffffff7e
	VPSHUFD $94, Y31, K4, Y6                           // 62917d2c70f75e
	VPSHUFD $94, 17(SP)(BP*2), K4, Y6                  // 62f17d2c70b46c110000005e
	VPSHUFD $94, -7(DI)(R8*4), K4, Y6                  // 62b17d2c70b487f9ffffff5e
	VPSHUFD $121, Z3, K7, Z8                           // 62717d4f70c379
	VPSHUFD $121, Z27, K7, Z8                          // 62117d4f70c379
	VPSHUFD $121, 7(AX)(CX*4), K7, Z8                  // 62717d4f7084880700000079
	VPSHUFD $121, 7(AX)(CX*1), K7, Z8                  // 62717d4f7084080700000079
	VPSHUFD $121, Z3, K7, Z2                           // 62f17d4f70d379
	VPSHUFD $121, Z27, K7, Z2                          // 62917d4f70d379
	VPSHUFD $121, 7(AX)(CX*4), K7, Z2                  // 62f17d4f7094880700000079
	VPSHUFD $121, 7(AX)(CX*1), K7, Z2                  // 62f17d4f7094080700000079
	VPSLLD $81, X0, K3, X14                            // 62f10d0b72f051
	VPSLLD $81, (R14), K3, X14                         // 62d10d0b723651
	VPSLLD $81, -7(DI)(R8*8), K3, X14                  // 62b10d0b72b4c7f9ffffff51
	VPSLLD $42, Y0, K3, Y6                             // 62f14d2b72f02a
	VPSLLD $42, -15(R14)(R15*1), K3, Y6                // 62914d2b72b43ef1ffffff2a
	VPSLLD $42, -15(BX), K3, Y6                        // 62f14d2b72b3f1ffffff2a
	VPSLLD $79, Z12, K3, Z9                            // 62d1354b72f44f
	VPSLLD $79, Z22, K3, Z9                            // 62b1354b72f64f
	VPSLLD $79, 7(SI)(DI*4), K3, Z9                    // 62f1354b72b4be070000004f
	VPSLLD $79, -7(DI)(R8*2), K3, Z9                   // 62b1354b72b447f9ffffff4f
	VPSLLD $79, Z12, K3, Z19                           // 62d1654372f44f
	VPSLLD $79, Z22, K3, Z19                           // 62b1654372f64f
	VPSLLD $79, 7(SI)(DI*4), K3, Z19                   // 62f1654372b4be070000004f
	VPSLLD $79, -7(DI)(R8*2), K3, Z19                  // 62b1654372b447f9ffffff4f
	VPSLLD X15, X7, K2, X17                            // 62c1450af2cf
	VPSLLD 99(R15)(R15*4), X7, K2, X17                 // 6281450af28cbf63000000
	VPSLLD 15(DX), X7, K2, X17                         // 62e1450af28a0f000000
	VPSLLD X11, Y5, K1, Y3                             // 62d15529f2db
	VPSLLD (CX), Y5, K1, Y3                            // 62f15529f219
	VPSLLD 99(R15), Y5, K1, Y3                         // 62d15529f29f63000000
	VPSLLD X0, Z18, K2, Z11                            // 62716d42f2d8
	VPSLLD 99(R15)(R15*2), Z18, K2, Z11                // 62116d42f29c7f63000000
	VPSLLD -7(DI), Z18, K2, Z11                        // 62716d42f29ff9ffffff
	VPSLLD X0, Z24, K2, Z11                            // 62713d42f2d8
	VPSLLD 99(R15)(R15*2), Z24, K2, Z11                // 62113d42f29c7f63000000
	VPSLLD -7(DI), Z24, K2, Z11                        // 62713d42f29ff9ffffff
	VPSLLD X0, Z18, K2, Z5                             // 62f16d42f2e8
	VPSLLD 99(R15)(R15*2), Z18, K2, Z5                 // 62916d42f2ac7f63000000
	VPSLLD -7(DI), Z18, K2, Z5                         // 62f16d42f2aff9ffffff
	VPSLLD X0, Z24, K2, Z5                             // 62f13d42f2e8
	VPSLLD 99(R15)(R15*2), Z24, K2, Z5                 // 62913d42f2ac7f63000000
	VPSLLD -7(DI), Z24, K2, Z5                         // 62f13d42f2aff9ffffff
	VPSLLQ $82, X25, K1, X27                           // 6291a50173f152
	VPSLLQ $82, 15(DX)(BX*1), K1, X27                  // 62f1a50173b41a0f00000052
	VPSLLQ $82, -7(CX)(DX*2), K1, X27                  // 62f1a50173b451f9ffffff52
	VPSLLQ $126, Y5, K7, Y3                            // 62f1e52f73f57e
	VPSLLQ $126, (SI), K7, Y3                          // 62f1e52f73367e
	VPSLLQ $126, 7(SI)(DI*2), K7, Y3                   // 62f1e52f73b47e070000007e
	VPSLLQ $94, Z6, K1, Z6                             // 62f1cd4973f65e
	VPSLLQ $94, Z22, K1, Z6                            // 62b1cd4973f65e
	VPSLLQ $94, 7(AX), K1, Z6                          // 62f1cd4973b0070000005e
	VPSLLQ $94, (DI), K1, Z6                           // 62f1cd4973375e
	VPSLLQ $94, Z6, K1, Z16                            // 62f1fd4173f65e
	VPSLLQ $94, Z22, K1, Z16                           // 62b1fd4173f65e
	VPSLLQ $94, 7(AX), K1, Z16                         // 62f1fd4173b0070000005e
	VPSLLQ $94, (DI), K1, Z16                          // 62f1fd4173375e
	VPSLLQ X15, X18, K1, X3                            // 62d1ed01f3df
	VPSLLQ -17(BP), X18, K1, X3                        // 62f1ed01f39defffffff
	VPSLLQ -15(R14)(R15*8), X18, K1, X3                // 6291ed01f39cfef1ffffff
	VPSLLQ X28, Y7, K1, Y28                            // 6201c529f3e4
	VPSLLQ 17(SP)(BP*2), Y7, K1, Y28                   // 6261c529f3a46c11000000
	VPSLLQ -7(DI)(R8*4), Y7, K1, Y28                   // 6221c529f3a487f9ffffff
	VPSLLQ X15, Z13, K7, Z1                            // 62d1954ff3cf
	VPSLLQ 15(R8), Z13, K7, Z1                         // 62d1954ff3880f000000
	VPSLLQ (BP), Z13, K7, Z1                           // 62f1954ff34d00
	VPSLLQ X15, Z13, K7, Z15                           // 6251954ff3ff
	VPSLLQ 15(R8), Z13, K7, Z15                        // 6251954ff3b80f000000
	VPSLLQ (BP), Z13, K7, Z15                          // 6271954ff37d00
	VPSLLVD X8, X13, K2, X7                            // 62d2150a47f8
	VPSLLVD 15(R8)(R14*8), X13, K2, X7                 // 6292150a47bcf00f000000
	VPSLLVD -15(R14)(R15*2), X13, K2, X7               // 6292150a47bc7ef1ffffff
	VPSLLVD Y13, Y22, K4, Y0                           // 62d24d2447c5
	VPSLLVD 17(SP)(BP*8), Y22, K4, Y0                  // 62f24d244784ec11000000
	VPSLLVD 17(SP)(BP*4), Y22, K4, Y0                  // 62f24d244784ac11000000
	VPSLLVD Z2, Z22, K1, Z18                           // 62e24d4147d2
	VPSLLVD Z31, Z22, K1, Z18                          // 62824d4147d7
	VPSLLVD 99(R15)(R15*1), Z22, K1, Z18               // 62824d4147943f63000000
	VPSLLVD (DX), Z22, K1, Z18                         // 62e24d414712
	VPSLLVD Z2, Z7, K1, Z18                            // 62e2454947d2
	VPSLLVD Z31, Z7, K1, Z18                           // 6282454947d7
	VPSLLVD 99(R15)(R15*1), Z7, K1, Z18                // 6282454947943f63000000
	VPSLLVD (DX), Z7, K1, Z18                          // 62e245494712
	VPSLLVD Z2, Z22, K1, Z8                            // 62724d4147c2
	VPSLLVD Z31, Z22, K1, Z8                           // 62124d4147c7
	VPSLLVD 99(R15)(R15*1), Z22, K1, Z8                // 62124d4147843f63000000
	VPSLLVD (DX), Z22, K1, Z8                          // 62724d414702
	VPSLLVD Z2, Z7, K1, Z8                             // 6272454947c2
	VPSLLVD Z31, Z7, K1, Z8                            // 6212454947c7
	VPSLLVD 99(R15)(R15*1), Z7, K1, Z8                 // 6212454947843f63000000
	VPSLLVD (DX), Z7, K1, Z8                           // 627245494702
	VPSLLVQ X0, X7, K3, X24                            // 6262c50b47c0
	VPSLLVQ -15(R14)(R15*1), X7, K3, X24               // 6202c50b47843ef1ffffff
	VPSLLVQ -15(BX), X7, K3, X24                       // 6262c50b4783f1ffffff
	VPSLLVQ Y14, Y1, K4, Y12                           // 6252f52c47e6
	VPSLLVQ 7(SI)(DI*4), Y1, K4, Y12                   // 6272f52c47a4be07000000
	VPSLLVQ -7(DI)(R8*2), Y1, K4, Y12                  // 6232f52c47a447f9ffffff
	VPSLLVQ Z12, Z1, K5, Z20                           // 62c2f54d47e4
	VPSLLVQ Z16, Z1, K5, Z20                           // 62a2f54d47e0
	VPSLLVQ -17(BP)(SI*8), Z1, K5, Z20                 // 62e2f54d47a4f5efffffff
	VPSLLVQ (R15), Z1, K5, Z20                         // 62c2f54d4727
	VPSLLVQ Z12, Z3, K5, Z20                           // 62c2e54d47e4
	VPSLLVQ Z16, Z3, K5, Z20                           // 62a2e54d47e0
	VPSLLVQ -17(BP)(SI*8), Z3, K5, Z20                 // 62e2e54d47a4f5efffffff
	VPSLLVQ (R15), Z3, K5, Z20                         // 62c2e54d4727
	VPSLLVQ Z12, Z1, K5, Z9                            // 6252f54d47cc
	VPSLLVQ Z16, Z1, K5, Z9                            // 6232f54d47c8
	VPSLLVQ -17(BP)(SI*8), Z1, K5, Z9                  // 6272f54d478cf5efffffff
	VPSLLVQ (R15), Z1, K5, Z9                          // 6252f54d470f
	VPSLLVQ Z12, Z3, K5, Z9                            // 6252e54d47cc
	VPSLLVQ Z16, Z3, K5, Z9                            // 6232e54d47c8
	VPSLLVQ -17(BP)(SI*8), Z3, K5, Z9                  // 6272e54d478cf5efffffff
	VPSLLVQ (R15), Z3, K5, Z9                          // 6252e54d470f
	VPSRAD $67, X7, K5, X24                            // 62f13d0572e743
	VPSRAD $67, 7(AX), K5, X24                         // 62f13d0572a00700000043
	VPSRAD $67, (DI), K5, X24                          // 62f13d05722743
	VPSRAD $127, Y7, K3, Y13                           // 62f1152b72e77f
	VPSRAD $127, 99(R15)(R15*1), K3, Y13               // 6291152b72a43f630000007f
	VPSRAD $127, (DX), K3, Y13                         // 62f1152b72227f
	VPSRAD $0, Z21, K4, Z14                            // 62b10d4c72e500
	VPSRAD $0, Z8, K4, Z14                             // 62d10d4c72e000
	VPSRAD $0, -7(DI)(R8*1), K4, Z14                   // 62b10d4c72a407f9ffffff00
	VPSRAD $0, (SP), K4, Z14                           // 62f10d4c72242400
	VPSRAD $0, Z21, K4, Z15                            // 62b1054c72e500
	VPSRAD $0, Z8, K4, Z15                             // 62d1054c72e000
	VPSRAD $0, -7(DI)(R8*1), K4, Z15                   // 62b1054c72a407f9ffffff00
	VPSRAD $0, (SP), K4, Z15                           // 62f1054c72242400
	VPSRAD X12, X16, K2, X20                           // 62c17d02e2e4
	VPSRAD 99(R15)(R15*1), X16, K2, X20                // 62817d02e2a43f63000000
	VPSRAD (DX), X16, K2, X20                          // 62e17d02e222
	VPSRAD X6, Y21, K2, Y2                             // 62f15522e2d6
	VPSRAD -17(BP)(SI*8), Y21, K2, Y2                  // 62f15522e294f5efffffff
	VPSRAD (R15), Y21, K2, Y2                          // 62d15522e217
	VPSRAD X17, Z20, K3, Z16                           // 62a15d43e2c1
	VPSRAD 7(SI)(DI*8), Z20, K3, Z16                   // 62e15d43e284fe07000000
	VPSRAD -15(R14), Z20, K3, Z16                      // 62c15d43e286f1ffffff
	VPSRAD X17, Z0, K3, Z16                            // 62a17d4be2c1
	VPSRAD 7(SI)(DI*8), Z0, K3, Z16                    // 62e17d4be284fe07000000
	VPSRAD -15(R14), Z0, K3, Z16                       // 62c17d4be286f1ffffff
	VPSRAD X17, Z20, K3, Z9                            // 62315d43e2c9
	VPSRAD 7(SI)(DI*8), Z20, K3, Z9                    // 62715d43e28cfe07000000
	VPSRAD -15(R14), Z20, K3, Z9                       // 62515d43e28ef1ffffff
	VPSRAD X17, Z0, K3, Z9                             // 62317d4be2c9
	VPSRAD 7(SI)(DI*8), Z0, K3, Z9                     // 62717d4be28cfe07000000
	VPSRAD -15(R14), Z0, K3, Z9                        // 62517d4be28ef1ffffff
	VPSRAQ $97, X6, K3, X28                            // 62f19d0372e661
	VPSRAQ $97, 7(SI)(DI*1), K3, X28                   // 62f19d0372a43e0700000061
	VPSRAQ $97, 15(DX)(BX*8), K3, X28                  // 62f19d0372a4da0f00000061
	VPSRAQ $81, Y9, K3, Y12                            // 62d19d2b72e151
	VPSRAQ $81, -17(BP)(SI*8), K3, Y12                 // 62f19d2b72a4f5efffffff51
	VPSRAQ $81, (R15), K3, Y12                         // 62d19d2b722751
	VPSRAQ $42, Z0, K2, Z23                            // 62f1c54272e02a
	VPSRAQ $42, Z11, K2, Z23                           // 62d1c54272e32a
	VPSRAQ $42, -7(CX), K2, Z23                        // 62f1c54272a1f9ffffff2a
	VPSRAQ $42, 15(DX)(BX*4), K2, Z23                  // 62f1c54272a49a0f0000002a
	VPSRAQ $42, Z0, K2, Z19                            // 62f1e54272e02a
	VPSRAQ $42, Z11, K2, Z19                           // 62d1e54272e32a
	VPSRAQ $42, -7(CX), K2, Z19                        // 62f1e54272a1f9ffffff2a
	VPSRAQ $42, 15(DX)(BX*4), K2, Z19                  // 62f1e54272a49a0f0000002a
	VPSRAQ X8, X8, K1, X1                              // 62d1bd09e2c8
	VPSRAQ -7(DI)(R8*1), X8, K1, X1                    // 62b1bd09e28c07f9ffffff
	VPSRAQ (SP), X8, K1, X1                            // 62f1bd09e20c24
	VPSRAQ X6, Y9, K2, Y1                              // 62f1b52ae2ce
	VPSRAQ -7(CX), Y9, K2, Y1                          // 62f1b52ae289f9ffffff
	VPSRAQ 15(DX)(BX*4), Y9, K2, Y1                    // 62f1b52ae28c9a0f000000
	VPSRAQ X0, Z24, K1, Z0                             // 62f1bd41e2c0
	VPSRAQ 99(R15)(R15*8), Z24, K1, Z0                 // 6291bd41e284ff63000000
	VPSRAQ 7(AX)(CX*8), Z24, K1, Z0                    // 62f1bd41e284c807000000
	VPSRAQ X0, Z12, K1, Z0                             // 62f19d49e2c0
	VPSRAQ 99(R15)(R15*8), Z12, K1, Z0                 // 62919d49e284ff63000000
	VPSRAQ 7(AX)(CX*8), Z12, K1, Z0                    // 62f19d49e284c807000000
	VPSRAQ X0, Z24, K1, Z25                            // 6261bd41e2c8
	VPSRAQ 99(R15)(R15*8), Z24, K1, Z25                // 6201bd41e28cff63000000
	VPSRAQ 7(AX)(CX*8), Z24, K1, Z25                   // 6261bd41e28cc807000000
	VPSRAQ X0, Z12, K1, Z25                            // 62619d49e2c8
	VPSRAQ 99(R15)(R15*8), Z12, K1, Z25                // 62019d49e28cff63000000
	VPSRAQ 7(AX)(CX*8), Z12, K1, Z25                   // 62619d49e28cc807000000
	VPSRAVD X6, X16, K7, X11                           // 62727d0746de
	VPSRAVD (AX), X16, K7, X11                         // 62727d074618
	VPSRAVD 7(SI), X16, K7, X11                        // 62727d07469e07000000
	VPSRAVD Y9, Y2, K1, Y3                             // 62d26d2946d9
	VPSRAVD 7(SI)(DI*8), Y2, K1, Y3                    // 62f26d29469cfe07000000
	VPSRAVD -15(R14), Y2, K1, Y3                       // 62d26d29469ef1ffffff
	VPSRAVD Z9, Z9, K1, Z0                             // 62d2354946c1
	VPSRAVD Z25, Z9, K1, Z0                            // 6292354946c1
	VPSRAVD 99(R15)(R15*8), Z9, K1, Z0                 // 629235494684ff63000000
	VPSRAVD 7(AX)(CX*8), Z9, K1, Z0                    // 62f235494684c807000000
	VPSRAVD Z9, Z3, K1, Z0                             // 62d2654946c1
	VPSRAVD Z25, Z3, K1, Z0                            // 6292654946c1
	VPSRAVD 99(R15)(R15*8), Z3, K1, Z0                 // 629265494684ff63000000
	VPSRAVD 7(AX)(CX*8), Z3, K1, Z0                    // 62f265494684c807000000
	VPSRAVD Z9, Z9, K1, Z26                            // 6242354946d1
	VPSRAVD Z25, Z9, K1, Z26                           // 6202354946d1
	VPSRAVD 99(R15)(R15*8), Z9, K1, Z26                // 620235494694ff63000000
	VPSRAVD 7(AX)(CX*8), Z9, K1, Z26                   // 626235494694c807000000
	VPSRAVD Z9, Z3, K1, Z26                            // 6242654946d1
	VPSRAVD Z25, Z3, K1, Z26                           // 6202654946d1
	VPSRAVD 99(R15)(R15*8), Z3, K1, Z26                // 620265494694ff63000000
	VPSRAVD 7(AX)(CX*8), Z3, K1, Z26                   // 626265494694c807000000
	VPSRAVQ X12, X22, K1, X6                           // 62d2cd0146f4
	VPSRAVQ (BX), X22, K1, X6                          // 62f2cd014633
	VPSRAVQ -17(BP)(SI*1), X22, K1, X6                 // 62f2cd0146b435efffffff
	VPSRAVQ Y14, Y21, K7, Y12                          // 6252d52746e6
	VPSRAVQ 7(SI)(DI*1), Y21, K7, Y12                  // 6272d52746a43e07000000
	VPSRAVQ 15(DX)(BX*8), Y21, K7, Y12                 // 6272d52746a4da0f000000
	VPSRAVQ Z17, Z20, K2, Z9                           // 6232dd4246c9
	VPSRAVQ Z0, Z20, K2, Z9                            // 6272dd4246c8
	VPSRAVQ (AX), Z20, K2, Z9                          // 6272dd424608
	VPSRAVQ 7(SI), Z20, K2, Z9                         // 6272dd42468e07000000
	VPSRAVQ Z17, Z0, K2, Z9                            // 6232fd4a46c9
	VPSRAVQ Z0, Z0, K2, Z9                             // 6272fd4a46c8
	VPSRAVQ (AX), Z0, K2, Z9                           // 6272fd4a4608
	VPSRAVQ 7(SI), Z0, K2, Z9                          // 6272fd4a468e07000000
	VPSRAVQ Z17, Z20, K2, Z28                          // 6222dd4246e1
	VPSRAVQ Z0, Z20, K2, Z28                           // 6262dd4246e0
	VPSRAVQ (AX), Z20, K2, Z28                         // 6262dd424620
	VPSRAVQ 7(SI), Z20, K2, Z28                        // 6262dd4246a607000000
	VPSRAVQ Z17, Z0, K2, Z28                           // 6222fd4a46e1
	VPSRAVQ Z0, Z0, K2, Z28                            // 6262fd4a46e0
	VPSRAVQ (AX), Z0, K2, Z28                          // 6262fd4a4620
	VPSRAVQ 7(SI), Z0, K2, Z28                         // 6262fd4a46a607000000
	VPSRLD $47, X0, K7, X0                             // 62f17d0f72d02f
	VPSRLD $47, (R14), K7, X0                          // 62d17d0f72162f
	VPSRLD $47, -7(DI)(R8*8), K7, X0                   // 62b17d0f7294c7f9ffffff2f
	VPSRLD $82, Y6, K4, Y22                            // 62f14d2472d652
	VPSRLD $82, 99(R15)(R15*8), K4, Y22                // 62914d247294ff6300000052
	VPSRLD $82, 7(AX)(CX*8), K4, Y22                   // 62f14d247294c80700000052
	VPSRLD $126, Z7, K4, Z26                           // 62f12d4472d77e
	VPSRLD $126, Z21, K4, Z26                          // 62b12d4472d57e
	VPSRLD $126, (R8), K4, Z26                         // 62d12d4472107e
	VPSRLD $126, 15(DX)(BX*2), K4, Z26                 // 62f12d4472945a0f0000007e
	VPSRLD $126, Z7, K4, Z22                           // 62f14d4472d77e
	VPSRLD $126, Z21, K4, Z22                          // 62b14d4472d57e
	VPSRLD $126, (R8), K4, Z22                         // 62d14d4472107e
	VPSRLD $126, 15(DX)(BX*2), K4, Z22                 // 62f14d4472945a0f0000007e
	VPSRLD X17, X11, K7, X25                           // 6221250fd2c9
	VPSRLD 99(R15)(R15*4), X11, K7, X25                // 6201250fd28cbf63000000
	VPSRLD 15(DX), X11, K7, X25                        // 6261250fd28a0f000000
	VPSRLD X18, Y7, K2, Y21                            // 62a1452ad2ea
	VPSRLD (CX), Y7, K2, Y21                           // 62e1452ad229
	VPSRLD 99(R15), Y7, K2, Y21                        // 62c1452ad2af63000000
	VPSRLD X11, Z14, K5, Z16                           // 62c10d4dd2c3
	VPSRLD 99(R15)(R15*2), Z14, K5, Z16                // 62810d4dd2847f63000000
	VPSRLD -7(DI), Z14, K5, Z16                        // 62e10d4dd287f9ffffff
	VPSRLD X11, Z13, K5, Z16                           // 62c1154dd2c3
	VPSRLD 99(R15)(R15*2), Z13, K5, Z16                // 6281154dd2847f63000000
	VPSRLD -7(DI), Z13, K5, Z16                        // 62e1154dd287f9ffffff
	VPSRLD X11, Z14, K5, Z25                           // 62410d4dd2cb
	VPSRLD 99(R15)(R15*2), Z14, K5, Z25                // 62010d4dd28c7f63000000
	VPSRLD -7(DI), Z14, K5, Z25                        // 62610d4dd28ff9ffffff
	VPSRLD X11, Z13, K5, Z25                           // 6241154dd2cb
	VPSRLD 99(R15)(R15*2), Z13, K5, Z25                // 6201154dd28c7f63000000
	VPSRLD -7(DI), Z13, K5, Z25                        // 6261154dd28ff9ffffff
	VPSRLQ $65, X2, K3, X24                            // 62f1bd0373d241
	VPSRLQ $65, 15(DX)(BX*1), K3, X24                  // 62f1bd0373941a0f00000041
	VPSRLQ $65, -7(CX)(DX*2), K3, X24                  // 62f1bd03739451f9ffffff41
	VPSRLQ $67, Y14, K4, Y20                           // 62d1dd2473d643
	VPSRLQ $67, (BX), K4, Y20                          // 62f1dd24731343
	VPSRLQ $67, -17(BP)(SI*1), K4, Y20                 // 62f1dd24739435efffffff43
	VPSRLQ $127, Z27, K2, Z2                           // 6291ed4a73d37f
	VPSRLQ $127, Z25, K2, Z2                           // 6291ed4a73d17f
	VPSRLQ $127, -17(BP)(SI*2), K2, Z2                 // 62f1ed4a739475efffffff7f
	VPSRLQ $127, 7(AX)(CX*2), K2, Z2                   // 62f1ed4a739448070000007f
	VPSRLQ $127, Z27, K2, Z7                           // 6291c54a73d37f
	VPSRLQ $127, Z25, K2, Z7                           // 6291c54a73d17f
	VPSRLQ $127, -17(BP)(SI*2), K2, Z7                 // 62f1c54a739475efffffff7f
	VPSRLQ $127, 7(AX)(CX*2), K2, Z7                   // 62f1c54a739448070000007f
	VPSRLQ X26, X27, K2, X2                            // 6291a502d3d2
	VPSRLQ -17(BP), X27, K2, X2                        // 62f1a502d395efffffff
	VPSRLQ -15(R14)(R15*8), X27, K2, X2                // 6291a502d394fef1ffffff
	VPSRLQ X22, Y13, K3, Y24                           // 6221952bd3c6
	VPSRLQ 17(SP)(BP*2), Y13, K3, Y24                  // 6261952bd3846c11000000
	VPSRLQ -7(DI)(R8*4), Y13, K3, Y24                  // 6221952bd38487f9ffffff
	VPSRLQ X30, Z27, K3, Z23                           // 6281a543d3fe
	VPSRLQ 15(R8), Z27, K3, Z23                        // 62c1a543d3b80f000000
	VPSRLQ (BP), Z27, K3, Z23                          // 62e1a543d37d00
	VPSRLQ X30, Z14, K3, Z23                           // 62818d4bd3fe
	VPSRLQ 15(R8), Z14, K3, Z23                        // 62c18d4bd3b80f000000
	VPSRLQ (BP), Z14, K3, Z23                          // 62e18d4bd37d00
	VPSRLQ X30, Z27, K3, Z9                            // 6211a543d3ce
	VPSRLQ 15(R8), Z27, K3, Z9                         // 6251a543d3880f000000
	VPSRLQ (BP), Z27, K3, Z9                           // 6271a543d34d00
	VPSRLQ X30, Z14, K3, Z9                            // 62118d4bd3ce
	VPSRLQ 15(R8), Z14, K3, Z9                         // 62518d4bd3880f000000
	VPSRLQ (BP), Z14, K3, Z9                           // 62718d4bd34d00
	VPSRLVD X15, X11, K3, X3                           // 62d2250b45df
	VPSRLVD 15(R8)(R14*8), X11, K3, X3                 // 6292250b459cf00f000000
	VPSRLVD -15(R14)(R15*2), X11, K3, X3               // 6292250b459c7ef1ffffff
	VPSRLVD Y21, Y14, K2, Y20                          // 62a20d2a45e5
	VPSRLVD 15(R8)(R14*4), Y14, K2, Y20                // 62820d2a45a4b00f000000
	VPSRLVD -7(CX)(DX*4), Y14, K2, Y20                 // 62e20d2a45a491f9ffffff
	VPSRLVD Z8, Z14, K1, Z3                            // 62d20d4945d8
	VPSRLVD Z24, Z14, K1, Z3                           // 62920d4945d8
	VPSRLVD 15(R8)(R14*1), Z14, K1, Z3                 // 62920d49459c300f000000
	VPSRLVD 15(R8)(R14*2), Z14, K1, Z3                 // 62920d49459c700f000000
	VPSRLVD Z8, Z7, K1, Z3                             // 62d2454945d8
	VPSRLVD Z24, Z7, K1, Z3                            // 6292454945d8
	VPSRLVD 15(R8)(R14*1), Z7, K1, Z3                  // 62924549459c300f000000
	VPSRLVD 15(R8)(R14*2), Z7, K1, Z3                  // 62924549459c700f000000
	VPSRLVD Z8, Z14, K1, Z0                            // 62d20d4945c0
	VPSRLVD Z24, Z14, K1, Z0                           // 62920d4945c0
	VPSRLVD 15(R8)(R14*1), Z14, K1, Z0                 // 62920d494584300f000000
	VPSRLVD 15(R8)(R14*2), Z14, K1, Z0                 // 62920d494584700f000000
	VPSRLVD Z8, Z7, K1, Z0                             // 62d2454945c0
	VPSRLVD Z24, Z7, K1, Z0                            // 6292454945c0
	VPSRLVD 15(R8)(R14*1), Z7, K1, Z0                  // 629245494584300f000000
	VPSRLVD 15(R8)(R14*2), Z7, K1, Z0                  // 629245494584700f000000
	VPSRLVQ X6, X13, K2, X30                           // 6262950a45f6
	VPSRLVQ -15(R14)(R15*1), X13, K2, X30              // 6202950a45b43ef1ffffff
	VPSRLVQ -15(BX), X13, K2, X30                      // 6262950a45b3f1ffffff
	VPSRLVQ Y30, Y26, K1, Y1                           // 6292ad2145ce
	VPSRLVQ (R8), Y26, K1, Y1                          // 62d2ad214508
	VPSRLVQ 15(DX)(BX*2), Y26, K1, Y1                  // 62f2ad21458c5a0f000000
	VPSRLVQ Z6, Z1, K7, Z22                            // 62e2f54f45f6
	VPSRLVQ Z2, Z1, K7, Z22                            // 62e2f54f45f2
	VPSRLVQ (R14), Z1, K7, Z22                         // 62c2f54f4536
	VPSRLVQ -7(DI)(R8*8), Z1, K7, Z22                  // 62a2f54f45b4c7f9ffffff
	VPSRLVQ Z6, Z16, K7, Z22                           // 62e2fd4745f6
	VPSRLVQ Z2, Z16, K7, Z22                           // 62e2fd4745f2
	VPSRLVQ (R14), Z16, K7, Z22                        // 62c2fd474536
	VPSRLVQ -7(DI)(R8*8), Z16, K7, Z22                 // 62a2fd4745b4c7f9ffffff
	VPSRLVQ Z6, Z1, K7, Z25                            // 6262f54f45ce
	VPSRLVQ Z2, Z1, K7, Z25                            // 6262f54f45ca
	VPSRLVQ (R14), Z1, K7, Z25                         // 6242f54f450e
	VPSRLVQ -7(DI)(R8*8), Z1, K7, Z25                  // 6222f54f458cc7f9ffffff
	VPSRLVQ Z6, Z16, K7, Z25                           // 6262fd4745ce
	VPSRLVQ Z2, Z16, K7, Z25                           // 6262fd4745ca
	VPSRLVQ (R14), Z16, K7, Z25                        // 6242fd47450e
	VPSRLVQ -7(DI)(R8*8), Z16, K7, Z25                 // 6222fd47458cc7f9ffffff
	VPSUBD X0, X1, K6, X8                              // 6271750efac0
	VPSUBD 99(R15)(R15*1), X1, K6, X8                  // 6211750efa843f63000000
	VPSUBD (DX), X1, K6, X8                            // 6271750efa02
	VPSUBD Y30, Y7, K3, Y21                            // 6281452bfaee
	VPSUBD (R14), Y7, K3, Y21                          // 62c1452bfa2e
	VPSUBD -7(DI)(R8*8), Y7, K3, Y21                   // 62a1452bfaacc7f9ffffff
	VPSUBD Z3, Z26, K7, Z13                            // 62712d47faeb
	VPSUBD Z0, Z26, K7, Z13                            // 62712d47fae8
	VPSUBD -7(CX)(DX*1), Z26, K7, Z13                  // 62712d47faac11f9ffffff
	VPSUBD -15(R14)(R15*4), Z26, K7, Z13               // 62112d47faacbef1ffffff
	VPSUBD Z3, Z3, K7, Z13                             // 6271654ffaeb
	VPSUBD Z0, Z3, K7, Z13                             // 6271654ffae8
	VPSUBD -7(CX)(DX*1), Z3, K7, Z13                   // 6271654ffaac11f9ffffff
	VPSUBD -15(R14)(R15*4), Z3, K7, Z13                // 6211654ffaacbef1ffffff
	VPSUBD Z3, Z26, K7, Z21                            // 62e12d47faeb
	VPSUBD Z0, Z26, K7, Z21                            // 62e12d47fae8
	VPSUBD -7(CX)(DX*1), Z26, K7, Z21                  // 62e12d47faac11f9ffffff
	VPSUBD -15(R14)(R15*4), Z26, K7, Z21               // 62812d47faacbef1ffffff
	VPSUBD Z3, Z3, K7, Z21                             // 62e1654ffaeb
	VPSUBD Z0, Z3, K7, Z21                             // 62e1654ffae8
	VPSUBD -7(CX)(DX*1), Z3, K7, Z21                   // 62e1654ffaac11f9ffffff
	VPSUBD -15(R14)(R15*4), Z3, K7, Z21                // 6281654ffaacbef1ffffff
	VPSUBQ X16, X0, K4, X15                            // 6231fd0cfbf8
	VPSUBQ -17(BP)(SI*8), X0, K4, X15                  // 6271fd0cfbbcf5efffffff
	VPSUBQ (R15), X0, K4, X15                          // 6251fd0cfb3f
	VPSUBQ Y24, Y18, K4, Y13                           // 6211ed24fbe8
	VPSUBQ 99(R15)(R15*4), Y18, K4, Y13                // 6211ed24fbacbf63000000
	VPSUBQ 15(DX), Y18, K4, Y13                        // 6271ed24fbaa0f000000
	VPSUBQ Z3, Z11, K7, Z21                            // 62e1a54ffbeb
	VPSUBQ Z12, Z11, K7, Z21                           // 62c1a54ffbec
	VPSUBQ 15(DX)(BX*1), Z11, K7, Z21                  // 62e1a54ffbac1a0f000000
	VPSUBQ -7(CX)(DX*2), Z11, K7, Z21                  // 62e1a54ffbac51f9ffffff
	VPSUBQ Z3, Z25, K7, Z21                            // 62e1b547fbeb
	VPSUBQ Z12, Z25, K7, Z21                           // 62c1b547fbec
	VPSUBQ 15(DX)(BX*1), Z25, K7, Z21                  // 62e1b547fbac1a0f000000
	VPSUBQ -7(CX)(DX*2), Z25, K7, Z21                  // 62e1b547fbac51f9ffffff
	VPSUBQ Z3, Z11, K7, Z13                            // 6271a54ffbeb
	VPSUBQ Z12, Z11, K7, Z13                           // 6251a54ffbec
	VPSUBQ 15(DX)(BX*1), Z11, K7, Z13                  // 6271a54ffbac1a0f000000
	VPSUBQ -7(CX)(DX*2), Z11, K7, Z13                  // 6271a54ffbac51f9ffffff
	VPSUBQ Z3, Z25, K7, Z13                            // 6271b547fbeb
	VPSUBQ Z12, Z25, K7, Z13                           // 6251b547fbec
	VPSUBQ 15(DX)(BX*1), Z25, K7, Z13                  // 6271b547fbac1a0f000000
	VPSUBQ -7(CX)(DX*2), Z25, K7, Z13                  // 6271b547fbac51f9ffffff
	VPTERNLOGD $42, X5, X14, K1, X12                   // 62730d0925e52a
	VPTERNLOGD $42, (AX), X14, K1, X12                 // 62730d0925202a
	VPTERNLOGD $42, 7(SI), X14, K1, X12                // 62730d0925a6070000002a
	VPTERNLOGD $79, Y12, Y26, K1, Y11                  // 62532d2125dc4f
	VPTERNLOGD $79, 17(SP)(BP*2), Y26, K1, Y11         // 62732d21259c6c110000004f
	VPTERNLOGD $79, -7(DI)(R8*4), Y26, K1, Y11         // 62332d21259c87f9ffffff4f
	VPTERNLOGD $64, Z0, Z7, K7, Z3                     // 62f3454f25d840
	VPTERNLOGD $64, Z6, Z7, K7, Z3                     // 62f3454f25de40
	VPTERNLOGD $64, 7(AX)(CX*4), Z7, K7, Z3            // 62f3454f259c880700000040
	VPTERNLOGD $64, 7(AX)(CX*1), Z7, K7, Z3            // 62f3454f259c080700000040
	VPTERNLOGD $64, Z0, Z9, K7, Z3                     // 62f3354f25d840
	VPTERNLOGD $64, Z6, Z9, K7, Z3                     // 62f3354f25de40
	VPTERNLOGD $64, 7(AX)(CX*4), Z9, K7, Z3            // 62f3354f259c880700000040
	VPTERNLOGD $64, 7(AX)(CX*1), Z9, K7, Z3            // 62f3354f259c080700000040
	VPTERNLOGD $64, Z0, Z7, K7, Z27                    // 6263454f25d840
	VPTERNLOGD $64, Z6, Z7, K7, Z27                    // 6263454f25de40
	VPTERNLOGD $64, 7(AX)(CX*4), Z7, K7, Z27           // 6263454f259c880700000040
	VPTERNLOGD $64, 7(AX)(CX*1), Z7, K7, Z27           // 6263454f259c080700000040
	VPTERNLOGD $64, Z0, Z9, K7, Z27                    // 6263354f25d840
	VPTERNLOGD $64, Z6, Z9, K7, Z27                    // 6263354f25de40
	VPTERNLOGD $64, 7(AX)(CX*4), Z9, K7, Z27           // 6263354f259c880700000040
	VPTERNLOGD $64, 7(AX)(CX*1), Z9, K7, Z27           // 6263354f259c080700000040
	VPTERNLOGQ $27, X8, X15, K2, X17                   // 62c3850a25c81b
	VPTERNLOGQ $27, (BX), X15, K2, X17                 // 62e3850a250b1b
	VPTERNLOGQ $27, -17(BP)(SI*1), X15, K2, X17        // 62e3850a258c35efffffff1b
	VPTERNLOGQ $47, Y31, Y18, K4, Y14                  // 6213ed2425f72f
	VPTERNLOGQ $47, 15(R8), Y18, K4, Y14               // 6253ed2425b00f0000002f
	VPTERNLOGQ $47, (BP), Y18, K4, Y14                 // 6273ed242575002f
	VPTERNLOGQ $82, Z9, Z3, K1, Z20                    // 62c3e54925e152
	VPTERNLOGQ $82, Z19, Z3, K1, Z20                   // 62a3e54925e352
	VPTERNLOGQ $82, (SI), Z3, K1, Z20                  // 62e3e549252652
	VPTERNLOGQ $82, 7(SI)(DI*2), Z3, K1, Z20           // 62e3e54925a47e0700000052
	VPTERNLOGQ $82, Z9, Z30, K1, Z20                   // 62c38d4125e152
	VPTERNLOGQ $82, Z19, Z30, K1, Z20                  // 62a38d4125e352
	VPTERNLOGQ $82, (SI), Z30, K1, Z20                 // 62e38d41252652
	VPTERNLOGQ $82, 7(SI)(DI*2), Z30, K1, Z20          // 62e38d4125a47e0700000052
	VPTERNLOGQ $82, Z9, Z3, K1, Z28                    // 6243e54925e152
	VPTERNLOGQ $82, Z19, Z3, K1, Z28                   // 6223e54925e352
	VPTERNLOGQ $82, (SI), Z3, K1, Z28                  // 6263e549252652
	VPTERNLOGQ $82, 7(SI)(DI*2), Z3, K1, Z28           // 6263e54925a47e0700000052
	VPTERNLOGQ $82, Z9, Z30, K1, Z28                   // 62438d4125e152
	VPTERNLOGQ $82, Z19, Z30, K1, Z28                  // 62238d4125e352
	VPTERNLOGQ $82, (SI), Z30, K1, Z28                 // 62638d41252652
	VPTERNLOGQ $82, 7(SI)(DI*2), Z30, K1, Z28          // 62638d4125a47e0700000052
	VPTESTMD X13, X23, K7, K4                          // 62d2450727e5
	VPTESTMD (R8), X23, K7, K4                         // 62d245072720
	VPTESTMD 15(DX)(BX*2), X23, K7, K4                 // 62f2450727a45a0f000000
	VPTESTMD X13, X23, K7, K6                          // 62d2450727f5
	VPTESTMD (R8), X23, K7, K6                         // 62d245072730
	VPTESTMD 15(DX)(BX*2), X23, K7, K6                 // 62f2450727b45a0f000000
	VPTESTMD Y2, Y24, K7, K0                           // 62f23d2727c2
	VPTESTMD -15(R14)(R15*1), Y24, K7, K0              // 62923d2727843ef1ffffff
	VPTESTMD -15(BX), Y24, K7, K0                      // 62f23d272783f1ffffff
	VPTESTMD Y2, Y24, K7, K7                           // 62f23d2727fa
	VPTESTMD -15(R14)(R15*1), Y24, K7, K7              // 62923d2727bc3ef1ffffff
	VPTESTMD -15(BX), Y24, K7, K7                      // 62f23d2727bbf1ffffff
	VPTESTMD Z2, Z18, K6, K5                           // 62f26d4627ea
	VPTESTMD Z21, Z18, K6, K5                          // 62b26d4627ed
	VPTESTMD 7(SI)(DI*4), Z18, K6, K5                  // 62f26d4627acbe07000000
	VPTESTMD -7(DI)(R8*2), Z18, K6, K5                 // 62b26d4627ac47f9ffffff
	VPTESTMD Z2, Z24, K6, K5                           // 62f23d4627ea
	VPTESTMD Z21, Z24, K6, K5                          // 62b23d4627ed
	VPTESTMD 7(SI)(DI*4), Z24, K6, K5                  // 62f23d4627acbe07000000
	VPTESTMD -7(DI)(R8*2), Z24, K6, K5                 // 62b23d4627ac47f9ffffff
	VPTESTMD Z2, Z18, K6, K4                           // 62f26d4627e2
	VPTESTMD Z21, Z18, K6, K4                          // 62b26d4627e5
	VPTESTMD 7(SI)(DI*4), Z18, K6, K4                  // 62f26d4627a4be07000000
	VPTESTMD -7(DI)(R8*2), Z18, K6, K4                 // 62b26d4627a447f9ffffff
	VPTESTMD Z2, Z24, K6, K4                           // 62f23d4627e2
	VPTESTMD Z21, Z24, K6, K4                          // 62b23d4627e5
	VPTESTMD 7(SI)(DI*4), Z24, K6, K4                  // 62f23d4627a4be07000000
	VPTESTMD -7(DI)(R8*2), Z24, K6, K4                 // 62b23d4627a447f9ffffff
	VPTESTMQ X24, X28, K3, K4                          // 62929d0327e0
	VPTESTMQ 17(SP)(BP*1), X28, K3, K4                 // 62f29d0327a42c11000000
	VPTESTMQ -7(CX)(DX*8), X28, K3, K4                 // 62f29d0327a4d1f9ffffff
	VPTESTMQ X24, X28, K3, K6                          // 62929d0327f0
	VPTESTMQ 17(SP)(BP*1), X28, K3, K6                 // 62f29d0327b42c11000000
	VPTESTMQ -7(CX)(DX*8), X28, K3, K6                 // 62f29d0327b4d1f9ffffff
	VPTESTMQ Y21, Y7, K7, K1                           // 62b2c52f27cd
	VPTESTMQ 7(AX)(CX*4), Y7, K7, K1                   // 62f2c52f278c8807000000
	VPTESTMQ 7(AX)(CX*1), Y7, K7, K1                   // 62f2c52f278c0807000000
	VPTESTMQ Y21, Y7, K7, K3                           // 62b2c52f27dd
	VPTESTMQ 7(AX)(CX*4), Y7, K7, K3                   // 62f2c52f279c8807000000
	VPTESTMQ 7(AX)(CX*1), Y7, K7, K3                   // 62f2c52f279c0807000000
	VPTESTMQ Z6, Z7, K4, K6                            // 62f2c54c27f6
	VPTESTMQ Z16, Z7, K4, K6                           // 62b2c54c27f0
	VPTESTMQ 17(SP), Z7, K4, K6                        // 62f2c54c27b42411000000
	VPTESTMQ -17(BP)(SI*4), Z7, K4, K6                 // 62f2c54c27b4b5efffffff
	VPTESTMQ Z6, Z13, K4, K6                           // 62f2954c27f6
	VPTESTMQ Z16, Z13, K4, K6                          // 62b2954c27f0
	VPTESTMQ 17(SP), Z13, K4, K6                       // 62f2954c27b42411000000
	VPTESTMQ -17(BP)(SI*4), Z13, K4, K6                // 62f2954c27b4b5efffffff
	VPTESTMQ Z6, Z7, K4, K7                            // 62f2c54c27fe
	VPTESTMQ Z16, Z7, K4, K7                           // 62b2c54c27f8
	VPTESTMQ 17(SP), Z7, K4, K7                        // 62f2c54c27bc2411000000
	VPTESTMQ -17(BP)(SI*4), Z7, K4, K7                 // 62f2c54c27bcb5efffffff
	VPTESTMQ Z6, Z13, K4, K7                           // 62f2954c27fe
	VPTESTMQ Z16, Z13, K4, K7                          // 62b2954c27f8
	VPTESTMQ 17(SP), Z13, K4, K7                       // 62f2954c27bc2411000000
	VPTESTMQ -17(BP)(SI*4), Z13, K4, K7                // 62f2954c27bcb5efffffff
	VPTESTNMD X1, X21, K2, K1                          // 62f2560227c9
	VPTESTNMD (R14), X21, K2, K1                       // 62d25602270e
	VPTESTNMD -7(DI)(R8*8), X21, K2, K1                // 62b25602278cc7f9ffffff
	VPTESTNMD X1, X21, K2, K5                          // 62f2560227e9
	VPTESTNMD (R14), X21, K2, K5                       // 62d25602272e
	VPTESTNMD -7(DI)(R8*8), X21, K2, K5                // 62b2560227acc7f9ffffff
	VPTESTNMD Y1, Y24, K2, K3                          // 62f23e2227d9
	VPTESTNMD 7(SI)(DI*4), Y24, K2, K3                 // 62f23e22279cbe07000000
	VPTESTNMD -7(DI)(R8*2), Y24, K2, K3                // 62b23e22279c47f9ffffff
	VPTESTNMD Y1, Y24, K2, K1                          // 62f23e2227c9
	VPTESTNMD 7(SI)(DI*4), Y24, K2, K1                 // 62f23e22278cbe07000000
	VPTESTNMD -7(DI)(R8*2), Y24, K2, K1                // 62b23e22278c47f9ffffff
	VPTESTNMD Z2, Z22, K3, K5                          // 62f24e4327ea
	VPTESTNMD Z31, Z22, K3, K5                         // 62924e4327ef
	VPTESTNMD -17(BP)(SI*8), Z22, K3, K5               // 62f24e4327acf5efffffff
	VPTESTNMD (R15), Z22, K3, K5                       // 62d24e43272f
	VPTESTNMD Z2, Z7, K3, K5                           // 62f2464b27ea
	VPTESTNMD Z31, Z7, K3, K5                          // 6292464b27ef
	VPTESTNMD -17(BP)(SI*8), Z7, K3, K5                // 62f2464b27acf5efffffff
	VPTESTNMD (R15), Z7, K3, K5                        // 62d2464b272f
	VPTESTNMD Z2, Z22, K3, K4                          // 62f24e4327e2
	VPTESTNMD Z31, Z22, K3, K4                         // 62924e4327e7
	VPTESTNMD -17(BP)(SI*8), Z22, K3, K4               // 62f24e4327a4f5efffffff
	VPTESTNMD (R15), Z22, K3, K4                       // 62d24e432727
	VPTESTNMD Z2, Z7, K3, K4                           // 62f2464b27e2
	VPTESTNMD Z31, Z7, K3, K4                          // 6292464b27e7
	VPTESTNMD -17(BP)(SI*8), Z7, K3, K4                // 62f2464b27a4f5efffffff
	VPTESTNMD (R15), Z7, K3, K4                        // 62d2464b2727
	VPTESTNMQ X31, X11, K3, K7                         // 6292a60b27ff
	VPTESTNMQ 99(R15)(R15*4), X11, K3, K7              // 6292a60b27bcbf63000000
	VPTESTNMQ 15(DX), X11, K3, K7                      // 62f2a60b27ba0f000000
	VPTESTNMQ X31, X11, K3, K6                         // 6292a60b27f7
	VPTESTNMQ 99(R15)(R15*4), X11, K3, K6              // 6292a60b27b4bf63000000
	VPTESTNMQ 15(DX), X11, K3, K6                      // 62f2a60b27b20f000000
	VPTESTNMQ Y18, Y5, K3, K4                          // 62b2d62b27e2
	VPTESTNMQ 17(SP), Y5, K3, K4                       // 62f2d62b27a42411000000
	VPTESTNMQ -17(BP)(SI*4), Y5, K3, K4                // 62f2d62b27a4b5efffffff
	VPTESTNMQ Y18, Y5, K3, K6                          // 62b2d62b27f2
	VPTESTNMQ 17(SP), Y5, K3, K6                       // 62f2d62b27b42411000000
	VPTESTNMQ -17(BP)(SI*4), Y5, K3, K6                // 62f2d62b27b4b5efffffff
	VPTESTNMQ Z1, Z20, K2, K0                          // 62f2de4227c1
	VPTESTNMQ Z3, Z20, K2, K0                          // 62f2de4227c3
	VPTESTNMQ 7(SI)(DI*8), Z20, K2, K0                 // 62f2de422784fe07000000
	VPTESTNMQ -15(R14), Z20, K2, K0                    // 62d2de422786f1ffffff
	VPTESTNMQ Z1, Z9, K2, K0                           // 62f2b64a27c1
	VPTESTNMQ Z3, Z9, K2, K0                           // 62f2b64a27c3
	VPTESTNMQ 7(SI)(DI*8), Z9, K2, K0                  // 62f2b64a2784fe07000000
	VPTESTNMQ -15(R14), Z9, K2, K0                     // 62d2b64a2786f1ffffff
	VPTESTNMQ Z1, Z20, K2, K7                          // 62f2de4227f9
	VPTESTNMQ Z3, Z20, K2, K7                          // 62f2de4227fb
	VPTESTNMQ 7(SI)(DI*8), Z20, K2, K7                 // 62f2de4227bcfe07000000
	VPTESTNMQ -15(R14), Z20, K2, K7                    // 62d2de4227bef1ffffff
	VPTESTNMQ Z1, Z9, K2, K7                           // 62f2b64a27f9
	VPTESTNMQ Z3, Z9, K2, K7                           // 62f2b64a27fb
	VPTESTNMQ 7(SI)(DI*8), Z9, K2, K7                  // 62f2b64a27bcfe07000000
	VPTESTNMQ -15(R14), Z9, K2, K7                     // 62d2b64a27bef1ffffff
	VPUNPCKHDQ X9, X7, K1, X20                         // 62c145096ae1
	VPUNPCKHDQ -7(CX)(DX*1), X7, K1, X20               // 62e145096aa411f9ffffff
	VPUNPCKHDQ -15(R14)(R15*4), X7, K1, X20            // 628145096aa4bef1ffffff
	VPUNPCKHDQ Y11, Y8, K7, Y1                         // 62d13d2f6acb
	VPUNPCKHDQ -17(BP)(SI*8), Y8, K7, Y1               // 62f13d2f6a8cf5efffffff
	VPUNPCKHDQ (R15), Y8, K7, Y1                       // 62d13d2f6a0f
	VPUNPCKHDQ Z3, Z5, K2, Z19                         // 62e1554a6adb
	VPUNPCKHDQ Z5, Z5, K2, Z19                         // 62e1554a6add
	VPUNPCKHDQ -7(CX), Z5, K2, Z19                     // 62e1554a6a99f9ffffff
	VPUNPCKHDQ 15(DX)(BX*4), Z5, K2, Z19               // 62e1554a6a9c9a0f000000
	VPUNPCKHDQ Z3, Z1, K2, Z19                         // 62e1754a6adb
	VPUNPCKHDQ Z5, Z1, K2, Z19                         // 62e1754a6add
	VPUNPCKHDQ -7(CX), Z1, K2, Z19                     // 62e1754a6a99f9ffffff
	VPUNPCKHDQ 15(DX)(BX*4), Z1, K2, Z19               // 62e1754a6a9c9a0f000000
	VPUNPCKHDQ Z3, Z5, K2, Z15                         // 6271554a6afb
	VPUNPCKHDQ Z5, Z5, K2, Z15                         // 6271554a6afd
	VPUNPCKHDQ -7(CX), Z5, K2, Z15                     // 6271554a6ab9f9ffffff
	VPUNPCKHDQ 15(DX)(BX*4), Z5, K2, Z15               // 6271554a6abc9a0f000000
	VPUNPCKHDQ Z3, Z1, K2, Z15                         // 6271754a6afb
	VPUNPCKHDQ Z5, Z1, K2, Z15                         // 6271754a6afd
	VPUNPCKHDQ -7(CX), Z1, K2, Z15                     // 6271754a6ab9f9ffffff
	VPUNPCKHDQ 15(DX)(BX*4), Z1, K2, Z15               // 6271754a6abc9a0f000000
	VPUNPCKHQDQ X5, X14, K4, X7                        // 62f18d0c6dfd
	VPUNPCKHQDQ 15(DX)(BX*1), X14, K4, X7              // 62f18d0c6dbc1a0f000000
	VPUNPCKHQDQ -7(CX)(DX*2), X14, K4, X7              // 62f18d0c6dbc51f9ffffff
	VPUNPCKHQDQ Y16, Y17, K1, Y27                      // 6221f5216dd8
	VPUNPCKHQDQ 7(SI)(DI*8), Y17, K1, Y27              // 6261f5216d9cfe07000000
	VPUNPCKHQDQ -15(R14), Y17, K1, Y27                 // 6241f5216d9ef1ffffff
	VPUNPCKHQDQ Z16, Z21, K3, Z14                      // 6231d5436df0
	VPUNPCKHQDQ Z9, Z21, K3, Z14                       // 6251d5436df1
	VPUNPCKHQDQ 99(R15)(R15*8), Z21, K3, Z14           // 6211d5436db4ff63000000
	VPUNPCKHQDQ 7(AX)(CX*8), Z21, K3, Z14              // 6271d5436db4c807000000
	VPUNPCKHQDQ Z16, Z8, K3, Z14                       // 6231bd4b6df0
	VPUNPCKHQDQ Z9, Z8, K3, Z14                        // 6251bd4b6df1
	VPUNPCKHQDQ 99(R15)(R15*8), Z8, K3, Z14            // 6211bd4b6db4ff63000000
	VPUNPCKHQDQ 7(AX)(CX*8), Z8, K3, Z14               // 6271bd4b6db4c807000000
	VPUNPCKHQDQ Z16, Z21, K3, Z15                      // 6231d5436df8
	VPUNPCKHQDQ Z9, Z21, K3, Z15                       // 6251d5436df9
	VPUNPCKHQDQ 99(R15)(R15*8), Z21, K3, Z15           // 6211d5436dbcff63000000
	VPUNPCKHQDQ 7(AX)(CX*8), Z21, K3, Z15              // 6271d5436dbcc807000000
	VPUNPCKHQDQ Z16, Z8, K3, Z15                       // 6231bd4b6df8
	VPUNPCKHQDQ Z9, Z8, K3, Z15                        // 6251bd4b6df9
	VPUNPCKHQDQ 99(R15)(R15*8), Z8, K3, Z15            // 6211bd4b6dbcff63000000
	VPUNPCKHQDQ 7(AX)(CX*8), Z8, K3, Z15               // 6271bd4b6dbcc807000000
	VPUNPCKLDQ X16, X30, K7, X0                        // 62b10d0762c0
	VPUNPCKLDQ 15(R8), X30, K7, X0                     // 62d10d0762800f000000
	VPUNPCKLDQ (BP), X30, K7, X0                       // 62f10d07624500
	VPUNPCKLDQ Y14, Y23, K4, Y1                        // 62d1452462ce
	VPUNPCKLDQ -7(CX), Y23, K4, Y1                     // 62f145246289f9ffffff
	VPUNPCKLDQ 15(DX)(BX*4), Y23, K4, Y1               // 62f14524628c9a0f000000
	VPUNPCKLDQ Z9, Z9, K4, Z9                          // 6251354c62c9
	VPUNPCKLDQ Z28, Z9, K4, Z9                         // 6211354c62cc
	VPUNPCKLDQ 15(R8)(R14*4), Z9, K4, Z9               // 6211354c628cb00f000000
	VPUNPCKLDQ -7(CX)(DX*4), Z9, K4, Z9                // 6271354c628c91f9ffffff
	VPUNPCKLDQ Z9, Z25, K4, Z9                         // 6251354462c9
	VPUNPCKLDQ Z28, Z25, K4, Z9                        // 6211354462cc
	VPUNPCKLDQ 15(R8)(R14*4), Z25, K4, Z9              // 62113544628cb00f000000
	VPUNPCKLDQ -7(CX)(DX*4), Z25, K4, Z9               // 62713544628c91f9ffffff
	VPUNPCKLDQ Z9, Z9, K4, Z3                          // 62d1354c62d9
	VPUNPCKLDQ Z28, Z9, K4, Z3                         // 6291354c62dc
	VPUNPCKLDQ 15(R8)(R14*4), Z9, K4, Z3               // 6291354c629cb00f000000
	VPUNPCKLDQ -7(CX)(DX*4), Z9, K4, Z3                // 62f1354c629c91f9ffffff
	VPUNPCKLDQ Z9, Z25, K4, Z3                         // 62d1354462d9
	VPUNPCKLDQ Z28, Z25, K4, Z3                        // 6291354462dc
	VPUNPCKLDQ 15(R8)(R14*4), Z25, K4, Z3              // 62913544629cb00f000000
	VPUNPCKLDQ -7(CX)(DX*4), Z25, K4, Z3               // 62f13544629c91f9ffffff
	VPUNPCKLQDQ X14, X11, K7, X14                      // 6251a50f6cf6
	VPUNPCKLQDQ 15(R8)(R14*8), X11, K7, X14            // 6211a50f6cb4f00f000000
	VPUNPCKLQDQ -15(R14)(R15*2), X11, K7, X14          // 6211a50f6cb47ef1ffffff
	VPUNPCKLQDQ Y2, Y25, K2, Y31                       // 6261b5226cfa
	VPUNPCKLQDQ 99(R15)(R15*8), Y25, K2, Y31           // 6201b5226cbcff63000000
	VPUNPCKLQDQ 7(AX)(CX*8), Y25, K2, Y31              // 6261b5226cbcc807000000
	VPUNPCKLQDQ Z17, Z17, K5, Z20                      // 62a1f5456ce1
	VPUNPCKLQDQ Z23, Z17, K5, Z20                      // 62a1f5456ce7
	VPUNPCKLQDQ (R8), Z17, K5, Z20                     // 62c1f5456c20
	VPUNPCKLQDQ 15(DX)(BX*2), Z17, K5, Z20             // 62e1f5456ca45a0f000000
	VPUNPCKLQDQ Z17, Z0, K5, Z20                       // 62a1fd4d6ce1
	VPUNPCKLQDQ Z23, Z0, K5, Z20                       // 62a1fd4d6ce7
	VPUNPCKLQDQ (R8), Z0, K5, Z20                      // 62c1fd4d6c20
	VPUNPCKLQDQ 15(DX)(BX*2), Z0, K5, Z20              // 62e1fd4d6ca45a0f000000
	VPUNPCKLQDQ Z17, Z17, K5, Z0                       // 62b1f5456cc1
	VPUNPCKLQDQ Z23, Z17, K5, Z0                       // 62b1f5456cc7
	VPUNPCKLQDQ (R8), Z17, K5, Z0                      // 62d1f5456c00
	VPUNPCKLQDQ 15(DX)(BX*2), Z17, K5, Z0              // 62f1f5456c845a0f000000
	VPUNPCKLQDQ Z17, Z0, K5, Z0                        // 62b1fd4d6cc1
	VPUNPCKLQDQ Z23, Z0, K5, Z0                        // 62b1fd4d6cc7
	VPUNPCKLQDQ (R8), Z0, K5, Z0                       // 62d1fd4d6c00
	VPUNPCKLQDQ 15(DX)(BX*2), Z0, K5, Z0               // 62f1fd4d6c845a0f000000
	VPXORD X12, X23, K2, X26                           // 62414502efd4
	VPXORD 7(AX)(CX*4), X23, K2, X26                   // 62614502ef948807000000
	VPXORD 7(AX)(CX*1), X23, K2, X26                   // 62614502ef940807000000
	VPXORD Y9, Y22, K3, Y9                             // 62514d23efc9
	VPXORD (BX), Y22, K3, Y9                           // 62714d23ef0b
	VPXORD -17(BP)(SI*1), Y22, K3, Y9                  // 62714d23ef8c35efffffff
	VPXORD Z30, Z20, K3, Z1                            // 62915d43efce
	VPXORD Z5, Z20, K3, Z1                             // 62f15d43efcd
	VPXORD -17(BP)(SI*2), Z20, K3, Z1                  // 62f15d43ef8c75efffffff
	VPXORD 7(AX)(CX*2), Z20, K3, Z1                    // 62f15d43ef8c4807000000
	VPXORD Z30, Z9, K3, Z1                             // 6291354befce
	VPXORD Z5, Z9, K3, Z1                              // 62f1354befcd
	VPXORD -17(BP)(SI*2), Z9, K3, Z1                   // 62f1354bef8c75efffffff
	VPXORD 7(AX)(CX*2), Z9, K3, Z1                     // 62f1354bef8c4807000000
	VPXORD Z30, Z20, K3, Z9                            // 62115d43efce
	VPXORD Z5, Z20, K3, Z9                             // 62715d43efcd
	VPXORD -17(BP)(SI*2), Z20, K3, Z9                  // 62715d43ef8c75efffffff
	VPXORD 7(AX)(CX*2), Z20, K3, Z9                    // 62715d43ef8c4807000000
	VPXORD Z30, Z9, K3, Z9                             // 6211354befce
	VPXORD Z5, Z9, K3, Z9                              // 6271354befcd
	VPXORD -17(BP)(SI*2), Z9, K3, Z9                   // 6271354bef8c75efffffff
	VPXORD 7(AX)(CX*2), Z9, K3, Z9                     // 6271354bef8c4807000000
	VPXORQ X23, X23, K3, X16                           // 62a1c503efc7
	VPXORQ (SI), X23, K3, X16                          // 62e1c503ef06
	VPXORQ 7(SI)(DI*2), X23, K3, X16                   // 62e1c503ef847e07000000
	VPXORQ Y6, Y1, K2, Y14                             // 6271f52aeff6
	VPXORQ 15(R8)(R14*4), Y1, K2, Y14                  // 6211f52aefb4b00f000000
	VPXORQ -7(CX)(DX*4), Y1, K2, Y14                   // 6271f52aefb491f9ffffff
	VPXORQ Z16, Z7, K1, Z26                            // 6221c549efd0
	VPXORQ Z25, Z7, K1, Z26                            // 6201c549efd1
	VPXORQ 15(R8)(R14*1), Z7, K1, Z26                  // 6201c549ef94300f000000
	VPXORQ 15(R8)(R14*2), Z7, K1, Z26                  // 6201c549ef94700f000000
	VPXORQ Z16, Z21, K1, Z26                           // 6221d541efd0
	VPXORQ Z25, Z21, K1, Z26                           // 6201d541efd1
	VPXORQ 15(R8)(R14*1), Z21, K1, Z26                 // 6201d541ef94300f000000
	VPXORQ 15(R8)(R14*2), Z21, K1, Z26                 // 6201d541ef94700f000000
	VPXORQ Z16, Z7, K1, Z22                            // 62a1c549eff0
	VPXORQ Z25, Z7, K1, Z22                            // 6281c549eff1
	VPXORQ 15(R8)(R14*1), Z7, K1, Z22                  // 6281c549efb4300f000000
	VPXORQ 15(R8)(R14*2), Z7, K1, Z22                  // 6281c549efb4700f000000
	VPXORQ Z16, Z21, K1, Z22                           // 62a1d541eff0
	VPXORQ Z25, Z21, K1, Z22                           // 6281d541eff1
	VPXORQ 15(R8)(R14*1), Z21, K1, Z22                 // 6281d541efb4300f000000
	VPXORQ 15(R8)(R14*2), Z21, K1, Z22                 // 6281d541efb4700f000000
	VRCP14PD X11, K5, X31                              // 6242fd0d4cfb
	VRCP14PD 17(SP), K5, X31                           // 6262fd0d4cbc2411000000
	VRCP14PD -17(BP)(SI*4), K5, X31                    // 6262fd0d4cbcb5efffffff
	VRCP14PD Y23, K7, Y9                               // 6232fd2f4ccf
	VRCP14PD -17(BP)(SI*2), K7, Y9                     // 6272fd2f4c8c75efffffff
	VRCP14PD 7(AX)(CX*2), K7, Y9                       // 6272fd2f4c8c4807000000
	VRCP14PD Z0, K7, Z6                                // 62f2fd4f4cf0
	VRCP14PD Z8, K7, Z6                                // 62d2fd4f4cf0
	VRCP14PD (CX), K7, Z6                              // 62f2fd4f4c31
	VRCP14PD 99(R15), K7, Z6                           // 62d2fd4f4cb763000000
	VRCP14PD Z0, K7, Z2                                // 62f2fd4f4cd0
	VRCP14PD Z8, K7, Z2                                // 62d2fd4f4cd0
	VRCP14PD (CX), K7, Z2                              // 62f2fd4f4c11
	VRCP14PD 99(R15), K7, Z2                           // 62d2fd4f4c9763000000
	VRCP14PS X5, K6, X22                               // 62e27d0e4cf5
	VRCP14PS 7(AX), K6, X22                            // 62e27d0e4cb007000000
	VRCP14PS (DI), K6, X22                             // 62e27d0e4c37
	VRCP14PS Y5, K3, Y31                               // 62627d2b4cfd
	VRCP14PS 15(R8)(R14*1), K3, Y31                    // 62027d2b4cbc300f000000
	VRCP14PS 15(R8)(R14*2), K3, Y31                    // 62027d2b4cbc700f000000
	VRCP14PS Z14, K7, Z15                              // 62527d4f4cfe
	VRCP14PS Z27, K7, Z15                              // 62127d4f4cfb
	VRCP14PS 99(R15)(R15*2), K7, Z15                   // 62127d4f4cbc7f63000000
	VRCP14PS -7(DI), K7, Z15                           // 62727d4f4cbff9ffffff
	VRCP14PS Z14, K7, Z12                              // 62527d4f4ce6
	VRCP14PS Z27, K7, Z12                              // 62127d4f4ce3
	VRCP14PS 99(R15)(R15*2), K7, Z12                   // 62127d4f4ca47f63000000
	VRCP14PS -7(DI), K7, Z12                           // 62727d4f4ca7f9ffffff
	VRCP14SD X17, X0, K4, X14                          // 6232fd0c4df1 or 6232fd2c4df1 or 6232fd4c4df1
	VRCP14SD (SI), X0, K4, X14                         // 6272fd0c4d36 or 6272fd2c4d36 or 6272fd4c4d36
	VRCP14SD 7(SI)(DI*2), X0, K4, X14                  // 6272fd0c4db47e07000000 or 6272fd2c4db47e07000000 or 6272fd4c4db47e07000000
	VRCP14SS X11, X15, K4, X7                          // 62d2050c4dfb or 62d2052c4dfb or 62d2054c4dfb
	VRCP14SS -7(DI)(R8*1), X15, K4, X7                 // 62b2050c4dbc07f9ffffff or 62b2052c4dbc07f9ffffff or 62b2054c4dbc07f9ffffff
	VRCP14SS (SP), X15, K4, X7                         // 62f2050c4d3c24 or 62f2052c4d3c24 or 62f2054c4d3c24
	VRNDSCALEPD $64, X16, K4, X20                      // 62a3fd0c09e040
	VRNDSCALEPD $64, 7(SI)(DI*8), K4, X20              // 62e3fd0c09a4fe0700000040
	VRNDSCALEPD $64, -15(R14), K4, X20                 // 62c3fd0c09a6f1ffffff40
	VRNDSCALEPD $27, Y2, K1, Y28                       // 6263fd2909e21b
	VRNDSCALEPD $27, (CX), K1, Y28                     // 6263fd2909211b
	VRNDSCALEPD $27, 99(R15), K1, Y28                  // 6243fd2909a7630000001b
	VRNDSCALEPD $47, Z21, K3, Z8                       // 6233fd4b09c52f
	VRNDSCALEPD $47, Z5, K3, Z8                        // 6273fd4b09c52f
	VRNDSCALEPD $47, Z21, K3, Z28                      // 6223fd4b09e52f
	VRNDSCALEPD $47, Z5, K3, Z28                       // 6263fd4b09e52f
	VRNDSCALEPD $82, Z12, K4, Z16                      // 62c3fd4c09c452
	VRNDSCALEPD $82, Z27, K4, Z16                      // 6283fd4c09c352
	VRNDSCALEPD $82, 15(R8), K4, Z16                   // 62c3fd4c09800f00000052
	VRNDSCALEPD $82, (BP), K4, Z16                     // 62e3fd4c09450052
	VRNDSCALEPD $82, Z12, K4, Z13                      // 6253fd4c09ec52
	VRNDSCALEPD $82, Z27, K4, Z13                      // 6213fd4c09eb52
	VRNDSCALEPD $82, 15(R8), K4, Z13                   // 6253fd4c09a80f00000052
	VRNDSCALEPD $82, (BP), K4, Z13                     // 6273fd4c096d0052
	VRNDSCALEPS $126, X6, K5, X12                      // 62737d0d08e67e
	VRNDSCALEPS $126, 7(SI)(DI*1), K5, X12             // 62737d0d08a43e070000007e
	VRNDSCALEPS $126, 15(DX)(BX*8), K5, X12            // 62737d0d08a4da0f0000007e
	VRNDSCALEPS $94, Y27, K7, Y24                      // 62037d2f08c35e
	VRNDSCALEPS $94, 99(R15)(R15*2), K7, Y24           // 62037d2f08847f630000005e
	VRNDSCALEPS $94, -7(DI), K7, Y24                   // 62637d2f0887f9ffffff5e
	VRNDSCALEPS $121, Z6, K7, Z22                      // 62e37d4f08f679
	VRNDSCALEPS $121, Z8, K7, Z22                      // 62c37d4f08f079
	VRNDSCALEPS $121, Z6, K7, Z11                      // 62737d4f08de79
	VRNDSCALEPS $121, Z8, K7, Z11                      // 62537d4f08d879
	VRNDSCALEPS $13, Z12, K6, Z25                      // 62437d4e08cc0d
	VRNDSCALEPS $13, Z17, K6, Z25                      // 62237d4e08c90d
	VRNDSCALEPS $13, 15(R8)(R14*8), K6, Z25            // 62037d4e088cf00f0000000d
	VRNDSCALEPS $13, -15(R14)(R15*2), K6, Z25          // 62037d4e088c7ef1ffffff0d
	VRNDSCALEPS $13, Z12, K6, Z12                      // 62537d4e08e40d
	VRNDSCALEPS $13, Z17, K6, Z12                      // 62337d4e08e10d
	VRNDSCALEPS $13, 15(R8)(R14*8), K6, Z12            // 62137d4e08a4f00f0000000d
	VRNDSCALEPS $13, -15(R14)(R15*2), K6, Z12          // 62137d4e08a47ef1ffffff0d
	VRNDSCALESD $65, X6, X28, K3, X17                  // 62e39d030bce41
	VRNDSCALESD $67, X8, X8, K7, X1                    // 62d3bd0f0bc843 or 62d3bd2f0bc843 or 62d3bd4f0bc843
	VRNDSCALESD $67, 17(SP), X8, K7, X1                // 62f3bd0f0b8c241100000043 or 62f3bd2f0b8c241100000043 or 62f3bd4f0b8c241100000043
	VRNDSCALESD $67, -17(BP)(SI*4), X8, K7, X1         // 62f3bd0f0b8cb5efffffff43 or 62f3bd2f0b8cb5efffffff43 or 62f3bd4f0b8cb5efffffff43
	VRNDSCALESS $127, X11, X0, K4, X6                  // 62d37d0c0af37f
	VRNDSCALESS $0, X6, X6, K4, X16                    // 62e34d0c0ac600 or 62e34d2c0ac600 or 62e34d4c0ac600
	VRNDSCALESS $0, (AX), X6, K4, X16                  // 62e34d0c0a0000 or 62e34d2c0a0000 or 62e34d4c0a0000
	VRNDSCALESS $0, 7(SI), X6, K4, X16                 // 62e34d0c0a860700000000 or 62e34d2c0a860700000000 or 62e34d4c0a860700000000
	VRSQRT14PD X12, K7, X22                            // 62c2fd0f4ef4
	VRSQRT14PD -7(DI)(R8*1), K7, X22                   // 62a2fd0f4eb407f9ffffff
	VRSQRT14PD (SP), K7, X22                           // 62e2fd0f4e3424
	VRSQRT14PD Y11, K2, Y0                             // 62d2fd2a4ec3
	VRSQRT14PD -7(CX)(DX*1), K2, Y0                    // 62f2fd2a4e8411f9ffffff
	VRSQRT14PD -15(R14)(R15*4), K2, Y0                 // 6292fd2a4e84bef1ffffff
	VRSQRT14PD Z6, K5, Z9                              // 6272fd4d4ece
	VRSQRT14PD Z25, K5, Z9                             // 6212fd4d4ec9
	VRSQRT14PD -15(R14)(R15*1), K5, Z9                 // 6212fd4d4e8c3ef1ffffff
	VRSQRT14PD -15(BX), K5, Z9                         // 6272fd4d4e8bf1ffffff
	VRSQRT14PD Z6, K5, Z12                             // 6272fd4d4ee6
	VRSQRT14PD Z25, K5, Z12                            // 6212fd4d4ee1
	VRSQRT14PD -15(R14)(R15*1), K5, Z12                // 6212fd4d4ea43ef1ffffff
	VRSQRT14PD -15(BX), K5, Z12                        // 6272fd4d4ea3f1ffffff
	VRSQRT14PS X28, K3, X16                            // 62827d0b4ec4
	VRSQRT14PS -7(CX), K3, X16                         // 62e27d0b4e81f9ffffff
	VRSQRT14PS 15(DX)(BX*4), K3, X16                   // 62e27d0b4e849a0f000000
	VRSQRT14PS Y3, K4, Y31                             // 62627d2c4efb
	VRSQRT14PS 15(DX)(BX*1), K4, Y31                   // 62627d2c4ebc1a0f000000
	VRSQRT14PS -7(CX)(DX*2), K4, Y31                   // 62627d2c4ebc51f9ffffff
	VRSQRT14PS Z8, K2, Z3                              // 62d27d4a4ed8
	VRSQRT14PS Z2, K2, Z3                              // 62f27d4a4eda
	VRSQRT14PS 7(AX)(CX*4), K2, Z3                     // 62f27d4a4e9c8807000000
	VRSQRT14PS 7(AX)(CX*1), K2, Z3                     // 62f27d4a4e9c0807000000
	VRSQRT14PS Z8, K2, Z21                             // 62c27d4a4ee8
	VRSQRT14PS Z2, K2, Z21                             // 62e27d4a4eea
	VRSQRT14PS 7(AX)(CX*4), K2, Z21                    // 62e27d4a4eac8807000000
	VRSQRT14PS 7(AX)(CX*1), K2, Z21                    // 62e27d4a4eac0807000000
	VRSQRT14SD X11, X15, K2, X8                        // 6252850a4fc3 or 6252852a4fc3 or 6252854a4fc3
	VRSQRT14SD 7(AX), X15, K2, X8                      // 6272850a4f8007000000 or 6272852a4f8007000000 or 6272854a4f8007000000
	VRSQRT14SD (DI), X15, K2, X8                       // 6272850a4f07 or 6272852a4f07 or 6272854a4f07
	VRSQRT14SS X13, X19, K3, X1                        // 62d265034fcd or 62d265234fcd or 62d265434fcd
	VRSQRT14SS (BX), X19, K3, X1                       // 62f265034f0b or 62f265234f0b or 62f265434f0b
	VRSQRT14SS -17(BP)(SI*1), X19, K3, X1              // 62f265034f8c35efffffff or 62f265234f8c35efffffff or 62f265434f8c35efffffff
	VSCALEFPD X27, X2, K1, X2                          // 6292ed092cd3
	VSCALEFPD 99(R15)(R15*8), X2, K1, X2               // 6292ed092c94ff63000000
	VSCALEFPD 7(AX)(CX*8), X2, K1, X2                  // 62f2ed092c94c807000000
	VSCALEFPD Y13, Y2, K1, Y14                         // 6252ed292cf5
	VSCALEFPD -17(BP), Y2, K1, Y14                     // 6272ed292cb5efffffff
	VSCALEFPD -15(R14)(R15*8), Y2, K1, Y14             // 6212ed292cb4fef1ffffff
	VSCALEFPD Z7, Z2, K7, Z18                          // 62e2ed4f2cd7
	VSCALEFPD Z13, Z2, K7, Z18                         // 62c2ed4f2cd5
	VSCALEFPD Z7, Z21, K7, Z18                         // 62e2d5472cd7
	VSCALEFPD Z13, Z21, K7, Z18                        // 62c2d5472cd5
	VSCALEFPD Z7, Z2, K7, Z24                          // 6262ed4f2cc7
	VSCALEFPD Z13, Z2, K7, Z24                         // 6242ed4f2cc5
	VSCALEFPD Z7, Z21, K7, Z24                         // 6262d5472cc7
	VSCALEFPD Z13, Z21, K7, Z24                        // 6242d5472cc5
	VSCALEFPD Z1, Z6, K2, Z6                           // 62f2cd4a2cf1
	VSCALEFPD Z15, Z6, K2, Z6                          // 62d2cd4a2cf7
	VSCALEFPD 7(SI)(DI*4), Z6, K2, Z6                  // 62f2cd4a2cb4be07000000
	VSCALEFPD -7(DI)(R8*2), Z6, K2, Z6                 // 62b2cd4a2cb447f9ffffff
	VSCALEFPD Z1, Z22, K2, Z6                          // 62f2cd422cf1
	VSCALEFPD Z15, Z22, K2, Z6                         // 62d2cd422cf7
	VSCALEFPD 7(SI)(DI*4), Z22, K2, Z6                 // 62f2cd422cb4be07000000
	VSCALEFPD -7(DI)(R8*2), Z22, K2, Z6                // 62b2cd422cb447f9ffffff
	VSCALEFPD Z1, Z6, K2, Z16                          // 62e2cd4a2cc1
	VSCALEFPD Z15, Z6, K2, Z16                         // 62c2cd4a2cc7
	VSCALEFPD 7(SI)(DI*4), Z6, K2, Z16                 // 62e2cd4a2c84be07000000
	VSCALEFPD -7(DI)(R8*2), Z6, K2, Z16                // 62a2cd4a2c8447f9ffffff
	VSCALEFPD Z1, Z22, K2, Z16                         // 62e2cd422cc1
	VSCALEFPD Z15, Z22, K2, Z16                        // 62c2cd422cc7
	VSCALEFPD 7(SI)(DI*4), Z22, K2, Z16                // 62e2cd422c84be07000000
	VSCALEFPD -7(DI)(R8*2), Z22, K2, Z16               // 62a2cd422c8447f9ffffff
	VSCALEFPS X30, X22, K4, X26                        // 62024d042cd6
	VSCALEFPS (AX), X22, K4, X26                       // 62624d042c10
	VSCALEFPS 7(SI), X22, K4, X26                      // 62624d042c9607000000
	VSCALEFPS Y22, Y15, K1, Y27                        // 622205292cde
	VSCALEFPS 17(SP)(BP*2), Y15, K1, Y27               // 626205292c9c6c11000000
	VSCALEFPS -7(DI)(R8*4), Y15, K1, Y27               // 622205292c9c87f9ffffff
	VSCALEFPS Z22, Z18, K3, Z13                        // 62326d432cee
	VSCALEFPS Z7, Z18, K3, Z13                         // 62726d432cef
	VSCALEFPS Z22, Z8, K3, Z13                         // 62323d4b2cee
	VSCALEFPS Z7, Z8, K3, Z13                          // 62723d4b2cef
	VSCALEFPS Z1, Z20, K4, Z2                          // 62f25d442cd1
	VSCALEFPS Z3, Z20, K4, Z2                          // 62f25d442cd3
	VSCALEFPS 17(SP), Z20, K4, Z2                      // 62f25d442c942411000000
	VSCALEFPS -17(BP)(SI*4), Z20, K4, Z2               // 62f25d442c94b5efffffff
	VSCALEFPS Z1, Z9, K4, Z2                           // 62f2354c2cd1
	VSCALEFPS Z3, Z9, K4, Z2                           // 62f2354c2cd3
	VSCALEFPS 17(SP), Z9, K4, Z2                       // 62f2354c2c942411000000
	VSCALEFPS -17(BP)(SI*4), Z9, K4, Z2                // 62f2354c2c94b5efffffff
	VSCALEFPS Z1, Z20, K4, Z31                         // 62625d442cf9
	VSCALEFPS Z3, Z20, K4, Z31                         // 62625d442cfb
	VSCALEFPS 17(SP), Z20, K4, Z31                     // 62625d442cbc2411000000
	VSCALEFPS -17(BP)(SI*4), Z20, K4, Z31              // 62625d442cbcb5efffffff
	VSCALEFPS Z1, Z9, K4, Z31                          // 6262354c2cf9
	VSCALEFPS Z3, Z9, K4, Z31                          // 6262354c2cfb
	VSCALEFPS 17(SP), Z9, K4, Z31                      // 6262354c2cbc2411000000
	VSCALEFPS -17(BP)(SI*4), Z9, K4, Z31               // 6262354c2cbcb5efffffff
	VSCALEFSD X15, X11, K5, X3                         // 62d2a50d2ddf
	VSCALEFSD X6, X13, K7, X30                         // 6262950f2df6 or 6262952f2df6 or 6262954f2df6
	VSCALEFSD -17(BP)(SI*8), X13, K7, X30              // 6262950f2db4f5efffffff or 6262952f2db4f5efffffff or 6262954f2db4f5efffffff
	VSCALEFSD (R15), X13, K7, X30                      // 6242950f2d37 or 6242952f2d37 or 6242954f2d37
	VSCALEFSS X30, X23, K7, X12                        // 621245072de6
	VSCALEFSS X2, X20, K6, X8                          // 62725d062dc2 or 62725d262dc2 or 62725d462dc2
	VSCALEFSS (R8), X20, K6, X8                        // 62525d062d00 or 62525d262d00 or 62525d462d00
	VSCALEFSS 15(DX)(BX*2), X20, K6, X8                // 62725d062d845a0f000000 or 62725d262d845a0f000000 or 62725d462d845a0f000000
	VSCATTERDPD X9, K3, (DX)(X10*4)                    // 6232fd0ba20c92
	VSCATTERDPD X9, K3, (SP)(X4*2)                     // 6272fd0ba20c64
	VSCATTERDPD X9, K3, (R14)(X29*8)                   // 6212fd03a20cee
	VSCATTERDPD Y20, K7, (AX)(X4*1)                    // 62e2fd2fa22420
	VSCATTERDPD Y20, K7, (BP)(X10*2)                   // 62a2fd2fa2645500
	VSCATTERDPD Y20, K7, (R10)(X29*8)                  // 6282fd27a224ea
	VSCATTERDPD Z12, K4, (R10)(Y29*8)                  // 6212fd44a224ea
	VSCATTERDPD Z16, K4, (R10)(Y29*8)                  // 6282fd44a204ea
	VSCATTERDPD Z12, K4, (SP)(Y4*2)                    // 6272fd4ca22464
	VSCATTERDPD Z16, K4, (SP)(Y4*2)                    // 62e2fd4ca20464
	VSCATTERDPD Z12, K4, (DX)(Y10*4)                   // 6232fd4ca22492
	VSCATTERDPD Z16, K4, (DX)(Y10*4)                   // 62a2fd4ca20492
	VSCATTERDPS X26, K4, (DX)(X10*4)                   // 62227d0ca21492
	VSCATTERDPS X26, K4, (SP)(X4*2)                    // 62627d0ca21464
	VSCATTERDPS X26, K4, (R14)(X29*8)                  // 62027d04a214ee
	VSCATTERDPS Y18, K7, (R14)(Y29*8)                  // 62827d27a214ee
	VSCATTERDPS Y18, K7, (AX)(Y4*1)                    // 62e27d2fa21420
	VSCATTERDPS Y18, K7, (BP)(Y10*2)                   // 62a27d2fa2545500
	VSCATTERDPS Z28, K2, (BP)(Z10*2)                   // 62227d4aa2645500
	VSCATTERDPS Z13, K2, (BP)(Z10*2)                   // 62327d4aa26c5500
	VSCATTERDPS Z28, K2, (R10)(Z29*8)                  // 62027d42a224ea
	VSCATTERDPS Z13, K2, (R10)(Z29*8)                  // 62127d42a22cea
	VSCATTERDPS Z28, K2, (R14)(Z29*8)                  // 62027d42a224ee
	VSCATTERDPS Z13, K2, (R14)(Z29*8)                  // 62127d42a22cee
	VSCATTERQPD X19, K2, (AX)(X4*1)                    // 62e2fd0aa31c20
	VSCATTERQPD X19, K2, (BP)(X10*2)                   // 62a2fd0aa35c5500
	VSCATTERQPD X19, K2, (R10)(X29*8)                  // 6282fd02a31cea
	VSCATTERQPD Y24, K1, (R10)(Y29*8)                  // 6202fd21a304ea
	VSCATTERQPD Y24, K1, (SP)(Y4*2)                    // 6262fd29a30464
	VSCATTERQPD Y24, K1, (DX)(Y10*4)                   // 6222fd29a30492
	VSCATTERQPD Z14, K2, (DX)(Z10*4)                   // 6232fd4aa33492
	VSCATTERQPD Z28, K2, (DX)(Z10*4)                   // 6222fd4aa32492
	VSCATTERQPD Z14, K2, (AX)(Z4*1)                    // 6272fd4aa33420
	VSCATTERQPD Z28, K2, (AX)(Z4*1)                    // 6262fd4aa32420
	VSCATTERQPD Z14, K2, (SP)(Z4*2)                    // 6272fd4aa33464
	VSCATTERQPD Z28, K2, (SP)(Z4*2)                    // 6262fd4aa32464
	VSCATTERQPS X0, K1, (DX)(X10*4)                    // 62b27d09a30492
	VSCATTERQPS X0, K1, (SP)(X4*2)                     // 62f27d09a30464
	VSCATTERQPS X0, K1, (R14)(X29*8)                   // 62927d01a304ee
	VSCATTERQPS X31, K7, (R14)(Y29*8)                  // 62027d27a33cee
	VSCATTERQPS X31, K7, (AX)(Y4*1)                    // 62627d2fa33c20
	VSCATTERQPS X31, K7, (BP)(Y10*2)                   // 62227d2fa37c5500
	VSCATTERQPS Y9, K1, (BP)(Z10*2)                    // 62327d49a34c5500
	VSCATTERQPS Y9, K1, (R10)(Z29*8)                   // 62127d41a30cea
	VSCATTERQPS Y9, K1, (R14)(Z29*8)                   // 62127d41a30cee
	VSHUFF32X4 $97, Y23, Y19, K1, Y3                   // 62b3652123df61
	VSHUFF32X4 $97, 15(R8), Y19, K1, Y3                // 62d3652123980f00000061
	VSHUFF32X4 $97, (BP), Y19, K1, Y3                  // 62f36521235d0061
	VSHUFF32X4 $81, Z19, Z15, K1, Z3                   // 62b3054923db51
	VSHUFF32X4 $81, Z15, Z15, K1, Z3                   // 62d3054923df51
	VSHUFF32X4 $81, 7(AX), Z15, K1, Z3                 // 62f3054923980700000051
	VSHUFF32X4 $81, (DI), Z15, K1, Z3                  // 62f30549231f51
	VSHUFF32X4 $81, Z19, Z30, K1, Z3                   // 62b30d4123db51
	VSHUFF32X4 $81, Z15, Z30, K1, Z3                   // 62d30d4123df51
	VSHUFF32X4 $81, 7(AX), Z30, K1, Z3                 // 62f30d4123980700000051
	VSHUFF32X4 $81, (DI), Z30, K1, Z3                  // 62f30d41231f51
	VSHUFF32X4 $81, Z19, Z15, K1, Z12                  // 6233054923e351
	VSHUFF32X4 $81, Z15, Z15, K1, Z12                  // 6253054923e751
	VSHUFF32X4 $81, 7(AX), Z15, K1, Z12                // 6273054923a00700000051
	VSHUFF32X4 $81, (DI), Z15, K1, Z12                 // 62730549232751
	VSHUFF32X4 $81, Z19, Z30, K1, Z12                  // 62330d4123e351
	VSHUFF32X4 $81, Z15, Z30, K1, Z12                  // 62530d4123e751
	VSHUFF32X4 $81, 7(AX), Z30, K1, Z12                // 62730d4123a00700000051
	VSHUFF32X4 $81, (DI), Z30, K1, Z12                 // 62730d41232751
	VSHUFF64X2 $42, Y21, Y14, K7, Y19                  // 62a38d2f23dd2a
	VSHUFF64X2 $42, 15(R8)(R14*8), Y14, K7, Y19        // 62838d2f239cf00f0000002a
	VSHUFF64X2 $42, -15(R14)(R15*2), Y14, K7, Y19      // 62838d2f239c7ef1ffffff2a
	VSHUFF64X2 $79, Z14, Z3, K2, Z5                    // 62d3e54a23ee4f
	VSHUFF64X2 $79, Z15, Z3, K2, Z5                    // 62d3e54a23ef4f
	VSHUFF64X2 $79, 99(R15)(R15*1), Z3, K2, Z5         // 6293e54a23ac3f630000004f
	VSHUFF64X2 $79, (DX), Z3, K2, Z5                   // 62f3e54a232a4f
	VSHUFF64X2 $79, Z14, Z5, K2, Z5                    // 62d3d54a23ee4f
	VSHUFF64X2 $79, Z15, Z5, K2, Z5                    // 62d3d54a23ef4f
	VSHUFF64X2 $79, 99(R15)(R15*1), Z5, K2, Z5         // 6293d54a23ac3f630000004f
	VSHUFF64X2 $79, (DX), Z5, K2, Z5                   // 62f3d54a232a4f
	VSHUFF64X2 $79, Z14, Z3, K2, Z1                    // 62d3e54a23ce4f
	VSHUFF64X2 $79, Z15, Z3, K2, Z1                    // 62d3e54a23cf4f
	VSHUFF64X2 $79, 99(R15)(R15*1), Z3, K2, Z1         // 6293e54a238c3f630000004f
	VSHUFF64X2 $79, (DX), Z3, K2, Z1                   // 62f3e54a230a4f
	VSHUFF64X2 $79, Z14, Z5, K2, Z1                    // 62d3d54a23ce4f
	VSHUFF64X2 $79, Z15, Z5, K2, Z1                    // 62d3d54a23cf4f
	VSHUFF64X2 $79, 99(R15)(R15*1), Z5, K2, Z1         // 6293d54a238c3f630000004f
	VSHUFF64X2 $79, (DX), Z5, K2, Z1                   // 62f3d54a230a4f
	VSHUFI32X4 $64, Y2, Y16, K4, Y5                    // 62f37d2443ea40
	VSHUFI32X4 $64, -15(R14)(R15*1), Y16, K4, Y5       // 62937d2443ac3ef1ffffff40
	VSHUFI32X4 $64, -15(BX), Y16, K4, Y5               // 62f37d2443abf1ffffff40
	VSHUFI32X4 $27, Z20, Z16, K1, Z21                  // 62a37d4143ec1b
	VSHUFI32X4 $27, Z0, Z16, K1, Z21                   // 62e37d4143e81b
	VSHUFI32X4 $27, -17(BP)(SI*8), Z16, K1, Z21        // 62e37d4143acf5efffffff1b
	VSHUFI32X4 $27, (R15), Z16, K1, Z21                // 62c37d41432f1b
	VSHUFI32X4 $27, Z20, Z9, K1, Z21                   // 62a3354943ec1b
	VSHUFI32X4 $27, Z0, Z9, K1, Z21                    // 62e3354943e81b
	VSHUFI32X4 $27, -17(BP)(SI*8), Z9, K1, Z21         // 62e3354943acf5efffffff1b
	VSHUFI32X4 $27, (R15), Z9, K1, Z21                 // 62c33549432f1b
	VSHUFI32X4 $27, Z20, Z16, K1, Z8                   // 62337d4143c41b
	VSHUFI32X4 $27, Z0, Z16, K1, Z8                    // 62737d4143c01b
	VSHUFI32X4 $27, -17(BP)(SI*8), Z16, K1, Z8         // 62737d414384f5efffffff1b
	VSHUFI32X4 $27, (R15), Z16, K1, Z8                 // 62537d4143071b
	VSHUFI32X4 $27, Z20, Z9, K1, Z8                    // 6233354943c41b
	VSHUFI32X4 $27, Z0, Z9, K1, Z8                     // 6273354943c01b
	VSHUFI32X4 $27, -17(BP)(SI*8), Z9, K1, Z8          // 627335494384f5efffffff1b
	VSHUFI32X4 $27, (R15), Z9, K1, Z8                  // 6253354943071b
	VSHUFI64X2 $47, Y6, Y20, K3, Y21                   // 62e3dd2343ee2f
	VSHUFI64X2 $47, 7(AX)(CX*4), Y20, K3, Y21          // 62e3dd2343ac88070000002f
	VSHUFI64X2 $47, 7(AX)(CX*1), Y20, K3, Y21          // 62e3dd2343ac08070000002f
	VSHUFI64X2 $82, Z0, Z0, K4, Z23                    // 62e3fd4c43f852
	VSHUFI64X2 $82, Z25, Z0, K4, Z23                   // 6283fd4c43f952
	VSHUFI64X2 $82, 7(SI)(DI*8), Z0, K4, Z23           // 62e3fd4c43bcfe0700000052
	VSHUFI64X2 $82, -15(R14), Z0, K4, Z23              // 62c3fd4c43bef1ffffff52
	VSHUFI64X2 $82, Z0, Z11, K4, Z23                   // 62e3a54c43f852
	VSHUFI64X2 $82, Z25, Z11, K4, Z23                  // 6283a54c43f952
	VSHUFI64X2 $82, 7(SI)(DI*8), Z11, K4, Z23          // 62e3a54c43bcfe0700000052
	VSHUFI64X2 $82, -15(R14), Z11, K4, Z23             // 62c3a54c43bef1ffffff52
	VSHUFI64X2 $82, Z0, Z0, K4, Z19                    // 62e3fd4c43d852
	VSHUFI64X2 $82, Z25, Z0, K4, Z19                   // 6283fd4c43d952
	VSHUFI64X2 $82, 7(SI)(DI*8), Z0, K4, Z19           // 62e3fd4c439cfe0700000052
	VSHUFI64X2 $82, -15(R14), Z0, K4, Z19              // 62c3fd4c439ef1ffffff52
	VSHUFI64X2 $82, Z0, Z11, K4, Z19                   // 62e3a54c43d852
	VSHUFI64X2 $82, Z25, Z11, K4, Z19                  // 6283a54c43d952
	VSHUFI64X2 $82, 7(SI)(DI*8), Z11, K4, Z19          // 62e3a54c439cfe0700000052
	VSHUFI64X2 $82, -15(R14), Z11, K4, Z19             // 62c3a54c439ef1ffffff52
	VSHUFPD $126, X8, X7, K5, X16                      // 62c1c50dc6c07e
	VSHUFPD $126, (BX), X7, K5, X16                    // 62e1c50dc6037e
	VSHUFPD $126, -17(BP)(SI*1), X7, K5, X16           // 62e1c50dc68435efffffff7e
	VSHUFPD $94, Y11, Y6, K7, Y31                      // 6241cd2fc6fb5e
	VSHUFPD $94, (SI), Y6, K7, Y31                     // 6261cd2fc63e5e
	VSHUFPD $94, 7(SI)(DI*2), Y6, K7, Y31              // 6261cd2fc6bc7e070000005e
	VSHUFPD $121, Z9, Z0, K7, Z24                      // 6241fd4fc6c179
	VSHUFPD $121, Z3, Z0, K7, Z24                      // 6261fd4fc6c379
	VSHUFPD $121, 7(SI)(DI*1), Z0, K7, Z24             // 6261fd4fc6843e0700000079
	VSHUFPD $121, 15(DX)(BX*8), Z0, K7, Z24            // 6261fd4fc684da0f00000079
	VSHUFPD $121, Z9, Z26, K7, Z24                     // 6241ad47c6c179
	VSHUFPD $121, Z3, Z26, K7, Z24                     // 6261ad47c6c379
	VSHUFPD $121, 7(SI)(DI*1), Z26, K7, Z24            // 6261ad47c6843e0700000079
	VSHUFPD $121, 15(DX)(BX*8), Z26, K7, Z24           // 6261ad47c684da0f00000079
	VSHUFPD $121, Z9, Z0, K7, Z12                      // 6251fd4fc6e179
	VSHUFPD $121, Z3, Z0, K7, Z12                      // 6271fd4fc6e379
	VSHUFPD $121, 7(SI)(DI*1), Z0, K7, Z12             // 6271fd4fc6a43e0700000079
	VSHUFPD $121, 15(DX)(BX*8), Z0, K7, Z12            // 6271fd4fc6a4da0f00000079
	VSHUFPD $121, Z9, Z26, K7, Z12                     // 6251ad47c6e179
	VSHUFPD $121, Z3, Z26, K7, Z12                     // 6271ad47c6e379
	VSHUFPD $121, 7(SI)(DI*1), Z26, K7, Z12            // 6271ad47c6a43e0700000079
	VSHUFPD $121, 15(DX)(BX*8), Z26, K7, Z12           // 6271ad47c6a4da0f00000079
	VSHUFPS $13, X15, X0, K6, X1                       // 62d17c0ec6cf0d
	VSHUFPS $13, 15(R8)(R14*4), X0, K6, X1             // 62917c0ec68cb00f0000000d
	VSHUFPS $13, -7(CX)(DX*4), X0, K6, X1              // 62f17c0ec68c91f9ffffff0d
	VSHUFPS $65, Y6, Y7, K3, Y19                       // 62e1442bc6de41
	VSHUFPS $65, 17(SP)(BP*8), Y7, K3, Y19             // 62e1442bc69cec1100000041
	VSHUFPS $65, 17(SP)(BP*4), Y7, K3, Y19             // 62e1442bc69cac1100000041
	VSHUFPS $67, Z20, Z9, K7, Z9                       // 6231344fc6cc43
	VSHUFPS $67, Z0, Z9, K7, Z9                        // 6271344fc6c843
	VSHUFPS $67, -7(DI)(R8*1), Z9, K7, Z9              // 6231344fc68c07f9ffffff43
	VSHUFPS $67, (SP), Z9, K7, Z9                      // 6271344fc60c2443
	VSHUFPS $67, Z20, Z28, K7, Z9                      // 62311c47c6cc43
	VSHUFPS $67, Z0, Z28, K7, Z9                       // 62711c47c6c843
	VSHUFPS $67, -7(DI)(R8*1), Z28, K7, Z9             // 62311c47c68c07f9ffffff43
	VSHUFPS $67, (SP), Z28, K7, Z9                     // 62711c47c60c2443
	VSHUFPS $67, Z20, Z9, K7, Z25                      // 6221344fc6cc43
	VSHUFPS $67, Z0, Z9, K7, Z25                       // 6261344fc6c843
	VSHUFPS $67, -7(DI)(R8*1), Z9, K7, Z25             // 6221344fc68c07f9ffffff43
	VSHUFPS $67, (SP), Z9, K7, Z25                     // 6261344fc60c2443
	VSHUFPS $67, Z20, Z28, K7, Z25                     // 62211c47c6cc43
	VSHUFPS $67, Z0, Z28, K7, Z25                      // 62611c47c6c843
	VSHUFPS $67, -7(DI)(R8*1), Z28, K7, Z25            // 62211c47c68c07f9ffffff43
	VSHUFPS $67, (SP), Z28, K7, Z25                    // 62611c47c60c2443
	VSQRTPD X16, K4, X0                                // 62b1fd0c51c0
	VSQRTPD (R8), K4, X0                               // 62d1fd0c5100
	VSQRTPD 15(DX)(BX*2), K4, X0                       // 62f1fd0c51845a0f000000
	VSQRTPD Y3, K4, Y0                                 // 62f1fd2c51c3
	VSQRTPD 7(SI)(DI*4), K4, Y0                        // 62f1fd2c5184be07000000
	VSQRTPD -7(DI)(R8*2), K4, Y0                       // 62b1fd2c518447f9ffffff
	VSQRTPD Z17, K7, Z17                               // 62a1fd4f51c9
	VSQRTPD Z23, K7, Z17                               // 62a1fd4f51cf
	VSQRTPD Z17, K7, Z0                                // 62b1fd4f51c1
	VSQRTPD Z23, K7, Z0                                // 62b1fd4f51c7
	VSQRTPD Z21, K2, Z31                               // 6221fd4a51fd
	VSQRTPD Z9, K2, Z31                                // 6241fd4a51f9
	VSQRTPD -7(CX), K2, Z31                            // 6261fd4a51b9f9ffffff
	VSQRTPD 15(DX)(BX*4), K2, Z31                      // 6261fd4a51bc9a0f000000
	VSQRTPD Z21, K2, Z0                                // 62b1fd4a51c5
	VSQRTPD Z9, K2, Z0                                 // 62d1fd4a51c1
	VSQRTPD -7(CX), K2, Z0                             // 62f1fd4a5181f9ffffff
	VSQRTPD 15(DX)(BX*4), K2, Z0                       // 62f1fd4a51849a0f000000
	VSQRTPS X0, K5, X21                                // 62e17c0d51e8
	VSQRTPS 17(SP)(BP*1), K5, X21                      // 62e17c0d51ac2c11000000
	VSQRTPS -7(CX)(DX*8), K5, X21                      // 62e17c0d51acd1f9ffffff
	VSQRTPS Y20, K3, Y5                                // 62b17c2b51ec
	VSQRTPS 17(SP), K3, Y5                             // 62f17c2b51ac2411000000
	VSQRTPS -17(BP)(SI*4), K3, Y5                      // 62f17c2b51acb5efffffff
	VSQRTPS Z1, K4, Z6                                 // 62f17c4c51f1
	VSQRTPS Z9, K4, Z6                                 // 62d17c4c51f1
	VSQRTPS Z1, K4, Z9                                 // 62717c4c51c9
	VSQRTPS Z9, K4, Z9                                 // 62517c4c51c9
	VSQRTPS Z30, K2, Z20                               // 62817c4a51e6
	VSQRTPS Z5, K2, Z20                                // 62e17c4a51e5
	VSQRTPS 99(R15)(R15*8), K2, Z20                    // 62817c4a51a4ff63000000
	VSQRTPS 7(AX)(CX*8), K2, Z20                       // 62e17c4a51a4c807000000
	VSQRTPS Z30, K2, Z9                                // 62117c4a51ce
	VSQRTPS Z5, K2, Z9                                 // 62717c4a51cd
	VSQRTPS 99(R15)(R15*8), K2, Z9                     // 62117c4a518cff63000000
	VSQRTPS 7(AX)(CX*8), K2, Z9                        // 62717c4a518cc807000000
	VSQRTSD X7, X22, K2, X28                           // 6261cf0251e7
	VSQRTSD X16, X7, K3, X19                           // 62a1c70b51d8 or 62a1c72b51d8 or 62a1c74b51d8
	VSQRTSD 7(SI)(DI*8), X7, K3, X19                   // 62e1c70b519cfe07000000 or 62e1c72b519cfe07000000 or 62e1c74b519cfe07000000
	VSQRTSD -15(R14), X7, K3, X19                      // 62c1c70b519ef1ffffff or 62c1c72b519ef1ffffff or 62c1c74b519ef1ffffff
	VSQRTSS X7, X1, K3, X31                            // 6261760b51ff
	VSQRTSS X12, X15, K3, X9                           // 6251060b51cc or 6251062b51cc or 6251064b51cc
	VSQRTSS 17(SP)(BP*1), X15, K3, X9                  // 6271060b518c2c11000000 or 6271062b518c2c11000000 or 6271064b518c2c11000000
	VSQRTSS -7(CX)(DX*8), X15, K3, X9                  // 6271060b518cd1f9ffffff or 6271062b518cd1f9ffffff or 6271064b518cd1f9ffffff
	VSUBPD X14, X12, K2, X0                            // 62d19d0a5cc6
	VSUBPD -17(BP)(SI*2), X12, K2, X0                  // 62f19d0a5c8475efffffff
	VSUBPD 7(AX)(CX*2), X12, K2, X0                    // 62f19d0a5c844807000000
	VSUBPD Y5, Y3, K1, Y12                             // 6271e5295ce5
	VSUBPD 7(AX), Y3, K1, Y12                          // 6271e5295ca007000000
	VSUBPD (DI), Y3, K1, Y12                           // 6271e5295c27
	VSUBPD Z16, Z7, K2, Z26                            // 6221c54a5cd0
	VSUBPD Z25, Z7, K2, Z26                            // 6201c54a5cd1
	VSUBPD Z16, Z21, K2, Z26                           // 6221d5425cd0
	VSUBPD Z25, Z21, K2, Z26                           // 6201d5425cd1
	VSUBPD Z16, Z7, K2, Z22                            // 62a1c54a5cf0
	VSUBPD Z25, Z7, K2, Z22                            // 6281c54a5cf1
	VSUBPD Z16, Z21, K2, Z22                           // 62a1d5425cf0
	VSUBPD Z25, Z21, K2, Z22                           // 6281d5425cf1
	VSUBPD Z21, Z12, K1, Z14                           // 62319d495cf5
	VSUBPD Z9, Z12, K1, Z14                            // 62519d495cf1
	VSUBPD (AX), Z12, K1, Z14                          // 62719d495c30
	VSUBPD 7(SI), Z12, K1, Z14                         // 62719d495cb607000000
	VSUBPD Z21, Z13, K1, Z14                           // 623195495cf5
	VSUBPD Z9, Z13, K1, Z14                            // 625195495cf1
	VSUBPD (AX), Z13, K1, Z14                          // 627195495c30
	VSUBPD 7(SI), Z13, K1, Z14                         // 627195495cb607000000
	VSUBPD Z21, Z12, K1, Z13                           // 62319d495ced
	VSUBPD Z9, Z12, K1, Z13                            // 62519d495ce9
	VSUBPD (AX), Z12, K1, Z13                          // 62719d495c28
	VSUBPD 7(SI), Z12, K1, Z13                         // 62719d495cae07000000
	VSUBPD Z21, Z13, K1, Z13                           // 623195495ced
	VSUBPD Z9, Z13, K1, Z13                            // 625195495ce9
	VSUBPD (AX), Z13, K1, Z13                          // 627195495c28
	VSUBPD 7(SI), Z13, K1, Z13                         // 627195495cae07000000
	VSUBPS X15, X17, K7, X5                            // 62d174075cef
	VSUBPS 15(R8)(R14*1), X17, K7, X5                  // 629174075cac300f000000
	VSUBPS 15(R8)(R14*2), X17, K7, X5                  // 629174075cac700f000000
	VSUBPS Y0, Y7, K1, Y28                             // 626144295ce0
	VSUBPS 99(R15)(R15*1), Y7, K1, Y28                 // 620144295ca43f63000000
	VSUBPS (DX), Y7, K1, Y28                           // 626144295c22
	VSUBPS Z23, Z27, K1, Z2                            // 62b124415cd7
	VSUBPS Z9, Z27, K1, Z2                             // 62d124415cd1
	VSUBPS Z23, Z25, K1, Z2                            // 62b134415cd7
	VSUBPS Z9, Z25, K1, Z2                             // 62d134415cd1
	VSUBPS Z23, Z27, K1, Z7                            // 62b124415cff
	VSUBPS Z9, Z27, K1, Z7                             // 62d124415cf9
	VSUBPS Z23, Z25, K1, Z7                            // 62b134415cff
	VSUBPS Z9, Z25, K1, Z7                             // 62d134415cf9
	VSUBPS Z14, Z3, K1, Z27                            // 624164495cde
	VSUBPS Z7, Z3, K1, Z27                             // 626164495cdf
	VSUBPS (BX), Z3, K1, Z27                           // 626164495c1b
	VSUBPS -17(BP)(SI*1), Z3, K1, Z27                  // 626164495c9c35efffffff
	VSUBPS Z14, Z0, K1, Z27                            // 62417c495cde
	VSUBPS Z7, Z0, K1, Z27                             // 62617c495cdf
	VSUBPS (BX), Z0, K1, Z27                           // 62617c495c1b
	VSUBPS -17(BP)(SI*1), Z0, K1, Z27                  // 62617c495c9c35efffffff
	VSUBPS Z14, Z3, K1, Z14                            // 625164495cf6
	VSUBPS Z7, Z3, K1, Z14                             // 627164495cf7
	VSUBPS (BX), Z3, K1, Z14                           // 627164495c33
	VSUBPS -17(BP)(SI*1), Z3, K1, Z14                  // 627164495cb435efffffff
	VSUBPS Z14, Z0, K1, Z14                            // 62517c495cf6
	VSUBPS Z7, Z0, K1, Z14                             // 62717c495cf7
	VSUBPS (BX), Z0, K1, Z14                           // 62717c495c33
	VSUBPS -17(BP)(SI*1), Z0, K1, Z14                  // 62717c495cb435efffffff
	VSUBSD X26, X3, K7, X8                             // 6211e70f5cc2
	VSUBSD X28, X13, K2, X23                           // 6281970a5cfc or 6281972a5cfc or 6281974a5cfc
	VSUBSD 7(SI)(DI*1), X13, K2, X23                   // 62e1970a5cbc3e07000000 or 62e1972a5cbc3e07000000 or 62e1974a5cbc3e07000000
	VSUBSD 15(DX)(BX*8), X13, K2, X23                  // 62e1970a5cbcda0f000000 or 62e1972a5cbcda0f000000 or 62e1974a5cbcda0f000000
	VSUBSS X15, X9, K4, X24                            // 6241360c5cc7
	VSUBSS X21, X18, K1, X26                           // 62216e015cd5 or 62216e215cd5 or 62216e415cd5
	VSUBSS -17(BP)(SI*2), X18, K1, X26                 // 62616e015c9475efffffff or 62616e215c9475efffffff or 62616e415c9475efffffff
	VSUBSS 7(AX)(CX*2), X18, K1, X26                   // 62616e015c944807000000 or 62616e215c944807000000 or 62616e415c944807000000
	VUCOMISD X3, X31                                   // 6261fd082efb or 6261fd282efb or 6261fd482efb
	VUCOMISD -7(DI)(R8*1), X31                         // 6221fd082ebc07f9ffffff or 6221fd282ebc07f9ffffff or 6221fd482ebc07f9ffffff
	VUCOMISD (SP), X31                                 // 6261fd082e3c24 or 6261fd282e3c24 or 6261fd482e3c24
	VUCOMISS X24, X0                                   // 62917c082ec0 or 62917c282ec0 or 62917c482ec0
	VUNPCKHPD X9, X7, K3, X20                          // 62c1c50b15e1
	VUNPCKHPD (R14), X7, K3, X20                       // 62c1c50b1526
	VUNPCKHPD -7(DI)(R8*8), X7, K3, X20                // 62a1c50b15a4c7f9ffffff
	VUNPCKHPD Y12, Y13, K4, Y22                        // 62c1952c15f4
	VUNPCKHPD -17(BP)(SI*8), Y13, K4, Y22              // 62e1952c15b4f5efffffff
	VUNPCKHPD (R15), Y13, K4, Y22                      // 62c1952c1537
	VUNPCKHPD Z1, Z22, K5, Z8                          // 6271cd4515c1
	VUNPCKHPD Z16, Z22, K5, Z8                         // 6231cd4515c0
	VUNPCKHPD 15(R8)(R14*4), Z22, K5, Z8               // 6211cd451584b00f000000
	VUNPCKHPD -7(CX)(DX*4), Z22, K5, Z8                // 6271cd45158491f9ffffff
	VUNPCKHPD Z1, Z25, K5, Z8                          // 6271b54515c1
	VUNPCKHPD Z16, Z25, K5, Z8                         // 6231b54515c0
	VUNPCKHPD 15(R8)(R14*4), Z25, K5, Z8               // 6211b5451584b00f000000
	VUNPCKHPD -7(CX)(DX*4), Z25, K5, Z8                // 6271b545158491f9ffffff
	VUNPCKHPD Z1, Z22, K5, Z24                         // 6261cd4515c1
	VUNPCKHPD Z16, Z22, K5, Z24                        // 6221cd4515c0
	VUNPCKHPD 15(R8)(R14*4), Z22, K5, Z24              // 6201cd451584b00f000000
	VUNPCKHPD -7(CX)(DX*4), Z22, K5, Z24               // 6261cd45158491f9ffffff
	VUNPCKHPD Z1, Z25, K5, Z24                         // 6261b54515c1
	VUNPCKHPD Z16, Z25, K5, Z24                        // 6221b54515c0
	VUNPCKHPD 15(R8)(R14*4), Z25, K5, Z24              // 6201b5451584b00f000000
	VUNPCKHPD -7(CX)(DX*4), Z25, K5, Z24               // 6261b545158491f9ffffff
	VUNPCKHPS X5, X14, K7, X7                          // 62f10c0f15fd
	VUNPCKHPS 99(R15)(R15*4), X14, K7, X7              // 62910c0f15bcbf63000000
	VUNPCKHPS 15(DX), X14, K7, X7                      // 62f10c0f15ba0f000000
	VUNPCKHPS Y17, Y14, K7, Y1                         // 62b10c2f15c9
	VUNPCKHPS 7(SI)(DI*8), Y14, K7, Y1                 // 62f10c2f158cfe07000000
	VUNPCKHPS -15(R14), Y14, K7, Y1                    // 62d10c2f158ef1ffffff
	VUNPCKHPS Z15, Z0, K6, Z6                          // 62d17c4e15f7
	VUNPCKHPS Z12, Z0, K6, Z6                          // 62d17c4e15f4
	VUNPCKHPS (R8), Z0, K6, Z6                         // 62d17c4e1530
	VUNPCKHPS 15(DX)(BX*2), Z0, K6, Z6                 // 62f17c4e15b45a0f000000
	VUNPCKHPS Z15, Z8, K6, Z6                          // 62d13c4e15f7
	VUNPCKHPS Z12, Z8, K6, Z6                          // 62d13c4e15f4
	VUNPCKHPS (R8), Z8, K6, Z6                         // 62d13c4e1530
	VUNPCKHPS 15(DX)(BX*2), Z8, K6, Z6                 // 62f13c4e15b45a0f000000
	VUNPCKHPS Z15, Z0, K6, Z2                          // 62d17c4e15d7
	VUNPCKHPS Z12, Z0, K6, Z2                          // 62d17c4e15d4
	VUNPCKHPS (R8), Z0, K6, Z2                         // 62d17c4e1510
	VUNPCKHPS 15(DX)(BX*2), Z0, K6, Z2                 // 62f17c4e15945a0f000000
	VUNPCKHPS Z15, Z8, K6, Z2                          // 62d13c4e15d7
	VUNPCKHPS Z12, Z8, K6, Z2                          // 62d13c4e15d4
	VUNPCKHPS (R8), Z8, K6, Z2                         // 62d13c4e1510
	VUNPCKHPS 15(DX)(BX*2), Z8, K6, Z2                 // 62f13c4e15945a0f000000
	VUNPCKLPD X21, X3, K3, X31                         // 6221e50b14fd
	VUNPCKLPD (CX), X3, K3, X31                        // 6261e50b1439
	VUNPCKLPD 99(R15), X3, K3, X31                     // 6241e50b14bf63000000
	VUNPCKLPD Y31, Y9, K7, Y7                          // 6291b52f14ff
	VUNPCKLPD 7(SI)(DI*1), Y9, K7, Y7                  // 62f1b52f14bc3e07000000
	VUNPCKLPD 15(DX)(BX*8), Y9, K7, Y7                 // 62f1b52f14bcda0f000000
	VUNPCKLPD Z13, Z11, K4, Z14                        // 6251a54c14f5
	VUNPCKLPD Z14, Z11, K4, Z14                        // 6251a54c14f6
	VUNPCKLPD 17(SP)(BP*1), Z11, K4, Z14               // 6271a54c14b42c11000000
	VUNPCKLPD -7(CX)(DX*8), Z11, K4, Z14               // 6271a54c14b4d1f9ffffff
	VUNPCKLPD Z13, Z5, K4, Z14                         // 6251d54c14f5
	VUNPCKLPD Z14, Z5, K4, Z14                         // 6251d54c14f6
	VUNPCKLPD 17(SP)(BP*1), Z5, K4, Z14                // 6271d54c14b42c11000000
	VUNPCKLPD -7(CX)(DX*8), Z5, K4, Z14                // 6271d54c14b4d1f9ffffff
	VUNPCKLPD Z13, Z11, K4, Z27                        // 6241a54c14dd
	VUNPCKLPD Z14, Z11, K4, Z27                        // 6241a54c14de
	VUNPCKLPD 17(SP)(BP*1), Z11, K4, Z27               // 6261a54c149c2c11000000
	VUNPCKLPD -7(CX)(DX*8), Z11, K4, Z27               // 6261a54c149cd1f9ffffff
	VUNPCKLPD Z13, Z5, K4, Z27                         // 6241d54c14dd
	VUNPCKLPD Z14, Z5, K4, Z27                         // 6241d54c14de
	VUNPCKLPD 17(SP)(BP*1), Z5, K4, Z27                // 6261d54c149c2c11000000
	VUNPCKLPD -7(CX)(DX*8), Z5, K4, Z27                // 6261d54c149cd1f9ffffff
	VUNPCKLPS X13, X11, K4, X1                         // 62d1240c14cd
	VUNPCKLPS 99(R15)(R15*2), X11, K4, X1              // 6291240c148c7f63000000
	VUNPCKLPS -7(DI), X11, K4, X1                      // 62f1240c148ff9ffffff
	VUNPCKLPS Y28, Y1, K7, Y8                          // 6211742f14c4
	VUNPCKLPS -7(DI)(R8*1), Y1, K7, Y8                 // 6231742f148407f9ffffff
	VUNPCKLPS (SP), Y1, K7, Y8                         // 6271742f140424
	VUNPCKLPS Z6, Z2, K2, Z5                           // 62f16c4a14ee
	VUNPCKLPS Z14, Z2, K2, Z5                          // 62d16c4a14ee
	VUNPCKLPS -17(BP)(SI*2), Z2, K2, Z5                // 62f16c4a14ac75efffffff
	VUNPCKLPS 7(AX)(CX*2), Z2, K2, Z5                  // 62f16c4a14ac4807000000
	VUNPCKLPS Z6, Z2, K2, Z23                          // 62e16c4a14fe
	VUNPCKLPS Z14, Z2, K2, Z23                         // 62c16c4a14fe
	VUNPCKLPS -17(BP)(SI*2), Z2, K2, Z23               // 62e16c4a14bc75efffffff
	VUNPCKLPS 7(AX)(CX*2), Z2, K2, Z23                 // 62e16c4a14bc4807000000
	RET
