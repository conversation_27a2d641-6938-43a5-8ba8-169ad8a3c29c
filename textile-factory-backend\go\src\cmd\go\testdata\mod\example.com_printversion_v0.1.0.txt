example.com/printversion v0.1.0

-- .mod --
module example.com/printversion
-- .info --
{"Version":"v0.1.0"}
-- README.txt --
There is no go.mod file for this version of the module.
-- printversion.go --
package main

import (
	"fmt"
	"os"
	"runtime/debug"

	_ "example.com/version"
)

func main() {
	info, _ := debug.ReadBuildInfo()
	fmt.Fprintf(os.Stdout, "path is %s\n", info.Path)
	fmt.Fprintf(os.Stdout, "main is %s %s\n", info.Main.Path, info.Main.Version)
	if r := info.Main.Replace; r != nil {
		fmt.Fprintf(os.Stdout, "\t(replaced by %s %s)\n", r.Path, r.Version)
	}
	for _, m := range info.Deps {
		fmt.Fprintf(os.Stdout, "using %s %s\n", m.Path, m.Version)
		if r := m.Replace; r != nil {
			fmt.Fprintf(os.Stdout, "\t(replaced by %s %s)\n", r.Path, r.Version)
		}
	}
}
