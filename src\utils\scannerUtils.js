/**
 * 新大陆扫码头工具类
 * 支持安卓PDA设备的扫码功能
 */

class ScannerUtils {
  constructor() {
    this.scanReceiver = null
    this.isPageActive = false
    this.scanCallback = null
    this.isInitialized = false
  }

  /**
   * 初始化扫码功能
   * @param {Function} callback 扫码成功回调函数
   * @param {Boolean} isPageActive 页面是否活跃
   */
  async initScanner(callback, isPageActive = true) {
	  console.log('initScaner')
    this.scanCallback = callback
    this.isPageActive = isPageActive

    // 检查是否为安卓平台
    if (uni.getSystemInfoSync().platform !== 'android') {
      console.log('非安卓平台，使用uni.scanCode')
      return this.useUniScanCode()
    }

    try {
      const main = plus.android.runtimeMainActivity()

      // 配置扫码枪广播设置
      await this.configureScannerBroadcast(main)

      // 注册广播接收器
      await this.registerBroadcastReceiver(main)

      this.isInitialized = true
      console.log('新大陆扫码头初始化成功')
      
      return {
        success: true,
        message: '扫码功能已启用，请使用扫码枪扫描'
      }
    } catch (error) {
      console.error('初始化扫码功能失败：', error)
      // 降级到uni.scanCode
      return this.useUniScanCode()
    }
  }

  /**
   * 配置扫码枪广播设置
   */
  async configureScannerBroadcast(main) {
    try {
      const Intent = plus.android.importClass("android.content.Intent")
      const intent = new Intent("com.android.scanner.service_settings")
      
      intent.putExtra(
        "action_barcode_broadcast",
        "com.android.server.scannerservice.broadcast"
      )
      intent.putExtra("key_barcode_broadcast", "scannerdata")
      
      main.sendBroadcast(intent)
      console.log('扫码枪广播配置完成')
    } catch (error) {
      console.error('配置扫码枪广播失败：', error)
      throw error
    }
  }

  /**
   * 注册广播接收器
   */
  async registerBroadcastReceiver(main) {
    try {
      const IntentFilter = plus.android.importClass("android.content.IntentFilter")
      const filter = new IntentFilter()
      filter.addAction("com.android.server.scannerservice.broadcast")

      const receiver = plus.android.implements(
        "io.dcloud.feature.internal.reflect.BroadcastReceiver",
        {
          onReceive: (context, intent) => {
            // 只有当页面活动时才处理广播
            if (!this.isPageActive) return

            try {
              const scanResult = intent.getStringExtra("scannerdata")
              console.log('扫码结果:', scanResult)
              
              if (scanResult && this.scanCallback) {
                this.scanCallback({
                  success: true,
                  result: scanResult,
                  scanType: 'QR_CODE'
                })
              }
            } catch (error) {
              console.error('处理广播数据时出错：', error)
              if (this.scanCallback) {
                this.scanCallback({
                  success: false,
                  error: error.message
                })
              }
            }
          }
        }
      )

      // 注册广播接收器
      main.registerReceiver(receiver, filter)
      this.scanReceiver = receiver
      console.log('扫码广播接收器注册成功')
    } catch (error) {
      console.error('注册广播接收器失败：', error)
      throw error
    }
  }

  /**
   * 使用uni.scanCode作为降级方案
   */
  useUniScanCode() {
    console.log('使用uni.scanCode扫码')
    uni.scanCode({
      success: (res) => {
        if (this.scanCallback) {
          this.scanCallback({
            success: true,
            result: res.result,
            scanType: res.scanType
          })
        }
      },
      fail: (err) => {
        console.error('扫码失败:', err)
        if (this.scanCallback) {
          this.scanCallback({
            success: false,
            error: err.errMsg || '扫码失败'
          })
        }
      }
    })

    return {
      success: true,
      message: '使用系统扫码功能'
    }
  }

  /**
   * 设置页面活跃状态
   */
  setPageActive(isActive) {
    this.isPageActive = isActive
  }

  /**
   * 销毁扫码功能
   */
  destroy() {
    if (this.scanReceiver && uni.getSystemInfoSync().platform === 'android') {
      try {
        const main = plus.android.runtimeMainActivity()
        main.unregisterReceiver(this.scanReceiver)
        console.log('扫码广播接收器已注销')
      } catch (error) {
        console.error('注销广播接收器失败：', error)
      }
    }
    
    this.scanReceiver = null
    this.scanCallback = null
    this.isInitialized = false
  }

  /**
   * 手动触发扫码（用于按钮点击等场景）
   */
  startScan() {
    if (!this.isInitialized) {
      return this.useUniScanCode()
    }
    
    // 对于新大陆扫码头，通常是自动触发的
    // 这里可以显示提示信息
    uni.showToast({
      title: '请使用扫码枪扫描',
      icon: 'none',
      duration: 2000
    })
  }
}

// 创建单例实例
const scannerUtils = new ScannerUtils()

export default scannerUtils
